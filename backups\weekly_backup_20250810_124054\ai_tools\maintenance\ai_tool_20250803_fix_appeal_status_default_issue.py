#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 修复appeal_status字段默认值导致的提审逻辑问题
清理条件: 问题修复后可归档，但建议保留作为维护工具
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timezone
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from loguru import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign

class AppealStatusFixer:
    """修复appeal_status默认值问题的工具类"""
    
    def __init__(self):
        self.fixed_count = 0
        self.error_count = 0
        self.issues_found = []
    
    def analyze_problem(self) -> Dict[str, Any]:
        """分析问题的影响范围"""
        
        logger.info("🔍 开始分析appeal_status默认值问题...")
        
        with database_session() as db:
            # 查询所有受影响的计划
            affected_plans = db.execute(text("""
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    appeal_attempt_count,
                    first_appeal_at,
                    last_appeal_at,
                    created_at,
                    CASE 
                        WHEN first_appeal_at IS NULL AND last_appeal_at IS NULL 
                             AND appeal_attempt_count = 0 THEN 'never_appealed'
                        WHEN first_appeal_at IS NULL AND last_appeal_at IS NOT NULL THEN 'data_inconsistent'
                        WHEN first_appeal_at IS NOT NULL THEN 'actually_appealed'
                        ELSE 'unknown'
                    END as issue_type
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending' 
                    AND status = 'AUDITING'
                ORDER BY created_at
            """)).fetchall()
            
            analysis = {
                'total_affected': len(affected_plans),
                'never_appealed': 0,
                'data_inconsistent': 0,
                'actually_appealed': 0,
                'plans_detail': []
            }
            
            for plan in affected_plans:
                plan_info = {
                    'campaign_id_qc': plan.campaign_id_qc,
                    'appeal_status': plan.appeal_status,
                    'appeal_attempt_count': plan.appeal_attempt_count,
                    'first_appeal_at': plan.first_appeal_at,
                    'last_appeal_at': plan.last_appeal_at,
                    'created_at': plan.created_at,
                    'issue_type': plan.issue_type
                }
                
                analysis['plans_detail'].append(plan_info)
                analysis[plan.issue_type] += 1
            
            logger.info(f"📊 问题分析结果:")
            logger.info(f"   总受影响计划: {analysis['total_affected']}")
            logger.info(f"   从未提审过: {analysis['never_appealed']}")
            logger.info(f"   数据不一致: {analysis['data_inconsistent']}")
            logger.info(f"   实际已提审: {analysis['actually_appealed']}")
            
            return analysis
    
    def fix_never_appealed_plans(self, dry_run: bool = True) -> List[str]:
        """修复从未提审过的计划"""
        
        logger.info(f"🔧 开始修复从未提审过的计划 (dry_run={dry_run})...")
        
        fixed_plans = []
        
        with database_session() as db:
            # 查找从未提审过的计划
            never_appealed = db.execute(text("""
                SELECT campaign_id_qc
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending' 
                    AND status = 'AUDITING'
                    AND first_appeal_at IS NULL 
                    AND last_appeal_at IS NULL
                    AND appeal_attempt_count = 0
            """)).fetchall()
            
            logger.info(f"找到 {len(never_appealed)} 个从未提审过的计划")
            
            for plan in never_appealed:
                campaign_id = plan.campaign_id_qc
                
                if not dry_run:
                    try:
                        # 重置appeal_status为NULL，让系统重新识别
                        db.execute(text("""
                            UPDATE campaigns 
                            SET appeal_status = NULL,
                                appeal_attempt_count = 0
                            WHERE campaign_id_qc = :campaign_id
                        """), {'campaign_id': campaign_id})
                        
                        fixed_plans.append(campaign_id)
                        logger.success(f"✅ 修复计划 {campaign_id}")
                        
                    except Exception as e:
                        logger.error(f"❌ 修复计划 {campaign_id} 失败: {e}")
                        self.error_count += 1
                else:
                    logger.info(f"🔍 [DRY RUN] 将修复计划 {campaign_id}")
                    fixed_plans.append(campaign_id)
            
            if not dry_run:
                db.commit()
                logger.success(f"✅ 成功修复 {len(fixed_plans)} 个计划")
            else:
                logger.info(f"🔍 [DRY RUN] 预计修复 {len(fixed_plans)} 个计划")
        
        return fixed_plans
    
    def fix_data_inconsistent_plans(self, dry_run: bool = True) -> List[str]:
        """修复数据不一致的计划"""
        
        logger.info(f"🔧 开始修复数据不一致的计划 (dry_run={dry_run})...")
        
        fixed_plans = []
        
        with database_session() as db:
            # 查找数据不一致的计划
            inconsistent = db.execute(text("""
                SELECT campaign_id_qc, last_appeal_at, appeal_attempt_count
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending' 
                    AND status = 'AUDITING'
                    AND first_appeal_at IS NULL 
                    AND last_appeal_at IS NOT NULL
            """)).fetchall()
            
            logger.info(f"找到 {len(inconsistent)} 个数据不一致的计划")
            
            for plan in inconsistent:
                campaign_id = plan.campaign_id_qc
                
                if not dry_run:
                    try:
                        # 根据last_appeal_at设置first_appeal_at
                        db.execute(text("""
                            UPDATE campaigns 
                            SET first_appeal_at = last_appeal_at
                            WHERE campaign_id_qc = :campaign_id
                        """), {'campaign_id': campaign_id})
                        
                        fixed_plans.append(campaign_id)
                        logger.success(f"✅ 修复计划 {campaign_id} 数据一致性")
                        
                    except Exception as e:
                        logger.error(f"❌ 修复计划 {campaign_id} 失败: {e}")
                        self.error_count += 1
                else:
                    logger.info(f"🔍 [DRY RUN] 将修复计划 {campaign_id} 数据一致性")
                    fixed_plans.append(campaign_id)
            
            if not dry_run:
                db.commit()
                logger.success(f"✅ 成功修复 {len(fixed_plans)} 个计划的数据一致性")
            else:
                logger.info(f"🔍 [DRY RUN] 预计修复 {len(fixed_plans)} 个计划的数据一致性")
        
        return fixed_plans
    
    def verify_fix(self) -> bool:
        """验证修复效果"""
        
        logger.info("🔍 验证修复效果...")
        
        with database_session() as db:
            # 检查是否还有问题
            remaining_issues = db.execute(text("""
                SELECT COUNT(*) as count
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending' 
                    AND status = 'AUDITING'
                    AND first_appeal_at IS NULL
                    AND appeal_attempt_count = 0
            """)).fetchone()
            
            if remaining_issues.count == 0:
                logger.success("✅ 所有问题已修复")
                return True
            else:
                logger.warning(f"⚠️ 仍有 {remaining_issues.count} 个问题未修复")
                return False

def main():
    """主函数"""
    
    logger.info("🚀 开始修复appeal_status默认值问题...")
    
    fixer = AppealStatusFixer()
    
    # 1. 分析问题
    analysis = fixer.analyze_problem()
    
    # 2. 显示具体的问题计划
    logger.info("\n📋 具体问题计划:")
    for plan in analysis['plans_detail']:
        if plan['issue_type'] == 'never_appealed':
            logger.warning(f"   ❌ {plan['campaign_id_qc']} - 从未提审过但状态为appeal_pending")
        elif plan['issue_type'] == 'data_inconsistent':
            logger.warning(f"   ⚠️ {plan['campaign_id_qc']} - 数据不一致 (有last_appeal_at但无first_appeal_at)")
    
    # 3. 询问是否执行修复
    print("\n" + "="*60)
    print("🎯 修复方案:")
    print("   1. 从未提审过的计划: 重置appeal_status为NULL")
    print("   2. 数据不一致的计划: 设置first_appeal_at = last_appeal_at")
    print("="*60)
    
    # 先执行dry run
    logger.info("🔍 执行预演 (dry run)...")
    never_appealed_plans = fixer.fix_never_appealed_plans(dry_run=True)
    inconsistent_plans = fixer.fix_data_inconsistent_plans(dry_run=True)
    
    # 询问是否真正执行
    response = input("\n是否执行实际修复? (y/N): ").strip().lower()
    
    if response == 'y':
        logger.info("🔧 开始执行实际修复...")
        
        # 执行修复
        fixed_never = fixer.fix_never_appealed_plans(dry_run=False)
        fixed_inconsistent = fixer.fix_data_inconsistent_plans(dry_run=False)
        
        # 验证修复效果
        success = fixer.verify_fix()
        
        if success:
            logger.success("🎉 修复完成！")
            logger.info(f"📊 修复统计:")
            logger.info(f"   从未提审过的计划: {len(fixed_never)}")
            logger.info(f"   数据不一致的计划: {len(fixed_inconsistent)}")
            
            print("\n💡 建议:")
            print("   1. 修改数据库表结构，将appeal_status默认值改为NULL")
            print("   2. 重新运行提审流程，这些计划现在应该能被正确识别")
            print("   3. 监控日志确保修复后的计划能正常提审")
        else:
            logger.error("💥 修复未完全成功，请检查日志")
    else:
        logger.info("❌ 用户取消修复操作")

if __name__ == "__main__":
    main()
