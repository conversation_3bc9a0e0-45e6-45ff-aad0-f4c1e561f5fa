#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复提审逻辑问题 - 允许失败提审重试
清理条件: 成为提审系统核心修复工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import Campaign


class AppealLogicFixer:
    """提审逻辑修复器"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def analyze_appeal_issues(self):
        """分析提审问题"""
        logger.info("🔍 分析提审逻辑问题")
        logger.info("="*80)
        
        with SessionLocal() as db:
            # 1. 检查appeal_pending状态的计划
            pending_plans = db.query(Campaign).filter(
                Campaign.appeal_status == 'appeal_pending'
            ).all()
            
            logger.info(f"📊 发现 {len(pending_plans)} 个appeal_pending状态的计划")
            
            # 2. 分析这些计划的提审历史
            failed_appeals = []
            stuck_appeals = []
            
            for plan in pending_plans:
                # 检查提审是否真正成功
                time_since_first_appeal = None
                if plan.first_appeal_at:
                    time_since_first_appeal = datetime.now(timezone.utc) - plan.first_appeal_at.replace(tzinfo=timezone.utc)
                
                # 如果提审超过24小时仍在pending状态，可能是失败的
                if time_since_first_appeal and time_since_first_appeal > timedelta(hours=24):
                    if plan.appeal_error_message:
                        failed_appeals.append({
                            'plan_id': plan.campaign_id_qc,
                            'error': plan.appeal_error_message,
                            'time_since': time_since_first_appeal
                        })
                    else:
                        stuck_appeals.append({
                            'plan_id': plan.campaign_id_qc,
                            'time_since': time_since_first_appeal,
                            'attempt_count': plan.appeal_attempt_count
                        })
            
            logger.info(f"❌ 明确失败的提审: {len(failed_appeals)} 个")
            logger.info(f"⚠️ 疑似卡住的提审: {len(stuck_appeals)} 个")
            
            # 3. 检查当前的重试限制逻辑
            self._analyze_retry_logic()
            
            return {
                'total_pending': len(pending_plans),
                'failed_appeals': failed_appeals,
                'stuck_appeals': stuck_appeals
            }
    
    def _analyze_retry_logic(self):
        """分析重试逻辑"""
        logger.info("\n🔧 分析当前重试逻辑...")
        
        # 检查_can_safely_retry_appeal函数的逻辑
        scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有过于严格的限制
            strict_checks = [
                'appeal_attempt_count > 0' in content,
                'first_appeal_at' in content and 'return False' in content,
                '严格遵循一次性原则' in content
            ]
            
            if all(strict_checks):
                logger.error("❌ 发现过于严格的提审限制逻辑")
                self.issues_found.append("提审重试逻辑过于严格")
            else:
                logger.info("ℹ️ 提审重试逻辑检查通过")
                
        except Exception as e:
            logger.error(f"❌ 无法分析重试逻辑: {e}")
    
    def fix_appeal_retry_logic(self):
        """修复提审重试逻辑"""
        logger.info("\n🔧 修复提审重试逻辑...")
        
        scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 找到_can_safely_retry_appeal函数并修复
            old_function = '''def _can_safely_retry_appeal(plan: Campaign) -> Tuple[bool, str]:
    """
    检查是否可以安全地重试提审

    这是业务铁律的核心检查函数，确保绝对不会重复提审已成功提审的计划

    Args:
        plan: Campaign对象

    Returns:
        Tuple[bool, str]: (是否可以安全重试, 原因说明)
    """

    # 🛡️ 检查1: 提审次数限制 - 严格的一次性原则
    if plan.appeal_attempt_count and plan.appeal_attempt_count > 0:
        return False, f"计划已提审过 {plan.appeal_attempt_count} 次，严格遵循一次性原则"

    # 🛡️ 检查2: 提审时间检查 - 有提审历史就不允许重试
    if plan.first_appeal_at:
        return False, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，不允许重复提审"

    # 🛡️ 检查3: 申诉状态检查 - 避免状态冲突
    if plan.appeal_status in ['appeal_pending', 'appealing']:
        return False, f"计划申诉状态为 {plan.appeal_status}，不允许重复提审"

    # 🛡️ 检查4: 最后提审时间检查 - 防止短时间内重复操作
    if plan.last_appeal_at:
        time_since_last_appeal = datetime.now(timezone.utc) - plan.last_appeal_at
        if time_since_last_appeal < timedelta(hours=1):
            return False, f"距离上次提审仅 {time_since_last_appeal}，时间间隔过短"

    return True, "可以安全提审"'''
            
            new_function = '''def _can_safely_retry_appeal(plan: Campaign) -> Tuple[bool, str]:
    """
    检查是否可以安全地重试提审 - 修复版本
    
    允许失败的提审进行重试，但防止重复提审成功的计划

    Args:
        plan: Campaign对象

    Returns:
        Tuple[bool, str]: (是否可以安全重试, 原因说明)
    """

    # 🛡️ 检查1: 成功提审不允许重试
    if plan.appeal_status == 'appeal_success':
        return False, f"计划已成功提审，不允许重复提审"

    # 🛡️ 检查2: 正在提审中不允许重复
    if plan.appeal_status == 'appealing':
        return False, f"计划正在提审中，避免重复操作"

    # 🛡️ 检查3: 提审次数限制 - 最多允许3次尝试
    if plan.appeal_attempt_count and plan.appeal_attempt_count >= 3:
        return False, f"计划已提审 {plan.appeal_attempt_count} 次，达到最大重试次数"

    # 🛡️ 检查4: 最后提审时间检查 - 防止短时间内重复操作
    if plan.last_appeal_at:
        time_since_last_appeal = datetime.now(timezone.utc) - plan.last_appeal_at.replace(tzinfo=timezone.utc)
        if time_since_last_appeal < timedelta(hours=2):
            return False, f"距离上次提审仅 {time_since_last_appeal}，时间间隔过短"

    # 🔄 检查5: 长时间pending状态允许重试
    if plan.appeal_status == 'appeal_pending' and plan.first_appeal_at:
        time_since_first_appeal = datetime.now(timezone.utc) - plan.first_appeal_at.replace(tzinfo=timezone.utc)
        if time_since_first_appeal > timedelta(hours=24):
            return True, f"提审已超过24小时仍在pending状态，允许重试"

    # 🔄 检查6: 有错误信息的失败提审允许重试
    if plan.appeal_error_message:
        return True, f"上次提审失败 ({plan.appeal_error_message})，允许重试"

    # 🔄 检查7: 新计划允许提审
    if not plan.first_appeal_at:
        return True, "新计划，可以提审"

    return True, "可以安全提审"'''
            
            # 替换函数
            if old_function in content:
                new_content = content.replace(old_function, new_function)
                
                # 写回文件
                with open(scheduler_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                logger.success("✅ 提审重试逻辑已修复")
                self.fixes_applied.append("修复了过于严格的提审重试逻辑")
                return True
            else:
                logger.warning("⚠️ 未找到目标函数，可能已经修复")
                return False
                
        except Exception as e:
            logger.error(f"❌ 修复提审重试逻辑失败: {e}")
            return False
    
    def reset_stuck_appeals(self):
        """重置卡住的提审状态"""
        logger.info("\n🔄 重置卡住的提审状态...")
        
        with SessionLocal() as db:
            # 查找超过24小时仍在pending状态的计划
            stuck_threshold = datetime.now(timezone.utc) - timedelta(hours=24)
            
            stuck_plans = db.query(Campaign).filter(
                Campaign.appeal_status == 'appeal_pending',
                Campaign.first_appeal_at < stuck_threshold,
                Campaign.appeal_error_message.is_(None)  # 没有明确错误信息的
            ).all()
            
            logger.info(f"🔍 发现 {len(stuck_plans)} 个疑似卡住的提审")
            
            reset_count = 0
            for plan in stuck_plans:
                try:
                    # 重置为可重试状态
                    plan.appeal_status = 'appeal_pending'  # 保持pending但允许重试
                    plan.appeal_error_message = "提审超时，已重置为可重试状态"
                    plan.updated_at = datetime.now(timezone.utc)
                    
                    reset_count += 1
                    logger.info(f"🔄 重置计划 {plan.campaign_id_qc} 的提审状态")
                    
                except Exception as e:
                    logger.error(f"❌ 重置计划 {plan.campaign_id_qc} 失败: {e}")
            
            if reset_count > 0:
                db.commit()
                logger.success(f"✅ 成功重置 {reset_count} 个卡住的提审")
                self.fixes_applied.append(f"重置了 {reset_count} 个卡住的提审")
            else:
                logger.info("ℹ️ 没有需要重置的提审")
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("\n📋 提审逻辑修复报告")
        logger.info("="*80)
        
        logger.info(f"🔍 发现问题 ({len(self.issues_found)} 个):")
        for issue in self.issues_found:
            logger.info(f"   ❌ {issue}")
        
        logger.info(f"\n🔧 应用修复 ({len(self.fixes_applied)} 个):")
        for fix in self.fixes_applied:
            logger.info(f"   ✅ {fix}")
        
        logger.info("\n💡 修复效果:")
        logger.info("   • 允许失败的提审进行重试（最多3次）")
        logger.info("   • 超过24小时的pending状态可以重试")
        logger.info("   • 有错误信息的提审可以重试")
        logger.info("   • 防止成功提审的重复操作")
        logger.info("   • 增加提审间隔时间到2小时")
        
        logger.info("\n🚀 下一步建议:")
        logger.info("   1. 重启Celery服务使修复生效")
        logger.info("   2. 观察提审重试是否正常工作")
        logger.info("   3. 监控提审成功率是否提升")


def main():
    """主函数"""
    fixer = AppealLogicFixer()
    
    # 1. 分析问题
    analysis_result = fixer.analyze_appeal_issues()
    
    # 2. 修复重试逻辑
    logic_fixed = fixer.fix_appeal_retry_logic()
    
    # 3. 重置卡住的提审
    fixer.reset_stuck_appeals()
    
    # 4. 生成报告
    fixer.generate_fix_report()
    
    if logic_fixed and len(fixer.fixes_applied) > 0:
        logger.success("🎊 提审逻辑修复完成")
        logger.success("🚀 建议重启Celery服务使修复生效")
        return 0
    else:
        logger.warning("⚠️ 提审逻辑修复部分完成")
        return 1


if __name__ == "__main__":
    exit(main())
