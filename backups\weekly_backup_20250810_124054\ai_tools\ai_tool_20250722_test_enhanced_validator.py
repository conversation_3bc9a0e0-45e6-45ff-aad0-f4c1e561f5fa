#!/usr/bin/env python3
"""
测试增强版视频验证器
验证新算法的准确性和误隔离率
"""

import sys
import os
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.enhanced_video_validator import EnhancedVideoValidator
from src.qianchuan_aw.utils.logger import logger

def test_enhanced_validator():
    """测试增强版视频验证器"""
    logger.info("🧪 测试增强版视频验证器")
    logger.info("=" * 50)
    
    # 创建测试验证器
    test_quarantine_dir = "quarantine/test_quarantine"
    validator = EnhancedVideoValidator(test_quarantine_dir)
    
    # 测试隔离目录中的文件
    quarantine_dir = Path("quarantine/invalid_videos")
    if not quarantine_dir.exists():
        logger.error("隔离目录不存在")
        return
    
    video_files = list(quarantine_dir.glob("*.mp4"))
    logger.info(f"测试 {len(video_files)} 个隔离视频文件")
    
    # 统计结果
    results = {
        'total_tested': 0,
        'now_valid': 0,
        'still_invalid': 0,
        'validation_improved': 0
    }
    
    improved_files = []
    
    for video_file in video_files[:10]:  # 测试前10个文件
        logger.info(f"\n测试文件: {video_file.name}")
        
        # 使用新验证器测试
        result = validator.validate_video(str(video_file))
        
        results['total_tested'] += 1
        
        logger.info(f"  验证结果: {'✅ 有效' if result.is_valid else '❌ 无效'}")
        logger.info(f"  原因: {result.reason}")
        logger.info(f"  置信度: {result.confidence_score:.2f}")
        
        if result.duration:
            logger.info(f"  检测时长: {result.duration:.1f}s")
        if result.resolution:
            logger.info(f"  分辨率: {result.resolution[0]}x{result.resolution[1]}")
        if result.file_size:
            logger.info(f"  文件大小: {result.file_size/(1024*1024):.1f}MB")
        
        if result.is_valid:
            results['now_valid'] += 1
            improved_files.append({
                'filename': video_file.name,
                'reason': result.reason,
                'confidence': result.confidence_score,
                'duration': result.duration
            })
        else:
            results['still_invalid'] += 1
    
    # 分析结果
    logger.info(f"\n📊 测试结果统计:")
    logger.info(f"  总测试文件: {results['total_tested']}")
    logger.info(f"  现在有效: {results['now_valid']}")
    logger.info(f"  仍然无效: {results['still_invalid']}")
    
    if results['total_tested'] > 0:
        improvement_rate = (results['now_valid'] / results['total_tested']) * 100
        logger.info(f"  改善率: {improvement_rate:.1f}%")
        
        if improvement_rate > 50:
            logger.info("✅ 验证器改善效果显著")
        elif improvement_rate > 20:
            logger.info("🟡 验证器有一定改善")
        else:
            logger.warning("🔴 验证器改善效果有限")
    
    # 显示改善的文件
    if improved_files:
        logger.info(f"\n📋 现在被认为有效的文件:")
        for file_info in improved_files:
            duration_str = f"{file_info['duration']:.1f}s" if file_info['duration'] else "未检测"
            logger.info(f"  {file_info['filename']}: {file_info['reason']} (时长: {duration_str}, 置信度: {file_info['confidence']:.2f})")
    
    # 获取验证统计
    stats = validator.get_validation_stats()
    logger.info(f"\n📈 验证器统计:")
    logger.info(f"  总验证次数: {stats['total_validated']}")
    logger.info(f"  隔离次数: {stats['quarantined']}")
    logger.info(f"  隔离率: {stats['quarantine_rate']:.1f}%")
    
    logger.info(f"\n  检测方法成功次数:")
    for method, count in stats['method_success'].items():
        logger.info(f"    {method}: {count}次")
    
    return results, improved_files

def test_normal_videos():
    """测试正常视频文件"""
    logger.info(f"\n🎬 测试正常视频文件")
    logger.info("=" * 50)
    
    # 测试工作目录中的正常视频
    work_dirs = [
        "G:/workflow_assets/01_materials_to_process/缇萃百货",
        "G:/workflow_assets/00_materials_archived/缇萃百货"
    ]
    
    validator = EnhancedVideoValidator()
    
    normal_results = {
        'total_tested': 0,
        'valid': 0,
        'invalid': 0
    }
    
    for work_dir in work_dirs:
        if not os.path.exists(work_dir):
            continue
        
        video_files = list(Path(work_dir).glob("*.mp4"))
        logger.info(f"测试目录: {work_dir} ({len(video_files)}个文件)")
        
        for video_file in video_files[:5]:  # 测试前5个文件
            result = validator.validate_video(str(video_file))
            
            normal_results['total_tested'] += 1
            
            if result.is_valid:
                normal_results['valid'] += 1
                logger.info(f"  ✅ {video_file.name}: {result.reason}")
            else:
                normal_results['invalid'] += 1
                logger.warning(f"  ❌ {video_file.name}: {result.reason}")
    
    logger.info(f"\n📊 正常视频测试结果:")
    logger.info(f"  总测试: {normal_results['total_tested']}")
    logger.info(f"  有效: {normal_results['valid']}")
    logger.info(f"  无效: {normal_results['invalid']}")
    
    if normal_results['total_tested'] > 0:
        valid_rate = (normal_results['valid'] / normal_results['total_tested']) * 100
        logger.info(f"  有效率: {valid_rate:.1f}%")
        
        if valid_rate > 90:
            logger.info("✅ 正常视频识别准确")
        elif valid_rate > 70:
            logger.warning("🟡 正常视频识别需要改进")
        else:
            logger.error("🔴 正常视频识别存在问题")
    
    return normal_results

def generate_deployment_recommendation(quarantine_results, normal_results):
    """生成部署建议"""
    logger.info(f"\n🚀 部署建议:")
    
    # 计算总体改善效果
    if quarantine_results['total_tested'] > 0:
        improvement_rate = (quarantine_results['now_valid'] / quarantine_results['total_tested']) * 100
    else:
        improvement_rate = 0
    
    if normal_results['total_tested'] > 0:
        normal_accuracy = (normal_results['valid'] / normal_results['total_tested']) * 100
    else:
        normal_accuracy = 100
    
    logger.info(f"  误隔离改善率: {improvement_rate:.1f}%")
    logger.info(f"  正常视频准确率: {normal_accuracy:.1f}%")
    
    # 部署决策
    if improvement_rate > 70 and normal_accuracy > 90:
        logger.info("✅ 强烈建议立即部署增强版验证器")
        logger.info("   预期效果: 误隔离率从92.6%降至<10%")
    elif improvement_rate > 50 and normal_accuracy > 80:
        logger.info("🟡 建议部署增强版验证器，但需要监控")
        logger.info("   预期效果: 误隔离率显著降低")
    else:
        logger.warning("🔴 建议进一步优化后再部署")
        logger.warning("   当前改善效果不够显著")
    
    logger.info(f"\n📋 部署步骤:")
    logger.info(f"  1. 备份现有验证器")
    logger.info(f"  2. 更新工作流调用新验证器")
    logger.info(f"  3. 监控误隔离率变化")
    logger.info(f"  4. 根据效果调整参数")

def main():
    """主函数"""
    try:
        # 测试隔离文件
        quarantine_results, improved_files = test_enhanced_validator()
        
        # 测试正常文件
        normal_results = test_normal_videos()
        
        # 生成部署建议
        generate_deployment_recommendation(quarantine_results, normal_results)
        
        logger.info(f"\n✅ 增强版验证器测试完成")
        
        return quarantine_results, normal_results
        
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
