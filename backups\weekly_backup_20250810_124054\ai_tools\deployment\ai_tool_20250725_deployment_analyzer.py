#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目部署分析工具，分析项目依赖关系和配置需求
清理条件: 项目不再需要部署迁移时可删除
"""

import os
import sys
import json
import yaml
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import shutil
import platform

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class QianchuanDeploymentAnalyzer:
    """千川自动化项目部署分析器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.analysis_result = {}
        self.deployment_issues = []
        
    def analyze_project_structure(self) -> Dict[str, Any]:
        """分析项目结构"""
        print("🔍 分析项目结构...")
        
        structure = {
            "core_files": [],
            "config_files": [],
            "data_files": [],
            "log_directories": [],
            "asset_directories": [],
            "missing_files": []
        }
        
        # 核心文件检查
        core_files = [
            "main.py", "web_ui.py", "requirements.txt",
            "run_celery_worker.py", "run_celery_beat.py",
            "src/qianchuan_aw/__init__.py"
        ]
        
        for file_path in core_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                structure["core_files"].append(str(file_path))
            else:
                structure["missing_files"].append(str(file_path))
        
        # 配置文件检查
        config_dir = self.project_root / "config"
        if config_dir.exists():
            for config_file in config_dir.glob("*.yml"):
                structure["config_files"].append(str(config_file.relative_to(self.project_root)))
            for config_file in config_dir.glob("*.json"):
                structure["config_files"].append(str(config_file.relative_to(self.project_root)))
        
        # 目录检查
        important_dirs = ["logs", "workflow_assets", "src", "tools", "doc"]
        for dir_name in important_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                if dir_name == "logs":
                    structure["log_directories"].append(str(dir_path))
                elif dir_name == "workflow_assets":
                    structure["asset_directories"].append(str(dir_path))
        
        return structure
    
    def analyze_dependencies(self) -> Dict[str, Any]:
        """分析项目依赖"""
        print("📦 分析项目依赖...")
        
        dependencies = {
            "python_packages": [],
            "system_requirements": [],
            "external_services": [],
            "browser_dependencies": []
        }
        
        # Python包依赖
        requirements_file = self.project_root / "requirements.txt"
        if requirements_file.exists():
            with open(requirements_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        dependencies["python_packages"].append(line)
        
        # 系统要求
        dependencies["system_requirements"] = [
            "Python 3.9+",
            "PostgreSQL 12+ (可选，可使用SQLite)",
            "Redis 6.0+",
            "Chrome/Edge浏览器 (用于自动化)",
            "网络连接 (访问千川API)"
        ]
        
        # 外部服务
        dependencies["external_services"] = [
            {"name": "PostgreSQL", "required": False, "alternative": "SQLite"},
            {"name": "Redis", "required": True, "purpose": "任务队列"},
            {"name": "千川API", "required": True, "purpose": "广告投放API"}
        ]
        
        # 浏览器依赖
        dependencies["browser_dependencies"] = [
            "Playwright浏览器驱动",
            "Chrome/Chromium浏览器",
            "浏览器用户数据目录"
        ]
        
        return dependencies
    
    def analyze_configuration(self) -> Dict[str, Any]:
        """分析配置文件"""
        print("⚙️ 分析配置文件...")
        
        config_analysis = {
            "hardcoded_paths": [],
            "environment_specific": [],
            "sensitive_data": [],
            "database_config": {},
            "api_config": {}
        }
        
        # 分析settings.yml
        settings_file = self.project_root / "config" / "settings.yml"
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = yaml.safe_load(f)
                
                # 检查硬编码路径
                if 'custom_workflow_assets_dir' in settings:
                    path = settings['custom_workflow_assets_dir']
                    if path and os.path.isabs(path):
                        config_analysis["hardcoded_paths"].append({
                            "file": "config/settings.yml",
                            "key": "custom_workflow_assets_dir",
                            "value": path,
                            "type": "工作流资产目录"
                        })
                
                # 数据库配置
                if 'database' in settings:
                    config_analysis["database_config"] = settings['database']
                
                # API配置
                if 'api_credentials' in settings:
                    config_analysis["api_config"] = {
                        "app_id": settings['api_credentials'].get('app_id'),
                        "has_secret": bool(settings['api_credentials'].get('secret'))
                    }
                    config_analysis["sensitive_data"].append("API密钥")
                
                # 素材收集路径
                if 'workflow' in settings and 'material_collection' in settings['workflow']:
                    mc = settings['workflow']['material_collection']
                    if 'dest_dir' in mc:
                        config_analysis["hardcoded_paths"].append({
                            "file": "config/settings.yml",
                            "key": "workflow.material_collection.dest_dir",
                            "value": mc['dest_dir'],
                            "type": "素材目标目录"
                        })
                    if 'source_dirs' in mc:
                        for i, source_dir in enumerate(mc['source_dirs']):
                            config_analysis["hardcoded_paths"].append({
                                "file": "config/settings.yml",
                                "key": f"workflow.material_collection.source_dirs[{i}]",
                                "value": source_dir,
                                "type": "素材源目录"
                            })
                
            except Exception as e:
                self.deployment_issues.append(f"配置文件解析错误: {e}")
        
        # 检查敏感文件
        sensitive_files = [
            "config/auth_tokens.json",
            "config/browser_cookies.json",
            "config/codegen_state.json"
        ]
        
        for file_path in sensitive_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                config_analysis["sensitive_data"].append(file_path)
        
        return config_analysis
    
    def analyze_database_setup(self) -> Dict[str, Any]:
        """分析数据库设置"""
        print("🗄️ 分析数据库设置...")
        
        db_analysis = {
            "current_type": "unknown",
            "sqlite_files": [],
            "postgresql_config": {},
            "migration_needed": False
        }
        
        # 检查配置中的数据库类型
        settings_file = self.project_root / "config" / "settings.yml"
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = yaml.safe_load(f)
                
                db_config = settings.get('database', {})
                db_analysis["current_type"] = db_config.get('type', 'sqlite')
                
                if db_analysis["current_type"] == 'postgresql':
                    db_analysis["postgresql_config"] = db_config.get('postgresql', {})
                elif db_analysis["current_type"] == 'sqlite':
                    sqlite_config = db_config.get('sqlite', {})
                    db_name = sqlite_config.get('db_name', 'qianchuan_aw.db')
                    
                    # 查找SQLite文件
                    possible_locations = [
                        self.project_root / db_name,
                        self.project_root / "workflow_assets" / "database" / db_name
                    ]
                    
                    # 检查自定义工作流目录
                    custom_dir = settings.get('custom_workflow_assets_dir')
                    if custom_dir:
                        possible_locations.append(Path(custom_dir) / "database" / db_name)
                    
                    for location in possible_locations:
                        if location.exists():
                            db_analysis["sqlite_files"].append(str(location))
                            db_analysis["migration_needed"] = True
                
            except Exception as e:
                self.deployment_issues.append(f"数据库配置分析错误: {e}")
        
        return db_analysis
    
    def check_system_requirements(self) -> Dict[str, Any]:
        """检查系统要求"""
        print("🖥️ 检查系统要求...")
        
        system_check = {
            "python_version": sys.version,
            "platform": platform.platform(),
            "available_commands": {},
            "missing_requirements": []
        }
        
        # 检查关键命令
        commands_to_check = ['redis-server', 'redis-cli', 'pg_dump', 'psql']
        for cmd in commands_to_check:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                system_check["available_commands"][cmd] = result.returncode == 0
            except (subprocess.TimeoutExpired, FileNotFoundError):
                system_check["available_commands"][cmd] = False
                if cmd in ['redis-server', 'redis-cli']:
                    system_check["missing_requirements"].append(f"Redis未安装或不在PATH中")
        
        return system_check
    
    def generate_deployment_report(self) -> Dict[str, Any]:
        """生成完整的部署分析报告"""
        print("📋 生成部署分析报告...")
        
        report = {
            "analysis_time": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "project_structure": self.analyze_project_structure(),
            "dependencies": self.analyze_dependencies(),
            "configuration": self.analyze_configuration(),
            "database": self.analyze_database_setup(),
            "system_requirements": self.check_system_requirements(),
            "deployment_issues": self.deployment_issues,
            "migration_recommendations": self.generate_migration_recommendations()
        }
        
        return report
    
    def generate_migration_recommendations(self) -> List[Dict[str, Any]]:
        """生成迁移建议"""
        recommendations = []
        
        # 基础建议
        recommendations.append({
            "priority": "高",
            "category": "环境准备",
            "title": "安装Python环境和依赖",
            "description": "在新环境中安装Python 3.9+和所有依赖包",
            "action": "pip install -r requirements.txt"
        })
        
        recommendations.append({
            "priority": "高", 
            "category": "服务安装",
            "title": "安装Redis服务",
            "description": "安装并启动Redis服务用于任务队列",
            "action": "根据操作系统安装Redis并启动服务"
        })
        
        # 配置相关建议
        if any(item["type"] == "工作流资产目录" for item in self.analysis_result.get("configuration", {}).get("hardcoded_paths", [])):
            recommendations.append({
                "priority": "高",
                "category": "配置修改",
                "title": "修改工作流资产目录路径",
                "description": "更新config/settings.yml中的custom_workflow_assets_dir路径",
                "action": "编辑配置文件，设置适合新环境的路径"
            })
        
        return recommendations

def main():
    """主函数"""
    print("🚀 千川自动化项目部署分析工具")
    print("=" * 60)
    
    analyzer = QianchuanDeploymentAnalyzer()
    
    try:
        # 生成分析报告
        report = analyzer.generate_deployment_report()
        
        # 保存报告
        report_file = analyzer.project_root / "ai_reports" / "deployment" / f"deployment_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 分析报告已保存到: {report_file}")
        
        # 显示关键信息
        print("\n📊 分析结果摘要:")
        print(f"- 项目根目录: {report['project_root']}")
        print(f"- 核心文件数量: {len(report['project_structure']['core_files'])}")
        print(f"- 配置文件数量: {len(report['project_structure']['config_files'])}")
        print(f"- Python包依赖: {len(report['dependencies']['python_packages'])}")
        print(f"- 硬编码路径数量: {len(report['configuration']['hardcoded_paths'])}")
        print(f"- 数据库类型: {report['database']['current_type']}")
        print(f"- 部署问题数量: {len(report['deployment_issues'])}")
        
        if report['deployment_issues']:
            print("\n⚠️ 发现的问题:")
            for issue in report['deployment_issues']:
                print(f"  - {issue}")
        
        print(f"\n💡 迁移建议数量: {len(report['migration_recommendations'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
