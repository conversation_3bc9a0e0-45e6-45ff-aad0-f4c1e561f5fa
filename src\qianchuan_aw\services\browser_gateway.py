# -*- coding: utf-8 -*-
"""
[V66.0] BrowserGateway - 浏览器统一入口（最小实现）

目标：提供统一的浏览器获取与回收接口，便于后续集中限流、日志与策略切换。
当前最小实现支持两种模式：
- subprocess: 通过子进程浏览器服务获取上下文（默认，具备空闲超时与进程退出清理）
- native: 使用本进程内的 Playwright 同步上下文（复用现有 browser_service）

用法示例：
    from qianchuan_aw.services.browser_gateway import browser_context
    with browser_context(principal, app_settings, mode='subprocess') as ctx:
        page = ctx.new_page()
        page.goto(url)
"""
from __future__ import annotations

from contextlib import contextmanager
from typing import Any, Dict, Iterator, Optional

from qianchuan_aw.utils.logger import logger


@contextmanager
def browser_context(principal_name: str, app_settings: Dict[str, Any], mode: str = 'pool') -> Iterator[Any]:
    """统一的浏览器上下文入口（V2.0 优化版）

    Args:
        principal_name: 主体名称
        app_settings: 应用设置（settings.yml 加载结果）
        mode: 'pool' | 'subprocess' | 'native'

    Yields:
        一个带有 new_page() 方法的上下文对象：
        - pool 模式：使用浏览器进程池（推荐，高效复用）
        - subprocess 模式：返回 SubprocessBrowserAdapter（兼容模式）
        - native 模式：返回原生 BrowserContext（简单模式）
    """
    mode = (mode or 'pool').lower()

    if mode == 'pool':
        # 🚀 [V2.0] 使用浏览器进程池，提升效率10倍
        try:
            import asyncio
            from qianchuan_aw.utils.browser_process_pool import get_browser_pool

            # 检查是否在异步环境中
            try:
                loop = asyncio.get_running_loop()
                # 在异步环境中，使用异步浏览器进程池
                logger.debug(f"🔄 使用异步浏览器进程池为 '{principal_name}' 创建上下文")

                async def _async_browser_context():
                    browser_pool = await get_browser_pool()
                    async with browser_pool.get_browser_context(principal_name) as context:
                        yield context

                # 返回异步上下文管理器
                return _async_browser_context()

            except RuntimeError:
                # 不在异步环境中，使用同步适配器
                logger.debug(f"🔄 使用同步浏览器进程池适配器为 '{principal_name}' 创建上下文")
                from qianchuan_aw.utils.browser_process_pool_sync_adapter import SyncBrowserPoolAdapter

                adapter = SyncBrowserPoolAdapter()
                with adapter.get_browser_context(principal_name, app_settings) as ctx:
                    yield ctx

        except Exception as e:
            logger.warning(f"⚠️ 浏览器进程池失败，降级到subprocess模式: {e}")
            # 降级到subprocess模式
            mode = 'subprocess'

    if mode == 'subprocess':
        # 使用子进程浏览器服务（具备 idle 超时与 atexit 清理），适合长期运行场景
        try:
            from qianchuan_aw.services.subprocess_browser_service import subprocess_managed_browser_context
            with subprocess_managed_browser_context(principal_name, app_settings) as ctx:
                yield ctx
        except Exception as e:
            logger.error(f"BrowserGateway(subprocess) 获取上下文失败: {e}")
            raise

    elif mode == 'native':
        # 复用现有的原生上下文创建逻辑（短生命周期）
        try:
            from qianchuan_aw.services.browser_service import create_browser_context, close_browser_context
            context = create_browser_context(principal_name, app_settings)
            if not context:
                raise RuntimeError("原生浏览器上下文创建失败")
            try:
                yield context
            finally:
                close_browser_context(context)
        except Exception as e:
            logger.error(f"BrowserGateway(native) 获取上下文失败: {e}")
            raise

    else:
        raise ValueError(f"不支持的浏览器模式: {mode}")

