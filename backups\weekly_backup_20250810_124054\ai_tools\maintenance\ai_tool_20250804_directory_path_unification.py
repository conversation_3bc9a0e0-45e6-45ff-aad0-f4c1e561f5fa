#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 彻底统一入库文件目录路径，确保只使用01_materials_to_process
清理条件: 成为项目核心维护工具，长期保留
"""

import os
import sys
import shutil
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def analyze_directory_structure():
    """分析目录结构"""
    logger.info("🔍 分析工作流目录结构...")
    
    base_dir = "D:/workflow_assets"
    directories_found = []
    
    if not os.path.exists(base_dir):
        logger.error(f"❌ 基础目录不存在: {base_dir}")
        return directories_found
    
    # 扫描所有可能的入库目录
    potential_dirs = [
        "01_to_process",
        "01_materials_to_process", 
        "01_input",
        "01_pending"
    ]
    
    for dir_name in potential_dirs:
        full_path = os.path.join(base_dir, dir_name)
        if os.path.exists(full_path):
            # 检查是否有缇萃百货子目录
            titui_path = os.path.join(full_path, "缇萃百货")
            if os.path.exists(titui_path):
                files = [f for f in os.listdir(titui_path) if f.endswith('.mp4')]
                directories_found.append({
                    'path': full_path,
                    'titui_path': titui_path,
                    'file_count': len(files),
                    'files': files[:10]  # 只显示前10个文件
                })
                logger.info(f"📁 发现目录: {full_path}")
                logger.info(f"   缇萃百货子目录: {len(files)} 个文件")
    
    return directories_found

def check_database_paths():
    """检查数据库中的路径引用"""
    logger.info("🔍 检查数据库中的路径引用...")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查询所有不同的路径模式
            path_patterns = db.query(
                db.func.substring(LocalCreative.file_path, r'D:/workflow_assets\\([^\\]+)').label('dir_pattern'),
                db.func.count(LocalCreative.id).label('count')
            ).group_by(
                db.func.substring(LocalCreative.file_path, r'D:/workflow_assets\\([^\\]+)')
            ).all()
            
            logger.info("📊 数据库中的路径模式:")
            db_paths = {}
            for pattern, count in path_patterns:
                if pattern:
                    db_paths[pattern] = count
                    logger.info(f"   {pattern}: {count} 条记录")
            
            # 查询具体的01_to_process路径记录
            old_path_records = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('%01_to_process%')
            ).all()
            
            logger.info(f"📋 仍使用旧路径的记录: {len(old_path_records)} 条")
            
            return db_paths, old_path_records
            
    except Exception as e:
        logger.error(f"❌ 数据库查询失败: {e}")
        return {}, []

def check_code_references():
    """检查代码中的路径引用"""
    logger.info("🔍 检查代码中的路径引用...")
    
    code_references = []
    
    # 要检查的文件模式
    files_to_check = [
        "src/qianchuan_aw/workflows/scheduler.py",
        "src/qianchuan_aw/utils/workflow_status.py", 
        "config/settings.yml",
        "src/qianchuan_aw/workflows/tasks.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含旧路径引用
                if '01_to_process' in content:
                    lines_with_old_path = []
                    for i, line in enumerate(content.split('\n'), 1):
                        if '01_to_process' in line:
                            lines_with_old_path.append(f"第{i}行: {line.strip()}")
                    
                    if lines_with_old_path:
                        code_references.append({
                            'file': file_path,
                            'references': lines_with_old_path
                        })
                        logger.warning(f"⚠️ {file_path} 中发现旧路径引用:")
                        for ref in lines_with_old_path:
                            logger.warning(f"   {ref}")
                
            except Exception as e:
                logger.error(f"❌ 读取文件失败 {file_path}: {e}")
    
    return code_references

def migrate_files_to_unified_directory(directories_found):
    """迁移文件到统一目录"""
    logger.info("🔄 迁移文件到统一目录...")
    
    target_dir = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    migrated_files = []
    
    # 确保目标目录存在
    os.makedirs(target_dir, exist_ok=True)
    
    for dir_info in directories_found:
        source_path = dir_info['titui_path']
        
        # 跳过目标目录本身
        if source_path == target_dir:
            logger.info(f"✅ 目标目录: {source_path} (跳过)")
            continue
        
        logger.info(f"🔄 处理源目录: {source_path}")
        
        if not os.path.exists(source_path):
            continue
        
        # 迁移文件
        for filename in os.listdir(source_path):
            if filename.endswith('.mp4'):
                source_file = os.path.join(source_path, filename)
                target_file = os.path.join(target_dir, filename)
                
                # 检查目标文件是否已存在
                if os.path.exists(target_file):
                    # 比较文件大小
                    source_size = os.path.getsize(source_file)
                    target_size = os.path.getsize(target_file)
                    
                    if source_size == target_size:
                        logger.info(f"   📄 文件已存在且相同: {filename}")
                        # 删除源文件
                        os.remove(source_file)
                        migrated_files.append({
                            'filename': filename,
                            'action': 'removed_duplicate',
                            'source': source_file,
                            'target': target_file
                        })
                    else:
                        # 重命名后移动
                        base_name, ext = os.path.splitext(filename)
                        new_filename = f"{base_name}_migrated_{datetime.now().strftime('%H%M%S')}{ext}"
                        new_target = os.path.join(target_dir, new_filename)
                        shutil.move(source_file, new_target)
                        logger.success(f"   📄 文件已重命名迁移: {filename} -> {new_filename}")
                        migrated_files.append({
                            'filename': filename,
                            'action': 'moved_renamed',
                            'source': source_file,
                            'target': new_target
                        })
                else:
                    # 直接移动
                    shutil.move(source_file, target_file)
                    logger.success(f"   📄 文件已迁移: {filename}")
                    migrated_files.append({
                        'filename': filename,
                        'action': 'moved',
                        'source': source_file,
                        'target': target_file
                    })
    
    return migrated_files

def cleanup_empty_directories(directories_found):
    """清理空目录"""
    logger.info("🧹 清理空目录...")
    
    cleaned_dirs = []
    target_dir = "D:/workflow_assets/01_materials_to_process"
    
    for dir_info in directories_found:
        dir_path = dir_info['path']
        
        # 跳过目标目录
        if dir_path == target_dir:
            continue
        
        titui_path = dir_info['titui_path']
        
        # 检查缇萃百货目录是否为空
        if os.path.exists(titui_path):
            files = os.listdir(titui_path)
            if not files:
                logger.info(f"🗑️ 删除空的缇萃百货目录: {titui_path}")
                os.rmdir(titui_path)
                cleaned_dirs.append(titui_path)
        
        # 检查父目录是否为空
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            if not files:
                logger.info(f"🗑️ 删除空的父目录: {dir_path}")
                os.rmdir(dir_path)
                cleaned_dirs.append(dir_path)
    
    return cleaned_dirs

def update_database_paths():
    """更新数据库中的路径"""
    logger.info("🔧 更新数据库中的路径...")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 更新所有旧路径
            updated_count = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('%01_to_process%')
            ).update({
                LocalCreative.file_path: db.func.replace(
                    LocalCreative.file_path, 
                    '01_to_process', 
                    '01_materials_to_process'
                ),
                LocalCreative.updated_at: datetime.now()
            }, synchronize_session=False)
            
            db.commit()
            logger.success(f"✅ 更新数据库路径: {updated_count} 条记录")
            return updated_count
            
    except Exception as e:
        logger.error(f"❌ 更新数据库路径失败: {e}")
        return 0

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 入库文件目录路径统一工具")
    logger.info("=" * 80)
    
    # 1. 分析目录结构
    directories_found = analyze_directory_structure()
    
    # 2. 检查数据库路径
    db_paths, old_path_records = check_database_paths()
    
    # 3. 检查代码引用
    code_references = check_code_references()
    
    # 4. 迁移文件
    migrated_files = migrate_files_to_unified_directory(directories_found)
    
    # 5. 更新数据库路径
    db_updated_count = update_database_paths()
    
    # 6. 清理空目录
    cleaned_dirs = cleanup_empty_directories(directories_found)
    
    # 生成报告
    logger.info("=" * 80)
    logger.info("🎯 统一结果总结:")
    logger.info(f"   📁 发现目录: {len(directories_found)} 个")
    logger.info(f"   📄 迁移文件: {len(migrated_files)} 个")
    logger.info(f"   💾 更新数据库: {db_updated_count} 条")
    logger.info(f"   🗑️ 清理目录: {len(cleaned_dirs)} 个")
    logger.info(f"   ⚠️ 代码引用: {len(code_references)} 处")
    
    if code_references:
        logger.warning("\n⚠️ 需要手动修复的代码引用:")
        for ref in code_references:
            logger.warning(f"   📄 {ref['file']}")
    
    logger.info("\n✅ 统一目标目录: D:/workflow_assets/01_materials_to_process/缇萃百货")
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
