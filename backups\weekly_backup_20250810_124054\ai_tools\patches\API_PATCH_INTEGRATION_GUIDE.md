
# API调用增强补丁集成指南

## 问题背景
数据库中存在非数字计划ID（如TEST_TRIGGER_12345），导致千川API调用失败：
`filtering: ids.1: Value must be an integer`

## 解决方案
在所有涉及计划ID的API调用前，使用filter_numeric_campaign_ids函数过滤非数字ID。

## 集成位置
主要修改文件：src/qianchuan_aw/sdk_qc/client.py

### 修改示例：
```python
# 在get_ad_plan_list方法中
def get_ad_plan_list(self, advertiser_id, campaign_ids=None, **kwargs):
    # 添加ID过滤
    if campaign_ids:
        campaign_ids = filter_numeric_campaign_ids(campaign_ids)
        if not campaign_ids:
            logger.warning("所有计划ID都被过滤，跳过API调用")
            return []
    
    # 继续原有逻辑...
```

## 验证方法
修改后运行提审模块，确认不再出现"Value must be an integer"错误。

创建时间: 2025-07-28 08:48:51.560124
