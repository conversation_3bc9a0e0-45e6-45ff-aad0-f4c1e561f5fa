#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时演示工具
生命周期: 7天
创建目的: 演示统一账户选择器功能
依赖关系: Streamlit, 统一账户选择器组件
清理条件: 演示完成后可删除
"""

import sys
import os
import streamlit as st
from pathlib import Path

# 必须在所有其他Streamlit命令之前设置页面配置
st.set_page_config(
    page_title="统一账户选择器演示",
    page_icon="⭐",
    layout="wide"
)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

try:
    from ai_tool_20250718_maintenance_unified_account_selector import (
        create_single_account_selector,
        create_multi_account_selector,
        get_account_options_dict,
        get_accounts_with_favorites
    )
except ImportError as e:
    st.error(f"导入统一账户选择器失败: {e}")
    st.stop()

def main():
    st.title("⭐ 统一账户选择器演示")
    st.markdown("---")
    
    st.markdown("""
    ## 🎯 功能特点
    
    ✅ **统一的用户体验**：所有账户选择都使用相同的界面和交互方式  
    ✅ **⭐ 收藏功能**：支持收藏常用账户，收藏账户自动置顶显示  
    ✅ **智能筛选**：支持显示全部、仅收藏、仅未收藏三种筛选模式  
    ✅ **实时统计**：显示收藏账户数量和总账户数量  
    ✅ **批量管理**：支持批量添加和取消收藏  
    ✅ **单选/多选**：支持单选和多选两种模式  
    ✅ **状态显示**：显示抖音号授权状态等额外信息  
    """)
    
    st.markdown("---")
    
    # 获取账户数据
    accounts = get_accounts_with_favorites()
    if not accounts:
        st.error("无法获取账户数据，请检查数据库连接")
        return
    
    favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
    st.info(f"📊 数据概览：共 {len(accounts)} 个账户，其中 {favorite_count} 个收藏账户")
    
    # 演示区域
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 🔸 单选账户选择器")
        st.markdown("适用于：手动批量投放、优质素材收割等需要选择单个账户的场景")
        
        selected_single = create_single_account_selector(
            key="demo_single",
            label="选择一个广告账户",
            show_filter=True,
            show_stats=True,
            show_aweme_status=True
        )
        
        if selected_single:
            st.success(f"✅ 已选择: {selected_single.name}")
            st.json({
                "账户名称": selected_single.name,
                "千川ID": selected_single.account_id_qc,
                "账户类型": selected_single.account_type,
                "是否收藏": getattr(selected_single, 'is_favorite', False),
                "抖音号": selected_single.aweme_id if hasattr(selected_single, 'aweme_id') else None
            })
    
    with col2:
        st.markdown("### 🔸 多选账户选择器")
        st.markdown("适用于：高级复制工具、抖音号授权等需要选择多个账户的场景")
        
        selected_multi = create_multi_account_selector(
            key="demo_multi",
            label="选择多个广告账户",
            show_filter=True,
            show_stats=True,
            show_aweme_status=True
        )
        
        if selected_multi:
            st.success(f"✅ 已选择 {len(selected_multi)} 个账户")
            
            # 显示选中的账户
            for i, account in enumerate(selected_multi, 1):
                is_favorite = getattr(account, 'is_favorite', False)
                star = "⭐ " if is_favorite else ""
                st.write(f"{i}. {star}{account.name} ({account.account_id_qc})")
    
    st.markdown("---")
    
    # 兼容性演示
    st.markdown("### 🔄 兼容性支持")
    st.markdown("为了保持与现有代码的兼容性，我们提供了传统格式的账户选项字典：")
    
    with st.expander("查看传统格式的账户选项"):
        options_dict = get_account_options_dict(accounts)
        
        st.markdown("**格式**: `显示名称 -> 千川ID`")
        
        # 显示前10个选项
        for i, (display_name, account_id) in enumerate(list(options_dict.items())[:10]):
            st.write(f"`{display_name}` → `{account_id}`")
        
        if len(options_dict) > 10:
            st.write(f"... 还有 {len(options_dict) - 10} 个选项")
    
    st.markdown("---")
    
    # 使用指南
    st.markdown("### 📖 使用指南")
    
    with st.expander("如何在代码中使用统一账户选择器"):
        st.code("""
# 1. 导入组件
from ai_tool_20250718_maintenance_unified_account_selector import (
    create_single_account_selector,
    create_multi_account_selector
)

# 2. 单选账户选择器
selected_account = create_single_account_selector(
    key="my_single_selector",
    label="选择广告账户",
    show_filter=True,
    show_stats=True
)

if selected_account:
    account_id = selected_account.account_id_qc
    account_name = selected_account.name

# 3. 多选账户选择器
selected_accounts = create_multi_account_selector(
    key="my_multi_selector",
    label="选择多个广告账户",
    show_filter=True,
    show_stats=True
)

if selected_accounts:
    account_ids = [acc.account_id_qc for acc in selected_accounts]
    account_names = [acc.name for acc in selected_accounts]
        """, language="python")
    
    with st.expander("替换现有代码的步骤"):
        st.markdown("""
        **步骤1**: 替换账户数据获取
        ```python
        # 旧代码
        accounts_df = get_all_ad_accounts_df()
        account_options = {f"{row['账户名称']} ({row['千川ID']})": row['千川ID'] for _, row in accounts_df.iterrows()}
        selected_account_display = st.selectbox("选择账户", options=list(account_options.keys()))
        
        # 新代码
        selected_account = create_single_account_selector(
            key="account_selector",
            label="选择账户"
        )
        ```
        
        **步骤2**: 替换账户ID获取
        ```python
        # 旧代码
        if selected_account_display:
            account_id = account_options[selected_account_display]
        
        # 新代码
        if selected_account:
            account_id = selected_account.account_id_qc
        ```
        """)
    
    st.markdown("---")
    st.markdown("### 🎉 统一账户选择器让用户体验更加一致和便捷！")

if __name__ == "__main__":
    main()
