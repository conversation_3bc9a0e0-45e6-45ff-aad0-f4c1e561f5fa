#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目性能监控和验证工具
清理条件: 性能稳定后可归档
"""

import os
import sys
import time
import psutil
import redis
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, deque

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.project_root = project_root
        self.log_file = self.project_root / 'logs' / f'app_{datetime.now().strftime("%Y-%m-%d")}.log'
        self.monitoring_duration = 30  # 监控30分钟
        self.check_interval = 60  # 每60秒检查一次
        
        # 性能指标
        self.metrics = {
            'task_intervals': deque(maxlen=50),
            'api_success_rate': deque(maxlen=100),
            'system_resources': deque(maxlen=30),
            'celery_queue_size': deque(maxlen=30)
        }
        
        # 目标指标
        self.targets = {
            'max_task_interval': 120,  # 最大任务间隔2分钟
            'min_api_success_rate': 95,  # API成功率95%
            'max_cpu_usage': 80,  # CPU使用率80%
            'max_memory_usage': 85,  # 内存使用率85%
        }
    
    def run_continuous_monitoring(self):
        """运行连续性能监控"""
        logger.critical("📊 开始千川自动化项目性能监控")
        logger.critical("=" * 80)
        logger.critical(f"⏱️ 监控时长: {self.monitoring_duration}分钟")
        logger.critical(f"🔄 检查间隔: {self.check_interval}秒")
        logger.critical("")
        
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=self.monitoring_duration)
        
        try:
            while datetime.now() < end_time:
                # 收集性能指标
                self._collect_task_performance()
                self._collect_api_performance()
                self._collect_system_resources()
                self._collect_celery_metrics()
                
                # 分析和报告
                self._analyze_performance()
                
                # 等待下次检查
                time.sleep(self.check_interval)
            
            # 生成最终报告
            self._generate_final_report()
            
        except KeyboardInterrupt:
            logger.info("⏹️ 监控被用户中断")
            self._generate_final_report()
        except Exception as e:
            logger.error(f"❌ 监控过程中发生错误: {e}")
    
    def _collect_task_performance(self):
        """收集任务执行性能"""
        try:
            if not self.log_file.exists():
                return
            
            # 分析最近的任务执行日志
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找任务执行时间戳
            task_timestamps = []
            current_time = datetime.now()
            one_hour_ago = current_time - timedelta(hours=1)
            
            for line in lines[-1000:]:  # 只检查最近1000行
                if any(keyword in line for keyword in [
                    "开始扫描所有计划中的素材状态",
                    "开始处理计划创建任务",
                    "开始素材监控工作流",
                    "开始组分发工作流"
                ]):
                    try:
                        timestamp_str = line.split(' | ')[0]
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                        if timestamp > one_hour_ago:
                            task_timestamps.append(timestamp)
                    except:
                        continue
            
            # 计算任务间隔
            if len(task_timestamps) >= 2:
                intervals = []
                for i in range(1, len(task_timestamps)):
                    interval = (task_timestamps[i] - task_timestamps[i-1]).total_seconds()
                    intervals.append(interval)
                
                if intervals:
                    avg_interval = sum(intervals) / len(intervals)
                    self.metrics['task_intervals'].append(avg_interval)
                    
                    logger.debug(f"📈 任务执行间隔: {avg_interval:.1f}秒")
            
        except Exception as e:
            logger.debug(f"收集任务性能数据失败: {e}")
    
    def _collect_api_performance(self):
        """收集API性能数据"""
        try:
            if not self.log_file.exists():
                return
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 统计API调用成功率
            api_calls = 0
            api_errors = 0
            
            for line in lines[-500:]:  # 检查最近500行
                if "API调用" in line or "请求成功" in line:
                    api_calls += 1
                elif any(error in line for error in ["API调用失败", "请求失败", "超时", "限流"]):
                    api_errors += 1
                    api_calls += 1
            
            if api_calls > 0:
                success_rate = ((api_calls - api_errors) / api_calls) * 100
                self.metrics['api_success_rate'].append(success_rate)
                logger.debug(f"📡 API成功率: {success_rate:.1f}%")
            
        except Exception as e:
            logger.debug(f"收集API性能数据失败: {e}")
    
    def _collect_system_resources(self):
        """收集系统资源使用情况"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            resource_data = {
                'cpu': cpu_percent,
                'memory': memory_percent,
                'disk': disk_percent,
                'timestamp': datetime.now()
            }
            
            self.metrics['system_resources'].append(resource_data)
            logger.debug(f"💻 系统资源: CPU {cpu_percent}%, 内存 {memory_percent}%, 磁盘 {disk_percent}%")
            
        except Exception as e:
            logger.debug(f"收集系统资源数据失败: {e}")
    
    def _collect_celery_metrics(self):
        """收集Celery队列指标"""
        try:
            # 连接Redis获取队列信息
            r = redis.Redis(host='localhost', port=6379, db=0)
            
            # 获取队列长度
            queue_length = r.llen('celery')
            
            # 获取活跃任务数（如果可能）
            active_tasks = 0  # 这需要更复杂的实现
            
            celery_data = {
                'queue_length': queue_length,
                'active_tasks': active_tasks,
                'timestamp': datetime.now()
            }
            
            self.metrics['celery_queue_size'].append(celery_data)
            logger.debug(f"🔄 Celery队列: {queue_length}个待处理任务")
            
        except Exception as e:
            logger.debug(f"收集Celery指标失败: {e}")
    
    def _analyze_performance(self):
        """分析性能指标"""
        current_time = datetime.now().strftime('%H:%M:%S')
        logger.info(f"⏰ [{current_time}] 性能分析报告:")
        
        # 分析任务间隔
        if self.metrics['task_intervals']:
            avg_interval = sum(self.metrics['task_intervals']) / len(self.metrics['task_intervals'])
            if avg_interval <= self.targets['max_task_interval']:
                logger.success(f"  ✅ 任务间隔: {avg_interval:.1f}秒 (目标: <{self.targets['max_task_interval']}秒)")
            else:
                logger.warning(f"  ⚠️ 任务间隔: {avg_interval:.1f}秒 (超出目标: {self.targets['max_task_interval']}秒)")
        
        # 分析API成功率
        if self.metrics['api_success_rate']:
            avg_success_rate = sum(self.metrics['api_success_rate']) / len(self.metrics['api_success_rate'])
            if avg_success_rate >= self.targets['min_api_success_rate']:
                logger.success(f"  ✅ API成功率: {avg_success_rate:.1f}% (目标: >{self.targets['min_api_success_rate']}%)")
            else:
                logger.warning(f"  ⚠️ API成功率: {avg_success_rate:.1f}% (低于目标: {self.targets['min_api_success_rate']}%)")
        
        # 分析系统资源
        if self.metrics['system_resources']:
            latest_resources = self.metrics['system_resources'][-1]
            cpu_usage = latest_resources['cpu']
            memory_usage = latest_resources['memory']
            
            if cpu_usage <= self.targets['max_cpu_usage']:
                logger.success(f"  ✅ CPU使用率: {cpu_usage}% (目标: <{self.targets['max_cpu_usage']}%)")
            else:
                logger.warning(f"  ⚠️ CPU使用率: {cpu_usage}% (超出目标: {self.targets['max_cpu_usage']}%)")
            
            if memory_usage <= self.targets['max_memory_usage']:
                logger.success(f"  ✅ 内存使用率: {memory_usage}% (目标: <{self.targets['max_memory_usage']}%)")
            else:
                logger.warning(f"  ⚠️ 内存使用率: {memory_usage}% (超出目标: {self.targets['max_memory_usage']}%)")
        
        # 分析Celery队列
        if self.metrics['celery_queue_size']:
            latest_queue = self.metrics['celery_queue_size'][-1]
            queue_length = latest_queue['queue_length']
            
            if queue_length <= 10:
                logger.success(f"  ✅ Celery队列: {queue_length}个任务 (正常)")
            elif queue_length <= 50:
                logger.warning(f"  ⚠️ Celery队列: {queue_length}个任务 (较多)")
            else:
                logger.error(f"  ❌ Celery队列: {queue_length}个任务 (积压严重)")
        
        logger.info("")
    
    def _generate_final_report(self):
        """生成最终性能报告"""
        logger.critical("📋 最终性能监控报告")
        logger.critical("=" * 50)
        
        # 任务间隔统计
        if self.metrics['task_intervals']:
            intervals = list(self.metrics['task_intervals'])
            avg_interval = sum(intervals) / len(intervals)
            min_interval = min(intervals)
            max_interval = max(intervals)
            
            logger.info(f"⏱️ 任务执行间隔统计:")
            logger.info(f"  - 平均间隔: {avg_interval:.1f}秒")
            logger.info(f"  - 最短间隔: {min_interval:.1f}秒")
            logger.info(f"  - 最长间隔: {max_interval:.1f}秒")
            logger.info(f"  - 样本数量: {len(intervals)}")
            
            if avg_interval <= 120:
                logger.success(f"  ✅ 性能目标达成: 平均间隔 {avg_interval:.1f}秒 < 120秒")
            else:
                logger.warning(f"  ⚠️ 性能目标未达成: 平均间隔 {avg_interval:.1f}秒 > 120秒")
        
        # API成功率统计
        if self.metrics['api_success_rate']:
            success_rates = list(self.metrics['api_success_rate'])
            avg_success_rate = sum(success_rates) / len(success_rates)
            min_success_rate = min(success_rates)
            
            logger.info(f"📡 API性能统计:")
            logger.info(f"  - 平均成功率: {avg_success_rate:.1f}%")
            logger.info(f"  - 最低成功率: {min_success_rate:.1f}%")
            logger.info(f"  - 样本数量: {len(success_rates)}")
            
            if avg_success_rate >= 95:
                logger.success(f"  ✅ API性能优秀: 成功率 {avg_success_rate:.1f}% >= 95%")
            else:
                logger.warning(f"  ⚠️ API性能需要改进: 成功率 {avg_success_rate:.1f}% < 95%")
        
        # 系统资源统计
        if self.metrics['system_resources']:
            resources = list(self.metrics['system_resources'])
            avg_cpu = sum(r['cpu'] for r in resources) / len(resources)
            avg_memory = sum(r['memory'] for r in resources) / len(resources)
            max_cpu = max(r['cpu'] for r in resources)
            max_memory = max(r['memory'] for r in resources)
            
            logger.info(f"💻 系统资源统计:")
            logger.info(f"  - 平均CPU使用率: {avg_cpu:.1f}%")
            logger.info(f"  - 平均内存使用率: {avg_memory:.1f}%")
            logger.info(f"  - 最高CPU使用率: {max_cpu:.1f}%")
            logger.info(f"  - 最高内存使用率: {max_memory:.1f}%")
            
            if max_cpu <= 80 and max_memory <= 85:
                logger.success("  ✅ 系统资源使用正常")
            else:
                logger.warning("  ⚠️ 系统资源使用较高，建议监控")
        
        # 优化建议
        logger.critical("💡 优化建议:")
        
        if self.metrics['task_intervals']:
            avg_interval = sum(self.metrics['task_intervals']) / len(self.metrics['task_intervals'])
            if avg_interval > 120:
                logger.info("  - 任务间隔仍然较长，建议进一步优化API频率限制")
                logger.info("  - 检查是否存在任务阻塞或依赖问题")
            else:
                logger.info("  - 任务调度性能良好，继续保持当前配置")
        
        if self.metrics['api_success_rate']:
            avg_success_rate = sum(self.metrics['api_success_rate']) / len(self.metrics['api_success_rate'])
            if avg_success_rate < 95:
                logger.info("  - API成功率偏低，建议检查网络连接和API配置")
                logger.info("  - 考虑增加重试机制和错误处理")
        
        logger.critical("📊 监控完成！")

def main():
    """主函数"""
    monitor = PerformanceMonitor()
    monitor.run_continuous_monitoring()

if __name__ == "__main__":
    main()
