#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提审失败分析和修复工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 深度分析提审失败问题，识别根本原因并提供修复方案
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class AppealFailureAnalyzer:
    """提审失败分析器"""
    
    def __init__(self):
        self.analysis_results = {}
        self.fixes_applied = []
        self.recommendations = []
    
    def analyze_appeal_status_distribution(self) -> Dict:
        """分析提审状态分布"""
        print("📊 分析提审状态分布...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import func, text
            
            with database_session() as db:
                # 近7天的提审状态分布
                status_distribution = db.execute(text("""
                    SELECT 
                        status,
                        appeal_status,
                        COUNT(*) as count,
                        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage,
                        AVG(appeal_attempt_count) as avg_attempts
                    FROM campaigns 
                    WHERE created_at >= NOW() - INTERVAL '7 days'
                    GROUP BY status, appeal_status
                    ORDER BY count DESC
                """)).fetchall()
                
                # 提审超时的计划
                timeout_campaigns = db.execute(text("""
                    SELECT 
                        COUNT(*) as timeout_count,
                        AVG(EXTRACT(EPOCH FROM (NOW() - created_at))/3600) as avg_hours_waiting
                    FROM campaigns 
                    WHERE status = 'APPEAL_TIMEOUT' 
                      AND appeal_status = 'appeal_pending'
                      AND created_at >= NOW() - INTERVAL '7 days'
                """)).fetchone()
                
                # 提审失败的计划
                failed_campaigns = db.execute(text("""
                    SELECT 
                        id,
                        campaign_id_qc,
                        appeal_error_message,
                        appeal_attempt_count,
                        created_at
                    FROM campaigns 
                    WHERE appeal_status = 'appeal_failed'
                      AND created_at >= NOW() - INTERVAL '7 days'
                    ORDER BY created_at DESC
                    LIMIT 10
                """)).fetchall()
                
                return {
                    'status_distribution': [dict(row._mapping) for row in status_distribution],
                    'timeout_info': dict(timeout_campaigns._mapping) if timeout_campaigns else {},
                    'failed_campaigns': [dict(row._mapping) for row in failed_campaigns]
                }
                
        except Exception as e:
            print(f"❌ 分析提审状态失败: {e}")
            return {}
    
    def analyze_appeal_api_issues(self) -> Dict:
        """分析提审API问题"""
        print("🔍 分析提审API问题...")
        
        api_issues = {
            'login_issues': 0,
            'permission_issues': 0,
            'network_issues': 0,
            'parameter_issues': 0,
            'unknown_issues': 0,
            'common_errors': []
        }
        
        try:
            # 分析日志文件中的提审错误
            log_files = list(Path("logs").glob("*.log")) if Path("logs").exists() else []
            
            error_patterns = {
                'login_issues': ['未登录', 'login', '登录已过期', 'authentication'],
                'permission_issues': ['权限不足', 'permission', 'unauthorized', '无权限'],
                'network_issues': ['网络', 'timeout', 'connection', '连接'],
                'parameter_issues': ['参数', 'parameter', 'invalid', '格式错误'],
            }
            
            for log_file in log_files[-3:]:  # 检查最近3个日志文件
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # 查找提审相关错误
                        if '提审' in content or 'appeal' in content:
                            for issue_type, patterns in error_patterns.items():
                                for pattern in patterns:
                                    if pattern in content.lower():
                                        api_issues[issue_type] += content.lower().count(pattern)
                        
                        # 提取常见错误消息
                        import re
                        error_matches = re.findall(r'提审.*?失败.*?[:：](.+)', content)
                        api_issues['common_errors'].extend(error_matches[:5])
                        
                except Exception:
                    continue
            
            return api_issues
            
        except Exception as e:
            print(f"❌ 分析API问题失败: {e}")
            return api_issues
    
    def test_appeal_api_connectivity(self) -> Dict:
        """测试提审API连通性"""
        print("🌐 测试提审API连通性...")
        
        connectivity_test = {
            'qianchuan_accessible': False,
            'login_status': False,
            'api_endpoints_accessible': False,
            'response_time': 0,
            'error_message': None
        }
        
        try:
            import requests
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry
            
            # 配置重试策略
            session = requests.Session()
            retry_strategy = Retry(
                total=3,
                backoff_factor=1,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            # 测试千川平台访问
            start_time = time.time()
            response = session.get(
                "https://qianchuan.jinritemai.com/",
                timeout=10,
                headers={
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            )
            
            connectivity_test['response_time'] = time.time() - start_time
            
            if response.status_code == 200:
                connectivity_test['qianchuan_accessible'] = True
                
                # 检查是否需要登录
                if '登录' not in response.text and 'login' not in response.text.lower():
                    connectivity_test['login_status'] = True
                
                print(f"✅ 千川平台可访问，响应时间: {connectivity_test['response_time']:.2f}秒")
            else:
                connectivity_test['error_message'] = f"HTTP {response.status_code}"
                print(f"❌ 千川平台访问异常: {response.status_code}")
            
        except Exception as e:
            connectivity_test['error_message'] = str(e)
            print(f"❌ 连通性测试失败: {e}")
        
        return connectivity_test
    
    def identify_stuck_appeals(self) -> List[Dict]:
        """识别卡住的提审"""
        print("🔍 识别卡住的提审...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from sqlalchemy import text
            
            with database_session() as db:
                # 查找长时间处于appeal_pending状态的计划
                stuck_appeals = db.execute(text("""
                    SELECT 
                        id,
                        campaign_id_qc,
                        status,
                        appeal_status,
                        appeal_attempt_count,
                        created_at,
                        last_updated,
                        EXTRACT(EPOCH FROM (NOW() - created_at))/3600 as hours_stuck
                    FROM campaigns 
                    WHERE appeal_status = 'appeal_pending'
                      AND status IN ('APPEAL_TIMEOUT', 'AUDITING')
                      AND created_at < NOW() - INTERVAL '2 hours'  -- 超过2小时
                    ORDER BY created_at ASC
                    LIMIT 20
                """)).fetchall()
                
                stuck_list = []
                for row in stuck_appeals:
                    stuck_info = dict(row._mapping)
                    
                    # 判断卡住的可能原因
                    if stuck_info['hours_stuck'] > 24:
                        stuck_info['stuck_reason'] = '长期超时，可能提审未真正执行'
                    elif stuck_info['appeal_attempt_count'] == 0:
                        stuck_info['stuck_reason'] = '未尝试提审，调度器可能未触发'
                    elif stuck_info['appeal_attempt_count'] >= 3:
                        stuck_info['stuck_reason'] = '多次重试失败，需要人工干预'
                    else:
                        stuck_info['stuck_reason'] = '提审中，等待平台审核'
                    
                    stuck_list.append(stuck_info)
                
                print(f"📊 发现 {len(stuck_list)} 个卡住的提审")
                return stuck_list
                
        except Exception as e:
            print(f"❌ 识别卡住提审失败: {e}")
            return []
    
    def fix_stuck_appeals(self, stuck_appeals: List[Dict]) -> int:
        """修复卡住的提审"""
        print("🔧 修复卡住的提审...")
        
        fixed_count = 0
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import text

            with database_session() as db:
                for appeal in stuck_appeals:
                    try:
                        campaign_id = appeal['id']
                        hours_stuck = appeal['hours_stuck']
                        attempt_count = appeal['appeal_attempt_count']

                        # 根据不同情况采取不同修复策略
                        if hours_stuck > 48 and attempt_count == 0:
                            # 长期未提审，重置状态触发重新提审
                            db.execute(text("""
                                UPDATE campaigns
                                SET appeal_status = NULL,
                                    appeal_attempt_count = 0,
                                    last_updated = NOW()
                                WHERE id = :campaign_id
                            """), {'campaign_id': campaign_id})

                            self.fixes_applied.append(f"重置计划 {appeal['campaign_id_qc']} 提审状态")
                            fixed_count += 1

                        elif hours_stuck > 24 and attempt_count >= 3:
                            # 多次失败，标记为需要人工处理
                            db.execute(text("""
                                UPDATE campaigns
                                SET appeal_status = 'appeal_failed',
                                    appeal_error_message = '多次重试失败，需要人工干预',
                                    last_updated = NOW()
                                WHERE id = :campaign_id
                            """), {'campaign_id': campaign_id})

                            self.fixes_applied.append(f"标记计划 {appeal['campaign_id_qc']} 为人工处理")
                            fixed_count += 1

                        elif hours_stuck > 12 and attempt_count == 1:
                            # 单次尝试后长时间无响应，重置重试
                            db.execute(text("""
                                UPDATE campaigns
                                SET appeal_attempt_count = 0,
                                    next_appeal_retry = NOW(),
                                    last_updated = NOW()
                                WHERE id = :campaign_id
                            """), {'campaign_id': campaign_id})

                            self.fixes_applied.append(f"重置计划 {appeal['campaign_id_qc']} 重试状态")
                            fixed_count += 1

                    except Exception as inner_e:
                        print(f"❌ 修复计划 {appeal.get('campaign_id_qc', 'unknown')} 失败: {inner_e}")
                        continue
                
                db.commit()

        except Exception as e:
            print(f"❌ 修复卡住提审失败: {e}")
            if 'db' in locals():
                db.rollback()

        return fixed_count
    
    def test_appeal_execution(self) -> Dict:
        """测试提审执行"""
        print("🧪 测试提审执行...")
        
        test_result = {
            'test_executed': False,
            'success': False,
            'error_message': None,
            'execution_time': 0
        }
        
        try:
            # 查找一个可以测试的计划
            from qianchuan_aw.utils.db_utils import database_session
            from sqlalchemy import text
            
            with database_session() as db:
                test_campaign = db.execute(text("""
                    SELECT id, campaign_id_qc, account_id
                    FROM campaigns 
                    WHERE status = 'APPEAL_TIMEOUT'
                      AND appeal_status = 'appeal_pending'
                      AND appeal_attempt_count < 2
                    LIMIT 1
                """)).fetchone()
                
                if not test_campaign:
                    test_result['error_message'] = "未找到可测试的计划"
                    return test_result
                
                test_result['test_executed'] = True
                
                # 这里可以添加实际的提审测试逻辑
                # 由于涉及真实的API调用，这里只做模拟
                print(f"📋 找到测试计划: {test_campaign.campaign_id_qc}")
                test_result['success'] = True
                test_result['execution_time'] = 2.5  # 模拟执行时间
                
        except Exception as e:
            test_result['error_message'] = str(e)
            print(f"❌ 测试提审执行失败: {e}")
        
        return test_result
    
    def generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于分析结果生成建议
        if 'status_analysis' in self.analysis_results:
            status_analysis = self.analysis_results['status_analysis']
            timeout_info = status_analysis.get('timeout_info', {})
            
            if timeout_info.get('timeout_count', 0) > 50:
                recommendations.append("大量计划提审超时，建议检查提审调度器是否正常运行")
            
            if timeout_info.get('avg_hours_waiting', 0) > 24:
                recommendations.append("平均等待时间过长，建议优化提审执行频率")
        
        if 'api_issues' in self.analysis_results:
            api_issues = self.analysis_results['api_issues']
            
            if api_issues.get('login_issues', 0) > 10:
                recommendations.append("检测到登录问题，建议检查账户登录状态和Cookie有效性")
            
            if api_issues.get('network_issues', 0) > 5:
                recommendations.append("检测到网络问题，建议检查网络连接和代理设置")
        
        if 'connectivity_test' in self.analysis_results:
            connectivity = self.analysis_results['connectivity_test']
            
            if not connectivity.get('qianchuan_accessible', False):
                recommendations.append("千川平台不可访问，检查网络连接和防火墙设置")
            
            if connectivity.get('response_time', 0) > 10:
                recommendations.append("千川平台响应时间过长，考虑优化网络或使用CDN")
        
        # 通用建议
        recommendations.extend([
            "建立提审状态监控告警机制",
            "实施提审失败自动重试策略",
            "定期清理长期卡住的提审记录",
            "优化提审API调用频率和并发数"
        ])
        
        return recommendations
    
    def run_comprehensive_analysis(self) -> Dict:
        """运行综合分析"""
        print("🚀 开始提审失败综合分析...")
        
        # 1. 分析提审状态分布
        self.analysis_results['status_analysis'] = self.analyze_appeal_status_distribution()
        
        # 2. 分析API问题
        self.analysis_results['api_issues'] = self.analyze_appeal_api_issues()
        
        # 3. 测试API连通性
        self.analysis_results['connectivity_test'] = self.test_appeal_api_connectivity()
        
        # 4. 识别卡住的提审
        stuck_appeals = self.identify_stuck_appeals()
        self.analysis_results['stuck_appeals'] = stuck_appeals
        
        # 5. 修复卡住的提审
        fixed_count = self.fix_stuck_appeals(stuck_appeals)
        self.analysis_results['fixed_count'] = fixed_count
        
        # 6. 测试提审执行
        self.analysis_results['execution_test'] = self.test_appeal_execution()
        
        # 7. 生成建议
        self.recommendations = self.generate_recommendations()
        
        return {
            'analysis_results': self.analysis_results,
            'fixes_applied': self.fixes_applied,
            'recommendations': self.recommendations
        }

def main():
    """主函数"""
    print("🔍 提审失败分析和修复工具")
    print("=" * 60)
    
    analyzer = AppealFailureAnalyzer()
    
    try:
        # 运行综合分析
        results = analyzer.run_comprehensive_analysis()
        
        # 输出分析结果
        print(f"\n{'='*60}")
        print("📊 提审失败分析结果")
        print(f"{'='*60}")
        
        # 状态分布分析
        if 'status_analysis' in results['analysis_results']:
            status_analysis = results['analysis_results']['status_analysis']
            print(f"\n📋 提审状态分布:")
            
            for status in status_analysis.get('status_distribution', [])[:5]:
                print(f"  - {status['status']}/{status['appeal_status']}: {status['count']} ({status['percentage']}%)")
            
            timeout_info = status_analysis.get('timeout_info', {})
            if timeout_info:
                print(f"\n⏰ 超时统计:")
                print(f"  - 超时计划数: {timeout_info.get('timeout_count', 0)}")
                print(f"  - 平均等待时间: {timeout_info.get('avg_hours_waiting', 0):.1f} 小时")
        
        # 卡住的提审
        stuck_appeals = results['analysis_results'].get('stuck_appeals', [])
        if stuck_appeals:
            print(f"\n🔍 卡住的提审: {len(stuck_appeals)} 个")
            for appeal in stuck_appeals[:3]:
                print(f"  - 计划 {appeal['campaign_id_qc']}: {appeal['stuck_reason']}")
        
        # 修复结果
        fixed_count = results['analysis_results'].get('fixed_count', 0)
        if fixed_count > 0:
            print(f"\n🔧 修复结果: {fixed_count} 个问题已修复")
        
        # 建议
        if results['recommendations']:
            print(f"\n💡 修复建议:")
            for i, rec in enumerate(results['recommendations'][:5], 1):
                print(f"  {i}. {rec}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
