#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强型原子状态管理器

集成统一状态枚举、状态验证器、版本控制和分布式锁的高级状态管理器。
确保状态转换的原子性、一致性和并发安全性。

创建时间: 2025-08-10
维护责任: 千川自动化系统核心团队
"""

import redis
from datetime import datetime, timezone
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging

from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.material_state_validator import (
    MaterialStateValidator, 
    ValidationResult, 
    TransitionContext
)
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.unified_material_status import MaterialStatus

logger = logging.getLogger(__name__)


@dataclass
class StateTransitionResult:
    """状态转换结果"""
    success: bool
    creative_id: int
    from_status: str
    to_status: str
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    execution_time_ms: Optional[float] = None


class StateTransitionError(Exception):
    """状态转换异常"""
    pass


class ConcurrencyError(Exception):
    """并发冲突异常"""
    pass


class EnhancedAtomicStateManager:
    """
    增强型原子状态管理器
    
    特性：
    1. 统一状态枚举集成
    2. 状态转换验证
    3. 版本控制（乐观锁）
    4. 分布式锁支持
    5. 事件发布机制
    6. 批量操作支持
    7. 性能监控
    """
    
    def __init__(self, db_session, redis_client: Optional[redis.Redis] = None):
        self.db = db_session
        self.redis = redis_client
        self.validator = MaterialStateValidator()
        
        # 性能统计
        self.stats = {
            'transitions_count': 0,
            'successful_transitions': 0,
            'failed_transitions': 0,
            'validation_failures': 0,
            'concurrency_conflicts': 0,
            'total_execution_time_ms': 0.0
        }
        
        # 分布式锁配置
        self.lock_timeout = 30  # 锁超时时间（秒）
        self.lock_prefix = "material_state_lock"
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str, 
                               metadata: Dict = None, use_distributed_lock: bool = True):
        """
        原子状态转换上下文管理器
        
        Args:
            creative_id: 素材ID
            from_status: 源状态
            to_status: 目标状态
            metadata: 转换元数据
            use_distributed_lock: 是否使用分布式锁
            
        Yields:
            LocalCreative: 素材对象
            
        Raises:
            StateTransitionError: 状态转换错误
            ConcurrencyError: 并发冲突错误
        """
        start_time = datetime.now()
        creative = None
        distributed_lock = None
        
        try:
            self.stats['transitions_count'] += 1
            
            # 1. 状态转换验证
            context = TransitionContext(
                creative_id=creative_id,
                from_status=from_status,
                to_status=to_status,
                metadata=metadata or {}
            )
            
            validation_result = self.validator.validate_transition(context)
            if not validation_result.is_valid:
                self.stats['validation_failures'] += 1
                raise StateTransitionError(f"状态转换验证失败: {validation_result.error_message}")
            
            # 2. 分布式锁（如果启用）
            if use_distributed_lock and self.redis:
                lock_key = f"{self.lock_prefix}:{creative_id}"
                distributed_lock = self.redis.lock(lock_key, timeout=self.lock_timeout)
                
                if not distributed_lock.acquire(blocking=False):
                    self.stats['concurrency_conflicts'] += 1
                    raise ConcurrencyError(f"无法获取素材 {creative_id} 的分布式锁")
            
            # 3. 数据库事务和行锁
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id,
                LocalCreative.status == from_status
            ).with_for_update(nowait=True).first()
            
            if not creative:
                raise StateTransitionError(f"素材 {creative_id} 不存在或状态不匹配 (期望: {from_status})")
            
            # 4. 版本控制检查
            expected_version = creative.state_version
            
            # 5. 执行状态转换
            original_status = creative.status
            creative.status = to_status
            creative.updated_at = datetime.now(timezone.utc)
            creative.state_version += 1  # 增加版本号
            
            # 记录警告信息
            if validation_result.warning_message:
                logger.warning(f"状态转换警告: 素材{creative_id} {from_status}->{to_status}: {validation_result.warning_message}")
            
            # 提供给调用者使用
            yield creative
            
            # 6. 提交事务
            self.db.commit()
            
            # 7. 发布状态变更事件
            self._publish_state_change_event(creative_id, from_status, to_status, metadata)
            
            # 8. 更新统计
            self.stats['successful_transitions'] += 1
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            self.stats['total_execution_time_ms'] += execution_time
            
            logger.info(f"✅ 状态转换成功: 素材{creative_id} {from_status}->{to_status} (版本: {expected_version}->{creative.state_version}, 耗时: {execution_time:.2f}ms)")
            
        except Exception as e:
            # 回滚事务
            self.db.rollback()
            self.stats['failed_transitions'] += 1
            
            logger.error(f"❌ 状态转换失败: 素材{creative_id} {from_status}->{to_status}: {str(e)}")
            raise
            
        finally:
            # 释放分布式锁
            if distributed_lock:
                try:
                    distributed_lock.release()
                except Exception as e:
                    logger.warning(f"释放分布式锁失败: {e}")
    
    def safe_state_transition(self, creative_id: int, from_status: str, to_status: str, 
                             metadata: Dict = None) -> StateTransitionResult:
        """
        安全的状态转换（不抛出异常）
        
        Args:
            creative_id: 素材ID
            from_status: 源状态
            to_status: 目标状态
            metadata: 转换元数据
            
        Returns:
            StateTransitionResult: 转换结果
        """
        start_time = datetime.now()
        
        try:
            with self.atomic_state_transition(creative_id, from_status, to_status, metadata):
                pass  # 状态转换在上下文管理器中完成
            
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return StateTransitionResult(
                success=True,
                creative_id=creative_id,
                from_status=from_status,
                to_status=to_status,
                execution_time_ms=execution_time
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            
            return StateTransitionResult(
                success=False,
                creative_id=creative_id,
                from_status=from_status,
                to_status=to_status,
                error_message=str(e),
                execution_time_ms=execution_time
            )
    
    def batch_state_transition(self, transitions: List[Tuple[int, str, str]], 
                              metadata: Dict = None) -> List[StateTransitionResult]:
        """
        批量状态转换
        
        Args:
            transitions: 转换列表 [(creative_id, from_status, to_status), ...]
            metadata: 共享元数据
            
        Returns:
            List[StateTransitionResult]: 转换结果列表
        """
        results = []
        
        # 按creative_id排序，避免死锁
        sorted_transitions = sorted(transitions, key=lambda x: x[0])
        
        logger.info(f"🔄 开始批量状态转换: {len(sorted_transitions)} 个转换")
        
        for creative_id, from_status, to_status in sorted_transitions:
            result = self.safe_state_transition(creative_id, from_status, to_status, metadata)
            results.append(result)
        
        # 统计结果
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        
        logger.info(f"📊 批量状态转换完成: 成功 {successful}, 失败 {failed}")
        
        return results
    
    def get_current_state(self, creative_id: int) -> Optional[str]:
        """
        获取当前状态
        
        Args:
            creative_id: 素材ID
            
        Returns:
            Optional[str]: 当前状态，如果不存在返回None
        """
        try:
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id
            ).first()
            
            return creative.status if creative else None
            
        except Exception as e:
            logger.error(f"获取状态失败: 素材{creative_id}: {e}")
            return None
    
    def get_state_with_version(self, creative_id: int) -> Optional[Tuple[str, int]]:
        """
        获取状态和版本号
        
        Args:
            creative_id: 素材ID
            
        Returns:
            Optional[Tuple[str, int]]: (状态, 版本号)，如果不存在返回None
        """
        try:
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id
            ).first()
            
            return (creative.status, creative.state_version) if creative else None
            
        except Exception as e:
            logger.error(f"获取状态和版本失败: 素材{creative_id}: {e}")
            return None
    
    def _publish_state_change_event(self, creative_id: int, from_status: str, 
                                   to_status: str, metadata: Dict = None):
        """发布状态变更事件"""
        try:
            if not self.redis:
                return
            
            event_data = {
                'creative_id': creative_id,
                'from_status': from_status,
                'to_status': to_status,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'metadata': metadata or {}
            }
            
            # 发布到Redis频道
            channel = "material_state_changes"
            self.redis.publish(channel, str(event_data))
            
        except Exception as e:
            logger.warning(f"发布状态变更事件失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self.stats.copy()
        
        if stats['transitions_count'] > 0:
            stats['success_rate'] = stats['successful_transitions'] / stats['transitions_count']
            stats['average_execution_time_ms'] = stats['total_execution_time_ms'] / stats['transitions_count']
        else:
            stats['success_rate'] = 0.0
            stats['average_execution_time_ms'] = 0.0
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        for key in self.stats:
            self.stats[key] = 0 if isinstance(self.stats[key], (int, float)) else self.stats[key]
        
        logger.info("状态管理器统计信息已重置")


# === 便捷函数 ===

def create_state_manager(db_session, redis_client: Optional[redis.Redis] = None) -> EnhancedAtomicStateManager:
    """
    创建增强型状态管理器实例
    
    Args:
        db_session: 数据库会话
        redis_client: Redis客户端（可选）
        
    Returns:
        EnhancedAtomicStateManager: 状态管理器实例
    """
    return EnhancedAtomicStateManager(db_session, redis_client)


# === 导出接口 ===

__all__ = [
    'EnhancedAtomicStateManager',
    'StateTransitionResult',
    'StateTransitionError',
    'ConcurrencyError',
    'create_state_manager'
]
