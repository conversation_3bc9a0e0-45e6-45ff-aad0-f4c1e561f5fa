#!/usr/bin/env python3
"""
千川工作流视频分组和计划创建机制分析
解决分组阻塞风险和系统优化问题
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class GroupingOptimizationAnalyzer:
    """分组优化分析器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流视频分组和计划创建机制分析")
        logger.critical("=" * 60)
        self.current_time = datetime.now()
    
    def analyze_grouping_blocking_risks(self):
        """问题1：分析视频分组阻塞风险"""
        logger.critical("\n🔍 问题1：视频分组阻塞风险分析")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 1. 检查分组相关的状态分布
                grouping_status_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        COUNT(CASE WHEN lc.created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as recent_count
                    FROM local_creatives lc
                    WHERE lc.status IN (MaterialStatus.NEW.value, MaterialStatus.PENDING_GROUPING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan')
                    GROUP BY lc.status
                    ORDER BY count DESC
                """)
                
                grouping_status = db.execute(grouping_status_query).fetchall()
                
                logger.critical("📊 分组相关状态分布:")
                total_grouping = sum(row.count for row in grouping_status)
                for row in grouping_status:
                    percentage = (row.count / total_grouping * 100) if total_grouping > 0 else 0
                    logger.critical(f"  {row.status}: {row.count} 个 ({percentage:.1f}%) - 最近24h: {row.recent_count}")
                
                # 2. 分析按账户分组的视频分布
                account_grouping_query = text("""
                    SELECT 
                        aa.name as account_name,
                        aa.account_type,
                        COUNT(CASE WHEN lc.status = MaterialStatus.NEW.value THEN 1 END) as new_count,
                        COUNT(CASE WHEN lc.status = MaterialStatus.PENDING_GROUPING.value THEN 1 END) as pending_grouping_count,
                        COUNT(CASE WHEN lc.status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 1 END) as uploaded_pending_count,
                        COUNT(CASE WHEN lc.status = 'creating_plan' THEN 1 END) as creating_plan_count,
                        COUNT(*) as total_videos
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN ad_accounts aa ON pc.account_id = aa.id
                    WHERE lc.status IN (MaterialStatus.NEW.value, MaterialStatus.PENDING_GROUPING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan')
                    AND aa.account_type = 'TEST'
                    GROUP BY aa.name, aa.account_type
                    ORDER BY total_videos DESC
                """)
                
                account_grouping = db.execute(account_grouping_query).fetchall()
                
                logger.critical(f"\n📊 按测试账户分组的视频分布:")
                blocked_accounts = []
                
                for account in account_grouping:
                    logger.critical(f"  {account.account_name}:")
                    logger.critical(f"    new: {account.new_count}")
                    logger.critical(f"    pending_grouping: {account.pending_grouping_count}")
                    logger.critical(f"    uploaded_pending_plan: {account.uploaded_pending_count}")
                    logger.critical(f"    creating_plan: {account.creating_plan_count}")
                    logger.critical(f"    总计: {account.total_videos}")
                    
                    # 识别可能阻塞的账户（视频数量少于9个）
                    if account.total_videos < 9 and account.total_videos > 0:
                        blocked_accounts.append({
                            'account_name': account.account_name,
                            'video_count': account.total_videos,
                            'shortage': 9 - account.total_videos
                        })
                
                # 3. 分析上传失败的视频
                upload_failed_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        COUNT(CASE WHEN lc.updated_at > NOW() - INTERVAL '24 hours' THEN 1 END) as recent_failures
                    FROM local_creatives lc
                    WHERE lc.status IN (MaterialStatus.UPLOAD_FAILED.value, MaterialStatus.PENDING_UPLOAD.value)
                    GROUP BY lc.status
                """)
                
                upload_failures = db.execute(upload_failed_query).fetchall()
                
                logger.critical(f"\n📊 上传失败分析:")
                total_failures = sum(row.count for row in upload_failures)
                logger.critical(f"  总上传失败: {total_failures} 个")
                
                for failure in upload_failures:
                    logger.critical(f"  {failure.status}: {failure.count} 个 - 最近24h: {failure.recent_failures}")
                
                # 4. 检查长时间卡在某状态的视频
                stuck_videos_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        AVG(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as avg_hours_stuck
                    FROM local_creatives lc
                    WHERE lc.status IN (MaterialStatus.PENDING_GROUPING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan')
                    AND lc.updated_at < NOW() - INTERVAL '2 hours'
                    GROUP BY lc.status
                    ORDER BY avg_hours_stuck DESC
                """)
                
                stuck_videos = db.execute(stuck_videos_query).fetchall()
                
                logger.critical(f"\n📊 长时间卡住的视频:")
                for stuck in stuck_videos:
                    logger.critical(f"  {stuck.status}: {stuck.count} 个 - 平均卡住 {stuck.avg_hours_stuck:.1f} 小时")
                
                return {
                    'grouping_status': grouping_status,
                    'account_grouping': account_grouping,
                    'blocked_accounts': blocked_accounts,
                    'upload_failures': upload_failures,
                    'stuck_videos': stuck_videos,
                    'total_blocked': len(blocked_accounts)
                }
                
        except Exception as e:
            logger.error(f"❌ 分组阻塞风险分析失败: {e}")
            return None
    
    def analyze_grouping_mechanism(self):
        """分析当前分组机制"""
        logger.critical("\n🔍 分析当前分组机制")
        logger.critical("=" * 60)
        
        # 检查scheduler.py中的分组逻辑
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找分组相关的代码
            grouping_logic = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if '分组' in line or 'group' in line.lower():
                    grouping_logic.append(f"第{i+1}行: {line.strip()}")
                elif '9' in line and ('视频' in line or 'video' in line.lower()):
                    grouping_logic.append(f"第{i+1}行: {line.strip()}")
            
            logger.critical("📊 分组机制代码分析:")
            if grouping_logic:
                logger.critical("  找到分组相关代码:")
                for logic in grouping_logic[:10]:  # 显示前10个
                    logger.critical(f"    {logic}")
            else:
                logger.critical("  ❌ 未找到明确的分组逻辑代码")
            
            # 查找硬编码的数字9
            nine_references = []
            for i, line in enumerate(lines):
                if '9' in line and ('batch' in line.lower() or 'group' in line.lower() or '批' in line):
                    nine_references.append(f"第{i+1}行: {line.strip()}")
            
            if nine_references:
                logger.critical("  发现可能的硬编码分组限制:")
                for ref in nine_references:
                    logger.critical(f"    {ref}")
            
            return {
                'grouping_logic_found': len(grouping_logic) > 0,
                'hard_coded_nine': len(nine_references) > 0,
                'grouping_logic': grouping_logic,
                'nine_references': nine_references
            }
            
        except Exception as e:
            logger.error(f"❌ 分组机制分析失败: {e}")
            return None
    
    def design_optimization_solution(self, blocking_analysis, mechanism_analysis):
        """问题2：设计优化方案"""
        logger.critical("\n🔧 问题2：优化方案设计")
        logger.critical("=" * 60)
        
        optimization_plan = {
            'grouping_strategy': {},
            'system_stability': {},
            'monitoring_alerts': {},
            'implementation_steps': []
        }
        
        # 1. 分组策略优化
        logger.critical("📋 1. 分组策略优化方案:")
        
        if blocking_analysis and blocking_analysis['blocked_accounts']:
            blocked_count = len(blocking_analysis['blocked_accounts'])
            logger.critical(f"  发现 {blocked_count} 个账户因视频不足被阻塞")
            
            optimization_plan['grouping_strategy'] = {
                'flexible_grouping': {
                    'description': '实现灵活分组机制',
                    'min_videos': 6,
                    'max_videos': 9,
                    'timeout_hours': 1,
                    'priority': 'HIGH'
                },
                'timeout_mechanism': {
                    'description': '添加分组超时机制',
                    'wait_time': '1小时',
                    'action': '自动用现有视频创建计划',
                    'priority': 'HIGH'
                },
                'problem_video_isolation': {
                    'description': '自动识别和隔离问题视频',
                    'criteria': ['上传失败3次', '格式不符合', '网络超时'],
                    'priority': 'MEDIUM'
                }
            }
            
            logger.critical("    ✅ 灵活分组：允许6-9个视频创建计划")
            logger.critical("    ✅ 超时机制：1小时后自动创建计划")
            logger.critical("    ✅ 问题隔离：自动识别无法上传的视频")
        
        # 2. 系统稳定性提升
        logger.critical("\n📋 2. 系统稳定性提升方案:")
        
        optimization_plan['system_stability'] = {
            'long_running_tasks': {
                'description': '解决长时间运行任务',
                'target': 'Uploading任务超过2小时',
                'solution': '添加任务超时和重启机制',
                'priority': 'HIGH'
            },
            'task_performance': {
                'description': '优化任务性能',
                'targets': ['Monitor任务平均33秒', 'Appeal任务最长146秒'],
                'solution': '并行处理和缓存优化',
                'priority': 'MEDIUM'
            },
            'api_rate_limiting': {
                'description': '改进API频率限制处理',
                'current_errors': 53,
                'solution': '指数退避重试机制',
                'priority': 'MEDIUM'
            }
        }
        
        logger.critical("    ✅ 任务超时：添加2小时超时机制")
        logger.critical("    ✅ 性能优化：并行处理和缓存")
        logger.critical("    ✅ API限制：指数退避重试")
        
        # 3. 监控和告警机制
        logger.critical("\n📋 3. 监控和告警机制:")
        
        optimization_plan['monitoring_alerts'] = {
            'grouping_monitoring': {
                'description': '实时监控视频分组状态',
                'metrics': ['各状态视频数量', '分组完成率', '阻塞账户数'],
                'alert_threshold': '阻塞账户 > 3个',
                'priority': 'MEDIUM'
            },
            'blocking_alerts': {
                'description': '阻塞视频自动告警',
                'conditions': ['视频卡住超过2小时', '上传失败超过3次'],
                'notification': '邮件 + 日志',
                'priority': 'MEDIUM'
            },
            'approval_tracking': {
                'description': '审核通过率跟踪',
                'metrics': ['日审核通过率', '周审核通过率', '账户审核表现'],
                'target': '审核通过率 > 60%',
                'priority': 'LOW'
            }
        }
        
        logger.critical("    ✅ 实时监控：分组状态和阻塞情况")
        logger.critical("    ✅ 自动告警：问题视频和长时间阻塞")
        logger.critical("    ✅ 通过率跟踪：审核效果监控")
        
        return optimization_plan
    
    def generate_implementation_plan(self, optimization_plan):
        """生成具体实施步骤"""
        logger.critical("\n📋 4. 具体实施步骤")
        logger.critical("=" * 60)
        
        implementation_steps = [
            {
                'step': 1,
                'title': '修复长时间运行任务',
                'priority': 'CRITICAL',
                'duration': '1天',
                'description': '添加任务超时机制，解决Uploading任务阻塞',
                'files_to_modify': ['src/qianchuan_aw/workflows/tasks.py'],
                'expected_effect': '消除长时间阻塞任务',
                'verification': '检查任务运行时间 < 2小时'
            },
            {
                'step': 2,
                'title': '实现灵活分组机制',
                'priority': 'HIGH',
                'duration': '2天',
                'description': '修改分组逻辑，支持6-9个视频创建计划',
                'files_to_modify': ['src/qianchuan_aw/workflows/scheduler.py'],
                'expected_effect': '减少50%的分组阻塞',
                'verification': '监控阻塞账户数量下降'
            },
            {
                'step': 3,
                'title': '添加分组超时机制',
                'priority': 'HIGH',
                'duration': '1天',
                'description': '1小时后自动用现有视频创建计划',
                'files_to_modify': ['src/qianchuan_aw/workflows/scheduler.py', 'config/settings.yml'],
                'expected_effect': '消除长期分组等待',
                'verification': '检查无超过1小时的分组等待'
            },
            {
                'step': 4,
                'title': '优化任务性能',
                'priority': 'MEDIUM',
                'duration': '3天',
                'description': '并行处理和缓存优化Monitor/Appeal任务',
                'files_to_modify': ['src/qianchuan_aw/workflows/scheduler.py'],
                'expected_effect': 'Monitor任务耗时 < 20秒',
                'verification': '监控任务平均执行时间'
            },
            {
                'step': 5,
                'title': '实现问题视频隔离',
                'priority': 'MEDIUM',
                'duration': '2天',
                'description': '自动识别和隔离无法上传的视频',
                'files_to_modify': ['src/qianchuan_aw/workflows/scheduler.py'],
                'expected_effect': '减少30%的上传失败重试',
                'verification': '监控上传成功率提升'
            },
            {
                'step': 6,
                'title': '建立监控告警系统',
                'priority': 'LOW',
                'duration': '3天',
                'description': '实现分组状态监控和自动告警',
                'files_to_modify': ['新建监控模块'],
                'expected_effect': '及时发现和处理问题',
                'verification': '告警系统正常工作'
            }
        ]
        
        logger.critical("🚀 实施计划 (按优先级排序):")
        
        total_duration = 0
        for step in implementation_steps:
            duration_days = int(step['duration'].replace('天', ''))
            total_duration += duration_days
            
            logger.critical(f"\n  步骤{step['step']}: {step['title']}")
            logger.critical(f"    优先级: {step['priority']}")
            logger.critical(f"    预计耗时: {step['duration']}")
            logger.critical(f"    描述: {step['description']}")
            logger.critical(f"    修改文件: {', '.join(step['files_to_modify'])}")
            logger.critical(f"    预期效果: {step['expected_effect']}")
            logger.critical(f"    验证方法: {step['verification']}")
        
        logger.critical(f"\n📊 总体实施计划:")
        logger.critical(f"  总步骤: {len(implementation_steps)} 个")
        logger.critical(f"  预计总耗时: {total_duration} 天")
        logger.critical(f"  关键路径: 步骤1-3 (4天)")
        
        return implementation_steps
    
    def generate_code_modifications(self):
        """生成具体的代码修改建议"""
        logger.critical("\n💻 具体代码修改建议")
        logger.critical("=" * 60)
        
        modifications = {
            'flexible_grouping': '''
# 在scheduler.py中修改分组逻辑
def create_flexible_groups(account_videos, min_size=6, max_size=9):
    """
    灵活分组机制：支持6-9个视频创建计划
    """
    groups = []
    current_group = []
    
    for video in account_videos:
        current_group.append(video)
        
        # 达到最大组大小或者是最后的视频
        if len(current_group) >= max_size or video == account_videos[-1]:
            if len(current_group) >= min_size:
                groups.append(current_group)
                current_group = []
            elif len(current_group) < min_size and video == account_videos[-1]:
                # 最后一组不足最小大小，合并到前一组
                if groups:
                    groups[-1].extend(current_group)
                else:
                    # 如果只有一组且不足最小大小，仍然创建
                    groups.append(current_group)
    
    return groups
''',
            'timeout_mechanism': '''
# 在scheduler.py中添加超时检查
def check_grouping_timeout(db, timeout_hours=1):
    """
    检查分组超时，自动创建计划
    """
    timeout_threshold = datetime.now() - timedelta(hours=timeout_hours)
    
    stuck_videos = db.query(LocalCreative).filter(
        LocalCreative.status == MaterialStatus.PENDING_GROUPING.value,
        LocalCreative.updated_at < timeout_threshold
    ).all()
    
    if stuck_videos:
        logger.warning(f"发现 {len(stuck_videos)} 个视频分组超时，强制创建计划")
        
        # 按账户分组
        account_videos = defaultdict(list)
        for video in stuck_videos:
            account_videos[video.account_id].append(video)
        
        for account_id, videos in account_videos.items():
            if len(videos) >= 3:  # 最少3个视频也可以创建计划
                create_campaign_for_videos(videos, force_create=True)
''',
            'task_timeout': '''
# 在tasks.py中添加任务超时机制
@app.task(name="tasks.upload_videos", bind=True)
def task_upload_videos(self, timeout_seconds=7200):  # 2小时超时
    """
    上传视频任务，带超时控制
    """
    try:
        # 设置任务超时
        signal.alarm(timeout_seconds)
        
        # 执行上传逻辑
        result = _run_task(scheduler.handle_video_upload)
        
        # 清除超时
        signal.alarm(0)
        
        return result
        
    except TimeoutError:
        logger.error(f"上传任务超时 ({timeout_seconds}秒)，强制终止")
        self.retry(countdown=300, max_retries=3)  # 5分钟后重试
    except Exception as e:
        logger.error(f"上传任务失败: {e}")
        raise
'''
        }
        
        logger.critical("📝 关键代码修改:")
        
        for mod_type, code in modifications.items():
            logger.critical(f"\n  {mod_type}:")
            logger.critical("    " + code.strip().replace('\n', '\n    '))
        
        return modifications
    
    def calculate_improvement_expectations(self, blocking_analysis):
        """计算可量化的改进效果预期"""
        logger.critical("\n📈 可量化的改进效果预期")
        logger.critical("=" * 60)
        
        if not blocking_analysis:
            logger.error("❌ 无法计算改进效果，缺少阻塞分析数据")
            return
        
        current_metrics = {
            'blocked_accounts': len(blocking_analysis.get('blocked_accounts', [])),
            'total_videos_in_grouping': sum(row.count for row in blocking_analysis.get('grouping_status', [])),
            'upload_failures': sum(row.count for row in blocking_analysis.get('upload_failures', [])),
            'stuck_videos': sum(row.count for row in blocking_analysis.get('stuck_videos', []))
        }
        
        expected_improvements = {
            'blocked_accounts_reduction': {
                'current': current_metrics['blocked_accounts'],
                'target': max(0, current_metrics['blocked_accounts'] - 3),
                'improvement': '50%减少',
                'method': '灵活分组机制'
            },
            'grouping_efficiency': {
                'current': '硬性要求9个视频',
                'target': '6-9个视频灵活分组',
                'improvement': '33%提升分组成功率',
                'method': '降低分组门槛'
            },
            'timeout_elimination': {
                'current': current_metrics['stuck_videos'],
                'target': 0,
                'improvement': '100%消除长期等待',
                'method': '1小时超时机制'
            },
            'task_performance': {
                'current': 'Monitor任务33秒',
                'target': 'Monitor任务<20秒',
                'improvement': '40%性能提升',
                'method': '并行处理优化'
            },
            'system_stability': {
                'current': '长时间运行任务存在',
                'target': '所有任务<2小时',
                'improvement': '100%消除阻塞任务',
                'method': '任务超时机制'
            }
        }
        
        logger.critical("🎯 预期改进效果:")
        
        for metric, data in expected_improvements.items():
            logger.critical(f"\n  {metric}:")
            logger.critical(f"    当前状态: {data['current']}")
            logger.critical(f"    目标状态: {data['target']}")
            logger.critical(f"    预期改进: {data['improvement']}")
            logger.critical(f"    实现方法: {data['method']}")
        
        # 计算整体效果
        logger.critical(f"\n📊 整体预期效果:")
        logger.critical(f"  视频处理效率: 提升40-50%")
        logger.critical(f"  系统稳定性: 提升60%")
        logger.critical(f"  审核通过率: 预计提升20%")
        logger.critical(f"  运维工作量: 减少30%")
        
        return expected_improvements

def main():
    """主分析函数"""
    try:
        analyzer = GroupingOptimizationAnalyzer()
        
        # 问题1：分析视频分组阻塞风险
        blocking_analysis = analyzer.analyze_grouping_blocking_risks()
        
        # 分析当前分组机制
        mechanism_analysis = analyzer.analyze_grouping_mechanism()
        
        # 问题2：设计优化方案
        optimization_plan = analyzer.design_optimization_solution(blocking_analysis, mechanism_analysis)
        
        # 生成具体实施步骤
        implementation_steps = analyzer.generate_implementation_plan(optimization_plan)
        
        # 生成代码修改建议
        code_modifications = analyzer.generate_code_modifications()
        
        # 计算改进效果预期
        improvement_expectations = analyzer.calculate_improvement_expectations(blocking_analysis)
        
        logger.critical(f"\n🎉 千川工作流视频分组优化分析完成!")
        logger.critical("建议按优先级顺序实施改进方案")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 分组优化分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
