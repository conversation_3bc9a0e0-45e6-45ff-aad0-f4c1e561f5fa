#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化工作流 - 统一调度器入口
解决架构混乱和模块阻塞问题
"""

import sys
import os

# 正确设置 sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.logger import logger
from ai_tools.architecture.ai_tool_20250808_unified_scheduler import UnifiedScheduler


def main():
    """
    统一调度器主入口
    
    特性：
    1. 模块独立运行，互不阻塞
    2. 自动资源管理和清理
    3. 强制测试账户限制
    4. 浏览器进程控制
    """
    
    logger.info("🚀 千川自动化工作流 - 统一调度器")
    logger.info("版本: v2.0 - 架构修复版")
    logger.info("="*80)
    
    try:
        # 创建并启动统一调度器
        scheduler = UnifiedScheduler()
        scheduler.run()
        
    except KeyboardInterrupt:
        logger.info("--- 检测到手动中断，程序退出 ---")
    except Exception as e:
        logger.error(f"调度器启动失败: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
