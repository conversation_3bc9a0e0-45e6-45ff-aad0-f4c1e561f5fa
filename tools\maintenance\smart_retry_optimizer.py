#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能重试逻辑优化器
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 智能重试逻辑优化器，减少无效重试，提升系统效率
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Set

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class SmartRetryOptimizer:
    """智能重试优化器"""
    
    def __init__(self):
        # 永久性错误 - 不应该重试
        self.permanent_errors = {
            '文件格式错误',
            '视频尺寸错误', 
            '文件不存在',
            'moov atom not found',
            '视频时长不符合要求',
            '文件损坏',
            '不支持的编码格式'
        }
        
        # 临时性错误 - 可以重试
        self.temporary_errors = {
            '网络连接超时',
            '服务器内部错误',
            '请求频率过高',
            '临时服务不可用',
            'Connection reset',
            'Read timeout'
        }
        
        # 需要特殊处理的错误
        self.special_errors = {
            'Token权限不足': 'refresh_token',
            '账户状态异常': 'check_account',
            '余额不足': 'check_balance'
        }
    
    def classify_error(self, error_message: str) -> str:
        """分类错误类型"""
        error_msg = error_message.lower()
        
        for permanent_error in self.permanent_errors:
            if permanent_error.lower() in error_msg:
                return 'permanent'
        
        for temporary_error in self.temporary_errors:
            if temporary_error.lower() in error_msg:
                return 'temporary'
        
        for special_error in self.special_errors:
            if special_error.lower() in error_msg:
                return 'special'
        
        return 'unknown'
    
    def should_retry(self, error_message: str, attempt_count: int, max_retries: int = 3) -> bool:
        """判断是否应该重试"""
        error_type = self.classify_error(error_message)
        
        if error_type == 'permanent':
            return False  # 永久性错误不重试
        
        if error_type == 'temporary':
            return attempt_count < max_retries  # 临时性错误可以重试
        
        if error_type == 'special':
            return attempt_count < 1  # 特殊错误只重试一次
        
        # 未知错误谨慎重试
        return attempt_count < 2
    
    def get_retry_delay(self, error_message: str, attempt_count: int) -> int:
        """获取重试延迟时间（秒）"""
        error_type = self.classify_error(error_message)
        
        if error_type == 'temporary':
            # 指数退避：2^attempt_count * 30秒
            return min(2 ** attempt_count * 30, 300)  # 最大5分钟
        
        if error_type == 'special':
            return 60  # 特殊错误等待1分钟
        
        # 未知错误使用固定延迟
        return 120  # 2分钟
    
    def analyze_current_retry_patterns(self) -> Dict[str, int]:
        """分析当前的重试模式"""
        print("🔍 分析当前重试模式...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 统计各种状态的素材数量
                status_counts = {}
                
                all_statuses = [
                    MaterialStatus.PENDING_UPLOAD.value,
                    MaterialStatus.UPLOADING.value,
                    MaterialStatus.UPLOAD_FAILED.value,
                    MaterialStatus.PROCESSING.value,
                    MaterialStatus.APPROVED.value
                ]
                
                for status in all_statuses:
                    count = db.query(LocalCreative).filter(
                        LocalCreative.status == status
                    ).count()
                    status_counts[status] = count
                
                print(f"📊 当前状态分布:")
                for status, count in status_counts.items():
                    print(f"  - {status}: {count}")
                
                # 计算失败率
                total_processed = sum(status_counts.values())
                failed_count = status_counts.get('upload_failed', 0)
                
                if total_processed > 0:
                    failure_rate = (failed_count / total_processed) * 100
                    print(f"📊 总体失败率: {failure_rate:.2f}%")
                    
                    if failure_rate > 20:
                        print("⚠️ 失败率过高，建议优化重试逻辑")
                    elif failure_rate > 10:
                        print("💡 失败率偏高，可以进一步优化")
                    else:
                        print("✅ 失败率在合理范围内")
                
                return status_counts
                
        except Exception as e:
            print(f"❌ 分析重试模式失败: {e}")
            return {}
    
    def generate_optimized_retry_code(self) -> str:
        """生成优化后的重试代码示例"""
        return '''
def smart_retry_decorator(max_retries=3):
    """智能重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            retry_optimizer = SmartRetryOptimizer()
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    error_message = str(e)
                    
                    # 使用智能重试判断
                    should_retry = retry_optimizer.should_retry(
                        error_message, attempt, max_retries
                    )
                    
                    if not should_retry or attempt == max_retries:
                        # 不重试或达到最大重试次数
                        error_type = retry_optimizer.classify_error(error_message)
                        logger.error(f"❌ 任务失败 (错误类型: {error_type}): {error_message}")
                        raise e
                    
                    # 计算重试延迟
                    delay = retry_optimizer.get_retry_delay(error_message, attempt)
                    logger.warning(f"🔄 重试 {attempt + 1}/{max_retries} (延迟{delay}秒): {error_message}")
                    
                    time.sleep(delay)
            
        return wrapper
    return decorator
'''

def main():
    """主函数"""
    print("🚀 开始智能重试逻辑优化...")
    
    optimizer = SmartRetryOptimizer()
    
    # 1. 分析当前重试模式
    print(f"\n{'='*60}")
    print("📊 当前重试模式分析")
    print(f"{'='*60}")
    
    status_analysis = optimizer.analyze_current_retry_patterns()
    
    # 2. 展示错误分类规则
    print(f"\n{'='*60}")
    print("🏷️ 错误分类规则")
    print(f"{'='*60}")
    
    print(f"永久性错误 (不重试):")
    for error in optimizer.permanent_errors:
        print(f"  - {error}")
    
    print(f"\n临时性错误 (可重试):")
    for error in optimizer.temporary_errors:
        print(f"  - {error}")
    
    print(f"\n特殊错误 (特殊处理):")
    for error, action in optimizer.special_errors.items():
        print(f"  - {error} → {action}")
    
    # 3. 测试错误分类
    print(f"\n{'='*60}")
    print("🧪 错误分类测试")
    print(f"{'='*60}")
    
    test_errors = [
        "文件格式错误",
        "视频尺寸错误", 
        "网络连接超时",
        "Token权限不足",
        "未知错误类型"
    ]
    
    for error in test_errors:
        error_type = optimizer.classify_error(error)
        should_retry = optimizer.should_retry(error, 1, 3)
        retry_delay = optimizer.get_retry_delay(error, 1)
        
        print(f"错误: {error}")
        print(f"  类型: {error_type}")
        print(f"  是否重试: {should_retry}")
        print(f"  重试延迟: {retry_delay}秒")
        print()
    
    # 4. 生成优化建议
    print(f"\n{'='*60}")
    print("💡 优化建议")
    print(f"{'='*60}")
    
    suggestions = [
        "实现视频质量预检查，避免无效上传",
        "对永久性错误立即标记失败，不进行重试",
        "对临时性错误使用指数退避重试策略",
        "添加错误统计和分析功能",
        "实现智能错误分类和处理",
        "优化重试延迟时间，避免系统过载"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"{i}. {suggestion}")
    
    print(f"\n🎉 智能重试逻辑优化分析完成！")
    print(f"💡 建议实施上述优化措施以提升系统效率")
    
    return 0

if __name__ == "__main__":
    exit(main())
