# 千川系统重启指南

## 🚀 立即执行步骤

### 1. 停止当前服务
```bash
# 停止所有Celery进程
pkill -f celery
# 或者在Windows上
taskkill /f /im python.exe
```

### 2. 重启服务 (按顺序执行)

**第一个终端 - 启动Beat调度器:**
```bash
python run_celery_beat.py
```

**第二个终端 - 启动Worker:**
```bash
python run_celery_worker.py
```

### 3. 验证修复效果

**运行监控脚本:**
```bash
python ai_tools/ai_tool_20250722_realtime_monitor.py
```

**观察关键指标:**
- creating_plan跳过次数应该大幅减少
- 应该开始有新的计划创建
- approved状态素材开始正常处理

### 4. 预期效果时间线

- **立即生效**: 配置修复生效
- **5分钟内**: 跳过次数显著减少
- **10分钟内**: 开始创建新计划
- **30分钟内**: 系统稳定运行

## 🔍 故障排除

### 如果跳过次数仍然很高:
1. 检查config/settings.yml是否正确修改
2. 确认Celery服务已完全重启
3. 检查是否有其他配置冲突

### 如果没有新计划创建:
1. 检查账户是否有足够的approved素材
2. 确认账户状态为active
3. 检查是否有其他限制条件

### 如果出现错误:
1. 查看详细错误日志
2. 从备份恢复配置文件
3. 重新运行修复脚本

## 📊 成功指标

- ✅ creating_plan跳过次数 < 1000次/小时
- ✅ 每小时至少创建1个新计划
- ✅ approved状态素材数量逐渐减少
- ✅ 系统日志无严重错误

## 🆘 紧急回滚

如果修复导致问题:
```bash
# 恢复配置文件
cp backup_final_fix_*/settings.yml.bak config/settings.yml

# 重启服务
pkill -f celery
python run_celery_beat.py &
python run_celery_worker.py &
```
