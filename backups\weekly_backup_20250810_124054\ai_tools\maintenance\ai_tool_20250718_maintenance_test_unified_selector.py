#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时测试工具
生命周期: 7天
创建目的: 测试统一账户选择器功能
依赖关系: web_ui.py, 统一账户选择器组件
清理条件: 测试完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def test_unified_selector_import():
    """测试统一账户选择器导入"""
    print("🔍 测试统一账户选择器导入...")
    
    try:
        # 测试导入
        sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))
        from ai_tool_20250718_maintenance_unified_account_selector import (
            create_single_account_selector,
            create_multi_account_selector,
            get_account_options_dict,
            get_accounts_with_favorites
        )
        print("✅ 统一账户选择器导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 统一账户选择器导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接...")
    
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            count = db.query(AdAccount).count()
            print(f"✅ 数据库连接成功，找到 {count} 个广告账户")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_get_accounts_function():
    """测试获取账户函数"""
    print("📋 测试获取账户函数...")
    
    try:
        from ai_tool_20250718_maintenance_unified_account_selector import get_accounts_with_favorites
        
        accounts = get_accounts_with_favorites()
        print(f"✅ 成功获取 {len(accounts)} 个账户")
        
        # 检查收藏状态
        favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
        print(f"   其中收藏账户: {favorite_count} 个")
        
        # 显示前几个账户信息
        if accounts:
            print("   前3个账户:")
            for i, acc in enumerate(accounts[:3]):
                is_favorite = getattr(acc, 'is_favorite', False)
                star = "⭐ " if is_favorite else ""
                print(f"   {i+1}. {star}{acc.name} ({acc.account_id_qc})")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取账户函数测试失败: {e}")
        return False

def test_account_options_dict():
    """测试账户选项字典函数"""
    print("📝 测试账户选项字典函数...")
    
    try:
        from ai_tool_20250718_maintenance_unified_account_selector import get_account_options_dict
        
        options = get_account_options_dict()
        print(f"✅ 成功生成 {len(options)} 个账户选项")
        
        # 显示前几个选项
        if options:
            print("   前3个选项:")
            for i, (display_name, account_id) in enumerate(list(options.items())[:3]):
                print(f"   {i+1}. {display_name} -> {account_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 账户选项字典函数测试失败: {e}")
        return False

def test_web_ui_integration():
    """测试Web UI集成"""
    print("🌐 测试Web UI集成...")
    
    try:
        # 检查web_ui.py中的导入
        web_ui_path = project_root / "web_ui.py"
        if not web_ui_path.exists():
            print("❌ web_ui.py文件不存在")
            return False
        
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含统一选择器的导入
        if "ai_tool_20250718_maintenance_unified_account_selector" in content:
            print("✅ Web UI已集成统一账户选择器导入")
        else:
            print("⚠️ Web UI未找到统一账户选择器导入")
            return False
        
        # 检查是否使用了新的选择器函数
        if "create_single_account_selector" in content:
            print("✅ Web UI使用了单选账户选择器")
        else:
            print("⚠️ Web UI未使用单选账户选择器")
        
        if "create_multi_account_selector" in content:
            print("✅ Web UI使用了多选账户选择器")
        else:
            print("⚠️ Web UI未使用多选账户选择器")
        
        return True
        
    except Exception as e:
        print(f"❌ Web UI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 统一账户选择器功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_unified_selector_import),
        ("数据库连接测试", test_database_connection),
        ("获取账户函数测试", test_get_accounts_function),
        ("账户选项字典测试", test_account_options_dict),
        ("Web UI集成测试", test_web_ui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一账户选择器功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
