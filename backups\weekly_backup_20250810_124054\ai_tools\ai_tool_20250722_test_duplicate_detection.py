#!/usr/bin/env python3
"""
测试重复检测逻辑是否正常工作
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def test_duplicate_detection():
    """测试重复检测逻辑"""
    logger.info("🧪 测试重复检测逻辑")
    
    try:
        with database_session() as db:
            # 查找有计划的视频
            test_query = text("""
                SELECT 
                    lc.filename,
                    lc.file_hash,
                    COUNT(c.id) as campaign_count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                GROUP BY lc.filename, lc.file_hash
                LIMIT 5
            """)
            
            results = db.execute(test_query).fetchall()
            
            logger.info("📊 测试样本:")
            for result in results:
                logger.info(f"  {result.filename}: {result.campaign_count}个计划")
                
                # 模拟重复检测逻辑
                hash_check = db.query(text("""
                    SELECT COUNT(*) as count
                    FROM campaigns c
                    JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                    JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                    JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE lc.file_hash = :file_hash
                """)).params(file_hash=result.file_hash).scalar()
                
                if hash_check > 0:
                    logger.info(f"    ✅ 重复检测生效: 发现{hash_check}个计划")
                else:
                    logger.warning(f"    ❌ 重复检测失效")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_duplicate_detection()
