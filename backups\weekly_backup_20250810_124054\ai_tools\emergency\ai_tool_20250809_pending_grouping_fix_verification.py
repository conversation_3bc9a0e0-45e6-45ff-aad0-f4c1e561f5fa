#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证pending_grouping状态转换修复效果
清理条件: 成为状态转换验证工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Principal


class PendingGroupingFixVerifier:
    """pending_grouping状态转换修复验证器"""
    
    def __init__(self):
        self.verification_results = {}
    
    def run_comprehensive_verification(self):
        """运行全面验证"""
        logger.info("🔍 pending_grouping状态转换修复验证")
        logger.info("="*100)
        
        # 1. 检查当前pending_grouping状态
        current_status = self._check_current_pending_grouping_status()
        
        # 2. 验证修复代码
        code_fix_verified = self._verify_code_fix()
        
        # 3. 模拟测试状态转换
        simulation_result = self._simulate_status_conversion()
        
        # 4. 检查文件系统一致性
        file_consistency = self._check_file_system_consistency()
        
        # 5. 生成综合报告
        self._generate_comprehensive_report({
            'current_status': current_status,
            'code_fix_verified': code_fix_verified,
            'simulation_result': simulation_result,
            'file_consistency': file_consistency
        })
    
    def _check_current_pending_grouping_status(self):
        """检查当前pending_grouping状态"""
        logger.info("📊 检查当前pending_grouping状态...")
        
        with SessionLocal() as db:
            # 按主体统计pending_grouping状态
            pending_grouping_stats = db.execute("""
                SELECT 
                    p.name as principal_name,
                    COUNT(lc.id) as pending_count,
                    MIN(lc.created_at) as earliest_created,
                    MAX(lc.updated_at) as latest_updated
                FROM local_creatives lc
                JOIN principals p ON lc.principal_id = p.id
                WHERE lc.status = MaterialStatus.PENDING_GROUPING.value
                GROUP BY p.name
                ORDER BY pending_count DESC
            """).fetchall()
            
            total_pending = sum(row.pending_count for row in pending_grouping_stats)
            
            logger.info(f"📋 pending_grouping状态统计:")
            logger.info(f"   总计: {total_pending} 个素材")
            
            for row in pending_grouping_stats:
                logger.info(f"   📁 {row.principal_name}: {row.pending_count} 个")
                logger.info(f"      最早创建: {row.earliest_created}")
                logger.info(f"      最近更新: {row.latest_updated}")
            
            # 特别关注缇萃百货
            ticui_pending = next((row.pending_count for row in pending_grouping_stats if row.principal_name == '缇萃百货'), 0)
            
            if ticui_pending > 0:
                logger.warning(f"⚠️ 缇萃百货有 {ticui_pending} 个pending_grouping状态素材需要处理")
            else:
                logger.success("✅ 缇萃百货没有pending_grouping状态素材")
            
            return {
                'total_pending': total_pending,
                'ticui_pending': ticui_pending,
                'principal_stats': {row.principal_name: row.pending_count for row in pending_grouping_stats}
            }
    
    def _verify_code_fix(self):
        """验证代码修复"""
        logger.info("🔧 验证batch_upload_videos任务修复...")
        
        try:
            tasks_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/tasks.py')
            
            with open(tasks_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查修复的关键代码
            fix_checks = {
                'pending_grouping_query': MaterialStatus.PENDING_GROUPING.value in content and 'batch_upload_videos' in content,
                'status_conversion': 'creative.status = \'pending_upload\'' in content,
                'commit_changes': 'db.commit()' in content and MaterialStatus.PENDING_GROUPING.value in content,
                'success_logging': '成功转换' in content or 'Successfully converted' in content,
                'emergency_fix_comment': '紧急修复' in content or 'Emergency Fix' in content
            }
            
            fixes_applied = sum(fix_checks.values())
            total_checks = len(fix_checks)
            
            logger.info(f"📊 代码修复检查: {fixes_applied}/{total_checks}")
            
            for check_name, passed in fix_checks.items():
                status_icon = "✅" if passed else "❌"
                logger.info(f"   {status_icon} {check_name}: {'通过' if passed else '失败'}")
            
            if fixes_applied >= 4:
                logger.success("✅ batch_upload_videos任务修复验证通过")
                return True
            else:
                logger.error("❌ batch_upload_videos任务修复不完整")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证代码修复失败: {e}")
            return False
    
    def _simulate_status_conversion(self):
        """模拟状态转换"""
        logger.info("🧪 模拟状态转换测试...")
        
        try:
            with SessionLocal() as db:
                # 查找一个pending_grouping状态的素材进行测试
                test_creative = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PENDING_GROUPING.value
                ).first()
                
                if not test_creative:
                    logger.info("ℹ️ 没有pending_grouping状态素材可供测试")
                    return {'test_possible': False, 'reason': 'no_pending_grouping_materials'}
                
                original_status = test_creative.status
                original_updated_at = test_creative.updated_at
                
                logger.info(f"🎯 测试素材: {os.path.basename(test_creative.file_path)}")
                logger.info(f"   原始状态: {original_status}")
                logger.info(f"   原始更新时间: {original_updated_at}")
                
                # 模拟状态转换（不实际提交）
                test_creative.status = MaterialStatus.PENDING_UPLOAD.value
                test_creative.updated_at = datetime.now(timezone.utc)
                
                new_status = test_creative.status
                new_updated_at = test_creative.updated_at
                
                logger.info(f"   新状态: {new_status}")
                logger.info(f"   新更新时间: {new_updated_at}")
                
                # 回滚测试（不提交更改）
                db.rollback()
                
                logger.success("✅ 状态转换模拟测试成功")
                
                return {
                    'test_possible': True,
                    'original_status': original_status,
                    'new_status': new_status,
                    'conversion_successful': new_status == MaterialStatus.PENDING_UPLOAD.value
                }
                
        except Exception as e:
            logger.error(f"❌ 状态转换模拟测试失败: {e}")
            return {'test_possible': False, 'error': str(e)}
    
    def _check_file_system_consistency(self):
        """检查文件系统一致性"""
        logger.info("📁 检查文件系统一致性...")
        
        try:
            with SessionLocal() as db:
                # 检查缇萃百货的pending_grouping素材
                ticui_creatives = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == MaterialStatus.PENDING_GROUPING.value
                ).all()
                
                if not ticui_creatives:
                    logger.info("ℹ️ 缇萃百货没有pending_grouping状态素材")
                    return {'consistency_check': True, 'missing_files': 0, 'total_checked': 0}
                
                logger.info(f"🔍 检查 {len(ticui_creatives)} 个缇萃百货pending_grouping素材的文件存在性...")
                
                missing_files = []
                existing_files = []
                
                for creative in ticui_creatives:
                    if os.path.exists(creative.file_path):
                        existing_files.append(creative.file_path)
                    else:
                        missing_files.append(creative.file_path)
                        logger.warning(f"⚠️ 文件不存在: {creative.file_path}")
                
                logger.info(f"📊 文件存在性检查结果:")
                logger.info(f"   ✅ 存在的文件: {len(existing_files)} 个")
                logger.info(f"   ❌ 缺失的文件: {len(missing_files)} 个")
                
                consistency_good = len(missing_files) == 0
                
                if consistency_good:
                    logger.success("✅ 文件系统一致性检查通过")
                else:
                    logger.warning(f"⚠️ 发现 {len(missing_files)} 个缺失文件")
                
                return {
                    'consistency_check': consistency_good,
                    'missing_files': len(missing_files),
                    'existing_files': len(existing_files),
                    'total_checked': len(ticui_creatives)
                }
                
        except Exception as e:
            logger.error(f"❌ 文件系统一致性检查失败: {e}")
            return {'consistency_check': False, 'error': str(e)}
    
    def _generate_comprehensive_report(self, results):
        """生成综合报告"""
        logger.info("\n📋 pending_grouping状态转换修复综合报告")
        logger.info("="*100)
        
        current_status = results['current_status']
        code_fix_verified = results['code_fix_verified']
        simulation_result = results['simulation_result']
        file_consistency = results['file_consistency']
        
        # 当前状态报告
        logger.info("📊 当前状态:")
        logger.info(f"   总pending_grouping素材: {current_status['total_pending']} 个")
        logger.info(f"   缇萃百货pending_grouping: {current_status['ticui_pending']} 个")
        
        # 修复验证报告
        if code_fix_verified:
            logger.success("✅ 代码修复: batch_upload_videos任务已修复")
        else:
            logger.error("❌ 代码修复: batch_upload_videos任务修复不完整")
        
        # 模拟测试报告
        if simulation_result['test_possible']:
            if simulation_result['conversion_successful']:
                logger.success("✅ 状态转换测试: 模拟转换成功")
            else:
                logger.error("❌ 状态转换测试: 模拟转换失败")
        else:
            logger.info("ℹ️ 状态转换测试: 无法进行测试")
        
        # 文件一致性报告
        if file_consistency['consistency_check']:
            logger.success("✅ 文件一致性: 所有文件都存在")
        else:
            logger.warning(f"⚠️ 文件一致性: {file_consistency.get('missing_files', 0)} 个文件缺失")
        
        # 总体评估
        success_count = sum([
            code_fix_verified,
            simulation_result.get('conversion_successful', False),
            file_consistency['consistency_check']
        ])
        
        logger.info(f"\n🎯 修复验证总分: {success_count}/3")
        
        if success_count >= 2:
            logger.success("🎊 pending_grouping状态转换修复验证通过")
            logger.success("🚀 建议：立即测试batch_upload_videos任务")
            
            if current_status['ticui_pending'] > 0:
                logger.info(f"\n💡 下一步行动:")
                logger.info(f"   1. 手动触发batch_upload_videos任务")
                logger.info(f"   2. 观察 {current_status['ticui_pending']} 个缇萃百货素材是否转换")
                logger.info(f"   3. 监控上传进度")
            
            return True
        else:
            logger.error("❌ pending_grouping状态转换修复验证失败")
            logger.error("🔧 需要进一步检查和修复")
            return False


def main():
    """主函数"""
    verifier = PendingGroupingFixVerifier()
    
    logger.info("🚀 启动pending_grouping状态转换修复验证")
    logger.info("🎯 目标：确认166个缇萃百货素材可以正常处理")
    
    success = verifier.run_comprehensive_verification()
    
    if success:
        logger.success("🎊 pending_grouping状态转换修复验证完成")
        logger.success("💡 建议：立即测试batch_upload_videos任务效果")
        return 0
    else:
        logger.error("❌ pending_grouping状态转换修复验证失败")
        logger.error("🔧 建议：检查修复代码并重新测试")
        return 1


if __name__ == "__main__":
    exit(main())
