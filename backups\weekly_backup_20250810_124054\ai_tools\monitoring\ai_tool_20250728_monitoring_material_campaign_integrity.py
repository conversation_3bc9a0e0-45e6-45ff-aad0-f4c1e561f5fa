#!/usr/bin/env python3
"""素材-计划关联完整性监控脚本"""

import sys
sys.path.insert(0, r"D:\Project\qianchuangzl\src")

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import PlatformCreative
from qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def check_material_campaign_integrity():
    """检查素材-计划关联完整性"""
    logger.info("🔍 检查素材-计划关联完整性...")

    with database_session() as db:
        # 查找所有已上传但未创建计划的素材
        orphaned_materials = db.execute(text("""
            SELECT pc.id, pc.material_id_qc
            FROM platform_creatives pc
            WHERE pc.material_id_qc IS NOT NULL
            AND pc.id NOT IN (
                SELECT platform_creative_id
                FROM campaign_platform_creative_association
            )
        """)).fetchall()
        
        if orphaned_materials:
            logger.warning(f"⚠️ 发现{len(orphaned_materials)}个已上传但未创建计划的素材:")
            for material in orphaned_materials:
                logger.warning(f"  - 素材ID: {material.material_id_qc}")
        else:
            logger.success("✅ 所有已上传素材都已创建测试计划")

        return len(orphaned_materials)

if __name__ == "__main__":
    orphaned_count = check_material_campaign_integrity()
    if orphaned_count > 0:
        logger.critical(f"🚨 发现{orphaned_count}个遗漏素材，需要立即修复！")
        exit(1)
    else:
        logger.success("✅ 素材-计划关联完整性检查通过")
        exit(0)
