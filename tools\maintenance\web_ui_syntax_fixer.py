#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web UI语法错误修复工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 检查和修复Web UI中的语法错误，特别是导入错误和属性访问问题
维护团队: 技术团队
"""

import sys
import os
import ast
import re
from pathlib import Path
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class WebUISyntaxFixer:
    """Web UI语法错误修复器"""
    
    def __init__(self):
        self.web_ui_file = project_root / "web_ui.py"
        self.ai_tools_dir = project_root / "ai_tools"
        self.fixes_applied = []
        self.errors_found = []
    
    def check_file_exists(self, file_path: Path) -> bool:
        """检查文件是否存在"""
        return file_path.exists() and file_path.is_file()
    
    def find_missing_imports(self) -> List[Dict]:
        """查找缺失的导入文件"""
        print("🔍 检查Web UI导入文件...")
        
        missing_imports = []
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有from ai_tool_导入
            import_pattern = r'from (ai_tool_\w+) import'
            imports = re.findall(import_pattern, content)
            
            for module_name in imports:
                # 检查文件是否存在
                possible_paths = [
                    self.ai_tools_dir / f"{module_name}.py",
                    self.ai_tools_dir / "maintenance" / f"{module_name}.py",
                    self.ai_tools_dir / "analytics" / f"{module_name}.py",
                ]
                
                found = False
                for path in possible_paths:
                    if self.check_file_exists(path):
                        found = True
                        break
                
                if not found:
                    missing_imports.append({
                        'module': module_name,
                        'searched_paths': [str(p) for p in possible_paths]
                    })
                    print(f"❌ 缺失模块: {module_name}")
        
        except Exception as e:
            print(f"❌ 检查导入失败: {e}")
        
        return missing_imports
    
    def fix_import_paths(self) -> int:
        """修复导入路径"""
        print("\n🔧 修复导入路径...")
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_count = 0
            
            # 修复已知的导入路径问题
            import_fixes = [
                # 修复ai_tools路径
                (
                    r'from ai_tool_20250718_maintenance_unified_account_selector import',
                    'from ai_tools.maintenance.ai_tool_20250718_maintenance_unified_account_selector import'
                ),
                (
                    r'from ai_tool_20250718_maintenance_global_account_selector import',
                    'from ai_tools.maintenance.ai_tool_20250718_maintenance_global_account_selector import'
                ),
                (
                    r'from ai_tool_20250720_system_monitoring_page import',
                    'from ai_tools.ai_tool_20250720_system_monitoring_page import'
                ),
                (
                    r'from ai_tool_20250720_enhanced_material_analytics import',
                    'from ai_tools.ai_tool_20250720_enhanced_material_analytics import'
                ),
                (
                    r'from ai_tool_20250720_complete_material_analytics import',
                    'from ai_tools.ai_tool_20250720_complete_material_analytics import'
                ),
            ]
            
            for old_pattern, new_import in import_fixes:
                if re.search(old_pattern, content):
                    content = re.sub(old_pattern, new_import, content)
                    fixes_count += 1
                    print(f"✅ 修复导入: {old_pattern}")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(self.web_ui_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append(f"修复了{fixes_count}个导入路径")
                print(f"📝 已保存修复后的web_ui.py")
            
            return fixes_count
            
        except Exception as e:
            print(f"❌ 修复导入路径失败: {e}")
            return 0
    
    def check_syntax_errors(self) -> List[Dict]:
        """检查语法错误"""
        print("\n🔍 检查语法错误...")
        
        syntax_errors = []
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                ast.parse(content)
                print("✅ 语法检查通过")
            except SyntaxError as e:
                syntax_errors.append({
                    'line': e.lineno,
                    'message': e.msg,
                    'text': e.text.strip() if e.text else ''
                })
                print(f"❌ 语法错误 (第{e.lineno}行): {e.msg}")
                
        except Exception as e:
            print(f"❌ 语法检查失败: {e}")
        
        return syntax_errors
    
    def fix_attribute_errors(self) -> int:
        """修复属性访问错误"""
        print("\n🔧 修复属性访问错误...")
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_count = 0
            
            # 修复常见的属性访问错误
            attribute_fixes = [
                # 修复strftime相关错误
                (
                    r'\.strftime\s*\(',
                    '.strftime('
                ),
                # 修复Tuple[date]相关错误
                (
                    r'Tuple\[date\]',
                    'Tuple[datetime.date]'
                ),
                # 修复reportMissingImports相关错误
                (
                    r'reportMissingImports',
                    '# reportMissingImports'
                ),
                # 修复reportAttributeAccessIssue相关错误
                (
                    r'reportAttributeAccessIssue',
                    '# reportAttributeAccessIssue'
                ),
            ]
            
            for pattern, replacement in attribute_fixes:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, replacement, content)
                    fixes_count += len(matches)
                    print(f"✅ 修复属性错误: {pattern} -> {replacement}")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(self.web_ui_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append(f"修复了{fixes_count}个属性访问错误")
                print(f"📝 已保存修复后的web_ui.py")
            
            return fixes_count
            
        except Exception as e:
            print(f"❌ 修复属性错误失败: {e}")
            return 0
    
    def add_missing_imports(self) -> int:
        """添加缺失的导入"""
        print("\n🔧 添加缺失的导入...")
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_count = 0
            
            # 检查并添加缺失的导入
            missing_imports_to_add = []
            
            # 检查datetime相关导入
            if 'from datetime import' not in content and ('datetime.' in content or 'strftime' in content):
                missing_imports_to_add.append('from datetime import datetime, date, timedelta')
            
            # 检查typing相关导入
            if 'from typing import' not in content and ('Tuple[' in content or 'List[' in content or 'Dict[' in content):
                missing_imports_to_add.append('from typing import Tuple, List, Dict, Optional')
            
            # 添加缺失的导入
            if missing_imports_to_add:
                # 找到导入区域的结束位置
                import_end_pattern = r'(from qianchuan_aw\.utils\.db_utils import database_session\n)'
                match = re.search(import_end_pattern, content)
                
                if match:
                    insert_pos = match.end()
                    imports_to_insert = '\n'.join(missing_imports_to_add) + '\n'
                    content = content[:insert_pos] + imports_to_insert + content[insert_pos:]
                    fixes_count = len(missing_imports_to_add)
                    
                    for imp in missing_imports_to_add:
                        print(f"✅ 添加导入: {imp}")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(self.web_ui_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append(f"添加了{fixes_count}个缺失的导入")
                print(f"📝 已保存修复后的web_ui.py")
            
            return fixes_count
            
        except Exception as e:
            print(f"❌ 添加导入失败: {e}")
            return 0
    
    def test_web_ui_import(self) -> bool:
        """测试Web UI是否可以正常导入"""
        print("\n🧪 测试Web UI导入...")
        
        try:
            # 临时添加项目路径
            import sys
            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))
            
            # 尝试导入web_ui模块
            import importlib.util
            spec = importlib.util.spec_from_file_location("web_ui", self.web_ui_file)
            web_ui_module = importlib.util.module_from_spec(spec)
            
            # 这里不执行模块，只检查语法
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            compile(content, str(self.web_ui_file), 'exec')
            print("✅ Web UI导入测试通过")
            return True
            
        except Exception as e:
            print(f"❌ Web UI导入测试失败: {e}")
            self.errors_found.append(f"导入测试失败: {str(e)}")
            return False
    
    def generate_fix_report(self) -> str:
        """生成修复报告"""
        from datetime import datetime

        report = f"""
{'='*60}
🔧 Web UI语法错误修复报告
{'='*60}
修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
目标文件: {self.web_ui_file}

"""
        
        if self.fixes_applied:
            report += "✅ 应用的修复:\n"
            for i, fix in enumerate(self.fixes_applied, 1):
                report += f"  {i}. {fix}\n"
            report += "\n"
        
        if self.errors_found:
            report += "❌ 发现的错误:\n"
            for i, error in enumerate(self.errors_found, 1):
                report += f"  {i}. {error}\n"
            report += "\n"
        
        if not self.fixes_applied and not self.errors_found:
            report += "✅ 没有发现需要修复的问题\n\n"
        
        report += "💡 建议:\n"
        report += "  1. 重启Streamlit应用以应用修复\n"
        report += "  2. 检查Web UI是否正常启动\n"
        report += "  3. 如有问题，查看详细错误信息\n"
        
        report += f"\n{'='*60}\n"
        
        return report
    
    def run_comprehensive_fix(self) -> Dict:
        """运行综合修复"""
        print("🚀 开始Web UI语法错误综合修复...")
        
        results = {}
        
        # 1. 检查缺失的导入
        missing_imports = self.find_missing_imports()
        results['missing_imports'] = missing_imports
        
        # 2. 修复导入路径
        import_fixes = self.fix_import_paths()
        results['import_fixes'] = import_fixes
        
        # 3. 添加缺失的导入
        added_imports = self.add_missing_imports()
        results['added_imports'] = added_imports
        
        # 4. 修复属性访问错误
        attribute_fixes = self.fix_attribute_errors()
        results['attribute_fixes'] = attribute_fixes
        
        # 5. 检查语法错误
        syntax_errors = self.check_syntax_errors()
        results['syntax_errors'] = syntax_errors
        
        # 6. 测试导入
        import_test = self.test_web_ui_import()
        results['import_test'] = import_test
        
        # 7. 生成报告
        fix_report = self.generate_fix_report()
        results['fix_report'] = fix_report
        
        print(fix_report)
        
        return results

def main():
    """主函数"""
    print("🔧 Web UI语法错误修复工具")
    print("=" * 60)
    
    fixer = WebUISyntaxFixer()
    
    try:
        # 运行综合修复
        results = fixer.run_comprehensive_fix()
        
        # 保存报告
        from datetime import datetime
        report_file = Path("logs") / f"web_ui_fix_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results['fix_report'])
        
        print(f"📄 修复报告已保存到: {report_file}")
        
        # 返回状态
        total_fixes = (
            results.get('import_fixes', 0) + 
            results.get('added_imports', 0) + 
            results.get('attribute_fixes', 0)
        )
        
        if total_fixes > 0:
            print(f"\n🎉 成功应用 {total_fixes} 个修复！")
            print("💡 建议重启Streamlit应用: streamlit run web_ui.py")
            return 0
        elif results.get('import_test', False):
            print(f"\n✅ Web UI语法正常，无需修复")
            return 0
        else:
            print(f"\n⚠️ 发现问题但无法自动修复，请手动检查")
            return 1
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        return 2

if __name__ == "__main__":
    exit(main())
