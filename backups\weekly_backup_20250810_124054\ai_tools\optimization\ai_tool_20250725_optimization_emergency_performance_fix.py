#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急性能优化修复工具
清理条件: 长期保留，用于性能问题修复

千川自动化项目紧急性能优化工具
============================

解决CPU 100%占用和系统卡顿问题的紧急修复工具。
"""

import os
import sys
import shutil
import psutil
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class EmergencyPerformanceFixer:
    """紧急性能修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_dir = os.path.join(project_root, 'ai_temp', 'performance_fix_backup')
        os.makedirs(self.backup_dir, exist_ok=True)
    
    def check_system_status(self):
        """检查系统状态"""
        logger.info("🔍 检查系统状态...")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        logger.info(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        logger.info(f"内存使用率: {memory.percent}%")
        
        # 进程数量
        process_count = len(psutil.pids())
        logger.info(f"活跃进程数: {process_count}")
        
        # 检查Celery进程
        celery_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('celery' in arg for arg in proc.info['cmdline']):
                    celery_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        logger.info(f"Celery进程数: {len(celery_processes)}")
        for proc in celery_processes:
            try:
                cpu_percent = proc.cpu_percent()
                memory_mb = proc.memory_info().rss / 1024 / 1024
                logger.info(f"  PID {proc.pid}: CPU {cpu_percent}%, 内存 {memory_mb:.1f}MB")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        # 检查浏览器进程
        browser_processes = []
        browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if any(browser in proc.info['name'].lower() for browser in browser_names):
                    browser_processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        logger.info(f"浏览器进程数: {len(browser_processes)}")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'process_count': process_count,
            'celery_processes': len(celery_processes),
            'browser_processes': len(browser_processes)
        }
    
    def backup_files(self):
        """备份关键文件"""
        logger.info("💾 备份关键文件...")
        
        files_to_backup = [
            'run_celery_worker.py',
            'run_celery_beat.py',
            'config/settings.yml'
        ]
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for file_path in files_to_backup:
            full_path = os.path.join(self.project_root, file_path)
            if os.path.exists(full_path):
                backup_name = f"{os.path.basename(file_path)}.backup_{timestamp}"
                backup_path = os.path.join(self.backup_dir, backup_name)
                shutil.copy2(full_path, backup_path)
                logger.info(f"✅ 已备份: {file_path} -> {backup_name}")
            else:
                logger.warning(f"⚠️ 文件不存在: {file_path}")
    
    def kill_celery_processes(self):
        """终止Celery进程"""
        logger.info("🔪 终止Celery进程...")
        
        killed_count = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('celery' in arg for arg in proc.info['cmdline']):
                    logger.info(f"终止Celery进程: PID {proc.pid}")
                    proc.terminate()
                    killed_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                pass
        
        # 等待进程终止
        time.sleep(3)
        
        # 强制杀死未终止的进程
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('celery' in arg for arg in proc.info['cmdline']):
                    logger.warning(f"强制杀死Celery进程: PID {proc.pid}")
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        logger.info(f"✅ 已终止 {killed_count} 个Celery进程")
    
    def optimize_celery_worker_config(self):
        """优化Celery Worker配置"""
        logger.info("⚙️ 优化Celery Worker配置...")
        
        worker_file = os.path.join(self.project_root, 'run_celery_worker.py')
        
        if not os.path.exists(worker_file):
            logger.error(f"❌ 文件不存在: {worker_file}")
            return False
        
        # 读取原文件
        with open(worker_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改并发数配置
        old_config = "'-c', '5'"
        new_config = "'-c', '2'"
        
        if old_config in content:
            content = content.replace(old_config, new_config)
            logger.info(f"✅ 并发数从5降低到2")
        else:
            logger.warning("⚠️ 未找到并发数配置，可能已经修改过")
        
        # 添加任务限制配置
        if "'--max-tasks-per-child'" not in content:
            # 在sys.argv列表中添加新配置
            insert_pos = content.find("'-c', '2'")
            if insert_pos != -1:
                # 找到下一行的位置
                next_line_pos = content.find('\n', insert_pos)
                if next_line_pos != -1:
                    new_line = "        '--max-tasks-per-child', '10',  # 限制每个进程任务数\n"
                    content = content[:next_line_pos] + '\n' + new_line + content[next_line_pos+1:]
                    logger.info("✅ 添加了任务数限制配置")
        
        # 写回文件
        with open(worker_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ Celery Worker配置优化完成")
        return True
    
    def disable_appeal_module(self):
        """暂时禁用提审模块"""
        logger.info("🚫 暂时禁用提审模块...")
        
        settings_file = os.path.join(self.project_root, 'config', 'settings.yml')
        
        if not os.path.exists(settings_file):
            logger.error(f"❌ 配置文件不存在: {settings_file}")
            return False
        
        # 读取配置文件
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并修改提审任务配置
        lines = content.split('\n')
        modified = False
        in_appeal_section = False
        
        for i, line in enumerate(lines):
            if 'appeal_plans:' in line:
                in_appeal_section = True
                continue
            
            if in_appeal_section:
                if line.strip().startswith('task:') or line.strip().startswith('schedule:'):
                    continue
                elif line.strip().startswith('enabled:'):
                    if 'true' in line.lower():
                        lines[i] = line.replace('true', 'false').replace('True', 'false')
                        modified = True
                        logger.info("✅ 已禁用提审任务")
                    break
                elif line.strip() and not line.startswith(' '):
                    # 进入下一个配置节
                    break
        
        if not modified:
            # 如果没有找到enabled配置，添加一个
            for i, line in enumerate(lines):
                if 'appeal_plans:' in line:
                    # 在下一行添加enabled: false
                    lines.insert(i + 1, '    enabled: false  # 临时禁用以优化性能')
                    modified = True
                    logger.info("✅ 添加了提审任务禁用配置")
                    break
        
        if modified:
            # 写回文件
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            logger.info("✅ 提审模块已暂时禁用")
        else:
            logger.warning("⚠️ 未找到提审模块配置")
        
        return modified
    
    def restart_celery_services(self):
        """重启Celery服务"""
        logger.info("🔄 重启Celery服务...")
        
        # 启动Beat进程
        beat_file = os.path.join(self.project_root, 'run_celery_beat.py')
        worker_file = os.path.join(self.project_root, 'run_celery_worker.py')
        
        if os.path.exists(beat_file) and os.path.exists(worker_file):
            logger.info("启动Celery Beat进程...")
            os.system(f'start /B python "{beat_file}"')
            
            time.sleep(2)
            
            logger.info("启动Celery Worker进程...")
            os.system(f'start /B python "{worker_file}"')
            
            logger.info("✅ Celery服务重启完成")
        else:
            logger.error("❌ Celery启动文件不存在")
    
    def verify_optimization(self):
        """验证优化效果"""
        logger.info("🔍 验证优化效果...")
        
        # 等待服务启动
        time.sleep(10)
        
        # 重新检查系统状态
        new_status = self.check_system_status()
        
        logger.info("📊 优化效果对比:")
        logger.info(f"CPU使用率: {new_status['cpu_percent']}%")
        logger.info(f"内存使用率: {new_status['memory_percent']}%")
        logger.info(f"Celery进程数: {new_status['celery_processes']}")
        logger.info(f"浏览器进程数: {new_status['browser_processes']}")
        
        # 判断优化效果
        if new_status['cpu_percent'] < 70:
            logger.info("✅ CPU使用率已降低到正常范围")
        else:
            logger.warning("⚠️ CPU使用率仍然较高，可能需要进一步优化")
        
        if new_status['celery_processes'] <= 2:
            logger.info("✅ Celery进程数已控制在合理范围")
        else:
            logger.warning("⚠️ Celery进程数仍然较多")
        
        return new_status
    
    def generate_optimization_report(self, before_status, after_status):
        """生成优化报告"""
        logger.info("📋 生成优化报告...")
        
        report = f"""
千川自动化项目紧急性能优化报告
============================

优化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

优化前状态:
- CPU使用率: {before_status['cpu_percent']}%
- 内存使用率: {before_status['memory_percent']}%
- Celery进程数: {before_status['celery_processes']}
- 浏览器进程数: {before_status['browser_processes']}

优化后状态:
- CPU使用率: {after_status['cpu_percent']}%
- 内存使用率: {after_status['memory_percent']}%
- Celery进程数: {after_status['celery_processes']}
- 浏览器进程数: {after_status['browser_processes']}

执行的优化措施:
1. ✅ 降低Celery并发数从5到2
2. ✅ 添加任务数限制配置
3. ✅ 暂时禁用提审模块
4. ✅ 重启Celery服务

优化效果:
- CPU使用率变化: {after_status['cpu_percent'] - before_status['cpu_percent']:+.1f}%
- 内存使用率变化: {after_status['memory_percent'] - before_status['memory_percent']:+.1f}%

后续建议:
1. 监控系统运行状态，确保稳定性
2. 逐步实施中期优化方案
3. 考虑重新启用提审模块（优化后）
4. 建立长期性能监控机制
"""
        
        report_file = os.path.join(self.backup_dir, f'optimization_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 优化报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚀 开始千川自动化项目紧急性能优化...")
    
    fixer = EmergencyPerformanceFixer()
    
    try:
        # 1. 检查优化前状态
        logger.info("=" * 50)
        logger.info("第一步: 检查优化前状态")
        before_status = fixer.check_system_status()
        
        # 2. 备份关键文件
        logger.info("=" * 50)
        logger.info("第二步: 备份关键文件")
        fixer.backup_files()
        
        # 3. 终止现有Celery进程
        logger.info("=" * 50)
        logger.info("第三步: 终止现有Celery进程")
        fixer.kill_celery_processes()
        
        # 4. 优化配置
        logger.info("=" * 50)
        logger.info("第四步: 优化配置")
        fixer.optimize_celery_worker_config()
        fixer.disable_appeal_module()
        
        # 5. 重启服务
        logger.info("=" * 50)
        logger.info("第五步: 重启服务")
        fixer.restart_celery_services()
        
        # 6. 验证优化效果
        logger.info("=" * 50)
        logger.info("第六步: 验证优化效果")
        after_status = fixer.verify_optimization()
        
        # 7. 生成报告
        logger.info("=" * 50)
        logger.info("第七步: 生成优化报告")
        report = fixer.generate_optimization_report(before_status, after_status)
        
        logger.info("=" * 50)
        logger.info("🎉 紧急性能优化完成！")
        logger.info("💡 建议继续监控系统状态，确保优化效果持续")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 优化过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
