#!/usr/bin/env python3
"""
千川视频素材工作流快速文件修复工具
专注于立即修复关键问题
"""

import sys
import os
from pathlib import Path
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def quick_fix_file_paths():
    """快速修复文件路径问题"""
    logger.info("🔧 开始快速修复文件路径...")
    
    with database_session() as db:
        # 查找物理文件不存在的记录
        missing_query = text("""
            SELECT 
                lc.id,
                lc.filename,
                lc.file_path,
                lc.status
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.file_path IS NOT NULL AND lc.file_path != ''
                AND lc.status IN (MaterialStatus.PROCESSING.value, MaterialStatus.UPLOAD_FAILED.value)
            ORDER BY lc.updated_at DESC
            LIMIT 20
        """)
        
        missing_results = db.execute(missing_query).fetchall()
        fixed_count = 0
        
        logger.info(f"找到 {len(missing_results)} 个需要检查的记录")
        
        for row in missing_results:
            if not os.path.exists(row.file_path):
                # 尝试在归档目录找到文件
                archived_path = row.file_path.replace(
                    "01_materials_to_process", 
                    "00_materials_archived"
                )
                
                if os.path.exists(archived_path):
                    logger.info(f"找到文件新位置: {row.filename}")
                    logger.info(f"  原路径: {row.file_path}")
                    logger.info(f"  新路径: {archived_path}")
                    
                    # 更新数据库中的文件路径
                    update_query = text("""
                        UPDATE local_creatives 
                        SET file_path = :new_path, updated_at = NOW()
                        WHERE id = :creative_id
                    """)
                    db.execute(update_query, {
                        'new_path': archived_path,
                        'creative_id': row.id
                    })
                    fixed_count += 1
                else:
                    logger.warning(f"未找到文件: {row.filename}")
        
        if fixed_count > 0:
            db.commit()
            logger.info(f"✅ 修复了 {fixed_count} 个文件路径")
        else:
            logger.info("✅ 无需修复文件路径")

def reset_failed_uploads():
    """重置失败的上传任务"""
    logger.info("🔄 重置失败的上传任务...")
    
    with database_session() as db:
        # 重置1小时前失败的上传任务
        reset_query = text("""
            UPDATE local_creatives 
            SET status = MaterialStatus.PENDING_GROUPING.value, updated_at = NOW()
            WHERE id IN (
                SELECT lc.id FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND lc.status = MaterialStatus.UPLOAD_FAILED.value
                    AND lc.file_path IS NOT NULL 
                    AND lc.updated_at < NOW() - INTERVAL '1 hour'
            )
        """)
        result = db.execute(reset_query)
        db.commit()
        logger.info(f"✅ 重置了 {result.rowcount} 个失败状态的素材")

def cleanup_duplicate_quarantine():
    """清理重复的隔离文件"""
    logger.info("🗑️ 清理重复的隔离文件...")
    
    quarantine_dir = Path("quarantine/invalid_videos")
    if not quarantine_dir.exists():
        logger.info("隔离目录不存在，跳过清理")
        return
    
    # 统计文件名出现次数
    file_counts = {}
    video_files = list(quarantine_dir.glob("*.mp4"))
    
    for video_file in video_files:
        # 提取基础文件名（去除时间戳）
        base_name = video_file.name
        if '_' in base_name and len(base_name.split('_')) > 1:
            # 如果文件名包含时间戳，提取原始名称
            parts = base_name.split('_')
            if len(parts) >= 2 and parts[-1].replace('.mp4', '').isdigit():
                base_name = '_'.join(parts[:-1]) + '.mp4'
        
        if base_name not in file_counts:
            file_counts[base_name] = []
        file_counts[base_name].append(video_file)
    
    # 清理重复文件（保留最新的）
    cleaned_count = 0
    for base_name, files in file_counts.items():
        if len(files) > 1:
            # 按修改时间排序，保留最新的
            files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # 删除除最新文件外的所有重复文件
            for old_file in files[1:]:
                try:
                    old_file.unlink()
                    # 同时删除对应的reason文件
                    reason_file = old_file.with_suffix(old_file.suffix + ".reason.txt")
                    if reason_file.exists():
                        reason_file.unlink()
                    cleaned_count += 1
                    logger.info(f"删除重复隔离文件: {old_file.name}")
                except Exception as e:
                    logger.error(f"删除文件失败: {e}")
    
    logger.info(f"✅ 清理了 {cleaned_count} 个重复隔离文件")

def verify_system_status():
    """验证系统状态"""
    logger.info("📊 验证系统状态...")
    
    with database_session() as db:
        # 检查当前状态分布
        status_query = text("""
            SELECT 
                lc.status,
                COUNT(*) as count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
            GROUP BY lc.status
            ORDER BY count DESC
        """)
        
        status_results = db.execute(status_query).fetchall()
        
        logger.info("当前素材状态分布:")
        total_count = 0
        failed_count = 0
        
        for row in status_results:
            logger.info(f"  {row.status}: {row.count}")
            total_count += row.count
            if row.status == MaterialStatus.UPLOAD_FAILED.value:
                failed_count = row.count
        
        if total_count > 0:
            failure_rate = (failed_count / total_count) * 100
            logger.info(f"总素材数: {total_count}")
            logger.info(f"失败率: {failure_rate:.1f}%")
            
            if failure_rate < 5:
                logger.info("✅ 失败率已降至5%以下")
            else:
                logger.warning(f"⚠️ 失败率仍然较高: {failure_rate:.1f}%")

def main():
    """主函数"""
    logger.info("🚀 开始千川文件管理快速修复")
    
    try:
        # 1. 快速修复文件路径
        quick_fix_file_paths()
        
        # 2. 重置失败的上传任务
        reset_failed_uploads()
        
        # 3. 清理重复隔离文件
        cleanup_duplicate_quarantine()
        
        # 4. 验证系统状态
        verify_system_status()
        
        logger.info("✅ 快速修复完成!")
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
