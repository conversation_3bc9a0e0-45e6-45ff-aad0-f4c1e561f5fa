#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 智能任务调度器，优化任务执行优先级和资源分配
清理条件: 长期使用，不删除
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class SmartTaskDispatcher:
    """智能任务调度器"""
    
    def __init__(self):
        self.priority_rules = {
            # 最高优先级：用户直接操作
            'upload_single_video': 10,
            'batch_upload_videos': 9,
            'batch_create_plans': 8,
            'appeal_plans': 8,
            
            # 中等优先级：业务流程
            'ingest_and_upload': 6,
            'harvest_materials': 5,
            'collect_materials': 5,
            
            # 低优先级：监控和维护
            'monitor_materials': 3,
            'reset_stale_processing_status': 3,
            'manage_comments': 2,
            'check_violations': 2,
        }
    
    def get_worker_status(self) -> Dict[str, Any]:
        """获取Worker状态"""
        try:
            from qianchuan_aw.celery_app import app
            
            inspect = app.control.inspect()
            
            # 获取活跃任务
            active_tasks = inspect.active()
            stats = inspect.stats()
            
            total_active = 0
            total_capacity = 0
            
            if active_tasks:
                total_active = sum(len(tasks) for tasks in active_tasks.values())
            
            if stats:
                for worker_name, worker_stats in stats.items():
                    pool_info = worker_stats.get('pool', {})
                    capacity = pool_info.get('max-concurrency', 0)
                    total_capacity += capacity
            
            return {
                'total_active': total_active,
                'total_capacity': total_capacity,
                'utilization': (total_active / total_capacity * 100) if total_capacity > 0 else 0,
                'available_slots': max(0, total_capacity - total_active)
            }
            
        except Exception as e:
            print(f"❌ 获取Worker状态失败: {e}")
            return {
                'total_active': 0,
                'total_capacity': 0,
                'utilization': 0,
                'available_slots': 0
            }
    
    def get_pending_tasks_count(self) -> Dict[str, int]:
        """获取待处理任务数量"""
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                pending_upload = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
                ).count()
                
                uploading = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOADING.value
                ).count()
                
                uploaded_pending_plan = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value
                ).count()
                
                return {
                    'pending_upload': pending_upload,
                    'uploading': uploading,
                    'uploaded_pending_plan': uploaded_pending_plan
                }
                
        except Exception as e:
            print(f"❌ 获取待处理任务数量失败: {e}")
            return {
                'pending_upload': 0,
                'uploading': 0,
                'uploaded_pending_plan': 0
            }
    
    def should_trigger_batch_upload(self, worker_status: Dict, pending_tasks: Dict) -> bool:
        """判断是否应该触发批量上传"""
        # 如果有可用Worker槽位且有待上传素材
        if worker_status['available_slots'] > 0 and pending_tasks['pending_upload'] > 0:
            return True
        
        # 如果Worker利用率低于50%且有大量待上传素材
        if worker_status['utilization'] < 50 and pending_tasks['pending_upload'] > 10:
            return True
        
        return False
    
    def should_trigger_plan_creation(self, worker_status: Dict, pending_tasks: Dict) -> bool:
        """判断是否应该触发计划创建"""
        # 如果有可用Worker槽位且有待创建计划的素材
        if worker_status['available_slots'] > 0 and pending_tasks['uploaded_pending_plan'] > 0:
            return True
        
        return False
    
    def optimize_task_execution(self) -> Dict[str, Any]:
        """优化任务执行"""
        print("🚀 开始智能任务调度优化...")
        
        # 获取系统状态
        worker_status = self.get_worker_status()
        pending_tasks = self.get_pending_tasks_count()
        
        print(f"📊 Worker状态:")
        print(f"  - 总容量: {worker_status['total_capacity']}")
        print(f"  - 活跃任务: {worker_status['total_active']}")
        print(f"  - 利用率: {worker_status['utilization']:.1f}%")
        print(f"  - 可用槽位: {worker_status['available_slots']}")
        
        print(f"📋 待处理任务:")
        print(f"  - 待上传: {pending_tasks['pending_upload']}")
        print(f"  - 上传中: {pending_tasks['uploading']}")
        print(f"  - 待创建计划: {pending_tasks['uploaded_pending_plan']}")
        
        recommendations = []
        actions_taken = []
        
        # 检查是否需要触发批量上传
        if self.should_trigger_batch_upload(worker_status, pending_tasks):
            try:
                from qianchuan_aw.workflows.tasks import batch_upload_videos
                
                # 根据可用槽位调整批次大小
                batch_size = min(5, worker_status['available_slots'], pending_tasks['pending_upload'])
                
                print(f"🚀 触发批量上传，批次大小: {batch_size}")
                result = batch_upload_videos.delay(batch_size=batch_size)
                actions_taken.append(f"触发批量上传任务: {result.id}")
                
            except Exception as e:
                print(f"❌ 触发批量上传失败: {e}")
        
        # 检查是否需要触发计划创建
        if self.should_trigger_plan_creation(worker_status, pending_tasks):
            try:
                from qianchuan_aw.workflows.tasks import batch_create_plans
                
                print(f"🚀 触发批量计划创建")
                result = batch_create_plans.delay()
                actions_taken.append(f"触发计划创建任务: {result.id}")
                
            except Exception as e:
                print(f"❌ 触发计划创建失败: {e}")
        
        # 生成优化建议
        if worker_status['utilization'] > 80:
            recommendations.append("Worker利用率过高，建议增加并发数或启动额外Worker")
        
        if pending_tasks['pending_upload'] > 50:
            recommendations.append("大量素材待上传，建议启动高优先级Worker专门处理上传任务")
        
        if worker_status['available_slots'] == 0 and pending_tasks['pending_upload'] > 0:
            recommendations.append("Worker已满负荷但有待处理任务，建议立即扩容")
        
        return {
            'worker_status': worker_status,
            'pending_tasks': pending_tasks,
            'actions_taken': actions_taken,
            'recommendations': recommendations,
            'timestamp': datetime.now().isoformat()
        }

def main():
    """主函数"""
    print("🚀 启动智能任务调度器...")
    
    dispatcher = SmartTaskDispatcher()
    result = dispatcher.optimize_task_execution()
    
    print(f"\n{'='*60}")
    print("📊 智能调度结果")
    print(f"{'='*60}")
    
    if result['actions_taken']:
        print("✅ 执行的操作:")
        for action in result['actions_taken']:
            print(f"  - {action}")
    else:
        print("⚠️ 未执行任何操作")
    
    if result['recommendations']:
        print("\n💡 优化建议:")
        for rec in result['recommendations']:
            print(f"  - {rec}")
    else:
        print("\n✅ 系统运行正常，无需优化")
    
    return 0 if result['actions_taken'] else 1

if __name__ == "__main__":
    exit(main())
