#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具: 千川自动化工作流启动测试脚本
生成时间: 2025-07-20
用途: 测试工作流程序启动和核心功能
生命周期: 永久工具
"""

import sys
import os
import time
import subprocess
import signal
from pathlib import Path
from datetime import datetime
import json

# 添加项目路径
project_root = Path(__file__).parent.parent
src_path = project_root / 'src'
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

class WorkflowStartupTester:
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'celery_worker_test': {},
            'celery_beat_test': {},
            'task_registration_test': {},
            'core_workflow_test': {},
            'recommendations': []
        }
        
    def log_result(self, category, test_name, status, details=None, error=None):
        """记录测试结果"""
        result = {
            'status': status,
            'details': details or '',
            'error': error or '',
            'timestamp': datetime.now().isoformat()
        }
        
        if category not in self.results:
            self.results[category] = {}
        self.results[category][test_name] = result
        
        # 实时输出
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} [{category}] {test_name}: {status}")
        if error:
            print(f"   错误: {error}")
        if details:
            print(f"   详情: {details}")
    
    def test_celery_worker_startup(self):
        """测试Celery Worker启动"""
        print("\n🔧 测试Celery Worker启动...")
        
        try:
            # 启动Celery Worker进程
            cmd = [sys.executable, "run_celery_worker.py"]
            process = subprocess.Popen(
                cmd,
                cwd=str(project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            # 等待启动
            time.sleep(10)
            
            # 检查进程状态
            if process.poll() is None:
                self.log_result('celery_worker_test', 'startup', 'PASS',
                              details='Celery Worker启动成功')
                
                # 尝试获取输出
                try:
                    stdout, stderr = process.communicate(timeout=5)
                    if "ready" in stdout.lower() or "started" in stdout.lower():
                        self.log_result('celery_worker_test', 'ready_status', 'PASS',
                                      details='Worker已就绪')
                    else:
                        self.log_result('celery_worker_test', 'ready_status', 'WARN',
                                      details='Worker状态未确认')
                except subprocess.TimeoutExpired:
                    self.log_result('celery_worker_test', 'ready_status', 'WARN',
                                  details='Worker状态检查超时')
                
                # 终止进程
                if os.name == 'nt':
                    process.send_signal(signal.CTRL_BREAK_EVENT)
                else:
                    process.terminate()
                process.wait(timeout=5)
                
            else:
                stdout, stderr = process.communicate()
                self.log_result('celery_worker_test', 'startup', 'FAIL',
                              error=f'Worker启动失败: {stderr}')
                
        except Exception as e:
            self.log_result('celery_worker_test', 'startup', 'FAIL',
                          error=f'Worker启动测试异常: {str(e)}')
    
    def test_celery_beat_startup(self):
        """测试Celery Beat启动"""
        print("\n⏰ 测试Celery Beat启动...")
        
        try:
            # 启动Celery Beat进程
            cmd = [sys.executable, "run_celery_beat.py"]
            process = subprocess.Popen(
                cmd,
                cwd=str(project_root),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
            )
            
            # 等待启动
            time.sleep(10)
            
            # 检查进程状态
            if process.poll() is None:
                self.log_result('celery_beat_test', 'startup', 'PASS',
                              details='Celery Beat启动成功')
                
                # 终止进程
                if os.name == 'nt':
                    process.send_signal(signal.CTRL_BREAK_EVENT)
                else:
                    process.terminate()
                process.wait(timeout=5)
                
            else:
                stdout, stderr = process.communicate()
                self.log_result('celery_beat_test', 'startup', 'FAIL',
                              error=f'Beat启动失败: {stderr}')
                
        except Exception as e:
            self.log_result('celery_beat_test', 'startup', 'FAIL',
                          error=f'Beat启动测试异常: {str(e)}')
    
    def test_task_registration(self):
        """测试任务注册"""
        print("\n📋 测试任务注册...")
        
        try:
            from qianchuan_aw.celery_app import app as celery_app
            from qianchuan_aw.workflows import tasks  # 确保任务模块被导入
            
            # 获取已注册的任务
            registered_tasks = list(celery_app.tasks.keys())
            
            # 预期的任务列表
            expected_tasks = [
                'tasks.ingest_and_upload',
                'tasks.group_and_dispatch', 
                'tasks.create_plans',
                'tasks.appeal_plans',
                'tasks.monitor_materials',
                'tasks.zombie_patrol',
                'tasks.violation_check',
                'tasks.comment_management',
                'tasks.material_collection'
            ]
            
            # 检查任务注册情况
            registered_expected = []
            missing_tasks = []
            
            for task in expected_tasks:
                if task in registered_tasks:
                    registered_expected.append(task)
                else:
                    missing_tasks.append(task)
            
            if len(registered_expected) >= 5:  # 至少5个核心任务注册
                self.log_result('task_registration_test', 'core_tasks', 'PASS',
                              details=f'已注册{len(registered_expected)}个核心任务')
            else:
                self.log_result('task_registration_test', 'core_tasks', 'WARN',
                              details=f'仅注册{len(registered_expected)}个核心任务')
            
            if missing_tasks:
                self.log_result('task_registration_test', 'missing_tasks', 'WARN',
                              details=f'缺少任务: {missing_tasks[:3]}...')
            else:
                self.log_result('task_registration_test', 'missing_tasks', 'PASS',
                              details='所有预期任务已注册')
                
        except Exception as e:
            self.log_result('task_registration_test', 'registration_check', 'FAIL',
                          error=f'任务注册检查失败: {str(e)}')
    
    def test_core_workflow_functions(self):
        """测试核心工作流功能"""
        print("\n⚙️ 测试核心工作流功能...")
        
        try:
            # 测试配置管理
            from qianchuan_aw.utils.config_manager import get_config_manager
            config_manager = get_config_manager()
            config = config_manager.get_config()
            
            if config and 'workflow' in config:
                self.log_result('core_workflow_test', 'config_loading', 'PASS',
                              details='工作流配置加载成功')
            else:
                self.log_result('core_workflow_test', 'config_loading', 'FAIL',
                              error='工作流配置加载失败')
            
            # 测试数据库会话
            from qianchuan_aw.utils.db_utils import database_session
            with database_session() as db:
                from sqlalchemy import text
                result = db.execute(text("SELECT COUNT(*) FROM local_creatives")).fetchone()
                count = result[0] if result else 0
                self.log_result('core_workflow_test', 'database_access', 'PASS',
                              details=f'数据库访问正常，素材数量: {count}')
                
        except Exception as e:
            self.log_result('core_workflow_test', 'database_access', 'FAIL',
                          error=f'数据库访问失败: {str(e)}')
        
        try:
            # 测试工作流调度器
            from qianchuan_aw.workflows import scheduler
            
            # 检查调度器模块是否可导入
            if hasattr(scheduler, 'handle_file_ingestion'):
                self.log_result('core_workflow_test', 'scheduler_module', 'PASS',
                              details='调度器模块导入成功')
            else:
                self.log_result('core_workflow_test', 'scheduler_module', 'WARN',
                              details='调度器模块功能不完整')
                
        except Exception as e:
            self.log_result('core_workflow_test', 'scheduler_module', 'FAIL',
                          error=f'调度器模块测试失败: {str(e)}')
    
    def generate_startup_recommendations(self):
        """生成启动建议"""
        print("\n💡 生成启动建议...")
        
        # 分析测试结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        
        for category, tests in self.results.items():
            if isinstance(tests, dict):
                for test_name, result in tests.items():
                    if isinstance(result, dict) and 'status' in result:
                        total_tests += 1
                        if result['status'] == 'PASS':
                            passed_tests += 1
                        elif result['status'] == 'FAIL':
                            failed_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate >= 80:
            self.results['recommendations'].append("✅ 系统启动就绪，可以开始运行工作流")
            self.results['recommendations'].append("建议启动顺序: 1) Celery Worker 2) Celery Beat 3) Web UI")
        elif success_rate >= 60:
            self.results['recommendations'].append("⚠️ 系统基本可用，建议解决警告项后启动")
            self.results['recommendations'].append("可以先启动核心功能，逐步完善其他组件")
        else:
            self.results['recommendations'].append("❌ 系统存在严重问题，需要修复后再启动")
            self.results['recommendations'].append("优先解决数据库连接和任务注册问题")
        
        # 具体建议
        self.results['recommendations'].append("监控建议: 启动后观察日志输出，确认任务正常执行")
        self.results['recommendations'].append("性能建议: 根据系统负载调整worker并发数和任务间隔")
        self.results['recommendations'].append("安全建议: 定期检查工作流目录权限和文件处理状态")
    
    def run_startup_test(self):
        """运行启动测试"""
        print("🚀 开始千川自动化工作流启动测试...")
        print(f"项目根目录: {project_root}")
        
        self.test_task_registration()
        self.test_core_workflow_functions()
        self.test_celery_worker_startup()
        self.test_celery_beat_startup()
        self.generate_startup_recommendations()
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 生成启动测试报告...")
        
        # 统计结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        warning_tests = 0
        
        for category, tests in self.results.items():
            if isinstance(tests, dict):
                for test_name, result in tests.items():
                    if isinstance(result, dict) and 'status' in result:
                        total_tests += 1
                        if result['status'] == 'PASS':
                            passed_tests += 1
                        elif result['status'] == 'FAIL':
                            failed_tests += 1
                        elif result['status'] == 'WARN':
                            warning_tests += 1
        
        # 保存详细报告
        report_path = project_root / 'ai_tools' / f'startup_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # 打印总结
        print(f"\n{'='*60}")
        print("🎯 工作流启动测试总结")
        print(f"{'='*60}")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"警告: {warning_tests} ⚠️") 
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if self.results['recommendations']:
            print(f"\n💡 启动建议:")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        print(f"\n📄 详细报告已保存: {report_path}")

if __name__ == "__main__":
    tester = WorkflowStartupTester()
    tester.run_startup_test()
