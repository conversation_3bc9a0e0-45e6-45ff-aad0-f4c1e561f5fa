#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复并发优化后引发的严重问题
清理条件: 问题修复并验证稳定后可归档
"""

import os
import sys
import yaml
import shutil
import time
import threading
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class EmergencyConcurrencyFixer:
    """紧急并发问题修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = self.project_root / 'config' / 'settings.yml'
        self.worker_file = self.project_root / 'run_celery_worker.py'
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"emergency_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 文件锁管理器
        self.file_locks = {}
        self.lock_manager_lock = threading.Lock()
    
    def run_emergency_fix(self):
        """执行紧急修复"""
        logger.critical("🚨 开始紧急修复并发问题")
        logger.critical("=" * 80)
        
        try:
            # 创建备份
            self._create_emergency_backup()
            
            # 按优先级修复问题
            logger.critical("📋 紧急修复优先级:")
            logger.critical("  1. 文件并发访问冲突（最高优先级）")
            logger.critical("  2. API频率限制调整（高优先级）")
            logger.critical("  3. Celery并发数调整（高优先级）")
            logger.critical("  4. 数据库完整性检查（中优先级）")
            logger.critical("  5. 提审模块检查（中优先级）")
            
            # 执行修复
            self._fix_file_concurrency_conflicts()  # 优先级1
            self._adjust_api_rate_limits()  # 优先级2
            self._adjust_celery_concurrency()  # 优先级3
            self._check_database_integrity()  # 优先级4
            self._check_appeal_module()  # 优先级5
            
            # 创建文件锁工具
            self._create_file_lock_utility()
            
            logger.critical("✅ 紧急修复完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 紧急修复失败: {e}")
            return False
    
    def _create_emergency_backup(self):
        """创建紧急备份"""
        logger.info("💾 创建紧急备份...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份关键文件
        files_to_backup = [
            self.config_file,
            self.worker_file,
            self.project_root / 'src' / 'qianchuan_aw' / 'utils' / 'workflow_file_operations.py'
        ]
        
        for file_path in files_to_backup:
            if file_path.exists():
                backup_path = self.backup_dir / file_path.name
                shutil.copy2(file_path, backup_path)
                logger.info(f"✅ 已备份: {file_path.name}")
    
    def _fix_file_concurrency_conflicts(self):
        """修复文件并发访问冲突（优先级1）"""
        logger.critical("🔧 修复文件并发访问冲突...")
        
        try:
            # 创建Windows兼容的文件锁工具类
            file_lock_code = '''
import threading
import time
import os
import platform
from contextlib import contextmanager
from pathlib import Path

# Windows兼容的文件锁实现
if platform.system() == 'Windows':
    import msvcrt

    def lock_file(file_handle):
        """Windows文件锁"""
        try:
            msvcrt.locking(file_handle.fileno(), msvcrt.LK_NBLCK, 1)
            return True
        except IOError:
            return False

    def unlock_file(file_handle):
        """Windows文件解锁"""
        try:
            msvcrt.locking(file_handle.fileno(), msvcrt.LK_UNLCK, 1)
        except IOError:
            pass
else:
    import fcntl

    def lock_file(file_handle):
        """Unix文件锁"""
        try:
            fcntl.flock(file_handle.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            return True
        except IOError:
            return False

    def unlock_file(file_handle):
        """Unix文件解锁"""
        try:
            fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)
        except IOError:
            pass

class FileOperationLock:
    """跨平台文件操作锁管理器"""

    def __init__(self):
        self.file_locks = {}
        self.lock_manager_lock = threading.Lock()

    @contextmanager
    def acquire_file_lock(self, file_path, timeout=30):
        """获取文件锁（跨平台）"""
        file_path = str(Path(file_path).resolve())

        # 使用线程锁确保同一进程内的互斥
        with self.lock_manager_lock:
            if file_path not in self.file_locks:
                self.file_locks[file_path] = threading.Lock()
            thread_lock = self.file_locks[file_path]

        # 获取线程锁
        acquired = thread_lock.acquire(timeout=timeout)
        if not acquired:
            raise TimeoutError(f"无法在{timeout}秒内获取文件锁: {file_path}")

        try:
            # 额外的文件系统级锁（用于跨进程）
            lock_file_path = file_path + '.lock'
            start_time = time.time()

            while time.time() - start_time < timeout:
                try:
                    # 创建锁文件
                    with open(lock_file_path, 'w') as lock_file:
                        lock_file.write(str(os.getpid()))
                    break
                except (IOError, OSError):
                    time.sleep(0.1)
            else:
                raise TimeoutError(f"无法创建文件系统锁: {lock_file_path}")

            try:
                yield file_path
            finally:
                # 清理锁文件
                try:
                    os.remove(lock_file_path)
                except (IOError, OSError):
                    pass

        finally:
            thread_lock.release()

    def is_file_locked(self, file_path):
        """检查文件是否被锁定"""
        file_path = str(Path(file_path).resolve())
        lock_file_path = file_path + '.lock'

        # 检查线程锁
        with self.lock_manager_lock:
            if file_path in self.file_locks and self.file_locks[file_path].locked():
                return True

        # 检查文件系统锁
        return os.path.exists(lock_file_path)

# 全局文件锁管理器
_global_file_lock_manager = FileOperationLock()

def get_file_lock_manager():
    """获取全局文件锁管理器"""
    return _global_file_lock_manager
'''
            
            # 创建文件锁工具
            lock_file = self.project_root / 'src' / 'qianchuan_aw' / 'utils' / 'file_lock_manager.py'
            with open(lock_file, 'w', encoding='utf-8') as f:
                f.write(file_lock_code)
            
            logger.success("✅ 文件锁工具已创建")
            
            # 修改workflow_file_operations.py以使用文件锁
            self._patch_file_operations_with_locks()
            
        except Exception as e:
            logger.error(f"❌ 修复文件并发冲突失败: {e}")
    
    def _patch_file_operations_with_locks(self):
        """为文件操作添加锁机制"""
        try:
            file_ops_file = self.project_root / 'src' / 'qianchuan_aw' / 'utils' / 'workflow_file_operations.py'
            
            with open(file_ops_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 添加文件锁导入
            if 'from qianchuan_aw.utils.file_lock_manager import get_file_lock_manager' not in content:
                import_pos = content.find('from qianchuan_aw.utils.logger import logger')
                if import_pos != -1:
                    import_line = 'from qianchuan_aw.utils.file_lock_manager import get_file_lock_manager\n'
                    content = content[:import_pos] + import_line + content[import_pos:]
            
            # 修改atomic_file_operation函数以使用文件锁
            old_function = '''def atomic_file_operation(operation_id: str, operation_func, *args, **kwargs):'''
            new_function = '''def atomic_file_operation(operation_id: str, operation_func, *args, **kwargs):
    """原子性文件操作（带文件锁）"""
    file_lock_manager = get_file_lock_manager()
    
    # 从参数中提取文件路径
    file_path = None
    if args:
        file_path = str(args[0])  # 假设第一个参数是文件路径
    elif 'file_path' in kwargs:
        file_path = str(kwargs['file_path'])
    
    if file_path:
        try:
            with file_lock_manager.acquire_file_lock(file_path, timeout=60):
                return _original_atomic_file_operation(operation_id, operation_func, *args, **kwargs)
        except TimeoutError as e:
            logger.error(f"文件锁获取超时: {e}")
            raise FileOperationError(f"文件被其他进程占用: {file_path}")
    else:
        return _original_atomic_file_operation(operation_id, operation_func, *args, **kwargs)

def _original_atomic_file_operation(operation_id: str, operation_func, *args, **kwargs):'''
            
            if old_function in content:
                content = content.replace(old_function, new_function)
                
                with open(file_ops_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.success("✅ 文件操作已添加锁机制")
            else:
                logger.warning("⚠️ 未找到预期的文件操作函数，可能需要手动修改")
                
        except Exception as e:
            logger.error(f"❌ 为文件操作添加锁机制失败: {e}")
    
    def _adjust_api_rate_limits(self):
        """调整API频率限制（优先级2）"""
        logger.critical("🔧 调整API频率限制...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 调整API频率限制，为特定接口设置专门限制
            config['rate_limiting'] = {
                'enabled': True,
                'global': {
                    'enabled': True,
                    'rate': 20,  # 从30降低到20，更保守
                    'capacity': 40  # 从50降低到40
                },
                'endpoints': {
                    'create_campaign': {
                        'enabled': True,
                        'rate': 5,  # 从8降低到5
                        'capacity': 15
                    },
                    'upload_video': {
                        'enabled': True,
                        'rate': 8,  # 从12降低到8
                        'capacity': 24
                    },
                    'get_materials_in_ad': {
                        'enabled': True,
                        'rate': 3,  # 从5降低到3
                        'capacity': 9
                    },
                    'security_score_disposal_info': {
                        'enabled': True,
                        'rate': 1,  # 健康度检测限制为1/秒
                        'capacity': 3
                    },
                    'security_score_violation_event': {
                        'enabled': True,
                        'rate': 1,  # 违规检测限制为1/秒
                        'capacity': 3
                    }
                },
                'error_handling': {
                    'backoff_factor': 2.0,  # 增加退避因子
                    'min_rate': 1,
                    'recovery_time': 600  # 增加恢复时间
                }
            }
            
            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success("✅ API频率限制已调整:")
            logger.info("  - 全局速率: 30/秒 → 20/秒")
            logger.info("  - 计划创建: 8/秒 → 5/秒")
            logger.info("  - 视频上传: 12/秒 → 8/秒")
            logger.info("  - 素材查询: 5/秒 → 3/秒")
            logger.info("  - 健康度检测: 新增 1/秒限制")
            logger.info("  - 违规检测: 新增 1/秒限制")
            
        except Exception as e:
            logger.error(f"❌ 调整API频率限制失败: {e}")
    
    def _adjust_celery_concurrency(self):
        """调整Celery并发数（优先级3）"""
        logger.critical("🔧 调整Celery并发数...")
        
        try:
            with open(self.worker_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 将并发数从15降低到8，减少文件冲突
            old_config = "'-c', '15',"
            new_config = "'-c', '8',"
            
            if old_config in content:
                content = content.replace(old_config, new_config)
                
                with open(self.worker_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.success("✅ Celery并发数已调整: 15 → 8")
                logger.info("  - 减少文件并发冲突")
                logger.info("  - 降低系统资源压力")
            else:
                logger.warning("⚠️ 未找到预期的并发配置")
                
        except Exception as e:
            logger.error(f"❌ 调整Celery并发数失败: {e}")
    
    def _check_database_integrity(self):
        """检查数据库完整性（优先级4）"""
        logger.critical("🔧 检查数据库完整性...")
        
        try:
            # 创建数据库完整性检查脚本
            db_check_script = f'''
import sys
sys.path.insert(0, r"{self.project_root / 'src'}")

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign, PlatformCreative
from qianchuan_aw.utils.logger import logger

def check_database_integrity():
    """检查数据库完整性"""
    logger.info("🔍 检查数据库完整性...")
    
    with database_session() as db:
        # 检查孤立的campaign记录
        orphaned_campaigns = db.query(Campaign).filter(
            ~Campaign.platform_creatives.any()
        ).count()
        
        logger.info(f"📊 孤立的计划记录: {{orphaned_campaigns}}个")
        
        # 检查引用不存在素材的计划
        invalid_refs = db.execute("""
            SELECT c.id, c.name, pc.platform_creative_id 
            FROM campaigns c 
            JOIN campaign_platform_creatives cpc ON c.id = cpc.campaign_id 
            JOIN platform_creatives pc ON cpc.platform_creative_id = pc.id 
            WHERE pc.id NOT IN (SELECT id FROM platform_creatives)
        """).fetchall()
        
        logger.info(f"📊 无效素材引用: {{len(invalid_refs)}}个")
        
        if invalid_refs:
            logger.warning("⚠️ 发现无效的素材引用:")
            for ref in invalid_refs[:5]:  # 只显示前5个
                logger.warning(f"  - 计划{{ref[0]}}: {{ref[1]}} → 素材{{ref[2]}}")

if __name__ == "__main__":
    check_database_integrity()
'''
            
            # 写入临时检查脚本
            check_script_file = self.project_root / 'ai_temp' / 'db_integrity_check.py'
            check_script_file.parent.mkdir(exist_ok=True)
            with open(check_script_file, 'w', encoding='utf-8') as f:
                f.write(db_check_script)
            
            logger.success("✅ 数据库完整性检查脚本已创建")
            logger.info(f"📋 运行检查: python {check_script_file}")
            
        except Exception as e:
            logger.error(f"❌ 创建数据库完整性检查失败: {e}")
    
    def _check_appeal_module(self):
        """检查提审模块（优先级5）"""
        logger.critical("🔧 检查提审模块...")
        
        try:
            # 检查提审任务配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            workflow = config.get('workflow', {})
            plan_appeal = workflow.get('plan_appeal', {})
            
            if plan_appeal.get('enabled', False):
                interval = plan_appeal.get('interval_seconds', 180)
                logger.success(f"✅ 提审模块已启用，间隔: {interval}秒")
            else:
                logger.warning("⚠️ 提审模块未启用")
                
                # 启用提审模块
                if 'plan_appeal' not in workflow:
                    workflow['plan_appeal'] = {}
                workflow['plan_appeal']['enabled'] = True
                workflow['plan_appeal']['interval_seconds'] = 300  # 5分钟间隔
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                logger.success("✅ 提审模块已启用")
            
        except Exception as e:
            logger.error(f"❌ 检查提审模块失败: {e}")
    
    def _create_file_lock_utility(self):
        """创建文件锁工具"""
        logger.info("🔧 创建文件锁监控工具...")
        
        try:
            monitor_script = f'''#!/usr/bin/env python3
"""文件锁监控工具"""

import sys
sys.path.insert(0, r"{self.project_root / 'src'}")

from qianchuan_aw.utils.file_lock_manager import get_file_lock_manager
from qianchuan_aw.utils.logger import logger
import time

def monitor_file_locks():
    """监控文件锁状态"""
    lock_manager = get_file_lock_manager()
    
    logger.info("🔍 文件锁监控启动...")
    
    while True:
        try:
            with lock_manager.lock_manager_lock:
                locked_files = [
                    path for path, lock in lock_manager.file_locks.items()
                    if lock.locked()
                ]
            
            if locked_files:
                logger.info(f"🔒 当前锁定文件数: {{len(locked_files)}}")
                for file_path in locked_files[:3]:  # 只显示前3个
                    logger.info(f"  - {{file_path}}")
            else:
                logger.debug("✅ 当前无文件被锁定")
            
            time.sleep(30)  # 每30秒检查一次
            
        except KeyboardInterrupt:
            logger.info("⏹️ 文件锁监控停止")
            break
        except Exception as e:
            logger.error(f"❌ 监控错误: {{e}}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_file_locks()
'''
            
            monitor_file = self.project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250728_monitoring_file_locks.py'
            monitor_file.parent.mkdir(exist_ok=True)
            with open(monitor_file, 'w', encoding='utf-8') as f:
                f.write(monitor_script)
            
            logger.success("✅ 文件锁监控工具已创建")
            
        except Exception as e:
            logger.error(f"❌ 创建文件锁工具失败: {e}")

def main():
    """主函数"""
    fixer = EmergencyConcurrencyFixer()
    success = fixer.run_emergency_fix()
    
    if success:
        logger.critical("🎉 紧急修复完成！")
        logger.critical("📋 下一步操作:")
        logger.critical("  1. 重启Celery服务以应用更改")
        logger.critical("  2. 运行数据库完整性检查")
        logger.critical("  3. 监控文件锁状态")
        logger.critical("  4. 观察文件并发冲突是否解决")
        logger.critical("")
        logger.critical("🔄 重启命令:")
        logger.critical("  python run_celery_worker.py")
        logger.critical("  python run_celery_beat.py")
        logger.critical("")
        logger.critical("📊 监控命令:")
        logger.critical("  python ai_tools/monitoring/ai_tool_20250728_monitoring_file_locks.py")
    else:
        logger.critical("❌ 紧急修复失败，请检查错误日志")

if __name__ == "__main__":
    main()
