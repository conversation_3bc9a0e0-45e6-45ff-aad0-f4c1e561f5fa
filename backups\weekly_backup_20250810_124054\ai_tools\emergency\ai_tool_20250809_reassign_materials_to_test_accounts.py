#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 重新分配错误分配到DELIVERY账户的素材到TEST账户
清理条件: 成为素材分配修复工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class MaterialReassigner:
    """素材重新分配器"""
    
    def __init__(self):
        self.reassignment_results = {}
    
    def run_reassignment(self):
        """运行重新分配"""
        logger.info("🔄 素材重新分配到TEST账户")
        logger.info("="*100)
        
        # 1. 检查当前错误分配的素材
        misassigned_materials = self._check_misassigned_materials()
        
        # 2. 获取可用的TEST账户
        test_accounts = self._get_available_test_accounts()
        
        # 3. 执行重新分配
        reassignment_success = self._reassign_materials(misassigned_materials, test_accounts)
        
        # 4. 验证重新分配结果
        verification_success = self._verify_reassignment()
        
        # 5. 生成报告
        self._generate_reassignment_report({
            'misassigned_materials': misassigned_materials,
            'test_accounts': test_accounts,
            'reassignment_success': reassignment_success,
            'verification_success': verification_success
        })
    
    def _check_misassigned_materials(self):
        """检查错误分配的素材"""
        logger.info("📊 检查错误分配到DELIVERY账户的素材...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, PlatformCreative, AdAccount, Principal
            
            with SessionLocal() as db:
                # 查找最近24小时内错误分配到DELIVERY账户的素材
                misassigned = db.query(PlatformCreative).join(LocalCreative).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value,
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'"),
                    AdAccount.account_type == 'DELIVERY'
                ).all()
                
                logger.info(f"📋 发现 {len(misassigned)} 个错误分配到DELIVERY账户的素材")
                
                if misassigned:
                    # 显示前5个素材的详情
                    logger.info("📋 前5个错误分配的素材:")
                    for i, pc in enumerate(misassigned[:5]):
                        logger.info(f"   {i+1}. 素材ID: {pc.local_creative_id}")
                        logger.info(f"      文件: {os.path.basename(pc.local_creative.file_path)}")
                        logger.info(f"      当前账户: {pc.account.name} ({pc.account.account_type})")
                
                return misassigned
                
        except Exception as e:
            logger.error(f"❌ 检查错误分配素材失败: {e}")
            return []
    
    def _get_available_test_accounts(self):
        """获取可用的TEST账户"""
        logger.info("🔍 获取可用的TEST账户...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import AdAccount, Principal
            
            with SessionLocal() as db:
                # 查找缇萃百货的活跃TEST账户
                test_accounts = db.query(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.account_type == 'TEST',
                    AdAccount.status == 'active'
                ).all()
                
                logger.info(f"📋 找到 {len(test_accounts)} 个可用的TEST账户:")
                for i, account in enumerate(test_accounts):
                    logger.info(f"   {i+1}. {account.name} (ID: {account.id})")
                
                return test_accounts
                
        except Exception as e:
            logger.error(f"❌ 获取TEST账户失败: {e}")
            return []
    
    def _reassign_materials(self, misassigned_materials, test_accounts):
        """重新分配素材"""
        logger.info("🔄 开始重新分配素材...")
        
        if not misassigned_materials:
            logger.info("ℹ️ 没有需要重新分配的素材")
            return True
        
        if not test_accounts:
            logger.error("❌ 没有可用的TEST账户")
            return False
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            
            with SessionLocal() as db:
                reassigned_count = 0
                account_index = 0
                
                for pc in misassigned_materials:
                    try:
                        # 选择TEST账户（轮询分配）
                        target_account = test_accounts[account_index % len(test_accounts)]
                        account_index += 1
                        
                        # 更新PlatformCreative记录的账户
                        pc.account_id = target_account.id
                        
                        # 更新LocalCreative记录的uploaded_to_account_id
                        pc.local_creative.uploaded_to_account_id = target_account.id
                        
                        reassigned_count += 1
                        
                        if reassigned_count <= 10:  # 只显示前10个
                            logger.info(f"  ✅ 素材 {pc.local_creative_id} → {target_account.name}")
                        
                    except Exception as e:
                        logger.error(f"❌ 重新分配素材 {pc.local_creative_id} 失败: {e}")
                
                # 提交所有更改
                db.commit()
                
                logger.success(f"✅ 成功重新分配 {reassigned_count} 个素材到TEST账户")
                
                return reassigned_count > 0
                
        except Exception as e:
            logger.error(f"❌ 重新分配素材失败: {e}")
            return False
    
    def _verify_reassignment(self):
        """验证重新分配结果"""
        logger.info("🔍 验证重新分配结果...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, PlatformCreative, AdAccount, Principal
            
            with SessionLocal() as db:
                # 检查还有多少素材在DELIVERY账户
                remaining_delivery = db.query(PlatformCreative).join(LocalCreative).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value,
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'"),
                    AdAccount.account_type == 'DELIVERY'
                ).count()
                
                # 检查现在有多少素材在TEST账户
                now_in_test = db.query(PlatformCreative).join(LocalCreative).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value,
                    LocalCreative.created_at >= db.func.now() - db.text("INTERVAL '24 hours'"),
                    AdAccount.account_type == 'TEST'
                ).count()
                
                logger.info(f"📊 重新分配后状态:")
                logger.info(f"   仍在DELIVERY账户: {remaining_delivery} 个")
                logger.info(f"   现在在TEST账户: {now_in_test} 个")
                
                if remaining_delivery == 0:
                    logger.success("✅ 所有素材已成功重新分配到TEST账户")
                    return True
                else:
                    logger.warning(f"⚠️ 仍有 {remaining_delivery} 个素材在DELIVERY账户")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证重新分配结果失败: {e}")
            return False
    
    def _generate_reassignment_report(self, results):
        """生成重新分配报告"""
        logger.info("\n📋 素材重新分配报告")
        logger.info("="*100)
        
        misassigned_materials = results['misassigned_materials']
        test_accounts = results['test_accounts']
        reassignment_success = results['reassignment_success']
        verification_success = results['verification_success']
        
        # 错误分配检查
        logger.info(f"📊 错误分配检查: 发现 {len(misassigned_materials)} 个素材")
        
        # TEST账户可用性
        logger.info(f"🏦 TEST账户可用性: {len(test_accounts)} 个可用账户")
        
        # 重新分配结果
        if reassignment_success:
            logger.success("✅ 重新分配: 执行成功")
        else:
            logger.error("❌ 重新分配: 执行失败")
        
        # 验证结果
        if verification_success:
            logger.success("✅ 验证结果: 所有素材已正确分配")
        else:
            logger.warning("⚠️ 验证结果: 部分素材可能仍需处理")
        
        # 总体评估
        success_count = sum([
            len(misassigned_materials) > 0,  # 发现了问题
            len(test_accounts) > 0,          # 有可用账户
            reassignment_success,            # 重新分配成功
            verification_success             # 验证通过
        ])
        
        logger.info(f"\n🎯 重新分配成功率: {success_count}/4")
        
        if success_count >= 3:
            logger.success("🎊 素材重新分配基本成功")
            logger.success("💡 现在TEST账户应该有足够的素材创建计划")
            
            logger.info("\n🚀 下一步行动:")
            logger.info("   1. 重启Celery服务使代码修复生效")
            logger.info("   2. 观察计划创建是否正常工作")
            logger.info("   3. 监控TEST账户的素材处理进度")
            
            return True
        else:
            logger.error("❌ 素材重新分配失败")
            logger.error("🔧 需要手动检查和修复")
            return False


def main():
    """主函数"""
    reassigner = MaterialReassigner()
    
    logger.info("🚀 启动素材重新分配")
    logger.info("🎯 目标：将错误分配到DELIVERY账户的素材重新分配到TEST账户")
    
    success = reassigner.run_reassignment()
    
    if success:
        logger.success("🎊 素材重新分配完成")
        logger.success("💡 建议：重启Celery服务并观察计划创建")
        return 0
    else:
        logger.error("❌ 素材重新分配失败")
        logger.error("🔧 建议：检查错误并手动修复")
        return 1


if __name__ == "__main__":
    exit(main())
