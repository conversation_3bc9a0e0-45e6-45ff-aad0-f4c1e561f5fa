"""
千川申诉参数捕获器 - 基于项目成功经验
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 基于项目现有成功的智投星交互方案，实现参数捕获
依赖关系: 项目现有的浏览器管理和cookies系统
清理条件: 功能被官方API替代时可删除

核心改进：
1. 借鉴项目中成功的智投星点击方案
2. 使用文字版命令提审的成功模式
3. 采用JavaScript点击避免被拦截
4. 使用项目验证过的选择器
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanProvenCapture:
    """千川申诉参数捕获器 - 基于项目成功经验"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的callback请求
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_callback_capture(self, page: Page):
        """设置callback请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                if 'copilot/api/v1/agw/card/callback' in url and request.method == 'POST':
                    logger.success(f"🎯 捕获到目标callback请求: {url}")
                    
                    try:
                        post_data = request.post_data
                        if post_data:
                            data = json.loads(post_data)
                            
                            # 记录完整的请求信息
                            callback_info = {
                                'url': url,
                                'method': request.method,
                                'data': data,
                                'headers': dict(request.headers),
                                'timestamp': time.time()
                            }
                            
                            self.callback_requests.append(callback_info)
                            logger.success(f"✅ 成功捕获callback参数")
                            
                            # 显示关键参数
                            logger.info(f"📋 sessionId: {data.get('sessionId', 'N/A')}")
                            logger.info(f"📋 windowId: {data.get('windowId', 'N/A')[:20]}...")
                            logger.info(f"📋 messageId: {data.get('messageId', 'N/A')}")
                            
                    except Exception as e:
                        logger.warning(f"解析callback数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        page.on("request", handle_request)
    
    def capture_with_proven_method(self, advertiser_id: str, plan_id: str) -> Dict[str, Any]:
        """使用项目验证过的方法捕获参数"""
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 使用项目验证过的方法捕获参数...")
            logger.info(f"📋 广告户ID: {advertiser_id}")
            logger.info(f"📋 计划ID: {plan_id}")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # 设置callback捕获
            self._setup_callback_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url, wait_until="domcontentloaded")
            time.sleep(3)
            
            # 清理引导弹窗（借鉴项目经验）
            try:
                page.get_by_text("我知道了").nth(1).click(timeout=5000)
                logger.info("✅ 已清理引导弹窗")
                time.sleep(1)
            except:
                logger.info("未发现引导弹窗")
            
            # 步骤1: 使用项目验证过的方法点击智投星
            logger.info("🔍 步骤1: 点击智投星图标...")
            copilot_opened = self._click_copilot_proven_method(page)
            
            if not copilot_opened:
                return {"success": False, "error": "无法打开智投星对话框"}
            
            # 等待智投星界面完全加载
            time.sleep(3)
            
            # 步骤2: 发送"自助申诉表单"命令
            logger.info("🔍 步骤2: 发送'自助申诉表单'命令...")
            command_sent = self._send_text_command(page, plan_id)

            if not command_sent:
                return {"success": False, "error": "无法发送文字命令"}

            # 步骤3: 等待智投星回复并点击"计划/商品申诉"
            logger.info("🔍 步骤3: 等待智投星回复并点击'计划/商品申诉'...")
            plan_appeal_clicked = self._wait_and_click_plan_appeal(page)

            if not plan_appeal_clicked:
                logger.warning("⚠️ 未能点击计划/商品申诉，但继续等待callback请求...")

            # 步骤4: 如果成功点击，继续按照真实流程操作表单
            if plan_appeal_clicked:
                logger.info("🔍 步骤4: 按照真实流程操作申诉表单...")
                form_submitted = self._handle_appeal_form_steps(page, plan_id)
                if not form_submitted:
                    logger.warning("⚠️ 申诉表单操作失败，但继续等待callback请求...")

            # 等待callback请求
            logger.info("⏳ 等待callback请求...")
            time.sleep(15)  # 增加等待时间
            
            # 检查捕获结果
            if self.callback_requests:
                logger.success(f"🎉 成功捕获 {len(self.callback_requests)} 个callback请求")
                return self._extract_captured_params()
            else:
                logger.warning("⚠️ 未捕获到callback请求")
                return {"success": False, "error": "未捕获到callback请求"}
            
        except Exception as e:
            logger.error(f"❌ 参数捕获失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 保持浏览器打开供查看
            try:
                logger.info("🔍 捕获完成，浏览器将保持打开状态供您查看")
                logger.info("💡 按 Ctrl+C 关闭浏览器")
                
                while True:
                    time.sleep(10)
                    
            except KeyboardInterrupt:
                logger.info("👋 用户中断，正在关闭浏览器...")
                
            finally:
                try:
                    if context:
                        context.close()
                    if browser:
                        browser.close()
                    if playwright:
                        playwright.stop()
                except:
                    pass
    
    def _click_copilot_proven_method(self, page: Page) -> bool:
        """使用项目验证过的方法点击智投星"""
        try:
            logger.info("🤖 打开智投星对话框...")
            
            # 使用项目中验证过的选择器
            selectors = [
                ".copilot-icon",
                "[data-v-ebe9c19e].copilot-icon",
                ".copilot-entrance", 
                ".ai-assistant-icon",
                "[data-testid='copilot-icon']",
                "button:has-text('智投星')"
            ]
            
            for selector in selectors:
                try:
                    copilot_icon = page.locator(selector).first
                    if copilot_icon.is_visible(timeout=3000):
                        # 使用JavaScript点击避免被拦截（借鉴项目经验）
                        page.evaluate(f"document.querySelector('{selector}').click()")
                        time.sleep(2)
                        logger.info(f"✅ 成功点击智投星图标: {selector}")
                        return True
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
                    continue
            
            # 如果自动点击失败，提供手动指导
            logger.warning("⚠️ 无法自动点击智投星图标")
            print("\n" + "="*50)
            print("🔍 请手动点击智投星图标")
            print("智投星图标通常位于：")
            print("- 页面右下角的圆形图标")
            print("- 工具栏中的'智投星'按钮")
            print("- 带有AI或机器人图标的按钮")
            print("="*50)
            
            input("请手动点击智投星图标，然后按回车继续...")
            return True
            
        except Exception as e:
            logger.error(f"点击智投星图标失败: {e}")
            return False
    
    def _send_text_command(self, page: Page, plan_id: str) -> bool:
        """发送文字版命令（借鉴项目成功经验）"""
        try:
            # 使用正确的命令格式：先发送"自助申诉表单"，然后在表单中输入计划ID
            command_text = "自助申诉表单"
            logger.info(f"📝 发送文字命令: '{command_text}'")
            
            # 查找输入框（使用项目中成功的方法）
            input_selectors = [
                "textbox[name='请描述你的问题']",
                "input[placeholder*='输入']",
                "textarea[placeholder*='输入']",
                ".copilot-input input",
                "[class*='input'][placeholder]"
            ]
            
            for selector in input_selectors:
                try:
                    if selector.startswith("textbox"):
                        # 使用get_by_role方法（项目成功经验）
                        chat_input = page.get_by_role("textbox", name="请描述你的问题")
                    else:
                        chat_input = page.locator(selector).first
                    
                    if chat_input.is_visible(timeout=3000):
                        logger.info(f"✅ 找到输入框: {selector}")
                        
                        # 输入命令
                        chat_input.fill(command_text)
                        time.sleep(1)
                        
                        # 使用Enter键发送（项目成功经验）
                        chat_input.press("Enter")
                        logger.info("✅ 文字命令已发送")
                        
                        return True
                        
                except Exception as e:
                    logger.debug(f"输入框选择器 {selector} 失败: {e}")
                    continue
            
            # 如果找不到输入框，提供手动指导
            logger.warning("⚠️ 无法找到输入框")
            print("\n" + "="*50)
            print("🔍 请手动输入申诉命令")
            print(f"在智投星对话框中输入: {command_text}")
            print("然后按回车键发送")
            print("="*50)
            
            input("请手动输入命令并发送，然后按回车继续...")
            return True
            
        except Exception as e:
            logger.error(f"发送文字命令失败: {e}")
            return False

    def _wait_and_click_plan_appeal(self, page: Page) -> bool:
        """等待智投星回复并点击'计划/商品申诉'"""
        try:
            logger.info("⏳ 等待智投星回复...")

            # 等待智投星回复（通常需要几秒钟）
            time.sleep(8)

            # 寻找"计划/商品申诉"按钮（智投星回复后直接出现的选项）
            plan_appeal_selectors = [
                "button:has-text('计划/商品申诉')",
                "div:has-text('计划/商品申诉')",
                "[class*='option']:has-text('计划/商品申诉')",
                "li:has-text('计划/商品申诉')",
                "[role='button']:has-text('计划/商品申诉')",
                "a:has-text('计划/商品申诉')"
            ]

            for selector in plan_appeal_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible(timeout=5000):
                            logger.info(f"✅ 找到'计划/商品申诉'选项: {selector}")
                            element.click(timeout=5000)
                            logger.info("✅ 成功点击'计划/商品申诉'")
                            return True
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
                    continue

            # 如果自动点击失败，提供手动指导
            logger.warning("⚠️ 未能自动点击'计划/商品申诉'")
            print("\n" + "="*50)
            print("🔍 请手动点击'计划/商品申诉'")
            print("在智投星的回复中应该有两个选项：")
            print("- 计划/商品申诉")
            print("- 账户质量申诉")
            print("请点击'计划/商品申诉'")
            print("="*50)

            input("请手动点击'计划/商品申诉'，然后按回车继续...")
            return True

        except Exception as e:
            logger.error(f"等待并点击计划/商品申诉失败: {e}")
            return False

    def _handle_appeal_form_steps(self, page: Page, plan_id: str) -> bool:
        """处理申诉表单步骤（点击计划/商品申诉后的流程）"""
        try:
            logger.info("🎯 开始处理申诉表单步骤...")

            # 等待表单加载
            time.sleep(3)

            # 步骤1: 选择问题类型"计划审核不通过/结果申诉"
            logger.info("🔍 步骤1: 选择问题类型...")
            problem_type_selected = self._select_problem_type(page)
            if not problem_type_selected:
                logger.warning("⚠️ 未能选择问题类型，尝试手动指导...")
                print("\n" + "="*50)
                print("🔍 请手动选择问题类型")
                print("在问题类型下拉框中选择：'计划审核不通过/结果申诉'")
                print("="*50)
                input("请手动选择问题类型，然后按回车继续...")

            time.sleep(2)

            # 步骤2: 输入计划ID
            logger.info("🔍 步骤2: 输入计划ID...")
            plan_id_entered = self._enter_plan_id_in_form(page, plan_id)
            if not plan_id_entered:
                logger.warning("⚠️ 未能输入计划ID，尝试手动指导...")
                print("\n" + "="*50)
                print("🔍 请手动输入计划ID")
                print(f"在计划ID输入框中输入：{plan_id}")
                print("="*50)
                input("请手动输入计划ID，然后按回车继续...")

            time.sleep(2)

            # 步骤3: 点击提交按钮
            logger.info("🔍 步骤3: 点击提交按钮...")
            submit_clicked = self._click_submit_button(page)
            if not submit_clicked:
                logger.warning("⚠️ 未能点击提交按钮，尝试手动指导...")
                print("\n" + "="*50)
                print("🔍 请手动点击提交按钮")
                print("找到并点击'提交'按钮")
                print("="*50)
                input("请手动点击提交按钮，然后按回车继续...")

            logger.info("✅ 申诉表单步骤处理完成")
            return True

        except Exception as e:
            logger.error(f"处理申诉表单步骤失败: {e}")
            return False



    def _select_problem_type(self, page: Page) -> bool:
        """选择问题类型：计划审核不通过/结果申诉"""
        try:
            # 先点击下拉框
            dropdown_selectors = [
                "select",
                ".select",
                "[class*='dropdown']",
                "[class*='select']",
                "div:has-text('请选择')"
            ]

            for selector in dropdown_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到下拉框: {selector}")
                            element.click(timeout=3000)
                            time.sleep(1)
                            break
                except:
                    continue

            # 选择"计划审核不通过/结果申诉"选项
            option_selectors = [
                "option:has-text('计划审核不通过/结果申诉')",
                "li:has-text('计划审核不通过/结果申诉')",
                "div:has-text('计划审核不通过/结果申诉')",
                ":has-text('计划审核不通过') >> :has-text('结果申诉')"
            ]

            for selector in option_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到问题类型选项: {selector}")
                            element.click(timeout=3000)
                            return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"选择问题类型失败: {e}")
            return False

    def _enter_plan_id_in_form(self, page: Page, plan_id: str) -> bool:
        """在表单中输入计划ID"""
        try:
            input_selectors = [
                "input[placeholder*='计划ID']",
                "input[placeholder*='ID']",
                "textarea[placeholder*='计划ID']",
                "input[name*='planId']",
                "input[name*='plan_id']"
            ]

            for selector in input_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到计划ID输入框: {selector}")
                            element.fill(plan_id)
                            logger.info(f"✅ 输入计划ID: {plan_id}")
                            return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"输入计划ID失败: {e}")
            return False

    def _click_submit_button(self, page: Page) -> bool:
        """点击提交按钮"""
        try:
            submit_selectors = [
                "button:has-text('提交')",
                "button[type='submit']",
                ".submit-button",
                "[class*='submit']",
                "button:has-text('确认')",
                "button:has-text('发送')"
            ]

            for selector in submit_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible() and element.is_enabled():
                            logger.info(f"✅ 找到提交按钮: {selector}")
                            element.click(timeout=5000)
                            logger.info("✅ 提交按钮点击成功")
                            return True
                except:
                    continue

            return False

        except Exception as e:
            logger.error(f"点击提交按钮失败: {e}")
            return False
    
    def _extract_captured_params(self) -> Dict[str, Any]:
        """提取捕获的参数"""
        try:
            if not self.callback_requests:
                return {"success": False, "error": "没有捕获到callback请求"}
            
            # 使用最新的请求
            latest_request = self.callback_requests[-1]
            data = latest_request['data']
            
            # 提取关键参数
            params = {
                "success": True,
                "sessionId": data.get('sessionId'),
                "windowId": data.get('windowId'),
                "messageId": data.get('messageId'),
                "callValue": data.get('callValue'),
                "applicationCode": data.get('applicationCode', 'QC'),
                "callBackCode": data.get('callBackCode'),
                "url": latest_request['url'],
                "headers": latest_request['headers'],
                "captured_at": latest_request['timestamp'],
                "total_requests": len(self.callback_requests),
                "raw_data": data
            }
            
            # 保存参数到文件
            self._save_captured_params(params)
            
            logger.success(f"✅ 成功提取callback参数")
            return params
            
        except Exception as e:
            logger.error(f"提取参数失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_captured_params(self, params: Dict[str, Any]):
        """保存捕获的参数到文件"""
        try:
            timestamp = int(time.time())
            filename = f"ai_temp/proven_callback_params_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(params, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"💾 参数已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存参数失败: {e}")


def capture_with_proven_method(advertiser_id: str, plan_id: str, principal_name: str = "缇萃百货") -> Dict[str, Any]:
    """
    便捷函数：使用项目验证过的方法捕获参数
    
    Args:
        advertiser_id: 广告户ID
        plan_id: 计划ID
        principal_name: 主体名称
        
    Returns:
        捕获结果字典
    """
    capture = QianchuanProvenCapture(principal_name)
    return capture.capture_with_proven_method(advertiser_id, plan_id)


if __name__ == "__main__":
    # 使用项目验证过的方法进行参数捕获
    test_advertiser_id = "1836333804939273"
    test_plan_id = "1838840072680523"
    
    print("🎯 千川申诉参数捕获器 - 基于项目成功经验")
    print("=" * 60)
    print("基于项目中已验证成功的方案：")
    print("1. 使用JavaScript点击智投星图标（避免被拦截）")
    print("2. 使用文字版命令提审方法")
    print("3. 采用项目验证过的选择器")
    print("4. 智能降级到手动操作指导")
    print()
    
    try:
        result = capture_with_proven_method(test_advertiser_id, test_plan_id)
        
        if result.get('success'):
            print("🎉 参数捕获成功！")
            print("现在您可以使用这些真实参数进行API调用")
        else:
            print(f"❌ 参数捕获失败: {result.get('error')}")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_proven_capture import capture_with_proven_method")
    print("result = capture_with_proven_method('广告户ID', '计划ID')")
