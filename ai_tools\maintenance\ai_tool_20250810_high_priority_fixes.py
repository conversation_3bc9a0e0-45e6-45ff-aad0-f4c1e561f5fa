#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实施千川自动化系统High级别问题的修复
清理条件: 修复完成后可归档，但建议保留作为参考
"""

import os
import sys
import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


class HighPriorityFixesImplementation:
    """High级别问题修复实施器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.fixes_failed = []
        
    def implement_all_high_priority_fixes(self) -> Dict[str, Any]:
        """实施所有High级别修复"""
        logger.info("🔥 开始实施High级别问题修复...")
        
        results = {
            'total_fixes': 3,
            'successful_fixes': 0,
            'failed_fixes': 0,
            'fixes_applied': [],
            'fixes_failed': []
        }
        
        # H1: 完善计划提审状态检查逻辑
        try:
            self._fix_plan_submission_logic()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('H1: 计划提审状态检查逻辑完善')
        except Exception as e:
            logger.error(f"H1修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'H1: {str(e)}')
        
        # H2: 统一账户状态权限检查
        try:
            self._fix_account_status_permissions()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('H2: 账户状态权限检查统一')
        except Exception as e:
            logger.error(f"H2修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'H2: {str(e)}')
        
        # H3: 实现分布式锁机制
        try:
            self._implement_distributed_locks()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('H3: 分布式锁机制实现')
        except Exception as e:
            logger.error(f"H3修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'H3: {str(e)}')
        
        logger.info(f"✅ High级别修复完成: {results['successful_fixes']}/{results['total_fixes']}")
        return results
    
    def _fix_plan_submission_logic(self):
        """完善计划提审状态检查逻辑"""
        logger.info("🔧 完善计划提审状态检查逻辑...")
        
        # 创建增强的提审状态检查器
        submission_checker_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计划提审状态检查器 - 增强版本
确保严格执行防重复提审铁律
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import Campaign, AdAccount


class PlanSubmissionChecker:
    """计划提审状态检查器 - 铁律4实施"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def validate_plan_submission_eligibility(self, campaign_id_qc: str) -> Dict[str, Any]:
        """验证计划提审资格 - 防重复提审铁律"""
        
        try:
            # 获取计划信息
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                return {
                    'is_eligible': False,
                    'reason': 'campaign_not_found',
                    'message': f'计划不存在: {campaign_id_qc}'
                }
            
            # 检查1: 计划状态必须是AUDITING
            if campaign.status != 'AUDITING':
                return {
                    'is_eligible': False,
                    'reason': 'invalid_campaign_status',
                    'message': f'计划状态不符合提审要求: {campaign.status}，期望: AUDITING',
                    'violation_rule': '铁律4：只有AUDITING状态的计划才能提审'
                }
            
            # 检查2: 提审状态检查
            if campaign.appeal_status == 'appeal_pending':
                return {
                    'is_eligible': False,
                    'reason': 'already_pending_appeal',
                    'message': '计划已在提审队列中',
                    'violation_rule': '铁律4：防重复提审约束'
                }
            
            # 检查3: 提审历史检查
            if campaign.first_appeal_at is not None:
                return {
                    'is_eligible': False,
                    'reason': 'already_appealed',
                    'message': f'计划已提审过，时间: {campaign.first_appeal_at}',
                    'violation_rule': '铁律4：每个计划只能提审一次'
                }
            
            # 检查4: 尝试次数限制
            appeal_attempt_count = getattr(campaign, 'appeal_attempt_count', 0)
            if appeal_attempt_count >= 3:
                return {
                    'is_eligible': False,
                    'reason': 'max_attempts_exceeded',
                    'message': f'提审尝试次数已达上限: {appeal_attempt_count}/3',
                    'violation_rule': '铁律4：提审尝试次数限制'
                }
            
            # 检查5: 账户状态检查
            account = campaign.account
            if account.status == 'deleted':
                return {
                    'is_eligible': False,
                    'reason': 'deleted_account',
                    'message': '账户已删除，禁止提审操作',
                    'violation_rule': '铁律3：deleted账户严禁任何操作'
                }
            
            # 检查6: 时间间隔检查（防止过于频繁的提审）
            if campaign.last_appeal_at:
                time_since_last_appeal = datetime.now(timezone.utc) - campaign.last_appeal_at
                if time_since_last_appeal < timedelta(hours=1):
                    return {
                        'is_eligible': False,
                        'reason': 'too_frequent_appeal',
                        'message': f'距离上次提审时间过短: {time_since_last_appeal}',
                        'violation_rule': '提审频率限制'
                    }
            
            # 所有检查通过
            return {
                'is_eligible': True,
                'reason': 'all_checks_passed',
                'message': '计划符合提审条件'
            }
            
        except Exception as e:
            logger.error(f"提审资格检查失败: {e}")
            return {
                'is_eligible': False,
                'reason': 'check_failed',
                'message': f'检查过程发生错误: {str(e)}'
            }
    
    def mark_plan_as_submitted(self, campaign_id_qc: str) -> Dict[str, Any]:
        """标记计划为已提审状态"""
        
        try:
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                return {'success': False, 'error': 'campaign_not_found'}
            
            # 更新提审状态
            campaign.appeal_status = 'appeal_pending'
            campaign.first_appeal_at = datetime.now(timezone.utc)
            campaign.last_appeal_at = datetime.now(timezone.utc)
            
            # 增加尝试次数
            if hasattr(campaign, 'appeal_attempt_count'):
                campaign.appeal_attempt_count = (campaign.appeal_attempt_count or 0) + 1
            
            self.db.commit()
            
            logger.info(f"✅ 计划 {campaign_id_qc} 已标记为提审状态")
            
            return {
                'success': True,
                'message': '计划提审状态更新成功'
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记提审状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_submission_statistics(self) -> Dict[str, Any]:
        """获取提审统计信息"""
        
        try:
            stats = self.db.execute(text("""
                SELECT 
                    COUNT(*) as total_campaigns,
                    COUNT(CASE WHEN appeal_status = 'appeal_pending' THEN 1 END) as pending_appeals,
                    COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as already_appealed,
                    COUNT(CASE WHEN status = 'AUDITING' AND appeal_status IS NULL THEN 1 END) as eligible_for_appeal
                FROM campaigns
                WHERE status IN ('AUDITING', 'AUDIT_ACCEPTED', 'AUDIT_REJECT')
            """)).fetchone()
            
            return {
                'total_campaigns': stats.total_campaigns,
                'pending_appeals': stats.pending_appeals,
                'already_appealed': stats.already_appealed,
                'eligible_for_appeal': stats.eligible_for_appeal,
                'compliance_rate': (stats.already_appealed / stats.total_campaigns * 100) if stats.total_campaigns > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取提审统计失败: {e}")
            return {'error': str(e)}
'''
        
        # 保存提审状态检查器
        submission_checker_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'plan_submission_checker.py'
        with open(submission_checker_file, 'w', encoding='utf-8') as f:
            f.write(submission_checker_code)
        
        logger.success("✅ 计划提审状态检查器创建完成")
    
    def _fix_account_status_permissions(self):
        """统一账户状态权限检查"""
        logger.info("🔧 统一账户状态权限检查...")
        
        # 创建统一的账户权限管理器
        account_permission_manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户权限管理器 - 统一版本
确保所有工作流使用一致的账户状态权限检查
"""

from typing import Dict, Any, List
from enum import Enum
from qianchuan_aw.database.models import AdAccount
from qianchuan_aw.utils.logger import logger


class AccountOperation(Enum):
    """账户操作类型枚举"""
    UPLOAD = "upload"
    CREATE_PLAN = "create_plan"
    SUBMIT_PLAN = "submit_plan"
    HARVEST = "harvest"
    MONITOR = "monitor"
    SYNC = "sync"


class AccountPermissionManager:
    """账户权限管理器 - 铁律3统一实施"""
    
    def __init__(self):
        # 定义账户状态与操作权限的映射关系
        self.permission_matrix = {
            'active': {
                AccountOperation.UPLOAD: True,
                AccountOperation.CREATE_PLAN: True,
                AccountOperation.SUBMIT_PLAN: True,
                AccountOperation.HARVEST: True,
                AccountOperation.MONITOR: True,
                AccountOperation.SYNC: True,
            },
            'temporarily_blocked': {
                AccountOperation.UPLOAD: False,        # 禁止新建操作
                AccountOperation.CREATE_PLAN: False,   # 禁止新建操作
                AccountOperation.SUBMIT_PLAN: True,    # 允许已有操作
                AccountOperation.HARVEST: True,        # 允许已有操作
                AccountOperation.MONITOR: True,        # 允许监控
                AccountOperation.SYNC: True,           # 允许同步
            },
            'deleted': {
                AccountOperation.UPLOAD: False,        # 严禁任何操作
                AccountOperation.CREATE_PLAN: False,   # 严禁任何操作
                AccountOperation.SUBMIT_PLAN: False,   # 严禁任何操作
                AccountOperation.HARVEST: False,       # 严禁任何操作
                AccountOperation.MONITOR: False,       # 严禁任何操作
                AccountOperation.SYNC: False,          # 严禁任何操作
            }
        }
    
    def check_operation_permission(self, account: AdAccount, operation: AccountOperation) -> Dict[str, Any]:
        """检查账户操作权限"""
        
        account_status = account.status.lower()
        
        # 检查账户状态是否在权限矩阵中
        if account_status not in self.permission_matrix:
            return {
                'is_allowed': False,
                'reason': 'unknown_account_status',
                'message': f'未知的账户状态: {account_status}',
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
        
        # 获取权限
        is_allowed = self.permission_matrix[account_status].get(operation, False)
        
        if is_allowed:
            return {
                'is_allowed': True,
                'reason': 'permission_granted',
                'message': f'{account_status}账户允许{operation.value}操作',
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
        else:
            violation_rule = self._get_violation_rule(account_status, operation)
            return {
                'is_allowed': False,
                'reason': 'permission_denied',
                'message': f'{account_status}账户禁止{operation.value}操作',
                'violation_rule': violation_rule,
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
    
    def _get_violation_rule(self, account_status: str, operation: AccountOperation) -> str:
        """获取违规的业务铁律"""
        
        if account_status == 'deleted':
            return '铁律3：deleted账户严禁任何操作'
        elif account_status == 'temporarily_blocked':
            if operation in [AccountOperation.UPLOAD, AccountOperation.CREATE_PLAN]:
                return '铁律3：temporarily_blocked账户禁止新建操作'
        
        return '铁律3：账户状态操作权限约束'
    
    def get_allowed_operations(self, account: AdAccount) -> List[AccountOperation]:
        """获取账户允许的操作列表"""
        
        account_status = account.status.lower()
        
        if account_status not in self.permission_matrix:
            return []
        
        allowed_operations = []
        for operation, is_allowed in self.permission_matrix[account_status].items():
            if is_allowed:
                allowed_operations.append(operation)
        
        return allowed_operations
    
    def validate_batch_operations(self, accounts: List[AdAccount], operation: AccountOperation) -> Dict[str, Any]:
        """批量验证账户操作权限"""
        
        results = {
            'total_accounts': len(accounts),
            'allowed_accounts': [],
            'denied_accounts': [],
            'unknown_status_accounts': []
        }
        
        for account in accounts:
            permission_result = self.check_operation_permission(account, operation)
            
            if permission_result['is_allowed']:
                results['allowed_accounts'].append({
                    'account_id': account.account_id_qc,
                    'account_name': account.name,
                    'status': account.status
                })
            else:
                denied_info = {
                    'account_id': account.account_id_qc,
                    'account_name': account.name,
                    'status': account.status,
                    'reason': permission_result['reason'],
                    'violation_rule': permission_result.get('violation_rule')
                }
                
                if permission_result['reason'] == 'unknown_account_status':
                    results['unknown_status_accounts'].append(denied_info)
                else:
                    results['denied_accounts'].append(denied_info)
        
        results['allowed_count'] = len(results['allowed_accounts'])
        results['denied_count'] = len(results['denied_accounts'])
        results['unknown_count'] = len(results['unknown_status_accounts'])
        
        return results
'''
        
        # 保存账户权限管理器
        permission_manager_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'account_permission_manager.py'
        with open(permission_manager_file, 'w', encoding='utf-8') as f:
            f.write(account_permission_manager_code)
        
        logger.success("✅ 账户权限管理器创建完成")
    
    def _implement_distributed_locks(self):
        """实现分布式锁机制"""
        logger.info("🔧 实现分布式锁机制...")
        
        # 创建分布式锁管理器
        distributed_lock_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式锁管理器
提供Redis基础的分布式锁机制，确保并发操作安全
"""

import redis
import time
import uuid
from typing import Optional, Dict, Any
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger


class DistributedLock:
    """分布式锁实现"""
    
    def __init__(self, redis_client: redis.Redis, key: str, timeout: int = 60, retry_delay: float = 0.1):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.retry_delay = retry_delay
        self.identifier = str(uuid.uuid4())
        self.acquired = False
    
    def acquire(self, blocking: bool = True, timeout: Optional[int] = None) -> bool:
        """获取锁"""
        
        end_time = time.time() + (timeout or self.timeout)
        
        while True:
            # 尝试获取锁
            if self.redis.set(self.key, self.identifier, nx=True, ex=self.timeout):
                self.acquired = True
                logger.debug(f"🔒 获取分布式锁成功: {self.key}")
                return True
            
            if not blocking or time.time() > end_time:
                logger.warning(f"⏰ 获取分布式锁超时: {self.key}")
                return False
            
            time.sleep(self.retry_delay)
    
    def release(self) -> bool:
        """释放锁"""
        
        if not self.acquired:
            return False
        
        # 使用Lua脚本确保原子性释放
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        try:
            result = self.redis.eval(lua_script, 1, self.key, self.identifier)
            if result:
                self.acquired = False
                logger.debug(f"🔓 释放分布式锁成功: {self.key}")
                return True
            else:
                logger.warning(f"⚠️ 释放分布式锁失败，锁可能已过期: {self.key}")
                return False
        except Exception as e:
            logger.error(f"❌ 释放分布式锁异常: {self.key}, 错误: {e}")
            return False
    
    @contextmanager
    def acquire_context(self, blocking: bool = True, timeout: Optional[int] = None):
        """上下文管理器方式使用锁"""
        
        acquired = self.acquire(blocking=blocking, timeout=timeout)
        try:
            yield acquired
        finally:
            if acquired:
                self.release()


class DistributedLockManager:
    """分布式锁管理器"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.active_locks = {}
    
    def create_lock(self, key: str, timeout: int = 60) -> DistributedLock:
        """创建分布式锁"""
        return DistributedLock(self.redis, key, timeout)
    
    @contextmanager
    def lock_context(self, key: str, timeout: int = 60, blocking: bool = True):
        """便捷的锁上下文管理器"""
        
        lock = self.create_lock(key, timeout)
        
        with lock.acquire_context(blocking=blocking, timeout=timeout) as acquired:
            if not acquired:
                raise RuntimeError(f"无法获取分布式锁: {key}")
            yield lock
    
    def get_lock_info(self, key: str) -> Dict[str, Any]:
        """获取锁信息"""
        
        lock_key = f"lock:{key}"
        
        try:
            value = self.redis.get(lock_key)
            ttl = self.redis.ttl(lock_key)
            
            if value is None:
                return {
                    'exists': False,
                    'key': key
                }
            
            return {
                'exists': True,
                'key': key,
                'identifier': value.decode('utf-8'),
                'ttl': ttl,
                'expires_at': time.time() + ttl if ttl > 0 else None
            }
            
        except Exception as e:
            logger.error(f"获取锁信息失败: {key}, 错误: {e}")
            return {
                'exists': False,
                'key': key,
                'error': str(e)
            }
    
    def cleanup_expired_locks(self) -> int:
        """清理过期锁"""
        
        try:
            # 获取所有锁键
            lock_keys = self.redis.keys("lock:*")
            cleaned_count = 0
            
            for key in lock_keys:
                ttl = self.redis.ttl(key)
                if ttl == -1:  # 没有过期时间的锁
                    self.redis.delete(key)
                    cleaned_count += 1
                    logger.info(f"🧹 清理无过期时间的锁: {key.decode('utf-8')}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期锁失败: {e}")
            return 0
'''
        
        # 保存分布式锁管理器
        distributed_lock_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'distributed_lock_manager.py'
        with open(distributed_lock_file, 'w', encoding='utf-8') as f:
            f.write(distributed_lock_code)
        
        logger.success("✅ 分布式锁管理器创建完成")


def main():
    """主函数"""
    fixer = HighPriorityFixesImplementation()
    
    try:
        results = fixer.implement_all_high_priority_fixes()
        
        print("\n" + "="*60)
        print("🔥 High级别问题修复结果")
        print("="*60)
        print(f"总修复项目: {results['total_fixes']}")
        print(f"成功修复: {results['successful_fixes']}")
        print(f"修复失败: {results['failed_fixes']}")
        
        if results['fixes_applied']:
            print("\n✅ 成功修复的问题:")
            for fix in results['fixes_applied']:
                print(f"  - {fix}")
        
        if results['fixes_failed']:
            print("\n❌ 修复失败的问题:")
            for fix in results['fixes_failed']:
                print(f"  - {fix}")
        
        print("\n📋 后续步骤:")
        print("1. 更新现有工作流代码以使用新的检查器")
        print("2. 配置Redis连接以支持分布式锁")
        print("3. 运行集成测试验证修复效果")
        print("4. 继续修复Medium级别问题")
        
    except Exception as e:
        logger.error(f"修复过程发生错误: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
