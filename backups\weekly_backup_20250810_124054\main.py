# -*- coding: utf-8 -*-
"""
[V6.0] 千川自动化工作流 - 主调度器入口
"""
import sys
import os
import asyncio

# [V43.0 终极修复] 正确设置 sys.path 以支持 src-layout
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)  # 为了找到 config
sys.path.insert(0, src_path)      # 为了找到 qianchuan_aw 包

import time
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.workflows import tasks
from qianchuan_aw.utils.config_loader import load_settings

def main():
    """
    [V51.0] 精细化 Celery 任务调度器主入口
    
    为每个原子任务独立计时和调度，实现真正的模块化运行。
    """
    logger.info("--- [精细化 Celery 调度器] 启动成功 ---")
    
    app_settings = load_settings()
    workflow_settings = app_settings.get('workflow', {})
    lighthouse_settings = app_settings.get('lighthouse_plan', {})

    # 定义任务及其调度配置
    schedule = {
        'ingest_and_upload': {
            'task': tasks.task_ingest_and_upload,
            'interval': workflow_settings.get('ingest_and_upload_interval_seconds', 60),
            'last_run': 0
        },
        'create_plans': {
            'task': tasks.task_create_plans,
            'interval': workflow_settings.get('create_plans_interval_seconds', 120),
            'last_run': 0
        },
        'appeal_plans': {
            'task': tasks.task_appeal_plans,
            'interval': workflow_settings.get('appeal_plans_interval_seconds', 180),
            'last_run': 0
        },
        'monitor_materials': {
            'task': tasks.task_monitor_materials,
            'interval': workflow_settings.get('monitor_materials_interval_seconds', 300),
            'last_run': 0
        },
        'manage_comments': {
            'task': tasks.task_manage_comments,
            'interval': workflow_settings.get('manage_comments_interval_seconds', 600),
            'last_run': 0
        },
        'fast_monitor': {
            'task': tasks.task_run_fast_monitor,
            'interval': lighthouse_settings.get('fast_monitor_interval_seconds', 300),
            'last_run': 0
        },
        'detect_events': {
            'task': tasks.task_detect_events,
            # 事件检测紧随高频监控，因此使用相同的间隔
            'interval': lighthouse_settings.get('fast_monitor_interval_seconds', 300),
            'last_run': 0
        }
    }
    
    for name, config in schedule.items():
        logger.info(f"任务 '{name}' 已配置，调度间隔: {config['interval']} 秒")

    while True:
        current_time = time.time()
        
        for name, config in schedule.items():
            if current_time - config['last_run'] > config['interval']:
                logger.info(f"调度: 任务 '{name}' 已发布到队列...")
                config['task'].delay()
                config['last_run'] = current_time

        # 休眠一小段时间，避免CPU空转
        time.sleep(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("--- [Celery 调度器] 检测到手动中断，程序退出。 ---")
