
def check_duplicate_before_plan_creation(file_hash: str) -> bool:
    """
    在创建计划前检查是否已存在相同文件哈希的计划
    使用MCP进行数据库查询
    """
    try:
        # 使用MCP查询
        query = """
        SELECT COUNT(*) as count
        FROM campaigns c
        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
        JOIN local_creatives lc ON pc.local_creative_id = lc.id
        WHERE lc.file_hash = %s
        """
        
        # 这里应该调用MCP执行查询
        # result = execute_sql_my-mcp-server-qianchuan(query, [file_hash])
        
        # 如果已存在，返回True（表示重复）
        # return result[0]['count'] > 0
        
        return False  # 临时返回
        
    except Exception as e:
        logger.error(f"检查重复计划失败: {e}")
        return False

def enhanced_file_movement_with_dedup(platform_creative, principal, app_settings):
    """
    增强的文件移动函数，包含去重检查
    """
    try:
        local_creative = platform_creative.local_creative
        if not local_creative or not local_creative.file_path:
            return False, None

        # 检查目标文件是否已存在
        date_str = datetime.now().strftime('%Y-%m-%d')
        filename = os.path.basename(local_creative.file_path)
        
        base_workflow_dir = app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        approved_dir = os.path.join(base_workflow_dir, '03_materials_approved', principal.name, date_str)
        target_path = os.path.join(approved_dir, filename)
        
        # 如果目标文件已存在，比较哈希
        if os.path.exists(target_path):
            from qianchuan_aw.utils.hash_utils import calculate_file_hash
            source_hash = calculate_file_hash(local_creative.file_path)
            target_hash = calculate_file_hash(target_path)
            
            if source_hash == target_hash:
                logger.info(f"文件已存在且内容相同，跳过移动: {filename}")
                return True, target_path
        
        # 执行移动
        os.makedirs(approved_dir, exist_ok=True)
        if os.path.exists(local_creative.file_path):
            shutil.move(local_creative.file_path, target_path)
            logger.success(f"文件移动成功: {filename}")
            return True, target_path
        
        return False, None
        
    except Exception as e:
        logger.error(f"文件移动失败: {e}")
        return False, None
