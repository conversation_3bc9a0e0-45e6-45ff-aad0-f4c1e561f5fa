"""
Playwright录制工具 - 千川申诉流程录制
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 使用Playwright录制功能捕获真实的千川申诉操作流程
依赖关系: Playwright录制功能
清理条件: 录制完成后可保留作为参考

使用方法：
1. 运行此脚本启动Playwright录制
2. 手动执行完整的申诉流程
3. 录制完成后获得精确的代码
4. 将录制的代码集成到参数捕获器中
"""

import json
import subprocess
import sys
import os
from pathlib import Path
from loguru import logger

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class PlaywrightRecorder:
    """Playwright录制工具"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
    def _load_cookies(self):
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def create_cookies_script(self, advertiser_id: str) -> str:
        """创建包含cookies的录制脚本"""
        try:
            cookies = self._load_cookies()
            
            # 创建临时的录制脚本
            script_content = f'''
import json
from playwright.sync_api import sync_playwright

def run():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context(
            viewport={{'width': 1920, 'height': 1080}},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        # 添加cookies
        cookies = {json.dumps(cookies, indent=8)}
        context.add_cookies(cookies)
        
        page = context.new_page()
        page.set_default_timeout(30000)
        
        # 导航到千川后台
        page.goto("https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}")
        
        # 等待页面加载
        page.wait_for_timeout(3000)
        
        # 清理引导弹窗
        try:
            page.get_by_text("我知道了").nth(1).click(timeout=5000)
        except:
            pass
        
        print("🎯 录制环境已准备就绪！")
        print("现在请按照以下步骤操作：")
        print("1. 点击智投星图标")
        print("2. 发送文字指令：自助申诉表单")
        print("3. 点击：计划/商品申诉")
        print("4. 选择问题类型：计划审核不通过/结果申诉")
        print("5. 输入计划ID：1838840072680523")
        print("6. 点击提交")
        print("7. 完成后关闭浏览器")
        print()
        print("⚠️ 注意：请在操作过程中观察网络请求，特别是callback请求")
        
        # 保持浏览器打开，等待用户操作
        input("按回车键关闭浏览器...")
        
        browser.close()

if __name__ == "__main__":
    run()
'''
            
            # 保存脚本
            script_path = "ai_temp/playwright_record_session.py"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            logger.info(f"✅ 录制脚本已创建: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"创建录制脚本失败: {e}")
            raise
    
    def start_recording(self, advertiser_id: str, output_file: str = None):
        """启动Playwright录制"""
        try:
            if output_file is None:
                output_file = "ai_temp/recorded_qianchuan_appeal.py"
            
            logger.info("🎬 启动Playwright录制...")
            
            # 创建包含cookies的录制脚本
            script_path = self.create_cookies_script(advertiser_id)
            
            print("\n" + "="*60)
            print("🎬 Playwright录制指南")
            print("="*60)
            print("方法1: 使用Playwright Inspector (推荐)")
            print("1. 打开命令行，运行以下命令：")
            print(f"   playwright codegen --target python https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}")
            print()
            print("2. 在打开的浏览器中：")
            print("   - 手动登录千川后台")
            print("   - 执行完整的申诉流程")
            print("   - Playwright Inspector会自动生成代码")
            print()
            print("方法2: 使用预配置的录制环境")
            print("1. 运行以下命令：")
            print(f"   python {script_path}")
            print("2. 在打开的浏览器中执行申诉流程")
            print("3. 手动记录关键的选择器和操作")
            print()
            print("方法3: 使用浏览器开发者工具")
            print("1. 打开千川后台")
            print("2. 按F12打开开发者工具")
            print("3. 在Network标签页监听请求")
            print("4. 执行申诉流程")
            print("5. 复制关键的网络请求参数")
            print()
            print("🎯 关键录制要点：")
            print("- 重点关注元素的精确选择器")
            print("- 记录每个步骤的等待时间")
            print("- 捕获callback网络请求的参数")
            print("- 注意动态生成的元素ID")
            print("="*60)
            
            choice = input("\n选择录制方法 (1/2/3): ").strip()
            
            if choice == "1":
                print("\n🚀 启动Playwright Inspector...")
                print("请在新打开的浏览器窗口中执行申诉流程")
                print("录制的代码将显示在Inspector窗口中")
                
                cmd = [
                    "playwright", "codegen", 
                    "--target", "python",
                    f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
                ]
                subprocess.run(cmd)
                
            elif choice == "2":
                print(f"\n🚀 启动预配置录制环境...")
                print(f"运行命令: python {script_path}")
                subprocess.run([sys.executable, script_path])
                
            elif choice == "3":
                print("\n🚀 手动录制指导...")
                print("请按照上述指导使用浏览器开发者工具")
                print("完成后，请将关键信息提供给我")
                
            else:
                print("无效选择，退出录制")
                return
            
            print("\n✅ 录制会话完成")
            print("请将录制的代码或关键信息提供给我，我将集成到参数捕获器中")
            
        except Exception as e:
            logger.error(f"启动录制失败: {e}")
            raise
    
    def analyze_recorded_code(self, recorded_code: str) -> dict:
        """分析录制的代码，提取关键信息"""
        try:
            logger.info("🔍 分析录制的代码...")
            
            # 提取关键的选择器和操作
            key_patterns = {
                "copilot_selectors": [],
                "input_selectors": [],
                "button_selectors": [],
                "form_selectors": [],
                "wait_times": []
            }
            
            lines = recorded_code.split('\n')
            for line in lines:
                line = line.strip()
                
                # 提取选择器
                if 'locator(' in line and '"' in line:
                    selector = line.split('locator(')[1].split(')')[0].strip('"\'')
                    if 'copilot' in selector.lower():
                        key_patterns["copilot_selectors"].append(selector)
                    elif 'input' in selector.lower() or 'textarea' in selector.lower():
                        key_patterns["input_selectors"].append(selector)
                    elif 'button' in selector.lower():
                        key_patterns["button_selectors"].append(selector)
                    else:
                        key_patterns["form_selectors"].append(selector)
                
                # 提取等待时间
                if 'wait_for_timeout(' in line:
                    timeout = line.split('wait_for_timeout(')[1].split(')')[0]
                    key_patterns["wait_times"].append(timeout)
            
            logger.info("✅ 录制代码分析完成")
            return key_patterns
            
        except Exception as e:
            logger.error(f"分析录制代码失败: {e}")
            return {}


def start_playwright_recording(advertiser_id: str = "1836333804939273", principal_name: str = "缇萃百货"):
    """
    便捷函数：启动Playwright录制
    
    Args:
        advertiser_id: 广告户ID
        principal_name: 主体名称
    """
    recorder = PlaywrightRecorder(principal_name)
    recorder.start_recording(advertiser_id)


if __name__ == "__main__":
    print("🎬 Playwright录制工具 - 千川申诉流程")
    print("=" * 50)
    print("这个工具将帮助您：")
    print("1. 启动Playwright录制环境")
    print("2. 录制真实的申诉操作流程")
    print("3. 生成精确的自动化代码")
    print("4. 捕获关键的网络请求参数")
    print()
    
    try:
        start_playwright_recording()
    except Exception as e:
        print(f"录制失败: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_playwright_recorder import start_playwright_recording")
    print("start_playwright_recording('广告户ID')")
