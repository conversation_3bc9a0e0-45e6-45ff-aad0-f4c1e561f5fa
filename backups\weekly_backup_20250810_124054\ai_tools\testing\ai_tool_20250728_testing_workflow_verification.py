#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证千川自动化项目工作流修复后的运行状态
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psutil
import redis
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_celery_processes():
    """检查Celery进程状态"""
    logger.info("🔍 检查Celery进程状态...")
    
    worker_found = False
    beat_found = False
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            
            if 'run_celery_worker.py' in cmdline:
                worker_found = True
                logger.success(f"✅ Celery Worker进程运行中 (PID: {proc.info['pid']})")
            
            if 'run_celery_beat.py' in cmdline:
                beat_found = True
                logger.success(f"✅ Celery Beat进程运行中 (PID: {proc.info['pid']})")
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if not worker_found:
        logger.error("❌ Celery Worker进程未运行")
    
    if not beat_found:
        logger.error("❌ Celery Beat进程未运行")
    
    return worker_found and beat_found

def check_redis_connection():
    """检查Redis连接"""
    logger.info("🔍 检查Redis连接...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        # 测试连接
        r.ping()
        logger.success("✅ Redis连接正常")
        
        # 检查队列状态
        celery_keys = r.keys('celery*')
        logger.info(f"📋 Redis中有 {len(celery_keys)} 个Celery相关键")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    logger.info("🔍 检查数据库连接...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM local_creatives WHERE status = 'new'")
        new_count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logger.success(f"✅ 数据库连接正常，发现 {new_count} 个待处理素材")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def monitor_workflow_progress(duration_minutes=5):
    """监控工作流进度"""
    logger.info(f"📊 开始监控工作流进度 ({duration_minutes} 分钟)...")
    
    config = load_config()
    db_config = config['database']['postgresql']
    
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=duration_minutes)
    
    initial_stats = get_material_stats(db_config)
    logger.info(f"📈 初始状态: {initial_stats}")
    
    check_count = 0
    while datetime.now() < end_time:
        time.sleep(30)  # 每30秒检查一次
        check_count += 1
        
        current_stats = get_material_stats(db_config)
        logger.info(f"📊 检查 {check_count}: {current_stats}")
        
        # 检查是否有状态变化
        changes_detected = False
        for status in [MaterialStatus.NEW.value, MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value]:
            if current_stats.get(status, 0) != initial_stats.get(status, 0):
                changes_detected = True
                break
        
        if changes_detected:
            logger.success("🎯 检测到工作流状态变化，工作流正在运行！")
            break
    
    final_stats = get_material_stats(db_config)
    
    # 分析变化
    logger.info("\n" + "="*50)
    logger.info("📊 工作流进度分析")
    logger.info("="*50)
    
    for status in [MaterialStatus.NEW.value, MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.APPROVED.value]:
        initial = initial_stats.get(status, 0)
        final = final_stats.get(status, 0)
        change = final - initial
        
        if change != 0:
            direction = "📈" if change > 0 else "📉"
            logger.info(f"{direction} {status}: {initial} → {final} (变化: {change:+d})")
        else:
            logger.info(f"➡️ {status}: {initial} (无变化)")
    
    # 判断工作流是否正常
    new_decreased = final_stats.get(MaterialStatus.NEW.value, 0) < initial_stats.get(MaterialStatus.NEW.value, 0)
    processing_activity = final_stats.get(MaterialStatus.PROCESSING.value, 0) > 0
    
    if new_decreased or processing_activity:
        logger.success("✅ 工作流正在正常处理素材")
        return True
    else:
        logger.warning("⚠️ 工作流可能没有活动，建议检查Celery进程")
        return False

def get_material_stats(db_config):
    """获取素材状态统计"""
    try:
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        cursor.execute("""
            SELECT status, COUNT(*) 
            FROM local_creatives 
            WHERE status IN (MaterialStatus.NEW.value, MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.APPROVED.value)
            GROUP BY status
        """)
        
        stats = {}
        for status, count in cursor.fetchall():
            stats[status] = count
        
        cursor.close()
        conn.close()
        
        return stats
        
    except Exception as e:
        logger.error(f"❌ 获取素材统计失败: {e}")
        return {}

def check_file_organization():
    """检查文件组织情况"""
    logger.info("📁 检查文件组织情况...")
    
    target_dir = Path("G:/workflow_assets/01_materials_to_process/缇萃百货")
    
    if not target_dir.exists():
        logger.error(f"❌ 目标目录不存在: {target_dir}")
        return False
    
    # 统计目录中的文件
    video_files = list(target_dir.glob("*.mp4"))
    logger.info(f"📦 标准目录中有 {len(video_files)} 个MP4文件")
    
    if len(video_files) > 0:
        logger.success("✅ 文件组织正常")
        return True
    else:
        logger.warning("⚠️ 标准目录中没有文件")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    logger.info("🚀 开始千川工作流综合验证测试")
    logger.info("="*60)
    
    test_results = {
        'celery_processes': False,
        'redis_connection': False,
        'database_connection': False,
        'file_organization': False,
        'workflow_progress': False
    }
    
    # 1. 检查Celery进程
    test_results['celery_processes'] = check_celery_processes()
    
    # 2. 检查Redis连接
    test_results['redis_connection'] = check_redis_connection()
    
    # 3. 检查数据库连接
    test_results['database_connection'] = check_database_connection()
    
    # 4. 检查文件组织
    test_results['file_organization'] = check_file_organization()
    
    # 5. 监控工作流进度（如果前面的检查都通过）
    if all([test_results['celery_processes'], test_results['redis_connection'], 
            test_results['database_connection']]):
        test_results['workflow_progress'] = monitor_workflow_progress(3)
    else:
        logger.warning("⚠️ 基础检查未通过，跳过工作流进度监控")
    
    # 输出测试结果
    logger.info("\n" + "="*60)
    logger.info("🎯 综合验证测试结果")
    logger.info("="*60)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display = test_name.replace('_', ' ').title()
        logger.info(f"{status} {test_display}")
    
    # 计算通过率
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    pass_rate = (passed_tests / total_tests) * 100
    
    logger.info(f"\n📊 测试通过率: {passed_tests}/{total_tests} ({pass_rate:.1f}%)")
    
    if pass_rate >= 80:
        logger.success("🎉 工作流验证测试基本通过！")
        logger.info("\n📋 建议后续操作:")
        logger.info("1. 持续监控素材处理进度")
        logger.info("2. 定期运行监控脚本")
        logger.info("3. 关注上传失败的素材处理")
        return True
    else:
        logger.error("❌ 工作流验证测试存在问题")
        logger.info("\n🔧 建议修复操作:")
        
        if not test_results['celery_processes']:
            logger.info("- 重启Celery Worker和Beat进程")
        if not test_results['redis_connection']:
            logger.info("- 检查Redis服务状态")
        if not test_results['database_connection']:
            logger.info("- 检查数据库连接配置")
        if not test_results['file_organization']:
            logger.info("- 检查文件目录结构")
        
        return False

def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️ 测试已取消")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
