"""
安全删除检查器 - 确保遵循视频删除业务铁律

业务铁律：只有计划提审完成且该计划下的视频审核不通过时，才能删除视频文件

创建时间: 2025-08-09
作者: AI Assistant
目的: 修复视频删除违规行为，确保业务安全
"""

import os
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger


class SafeDeletionChecker:
    """安全删除检查器 - 确保遵循业务铁律"""
    
    def __init__(self, db_session):
        self.db = db_session
    
    def can_safely_delete_video(self, file_path: str, campaign_id_qc: Optional[str] = None) -> Dict[str, Any]:
        """
        检查是否可以安全删除视频文件
        
        业务铁律：只有计划提审完成且该计划下的视频审核不通过时，才能删除视频文件
        
        Args:
            file_path: 视频文件路径
            campaign_id_qc: 关联的计划ID
            
        Returns:
            Dict: {
                'can_delete': bool,
                'reason': str,
                'campaign_status': str,
                'appeal_status': str,
                'video_status': str
            }
        """
        result = {
            'can_delete': False,
            'reason': '',
            'campaign_status': '',
            'appeal_status': '',
            'video_status': ''
        }
        
        try:
            # 1. 如果没有关联计划，不能删除
            if not campaign_id_qc:
                result['reason'] = '🛡️ 业务铁律保护: 无关联计划，不能删除源视频文件'
                logger.warning(f"删除被阻止: {file_path} - 无关联计划")
                return result
            
            # 2. 查询计划状态
            from qianchuan_aw.database.models import Campaign
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                result['reason'] = f'🛡️ 业务铁律保护: 计划 {campaign_id_qc} 不存在'
                logger.warning(f"删除被阻止: {file_path} - 计划不存在")
                return result
            
            result['campaign_status'] = campaign.status
            result['appeal_status'] = campaign.appeal_status or 'not_appealed'
            
            # 3. 检查申诉是否完成
            from qianchuan_aw.utils.appeal_status_definitions import is_terminal_state
            
            if not is_terminal_state(campaign.appeal_status):
                result['reason'] = f'🛡️ 业务铁律保护: 计划 {campaign_id_qc} 申诉未完成，当前状态: {campaign.appeal_status}'
                logger.warning(f"删除被阻止: {file_path} - 申诉未完成")
                return result
            
            # 4. 检查申诉结果
            from qianchuan_aw.utils.appeal_status_definitions import AppealStatus
            
            if campaign.appeal_status == AppealStatus.APPEAL_SUCCESS.value:
                result['reason'] = f'🛡️ 业务铁律保护: 计划 {campaign_id_qc} 申诉成功，不能删除视频'
                logger.warning(f"删除被阻止: {file_path} - 申诉成功")
                return result
            
            # 5. 检查视频审核状态
            if campaign.appeal_status == AppealStatus.APPEAL_FAILED.value:
                result['can_delete'] = True
                result['reason'] = f'✅ 符合业务铁律: 计划 {campaign_id_qc} 申诉失败，视频审核不通过，可以删除'
                result['video_status'] = MaterialStatus.REJECTED.value
                logger.info(f"删除权限验证通过: {file_path} - 申诉失败")
                return result
            
            result['reason'] = f'🛡️ 业务铁律保护: 计划 {campaign_id_qc} 状态不明确，不能删除'
            logger.warning(f"删除被阻止: {file_path} - 状态不明确")
            return result
            
        except Exception as e:
            result['reason'] = f'🛡️ 业务铁律保护: 检查删除权限时出错: {e}'
            logger.error(f"删除权限检查失败: {file_path} - {e}")
            return result
    
    def safe_delete_video_file(self, file_path: str, campaign_id_qc: Optional[str] = None) -> Dict[str, Any]:
        """
        安全删除视频文件
        
        Args:
            file_path: 视频文件路径
            campaign_id_qc: 关联的计划ID
            
        Returns:
            Dict: 删除操作结果
        """
        # 1. 检查是否可以删除
        check_result = self.can_safely_delete_video(file_path, campaign_id_qc)
        
        if not check_result['can_delete']:
            return {
                'success': False,
                'deleted': False,
                'reason': check_result['reason'],
                'violation': True,
                'message': check_result['reason']
            }
        
        # 2. 执行删除
        try:
            if os.path.exists(file_path):
                # 记录删除操作
                logger.warning(f"🗑️ 执行安全删除: {file_path}")
                logger.info(f"删除原因: {check_result['reason']}")
                
                os.remove(file_path)
                
                return {
                    'success': True,
                    'deleted': True,
                    'reason': check_result['reason'],
                    'violation': False,
                    'message': f'✅ 安全删除成功: {os.path.basename(file_path)}'
                }
            else:
                return {
                    'success': True,
                    'deleted': True,
                    'reason': '文件不存在',
                    'violation': False,
                    'message': f'文件不存在，无需删除: {file_path}'
                }
                
        except Exception as e:
            return {
                'success': False,
                'deleted': False,
                'reason': f'删除操作失败: {e}',
                'violation': False,
                'message': f'❌ 删除失败: {e}'
            }
    
    def mark_quality_issue_without_deletion(self, file_path: str, reason: str, creative_id: Optional[int] = None) -> Dict[str, Any]:
        """
        标记质量问题但不删除文件
        
        这是符合业务铁律的处理方式：质量问题应该标记为失败，而不是删除文件
        
        Args:
            file_path: 文件路径
            reason: 质量问题原因
            creative_id: 关联的素材ID
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.warning(f"🔍 质量问题标记: {file_path}")
            logger.info(f"质量问题原因: {reason}")
            logger.info(f"🛡️ 业务铁律保护: 文件未删除，仅标记为质量问题")
            
            # 如果有关联的数据库记录，标记为失败状态
            if creative_id:
                from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.unified_material_status import MaterialStatus
                creative = self.db.get(LocalCreative, creative_id)
                if creative:
                    creative.status = MaterialStatus.QUALITY_FAILED.value
                    creative.error_message = f'质量问题: {reason}'
                    creative.updated_at = datetime.now()
                    self.db.commit()
                    
                    logger.info(f"已标记数据库记录为质量失败: LocalCreative ID {creative_id}")
            
            return {
                'success': True,
                'deleted': False,
                'marked_failed': True,
                'reason': reason,
                'message': f'✅ 质量问题已标记，文件保留: {os.path.basename(file_path)}'
            }
            
        except Exception as e:
            logger.error(f"标记质量问题失败: {e}")
            return {
                'success': False,
                'deleted': False,
                'marked_failed': False,
                'reason': f'标记失败: {e}',
                'message': f'❌ 标记质量问题失败: {e}'
            }


def get_safe_deletion_checker(db_session) -> SafeDeletionChecker:
    """获取安全删除检查器实例"""
    return SafeDeletionChecker(db_session)


# 兼容性函数：为质量控制器提供便捷方法
def create_quality_issue_marker(db_session):
    """创建质量问题标记器（不删除文件）"""
    return SafeDeletionChecker(db_session)
