#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实时系统运行状态监控
清理条件: 成为项目核心监控工具，长期保留
"""

import os
import sys
import time
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.unified_material_status import MaterialStatus


class RealtimeSystemMonitor:
    """实时系统监控器"""
    
    def __init__(self):
        self.baseline_data = None
        self.monitoring_start_time = datetime.now(timezone.utc)
    
    def start_monitoring(self, duration_minutes: int = 10):
        """开始监控系统运行状态"""
        logger.info("📊 启动实时系统监控")
        logger.info("="*80)
        
        # 记录基线数据
        self.baseline_data = self._collect_current_status()
        logger.info("📋 基线数据已记录")
        
        # 显示基线状态
        self._display_status(self.baseline_data, "基线状态")
        
        # 开始监控循环
        logger.info(f"🔄 开始监控 {duration_minutes} 分钟...")
        
        for minute in range(1, duration_minutes + 1):
            time.sleep(60)  # 等待1分钟
            
            current_data = self._collect_current_status()
            self._display_progress(current_data, minute, duration_minutes)
        
        # 生成最终报告
        final_data = self._collect_current_status()
        self._generate_monitoring_report(self.baseline_data, final_data, duration_minutes)
    
    def _collect_current_status(self):
        """收集当前状态"""
        with SessionLocal() as db:
            status_counts = {}
            
            # 查询各状态数量
            all_statuses = [
                MaterialStatus.NEW.value, MaterialStatus.PENDING_GROUPING.value, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value,
                MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value, 
                MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, MaterialStatus.UPLOAD_FAILED.value, MaterialStatus.ALREADY_TESTED.value
            ]
            
            for status in all_statuses:
                count = db.query(LocalCreative).filter(
                    LocalCreative.status == status
                ).count()
                status_counts[status] = count
            
            return {
                'timestamp': datetime.now(timezone.utc),
                'status_counts': status_counts,
                'total_creatives': sum(status_counts.values())
            }
    
    def _display_status(self, data, title):
        """显示状态信息"""
        logger.info(f"\n📊 {title} ({data['timestamp'].strftime('%H:%M:%S')})")
        logger.info("-" * 60)
        
        status_counts = data['status_counts']
        
        # 关键状态
        key_statuses = [
            (MaterialStatus.PENDING_UPLOAD.value, '📤', '待上传'),
            (MaterialStatus.PROCESSING.value, '⏳', '处理中'),
            (MaterialStatus.UPLOADED_PENDING_PLAN.value, '📋', '待创建计划'),
            (MaterialStatus.TESTING_PENDING_REVIEW.value, '🔍', '测试中'),
            (MaterialStatus.APPROVED.value, '✅', '已通过'),
            (MaterialStatus.REJECTED.value, '❌', '已拒绝')
        ]
        
        for status, icon, desc in key_statuses:
            count = status_counts.get(status, 0)
            if count > 0:
                logger.info(f"   {icon} {desc}: {count} 个")
    
    def _display_progress(self, current_data, minute, total_minutes):
        """显示进度"""
        logger.info(f"\n⏱️ 监控进度: {minute}/{total_minutes} 分钟")
        
        # 计算变化
        baseline_counts = self.baseline_data['status_counts']
        current_counts = current_data['status_counts']
        
        changes = {}
        for status in current_counts:
            baseline_count = baseline_counts.get(status, 0)
            current_count = current_counts.get(status, 0)
            change = current_count - baseline_count
            if change != 0:
                changes[status] = change
        
        if changes:
            logger.info("📈 状态变化:")
            for status, change in changes.items():
                change_icon = "📈" if change > 0 else "📉"
                change_sign = "+" if change > 0 else ""
                logger.info(f"   {change_icon} {status}: {change_sign}{change}")
        else:
            logger.info("📊 无状态变化")
        
        # 关键指标
        processing_count = current_counts.get(MaterialStatus.PROCESSING.value, 0)
        pending_upload_count = current_counts.get(MaterialStatus.PENDING_UPLOAD.value, 0)
        
        if processing_count > 50:
            logger.warning(f"⚠️ Processing积压较多: {processing_count}个")
        elif processing_count > 0:
            logger.info(f"ℹ️ Processing正常: {processing_count}个")
        else:
            logger.success("✅ 无Processing积压")
    
    def _generate_monitoring_report(self, baseline_data, final_data, duration_minutes):
        """生成监控报告"""
        logger.info(f"\n📋 {duration_minutes}分钟监控报告")
        logger.info("="*80)
        
        baseline_counts = baseline_data['status_counts']
        final_counts = final_data['status_counts']
        
        # 计算总体变化
        total_changes = {}
        significant_changes = {}
        
        for status in set(list(baseline_counts.keys()) + list(final_counts.keys())):
            baseline_count = baseline_counts.get(status, 0)
            final_count = final_counts.get(status, 0)
            change = final_count - baseline_count
            
            total_changes[status] = change
            
            if abs(change) >= 5:  # 变化超过5个认为是显著变化
                significant_changes[status] = change
        
        # 显示显著变化
        if significant_changes:
            logger.info("📈 显著状态变化:")
            for status, change in significant_changes.items():
                change_icon = "📈" if change > 0 else "📉"
                change_sign = "+" if change > 0 else ""
                
                # 特殊状态的解释
                if status == MaterialStatus.PENDING_UPLOAD.value and change < 0:
                    logger.success(f"   {change_icon} {status}: {change_sign}{change} (✅ 上传进度良好)")
                elif status == MaterialStatus.UPLOADED_PENDING_PLAN.value and change > 0:
                    logger.success(f"   {change_icon} {status}: {change_sign}{change} (✅ 上传成功增加)")
                elif status == MaterialStatus.PROCESSING.value and change > 0:
                    logger.warning(f"   {change_icon} {status}: {change_sign}{change} (⚠️ 需要关注)")
                else:
                    logger.info(f"   {change_icon} {status}: {change_sign}{change}")
        else:
            logger.info("📊 无显著状态变化")
        
        # 性能评估
        upload_progress = abs(total_changes.get(MaterialStatus.PENDING_UPLOAD.value, 0))
        upload_success = total_changes.get(MaterialStatus.UPLOADED_PENDING_PLAN.value, 0)
        processing_change = total_changes.get(MaterialStatus.PROCESSING.value, 0)
        
        logger.info(f"\n🎯 性能评估:")
        
        # 上传效率
        if upload_progress > 0:
            upload_rate = upload_progress / duration_minutes * 60  # 每小时
            logger.info(f"   📤 上传效率: ~{upload_rate:.1f} 个/小时")
            
            if upload_rate >= 50:
                logger.success("   ✅ 上传效率优秀")
            elif upload_rate >= 20:
                logger.info("   ℹ️ 上传效率良好")
            else:
                logger.warning("   ⚠️ 上传效率需要改进")
        
        # 状态稳定性
        if abs(processing_change) <= 5:
            logger.success("   ✅ Processing状态稳定")
        else:
            logger.warning(f"   ⚠️ Processing状态变化较大: {processing_change}")
        
        # 总体评估
        if upload_progress > 0 and abs(processing_change) <= 10:
            logger.success("🎊 系统运行良好，优化效果显著")
            overall_status = 'EXCELLENT'
        elif upload_progress > 0:
            logger.info("ℹ️ 系统运行正常，有改进效果")
            overall_status = 'GOOD'
        else:
            logger.warning("⚠️ 系统运行需要关注")
            overall_status = 'NEEDS_ATTENTION'
        
        return {
            'duration_minutes': duration_minutes,
            'upload_progress': upload_progress,
            'upload_success': upload_success,
            'processing_change': processing_change,
            'overall_status': overall_status,
            'significant_changes': significant_changes
        }


def main():
    """主函数"""
    monitor = RealtimeSystemMonitor()
    
    logger.info("🚀 启动千川自动化项目实时监控")
    logger.info("🎯 监控目标：验证第二阶段优化效果")
    
    # 监控5分钟
    monitor.start_monitoring(duration_minutes=5)
    
    logger.success("📊 实时监控完成")
    logger.success("💡 建议：根据监控结果调整系统参数")
    
    return 0


if __name__ == "__main__":
    exit(main())
