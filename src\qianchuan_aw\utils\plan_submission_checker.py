#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计划提审状态检查器 - 增强版本
确保严格执行防重复提审铁律
"""

from typing import Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import Campaign, AdAccount


class PlanSubmissionChecker:
    """计划提审状态检查器 - 铁律4实施"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def validate_plan_submission_eligibility(self, campaign_id_qc: str) -> Dict[str, Any]:
        """验证计划提审资格 - 防重复提审铁律"""
        
        try:
            # 获取计划信息
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                return {
                    'is_eligible': False,
                    'reason': 'campaign_not_found',
                    'message': f'计划不存在: {campaign_id_qc}'
                }
            
            # 检查1: 计划状态必须是AUDITING
            if campaign.status != 'AUDITING':
                return {
                    'is_eligible': False,
                    'reason': 'invalid_campaign_status',
                    'message': f'计划状态不符合提审要求: {campaign.status}，期望: AUDITING',
                    'violation_rule': '铁律4：只有AUDITING状态的计划才能提审'
                }
            
            # 检查2: 提审状态检查
            if campaign.appeal_status == 'appeal_pending':
                return {
                    'is_eligible': False,
                    'reason': 'already_pending_appeal',
                    'message': '计划已在提审队列中',
                    'violation_rule': '铁律4：防重复提审约束'
                }
            
            # 检查3: 提审历史检查
            if campaign.first_appeal_at is not None:
                return {
                    'is_eligible': False,
                    'reason': 'already_appealed',
                    'message': f'计划已提审过，时间: {campaign.first_appeal_at}',
                    'violation_rule': '铁律4：每个计划只能提审一次'
                }
            
            # 检查4: 尝试次数限制
            appeal_attempt_count = getattr(campaign, 'appeal_attempt_count', 0)
            if appeal_attempt_count >= 3:
                return {
                    'is_eligible': False,
                    'reason': 'max_attempts_exceeded',
                    'message': f'提审尝试次数已达上限: {appeal_attempt_count}/3',
                    'violation_rule': '铁律4：提审尝试次数限制'
                }
            
            # 检查5: 账户状态检查
            account = campaign.account
            if account.status == 'deleted':
                return {
                    'is_eligible': False,
                    'reason': 'deleted_account',
                    'message': '账户已删除，禁止提审操作',
                    'violation_rule': '铁律3：deleted账户严禁任何操作'
                }
            
            # 检查6: 时间间隔检查（防止过于频繁的提审）
            if campaign.last_appeal_at:
                time_since_last_appeal = datetime.now(timezone.utc) - campaign.last_appeal_at
                if time_since_last_appeal < timedelta(hours=1):
                    return {
                        'is_eligible': False,
                        'reason': 'too_frequent_appeal',
                        'message': f'距离上次提审时间过短: {time_since_last_appeal}',
                        'violation_rule': '提审频率限制'
                    }
            
            # 所有检查通过
            return {
                'is_eligible': True,
                'reason': 'all_checks_passed',
                'message': '计划符合提审条件'
            }
            
        except Exception as e:
            logger.error(f"提审资格检查失败: {e}")
            return {
                'is_eligible': False,
                'reason': 'check_failed',
                'message': f'检查过程发生错误: {str(e)}'
            }
    
    def mark_plan_as_submitted(self, campaign_id_qc: str) -> Dict[str, Any]:
        """标记计划为已提审状态"""
        
        try:
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                return {'success': False, 'error': 'campaign_not_found'}
            
            # 更新提审状态
            campaign.appeal_status = 'appeal_pending'
            campaign.first_appeal_at = datetime.now(timezone.utc)
            campaign.last_appeal_at = datetime.now(timezone.utc)
            
            # 增加尝试次数
            if hasattr(campaign, 'appeal_attempt_count'):
                campaign.appeal_attempt_count = (campaign.appeal_attempt_count or 0) + 1
            
            self.db.commit()
            
            logger.info(f"✅ 计划 {campaign_id_qc} 已标记为提审状态")
            
            return {
                'success': True,
                'message': '计划提审状态更新成功'
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"标记提审状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_submission_statistics(self) -> Dict[str, Any]:
        """获取提审统计信息"""
        
        try:
            stats = self.db.execute(text("""
                SELECT 
                    COUNT(*) as total_campaigns,
                    COUNT(CASE WHEN appeal_status = 'appeal_pending' THEN 1 END) as pending_appeals,
                    COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as already_appealed,
                    COUNT(CASE WHEN status = 'AUDITING' AND appeal_status IS NULL THEN 1 END) as eligible_for_appeal
                FROM campaigns
                WHERE status IN ('AUDITING', 'AUDIT_ACCEPTED', 'AUDIT_REJECT')
            """)).fetchone()
            
            return {
                'total_campaigns': stats.total_campaigns,
                'pending_appeals': stats.pending_appeals,
                'already_appealed': stats.already_appealed,
                'eligible_for_appeal': stats.eligible_for_appeal,
                'compliance_rate': (stats.already_appealed / stats.total_campaigns * 100) if stats.total_campaigns > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"获取提审统计失败: {e}")
            return {'error': str(e)}
    
    def cleanup_duplicate_appeal_status(self) -> Dict[str, Any]:
        """清理重复提审状态"""
        
        try:
            # 查找可能的重复提审
            duplicate_appeals = self.db.execute(text("""
                SELECT campaign_id_qc, appeal_status, first_appeal_at, last_appeal_at
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending'
                  AND first_appeal_at IS NOT NULL 
                  AND last_appeal_at IS NOT NULL
                  AND first_appeal_at != last_appeal_at
            """)).fetchall()
            
            cleaned_count = 0
            
            for appeal in duplicate_appeals:
                # 检查计划当前状态，如果已经成功则更新状态
                campaign = self.db.query(Campaign).filter(
                    Campaign.campaign_id_qc == appeal.campaign_id_qc
                ).first()
                
                if campaign and campaign.status in ['AUDIT_ACCEPTED', 'DELIVERY_OK']:
                    campaign.appeal_status = 'appeal_success'
                    cleaned_count += 1
            
            self.db.commit()
            
            return {
                'success': True,
                'cleaned_count': cleaned_count,
                'total_duplicates_found': len(duplicate_appeals)
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"清理重复提审状态失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_batch_submissions(self, campaign_ids: list) -> Dict[str, Any]:
        """批量验证计划提审资格"""
        
        results = {
            'total_campaigns': len(campaign_ids),
            'eligible_campaigns': [],
            'ineligible_campaigns': [],
            'errors': []
        }
        
        for campaign_id in campaign_ids:
            try:
                validation_result = self.validate_plan_submission_eligibility(campaign_id)
                
                if validation_result['is_eligible']:
                    results['eligible_campaigns'].append({
                        'campaign_id': campaign_id,
                        'message': validation_result['message']
                    })
                else:
                    results['ineligible_campaigns'].append({
                        'campaign_id': campaign_id,
                        'reason': validation_result['reason'],
                        'message': validation_result['message'],
                        'violation_rule': validation_result.get('violation_rule')
                    })
                    
            except Exception as e:
                results['errors'].append({
                    'campaign_id': campaign_id,
                    'error': str(e)
                })
        
        results['eligible_count'] = len(results['eligible_campaigns'])
        results['ineligible_count'] = len(results['ineligible_campaigns'])
        results['error_count'] = len(results['errors'])
        
        return results
