"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化提审模块的工作流集成组件
清理条件: 作为核心集成组件，长期维护
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class AppealWorkflowScheduler:
    """提审工作流调度器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._load_default_config()
        self.last_execution = None
        self.execution_history = []
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'max_plans_per_advertiser': 10,
            'max_concurrent_advertisers': 3,
            'execution_interval_minutes': 30,
            'max_daily_executions': 48,
            'success_rate_threshold': 80.0,
            'retry_failed_advertisers': True,
            'notification_enabled': True
        }
    
    def should_execute(self) -> bool:
        """判断是否应该执行提审"""
        now = datetime.now()
        
        # 检查执行间隔
        if self.last_execution:
            interval = timedelta(minutes=self.config['execution_interval_minutes'])
            if now - self.last_execution < interval:
                return False
        
        # 检查每日执行次数限制
        today = now.date()
        today_executions = [
            exec_time for exec_time in self.execution_history 
            if exec_time.date() == today
        ]
        
        if len(today_executions) >= self.config['max_daily_executions']:
            logger.warning(f"⚠️ 今日执行次数已达上限: {len(today_executions)}")
            return False
        
        # 检查系统健康状态
        from ai_tool_20250729_batch_appeal_architecture import AppealMonitor
        health = AppealMonitor.check_system_health()
        
        if not health['healthy']:
            logger.error(f"❌ 系统健康检查失败: {health.get('error', '未知错误')}")
            return False
        
        # 检查是否有待提审计划
        if health['pending_appeals'] == 0:
            logger.info("📋 没有待提审计划，跳过执行")
            return False
        
        logger.info(f"✅ 满足执行条件，待提审计划: {health['pending_appeals']} 个")
        return True
    
    def execute_scheduled_appeal(self) -> Dict[str, Any]:
        """执行调度的提审任务"""
        if not self.should_execute():
            return {'skipped': True, 'reason': '不满足执行条件'}
        
        logger.info("🚀 开始执行调度的批量提审任务")
        
        try:
            from ai_tool_20250729_batch_appeal_architecture import execute_advertiser_level_batch_appeal
            
            # 执行批量提审
            result = execute_advertiser_level_batch_appeal(
                max_plans_per_advertiser=self.config['max_plans_per_advertiser'],
                max_concurrent_advertisers=self.config['max_concurrent_advertisers']
            )
            
            # 记录执行历史
            self.last_execution = datetime.now()
            self.execution_history.append(self.last_execution)
            
            # 保留最近100次执行记录
            if len(self.execution_history) > 100:
                self.execution_history = self.execution_history[-100:]
            
            # 检查执行结果
            if 'error' not in result:
                success_rate = result.get('success_rate', 0)
                
                if success_rate < self.config['success_rate_threshold']:
                    logger.warning(f"⚠️ 成功率低于阈值: {success_rate:.1f}% < {self.config['success_rate_threshold']}%")
                    result['warning'] = f"成功率低于阈值: {success_rate:.1f}%"
                
                # 发送通知
                if self.config['notification_enabled']:
                    self._send_notification(result)
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 调度执行失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now()}
    
    def _send_notification(self, result: Dict[str, Any]):
        """发送执行通知"""
        try:
            from ai_tool_20250729_batch_appeal_architecture import AppealMonitor
            
            report = AppealMonitor.generate_execution_report(result)
            
            # 这里可以集成邮件、钉钉、企业微信等通知方式
            logger.info(f"📧 执行报告:\n{report}")
            
            # 如果成功率过低，发送警告通知
            if result.get('success_rate', 100) < self.config['success_rate_threshold']:
                logger.warning(f"🚨 提审成功率警告: {result.get('success_rate', 0):.1f}%")
            
        except Exception as e:
            logger.error(f"❌ 发送通知失败: {e}")

class CeleryTaskIntegration:
    """Celery任务集成"""
    
    @staticmethod
    def register_appeal_tasks():
        """注册提审相关的Celery任务"""
        
        # 这里展示如何集成到现有的Celery任务系统
        task_definitions = """
# 添加到 celery_tasks.py 或相应的任务文件中

from celery import Celery
from ai_tools.ai_tool_20250729_workflow_integration import AppealWorkflowScheduler

app = Celery('qianchuan_automation')

@app.task(name='batch_appeal_scheduled')
def batch_appeal_scheduled_task():
    '''定时批量提审任务'''
    scheduler = AppealWorkflowScheduler()
    result = scheduler.execute_scheduled_appeal()
    return result

@app.task(name='batch_appeal_manual')
def batch_appeal_manual_task(max_plans_per_advertiser=10, max_concurrent_advertisers=3):
    '''手动触发批量提审任务'''
    from ai_tools.ai_tool_20250729_batch_appeal_architecture import execute_advertiser_level_batch_appeal
    
    result = execute_advertiser_level_batch_appeal(
        max_plans_per_advertiser=max_plans_per_advertiser,
        max_concurrent_advertisers=max_concurrent_advertisers
    )
    return result

@app.task(name='appeal_health_check')
def appeal_health_check_task():
    '''提审系统健康检查任务'''
    from ai_tools.ai_tool_20250729_batch_appeal_architecture import AppealMonitor
    
    health = AppealMonitor.check_system_health()
    return health
        """
        
        return task_definitions
    
    @staticmethod
    def get_celery_beat_schedule():
        """获取Celery Beat调度配置"""
        return {
            'batch-appeal-every-30-minutes': {
                'task': 'batch_appeal_scheduled',
                'schedule': 30.0 * 60,  # 每30分钟执行一次
                'options': {'queue': 'appeal_queue'}
            },
            'appeal-health-check-every-5-minutes': {
                'task': 'appeal_health_check',
                'schedule': 5.0 * 60,   # 每5分钟检查一次
                'options': {'queue': 'monitor_queue'}
            }
        }

class WebUIIntegration:
    """Web UI集成"""
    
    @staticmethod
    def get_streamlit_components():
        """获取Streamlit组件代码"""
        
        components_code = '''
# 添加到 web_ui.py 或相应的UI文件中

import streamlit as st
from ai_tools.ai_tool_20250729_workflow_integration import AppealWorkflowScheduler
from ai_tools.ai_tool_20250729_batch_appeal_architecture import execute_advertiser_level_batch_appeal, AppealMonitor

def render_batch_appeal_page():
    """渲染批量提审页面"""
    st.title("🚀 千川自动化批量提审")
    
    # 系统状态
    st.subheader("📊 系统状态")
    health = AppealMonitor.check_system_health()
    
    if health['healthy']:
        st.success(f"✅ 系统正常 - 待提审: {health['pending_appeals']} 个，今日已处理: {health['today_appeals']} 个")
    else:
        st.error(f"❌ 系统异常: {health.get('error', '未知错误')}")
    
    # 手动执行
    st.subheader("🎯 手动执行")
    
    col1, col2 = st.columns(2)
    with col1:
        max_plans = st.number_input("每广告户最大计划数", min_value=1, max_value=20, value=10)
    with col2:
        max_concurrent = st.number_input("最大并发广告户数", min_value=1, max_value=5, value=3)
    
    if st.button("🚀 立即执行批量提审", type="primary"):
        with st.spinner("正在执行批量提审..."):
            result = execute_advertiser_level_batch_appeal(
                max_plans_per_advertiser=max_plans,
                max_concurrent_advertisers=max_concurrent
            )
        
        if 'error' in result:
            st.error(f"❌ 执行失败: {result['error']}")
        else:
            st.success(f"✅ 执行完成！成功率: {result['success_rate']:.1f}%")
            
            # 显示详细结果
            st.subheader("📋 执行结果")
            
            metrics_col1, metrics_col2, metrics_col3, metrics_col4 = st.columns(4)
            with metrics_col1:
                st.metric("处理广告户", result['total_advertisers'])
            with metrics_col2:
                st.metric("处理计划", result['total_plans'])
            with metrics_col3:
                st.metric("成功计划", result['success_count'])
            with metrics_col4:
                st.metric("成功率", f"{result['success_rate']:.1f}%")
            
            # 广告户详情
            if result.get('summaries'):
                st.subheader("📊 广告户详情")
                
                for summary in result['summaries']:
                    with st.expander(f"{summary.account_name} - {summary.success_count}/{summary.total_plans}"):
                        st.write(f"**成功率**: {summary.success_rate:.1f}%")
                        st.write(f"**耗时**: {summary.total_duration:.1f} 秒")
                        st.write(f"**方法统计**: {summary.method_stats}")
    
    # 调度配置
    st.subheader("⏰ 调度配置")
    scheduler = AppealWorkflowScheduler()
    
    st.write(f"**执行间隔**: {scheduler.config['execution_interval_minutes']} 分钟")
    st.write(f"**成功率阈值**: {scheduler.config['success_rate_threshold']}%")
    st.write(f"**最后执行时间**: {scheduler.last_execution or '未执行'}")
    
    if st.button("🔧 测试调度条件"):
        should_execute = scheduler.should_execute()
        if should_execute:
            st.success("✅ 满足调度执行条件")
        else:
            st.info("ℹ️ 暂不满足调度执行条件")

# 在主UI中添加页面
def main():
    # ... 其他页面代码 ...
    
    if selected_page == "批量提审":
        render_batch_appeal_page()
        '''
        
        return components_code

def main():
    """主函数 - 演示集成使用"""
    logger.info("🚀 千川自动化提审工作流集成演示")
    
    # 1. 创建调度器
    scheduler = AppealWorkflowScheduler()
    
    # 2. 检查执行条件
    if scheduler.should_execute():
        logger.info("✅ 满足执行条件，开始执行")
        
        # 3. 执行批量提审
        result = scheduler.execute_scheduled_appeal()
        
        # 4. 显示结果
        if 'error' in result:
            logger.error(f"❌ 执行失败: {result['error']}")
        elif result.get('skipped'):
            logger.info(f"⏭️ 跳过执行: {result.get('reason')}")
        else:
            logger.success(f"🎉 执行成功！处理了 {result['total_advertisers']} 个广告户，{result['total_plans']} 个计划")
    else:
        logger.info("⏸️ 不满足执行条件，跳过本次执行")
    
    # 5. 显示集成信息
    print("\n" + "="*60)
    print("📋 工作流集成信息")
    print("="*60)
    
    print("\n🔧 Celery任务集成:")
    celery_integration = CeleryTaskIntegration()
    print("- 任务定义已生成，请添加到celery_tasks.py")
    print("- Beat调度配置已生成，请添加到celery配置")
    
    print("\n🌐 Web UI集成:")
    print("- Streamlit组件代码已生成，请添加到web_ui.py")
    print("- 支持手动执行、状态监控、调度配置")
    
    print("\n📊 监控集成:")
    print("- 系统健康检查")
    print("- 执行报告生成")
    print("- 成功率监控和告警")

if __name__ == "__main__":
    main()
