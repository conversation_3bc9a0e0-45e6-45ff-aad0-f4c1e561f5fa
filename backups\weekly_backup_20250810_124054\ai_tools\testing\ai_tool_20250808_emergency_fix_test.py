#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 紧急修复测试
生命周期: 临时使用
创建目的: 测试KeyError修复效果，确保系统稳定
清理条件: 修复验证完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

def test_incomplete_task_handling():
    """测试不完整任务处理"""
    print("🔍 测试不完整任务处理...")
    
    try:
        from qianchuan_aw.workflows.batch_uploader import BatchUploader
        
        # 模拟配置
        mock_settings = {
            'workflow': {'max_upload_workers': 2},
            'upload_optimization': {
                'batch_size': 5,
                'pre_compute_md5': True,
                'validation_cache_enabled': True,
                'batch_db_operations': True
            },
            'api_credentials': {'app_id': 'test', 'secret': 'test'}
        }
        
        uploader = BatchUploader(mock_settings)
        
        # 创建混合任务列表（包含完整和不完整的任务）
        mixed_tasks = [
            # 完整任务
            {
                'file_path': '/test/video1.mp4',
                'local_creative_id': 1,
                'account_id': 1,
                'principal_name': 'test'
            },
            # 不完整任务 - 缺少local_creative_id
            {
                'file_path': '/test/video2.mp4',
                'account_id': 1,
                'principal_name': 'test'
            },
            # 不完整任务 - 缺少file_path
            {
                'local_creative_id': 3,
                'account_id': 1,
                'principal_name': 'test'
            },
            # 完全错误的任务
            "invalid_task_string",
            # 空任务
            {},
            # None任务
            None,
            # 另一个完整任务
            {
                'file_path': '/test/video3.mp4',
                'local_creative_id': 4,
                'account_id': 1,
                'principal_name': 'test'
            }
        ]
        
        print(f"✅ 批量上传器初始化成功")
        print(f"   测试任务数: {len(mixed_tasks)} (包含不完整任务)")
        
        # 测试batch_upload_videos方法是否能处理混合任务
        try:
            # 这应该不会抛出KeyError异常
            results = uploader.batch_upload_videos(mixed_tasks)
            
            print(f"✅ 批量上传处理完成")
            print(f"   返回结果数: {len(results)}")
            
            # 分析结果
            success_count = sum(1 for r in results if r.get('success'))
            parameter_errors = sum(1 for r in results if r.get('error_type') == 'parameter_error')
            
            print(f"   成功任务: {success_count}")
            print(f"   参数错误: {parameter_errors}")
            
            # 检查是否正确处理了不完整任务
            if parameter_errors > 0:
                print("✅ 参数验证正常工作，不完整任务被正确识别")
                return True
            else:
                print("⚠️ 参数验证可能有问题，没有检测到不完整任务")
                return False
                
        except KeyError as e:
            print(f"❌ 仍然存在KeyError: {e}")
            return False
        except Exception as e:
            print(f"❌ 其他异常: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_md5_precompute_safety():
    """测试MD5预计算的安全性"""
    print("\n🔍 测试MD5预计算安全性...")
    
    try:
        from qianchuan_aw.workflows.batch_uploader import BatchUploader
        
        mock_settings = {
            'workflow': {'max_upload_workers': 2},
            'upload_optimization': {
                'batch_size': 5,
                'pre_compute_md5': True  # 启用MD5预计算
            },
            'api_credentials': {'app_id': 'test', 'secret': 'test'}
        }
        
        uploader = BatchUploader(mock_settings)
        
        # 创建包含无效任务的列表
        invalid_tasks = [
            {'file_path': '/test/video1.mp4'},  # 缺少其他字段
            "string_task",  # 字符串而不是字典
            None,  # None值
            {},  # 空字典
            {'other_field': 'value'}  # 没有file_path字段
        ]
        
        try:
            # 这应该不会在MD5预计算阶段崩溃
            results = uploader.batch_upload_videos(invalid_tasks)
            print("✅ MD5预计算安全处理了无效任务")
            return True
            
        except KeyError as e:
            print(f"❌ MD5预计算阶段仍有KeyError: {e}")
            return False
        except Exception as e:
            print(f"⚠️ 其他异常（可能正常）: {e}")
            return True  # 其他异常可能是正常的，比如文件不存在
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_task_validation():
    """测试任务验证逻辑"""
    print("\n🔍 测试任务验证逻辑...")
    
    try:
        from qianchuan_aw.workflows.batch_uploader import BatchUploader
        
        mock_settings = {
            'workflow': {'max_upload_workers': 1},
            'upload_optimization': {'batch_size': 1},
            'api_credentials': {'app_id': 'test', 'secret': 'test'}
        }
        
        uploader = BatchUploader(mock_settings)
        
        # 测试各种无效任务
        test_cases = [
            ({}, "空任务"),
            ({'file_path': '/test.mp4'}, "缺少local_creative_id"),
            ({'local_creative_id': 1}, "缺少file_path"),
            ({'file_path': '/test.mp4', 'local_creative_id': 1}, "缺少account_id"),
            ({'file_path': '/test.mp4', 'local_creative_id': 1, 'account_id': 1}, "缺少principal_name"),
            ("string", "字符串任务"),
            (None, "None任务")
        ]
        
        all_handled = True
        for task, description in test_cases:
            try:
                result = uploader.upload_single_video(task)
                if result.get('error_type') == 'parameter_error':
                    print(f"✅ {description}: 正确识别为参数错误")
                else:
                    print(f"⚠️ {description}: 未正确识别 - {result}")
                    all_handled = False
            except Exception as e:
                print(f"❌ {description}: 抛出异常 - {e}")
                all_handled = False
        
        return all_handled
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 千川自动化 - 紧急KeyError修复验证")
    print("📌 目标: 验证 'local_creative_id' KeyError 修复效果")
    print("="*60)
    
    tests = [
        ("不完整任务处理", test_incomplete_task_handling),
        ("MD5预计算安全性", test_md5_precompute_safety),
        ("任务验证逻辑", test_task_validation)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: 通过")
            else:
                print(f"❌ {test_name}: 失败")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {e}")
    
    print("\n" + "="*60)
    print("📊 紧急修复验证结果:")
    print(f"  ✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"  ❌ 失败测试: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有紧急修复验证通过！KeyError问题已解决")
        print("💡 建议: 可以重新测试批量上传功能")
        print("🚀 系统现在应该能够安全处理各种异常情况")
    else:
        print("\n⚠️ 部分验证失败，可能仍存在问题")
        print("💡 建议: 检查失败的测试项目")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"验证执行失败: {e}")
        sys.exit(1)
