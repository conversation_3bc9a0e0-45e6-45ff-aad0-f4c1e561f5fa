#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的素材收割工具
================

遵循收割铁律：一个素材只能被收割一次
"""

import os
import sys
import shutil
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


def proper_harvest():
    """正确的收割操作 - 遵循收割铁律"""
    logger.info("🚀 开始正确的收割操作...")
    
    today = datetime.now().strftime('%Y-%m-%d')
    approved_base_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货')
    today_dir = os.path.join(approved_base_dir, today)
    
    try:
        with database_session() as db:
            # 获取所有已收割的文件名（检查所有日期目录）
            already_harvested = set()
            
            if os.path.exists(approved_base_dir):
                for date_dir in os.listdir(approved_base_dir):
                    date_path = os.path.join(approved_base_dir, date_dir)
                    if os.path.isdir(date_path) and date_dir.startswith('2025-'):
                        try:
                            for filename in os.listdir(date_path):
                                if filename.endswith('.mp4'):
                                    already_harvested.add(filename)
                        except:
                            continue
            
            logger.info(f"📊 已收割文件数量: {len(already_harvested)}")
            
            # 查找需要收割的素材（排除已收割的）
            unharvested_materials = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.ALREADY_TESTED.value,
                LocalCreative.harvest_status == 'not_harvested',
                ~LocalCreative.filename.in_(already_harvested)  # 排除已收割的
            ).all()
            
            logger.info(f"📊 发现 {len(unharvested_materials)} 个真正需要收割的素材")
            
            if not unharvested_materials:
                logger.info("✅ 没有需要收割的素材")
                return True
            
            # 创建今天的收割目录
            os.makedirs(today_dir, exist_ok=True)
            
            # 执行收割（移动文件，不是复制）
            harvested_count = 0
            for material in unharvested_materials:
                try:
                    if not material.file_path or not os.path.exists(material.file_path):
                        logger.warning(f"⚠️ 源文件不存在: {material.filename}")
                        continue
                    
                    target_path = os.path.join(today_dir, material.filename)
                    
                    # 移动文件（不是复制！）
                    shutil.move(material.file_path, target_path)
                    
                    # 更新数据库状态
                    material.harvest_status = MaterialStatus.HARVESTED.value
                    material.file_path = target_path  # 更新文件路径
                    material.updated_at = datetime.now()
                    
                    harvested_count += 1
                    logger.info(f"✅ 收割完成: {material.filename}")
                    
                except Exception as e:
                    logger.error(f"❌ 收割失败 {material.filename}: {e}")
            
            db.commit()
            
            logger.info(f"🎯 收割操作完成！成功收割 {harvested_count} 个素材")
            return harvested_count > 0
            
    except Exception as e:
        logger.error(f"❌ 收割操作失败: {e}")
        return False


if __name__ == '__main__':
    proper_harvest()
