#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 业务铁律合规性自动监控器，确保系统严格遵循业务规则
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount, PlatformCreative
from sqlalchemy import func, and_, or_


@dataclass
class ComplianceViolation:
    """合规性违规记录"""
    rule_id: str
    rule_name: str
    severity: str  # CRITICAL, WARNING, INFO
    violation_type: str
    description: str
    affected_entities: List[str]
    detected_at: datetime
    auto_fixable: bool
    fix_suggestion: str


class BusinessRulesComplianceMonitor:
    """业务铁律合规性监控器"""
    
    def __init__(self):
        self.violations = []
        
        # 业务铁律定义
        self.business_rules = {
            'rule_1': {
                'name': '账户类型分离',
                'description': 'TEST、DELIVERY、UNSET账户类型必须严格分离使用',
                'severity': 'CRITICAL'
            },
            'rule_2': {
                'name': '测试视频全局唯一性',
                'description': 'TEST账户中的视频不能在DELIVERY账户中重复使用',
                'severity': 'CRITICAL'
            },
            'rule_3': {
                'name': '账户状态操作权限',
                'description': 'deleted账户严禁任何操作，temporarily_blocked账户限制新建操作',
                'severity': 'CRITICAL'
            },
            'rule_4': {
                'name': '防重复提审约束',
                'description': '每个计划只能提审一次，防止重复提审',
                'severity': 'CRITICAL'
            },
            'rule_6': {
                'name': '并发操作限制',
                'description': '同一素材或计划的并发操作必须通过锁机制控制',
                'severity': 'WARNING'
            },
            'rule_12': {
                'name': '计划创建数量限制',
                'description': '每个账户的计划创建数量应在合理范围内',
                'severity': 'WARNING'
            }
        }
    
    def run_compliance_check(self) -> Dict[str, Any]:
        """运行完整的合规性检查"""
        logger.info("⚖️ 开始业务铁律合规性检查...")
        
        self.violations = []
        
        try:
            with database_session() as db:
                # 检查各项业务铁律
                self._check_account_type_separation(db)
                self._check_test_video_uniqueness(db)
                self._check_account_status_permissions(db)
                self._check_duplicate_appeal_prevention(db)
                self._check_plan_creation_limits(db)
                
                # 生成合规性报告
                report = self._generate_compliance_report()
                
                # 保存违规记录
                self._save_violations()
                
                logger.info(f"✅ 业务铁律合规性检查完成: 发现{len(self.violations)}项违规")
                
                return report
                
        except Exception as e:
            logger.error(f"❌ 业务铁律合规性检查失败: {e}")
            return {
                'status': 'ERROR',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    def _check_account_type_separation(self, db):
        """检查账户类型分离 - 铁律1"""
        logger.debug("检查铁律1: 账户类型分离")
        
        try:
            # 检查是否有UNSET类型的账户参与了工作流
            unset_accounts_in_workflow = db.query(AdAccount).join(
                PlatformCreative, AdAccount.id == PlatformCreative.account_id
            ).filter(
                AdAccount.account_type == 'UNSET'
            ).distinct().all()
            
            if unset_accounts_in_workflow:
                violation = ComplianceViolation(
                    rule_id='rule_1',
                    rule_name='账户类型分离',
                    severity='CRITICAL',
                    violation_type='UNSET_ACCOUNT_IN_WORKFLOW',
                    description='UNSET类型账户不应参与工作流操作',
                    affected_entities=[acc.account_id_qc for acc in unset_accounts_in_workflow],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=False,
                    fix_suggestion='将UNSET账户的素材移除或更改账户类型'
                )
                self.violations.append(violation)
            
            # 检查账户类型为空或无效的情况
            invalid_type_accounts = db.query(AdAccount).filter(
                or_(
                    AdAccount.account_type.is_(None),
                    ~AdAccount.account_type.in_(['TEST', 'DELIVERY', 'UNSET'])
                )
            ).all()
            
            if invalid_type_accounts:
                violation = ComplianceViolation(
                    rule_id='rule_1',
                    rule_name='账户类型分离',
                    severity='CRITICAL',
                    violation_type='INVALID_ACCOUNT_TYPE',
                    description='账户类型无效或为空',
                    affected_entities=[acc.account_id_qc for acc in invalid_type_accounts],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=True,
                    fix_suggestion='设置正确的账户类型: TEST, DELIVERY, 或 UNSET'
                )
                self.violations.append(violation)
                
        except Exception as e:
            logger.error(f"检查账户类型分离失败: {e}")
    
    def _check_test_video_uniqueness(self, db):
        """检查测试视频全局唯一性 - 铁律2"""
        logger.debug("检查铁律2: 测试视频全局唯一性")
        
        try:
            # 查找在TEST和DELIVERY账户中都存在的视频
            duplicate_videos = db.query(
                LocalCreative.filename,
                func.count(func.distinct(AdAccount.account_type)).label('type_count')
            ).join(
                PlatformCreative, LocalCreative.id == PlatformCreative.local_creative_id
            ).join(
                AdAccount, PlatformCreative.account_id == AdAccount.id
            ).filter(
                AdAccount.account_type.in_(['TEST', 'DELIVERY'])
            ).group_by(
                LocalCreative.filename
            ).having(
                func.count(func.distinct(AdAccount.account_type)) > 1
            ).all()
            
            if duplicate_videos:
                violation = ComplianceViolation(
                    rule_id='rule_2',
                    rule_name='测试视频全局唯一性',
                    severity='CRITICAL',
                    violation_type='VIDEO_CROSS_ACCOUNT_TYPE',
                    description='视频在TEST和DELIVERY账户中重复使用',
                    affected_entities=[video.filename for video in duplicate_videos],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=False,
                    fix_suggestion='移除重复视频或重新分配到正确的账户类型'
                )
                self.violations.append(violation)
                
        except Exception as e:
            logger.error(f"检查测试视频全局唯一性失败: {e}")
    
    def _check_account_status_permissions(self, db):
        """检查账户状态操作权限 - 铁律3"""
        logger.debug("检查铁律3: 账户状态操作权限")
        
        try:
            # 检查deleted账户是否有活跃操作
            deleted_accounts_with_activity = db.query(AdAccount).join(
                PlatformCreative, AdAccount.id == PlatformCreative.account_id
            ).join(
                LocalCreative, PlatformCreative.local_creative_id == LocalCreative.id
            ).filter(
                AdAccount.status == 'deleted',
                LocalCreative.updated_at > datetime.now(timezone.utc) - timedelta(days=7)
            ).distinct().all()
            
            if deleted_accounts_with_activity:
                violation = ComplianceViolation(
                    rule_id='rule_3',
                    rule_name='账户状态操作权限',
                    severity='CRITICAL',
                    violation_type='DELETED_ACCOUNT_ACTIVITY',
                    description='deleted状态账户存在近期活动',
                    affected_entities=[acc.account_id_qc for acc in deleted_accounts_with_activity],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=True,
                    fix_suggestion='停止deleted账户的所有操作或恢复账户状态'
                )
                self.violations.append(violation)
                
        except Exception as e:
            logger.error(f"检查账户状态操作权限失败: {e}")
    
    def _check_duplicate_appeal_prevention(self, db):
        """检查防重复提审约束 - 铁律4"""
        logger.debug("检查铁律4: 防重复提审约束")
        
        try:
            # 查找可能的重复提审
            duplicate_appeals = db.query(Campaign).filter(
                and_(
                    Campaign.appeal_status == 'appeal_pending',
                    Campaign.first_appeal_at.isnot(None),
                    Campaign.last_appeal_at.isnot(None),
                    Campaign.first_appeal_at != Campaign.last_appeal_at
                )
            ).all()
            
            if duplicate_appeals:
                violation = ComplianceViolation(
                    rule_id='rule_4',
                    rule_name='防重复提审约束',
                    severity='CRITICAL',
                    violation_type='DUPLICATE_APPEAL_DETECTED',
                    description='检测到可能的重复提审',
                    affected_entities=[camp.campaign_id_qc for camp in duplicate_appeals],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=True,
                    fix_suggestion='清理重复提审状态，确保每个计划只提审一次'
                )
                self.violations.append(violation)
                
        except Exception as e:
            logger.error(f"检查防重复提审约束失败: {e}")
    
    def _check_plan_creation_limits(self, db):
        """检查计划创建数量限制 - 铁律12"""
        logger.debug("检查铁律12: 计划创建数量限制")
        
        try:
            # 检查每个账户的计划数量
            account_plan_counts = db.query(
                AdAccount.account_id_qc,
                AdAccount.name,
                func.count(Campaign.id).label('plan_count')
            ).join(
                Campaign, AdAccount.id == Campaign.account_id
            ).group_by(
                AdAccount.id, AdAccount.account_id_qc, AdAccount.name
            ).all()
            
            excessive_accounts = [
                acc for acc in account_plan_counts 
                if acc.plan_count > 100  # 假设100个计划为合理上限
            ]
            
            if excessive_accounts:
                violation = ComplianceViolation(
                    rule_id='rule_12',
                    rule_name='计划创建数量限制',
                    severity='WARNING',
                    violation_type='EXCESSIVE_PLAN_COUNT',
                    description='账户计划数量超过合理限制',
                    affected_entities=[f"{acc.account_id_qc}({acc.plan_count}个计划)" for acc in excessive_accounts],
                    detected_at=datetime.now(timezone.utc),
                    auto_fixable=False,
                    fix_suggestion='审查账户计划数量，清理无效或过期计划'
                )
                self.violations.append(violation)
                
        except Exception as e:
            logger.error(f"检查计划创建数量限制失败: {e}")
    
    def _generate_compliance_report(self) -> Dict[str, Any]:
        """生成合规性报告"""
        
        # 按严重程度分类违规
        critical_violations = [v for v in self.violations if v.severity == 'CRITICAL']
        warning_violations = [v for v in self.violations if v.severity == 'WARNING']
        
        # 按规则分组
        violations_by_rule = {}
        for violation in self.violations:
            rule_id = violation.rule_id
            if rule_id not in violations_by_rule:
                violations_by_rule[rule_id] = []
            violations_by_rule[rule_id].append(violation)
        
        # 计算合规分数
        total_rules = len(self.business_rules)
        violated_rules = len(violations_by_rule)
        compliance_score = max(0, int((total_rules - violated_rules) / total_rules * 100))
        
        report = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'compliance_score': compliance_score,
            'total_violations': len(self.violations),
            'critical_violations': len(critical_violations),
            'warning_violations': len(warning_violations),
            'violations_by_rule': {
                rule_id: len(violations) for rule_id, violations in violations_by_rule.items()
            },
            'compliance_status': 'COMPLIANT' if len(critical_violations) == 0 else 'NON_COMPLIANT',
            'auto_fixable_violations': len([v for v in self.violations if v.auto_fixable]),
            'detailed_violations': [asdict(v) for v in self.violations]
        }
        
        return report
    
    def _save_violations(self):
        """保存违规记录"""
        try:
            violations_dir = project_root / 'ai_reports' / 'compliance'
            violations_dir.mkdir(parents=True, exist_ok=True)
            
            violations_file = violations_dir / f'violations_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            
            violations_data = [asdict(v) for v in self.violations]
            
            # 转换datetime为字符串
            for violation in violations_data:
                if isinstance(violation['detected_at'], datetime):
                    violation['detected_at'] = violation['detected_at'].isoformat()
            
            with open(violations_file, 'w', encoding='utf-8') as f:
                json.dump(violations_data, f, indent=2, ensure_ascii=False, default=str)
                
            logger.info(f"违规记录已保存到: {violations_file}")
            
        except Exception as e:
            logger.error(f"保存违规记录失败: {e}")
    
    def get_compliance_summary(self) -> Dict[str, Any]:
        """获取合规性摘要"""
        
        with database_session() as db:
            # 基础统计
            total_accounts = db.query(AdAccount).count()
            total_campaigns = db.query(Campaign).count()
            total_creatives = db.query(LocalCreative).count()
            
            # 账户类型分布
            account_type_dist = db.query(
                AdAccount.account_type,
                func.count(AdAccount.id).label('count')
            ).group_by(AdAccount.account_type).all()
            
            # 账户状态分布
            account_status_dist = db.query(
                AdAccount.status,
                func.count(AdAccount.id).label('count')
            ).group_by(AdAccount.status).all()
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'system_overview': {
                    'total_accounts': total_accounts,
                    'total_campaigns': total_campaigns,
                    'total_creatives': total_creatives
                },
                'account_type_distribution': {
                    acc_type.account_type: acc_type.count 
                    for acc_type in account_type_dist
                },
                'account_status_distribution': {
                    acc_status.status: acc_status.count 
                    for acc_status in account_status_dist
                },
                'business_rules_count': len(self.business_rules)
            }


def main():
    """主函数"""
    monitor = BusinessRulesComplianceMonitor()
    
    import argparse
    parser = argparse.ArgumentParser(description='业务铁律合规性监控器')
    parser.add_argument('--check', action='store_true', help='运行合规性检查')
    parser.add_argument('--summary', action='store_true', help='获取合规性摘要')
    
    args = parser.parse_args()
    
    if args.check:
        report = monitor.run_compliance_check()
        print(json.dumps(report, indent=2, ensure_ascii=False, default=str))
    elif args.summary:
        summary = monitor.get_compliance_summary()
        print(json.dumps(summary, indent=2, ensure_ascii=False, default=str))
    else:
        print("使用 --check 运行合规性检查或 --summary 获取摘要")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
