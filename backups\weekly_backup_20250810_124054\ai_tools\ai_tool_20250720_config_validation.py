#!/usr/bin/env python3
"""
千川配置文件验证脚本
验证settings.yml配置修改后系统能正常运行
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from pathlib import Path

def validate_config():
    """验证配置文件"""
    print("🔍 千川配置文件验证")
    print("=" * 50)
    
    config_path = Path("config/settings.yml")
    
    try:
        # 1. 验证YAML语法
        print("\n📋 验证YAML语法...")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ YAML语法正确")
        
        # 2. 验证关键配置项
        print("\n⚙️ 验证关键配置项...")
        
        # 检查工作流配置
        workflow = config.get('workflow', {})
        
        # 验证计划创建配置
        plan_creation = workflow.get('plan_creation', {})
        if plan_creation.get('enabled'):
            interval = plan_creation.get('interval_seconds')
            print(f"  计划创建调度: {interval}秒 {'✅' if interval == 60 else '❌'}")
            if interval == 60:
                print("    ✅ 调度频率已优化为60秒")
            else:
                print(f"    ❌ 期望60秒，实际{interval}秒")
        
        # 验证其他关键配置
        configs_to_check = [
            ('file_ingestion', 'interval_seconds', '文件摄取'),
            ('group_dispatch', 'interval_seconds', '分组派发'),
            ('plan_appeal', 'interval_seconds', '计划申诉'),
            ('material_monitoring', 'interval_seconds', '素材监控')
        ]
        
        for config_name, key, desc in configs_to_check:
            config_section = workflow.get(config_name, {})
            if config_section.get('enabled'):
                value = config_section.get(key)
                print(f"  {desc}调度: {value}秒 ✅")
        
        # 3. 验证配置合理性
        print("\n🎯 验证配置合理性...")
        
        # 检查调度频率是否合理
        plan_interval = workflow.get('plan_creation', {}).get('interval_seconds', 0)
        group_interval = workflow.get('group_dispatch', {}).get('interval_seconds', 0)
        
        if plan_interval > 0 and group_interval > 0:
            if plan_interval <= group_interval:
                print("  ✅ 计划创建频率 ≤ 分组派发频率，配置合理")
            else:
                print("  ⚠️ 计划创建频率 > 分组派发频率，可能导致积压")
        
        # 4. 验证路径配置
        print("\n📁 验证路径配置...")
        
        material_collection = workflow.get('material_collection', {})
        dest_dir = material_collection.get('dest_dir')
        if dest_dir:
            if os.path.exists(dest_dir):
                print(f"  ✅ 素材目录存在: {dest_dir}")
            else:
                print(f"  ⚠️ 素材目录不存在: {dest_dir}")
        
        # 5. 生成配置摘要
        print("\n📊 配置摘要:")
        print(f"  - 计划创建调度: {plan_interval}秒")
        print(f"  - 分组派发调度: {group_interval}秒") 
        print(f"  - 素材监控调度: {workflow.get('material_monitoring', {}).get('interval_seconds')}秒")
        print(f"  - 计划申诉调度: {workflow.get('plan_appeal', {}).get('interval_seconds')}秒")
        
        print(f"\n🎉 配置验证完成!")
        print(f"✅ 配置文件语法正确")
        print(f"✅ 关键配置项完整")
        print(f"✅ 调度频率优化生效")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML语法错误: {e}")
        return False
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🧪 测试配置加载...")

    try:
        # 尝试导入配置加载模块
        from qianchuan_aw.utils.config_loader import load_settings

        config = load_settings()
        workflow_settings = config.get('workflow', {})

        plan_creation_interval = workflow_settings.get('plan_creation', {}).get('interval_seconds')

        print(f"  ✅ 配置加载成功")
        print(f"  ✅ 计划创建间隔: {plan_creation_interval}秒")

        if plan_creation_interval == 60:
            print(f"  ✅ 优化配置已生效")
        else:
            print(f"  ⚠️ 配置可能未生效，期望60秒，实际{plan_creation_interval}秒")

        # 测试Celery配置加载
        try:
            from qianchuan_aw.celery_app import plan_creation_interval as celery_interval
            print(f"  ✅ Celery配置间隔: {celery_interval}秒")
        except Exception as e:
            print(f"  ⚠️ Celery配置加载失败: {e}")

        return True

    except ImportError as e:
        print(f"  ⚠️ 无法导入配置模块: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")
        return False

if __name__ == "__main__":
    success = validate_config()
    if success:
        test_config_loading()
    
    print(f"\n{'='*50}")
    print(f"验证结果: {'✅ 通过' if success else '❌ 失败'}")
