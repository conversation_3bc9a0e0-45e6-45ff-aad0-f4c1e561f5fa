#!/usr/bin/env python3
"""
千川素材状态修复验证脚本
验证"其他状态"修复效果和状态中文化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text
import pandas as pd

def verify_status_fix():
    """验证状态修复效果"""
    print("🔍 千川素材状态修复验证")
    print("=" * 50)
    
    with database_session() as db:
        # 1. 查询TEST账户的状态分布
        print("\n📊 TEST账户素材状态分布:")
        status_query = text("""
            SELECT 
                lc.status,
                COUNT(*) as count,
                COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
            GROUP BY lc.status 
            ORDER BY count DESC
        """)
        
        status_results = db.execute(status_query).fetchall()
        
        # 状态中文映射
        status_map = {
            MaterialStatus.NEW.value: '新建', MaterialStatus.PENDING_UPLOAD.value: '待上传', MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划',
            'creating_plan': '创建计划中', MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核',
            MaterialStatus.APPROVED.value: '审核通过', MaterialStatus.REJECTED.value: '审核拒绝', MaterialStatus.UPLOAD_FAILED.value: '上传失败',
            MaterialStatus.PENDING_GROUPING.value: '待分组', MaterialStatus.PROCESSING.value: '处理中', MaterialStatus.ALREADY_TESTED.value: '已测试'
        }
        
        total_count = 0
        creating_plan_count = 0
        
        for row in status_results:
            status_cn = status_map.get(row.status, f"未知状态({row.status})")
            print(f"  {status_cn}: {row.count} ({row.percentage:.1f}%)")
            total_count += row.count
            
            if row.status == 'creating_plan':
                creating_plan_count = row.count
        
        print(f"\n📈 总计: {total_count} 个TEST账户素材")
        
        # 2. 验证"其他状态"的构成
        print(f"\n🎯 关键发现:")
        print(f"  - 创建计划中状态: {creating_plan_count} 个")
        print(f"  - 这就是之前显示为'其他状态'的素材！")
        
        # 3. 查询真正的"其他状态"
        other_query = text("""
            SELECT 
                lc.status,
                COUNT(*) as count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status NOT IN (MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, MaterialStatus.PENDING_GROUPING.value, MaterialStatus.PROCESSING.value,
                                     MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', MaterialStatus.TESTING_PENDING_REVIEW.value,
                                     MaterialStatus.UPLOAD_FAILED.value, MaterialStatus.ALREADY_TESTED.value, MaterialStatus.NEW.value, MaterialStatus.PENDING_UPLOAD.value)
            GROUP BY lc.status
            ORDER BY count DESC
        """)
        
        other_results = db.execute(other_query).fetchall()
        
        print(f"\n🔍 真正的其他状态:")
        if other_results:
            for row in other_results:
                status_cn = status_map.get(row.status, f"未知状态({row.status})")
                print(f"  {status_cn}: {row.count}")
        else:
            print("  ✅ 无其他未知状态")
        
        # 4. 验证代朋飞的状态分布
        print(f"\n👤 代朋飞状态分布验证:")
        author_query = text("""
            SELECT
                COUNT(*) as total_count,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN lc.status = MaterialStatus.REJECTED.value THEN 1 ELSE 0 END) as rejected_count,
                SUM(CASE WHEN lc.status = 'creating_plan' THEN 1 ELSE 0 END) as creating_plan_count,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 1 ELSE 0 END) as uploaded_pending_plan_count,
                SUM(CASE WHEN lc.status = MaterialStatus.TESTING_PENDING_REVIEW.value THEN 1 ELSE 0 END) as testing_pending_review_count,
                SUM(CASE WHEN lc.status = MaterialStatus.ALREADY_TESTED.value THEN 1 ELSE 0 END) as already_tested_count,
                SUM(CASE WHEN lc.status NOT IN (MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, 'creating_plan', MaterialStatus.UPLOADED_PENDING_PLAN.value,
                                               MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.ALREADY_TESTED.value) THEN 1 ELSE 0 END) as other_count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND (lc.filename ~ '^[0-9]+\\.[0-9]+-代朋飞-' OR lc.filename ~ '-代朋飞-[0-9]+\\.mp4')
        """)
        
        author_result = db.execute(author_query).fetchone()
        
        if author_result:
            print(f"  总素材: {author_result.total_count}")
            print(f"  审核通过: {author_result.approved_count}")
            print(f"  审核拒绝: {author_result.rejected_count}")
            print(f"  创建计划中: {author_result.creating_plan_count}")
            print(f"  已上传待建计划: {author_result.uploaded_pending_plan_count}")
            print(f"  测试待审核: {author_result.testing_pending_review_count}")
            print(f"  已测试: {author_result.already_tested_count}")
            print(f"  其他状态: {author_result.other_count}")
            
            # 验证总数
            calculated_total = (author_result.approved_count + author_result.rejected_count +
                              author_result.creating_plan_count + author_result.uploaded_pending_plan_count +
                              author_result.testing_pending_review_count + author_result.already_tested_count +
                              author_result.other_count)
            
            if calculated_total == author_result.total_count:
                print(f"  ✅ 状态统计一致: {calculated_total} = {author_result.total_count}")
            else:
                print(f"  ❌ 状态统计不一致: {calculated_total} ≠ {author_result.total_count}")
        
        print(f"\n🎉 修复验证完成!")
        print(f"✅ 'creating_plan'状态已从'其他状态'中分离")
        print(f"✅ 状态中文映射已完善")
        print(f"✅ 数据统计保持一致")

if __name__ == "__main__":
    verify_status_fix()
