#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 简单同步有头浏览器提审验证
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def get_latest_test_plan():
    """获取最新的测试计划"""
    logger.info("🔍 查找最新的测试计划...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 查找今天创建的最新计划
        cursor.execute("""
            SELECT 
                c.campaign_id_qc,
                c.status,
                c.appeal_status,
                c.appeal_error_message,
                c.created_at,
                a.account_id_qc,
                p.name as principal_name
            FROM campaigns c
            JOIN ad_accounts a ON c.account_id = a.id
            JOIN principals p ON a.principal_id = p.id
            WHERE c.created_at >= CURRENT_DATE
            AND c.status = 'AUDITING'
            ORDER BY c.created_at DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            campaign_id_qc, status, appeal_status, appeal_error_message, created_at, account_id_qc, principal_name = result
            
            plan_info = {
                'campaign_id': campaign_id_qc,
                'status': status,
                'appeal_status': appeal_status,
                'appeal_error_message': appeal_error_message,
                'created_at': created_at,
                'account_id': account_id_qc,
                'principal_name': principal_name
            }
            
            logger.success(f"✅ 找到最新测试计划: {campaign_id_qc}")
            logger.info(f"   👤 主体: {principal_name}")
            logger.info(f"   🏢 账户: {account_id_qc}")
            logger.info(f"   📊 状态: {status}")
            logger.info(f"   📤 提审状态: {appeal_status}")
            logger.info(f"   📅 创建时间: {created_at}")
            
            return plan_info
        else:
            logger.error("❌ 没有找到今天创建的AUDITING状态计划")
            return None
            
    except Exception as e:
        logger.error(f"❌ 查找最新测试计划失败: {e}")
        return None

def reset_plan_for_visual_test(campaign_id):
    """重置计划状态用于可视化测试"""
    logger.info(f"🔄 重置计划状态用于可视化测试: {campaign_id}")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 重置提审状态
        cursor.execute("""
            UPDATE campaigns 
            SET appeal_status = NULL,
                appeal_error_message = NULL,
                first_appeal_at = NULL
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        affected_rows = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        if affected_rows > 0:
            logger.success(f"✅ 计划状态已重置: {campaign_id}")
            return True
        else:
            logger.warning(f"⚠️ 没有找到需要重置的计划: {campaign_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 重置计划状态失败: {e}")
        return False

def run_simple_visual_appeal_test(plan_info):
    """运行简单可视化提审测试"""
    logger.info("🎯 开始简单可视化提审测试...")
    logger.info("="*80)
    logger.info("🖥️ 使用同步SimpleCopilotSession，浏览器将以有头模式启动")
    logger.info("="*80)
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.copilot_service import SimpleCopilotSession
        
        # 加载配置
        app_settings = load_config()
        
        logger.info(f"🎯 开始对计划 {plan_info['campaign_id']} 进行可视化提审")
        logger.info(f"   👤 主体: {plan_info['principal_name']}")
        logger.info(f"   🏢 账户: {plan_info['account_id']}")
        
        logger.info("\n🖥️ 浏览器即将启动，请观察以下过程:")
        logger.info("   1. Chrome浏览器窗口打开（有头模式）")
        logger.info("   2. 自动导航到千川平台")
        logger.info("   3. 使用cookies自动登录")
        logger.info("   4. 打开智投星聊天界面")
        logger.info("   5. 发送申诉指令文字")
        logger.info("   6. 等待智投星AI回复")
        logger.info("   7. 处理申诉表单（如需要）")
        logger.info("   8. 完成提审流程")
        
        logger.info("\n⏳ 开始执行提审，请观察浏览器窗口...")
        
        # 使用SimpleCopilotSession进行提审
        with SimpleCopilotSession(
            plan_info['principal_name'], 
            plan_info['account_id'], 
            app_settings
        ) as session:
            
            logger.info("✅ 浏览器会话已创建，开始提审...")
            
            # 执行提审
            success, result = session.appeal_via_text_command(plan_info['campaign_id'])
            
            logger.info(f"\n📊 提审结果:")
            logger.info(f"   🎯 成功: {success}")
            logger.info(f"   📝 结果: {result}")
            
            if success:
                logger.success(f"✅ 简单可视化提审成功: {plan_info['campaign_id']}")
                logger.info("\n🎉 您应该已经看到了:")
                logger.info("   ✅ 浏览器自动打开千川平台")
                logger.info("   ✅ 智投星聊天界面正常工作")
                logger.info("   ✅ 文字指令成功发送")
                logger.info("   ✅ AI回复正常接收")
                logger.info("   ✅ 提审流程完整执行")
                
                return True, result
            else:
                logger.error(f"❌ 简单可视化提审失败: {result}")
                logger.info("\n🔍 请检查浏览器窗口中的具体错误:")
                logger.info("   - 是否成功登录千川平台？")
                logger.info("   - 智投星界面是否正常打开？")
                logger.info("   - 文字指令是否成功发送？")
                logger.info("   - 是否收到AI回复？")
                
                return False, result
            
    except Exception as e:
        logger.error(f"❌ 简单可视化提审测试失败: {e}")
        return False, str(e)

def check_final_status(campaign_id, wait_seconds=10):
    """检查最终状态"""
    logger.info(f"🔍 检查提审后状态: {campaign_id}")
    
    # 等待一段时间让数据库更新
    logger.info(f"⏳ 等待 {wait_seconds} 秒...")
    time.sleep(wait_seconds)
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                status,
                appeal_status,
                appeal_error_message,
                first_appeal_at
            FROM campaigns 
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            status, appeal_status, appeal_error_message, first_appeal_at = result
            
            logger.info("📊 提审后状态:")
            logger.info(f"   📋 计划状态: {status}")
            logger.info(f"   📤 提审状态: {appeal_status}")
            logger.info(f"   📅 提审时间: {first_appeal_at}")
            
            if appeal_error_message:
                logger.error(f"   ❌ 错误信息: {appeal_error_message}")
                return False, appeal_error_message
            elif appeal_status == 'appeal_pending':
                logger.success("✅ 提审状态正常：appeal_pending")
                return True, "提审成功，状态已更新"
            elif appeal_status is None:
                logger.warning("⚠️ 提审状态未更新，可能仍在处理中")
                return False, "状态未更新"
            else:
                logger.info(f"📋 当前提审状态: {appeal_status}")
                return True, f"状态: {appeal_status}"
        else:
            logger.error(f"❌ 未找到计划: {campaign_id}")
            return False, "计划不存在"
            
    except Exception as e:
        logger.error(f"❌ 检查状态失败: {e}")
        return False, str(e)

def main():
    """主函数"""
    logger.info("🎯 开始简单有头浏览器可视化提审验证")
    logger.info("="*80)
    logger.info("🖥️ 此测试使用同步SimpleCopilotSession，避免异步对象管理问题")
    logger.info("="*80)
    
    try:
        # 1. 获取最新测试计划
        plan_info = get_latest_test_plan()
        if not plan_info:
            logger.error("❌ 无法获取测试计划")
            return False
        
        # 2. 重置计划状态
        if not reset_plan_for_visual_test(plan_info['campaign_id']):
            logger.error("❌ 无法重置计划状态")
            return False
        
        # 3. 运行简单可视化提审测试
        test_success, test_message = run_simple_visual_appeal_test(plan_info)
        
        # 4. 检查最终状态
        status_success, status_message = check_final_status(plan_info['campaign_id'])
        
        # 生成测试报告
        logger.info("\n" + "="*80)
        logger.info("🎯 简单可视化提审验证结果")
        logger.info("="*80)
        
        logger.info(f"📋 测试计划: ✅ {plan_info['campaign_id']}")
        logger.info(f"🔄 状态重置: ✅ 成功")
        logger.info(f"🎯 可视化提审: {'✅' if test_success else '❌'} {test_message}")
        logger.info(f"📊 最终状态: {'✅' if status_success else '❌'} {status_message}")
        
        overall_success = test_success and status_success
        
        if overall_success:
            logger.success("\n🎉 简单可视化提审验证完全成功！")
            logger.info("\n📋 验证总结:")
            logger.info("✅ 同步浏览器自动化正常工作")
            logger.info("✅ 千川平台自动登录成功")
            logger.info("✅ 智投星聊天界面正常")
            logger.info("✅ 文字指令提审功能正常")
            logger.info("✅ 计划状态正确更新")
            logger.info("✅ 完整的提审流程可视化验证成功")
            
            logger.info("\n🎯 这证明了:")
            logger.info("- Playwright浏览器自动化完全正常")
            logger.info("- Cookies自动登录机制工作正常")
            logger.info("- 智投星文字指令提审功能正常")
            logger.info("- 同步版本的提审模块完全可用")
            logger.info("- 千川自动化项目提审模块核心功能正常")
            
        else:
            logger.error("\n❌ 简单可视化提审验证失败")
            logger.info(f"\n📋 失败原因:")
            if not test_success:
                logger.info(f"   ❌ 提审执行失败: {test_message}")
            if not status_success:
                logger.info(f"   ❌ 状态更新失败: {status_message}")
        
        return overall_success
        
    except Exception as e:
        logger.error(f"❌ 简单可视化验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 千川自动化项目提审模块简单可视化验证成功！")
        logger.info("💡 您已经亲眼看到了完整的浏览器自动化提审过程")
        logger.info("💡 同步版本的提审功能完全正常，异步版本需要进一步调试")
    else:
        logger.error("\n❌ 简单可视化验证失败，请检查具体错误信息")
    
    sys.exit(0 if success else 1)
