#!/usr/bin/env python3
"""
千川工作流目标时间段日志分析
分析2025-07-23 10:05:00到当前时间的日志
重点检查灵活分组功能和系统稳定性
"""

import sys
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class TargetedLogAnalyzer:
    """目标时间段日志分析器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流目标时间段日志分析")
        logger.critical("=" * 60)
        self.analysis_start_time = datetime(2025, 7, 23, 10, 5, 0)
        self.current_time = datetime.now()
        self.log_file = Path("logs/app_2025-07-23.log")
        
        # 分析结果存储
        self.analysis_results = {
            'flexible_grouping_verification': {},
            'system_stability_issues': {},
            'workflow_logic_defects': {},
            'code_design_problems': {},
            'overall_health': {}
        }
    
    def read_target_timerange_logs(self):
        """读取目标时间范围内的日志"""
        logger.critical("📊 读取目标时间范围内的日志")
        logger.critical("=" * 60)
        
        if not self.log_file.exists():
            logger.error(f"❌ 日志文件不存在: {self.log_file}")
            return []
        
        target_logs = []
        
        try:
            # 尝试多种编码方式
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            content = None

            for encoding in encodings:
                try:
                    with open(self.log_file, 'r', encoding=encoding) as f:
                        content = f.readlines()
                    logger.critical(f"  使用编码: {encoding}")
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                logger.error("❌ 无法解码日志文件")
                return []

            for line in content:
                    # 提取时间戳
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        try:
                            log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if self.analysis_start_time <= log_time <= self.current_time:
                                target_logs.append((log_time, line.strip()))
                        except ValueError:
                            continue
            
            logger.critical(f"📊 日志读取统计:")
            logger.critical(f"  分析时间范围: {self.analysis_start_time.strftime('%H:%M:%S')} - {self.current_time.strftime('%H:%M:%S')}")
            logger.critical(f"  目标日志行数: {len(target_logs)}")
            
            return target_logs
            
        except Exception as e:
            logger.error(f"❌ 读取日志失败: {e}")
            return []
    
    def verify_flexible_grouping_function(self, log_lines):
        """1. 验证灵活分组功能"""
        logger.critical("\n🔍 1. 灵活分组功能验证")
        logger.critical("=" * 60)
        
        grouping_events = {
            'flexible_grouping_triggers': [],
            'timeout_triggers': [],
            'force_create_triggers': [],
            'six_video_groups': [],
            'grouping_errors': []
        }
        
        for log_time, line in log_lines:
            # 检查灵活分组触发
            if '灵活分组' in line:
                grouping_events['flexible_grouping_triggers'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 检查超时触发
            if '超时' in line and ('小时' in line or 'timeout' in line.lower()):
                grouping_events['timeout_triggers'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 检查强制创建
            if '强制创建' in line or 'force_create' in line.lower():
                grouping_events['force_create_triggers'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 检查6个视频的分组
            if re.search(r'[6-8]\s*个.*?视频.*?创建', line) or re.search(r'[6-8]\s*个.*?素材.*?创建', line):
                grouping_events['six_video_groups'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 检查分组相关错误
            if ('ERROR' in line or 'CRITICAL' in line) and ('分组' in line or 'group' in line.lower()):
                grouping_events['grouping_errors'].append({
                    'time': log_time,
                    'line': line
                })
        
        logger.critical("📊 灵活分组功能验证结果:")
        logger.critical(f"  灵活分组触发: {len(grouping_events['flexible_grouping_triggers'])} 次")
        logger.critical(f"  超时触发: {len(grouping_events['timeout_triggers'])} 次")
        logger.critical(f"  强制创建触发: {len(grouping_events['force_create_triggers'])} 次")
        logger.critical(f"  6-8个视频分组: {len(grouping_events['six_video_groups'])} 次")
        logger.critical(f"  分组错误: {len(grouping_events['grouping_errors'])} 次")
        
        # 显示具体事件
        if grouping_events['flexible_grouping_triggers']:
            logger.critical("\n  灵活分组触发事件:")
            for event in grouping_events['flexible_grouping_triggers'][:3]:
                logger.critical(f"    {event['time'].strftime('%H:%M:%S')}: {event['line'][:100]}...")
        
        if grouping_events['timeout_triggers']:
            logger.critical("\n  超时触发事件:")
            for event in grouping_events['timeout_triggers'][:3]:
                logger.critical(f"    {event['time'].strftime('%H:%M:%S')}: {event['line'][:100]}...")
        
        if grouping_events['force_create_triggers']:
            logger.critical("\n  强制创建事件:")
            for event in grouping_events['force_create_triggers'][:3]:
                logger.critical(f"    {event['time'].strftime('%H:%M:%S')}: {event['line'][:100]}...")
        
        if grouping_events['grouping_errors']:
            logger.critical("\n  ❌ 分组错误:")
            for error in grouping_events['grouping_errors']:
                logger.critical(f"    {error['time'].strftime('%H:%M:%S')}: {error['line'][:100]}...")
        
        # 评估灵活分组功能状态
        function_status = 'working'
        if grouping_events['grouping_errors']:
            function_status = 'error'
        elif not grouping_events['flexible_grouping_triggers'] and not grouping_events['timeout_triggers']:
            function_status = 'not_triggered'
        
        self.analysis_results['flexible_grouping_verification'] = {
            'events': grouping_events,
            'status': function_status,
            'effectiveness_score': self.calculate_grouping_effectiveness(grouping_events)
        }
        
        return grouping_events
    
    def calculate_grouping_effectiveness(self, events):
        """计算分组功能有效性评分"""
        score = 100
        
        # 如果有错误，扣分
        if events['grouping_errors']:
            score -= len(events['grouping_errors']) * 20
        
        # 如果有超时触发，说明功能正常工作
        if events['timeout_triggers']:
            score += 10
        
        # 如果有强制创建，说明功能正常工作
        if events['force_create_triggers']:
            score += 10
        
        # 如果有6-8个视频的分组，说明灵活分组生效
        if events['six_video_groups']:
            score += 20
        
        return max(0, min(100, score))
    
    def analyze_system_stability_issues(self, log_lines):
        """2. 分析系统稳定性问题"""
        logger.critical("\n🔍 2. 系统稳定性问题分析")
        logger.critical("=" * 60)
        
        stability_issues = {
            'import_errors': [],
            'function_undefined_errors': [],
            'task_failures': [],
            'api_rate_limit_errors': [],
            'timeout_errors': [],
            'critical_errors': []
        }
        
        for log_time, line in log_lines:
            # 导入错误
            if 'ImportError' in line or 'ModuleNotFoundError' in line:
                stability_issues['import_errors'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 函数未定义错误
            if 'is not defined' in line or 'NameError' in line:
                stability_issues['function_undefined_errors'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 任务失败
            if 'Task Failed' in line or '任务失败' in line:
                stability_issues['task_failures'].append({
                    'time': log_time,
                    'line': line
                })
            
            # API频率限制
            if 'Too many requests' in line or '40003' in line or '频率限制' in line:
                stability_issues['api_rate_limit_errors'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 超时错误
            if 'timeout' in line.lower() and ('ERROR' in line or 'WARNING' in line):
                stability_issues['timeout_errors'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 严重错误
            if 'CRITICAL' in line:
                stability_issues['critical_errors'].append({
                    'time': log_time,
                    'line': line
                })
        
        logger.critical("📊 系统稳定性问题统计:")
        logger.critical(f"  导入错误: {len(stability_issues['import_errors'])} 次")
        logger.critical(f"  函数未定义错误: {len(stability_issues['function_undefined_errors'])} 次")
        logger.critical(f"  任务失败: {len(stability_issues['task_failures'])} 次")
        logger.critical(f"  API频率限制: {len(stability_issues['api_rate_limit_errors'])} 次")
        logger.critical(f"  超时错误: {len(stability_issues['timeout_errors'])} 次")
        logger.critical(f"  严重错误: {len(stability_issues['critical_errors'])} 次")
        
        # 显示最严重的问题
        if stability_issues['critical_errors']:
            logger.critical("\n  🚨 严重错误详情:")
            for error in stability_issues['critical_errors'][:3]:
                logger.critical(f"    {error['time'].strftime('%H:%M:%S')}: {error['line'][:100]}...")
        
        if stability_issues['function_undefined_errors']:
            logger.critical("\n  ❌ 函数未定义错误:")
            for error in stability_issues['function_undefined_errors'][:3]:
                logger.critical(f"    {error['time'].strftime('%H:%M:%S')}: {error['line'][:100]}...")
        
        if stability_issues['import_errors']:
            logger.critical("\n  ❌ 导入错误:")
            for error in stability_issues['import_errors'][:3]:
                logger.critical(f"    {error['time'].strftime('%H:%M:%S')}: {error['line'][:100]}...")
        
        # 计算稳定性评分
        total_errors = sum(len(issues) for issues in stability_issues.values())
        stability_score = max(0, 100 - total_errors * 5)
        
        self.analysis_results['system_stability_issues'] = {
            'issues': stability_issues,
            'total_errors': total_errors,
            'stability_score': stability_score
        }
        
        return stability_issues
    
    def analyze_workflow_logic_defects(self, log_lines):
        """3. 分析工作流逻辑缺陷"""
        logger.critical("\n🔍 3. 工作流逻辑缺陷分析")
        logger.critical("=" * 60)
        
        logic_defects = {
            'status_transition_errors': [],
            'deadlock_indicators': [],
            'duplicate_check_failures': [],
            'state_inconsistencies': []
        }
        
        # 状态流转统计
        status_transitions = defaultdict(int)
        
        for log_time, line in log_lines:
            # 状态流转错误
            if '状态' in line and ('错误' in line or 'ERROR' in line):
                logic_defects['status_transition_errors'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 死锁指标
            if '阻塞' in line or '卡住' in line or 'blocked' in line.lower():
                logic_defects['deadlock_indicators'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 重复检查失败
            if '重复检查' in line and ('失败' in line or 'ERROR' in line):
                logic_defects['duplicate_check_failures'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 状态不一致
            if '不一致' in line or 'inconsistent' in line.lower():
                logic_defects['state_inconsistencies'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 统计状态流转
            status_match = re.search(r'状态.*?从\s*(\w+)\s*变更为\s*(\w+)|(\w+)\s*→\s*(\w+)', line)
            if status_match:
                if status_match.group(1) and status_match.group(2):
                    transition = f"{status_match.group(1)} → {status_match.group(2)}"
                elif status_match.group(3) and status_match.group(4):
                    transition = f"{status_match.group(3)} → {status_match.group(4)}"
                else:
                    continue
                status_transitions[transition] += 1
        
        logger.critical("📊 工作流逻辑缺陷统计:")
        logger.critical(f"  状态流转错误: {len(logic_defects['status_transition_errors'])} 次")
        logger.critical(f"  死锁指标: {len(logic_defects['deadlock_indicators'])} 次")
        logger.critical(f"  重复检查失败: {len(logic_defects['duplicate_check_failures'])} 次")
        logger.critical(f"  状态不一致: {len(logic_defects['state_inconsistencies'])} 次")
        
        if status_transitions:
            logger.critical("\n  📊 状态流转统计:")
            for transition, count in sorted(status_transitions.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.critical(f"    {transition}: {count} 次")
        
        # 显示逻辑缺陷详情
        for defect_type, defects in logic_defects.items():
            if defects:
                logger.critical(f"\n  ⚠️ {defect_type}:")
                for defect in defects[:2]:
                    logger.critical(f"    {defect['time'].strftime('%H:%M:%S')}: {defect['line'][:100]}...")
        
        self.analysis_results['workflow_logic_defects'] = {
            'defects': logic_defects,
            'status_transitions': dict(status_transitions),
            'logic_health_score': max(0, 100 - sum(len(defects) for defects in logic_defects.values()) * 10)
        }
        
        return logic_defects
    
    def analyze_code_design_problems(self, log_lines):
        """4. 分析代码设计问题"""
        logger.critical("\n🔍 4. 代码设计问题分析")
        logger.critical("=" * 60)
        
        design_problems = {
            'hardcoded_values': [],
            'performance_bottlenecks': [],
            'error_handling_gaps': [],
            'resource_leaks': []
        }
        
        for log_time, line in log_lines:
            # 硬编码值
            if re.search(r'硬编码|hard.*?code', line, re.IGNORECASE):
                design_problems['hardcoded_values'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 性能瓶颈
            if any(keyword in line for keyword in ['耗时', '缓慢', 'slow', '性能']):
                if 'WARNING' in line or 'ERROR' in line:
                    design_problems['performance_bottlenecks'].append({
                        'time': log_time,
                        'line': line
                    })
            
            # 错误处理缺失
            if 'Exception' in line and 'unhandled' in line.lower():
                design_problems['error_handling_gaps'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 资源泄露
            if any(keyword in line for keyword in ['内存', '连接', 'memory', 'connection']) and 'leak' in line.lower():
                design_problems['resource_leaks'].append({
                    'time': log_time,
                    'line': line
                })
        
        logger.critical("📊 代码设计问题统计:")
        logger.critical(f"  硬编码值: {len(design_problems['hardcoded_values'])} 次")
        logger.critical(f"  性能瓶颈: {len(design_problems['performance_bottlenecks'])} 次")
        logger.critical(f"  错误处理缺失: {len(design_problems['error_handling_gaps'])} 次")
        logger.critical(f"  资源泄露: {len(design_problems['resource_leaks'])} 次")
        
        # 显示设计问题详情
        for problem_type, problems in design_problems.items():
            if problems:
                logger.critical(f"\n  ⚠️ {problem_type}:")
                for problem in problems[:2]:
                    logger.critical(f"    {problem['time'].strftime('%H:%M:%S')}: {problem['line'][:100]}...")
        
        self.analysis_results['code_design_problems'] = {
            'problems': design_problems,
            'design_health_score': max(0, 100 - sum(len(problems) for problems in design_problems.values()) * 15)
        }
        
        return design_problems
    
    def generate_fix_recommendations(self):
        """生成修复建议"""
        logger.critical("\n💡 修复建议")
        logger.critical("=" * 60)
        
        recommendations = []
        
        # 基于灵活分组验证的建议
        grouping_result = self.analysis_results['flexible_grouping_verification']
        if grouping_result['status'] == 'error':
            recommendations.append({
                'priority': 'CRITICAL',
                'category': '灵活分组功能',
                'issue': '灵活分组功能存在错误',
                'solution': '检查并修复分组逻辑错误',
                'urgency': 'immediate'
            })
        elif grouping_result['status'] == 'not_triggered':
            recommendations.append({
                'priority': 'HIGH',
                'category': '灵活分组功能',
                'issue': '灵活分组功能未触发',
                'solution': '检查触发条件和配置参数',
                'urgency': 'within_24h'
            })
        
        # 基于系统稳定性的建议
        stability_result = self.analysis_results['system_stability_issues']
        if stability_result['stability_score'] < 80:
            recommendations.append({
                'priority': 'HIGH',
                'category': '系统稳定性',
                'issue': f"系统稳定性评分低: {stability_result['stability_score']}/100",
                'solution': '修复导入错误和函数未定义问题',
                'urgency': 'within_24h'
            })
        
        # 基于工作流逻辑的建议
        logic_result = self.analysis_results['workflow_logic_defects']
        if logic_result['logic_health_score'] < 70:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': '工作流逻辑',
                'issue': f"工作流逻辑健康度低: {logic_result['logic_health_score']}/100",
                'solution': '优化状态流转和重复检查逻辑',
                'urgency': 'within_week'
            })
        
        logger.critical("🎯 修复建议 (按优先级排序):")
        for i, rec in enumerate(recommendations, 1):
            logger.critical(f"\n  {i}. [{rec['priority']}] {rec['category']}")
            logger.critical(f"     问题: {rec['issue']}")
            logger.critical(f"     解决方案: {rec['solution']}")
            logger.critical(f"     紧急程度: {rec['urgency']}")
        
        if not recommendations:
            logger.critical("  ✅ 未发现需要立即修复的问题")
        
        return recommendations
    
    def calculate_overall_health_score(self):
        """计算整体健康度评分"""
        logger.critical("\n🏥 整体健康度评估")
        logger.critical("=" * 60)
        
        # 各组件权重
        weights = {
            'flexible_grouping': 0.3,
            'system_stability': 0.4,
            'workflow_logic': 0.2,
            'code_design': 0.1
        }
        
        # 获取各组件评分
        scores = {
            'flexible_grouping': self.analysis_results['flexible_grouping_verification']['effectiveness_score'],
            'system_stability': self.analysis_results['system_stability_issues']['stability_score'],
            'workflow_logic': self.analysis_results['workflow_logic_defects']['logic_health_score'],
            'code_design': self.analysis_results['code_design_problems']['design_health_score']
        }
        
        # 计算加权平均分
        overall_score = sum(scores[component] * weights[component] for component in scores)
        
        # 确定健康状态
        if overall_score >= 85:
            health_status = '优秀'
            health_icon = '✅'
        elif overall_score >= 70:
            health_status = '良好'
            health_icon = '🟢'
        elif overall_score >= 55:
            health_status = '一般'
            health_icon = '🟡'
        else:
            health_status = '需要改进'
            health_icon = '🔴'
        
        logger.critical("📊 各组件健康度评分:")
        for component, score in scores.items():
            logger.critical(f"  {component}: {score:.1f}/100")
        
        logger.critical(f"\n🎯 整体健康度:")
        logger.critical(f"  综合评分: {overall_score:.1f}/100")
        logger.critical(f"  健康状态: {health_icon} {health_status}")
        
        self.analysis_results['overall_health'] = {
            'component_scores': scores,
            'overall_score': overall_score,
            'health_status': health_status
        }
        
        return overall_score, health_status

def main():
    """主分析函数"""
    try:
        analyzer = TargetedLogAnalyzer()
        
        # 读取目标时间范围的日志
        log_lines = analyzer.read_target_timerange_logs()
        
        if not log_lines:
            logger.error("❌ 没有找到目标时间范围内的日志")
            return False
        
        # 1. 验证灵活分组功能
        grouping_events = analyzer.verify_flexible_grouping_function(log_lines)
        
        # 2. 分析系统稳定性问题
        stability_issues = analyzer.analyze_system_stability_issues(log_lines)
        
        # 3. 分析工作流逻辑缺陷
        logic_defects = analyzer.analyze_workflow_logic_defects(log_lines)
        
        # 4. 分析代码设计问题
        design_problems = analyzer.analyze_code_design_problems(log_lines)
        
        # 5. 生成修复建议
        recommendations = analyzer.generate_fix_recommendations()
        
        # 6. 计算整体健康度
        overall_score, health_status = analyzer.calculate_overall_health_score()
        
        logger.critical(f"\n🎉 千川工作流目标时间段日志分析完成!")
        logger.critical(f"整体健康状态: {health_status} ({overall_score:.1f}/100)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 目标日志分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
