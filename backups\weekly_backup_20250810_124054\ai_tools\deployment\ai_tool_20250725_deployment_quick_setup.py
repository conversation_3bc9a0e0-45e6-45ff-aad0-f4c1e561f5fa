#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目快速部署脚本，适用于常见部署场景的简化流程
清理条件: 项目不再需要快速部署时可删除
"""

import os
import sys
import json
import yaml
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class QianchuanQuickSetup:
    """千川自动化项目快速部署设置"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.setup_log = []
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.setup_log.append(log_entry)
        
        if level == "ERROR":
            print(f"❌ {message}")
        elif level == "SUCCESS":
            print(f"✅ {message}")
        elif level == "WARNING":
            print(f"⚠️ {message}")
        else:
            print(f"ℹ️ {message}")
    
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        self.log("检查前置条件...")
        
        # 检查Python版本
        if sys.version_info < (3, 9):
            self.log(f"Python版本过低: {sys.version}, 需要3.9+", "ERROR")
            return False
        
        self.log(f"Python版本: {sys.version}", "SUCCESS")
        
        # 检查关键文件
        required_files = ["requirements.txt", "config/settings.yml", "main.py", "web_ui.py"]
        missing_files = []
        
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            self.log(f"缺少关键文件: {', '.join(missing_files)}", "ERROR")
            return False
        
        self.log("关键文件检查通过", "SUCCESS")
        return True
    
    def install_dependencies(self) -> bool:
        """安装依赖包"""
        self.log("安装Python依赖包...")
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.log("Python依赖包安装完成", "SUCCESS")
                return True
            else:
                self.log(f"依赖包安装失败: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"依赖包安装异常: {e}", "ERROR")
            return False
    
    def install_playwright(self) -> bool:
        """安装Playwright浏览器"""
        self.log("安装Playwright浏览器...")
        
        try:
            result = subprocess.run([
                sys.executable, "-m", "playwright", "install", "chromium"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("Playwright浏览器安装完成", "SUCCESS")
                return True
            else:
                self.log(f"浏览器安装失败: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"浏览器安装异常: {e}", "ERROR")
            return False
    
    def setup_directories(self, workflow_dir: str) -> bool:
        """设置目录结构"""
        self.log(f"创建目录结构: {workflow_dir}")
        
        try:
            base_dir = Path(workflow_dir)
            
            # 创建主要目录
            directories = [
                "01_materials_to_process/缇萃百货",
                "02_materials_approved/缇萃百货", 
                "03_materials_rejected/缇萃百货",
                "auth_states",
                "database",
                "logs"
            ]
            
            for dir_path in directories:
                full_path = base_dir / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
            
            # 创建项目日志目录
            (self.project_root / "logs").mkdir(exist_ok=True)
            
            self.log("目录结构创建完成", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"目录创建失败: {e}", "ERROR")
            return False
    
    def update_config(self, config_updates: Dict[str, Any]) -> bool:
        """更新配置文件"""
        self.log("更新配置文件...")
        
        try:
            settings_file = self.project_root / "config" / "settings.yml"
            
            # 备份原配置
            backup_file = settings_file.with_suffix('.yml.backup')
            shutil.copy2(settings_file, backup_file)
            self.log(f"配置文件已备份: {backup_file}")
            
            # 读取现有配置
            with open(settings_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            
            # 应用更新
            for key, value in config_updates.items():
                if '.' in key:
                    # 处理嵌套键，如 'workflow.material_collection.dest_dir'
                    keys = key.split('.')
                    current = config
                    for k in keys[:-1]:
                        if k not in current:
                            current[k] = {}
                        current = current[k]
                    current[keys[-1]] = value
                else:
                    config[key] = value
            
            # 保存配置
            with open(settings_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.log("配置文件更新完成", "SUCCESS")
            return True
            
        except Exception as e:
            self.log(f"配置更新失败: {e}", "ERROR")
            return False
    
    def initialize_database(self, db_type: str = "sqlite") -> bool:
        """初始化数据库"""
        self.log(f"初始化{db_type}数据库...")
        
        try:
            if db_type == "sqlite":
                # SQLite初始化
                init_script = '''
from qianchuan_aw.database.models import Base
from qianchuan_aw.utils.db_utils import get_engine
Base.metadata.create_all(get_engine())
print("数据库初始化完成")
'''
                result = subprocess.run([
                    sys.executable, "-c", init_script
                ], capture_output=True, text=True, cwd=self.project_root)
                
                if result.returncode == 0:
                    self.log("SQLite数据库初始化完成", "SUCCESS")
                    return True
                else:
                    self.log(f"数据库初始化失败: {result.stderr}", "ERROR")
                    return False
            
            # PostgreSQL需要手动配置
            elif db_type == "postgresql":
                self.log("PostgreSQL需要手动配置数据库连接", "WARNING")
                return True
                
        except Exception as e:
            self.log(f"数据库初始化异常: {e}", "ERROR")
            return False
    
    def test_basic_functionality(self) -> bool:
        """测试基础功能"""
        self.log("测试基础功能...")
        
        try:
            # 测试配置加载
            test_script = '''
from qianchuan_aw.utils.config_loader import load_settings
settings = load_settings()
print(f"配置加载成功，工作流目录: {settings.get('custom_workflow_assets_dir', '默认')}")
'''
            result = subprocess.run([
                sys.executable, "-c", test_script
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.log("基础功能测试通过", "SUCCESS")
                self.log(result.stdout.strip())
                return True
            else:
                self.log(f"功能测试失败: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"功能测试异常: {e}", "ERROR")
            return False
    
    def interactive_setup(self) -> bool:
        """交互式快速设置"""
        print("🚀 千川自动化项目快速部署设置")
        print("=" * 50)
        
        try:
            # 1. 检查前置条件
            if not self.check_prerequisites():
                return False
            
            # 2. 获取用户配置
            print("\n📝 请提供部署配置:")
            
            # 工作流目录
            default_workflow = str(self.project_root / "workflow_assets")
            workflow_dir = input(f"工作流资产目录 (默认: {default_workflow}): ").strip()
            if not workflow_dir:
                workflow_dir = default_workflow
            
            # 数据库选择
            print("\n数据库类型:")
            print("1. SQLite (推荐，简单)")
            print("2. PostgreSQL (需要手动配置)")
            db_choice = input("选择数据库类型 (1/2, 默认: 1): ").strip() or "1"
            db_type = "sqlite" if db_choice == "1" else "postgresql"
            
            # 素材源目录
            print("\n素材源目录 (可选，稍后可在配置文件中修改):")
            source_dirs = []
            while True:
                source_dir = input("输入素材源目录路径 (回车结束): ").strip()
                if not source_dir:
                    break
                source_dirs.append(source_dir)
            
            # 3. 执行设置步骤
            print("\n🔧 开始执行设置...")
            
            # 安装依赖
            if not self.install_dependencies():
                return False
            
            # 安装浏览器
            if not self.install_playwright():
                self.log("浏览器安装失败，但继续执行", "WARNING")
            
            # 创建目录
            if not self.setup_directories(workflow_dir):
                return False
            
            # 更新配置
            config_updates = {
                'custom_workflow_assets_dir': workflow_dir,
                'database.type': db_type,
                'workflow.material_collection.dest_dir': f"{workflow_dir}/01_materials_to_process/缇萃百货"
            }
            
            if source_dirs:
                config_updates['workflow.material_collection.source_dirs'] = source_dirs
            
            if not self.update_config(config_updates):
                return False
            
            # 初始化数据库
            if not self.initialize_database(db_type):
                self.log("数据库初始化失败，但继续执行", "WARNING")
            
            # 测试功能
            if not self.test_basic_functionality():
                self.log("功能测试失败，但设置已完成", "WARNING")
            
            print("\n✅ 快速设置完成!")
            print("\n📋 设置摘要:")
            print(f"- 工作流目录: {workflow_dir}")
            print(f"- 数据库类型: {db_type}")
            print(f"- 素材源目录: {len(source_dirs)} 个")
            
            print("\n📝 下一步操作:")
            print("1. 检查并完善config/settings.yml配置")
            print("2. 配置API凭证 (app_id, secret)")
            print("3. 启动Redis服务")
            if db_type == "postgresql":
                print("4. 配置PostgreSQL数据库连接")
            print("5. 测试系统启动:")
            print("   - streamlit run web_ui.py")
            print("   - python run_celery_worker.py")
            print("   - python run_celery_beat.py")
            
            return True
            
        except KeyboardInterrupt:
            self.log("用户取消设置", "WARNING")
            return False
        except Exception as e:
            self.log(f"设置过程异常: {e}", "ERROR")
            return False
    
    def save_setup_log(self) -> str:
        """保存设置日志"""
        log_file = self.project_root / "ai_reports" / "deployment" / f"quick_setup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write("千川自动化项目快速设置日志\n")
            f.write("=" * 40 + "\n")
            f.write(f"设置时间: {datetime.now().isoformat()}\n")
            f.write(f"项目根目录: {self.project_root}\n")
            f.write("\n设置过程:\n")
            for log_entry in self.setup_log:
                f.write(log_entry + "\n")
        
        return str(log_file)

def main():
    """主函数"""
    print("欢迎使用千川自动化项目快速部署工具!")
    print("\n💡 这是一个简化的部署流程，适用于:")
    print("- 新环境快速搭建")
    print("- 开发测试环境")
    print("- 基础功能验证")
    print("\n⚠️ 对于生产环境，建议使用完整部署流程")
    
    confirm = input("\n是否继续快速设置? (y/n): ").strip().lower()
    if confirm != 'y':
        print("设置已取消")
        return False
    
    setup = QianchuanQuickSetup()
    success = setup.interactive_setup()
    
    # 保存日志
    log_file = setup.save_setup_log()
    print(f"\n📄 设置日志已保存: {log_file}")
    
    if success:
        print("\n🎉 快速设置成功!")
        print("请按照上述下一步操作完成最终配置")
    else:
        print("\n❌ 快速设置失败")
        print("请查看日志文件或使用完整部署流程")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
