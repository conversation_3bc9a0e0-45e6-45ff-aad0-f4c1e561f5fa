#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 监控工具
生命周期: 永久保留
创建目的: 实时监控浏览器实例数量，验证配置修复效果
清理条件: 成为系统监控组件后可归档
"""

import time
import subprocess
import os
from datetime import datetime
from typing import List, Dict

class BrowserMonitor:
    """浏览器实例监控器"""
    
    def __init__(self):
        self.max_allowed_browsers = 3  # 最大允许的浏览器数量
        self.check_interval = 30  # 检查间隔(秒)
        self.alert_threshold = 5  # 告警阈值
        self.log_file = "ai_reports/monitoring/browser_monitor.log"
        
        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

    def get_browser_processes(self) -> List[Dict]:
        """获取浏览器进程信息"""
        processes = []
        
        try:
            # 检查Chrome/Chromium进程
            for browser_name in ['chrome.exe', 'chromium.exe']:
                result = subprocess.run([
                    'tasklist', '/FI', f'IMAGENAME eq {browser_name}', '/FO', 'CSV'
                ], capture_output=True, text=True, encoding='gbk')
                
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # 跳过标题行
                        if line.strip() and '信息: 没有运行的任务' not in line:
                            parts = line.split('","')
                            if len(parts) >= 5:
                                name = parts[0].strip('"')
                                pid = parts[1].strip('"')
                                memory = parts[4].strip('"').replace(' K', '').replace(',', '')
                                
                                try:
                                    memory_mb = int(memory) / 1024
                                    processes.append({
                                        'name': name,
                                        'pid': int(pid),
                                        'memory_mb': round(memory_mb, 1),
                                        'type': 'Chrome/Chromium'
                                    })
                                except ValueError:
                                    continue
            
            return processes
            
        except Exception as e:
            self.log(f"❌ 获取浏览器进程失败: {e}")
            return []

    def get_python_processes(self) -> List[Dict]:
        """获取Python进程信息（可能的Celery工作进程）"""
        processes = []
        
        try:
            result = subprocess.run([
                'tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'
            ], capture_output=True, text=True, encoding='gbk')
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:
                    if line.strip() and '信息: 没有运行的任务' not in line:
                        parts = line.split('","')
                        if len(parts) >= 5:
                            name = parts[0].strip('"')
                            pid = parts[1].strip('"')
                            memory = parts[4].strip('"').replace(' K', '').replace(',', '')
                            
                            try:
                                memory_mb = int(memory) / 1024
                                processes.append({
                                    'name': name,
                                    'pid': int(pid),
                                    'memory_mb': round(memory_mb, 1),
                                    'type': 'Python'
                                })
                            except ValueError:
                                continue
            
            return processes
            
        except Exception as e:
            self.log(f"❌ 获取Python进程失败: {e}")
            return []

    def log(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        
        print(log_message)
        
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + '\n')
        except Exception as e:
            print(f"写入日志失败: {e}")

    def check_browser_status(self) -> Dict:
        """检查浏览器状态"""
        browser_processes = self.get_browser_processes()
        python_processes = self.get_python_processes()
        
        total_browsers = len(browser_processes)
        total_memory = sum(p['memory_mb'] for p in browser_processes)
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'browser_count': total_browsers,
            'total_memory_mb': round(total_memory, 1),
            'python_processes': len(python_processes),
            'status': 'NORMAL' if total_browsers <= self.max_allowed_browsers else 'ALERT',
            'processes': browser_processes
        }
        
        return status

    def monitor_once(self) -> Dict:
        """执行一次监控检查"""
        status = self.check_browser_status()
        
        # 记录状态
        if status['browser_count'] == 0:
            self.log(f"✅ 无浏览器进程运行")
        elif status['browser_count'] <= self.max_allowed_browsers:
            self.log(f"✅ 浏览器进程正常: {status['browser_count']} 个, 内存: {status['total_memory_mb']}MB")
        else:
            self.log(f"⚠️ 浏览器进程过多: {status['browser_count']} 个, 内存: {status['total_memory_mb']}MB")
            
            # 显示详细信息
            for proc in status['processes']:
                self.log(f"   - {proc['name']} PID: {proc['pid']}, 内存: {proc['memory_mb']}MB")
        
        # Python进程信息
        if status['python_processes'] > 0:
            self.log(f"🐍 Python进程: {status['python_processes']} 个")
        
        return status

    def continuous_monitor(self, duration_minutes: int = 60):
        """持续监控"""
        self.log(f"🚀 开始持续监控浏览器实例 (持续 {duration_minutes} 分钟)")
        self.log(f"📊 监控参数: 最大允许 {self.max_allowed_browsers} 个, 检查间隔 {self.check_interval} 秒")
        self.log("="*60)
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        alert_count = 0
        max_browsers_seen = 0
        
        try:
            while time.time() < end_time:
                status = self.monitor_once()
                
                # 统计信息
                if status['browser_count'] > max_browsers_seen:
                    max_browsers_seen = status['browser_count']
                
                if status['status'] == 'ALERT':
                    alert_count += 1
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            self.log("⏹️ 监控被用户中断")
        
        # 监控总结
        self.log("="*60)
        self.log(f"📊 监控总结:")
        self.log(f"   监控时长: {(time.time() - start_time) / 60:.1f} 分钟")
        self.log(f"   最大浏览器数: {max_browsers_seen} 个")
        self.log(f"   告警次数: {alert_count} 次")
        
        if max_browsers_seen <= self.max_allowed_browsers:
            self.log("🎉 监控期间浏览器实例数量控制良好！")
        else:
            self.log("⚠️ 监控期间发现浏览器实例过多，需要进一步优化")

    def quick_check(self):
        """快速检查当前状态"""
        print("🎯 浏览器实例快速检查")
        print("="*40)
        
        status = self.monitor_once()
        
        print(f"\n📊 当前状态:")
        print(f"  🖥️ 浏览器进程: {status['browser_count']} 个")
        print(f"  💾 内存占用: {status['total_memory_mb']} MB")
        print(f"  🐍 Python进程: {status['python_processes']} 个")
        print(f"  📈 状态: {status['status']}")
        
        if status['browser_count'] > 0:
            print(f"\n📋 浏览器进程详情:")
            for proc in status['processes']:
                print(f"  - {proc['name']} PID: {proc['pid']}, 内存: {proc['memory_mb']}MB")
        
        # 给出建议
        print(f"\n💡 建议:")
        if status['browser_count'] == 0:
            print("  ✅ 当前无浏览器进程，可以安全启动工作流")
        elif status['browser_count'] <= self.max_allowed_browsers:
            print("  ✅ 浏览器进程数量正常")
        else:
            print(f"  ⚠️ 浏览器进程过多，建议清理或检查配置")
        
        return status

def main():
    """主函数"""
    import sys
    
    monitor = BrowserMonitor()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'continuous':
            # 持续监控模式
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            monitor.continuous_monitor(duration)
        elif sys.argv[1] == 'quick':
            # 快速检查模式
            monitor.quick_check()
    else:
        # 默认快速检查
        monitor.quick_check()
        
        print(f"\n🔧 使用方法:")
        print(f"  python {__file__} quick          # 快速检查")
        print(f"  python {__file__} continuous 30  # 持续监控30分钟")

if __name__ == "__main__":
    main()
