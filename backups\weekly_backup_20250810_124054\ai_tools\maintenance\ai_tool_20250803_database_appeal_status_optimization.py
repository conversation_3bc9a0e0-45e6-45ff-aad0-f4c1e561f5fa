#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 优化数据库appeal_status字段，确保默认值为NULL
清理条件: 数据库优化完成后可归档，但建议保留作为维护工具
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text, inspect

class DatabaseAppealStatusOptimizer:
    """数据库appeal_status字段优化器"""
    
    def __init__(self):
        self.optimization_start_time = datetime.now()
        self.changes_made = []
        self.issues_found = []
    
    def check_current_schema(self):
        """检查当前数据库模式"""
        
        logger.info("🔍 检查当前数据库模式...")
        
        with database_session() as db:
            # 检查appeal_status字段的当前定义
            inspector = inspect(db.bind)
            columns = inspector.get_columns('campaigns')
            
            appeal_status_column = None
            for column in columns:
                if column['name'] == 'appeal_status':
                    appeal_status_column = column
                    break
            
            if appeal_status_column:
                logger.info(f"📋 appeal_status字段当前定义:")
                logger.info(f"   类型: {appeal_status_column['type']}")
                logger.info(f"   可空: {appeal_status_column['nullable']}")
                logger.info(f"   默认值: {appeal_status_column.get('default', 'None')}")
                
                # 检查是否有问题的默认值
                default_value = appeal_status_column.get('default')
                if default_value and 'appeal_pending' in str(default_value):
                    self.issues_found.append("appeal_status字段有问题的默认值")
                    logger.warning(f"⚠️ 发现问题: appeal_status字段默认值为{default_value}")
                else:
                    logger.success("✅ appeal_status字段默认值正常")
            else:
                logger.error("❌ 未找到appeal_status字段")
                self.issues_found.append("appeal_status字段不存在")
            
            return appeal_status_column
    
    def check_data_consistency(self):
        """检查数据一致性"""
        
        logger.info("🔍 检查数据一致性...")
        
        with database_session() as db:
            # 检查是否有不一致的数据
            inconsistent_data = db.execute(text("""
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(CASE WHEN appeal_status = 'appeal_pending' 
                               AND first_appeal_at IS NULL 
                               AND appeal_attempt_count = 0 THEN 1 END) as problematic_count
                FROM campaigns
                WHERE status = 'AUDITING'
            """)).fetchone()
            
            logger.info(f"📊 数据一致性检查结果:")
            logger.info(f"   AUDITING状态计划总数: {inconsistent_data.total_count}")
            logger.info(f"   有问题的计划数: {inconsistent_data.problematic_count}")
            
            if inconsistent_data.problematic_count > 0:
                self.issues_found.append(f"{inconsistent_data.problematic_count}个计划有数据不一致问题")
                logger.warning(f"⚠️ 发现 {inconsistent_data.problematic_count} 个有问题的计划")
            else:
                logger.success("✅ 数据一致性检查通过")
            
            return inconsistent_data
    
    def optimize_database_schema(self, dry_run: bool = True):
        """优化数据库模式"""
        
        logger.info(f"🔧 开始优化数据库模式 (dry_run={dry_run})...")
        
        # 检查当前模式
        current_schema = self.check_current_schema()
        
        if not current_schema:
            logger.error("❌ 无法获取当前模式信息，跳过优化")
            return False
        
        # 如果已经是正确的配置，跳过
        default_value = current_schema.get('default')
        if not default_value or 'appeal_pending' not in str(default_value):
            logger.success("✅ 数据库模式已经是最优配置，无需修改")
            return True
        
        with database_session() as db:
            try:
                if not dry_run:
                    # 移除默认值
                    logger.info("🔧 移除appeal_status字段的默认值...")
                    db.execute(text("""
                        ALTER TABLE campaigns 
                        ALTER COLUMN appeal_status DROP DEFAULT
                    """))
                    
                    self.changes_made.append("移除appeal_status字段默认值")
                    logger.success("✅ 成功移除appeal_status字段默认值")
                else:
                    logger.info("🔍 [DRY RUN] 将移除appeal_status字段默认值")
                
                return True
                
            except Exception as e:
                logger.error(f"❌ 优化数据库模式失败: {e}")
                return False
    
    def verify_optimization(self):
        """验证优化效果"""
        
        logger.info("🔍 验证优化效果...")
        
        # 重新检查模式
        current_schema = self.check_current_schema()
        
        if not current_schema:
            return False
        
        # 检查默认值是否已移除
        if current_schema.get('default') is None:
            logger.success("✅ 验证通过: appeal_status字段默认值已正确移除")
            return True
        else:
            logger.error(f"❌ 验证失败: appeal_status字段仍有默认值: {current_schema.get('default')}")
            return False
    
    def create_test_campaign(self):
        """创建测试计划验证新的行为"""
        
        logger.info("🧪 创建测试计划验证新行为...")
        
        with database_session() as db:
            try:
                # 创建一个测试计划（不会真正插入，只是测试）
                from qianchuan_aw.database.models import Campaign
                
                test_campaign = Campaign(
                    campaign_id_qc='test_campaign_' + str(int(datetime.now().timestamp())),
                    account_id=1,  # 假设存在ID为1的账户
                    status='AUDITING',
                    appeal_status=None
                )
                
                # 不实际插入，只检查对象属性
                logger.info(f"📋 测试计划对象创建:")
                logger.info(f"   appeal_status: {test_campaign.appeal_status}")
                logger.info(f"   appeal_attempt_count: {test_campaign.appeal_attempt_count}")
                
                if test_campaign.appeal_status is None:
                    logger.success("✅ 测试通过: 新创建的计划appeal_status为NULL")
                    return True
                else:
                    logger.warning(f"⚠️ 测试异常: 新创建的计划appeal_status为{test_campaign.appeal_status}")
                    return False
                
            except Exception as e:
                logger.error(f"❌ 测试失败: {e}")
                return False
    
    def generate_optimization_report(self):
        """生成优化报告"""
        
        logger.info("📊 生成优化报告...")
        
        report = {
            'optimization_time': self.optimization_start_time,
            'completion_time': datetime.now(),
            'issues_found': self.issues_found,
            'changes_made': self.changes_made,
            'success': len(self.issues_found) == 0 or len(self.changes_made) > 0
        }
        
        logger.info("="*60)
        logger.info("📋 数据库优化报告")
        logger.info("="*60)
        logger.info(f"开始时间: {report['optimization_time']}")
        logger.info(f"完成时间: {report['completion_time']}")
        logger.info(f"发现问题: {len(report['issues_found'])} 个")
        for issue in report['issues_found']:
            logger.info(f"   - {issue}")
        logger.info(f"执行修改: {len(report['changes_made'])} 项")
        for change in report['changes_made']:
            logger.info(f"   - {change}")
        
        if report['success']:
            logger.success("✅ 数据库优化成功完成")
        else:
            logger.warning("⚠️ 数据库优化部分完成或存在问题")
        
        return report

def main():
    """主函数"""
    
    logger.info("🚀 开始数据库appeal_status字段优化...")
    
    optimizer = DatabaseAppealStatusOptimizer()
    
    # 1. 检查当前状态
    logger.info("="*60)
    logger.info("第一步: 检查当前数据库状态")
    logger.info("="*60)
    
    optimizer.check_current_schema()
    optimizer.check_data_consistency()
    
    # 2. 执行优化（先dry run）
    logger.info("\n" + "="*60)
    logger.info("第二步: 执行数据库优化")
    logger.info("="*60)
    
    # 先执行dry run
    logger.info("🔍 执行预演 (dry run)...")
    optimizer.optimize_database_schema(dry_run=True)
    
    # 询问是否执行实际优化
    response = input("\n是否执行实际的数据库优化? (y/N): ").strip().lower()
    
    if response == 'y':
        logger.info("🔧 开始执行实际优化...")
        success = optimizer.optimize_database_schema(dry_run=False)
        
        if success:
            # 3. 验证优化效果
            logger.info("\n" + "="*60)
            logger.info("第三步: 验证优化效果")
            logger.info("="*60)
            
            verification_success = optimizer.verify_optimization()
            test_success = optimizer.create_test_campaign()
            
            if verification_success and test_success:
                logger.success("🎉 数据库优化完成并验证成功！")
            else:
                logger.warning("⚠️ 优化完成但验证存在问题")
        else:
            logger.error("💥 数据库优化失败")
    else:
        logger.info("❌ 用户取消优化操作")
    
    # 4. 生成报告
    logger.info("\n" + "="*60)
    logger.info("第四步: 生成优化报告")
    logger.info("="*60)
    
    optimizer.generate_optimization_report()
    
    print("\n💡 后续建议:")
    print("   1. 检查所有创建Campaign的代码，确保明确设置appeal_status=None")
    print("   2. 运行代码修复脚本更新相关代码")
    print("   3. 测试新创建的计划是否能被正确识别")

if __name__ == "__main__":
    main()
