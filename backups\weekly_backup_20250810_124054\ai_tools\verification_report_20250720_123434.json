{"timestamp": "2025-07-20T12:34:33.793095", "config_validation": {"required_sections": {"status": "PASS", "details": "所有必需配置节都存在", "error": "", "timestamp": "2025-07-20T12:34:33.820218"}, "database_config": {"status": "PASS", "details": "数据库配置完整", "error": "", "timestamp": "2025-07-20T12:34:33.820218"}, "api_credentials": {"status": "PASS", "details": "API凭据配置完整", "error": "", "timestamp": "2025-07-20T12:34:33.820218"}}, "dependency_check": {"critical_packages": {"status": "PASS", "details": "所有关键包已安装: ['celery', 'redis', 'sqlalchemy', 'psycopg2-binary', 'playwright', 'streamlit', 'pyyaml', 'requests']", "error": "", "timestamp": "2025-07-20T12:34:34.500894"}}, "database_connectivity": {"redis_connection": {"status": "PASS", "details": "Redis连接成功: localhost:6379", "error": "", "timestamp": "2025-07-20T12:34:34.661395"}, "postgresql_connection": {"status": "PASS", "details": "PostgreSQL连接成功", "error": "", "timestamp": "2025-07-20T12:34:34.694976"}}, "core_functionality": {"config_manager": {"status": "PASS", "details": "配置管理器工作正常", "error": "", "timestamp": "2025-07-20T12:34:34.695981"}, "fault_tolerance_service": {"status": "PASS", "details": "容错服务模块导入成功", "error": "", "timestamp": "2025-07-20T12:34:34.702482"}, "celery_app": {"status": "PASS", "details": "Celery应用初始化成功", "error": "", "timestamp": "2025-07-20T12:34:34.777388"}, "celery_tasks": {"status": "WARN", "details": "部分任务未注册: ['tasks.ingest_and_upload', 'tasks.group_and_dispatch', 'tasks.create_plans', 'tasks.appeal_plans']", "error": "", "timestamp": "2025-07-20T12:34:34.783387"}}, "workflow_readiness": {"assets_directory": {"status": "PASS", "details": "工作流资产目录存在: G:/workflow_assets", "error": "", "timestamp": "2025-07-20T12:34:34.784386"}, "workflow_subdirs": {"status": "PASS", "details": "所有工作流子目录存在", "error": "", "timestamp": "2025-07-20T12:34:34.784386"}, "source_directories": {"status": "PASS", "details": "所有源目录可访问: 5", "error": "", "timestamp": "2025-07-20T12:34:34.790392"}, "run_celery_worker.py_exists": {"status": "PASS", "details": "启动脚本存在: run_celery_worker.py", "error": "", "timestamp": "2025-07-20T12:34:34.790392"}, "run_celery_beat.py_exists": {"status": "PASS", "details": "启动脚本存在: run_celery_beat.py", "error": "", "timestamp": "2025-07-20T12:34:34.790392"}, "startup_guide": {"status": "PASS", "details": "启动指南已生成: D:\\Project\\qianchuangzl\\ai_tools\\startup_guide.md", "error": "", "timestamp": "2025-07-20T12:34:34.791392"}}, "recommendations": []}