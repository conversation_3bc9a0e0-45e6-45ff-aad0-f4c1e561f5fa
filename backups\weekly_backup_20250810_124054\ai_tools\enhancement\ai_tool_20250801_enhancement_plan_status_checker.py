"""
计划状态检查增强工具
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实现智能计划状态检查，判断计划是否准备好提审
清理条件: 成为核心功能后可集成到主代码
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.database.models import Campaign, AdAccount, Principal


class PlanStatusChecker:
    """计划状态检查器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.api_credentials = app_settings['api_credentials']
        
    def check_plan_ready_for_appeal(self, db: Session, campaign: Campaign) -> Tuple[bool, str, Optional[str]]:
        """
        检查计划是否准备好提审
        
        Args:
            db: 数据库会话
            campaign: 计划对象
            
        Returns:
            Tuple[bool, str, Optional[str]]: (是否准备好, 原因, 当前状态)
        """
        try:
            # 1. 基础检查：计划是否存在且有效
            if not campaign or not campaign.campaign_id_qc:
                return False, "计划ID无效", None
                
            # 2. 跳过计划年龄检查，因为数据库时间戳可能有问题
            # 直接检查计划的真实状态更可靠
            logger.info(f"跳过计划年龄检查，直接检查API状态")
                    
            # 3. 检查是否已经提审过
            if campaign.appeal_status == 'appeal_pending':
                return False, "计划已经提审成功，无需重复提审", "ALREADY_APPEALED"
                
            # 4. 使用千川API检查计划真实状态
            real_status = self._get_plan_real_status(campaign)
            if not real_status:
                return False, "无法获取计划真实状态", "API_ERROR"
                
            # 5. 判断状态是否适合提审
            return self._is_status_ready_for_appeal(real_status)
            
        except Exception as e:
            logger.error(f"检查计划 {campaign.campaign_id_qc} 状态时出错: {e}")
            return False, f"状态检查异常: {e}", "ERROR"
    
    def _get_plan_real_status(self, campaign: Campaign) -> Optional[str]:
        """获取计划的真实状态"""
        try:
            # 创建API客户端
            client = QianchuanClient(
                app_id=self.api_credentials['app_id'],
                secret=self.api_credentials['secret'],
                principal_id=campaign.account.principal.id
            )
            
            # 查询计划列表
            end_time = datetime.now()
            start_time = end_time - timedelta(days=1)
            filtering = {
                "ids": [campaign.campaign_id_qc],
                "create_time": {
                    "start_time": start_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "end_time": end_time.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            plans = client.get_ad_plan_list(
                advertiser_id=campaign.account.account_id_qc,
                filtering=filtering
            )
            
            if plans and len(plans) > 0:
                plan_info = plans[0]
                real_status = plan_info.get('status', '')
                logger.info(f"计划 {campaign.campaign_id_qc} 真实状态: {real_status}")
                return real_status
            else:
                logger.warning(f"未找到计划 {campaign.campaign_id_qc} 的信息")
                return None
                
        except Exception as e:
            logger.error(f"获取计划 {campaign.campaign_id_qc} 真实状态失败: {e}")
            return None
    
    def _is_status_ready_for_appeal(self, status: str) -> Tuple[bool, str, str]:
        """
        判断状态是否适合提审

        Args:
            status: 计划状态

        Returns:
            Tuple[bool, str, str]: (是否准备好, 原因, 状态)
        """
        # 定义可以提审的状态
        appealable_statuses = {
            'OFFLINE_AUDIT': '审核不通过，可以提审',
            'REJECT': '被拒绝，可以提审',
            'AUDIT_REJECT': '审核拒绝，可以提审',
            'CAMPAIGN_STATUS_DISABLE': '计划已禁用，可以提审',
            'CAMPAIGN_DISABLE': '计划已禁用，可以提审',
            'OFFLINE_BALANCE': '余额不足下线，可以提审',
            'OFFLINE': '计划下线，可以提审',
            'FROZEN': '已终止，可以提审',
            'SYSTEM_DISABLE': '系统暂停，可以提审'
        }

        # 定义不能提审的状态（需要等待或无需提审）
        non_appealable_statuses = {
            # 审核相关状态 - 需要等待
            'AUDIT': '新建审核中，需要等待审核完成',
            'REAUDIT': '修改审核中，需要等待审核完成',
            'AUDITING': '仍在初审中，需要等待',

            # 正常运行状态 - 无需提审
            'DELIVERY_OK': '投放中，无需提审',
            'TIME_DONE': '已完成，无需提审',
            'DISABLE': '已暂停，无需提审',
            'TIME_NO_REACH': '未到达投放时间，无需提审',
            'NO_SCHEDULE': '不在投放时段，无需提审',
            'OFFLINE_BUDGET': '广告预算不足，无需提审',
            'EXTERNAL_URL_DISABLE': '落地页暂不可用，无需提审',
            'LIVE_ROOM_OFF': '关联直播间未开播，无需提审',
            'QUOTA_DISABLE': '在投计划配额超限，无需提审',
            'ROI2_DISABLE': '全域推广暂停，无需提审',

            # 其他状态
            'DELETED': '已删除，无法提审',
            'AUDIT_ACCEPTED': '已审核通过，无需提审',
            'CAMPAIGN_STATUS_ENABLE': '计划正常运行，无需提审',
            'CAMPAIGN_STATUS_NOT_START': '计划未开始，无需提审',
            'CAMPAIGN_STATUS_DONE': '计划已完成，无需提审',
            'ENABLE': '计划正常运行，无需提审',
            'CAMPAIGN_STATUS_ALL_ENABLE': '计划全部启用，无需提审'
        }

        if status in appealable_statuses:
            return True, appealable_statuses[status], status
        elif status in non_appealable_statuses:
            return False, non_appealable_statuses[status], status
        else:
            # 未知状态，保守处理
            logger.warning(f"遇到未知计划状态: {status}")
            return False, f"未知状态({status})，保守不提审", status
    
    def batch_check_plans_ready(self, db: Session, campaigns: List[Campaign]) -> Dict[str, Dict]:
        """
        批量检查计划是否准备好提审
        
        Args:
            db: 数据库会话
            campaigns: 计划列表
            
        Returns:
            Dict[str, Dict]: 检查结果字典
        """
        results = {}
        
        for campaign in campaigns:
            ready, reason, status = self.check_plan_ready_for_appeal(db, campaign)
            results[campaign.campaign_id_qc] = {
                'ready': ready,
                'reason': reason,
                'status': status,
                'campaign_id': campaign.id,
                'account_id': campaign.account.account_id_qc,
                'principal_name': campaign.account.principal.name
            }
            
            # API调用间隔
            time.sleep(0.5)
            
        return results
    
    def wait_for_plans_ready(self, db: Session, campaigns: List[Campaign], 
                           max_wait_minutes: int = 10, check_interval_seconds: int = 30) -> List[Campaign]:
        """
        等待计划准备好提审
        
        Args:
            db: 数据库会话
            campaigns: 计划列表
            max_wait_minutes: 最大等待时间(分钟)
            check_interval_seconds: 检查间隔(秒)
            
        Returns:
            List[Campaign]: 准备好的计划列表
        """
        logger.info(f"开始等待 {len(campaigns)} 个计划准备好提审，最大等待 {max_wait_minutes} 分钟")
        
        ready_campaigns = []
        remaining_campaigns = campaigns.copy()
        start_time = datetime.now()
        max_wait_time = timedelta(minutes=max_wait_minutes)
        
        while remaining_campaigns and (datetime.now() - start_time) < max_wait_time:
            logger.info(f"检查剩余 {len(remaining_campaigns)} 个计划的状态...")
            
            newly_ready = []
            still_waiting = []
            
            for campaign in remaining_campaigns:
                ready, reason, status = self.check_plan_ready_for_appeal(db, campaign)
                
                if ready:
                    logger.success(f"✅ 计划 {campaign.campaign_id_qc} 准备好提审: {reason}")
                    newly_ready.append(campaign)
                else:
                    logger.info(f"⏳ 计划 {campaign.campaign_id_qc} 仍需等待: {reason}")
                    still_waiting.append(campaign)
                    
                time.sleep(0.5)  # API调用间隔
            
            ready_campaigns.extend(newly_ready)
            remaining_campaigns = still_waiting
            
            if remaining_campaigns:
                logger.info(f"等待 {check_interval_seconds} 秒后再次检查...")
                time.sleep(check_interval_seconds)
        
        # 超时处理
        if remaining_campaigns:
            elapsed = datetime.now() - start_time
            logger.warning(f"⚠️ 等待超时({elapsed.total_seconds():.0f}秒)，仍有 {len(remaining_campaigns)} 个计划未准备好")
            for campaign in remaining_campaigns:
                logger.warning(f"   - 计划 {campaign.campaign_id_qc} 仍未准备好")
        
        logger.info(f"✅ 共有 {len(ready_campaigns)} 个计划准备好提审")
        return ready_campaigns


def create_plan_status_checker(app_settings: Dict[str, Any]) -> PlanStatusChecker:
    """创建计划状态检查器实例"""
    return PlanStatusChecker(app_settings)


# 测试函数
def test_plan_status_checker():
    """测试计划状态检查器"""
    from qianchuan_aw.utils.config_manager import get_settings
    from qianchuan_aw.utils.db_utils import database_session
    
    app_settings = get_settings()
    checker = create_plan_status_checker(app_settings)
    
    with database_session() as db:
        # 获取所有AUDITING状态的计划
        campaigns = db.query(Campaign).filter(
            Campaign.status == 'AUDITING'
        ).all()
        
        if campaigns:
            logger.info(f"测试 {len(campaigns)} 个计划的状态检查")
            results = checker.batch_check_plans_ready(db, campaigns)
            
            for campaign_id, result in results.items():
                status_icon = "✅" if result['ready'] else "❌"
                logger.info(f"{status_icon} 计划 {campaign_id}: {result['reason']}")
        else:
            logger.info("没有找到AUDITING状态的计划")


if __name__ == "__main__":
    test_plan_status_checker()
