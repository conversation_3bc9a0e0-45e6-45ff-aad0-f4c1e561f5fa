#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 统一Web UI中的账户选择组件，实现⭐账户筛选功能
依赖关系: web_ui.py, 数据库模型
清理条件: 项目不再需要统一账户选择时
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Optional, Union, Tuple
from sqlalchemy.orm import joinedload

def _is_in_form():
    """检测当前是否在Streamlit表单内 - 简化版本"""
    # 由于检测表单状态比较复杂，我们使用参数传递的方式
    # 在调用时明确指定是否在表单内
    return False

def create_unified_account_selector(
    accounts: List,
    key: str,
    label: str = "选择广告账户",
    help_text: str = "⭐ 标记表示收藏账户",
    multi_select: bool = False,
    show_filter: bool = True,
    show_stats: bool = True,
    show_aweme_status: bool = True,
    default_filter: str = "显示全部账户",
    in_form: bool = False
) -> Union[List, str, None]:
    """
    创建统一的账户选择组件
    
    Args:
        accounts: 账户列表
        key: Streamlit组件的唯一键
        label: 选择框标签
        help_text: 帮助文本
        multi_select: 是否支持多选
        show_filter: 是否显示筛选选项
        show_stats: 是否显示统计信息
        show_aweme_status: 是否显示抖音号授权状态
        default_filter: 默认筛选选项
    
    Returns:
        选中的账户对象或账户对象列表
    """
    
    if not accounts:
        st.warning("没有找到可用的广告账户")
        return None if not multi_select else []
    
    # 筛选和统计区域
    if show_filter or show_stats:
        st.markdown("#### ⭐ 账户筛选与收藏管理")

        # 搜索框
        search_term = ""
        show_favorites_only = False
        show_authorized_only = False

        if show_filter:
            # 搜索框
            search_term = st.text_input(
                "🔍 搜索账户",
                placeholder="输入账户名称或千川ID搜索...",
                key=f"{key}_search_input",
                help="支持按账户名称或千川ID进行搜索"
            )

            # 筛选选项
            filter_col1, filter_col2 = st.columns(2)
            with filter_col1:
                show_favorites_only = st.checkbox(
                    "⭐ 仅显示收藏账户",
                    key=f"{key}_favorites_filter",
                    help="只显示已收藏的账户"
                )
            with filter_col2:
                show_authorized_only = st.checkbox(
                    "📱 仅显示已授权账户",
                    key=f"{key}_authorized_filter",
                    help="只显示已授权抖音号的账户"
                )

        # 统计信息
        if show_stats:
            total_count = len(accounts)
            favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
            authorized_count = len([acc for acc in accounts if getattr(acc, 'douyin_id', None)])

            stats_col1, stats_col2, stats_col3 = st.columns(3)
            with stats_col1:
                st.metric("总账户数", total_count)
            with stats_col2:
                st.metric("收藏账户", favorite_count)
            with stats_col3:
                st.metric("已授权账户", authorized_count)

        # 收藏管理按钮
        if show_filter and show_stats and not in_form:
            if st.button("🛠️ 管理收藏", key=f"{key}_manage_favorites"):
                st.session_state[f"{key}_show_favorite_manager"] = True

    # 根据筛选条件过滤账户
    if in_form:
        # 在表单内时，使用简化的筛选策略
        # 筛选只影响显示提示，不改变选项列表

        # 计算筛选结果（仅用于显示）
        display_filtered_accounts = []
        for account in accounts:
            # 搜索筛选
            if search_term:
                search_lower = search_term.lower()
                if (search_lower not in account.name.lower() and
                    search_term not in str(account.account_id_qc)):
                    continue

            # 收藏筛选
            if show_favorites_only and not getattr(account, 'is_favorite', False):
                continue

            # 授权筛选
            if show_authorized_only and not getattr(account, 'douyin_id', None):
                continue

            display_filtered_accounts.append(account)

        # 显示筛选结果统计（不影响选择）
        if show_filter and (search_term or show_favorites_only or show_authorized_only):
            if display_filtered_accounts:
                st.info(f"📊 筛选结果：找到 {len(display_filtered_accounts)} 个符合条件的账户（共 {len(accounts)} 个）")
                st.info("💡 提示：筛选仅用于查看，选择器中仍显示所有账户以保持选择状态")
            else:
                st.warning("🔍 没有找到符合筛选条件的账户")

        # 使用所有账户作为选项（保持选择状态稳定）
        filtered_accounts = accounts

    else:
        # 在表单外时，使用完整的筛选功能
        filtered_accounts = []
        for account in accounts:
            # 搜索筛选
            if search_term:
                search_lower = search_term.lower()
                if (search_lower not in account.name.lower() and
                    search_term not in str(account.account_id_qc)):
                    continue

            # 收藏筛选
            if show_favorites_only and not getattr(account, 'is_favorite', False):
                continue

            # 授权筛选
            if show_authorized_only and not getattr(account, 'douyin_id', None):
                continue

            filtered_accounts.append(account)

        # 显示筛选结果统计
        if show_filter and (search_term or show_favorites_only or show_authorized_only):
            if filtered_accounts:
                st.info(f"📊 筛选结果：显示 {len(filtered_accounts)} 个账户（共 {len(accounts)} 个）")
            else:
                st.warning("🔍 没有找到符合筛选条件的账户")
                return [] if multi_select else None

    # 按收藏状态排序（收藏的在前）
    filtered_accounts.sort(key=lambda x: (not getattr(x, 'is_favorite', False), x.name))
    
    # 创建账户选择选项（带收藏标识）
    account_options = {}
    for account in filtered_accounts:
        is_favorite = getattr(account, 'is_favorite', False)
        star_icon = "⭐ " if is_favorite else ""
        
        if show_aweme_status:
            aweme_status = f" [已授权]" if getattr(account, 'aweme_id', None) else ""
        else:
            aweme_status = ""
        
        # 构建显示名称
        display_name = f"{star_icon}{account.name} ({account.account_id_qc})"
        if hasattr(account, 'principal') and account.principal:
            display_name += f" - {account.principal.name}"
        display_name += aweme_status
        
        account_options[display_name] = account
    
    # 显示筛选统计
    if filtered_accounts != accounts:
        st.info(f"📊 当前筛选显示 {len(filtered_accounts)} 个账户（共 {len(accounts)} 个）")
    
    # 账户选择组件
    if multi_select:
        selected_display_names = st.multiselect(
            label,
            options=list(account_options.keys()),
            help=help_text,
            key=f"{key}_multiselect"
        )
        
        if selected_display_names:
            return [account_options[name] for name in selected_display_names]
        else:
            return []
    else:
        selected_display_name = st.selectbox(
            label,
            options=["请选择..."] + list(account_options.keys()),
            help=help_text,
            key=f"{key}_selectbox"
        )
        
        if selected_display_name and selected_display_name != "请选择...":
            return account_options[selected_display_name]
        else:
            return None
    
    # 收藏管理界面（只在非表单模式下显示）
    if show_filter and not in_form and st.session_state.get(f"{key}_show_favorite_manager", False):
        render_favorite_manager(accounts, key)

def render_favorite_manager(accounts: List, key: str):
    """渲染收藏管理界面"""
    st.markdown("---")
    st.markdown("#### 🛠️ 收藏管理")
    
    with st.expander("批量收藏管理", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**未收藏账户**")
            unfavorited = [acc for acc in accounts if not getattr(acc, 'is_favorite', False)]
            
            if unfavorited:
                unfavorited_options = {
                    f"{acc.name} ({acc.account_id_qc})": acc 
                    for acc in unfavorited
                }
                
                to_favorite = st.multiselect(
                    "选择要收藏的账户",
                    options=list(unfavorited_options.keys()),
                    key=f"{key}_to_favorite"
                )
                
                if st.button("⭐ 添加收藏", key=f"{key}_add_favorite"):
                    if to_favorite:
                        update_favorite_status([unfavorited_options[name] for name in to_favorite], True)
                        st.success(f"已收藏 {len(to_favorite)} 个账户")
                        st.session_state[f"{key}_show_favorite_manager"] = False
                        st.rerun()
            else:
                st.info("所有账户都已收藏")
        
        with col2:
            st.markdown("**已收藏账户**")
            favorited = [acc for acc in accounts if getattr(acc, 'is_favorite', False)]
            
            if favorited:
                favorited_options = {
                    f"⭐ {acc.name} ({acc.account_id_qc})": acc 
                    for acc in favorited
                }
                
                to_unfavorite = st.multiselect(
                    "选择要取消收藏的账户",
                    options=list(favorited_options.keys()),
                    key=f"{key}_to_unfavorite"
                )
                
                if st.button("❌ 取消收藏", key=f"{key}_remove_favorite"):
                    if to_unfavorite:
                        update_favorite_status([favorited_options[name] for name in to_unfavorite], False)
                        st.success(f"已取消收藏 {len(to_unfavorite)} 个账户")
                        st.session_state[f"{key}_show_favorite_manager"] = False
                        st.rerun()
            else:
                st.info("没有收藏的账户")
        
        if st.button("✅ 完成管理", key=f"{key}_finish_manage"):
            st.session_state[f"{key}_show_favorite_manager"] = False
            st.rerun()

def update_favorite_status(accounts: List, is_favorite: bool):
    """更新账户收藏状态"""
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount

        with database_session() as db:
            for account in accounts:
                # 重新查询账户对象以确保会话绑定
                db_account = db.query(AdAccount).filter(
                    AdAccount.id == account.id
                ).first()
                if db_account:
                    db_account.is_favorite = is_favorite
            db.commit()

        # 清除缓存
        if 'st' in globals():
            st.cache_data.clear()

    except Exception as e:
        if 'st' in globals():
            st.error(f"更新收藏状态失败: {e}")
        else:
            print(f"更新收藏状态失败: {e}")

def get_accounts_with_favorites():
    """获取包含收藏状态的账户列表"""
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount

        with database_session() as db:
            accounts = db.query(AdAccount).options(
                joinedload(AdAccount.principal)
            ).filter(AdAccount.status == 'active').all()

            # 确保对象与会话分离，避免会话问题
            db.expunge_all()

            return accounts

    except Exception as e:
        if 'st' in globals():
            st.error(f"获取账户列表失败: {e}")
        else:
            print(f"获取账户列表失败: {e}")
        return []

# 便捷函数
def create_single_account_selector(key: str, label: str = "选择广告账户", **kwargs):
    """创建单选账户选择器"""
    accounts = get_accounts_with_favorites()

    # 如果没有明确指定in_form，则尝试检测
    if 'in_form' not in kwargs:
        kwargs['in_form'] = _is_in_form()

    return create_unified_account_selector(
        accounts=accounts,
        key=key,
        label=label,
        multi_select=False,
        **kwargs
    )

def create_multi_account_selector(key: str, label: str = "选择广告账户", **kwargs):
    """创建多选账户选择器"""
    accounts = get_accounts_with_favorites()

    # 如果没有明确指定in_form，则尝试检测
    if 'in_form' not in kwargs:
        kwargs['in_form'] = _is_in_form()

    return create_unified_account_selector(
        accounts=accounts,
        key=key,
        label=label,
        multi_select=True,
        **kwargs
    )

# 兼容性函数 - 返回旧格式的选项字典
def get_account_options_dict(accounts: List = None) -> Dict[str, str]:
    """获取账户选项字典（兼容旧代码）"""
    if accounts is None:
        accounts = get_accounts_with_favorites()
    
    account_options = {}
    for account in accounts:
        is_favorite = getattr(account, 'is_favorite', False)
        star_icon = "⭐ " if is_favorite else ""
        display_name = f"{star_icon}{account.name} ({account.account_id_qc})"
        account_options[display_name] = account.account_id_qc
    
    return account_options
