#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 诊断Streamlit状态管理问题
依赖关系: web_ui.py, 全局账户选择器
清理条件: 问题修复后可删除
"""

import sys
import os
import re
from pathlib import Path
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_session_state_variables():
    """分析session_state变量使用情况"""
    print("🔍 分析session_state变量使用情况...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有session_state变量
        pattern = r"st\.session_state\[?['\"]([^'\"]+)['\"]?\]?"
        matches = re.findall(pattern, content)
        
        # 统计变量使用频次
        var_counts = defaultdict(int)
        for var in matches:
            var_counts[var] += 1
        
        print(f"📊 发现 {len(var_counts)} 个不同的session_state变量:")
        
        # 按使用频次排序
        sorted_vars = sorted(var_counts.items(), key=lambda x: x[1], reverse=True)
        
        for var, count in sorted_vars:
            print(f"  - {var}: {count} 次使用")
        
        # 检查可能的变量名冲突
        potential_conflicts = []
        batch_vars = [var for var in var_counts.keys() if 'batch' in var]
        single_vars = [var for var in var_counts.keys() if 'single' in var]
        global_vars = [var for var in var_counts.keys() if 'global' in var]
        
        print(f"\n📋 变量分类:")
        print(f"  - 批量复制相关: {len(batch_vars)} 个")
        print(f"  - 单账户复制相关: {len(single_vars)} 个")
        print(f"  - 全局相关: {len(global_vars)} 个")
        
        return var_counts
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return {}

def analyze_rerun_usage():
    """分析st.rerun()使用情况"""
    print("\n🔄 分析st.rerun()使用情况...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        rerun_locations = []
        for i, line in enumerate(lines, 1):
            if 'st.rerun()' in line:
                # 获取上下文
                context_start = max(0, i-3)
                context_end = min(len(lines), i+2)
                context = ''.join(lines[context_start:context_end])
                
                rerun_locations.append({
                    'line': i,
                    'content': line.strip(),
                    'context': context
                })
        
        print(f"📊 发现 {len(rerun_locations)} 个st.rerun()调用:")
        
        for loc in rerun_locations:
            print(f"  - 第{loc['line']}行: {loc['content']}")
        
        # 检查可能的问题模式
        problematic_patterns = []
        
        for loc in rerun_locations:
            context = loc['context']
            
            # 检查是否在表单提交后立即调用rerun
            if 'submitted' in context and 'st.rerun()' in context:
                problematic_patterns.append({
                    'line': loc['line'],
                    'issue': '表单提交后立即调用rerun',
                    'context': context
                })
            
            # 检查是否在条件判断中调用rerun
            if 'if ' in context and 'st.rerun()' in context:
                problematic_patterns.append({
                    'line': loc['line'],
                    'issue': '条件判断中调用rerun',
                    'context': context
                })
        
        if problematic_patterns:
            print(f"\n⚠️ 发现 {len(problematic_patterns)} 个可能的问题模式:")
            for pattern in problematic_patterns:
                print(f"  - 第{pattern['line']}行: {pattern['issue']}")
        
        return rerun_locations
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return []

def analyze_form_usage():
    """分析表单使用情况"""
    print("\n📝 分析表单使用情况...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有表单
        form_pattern = r'with st\.form\(["\']([^"\']+)["\']\):'
        forms = re.findall(form_pattern, content)
        
        print(f"📊 发现 {len(forms)} 个表单:")
        for form in forms:
            print(f"  - {form}")
        
        # 检查表单提交按钮
        submit_pattern = r'st\.form_submit_button\(["\']([^"\']+)["\']\)'
        submits = re.findall(submit_pattern, content)
        
        print(f"\n📊 发现 {len(submits)} 个提交按钮:")
        for submit in submits:
            print(f"  - {submit}")
        
        return forms, submits
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return [], []

def check_account_selector_integration():
    """检查账户选择器集成情况"""
    print("\n👥 检查账户选择器集成情况...")
    
    web_ui_path = project_root / 'web_ui.py'
    global_selector_path = project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_global_account_selector.py'
    
    try:
        # 检查web_ui.py中的集成
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            web_content = f.read()
        
        # 检查导入
        if 'render_global_account_selector' in web_content:
            print("✅ Web UI已导入全局账户选择器")
        else:
            print("❌ Web UI未导入全局账户选择器")
        
        # 检查调用
        if 'render_global_account_selector()' in web_content:
            print("✅ Web UI调用了全局账户选择器")
        else:
            print("❌ Web UI未调用全局账户选择器")
        
        # 检查全局选择器文件
        if global_selector_path.exists():
            print("✅ 全局账户选择器文件存在")
            
            with open(global_selector_path, 'r', encoding='utf-8') as f:
                selector_content = f.read()
            
            # 检查关键函数
            key_functions = [
                'get_global_selected_account',
                'set_global_selected_account',
                'render_global_account_selector',
                '_on_account_change'
            ]
            
            for func in key_functions:
                if f'def {func}(' in selector_content:
                    print(f"✅ 函数 {func} 存在")
                else:
                    print(f"❌ 函数 {func} 缺失")
        else:
            print("❌ 全局账户选择器文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def identify_potential_issues():
    """识别潜在问题"""
    print("\n🚨 识别潜在问题...")
    
    issues = []
    
    # 问题1: 检查批量复制的表单和rerun冲突
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找批量复制相关的表单提交和rerun
        for i, line in enumerate(lines, 1):
            if 'batch_select_source_accounts_form' in line:
                # 检查后续几行是否有rerun
                for j in range(i, min(i+20, len(lines))):
                    if 'st.rerun()' in lines[j]:
                        issues.append({
                            'type': '表单提交后立即rerun',
                            'line': j+1,
                            'description': '批量复制源账户选择表单提交后立即调用rerun可能导致页面跳转'
                        })
                        break
        
        # 问题2: 检查账户选择器的状态同步
        if 'global_selected_account' in ''.join(lines):
            print("✅ 发现全局账户选择器状态变量")
        else:
            issues.append({
                'type': '全局状态缺失',
                'line': 0,
                'description': '未发现全局账户选择器状态变量，可能导致账户选择失效'
            })
        
        # 问题3: 检查session_state变量初始化
        batch_vars = ['batch_replication_step', 'batch_found_campaigns', 'batch_selected_campaigns']
        for var in batch_vars:
            if f"'{var}' not in st.session_state" not in ''.join(lines):
                issues.append({
                    'type': '状态变量未初始化',
                    'line': 0,
                    'description': f'变量 {var} 可能未正确初始化'
                })
        
        if issues:
            print(f"⚠️ 发现 {len(issues)} 个潜在问题:")
            for issue in issues:
                print(f"  - {issue['type']} (第{issue['line']}行): {issue['description']}")
        else:
            print("✅ 未发现明显的潜在问题")
        
        return issues
        
    except Exception as e:
        print(f"❌ 问题识别失败: {e}")
        return []

def main():
    """主诊断函数"""
    print("🔧 Streamlit状态管理问题诊断")
    print("=" * 60)
    
    # 执行各项诊断
    var_counts = analyze_session_state_variables()
    rerun_locations = analyze_rerun_usage()
    forms, submits = analyze_form_usage()
    account_integration = check_account_selector_integration()
    issues = identify_potential_issues()
    
    print("\n" + "=" * 60)
    print("📊 诊断总结")
    print(f"  - Session State变量: {len(var_counts)} 个")
    print(f"  - st.rerun()调用: {len(rerun_locations)} 个")
    print(f"  - 表单数量: {len(forms)} 个")
    print(f"  - 提交按钮: {len(submits)} 个")
    print(f"  - 潜在问题: {len(issues)} 个")
    
    if issues:
        print("\n🎯 修复建议:")
        print("1. 检查表单提交后的rerun调用时机")
        print("2. 确保全局账户选择器状态正确同步")
        print("3. 验证session_state变量初始化")
        print("4. 优化多步骤流程的状态管理")
    else:
        print("\n✅ 未发现明显问题，可能需要运行时调试")
    
    return len(issues) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
