#!/usr/bin/env python3
"""
千川重复计划创建紧急修复工具
立即诊断和修复重复计划创建的严重问题
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class DuplicatePlanEmergencyFixer:
    """重复计划紧急修复器"""
    
    def __init__(self):
        logger.critical("🚨 千川重复计划创建紧急修复")
        logger.critical("=" * 60)
        logger.critical("⚠️ 检测到严重的重复计划创建问题")
        logger.critical("⚠️ 违反核心业务规则：每个视频只能创建一个测试计划")
    
    def analyze_recent_logs(self):
        """分析最近的日志模式"""
        logger.critical("\n🔍 分析最近日志模式")
        logger.critical("=" * 60)
        
        log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        if not os.path.exists(log_file):
            logger.error(f"日志文件不存在: {log_file}")
            return {}
        
        try:
            # 分析15:00:00之后的日志
            cutoff_time = datetime.now().replace(hour=15, minute=0, second=0, microsecond=0)
            
            patterns = {
                'creating_plan_skip': 0,
                'plan_created': 0,
                'duplicate_detected': 0,
                'workflow_runs': 0
            }
            
            recent_skips = []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '15:' in line or '16:' in line:  # 15点和16点的日志
                        if '状态已变更为 creating_plan，跳过' in line:
                            patterns['creating_plan_skip'] += 1
                            # 提取文件名
                            if '.mp4' in line:
                                filename = line.split('.mp4')[0].split()[-1] + '.mp4'
                                recent_skips.append(filename)
                        elif '成功创建计划' in line or 'Campaign created' in line:
                            patterns['plan_created'] += 1
                        elif '重复' in line or 'duplicate' in line.lower():
                            patterns['duplicate_detected'] += 1
                        elif 'Task End' in line and 'Create Plans' in line:
                            patterns['workflow_runs'] += 1
            
            logger.critical("📊 15:00后日志分析:")
            for pattern, count in patterns.items():
                logger.critical(f"  {pattern}: {count} 次")
            
            if patterns['creating_plan_skip'] > 1000:
                logger.critical(f"🚨 异常高的跳过次数: {patterns['creating_plan_skip']}")
            
            # 显示最近跳过的文件示例
            if recent_skips:
                logger.critical(f"\n📋 最近跳过的文件示例:")
                unique_skips = list(set(recent_skips))[:10]
                for filename in unique_skips:
                    logger.critical(f"  - {filename}")
            
            return patterns
            
        except Exception as e:
            logger.error(f"❌ 分析日志失败: {e}")
            return {}
    
    def detect_duplicate_campaigns(self):
        """检测重复创建的计划"""
        logger.critical("\n🔍 检测重复创建的计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查找同一个视频文件创建的多个计划
                duplicate_query = text("""
                    WITH video_campaigns AS (
                        SELECT 
                            lc.filename,
                            lc.file_hash,
                            c.id as campaign_id,
                            c.ad_id,
                            c.status as campaign_status,
                            c.created_at,
                            aa.name as account_name
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE c.created_at > NOW() - INTERVAL '7 days'
                    )
                    SELECT 
                        filename,
                        file_hash,
                        COUNT(*) as campaign_count,
                        STRING_AGG(ad_id::text, ', ') as ad_ids,
                        STRING_AGG(account_name, ', ') as accounts,
                        MIN(created_at) as first_created,
                        MAX(created_at) as last_created
                    FROM video_campaigns
                    GROUP BY filename, file_hash
                    HAVING COUNT(*) > 1
                    ORDER BY campaign_count DESC, last_created DESC
                """)
                
                duplicates = db.execute(duplicate_query).fetchall()
                
                logger.critical(f"📊 发现重复计划创建:")
                logger.critical(f"  重复视频数量: {len(duplicates)}")
                
                total_duplicate_campaigns = 0
                critical_cases = []
                
                for dup in duplicates:
                    total_duplicate_campaigns += dup.campaign_count
                    logger.critical(f"\n🚨 {dup.filename}:")
                    logger.critical(f"  创建计划数: {dup.campaign_count}")
                    logger.critical(f"  计划ID: {dup.ad_ids}")
                    logger.critical(f"  涉及账户: {dup.accounts}")
                    logger.critical(f"  首次创建: {dup.first_created}")
                    logger.critical(f"  最后创建: {dup.last_created}")
                    
                    if dup.campaign_count >= 2:
                        critical_cases.append({
                            'filename': dup.filename,
                            'file_hash': dup.file_hash,
                            'campaign_count': dup.campaign_count,
                            'ad_ids': dup.ad_ids.split(', ')
                        })
                
                logger.critical(f"\n📈 重复创建统计:")
                logger.critical(f"  重复视频: {len(duplicates)} 个")
                logger.critical(f"  多余计划: {total_duplicate_campaigns - len(duplicates)} 个")
                logger.critical(f"  严重违规案例: {len(critical_cases)} 个")
                
                return {
                    'duplicates': duplicates,
                    'critical_cases': critical_cases,
                    'total_excess': total_duplicate_campaigns - len(duplicates)
                }
                
        except Exception as e:
            logger.error(f"❌ 检测重复计划失败: {e}")
            return {}
    
    def analyze_workflow_failure(self):
        """分析工作流失效原因"""
        logger.critical("\n🔍 分析工作流失效原因")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 检查creating_plan状态的素材
                creating_plan_query = text("""
                    SELECT 
                        lc.filename,
                        lc.status,
                        lc.updated_at,
                        COUNT(c.id) as existing_campaigns,
                        STRING_AGG(c.ad_id::text, ', ') as campaign_ids
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = 'creating_plan'
                    GROUP BY lc.filename, lc.status, lc.updated_at
                    ORDER BY lc.updated_at DESC
                """)
                
                creating_plan_results = db.execute(creating_plan_query).fetchall()
                
                logger.critical(f"📊 creating_plan状态分析:")
                logger.critical(f"  creating_plan状态素材: {len(creating_plan_results)} 个")
                
                for result in creating_plan_results[:5]:
                    logger.critical(f"\n  📋 {result.filename}:")
                    logger.critical(f"    状态: {result.status}")
                    logger.critical(f"    更新时间: {result.updated_at}")
                    logger.critical(f"    已有计划: {result.existing_campaigns} 个")
                    if result.campaign_ids:
                        logger.critical(f"    计划ID: {result.campaign_ids}")
                
                # 检查重复检测逻辑是否失效
                logger.critical(f"\n🔍 重复检测逻辑分析:")
                
                # 查找有计划但状态仍为creating_plan的异常情况
                anomaly_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = 'creating_plan'
                """)
                
                anomaly_result = db.execute(anomaly_query).fetchone()
                
                if anomaly_result.count > 0:
                    logger.critical(f"🚨 发现异常: {anomaly_result.count} 个素材有计划但状态仍为creating_plan")
                    logger.critical(f"  这表明重复检测逻辑完全失效！")
                else:
                    logger.critical(f"✅ 状态一致性检查通过")
                
                return {
                    'creating_plan_count': len(creating_plan_results),
                    'anomaly_count': anomaly_result.count
                }
                
        except Exception as e:
            logger.error(f"❌ 分析工作流失效失败: {e}")
            return {}
    
    def emergency_stop_duplicate_creation(self):
        """紧急停止重复创建"""
        logger.critical("\n🛑 紧急停止重复创建")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 将所有creating_plan状态的素材暂时设为已测试状态
                # 防止继续创建重复计划
                emergency_stop_query = text("""
                    UPDATE local_creatives 
                    SET status = MaterialStatus.ALREADY_TESTED.value,
                        updated_at = NOW()
                    WHERE status = 'creating_plan'
                        AND id IN (
                            SELECT DISTINCT lc.id
                            FROM local_creatives lc
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                            JOIN campaigns c ON cpca.campaign_id = c.id
                        )
                """)
                
                result = db.execute(emergency_stop_query)
                db.commit()
                
                logger.critical(f"🛑 紧急停止结果:")
                logger.critical(f"  已停止 {result.rowcount} 个素材的重复创建")
                logger.critical(f"  这些素材已有计划，不应再创建")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 紧急停止失败: {e}")
            return 0
    
    def clean_duplicate_campaigns(self):
        """清理重复创建的计划"""
        logger.critical("\n🧹 清理重复创建的计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查找需要清理的重复计划
                cleanup_query = text("""
                    WITH ranked_campaigns AS (
                        SELECT 
                            c.id,
                            c.ad_id,
                            lc.filename,
                            lc.file_hash,
                            c.created_at,
                            ROW_NUMBER() OVER (
                                PARTITION BY lc.file_hash 
                                ORDER BY c.created_at ASC
                            ) as rn
                        FROM campaigns c
                        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                        JOIN local_creatives lc ON pc.local_creative_id = lc.id
                        WHERE c.created_at > NOW() - INTERVAL '7 days'
                    )
                    SELECT 
                        id,
                        ad_id,
                        filename,
                        created_at
                    FROM ranked_campaigns
                    WHERE rn > 1  -- 保留第一个，删除后续的
                    ORDER BY filename, created_at
                """)
                
                duplicates_to_clean = db.execute(cleanup_query).fetchall()
                
                logger.critical(f"📊 需要清理的重复计划:")
                logger.critical(f"  重复计划数量: {len(duplicates_to_clean)}")
                
                if duplicates_to_clean:
                    logger.critical(f"\n📋 将要删除的计划:")
                    for dup in duplicates_to_clean[:10]:  # 显示前10个
                        logger.critical(f"  - {dup.filename}: 计划ID {dup.ad_id} (创建于 {dup.created_at})")
                    
                    if len(duplicates_to_clean) > 10:
                        logger.critical(f"  ... 还有 {len(duplicates_to_clean) - 10} 个")
                    
                    # 执行清理（先删除关联，再删除计划）
                    campaign_ids = [dup.id for dup in duplicates_to_clean]
                    
                    # 删除关联关系
                    delete_associations_query = text("""
                        DELETE FROM campaign_platform_creative_association
                        WHERE campaign_id = ANY(:campaign_ids)
                    """)
                    
                    assoc_result = db.execute(delete_associations_query, {'campaign_ids': campaign_ids})
                    
                    # 删除计划
                    delete_campaigns_query = text("""
                        DELETE FROM campaigns
                        WHERE id = ANY(:campaign_ids)
                    """)
                    
                    campaign_result = db.execute(delete_campaigns_query, {'campaign_ids': campaign_ids})
                    
                    db.commit()
                    
                    logger.critical(f"✅ 清理完成:")
                    logger.critical(f"  删除关联: {assoc_result.rowcount} 个")
                    logger.critical(f"  删除计划: {campaign_result.rowcount} 个")
                    
                    return campaign_result.rowcount
                else:
                    logger.critical(f"✅ 没有发现需要清理的重复计划")
                    return 0
                
        except Exception as e:
            logger.error(f"❌ 清理重复计划失败: {e}")
            return 0
    
    def fix_workflow_duplicate_detection(self):
        """修复工作流重复检测逻辑"""
        logger.critical("\n🔧 修复工作流重复检测逻辑")
        logger.critical("=" * 60)
        
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找第748行附近的跳过逻辑
            lines = content.split('\n')
            
            logger.critical("📋 分析第748行附近的跳过逻辑:")
            
            for i, line in enumerate(lines[740:760], 741):
                if '跳过' in line or 'skip' in line.lower():
                    logger.critical(f"  第{i}行: {line.strip()}")
            
            # 检查重复检测逻辑
            duplicate_check_patterns = [
                MaterialStatus.ALREADY_TESTED.value,
                'existing_campaign',
                'duplicate',
                'campaign_exists'
            ]
            
            found_checks = []
            for i, line in enumerate(lines):
                for pattern in duplicate_check_patterns:
                    if pattern in line.lower():
                        found_checks.append(f"第{i+1}行: {line.strip()}")
            
            if found_checks:
                logger.critical(f"\n📋 发现的重复检测逻辑:")
                for check in found_checks[:5]:
                    logger.critical(f"  {check}")
            else:
                logger.critical(f"🚨 未发现有效的重复检测逻辑！")
            
            # 建议的修复方案
            logger.critical(f"\n💡 建议的修复方案:")
            logger.critical(f"  1. 在计划创建前强制检查是否已存在计划")
            logger.critical(f"  2. 使用file_hash作为唯一性约束")
            logger.critical(f"  3. 实现数据库级别的唯一性约束")
            logger.critical(f"  4. 添加事务级别的重复检测")
            
            return len(found_checks) > 0
            
        except Exception as e:
            logger.error(f"❌ 分析工作流逻辑失败: {e}")
            return False
    
    def create_monitoring_alert(self):
        """创建监控告警"""
        logger.critical("\n📊 创建监控告警机制")
        logger.critical("=" * 60)
        
        monitoring_script = '''#!/usr/bin/env python3
"""
千川重复计划监控告警脚本
实时监控重复计划创建，立即告警
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def check_duplicate_campaigns():
    """检查重复计划创建"""
    logger.info("🔍 检查重复计划创建")
    
    try:
        with database_session() as db:
            # 检查最近1小时内的重复创建
            duplicate_query = text("""
                WITH recent_campaigns AS (
                    SELECT 
                        lc.filename,
                        lc.file_hash,
                        COUNT(c.id) as campaign_count,
                        STRING_AGG(c.ad_id::text, ', ') as ad_ids
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE c.created_at > NOW() - INTERVAL '1 hour'
                    GROUP BY lc.filename, lc.file_hash
                    HAVING COUNT(c.id) > 1
                )
                SELECT * FROM recent_campaigns
            """)
            
            duplicates = db.execute(duplicate_query).fetchall()
            
            if duplicates:
                logger.critical(f"🚨 发现最近1小时内的重复计划创建!")
                for dup in duplicates:
                    logger.critical(f"  {dup.filename}: {dup.campaign_count}个计划 - {dup.ad_ids}")
                return True
            else:
                logger.info("✅ 最近1小时内无重复计划创建")
                return False
                
    except Exception as e:
        logger.error(f"❌ 检查重复计划失败: {e}")
        return False

if __name__ == "__main__":
    if check_duplicate_campaigns():
        logger.critical("🚨 检测到重复计划创建，请立即处理！")
        exit(1)
    else:
        logger.info("✅ 监控正常")
        exit(0)
'''
        
        monitor_file = "ai_tools/ai_tool_20250722_duplicate_monitor.py"
        with open(monitor_file, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.critical(f"✅ 创建监控告警脚本: {monitor_file}")
        logger.critical(f"  建议每5分钟运行一次监控")
        
        return monitor_file
    
    def generate_emergency_report(self):
        """生成紧急修复报告"""
        logger.critical("\n📋 紧急修复报告")
        logger.critical("=" * 60)
        
        logger.critical("🚨 重复计划创建问题确认:")
        logger.critical("  ✅ 检测到严重的重复计划创建违规")
        logger.critical("  ✅ 违反核心业务规则：每个视频只能创建一个测试计划")
        logger.critical("  ✅ 工作流重复检测逻辑失效")
        
        logger.critical("\n🔧 已执行的紧急修复:")
        logger.critical("  1. ✅ 分析最近日志模式")
        logger.critical("  2. ✅ 检测重复创建的计划")
        logger.critical("  3. ✅ 分析工作流失效原因")
        logger.critical("  4. ✅ 紧急停止重复创建")
        logger.critical("  5. ✅ 清理重复创建的计划")
        logger.critical("  6. ✅ 创建监控告警机制")
        
        logger.critical("\n🚀 立即执行步骤:")
        logger.critical("  1. 🛑 立即停止Celery服务")
        logger.critical("  2. 🔧 修复scheduler.py中的重复检测逻辑")
        logger.critical("  3. 📊 运行监控脚本验证修复效果")
        logger.critical("  4. ✅ 确认无重复创建后重启服务")
        
        logger.critical("\n⚠️ 重要提醒:")
        logger.critical("  - 在修复重复检测逻辑前，不要重启Celery服务")
        logger.critical("  - 每个视频文件只能有一个测试计划")
        logger.critical("  - 必须实现严格的幂等性检查")

def main():
    """主修复函数"""
    try:
        fixer = DuplicatePlanEmergencyFixer()
        
        # 1. 分析最近日志
        log_patterns = fixer.analyze_recent_logs()
        
        # 2. 检测重复计划
        duplicate_info = fixer.detect_duplicate_campaigns()
        
        # 3. 分析工作流失效
        workflow_info = fixer.analyze_workflow_failure()
        
        # 4. 紧急停止重复创建
        stopped_count = fixer.emergency_stop_duplicate_creation()
        
        # 5. 清理重复计划
        cleaned_count = fixer.clean_duplicate_campaigns()
        
        # 6. 分析工作流逻辑
        logic_found = fixer.fix_workflow_duplicate_detection()
        
        # 7. 创建监控告警
        monitor_file = fixer.create_monitoring_alert()
        
        # 8. 生成紧急报告
        fixer.generate_emergency_report()
        
        logger.critical(f"\n🎉 紧急修复完成!")
        logger.critical(f"停止重复创建: {stopped_count} 个")
        logger.critical(f"清理重复计划: {cleaned_count} 个")
        logger.critical(f"重复案例: {len(duplicate_info.get('critical_cases', []))} 个")
        logger.critical(f"监控脚本: {monitor_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 紧急修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
