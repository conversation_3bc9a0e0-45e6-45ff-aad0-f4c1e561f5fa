#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 千川项目保护机制 - 备份、回滚、恢复系统
依赖关系: 与git_auto_commit.py配合使用，依赖PostgreSQL
清理条件: 项目不再需要保护机制时
"""

import os
import sys
import subprocess
import json
import yaml
import shutil
import psycopg2
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
import zipfile
import hashlib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class QianchuanProjectProtection:
    """千川项目保护机制"""
    
    def __init__(self):
        self.project_root = project_root
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # 保护配置
        self.protection_config = {
            "backup_strategy": {
                "daily_backup": True,
                "weekly_full_backup": True,
                "monthly_archive": True,
                "retention_days": 30,
                "max_backups": 50
            },
            "critical_files": {
                "config/settings.yml": {
                    "backup_before_change": True,
                    "require_confirmation": True,
                    "max_versions": 10
                },
                "src/qianchuan_aw/database/models.py": {
                    "backup_before_change": True,
                    "require_confirmation": False,
                    "max_versions": 5
                },
                "src/qianchuan_aw/sdk_qc/client.py": {
                    "backup_before_change": True,
                    "require_confirmation": False,
                    "max_versions": 5
                },
                "requirements.txt": {
                    "backup_before_change": True,
                    "require_confirmation": True,
                    "max_versions": 10
                }
            },
            "database_backup": {
                "enabled": True,
                "daily_backup": True,
                "retention_days": 14,
                "compress": True
            }
        }
        
        # 备份目录结构
        self.backup_root = self.project_root / "backups"
        self.backup_dirs = {
            "daily": self.backup_root / "daily",
            "weekly": self.backup_root / "weekly", 
            "monthly": self.backup_root / "monthly",
            "critical": self.backup_root / "critical_files",
            "database": self.backup_root / "database",
            "emergency": self.backup_root / "emergency"
        }
        
        # 创建备份目录
        for backup_dir in self.backup_dirs.values():
            backup_dir.mkdir(parents=True, exist_ok=True)
    
    def _load_config(self) -> Dict:
        """加载项目配置"""
        config_path = self.project_root / "config" / "settings.yml"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('project_protection')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "project_protection.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def create_full_backup(self, backup_type: str = "manual") -> Dict:
        """创建完整项目备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"qianchuan_full_{backup_type}_{timestamp}"
            
            # 选择备份目录
            if backup_type == "daily":
                backup_dir = self.backup_dirs["daily"]
            elif backup_type == "weekly":
                backup_dir = self.backup_dirs["weekly"]
            elif backup_type == "monthly":
                backup_dir = self.backup_dirs["monthly"]
            else:
                backup_dir = self.backup_dirs["emergency"]
            
            backup_path = backup_dir / f"{backup_name}.zip"
            
            # 创建ZIP备份
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 备份核心文件
                core_patterns = [
                    "src/**/*.py",
                    "config/*.yml",
                    "config/*.json",
                    "tools/*.py",
                    "ai_tools/**/*",
                    "ai_templates/**/*",
                    "main.py",
                    "web_ui.py",
                    "run_celery_*.py",
                    "requirements.txt",
                    "README.md"
                ]
                
                # 排除模式
                exclude_patterns = [
                    "**/__pycache__/**",
                    "*.pyc",
                    "logs/**",
                    "debug_screenshots/**",
                    "ai_temp/**",
                    "backups/**",
                    "snapshots/**",
                    "workflow_assets/**"
                ]
                
                for pattern in core_patterns:
                    for file_path in self.project_root.glob(pattern):
                        if file_path.is_file():
                            # 检查是否应该排除
                            relative_path = file_path.relative_to(self.project_root)
                            should_exclude = any(
                                relative_path.match(exclude_pattern)
                                for exclude_pattern in exclude_patterns
                            )
                            
                            if not should_exclude:
                                zipf.write(file_path, relative_path)
            
            # 计算备份文件哈希
            backup_hash = self._calculate_file_hash(backup_path)
            
            # 记录备份信息
            backup_info = {
                "timestamp": timestamp,
                "type": backup_type,
                "path": str(backup_path),
                "size": backup_path.stat().st_size,
                "hash": backup_hash,
                "created_at": datetime.now().isoformat()
            }
            
            # 保存备份信息
            info_path = backup_path.with_suffix('.json')
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"完整备份创建成功: {backup_path}")
            
            return {
                "success": True,
                "backup_path": str(backup_path),
                "backup_info": backup_info
            }
            
        except Exception as e:
            self.logger.error(f"创建完整备份失败: {e}")
            return {"success": False, "error": str(e)}
    
    def backup_critical_file(self, file_path: str) -> Dict:
        """备份关键文件"""
        try:
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                return {"success": False, "error": f"文件不存在: {file_path}"}
            
            # 获取文件配置
            file_config = self.protection_config["critical_files"].get(file_path, {})
            max_versions = file_config.get("max_versions", 5)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{file_path_obj.name}_{timestamp}"
            
            # 创建文件特定的备份目录
            file_backup_dir = self.backup_dirs["critical"] / file_path_obj.parent.name
            file_backup_dir.mkdir(parents=True, exist_ok=True)
            
            backup_path = file_backup_dir / backup_name
            
            # 复制文件
            shutil.copy2(file_path_obj, backup_path)
            
            # 清理旧版本
            self._cleanup_old_versions(file_backup_dir, file_path_obj.name, max_versions)
            
            self.logger.info(f"关键文件备份成功: {file_path} -> {backup_path}")
            
            return {
                "success": True,
                "backup_path": str(backup_path),
                "original_file": file_path
            }
            
        except Exception as e:
            self.logger.error(f"备份关键文件失败: {e}")
            return {"success": False, "error": str(e)}
    
    def backup_database(self) -> Dict:
        """备份PostgreSQL数据库"""
        try:
            if not self.protection_config["database_backup"]["enabled"]:
                return {"success": False, "error": "数据库备份功能已禁用"}
            
            # 获取数据库配置
            db_config = self.config.get("database", {}).get("postgresql", {})
            if not db_config:
                return {"success": False, "error": "未找到数据库配置"}
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"qianchuan_db_{timestamp}.sql"
            backup_path = self.backup_dirs["database"] / backup_name
            
            # 构建pg_dump命令
            cmd = [
                "pg_dump",
                f"--host={db_config.get('host', 'localhost')}",
                f"--port={db_config.get('port', 5432)}",
                f"--username={db_config.get('username', 'postgres')}",
                f"--dbname={db_config.get('dbname', 'qianchuan_analytics')}",
                "--no-password",
                "--verbose",
                "--clean",
                "--if-exists",
                f"--file={backup_path}"
            ]
            
            # 设置环境变量
            env = os.environ.copy()
            if db_config.get('password'):
                env['PGPASSWORD'] = db_config['password']
            
            # 执行备份
            result = subprocess.run(
                cmd,
                env=env,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                return {
                    "success": False,
                    "error": f"数据库备份失败: {result.stderr}"
                }
            
            # 压缩备份文件
            if self.protection_config["database_backup"]["compress"]:
                compressed_path = backup_path.with_suffix('.sql.gz')
                with open(backup_path, 'rb') as f_in:
                    import gzip
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                # 删除未压缩文件
                backup_path.unlink()
                backup_path = compressed_path
            
            # 清理旧备份
            self._cleanup_old_database_backups()
            
            self.logger.info(f"数据库备份成功: {backup_path}")
            
            return {
                "success": True,
                "backup_path": str(backup_path),
                "size": backup_path.stat().st_size
            }
            
        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _cleanup_old_versions(self, backup_dir: Path, file_name: str, max_versions: int):
        """清理旧版本备份"""
        # 获取所有相关备份文件
        backup_files = []
        for backup_file in backup_dir.glob(f"{file_name}_*"):
            if backup_file.is_file():
                backup_files.append(backup_file)
        
        # 按修改时间排序
        backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        
        # 删除超出限制的文件
        for old_backup in backup_files[max_versions:]:
            try:
                old_backup.unlink()
                self.logger.info(f"删除旧备份: {old_backup}")
            except Exception as e:
                self.logger.warning(f"删除旧备份失败: {old_backup} - {e}")
    
    def _cleanup_old_database_backups(self):
        """清理旧数据库备份"""
        retention_days = self.protection_config["database_backup"]["retention_days"]
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        for backup_file in self.backup_dirs["database"].glob("qianchuan_db_*.sql*"):
            if backup_file.is_file():
                file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                if file_time < cutoff_date:
                    try:
                        backup_file.unlink()
                        self.logger.info(f"删除过期数据库备份: {backup_file}")
                    except Exception as e:
                        self.logger.warning(f"删除过期数据库备份失败: {backup_file} - {e}")
    
    def list_backups(self, backup_type: Optional[str] = None) -> Dict:
        """列出备份文件"""
        backups = {}
        
        dirs_to_scan = [backup_type] if backup_type else self.backup_dirs.keys()
        
        for dir_name in dirs_to_scan:
            if dir_name not in self.backup_dirs:
                continue
                
            backup_dir = self.backup_dirs[dir_name]
            backups[dir_name] = []
            
            for backup_file in backup_dir.glob("*.zip"):
                if backup_file.is_file():
                    info_file = backup_file.with_suffix('.json')
                    backup_info = {
                        "name": backup_file.name,
                        "path": str(backup_file),
                        "size": backup_file.stat().st_size,
                        "created": datetime.fromtimestamp(backup_file.stat().st_ctime).isoformat()
                    }
                    
                    # 加载详细信息
                    if info_file.exists():
                        try:
                            with open(info_file, 'r', encoding='utf-8') as f:
                                detailed_info = json.load(f)
                                backup_info.update(detailed_info)
                        except Exception:
                            pass
                    
                    backups[dir_name].append(backup_info)
            
            # 按创建时间排序
            backups[dir_name].sort(key=lambda x: x["created"], reverse=True)
        
        return backups

    def restore_from_backup(self, backup_path: str, target_dir: Optional[str] = None) -> Dict:
        """从备份恢复项目"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                return {"success": False, "error": f"备份文件不存在: {backup_path}"}

            if target_dir:
                restore_dir = Path(target_dir)
            else:
                # 创建临时恢复目录
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                restore_dir = self.project_root.parent / f"qianchuan_restore_{timestamp}"

            restore_dir.mkdir(parents=True, exist_ok=True)

            # 解压备份文件
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(restore_dir)

            self.logger.info(f"备份恢复成功: {backup_path} -> {restore_dir}")

            return {
                "success": True,
                "restore_path": str(restore_dir),
                "backup_file": backup_path
            }

        except Exception as e:
            self.logger.error(f"备份恢复失败: {e}")
            return {"success": False, "error": str(e)}

    def emergency_rollback(self, target_commit: Optional[str] = None) -> Dict:
        """紧急回滚到指定提交"""
        try:
            # 创建紧急备份
            emergency_backup = self.create_full_backup("emergency")
            if not emergency_backup["success"]:
                return {"success": False, "error": "创建紧急备份失败"}

            # 获取目标提交
            if not target_commit:
                # 获取上一个提交
                result = subprocess.run(
                    ["git", "rev-parse", "HEAD~1"],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True
                )

                if result.returncode != 0:
                    return {"success": False, "error": "无法获取上一个提交"}

                target_commit = result.stdout.strip()

            # 执行回滚
            result = subprocess.run(
                ["git", "reset", "--hard", target_commit],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                return {"success": False, "error": f"Git回滚失败: {result.stderr}"}

            self.logger.info(f"紧急回滚成功: {target_commit}")

            return {
                "success": True,
                "target_commit": target_commit,
                "emergency_backup": emergency_backup["backup_path"]
            }

        except Exception as e:
            self.logger.error(f"紧急回滚失败: {e}")
            return {"success": False, "error": str(e)}

    def verify_project_integrity(self) -> Dict:
        """验证项目完整性"""
        checks = {
            "critical_files": {"passed": True, "missing": []},
            "python_syntax": {"passed": True, "errors": []},
            "config_files": {"passed": True, "errors": []},
            "database_connection": {"passed": True, "error": None},
            "git_status": {"passed": True, "error": None},
            "overall_passed": True
        }

        # 检查关键文件
        for file_path in self.protection_config["critical_files"]:
            if not (self.project_root / file_path).exists():
                checks["critical_files"]["passed"] = False
                checks["critical_files"]["missing"].append(file_path)
                checks["overall_passed"] = False

        # 检查Python语法
        for py_file in self.project_root.rglob("*.py"):
            if "ai_temp" in str(py_file) or "__pycache__" in str(py_file):
                continue

            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    compile(f.read(), str(py_file), 'exec')
            except SyntaxError as e:
                checks["python_syntax"]["passed"] = False
                checks["python_syntax"]["errors"].append(f"{py_file}: {e}")
                checks["overall_passed"] = False

        # 检查配置文件
        config_files = ["config/settings.yml", "config/banned_terms.yml"]
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                except yaml.YAMLError as e:
                    checks["config_files"]["passed"] = False
                    checks["config_files"]["errors"].append(f"{config_file}: {e}")
                    checks["overall_passed"] = False

        # 检查数据库连接
        try:
            db_config = self.config.get("database", {}).get("postgresql", {})
            if db_config:
                conn = psycopg2.connect(
                    host=db_config.get('host', 'localhost'),
                    port=db_config.get('port', 5432),
                    database=db_config.get('dbname', 'qianchuan_analytics'),
                    user=db_config.get('username', 'postgres'),
                    password=db_config.get('password', '')
                )
                conn.close()
        except Exception as e:
            checks["database_connection"]["passed"] = False
            checks["database_connection"]["error"] = str(e)
            checks["overall_passed"] = False

        # 检查Git状态
        try:
            result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                checks["git_status"]["passed"] = False
                checks["git_status"]["error"] = "Git状态检查失败"
                checks["overall_passed"] = False

        except Exception as e:
            checks["git_status"]["passed"] = False
            checks["git_status"]["error"] = str(e)
            checks["overall_passed"] = False

        return checks

    def scheduled_maintenance(self) -> Dict:
        """定期维护任务"""
        results = {
            "daily_backup": None,
            "database_backup": None,
            "cleanup_old_backups": None,
            "integrity_check": None
        }

        # 每日备份
        if self.protection_config["backup_strategy"]["daily_backup"]:
            results["daily_backup"] = self.create_full_backup("daily")

        # 数据库备份
        if self.protection_config["database_backup"]["daily_backup"]:
            results["database_backup"] = self.backup_database()

        # 清理旧备份
        results["cleanup_old_backups"] = self._cleanup_old_backups()

        # 完整性检查
        results["integrity_check"] = self.verify_project_integrity()

        return results

    def _cleanup_old_backups(self) -> Dict:
        """清理过期备份"""
        try:
            retention_days = self.protection_config["backup_strategy"]["retention_days"]
            max_backups = self.protection_config["backup_strategy"]["max_backups"]
            cutoff_date = datetime.now() - timedelta(days=retention_days)

            cleaned_count = 0

            for backup_type, backup_dir in self.backup_dirs.items():
                if backup_type == "emergency":  # 紧急备份不自动清理
                    continue

                backup_files = []
                for backup_file in backup_dir.glob("*.zip"):
                    if backup_file.is_file():
                        backup_files.append(backup_file)

                # 按修改时间排序
                backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                # 删除过期文件
                for backup_file in backup_files:
                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date or len(backup_files) > max_backups:
                        try:
                            backup_file.unlink()
                            # 同时删除信息文件
                            info_file = backup_file.with_suffix('.json')
                            if info_file.exists():
                                info_file.unlink()
                            cleaned_count += 1
                            self.logger.info(f"删除过期备份: {backup_file}")
                        except Exception as e:
                            self.logger.warning(f"删除过期备份失败: {backup_file} - {e}")

            return {"success": True, "cleaned_count": cleaned_count}

        except Exception as e:
            return {"success": False, "error": str(e)}

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='千川项目保护机制')
    parser.add_argument('action', choices=[
        'backup', 'restore', 'rollback', 'check', 'list', 'maintenance'
    ], help='执行的操作')
    parser.add_argument('--type', choices=['daily', 'weekly', 'monthly', 'manual'],
                       default='manual', help='备份类型')
    parser.add_argument('--file', help='要备份的文件路径')
    parser.add_argument('--backup-path', help='备份文件路径')
    parser.add_argument('--target-dir', help='恢复目标目录')
    parser.add_argument('--commit', help='回滚目标提交')

    args = parser.parse_args()

    protection = QianchuanProjectProtection()

    if args.action == 'backup':
        if args.file:
            result = protection.backup_critical_file(args.file)
        else:
            result = protection.create_full_backup(args.type)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'restore':
        if not args.backup_path:
            print("错误: 需要指定备份文件路径 --backup-path")
            return
        result = protection.restore_from_backup(args.backup_path, args.target_dir)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'rollback':
        result = protection.emergency_rollback(args.commit)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'check':
        result = protection.verify_project_integrity()
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'list':
        result = protection.list_backups(args.type)
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'maintenance':
        result = protection.scheduled_maintenance()
        print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
