#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步浏览器进程池适配器
将异步浏览器进程池适配为同步接口，供现有同步代码使用
"""

import asyncio
import threading
import time
from contextlib import contextmanager
from typing import Dict, Any, Optional
from qianchuan_aw.utils.logger import logger


class SyncBrowserPoolAdapter:
    """同步浏览器进程池适配器"""
    
    def __init__(self):
        self._loop = None
        self._loop_thread = None
        self._pool = None
        self._lock = threading.Lock()
    
    def _ensure_event_loop(self):
        """确保事件循环运行"""
        with self._lock:
            if self._loop is None or self._loop.is_closed():
                # 创建新的事件循环线程
                self._loop = asyncio.new_event_loop()
                self._loop_thread = threading.Thread(
                    target=self._run_event_loop,
                    daemon=True
                )
                self._loop_thread.start()
                
                # 等待事件循环启动
                time.sleep(0.1)
    
    def _run_event_loop(self):
        """在独立线程中运行事件循环"""
        asyncio.set_event_loop(self._loop)
        try:
            self._loop.run_forever()
        except Exception as e:
            logger.error(f"事件循环异常: {e}")
        finally:
            self._loop.close()
    
    async def _get_browser_pool(self):
        """获取浏览器进程池"""
        if self._pool is None:
            from qianchuan_aw.utils.browser_process_pool import get_browser_pool
            self._pool = await get_browser_pool()
        return self._pool
    
    def _run_async(self, coro):
        """在事件循环中运行协程"""
        self._ensure_event_loop()
        
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result(timeout=30)  # 30秒超时
    
    @contextmanager
    def get_browser_context(self, principal_name: str, app_settings: Dict[str, Any]):
        """获取浏览器上下文（同步接口）"""
        logger.debug(f"🔄 同步适配器为 '{principal_name}' 创建浏览器上下文")
        
        context = None
        try:
            # 异步获取浏览器上下文
            async def _get_context():
                pool = await self._get_browser_pool()
                return await pool.get_browser_context(principal_name).__aenter__()
            
            context = self._run_async(_get_context())
            logger.debug(f"✅ 同步适配器成功创建浏览器上下文")
            
            # 创建同步页面适配器
            sync_context = SyncBrowserContextAdapter(context, self._loop)
            yield sync_context
            
        except Exception as e:
            logger.error(f"❌ 同步适配器创建浏览器上下文失败: {e}")
            raise
        
        finally:
            if context:
                try:
                    # 异步关闭上下文
                    async def _close_context():
                        pool = await self._get_browser_pool()
                        await pool.get_browser_context(principal_name).__aexit__(None, None, None)
                    
                    self._run_async(_close_context())
                    logger.debug(f"✅ 同步适配器成功关闭浏览器上下文")
                    
                except Exception as e:
                    logger.error(f"❌ 关闭浏览器上下文失败: {e}")
    
    def cleanup(self):
        """清理资源"""
        with self._lock:
            if self._loop and not self._loop.is_closed():
                self._loop.call_soon_threadsafe(self._loop.stop)
                
            if self._loop_thread and self._loop_thread.is_alive():
                self._loop_thread.join(timeout=5)


class SyncBrowserContextAdapter:
    """同步浏览器上下文适配器"""
    
    def __init__(self, async_context, event_loop):
        self._async_context = async_context
        self._loop = event_loop
        self._pages = []
    
    def new_page(self):
        """创建新页面（同步接口）"""
        logger.debug("🔄 同步适配器创建新页面")
        
        try:
            # 异步创建页面
            async def _create_page():
                return await self._async_context.new_page()
            
            future = asyncio.run_coroutine_threadsafe(_create_page(), self._loop)
            async_page = future.result(timeout=30)
            
            # 创建同步页面适配器
            sync_page = SyncPageAdapter(async_page, self._loop)
            self._pages.append(sync_page)
            
            logger.debug("✅ 同步适配器成功创建新页面")
            return sync_page
            
        except Exception as e:
            logger.error(f"❌ 同步适配器创建页面失败: {e}")
            raise
    
    def close(self):
        """关闭上下文"""
        try:
            # 关闭所有页面
            for page in self._pages:
                page.close()
            
            # 异步关闭上下文
            async def _close_context():
                await self._async_context.close()
            
            future = asyncio.run_coroutine_threadsafe(_close_context(), self._loop)
            future.result(timeout=10)
            
            logger.debug("✅ 同步适配器成功关闭上下文")
            
        except Exception as e:
            logger.error(f"❌ 关闭上下文失败: {e}")


class SyncPageAdapter:
    """同步页面适配器"""
    
    def __init__(self, async_page, event_loop):
        self._async_page = async_page
        self._loop = event_loop
    
    def _run_async(self, coro):
        """在事件循环中运行协程"""
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result(timeout=30)
    
    def goto(self, url: str, **kwargs):
        """导航到URL"""
        return self._run_async(self._async_page.goto(url, **kwargs))
    
    def click(self, selector: str, **kwargs):
        """点击元素"""
        return self._run_async(self._async_page.click(selector, **kwargs))
    
    def fill(self, selector: str, value: str, **kwargs):
        """填充输入框"""
        return self._run_async(self._async_page.fill(selector, value, **kwargs))
    
    def wait_for_selector(self, selector: str, **kwargs):
        """等待选择器"""
        return self._run_async(self._async_page.wait_for_selector(selector, **kwargs))
    
    def locator(self, selector: str):
        """获取定位器"""
        # 返回同步定位器适配器
        async_locator = self._async_page.locator(selector)
        return SyncLocatorAdapter(async_locator, self._loop)
    
    def set_default_timeout(self, timeout: float):
        """设置默认超时"""
        self._async_page.set_default_timeout(timeout)
    
    def title(self):
        """获取页面标题"""
        return self._run_async(self._async_page.title())
    
    def url(self):
        """获取页面URL"""
        return self._async_page.url
    
    def close(self):
        """关闭页面"""
        try:
            self._run_async(self._async_page.close())
            logger.debug("✅ 同步适配器成功关闭页面")
        except Exception as e:
            logger.error(f"❌ 关闭页面失败: {e}")


class SyncLocatorAdapter:
    """同步定位器适配器"""
    
    def __init__(self, async_locator, event_loop):
        self._async_locator = async_locator
        self._loop = event_loop
    
    def _run_async(self, coro):
        """在事件循环中运行协程"""
        future = asyncio.run_coroutine_threadsafe(coro, self._loop)
        return future.result(timeout=30)
    
    def click(self, **kwargs):
        """点击元素"""
        return self._run_async(self._async_locator.click(**kwargs))
    
    def fill(self, value: str, **kwargs):
        """填充输入框"""
        return self._run_async(self._async_locator.fill(value, **kwargs))
    
    def is_visible(self, **kwargs):
        """检查元素是否可见"""
        return self._run_async(self._async_locator.is_visible(**kwargs))
    
    def wait_for(self, **kwargs):
        """等待元素"""
        return self._run_async(self._async_locator.wait_for(**kwargs))
    
    def text_content(self, **kwargs):
        """获取文本内容"""
        return self._run_async(self._async_locator.text_content(**kwargs))


# 全局同步适配器实例
_sync_adapter = None

def get_sync_browser_adapter() -> SyncBrowserPoolAdapter:
    """获取全局同步浏览器适配器"""
    global _sync_adapter
    
    if _sync_adapter is None:
        _sync_adapter = SyncBrowserPoolAdapter()
    
    return _sync_adapter


def cleanup_sync_browser_adapter():
    """清理全局同步浏览器适配器"""
    global _sync_adapter
    
    if _sync_adapter:
        _sync_adapter.cleanup()
        _sync_adapter = None


# 注册清理函数
import atexit
atexit.register(cleanup_sync_browser_adapter)
