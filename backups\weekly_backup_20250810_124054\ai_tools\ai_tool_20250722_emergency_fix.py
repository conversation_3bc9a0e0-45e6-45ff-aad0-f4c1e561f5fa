#!/usr/bin/env python3
"""
千川系统紧急修复工具
基于系统诊断结果，立即修复卡住的素材和异常状态
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class EmergencySystemFixer:
    """系统紧急修复器"""
    
    def __init__(self):
        self.fix_results = {
            'creating_plan_fixed': 0,
            'pending_plan_fixed': 0,
            'stuck_materials_fixed': 0,
            'total_affected': 0
        }
    
    def backup_database(self):
        """创建数据库备份"""
        logger.info("🔧 创建数据库备份...")
        
        backup_file = f"backup_emergency_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        
        try:
            # 这里应该执行实际的备份命令
            # 由于环境限制，我们只记录备份意图
            logger.info(f"✅ 数据库备份计划: {backup_file}")
            logger.info("📋 建议手动执行: pg_dump qianchuan > " + backup_file)
            return backup_file
            
        except Exception as e:
            logger.error(f"❌ 备份失败: {e}")
            return None
    
    def fix_stuck_creating_plan_materials(self):
        """修复卡住的creating_plan状态素材"""
        logger.info("🔧 修复卡住的creating_plan状态素材...")
        
        try:
            with database_session() as db:
                # 查询卡住的creating_plan素材
                query_stuck = text("""
                    SELECT id, filename, updated_at,
                           EXTRACT(EPOCH FROM (NOW() - updated_at))/3600 as hours_stuck
                    FROM local_creatives
                    WHERE status = 'creating_plan' 
                        AND updated_at < NOW() - INTERVAL '2 hours'
                    ORDER BY updated_at ASC
                """)
                
                stuck_materials = db.execute(query_stuck).fetchall()
                
                if stuck_materials:
                    logger.info(f"📊 发现 {len(stuck_materials)} 个卡住的creating_plan素材:")
                    for material in stuck_materials[:5]:  # 只显示前5个
                        logger.info(f"  - {material.filename}: 卡住 {material.hours_stuck:.1f} 小时")
                    
                    if len(stuck_materials) > 5:
                        logger.info(f"  ... 还有 {len(stuck_materials) - 5} 个")
                    
                    # 修复这些素材
                    fix_query = text("""
                        UPDATE local_creatives 
                        SET status = MaterialStatus.UPLOADED_PENDING_PLAN.value, 
                            updated_at = NOW()
                        WHERE status = 'creating_plan' 
                            AND updated_at < NOW() - INTERVAL '2 hours'
                    """)
                    
                    result = db.execute(fix_query)
                    db.commit()
                    
                    self.fix_results['creating_plan_fixed'] = result.rowcount
                    logger.info(f"✅ 成功修复 {result.rowcount} 个creating_plan状态素材")
                else:
                    logger.info("✅ 未发现卡住的creating_plan素材")
                    
        except Exception as e:
            logger.error(f"❌ 修复creating_plan素材失败: {e}")
    
    def fix_long_pending_materials(self):
        """修复长期pending的素材"""
        logger.info("🔧 修复长期pending的素材...")
        
        try:
            with database_session() as db:
                # 查询长期pending的素材
                query_pending = text("""
                    SELECT id, filename, updated_at,
                           EXTRACT(EPOCH FROM (NOW() - updated_at))/3600 as hours_pending
                    FROM local_creatives
                    WHERE status = MaterialStatus.UPLOADED_PENDING_PLAN.value 
                        AND updated_at < NOW() - INTERVAL '24 hours'
                    ORDER BY updated_at ASC
                    LIMIT 20
                """)
                
                pending_materials = db.execute(query_pending).fetchall()
                
                if pending_materials:
                    logger.info(f"📊 发现 {len(pending_materials)} 个长期pending素材:")
                    for material in pending_materials[:5]:
                        logger.info(f"  - {material.filename}: pending {material.hours_pending:.1f} 小时")
                    
                    # 重新激活这些素材
                    fix_query = text("""
                        UPDATE local_creatives 
                        SET status = MaterialStatus.APPROVED.value, 
                            updated_at = NOW()
                        WHERE status = MaterialStatus.UPLOADED_PENDING_PLAN.value 
                            AND updated_at < NOW() - INTERVAL '24 hours'
                    """)
                    
                    result = db.execute(fix_query)
                    db.commit()
                    
                    self.fix_results['pending_plan_fixed'] = result.rowcount
                    logger.info(f"✅ 成功重新激活 {result.rowcount} 个pending素材")
                else:
                    logger.info("✅ 未发现长期pending素材")
                    
        except Exception as e:
            logger.error(f"❌ 修复pending素材失败: {e}")
    
    def fix_stuck_materials_general(self):
        """修复其他卡住的素材"""
        logger.info("🔧 修复其他卡住的素材...")
        
        try:
            with database_session() as db:
                # 查询各种卡住的素材
                stuck_statuses = [
                    (MaterialStatus.NEW.value, 72),  # new状态超过72小时
                    (MaterialStatus.PENDING_UPLOAD.value, 48),  # pending_upload超过48小时
                    (MaterialStatus.UPLOAD_FAILED.value, 24),  # upload_failed超过24小时
                ]
                
                total_fixed = 0
                
                for status, hours in stuck_statuses:
                    query = text(f"""
                        SELECT COUNT(*) as count
                        FROM local_creatives
                        WHERE status = :status 
                            AND updated_at < NOW() - INTERVAL '{hours} hours'
                    """)
                    
                    count_result = db.execute(query, {'status': status}).fetchone()
                    
                    if count_result.count > 0:
                        logger.info(f"📊 发现 {count_result.count} 个卡住的{status}状态素材")
                        
                        # 根据状态类型决定修复策略
                        if status == MaterialStatus.NEW.value:
                            new_status = MaterialStatus.PENDING_UPLOAD.value
                        elif status == MaterialStatus.PENDING_UPLOAD.value:
                            new_status = MaterialStatus.NEW.value  # 重新开始
                        elif status == MaterialStatus.UPLOAD_FAILED.value:
                            new_status = MaterialStatus.PENDING_UPLOAD.value  # 重试上传
                        
                        fix_query = text(f"""
                            UPDATE local_creatives 
                            SET status = :new_status, 
                                updated_at = NOW()
                            WHERE status = :old_status 
                                AND updated_at < NOW() - INTERVAL '{hours} hours'
                        """)
                        
                        result = db.execute(fix_query, {
                            'new_status': new_status,
                            'old_status': status
                        })
                        
                        total_fixed += result.rowcount
                        logger.info(f"✅ 修复 {result.rowcount} 个{status}状态素材 -> {new_status}")
                
                db.commit()
                self.fix_results['stuck_materials_fixed'] = total_fixed
                
                if total_fixed > 0:
                    logger.info(f"✅ 总共修复 {total_fixed} 个其他卡住的素材")
                else:
                    logger.info("✅ 未发现其他需要修复的卡住素材")
                    
        except Exception as e:
            logger.error(f"❌ 修复其他卡住素材失败: {e}")
    
    def analyze_plan_creation_issue(self):
        """分析计划创建问题"""
        logger.info("🔍 分析计划创建问题...")
        
        try:
            with database_session() as db:
                # 查询可能导致跳过的原因
                analysis_queries = [
                    ("账户状态检查", """
                        SELECT a.account_id_qc, a.status, a.account_type,
                               COUNT(lc.id) as material_count
                        FROM ad_accounts a
                        LEFT JOIN local_creatives lc ON a.principal_id = lc.principal_id
                        WHERE lc.status = MaterialStatus.UPLOADED_PENDING_PLAN.value
                        GROUP BY a.account_id_qc, a.status, a.account_type
                        ORDER BY material_count DESC
                        LIMIT 10
                    """),
                    ("素材分组检查", """
                        SELECT principal_id, COUNT(*) as count
                        FROM local_creatives
                        WHERE status = MaterialStatus.UPLOADED_PENDING_PLAN.value
                        GROUP BY principal_id
                        ORDER BY count DESC
                        LIMIT 10
                    """),
                    ("最近创建的计划", """
                        SELECT account_id, COUNT(*) as recent_campaigns,
                               MAX(created_at) as last_created
                        FROM campaigns
                        WHERE created_at > NOW() - INTERVAL '24 hours'
                        GROUP BY account_id
                        ORDER BY recent_campaigns DESC
                        LIMIT 10
                    """)
                ]
                
                for title, query in analysis_queries:
                    logger.info(f"\n📊 {title}:")
                    try:
                        results = db.execute(text(query)).fetchall()
                        if results:
                            for i, row in enumerate(results[:5], 1):
                                logger.info(f"  {i}. {dict(row)}")
                        else:
                            logger.info("  无相关数据")
                    except Exception as e:
                        logger.warning(f"  查询失败: {e}")
                        
        except Exception as e:
            logger.error(f"❌ 分析计划创建问题失败: {e}")
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("\n📋 紧急修复报告")
        logger.info("=" * 60)
        
        total_fixed = (
            self.fix_results['creating_plan_fixed'] + 
            self.fix_results['pending_plan_fixed'] + 
            self.fix_results['stuck_materials_fixed']
        )
        
        logger.info("🎯 修复结果:")
        logger.info(f"  creating_plan状态修复: {self.fix_results['creating_plan_fixed']} 个")
        logger.info(f"  pending状态重新激活: {self.fix_results['pending_plan_fixed']} 个")
        logger.info(f"  其他卡住素材修复: {self.fix_results['stuck_materials_fixed']} 个")
        logger.info(f"  总计修复素材: {total_fixed} 个")
        
        if total_fixed > 0:
            logger.info("\n💡 后续建议:")
            logger.info("  1. 重启Celery工作流服务")
            logger.info("  2. 监控素材状态流转是否恢复正常")
            logger.info("  3. 观察creating_plan跳过次数是否减少")
            logger.info("  4. 如有问题可从备份恢复")
        else:
            logger.info("\n✅ 系统状态良好，无需修复")
        
        return total_fixed
    
    def restart_services_reminder(self):
        """服务重启提醒"""
        logger.info("\n🔄 服务重启建议")
        logger.info("=" * 60)
        
        logger.info("建议执行以下命令重启服务:")
        logger.info("  1. 停止Celery服务:")
        logger.info("     pkill -f celery")
        logger.info("  2. 重启主服务:")
        logger.info("     python main.py restart")
        logger.info("  3. 检查服务状态:")
        logger.info("     python main.py status")
        
        logger.info("\n📊 监控指标:")
        logger.info("  - 观察日志中creating_plan跳过次数")
        logger.info("  - 检查素材状态分布变化")
        logger.info("  - 监控新计划创建情况")

def main():
    """主修复函数"""
    try:
        fixer = EmergencySystemFixer()
        
        logger.info("🚨 开始千川系统紧急修复")
        logger.info("=" * 60)
        
        # 1. 创建备份
        backup_file = fixer.backup_database()
        
        # 2. 修复卡住的素材
        fixer.fix_stuck_creating_plan_materials()
        fixer.fix_long_pending_materials()
        fixer.fix_stuck_materials_general()
        
        # 3. 分析计划创建问题
        fixer.analyze_plan_creation_issue()
        
        # 4. 生成修复报告
        total_fixed = fixer.generate_fix_report()
        
        # 5. 服务重启提醒
        fixer.restart_services_reminder()
        
        logger.info(f"\n🎉 紧急修复完成!")
        logger.info(f"共修复 {total_fixed} 个问题")
        
        return total_fixed > 0
        
    except Exception as e:
        logger.error(f"❌ 紧急修复失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
