#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 诊断千川自动化项目计划提审环节缺失问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def analyze_campaign_status_distribution():
    """分析计划状态分布"""
    logger.info("🔍 分析计划状态分布...")
    
    conn = get_database_connection()
    cursor = conn.cursor()
    
    # 检查计划状态分布
    cursor.execute("""
        SELECT 
            status,
            appeal_status,
            COUNT(*) as count,
            MIN(created_at) as earliest,
            MAX(created_at) as latest
        FROM campaigns 
        GROUP BY status, appeal_status
        ORDER BY count DESC
    """)
    
    logger.info("📊 计划状态分布:")
    logger.info("="*80)
    
    total_campaigns = 0
    critical_issues = []
    
    for status, appeal_status, count, earliest, latest in cursor.fetchall():
        total_campaigns += count
        
        # 分析关键问题
        if status == 'AUDITING' and appeal_status is None:
            critical_issues.append(f"发现 {count} 个AUDITING状态但未提审的计划")
            logger.error(f"🚨 {status} (appeal_status: {appeal_status}): {count} 个")
        elif status == 'APPEAL_TIMEOUT':
            logger.warning(f"⚠️ {status}: {count} 个")
        else:
            logger.info(f"📋 {status} (appeal_status: {appeal_status}): {count} 个")
        
        logger.info(f"   📅 时间范围: {earliest} ~ {latest}")
    
    logger.info(f"\n📊 总计划数: {total_campaigns}")
    
    cursor.close()
    conn.close()
    
    return critical_issues

def check_recent_campaigns_submission_status():
    """检查最近创建计划的提审状态"""
    logger.info("\n🔍 检查最近创建计划的提审状态...")
    
    conn = get_database_connection()
    cursor = conn.cursor()
    
    # 检查最近24小时创建的计划
    cursor.execute("""
        SELECT 
            id,
            campaign_id_qc,
            status,
            appeal_status,
            first_appeal_at,
            created_at,
            EXTRACT(EPOCH FROM (NOW() - created_at))/3600 as hours_since_creation
        FROM campaigns 
        WHERE created_at >= NOW() - INTERVAL '24 hours'
        ORDER BY created_at DESC
        LIMIT 30
    """)
    
    logger.info("📋 最近24小时创建的计划:")
    logger.info("-"*100)
    
    unsubmitted_count = 0
    submitted_count = 0
    
    for row in cursor.fetchall():
        campaign_id, campaign_id_qc, status, appeal_status, first_appeal_at, created_at, hours_since = row
        
        # 判断是否已提审
        is_submitted = first_appeal_at is not None or appeal_status is not None
        
        if is_submitted:
            submitted_count += 1
            logger.success(f"✅ {campaign_id_qc} | {status} | 已提审 | 创建于 {hours_since:.1f}小时前")
        else:
            unsubmitted_count += 1
            if hours_since > 2:  # 超过2小时未提审
                logger.error(f"🚨 {campaign_id_qc} | {status} | 未提审 | 创建于 {hours_since:.1f}小时前 (超时)")
            else:
                logger.warning(f"⚠️ {campaign_id_qc} | {status} | 未提审 | 创建于 {hours_since:.1f}小时前")
    
    logger.info(f"\n📊 最近24小时统计:")
    logger.info(f"   ✅ 已提审: {submitted_count}")
    logger.info(f"   ❌ 未提审: {unsubmitted_count}")
    
    submission_rate = (submitted_count / (submitted_count + unsubmitted_count)) * 100 if (submitted_count + unsubmitted_count) > 0 else 0
    logger.info(f"   📈 提审率: {submission_rate:.1f}%")
    
    cursor.close()
    conn.close()
    
    return unsubmitted_count, submission_rate

def analyze_workflow_gap():
    """分析工作流缺口"""
    logger.info("\n🔍 分析工作流缺口...")
    
    try:
        # 检查Celery任务配置
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        beat_schedule = app.conf.beat_schedule
        
        # 检查是否有计划提审任务
        submission_tasks = []
        appeal_tasks = []
        
        for task_name, config in beat_schedule.items():
            task_func = config.get('task', '')
            if 'submit' in task_name.lower() or 'submission' in task_name.lower():
                submission_tasks.append(task_name)
            elif 'appeal' in task_name.lower():
                appeal_tasks.append(task_name)
        
        logger.info("📋 工作流任务分析:")
        logger.info("-"*50)
        
        if submission_tasks:
            logger.success(f"✅ 发现计划提审任务: {submission_tasks}")
        else:
            logger.error("🚨 未发现计划提审任务")
        
        if appeal_tasks:
            logger.info(f"📋 发现申诉任务: {appeal_tasks}")
        else:
            logger.warning("⚠️ 未发现申诉任务")
        
        # 分析工作流逻辑
        logger.info("\n🔍 工作流逻辑分析:")
        logger.info("-"*50)
        
        # 检查计划创建后的处理逻辑
        from qianchuan_aw.workflows import scheduler
        
        # 检查是否有计划提审的处理函数
        submission_functions = []
        for attr_name in dir(scheduler):
            if 'submit' in attr_name.lower() or 'submission' in attr_name.lower():
                submission_functions.append(attr_name)
        
        if submission_functions:
            logger.success(f"✅ 发现提审处理函数: {submission_functions}")
        else:
            logger.error("🚨 未发现计划提审处理函数")
        
        # 检查appeal相关函数
        appeal_functions = []
        for attr_name in dir(scheduler):
            if 'appeal' in attr_name.lower():
                appeal_functions.append(attr_name)
        
        if appeal_functions:
            logger.info(f"📋 发现申诉处理函数: {appeal_functions}")
        else:
            logger.warning("⚠️ 未发现申诉处理函数")
        
        return len(submission_tasks) == 0, len(submission_functions) == 0
        
    except Exception as e:
        logger.error(f"❌ 工作流分析失败: {e}")
        return True, True

def check_plan_creation_to_submission_logic():
    """检查计划创建到提审的逻辑"""
    logger.info("\n🔍 检查计划创建到提审的逻辑...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows import scheduler
        
        # 检查计划创建函数
        if hasattr(scheduler, 'handle_plan_creation'):
            logger.success("✅ 发现计划创建函数: handle_plan_creation")
            
            # 尝试分析函数逻辑（通过源码检查）
            import inspect
            source = inspect.getsource(scheduler.handle_plan_creation)
            
            # 检查是否有提审相关逻辑
            if 'submit' in source.lower() or 'appeal' in source.lower():
                logger.success("✅ 计划创建函数包含提审逻辑")
            else:
                logger.error("🚨 计划创建函数缺少提审逻辑")
                
            # 检查状态设置
            if 'AUDITING' in source:
                logger.info("📋 计划创建后设置为AUDITING状态")
            else:
                logger.warning("⚠️ 计划创建后状态设置不明确")
        else:
            logger.error("❌ 未发现计划创建函数")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查计划创建逻辑失败: {e}")
        return False

def generate_diagnosis_report():
    """生成诊断报告"""
    logger.info("🚀 开始计划提审环节缺失诊断...")
    logger.info("="*80)
    
    # 1. 分析计划状态分布
    critical_issues = analyze_campaign_status_distribution()
    
    # 2. 检查最近计划的提审状态
    unsubmitted_count, submission_rate = check_recent_campaigns_submission_status()
    
    # 3. 分析工作流缺口
    missing_tasks, missing_functions = analyze_workflow_gap()
    
    # 4. 检查计划创建到提审的逻辑
    creation_logic_ok = check_plan_creation_to_submission_logic()
    
    # 5. 生成诊断结论
    logger.info("\n" + "="*80)
    logger.info("🎯 诊断结论")
    logger.info("="*80)
    
    # 判断是否确实缺少计划提审环节
    has_submission_gap = False
    
    if unsubmitted_count > 10:
        has_submission_gap = True
        logger.error(f"🚨 严重问题: {unsubmitted_count} 个计划未提审")
    
    if submission_rate < 50:
        has_submission_gap = True
        logger.error(f"🚨 严重问题: 提审率仅 {submission_rate:.1f}%")
    
    if missing_tasks:
        has_submission_gap = True
        logger.error("🚨 严重问题: 缺少计划提审Celery任务")
    
    if missing_functions:
        has_submission_gap = True
        logger.error("🚨 严重问题: 缺少计划提审处理函数")
    
    # 输出诊断结果
    if has_submission_gap:
        logger.error("❌ 确认存在计划提审环节缺失问题")
        
        logger.info("\n💡 问题分析:")
        logger.info("1. 计划创建任务正常执行，创建了大量AUDITING状态的计划")
        logger.info("2. 但这些计划的appeal_status为NULL，说明从未被提审")
        logger.info("3. 缺少从AUDITING状态到实际提审的工作流环节")
        logger.info("4. 导致计划永远停留在草稿状态，无法进入千川审核流程")
        
        logger.info("\n🔧 需要立即修复:")
        logger.info("1. 实现计划提审任务和处理函数")
        logger.info("2. 添加计划提审到Celery调度中")
        logger.info("3. 修复计划创建后的状态流转逻辑")
        logger.info("4. 批量提审现有未提审的计划")
        
    else:
        logger.success("✅ 计划提审环节正常")
    
    return {
        'has_submission_gap': has_submission_gap,
        'unsubmitted_count': unsubmitted_count,
        'submission_rate': submission_rate,
        'critical_issues': critical_issues,
        'missing_tasks': missing_tasks,
        'missing_functions': missing_functions
    }

def main():
    """主函数"""
    try:
        report = generate_diagnosis_report()
        
        # 保存诊断报告
        report_file = project_root / 'ai_temp' / f'plan_submission_gap_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"\n📋 诊断报告已保存: {report_file}")
        
        return not report['has_submission_gap']
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
