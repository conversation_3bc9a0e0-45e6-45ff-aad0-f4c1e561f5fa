#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急禁用工作流任务
清理条件: 问题解决后可删除

紧急工作流禁用工具
================

暂时禁用所有可能导致任务循环的工作流，防止CPU过载。
"""

import os
import sys
import yaml
import shutil
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class EmergencyWorkflowDisabler:
    """紧急工作流禁用器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = os.path.join(project_root, 'config', 'settings.yml')
        self.backup_file = os.path.join(project_root, 'ai_temp', f'settings_backup_emergency_{int(__import__("time").time())}.yml')
        
    def backup_config(self):
        """备份配置文件"""
        try:
            os.makedirs(os.path.dirname(self.backup_file), exist_ok=True)
            shutil.copy2(self.config_file, self.backup_file)
            logger.info(f"✅ 配置文件已备份到: {self.backup_file}")
            return True
        except Exception as e:
            logger.error(f"❌ 备份配置文件失败: {e}")
            return False
    
    def disable_all_workflows(self):
        """禁用所有工作流"""
        try:
            # 读取配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 确保workflow配置存在
            if 'workflow' not in config:
                config['workflow'] = {}
            
            # 禁用所有工作流任务
            workflows_to_disable = [
                'file_ingestion',
                'group_dispatch', 
                'plan_creation',
                'plan_appeal',
                'material_monitoring',
                'zombie_cleanup',
                'violation_detection',
                'comment_management',
                'material_collection'
            ]
            
            disabled_count = 0
            for workflow in workflows_to_disable:
                if workflow not in config['workflow']:
                    config['workflow'][workflow] = {}
                
                # 记录原始状态
                original_enabled = config['workflow'][workflow].get('enabled', True)
                
                # 禁用工作流
                config['workflow'][workflow]['enabled'] = False
                
                if original_enabled:
                    disabled_count += 1
                    logger.info(f"🔴 禁用工作流: {workflow}")
                else:
                    logger.info(f"⚪ 工作流已禁用: {workflow}")
            
            # 写回配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"✅ 已禁用 {disabled_count} 个工作流任务")
            return True
            
        except Exception as e:
            logger.error(f"❌ 禁用工作流失败: {e}")
            return False
    
    def show_current_status(self):
        """显示当前工作流状态"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            workflow_config = config.get('workflow', {})
            
            logger.info("📊 当前工作流状态:")
            logger.info("=" * 50)
            
            workflows = [
                'file_ingestion',
                'group_dispatch', 
                'plan_creation',
                'plan_appeal',
                'material_monitoring',
                'zombie_cleanup',
                'violation_detection',
                'comment_management',
                'material_collection'
            ]
            
            for workflow in workflows:
                workflow_settings = workflow_config.get(workflow, {})
                enabled = workflow_settings.get('enabled', True)
                interval = workflow_settings.get('interval_seconds', 'N/A')
                
                status_icon = "🟢" if enabled else "🔴"
                logger.info(f"{status_icon} {workflow}: {'启用' if enabled else '禁用'} (间隔: {interval}s)")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 显示状态失败: {e}")
            return False
    
    def create_safe_restart_script(self):
        """创建安全重启脚本"""
        try:
            restart_script = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
安全重启脚本 - 自动生成
===================

在确认系统稳定后，使用此脚本安全地重新启动工作流。
\"\"\"

import os
import sys
import yaml
import time

project_root = r"{self.project_root}"
config_file = os.path.join(project_root, "config", "settings.yml")

def enable_workflows_gradually():
    \"\"\"逐步启用工作流\"\"\"
    print("🚀 开始逐步启用工作流...")
    
    # 读取配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # 第一批：基础工作流（低频率）
    first_batch = [
        ('material_monitoring', 600),  # 10分钟
        ('zombie_cleanup', 1800),      # 30分钟
    ]
    
    # 第二批：核心工作流（中频率）
    second_batch = [
        ('file_ingestion', 300),       # 5分钟
        ('group_dispatch', 180),       # 3分钟
    ]
    
    # 第三批：高频工作流（需要谨慎）
    third_batch = [
        ('plan_creation', 600),        # 10分钟（降低频率）
        ('plan_appeal', 900),          # 15分钟（降低频率）
    ]
    
    def enable_batch(batch, batch_name):
        print(f"\\n📋 启用{batch_name}...")
        for workflow, interval in batch:
            config['workflow'][workflow]['enabled'] = True
            config['workflow'][workflow]['interval_seconds'] = interval
            print(f"✅ 启用: {workflow} (间隔: {interval}s)")
        
        # 保存配置
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        print(f"⏳ 等待30秒观察系统状态...")
        time.sleep(30)
    
    # 逐批启用
    enable_batch(first_batch, "第一批（监控类）")
    enable_batch(second_batch, "第二批（处理类）")
    
    print("\\n⚠️ 准备启用第三批（高频任务）")
    print("请确认系统CPU使用率正常后，手动运行以下命令启用：")
    print("python -c \\"import yaml; config=yaml.safe_load(open('config/settings.yml')); config['workflow']['plan_creation']['enabled']=True; config['workflow']['plan_appeal']['enabled']=True; yaml.dump(config, open('config/settings.yml', 'w'), default_flow_style=False)\\"")

if __name__ == '__main__':
    enable_workflows_gradually()
"""
            
            script_path = os.path.join(self.project_root, 'ai_tools', 'recovery', 'ai_tool_20250725_recovery_safe_restart.py')
            os.makedirs(os.path.dirname(script_path), exist_ok=True)
            
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(restart_script)
            
            logger.info(f"✅ 安全重启脚本已创建: {script_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建重启脚本失败: {e}")
            return False
    
    def generate_report(self):
        """生成禁用报告"""
        report = f"""
紧急工作流禁用报告
================

禁用时间: {__import__('time').strftime('%Y-%m-%d %H:%M:%S')}

执行的操作:
1. ✅ 备份了原始配置文件
2. ✅ 禁用了所有工作流任务
3. ✅ 创建了安全重启脚本

备份文件位置:
{self.backup_file}

安全重启脚本:
ai_tools/recovery/ai_tool_20250725_recovery_safe_restart.py

禁用的工作流:
- file_ingestion (文件摄取)
- group_dispatch (分组派发)
- plan_creation (计划创建) ⚠️ 主要问题源
- plan_appeal (计划提审)
- material_monitoring (素材监控)
- zombie_cleanup (僵尸清理) ⚠️ 可能导致循环
- violation_detection (违规检测)
- comment_management (评论管理)
- material_collection (素材收集)

下一步操作:
1. 确认CPU使用率降低到正常范围
2. 等待系统稳定（建议等待5-10分钟）
3. 使用安全重启脚本逐步恢复工作流
4. 监控系统状态，确保不再出现任务循环

注意事项:
- 在工作流禁用期间，系统不会处理新的素材
- 现有的异常状态素材已经重置
- 重新启用时建议降低任务频率
"""
        
        report_file = os.path.join(self.project_root, 'ai_temp', f'emergency_workflow_disable_report_{int(__import__("time").time())}.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 禁用报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚨 开始紧急工作流禁用...")
    
    disabler = EmergencyWorkflowDisabler()
    
    try:
        # 1. 显示当前状态
        logger.info("📊 当前工作流状态:")
        disabler.show_current_status()
        
        # 2. 备份配置文件
        if not disabler.backup_config():
            return 1
        
        # 3. 禁用所有工作流
        if not disabler.disable_all_workflows():
            return 1
        
        # 4. 创建安全重启脚本
        if not disabler.create_safe_restart_script():
            return 1
        
        # 5. 生成报告
        report = disabler.generate_report()
        
        logger.info("🎯 紧急工作流禁用完成！")
        print(report)
        
        logger.info("💡 下一步建议:")
        logger.info("1. 等待5-10分钟确认CPU使用率降低")
        logger.info("2. 运行 'python ai_tools/recovery/ai_tool_20250725_recovery_safe_restart.py' 逐步恢复")
        logger.info("3. 持续监控系统状态")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 紧急禁用过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
