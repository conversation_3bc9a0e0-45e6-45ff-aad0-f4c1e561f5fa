#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 视频测试工作流账户限制器 - 确保只对测试账户进行操作
清理条件: 系统架构重大变更时可考虑重构
"""

import sys
import os
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import AdAccount, Campaign, LocalCreative, PlatformCreative
from sqlalchemy import and_, or_

class TestAccountLimiter:
    """测试账户限制器 - 确保工作流只操作测试账户"""
    
    def __init__(self):
        self.test_account_ids = None
        self.last_refresh = None
        self.refresh_interval = 300  # 5分钟刷新一次缓存
    
    def get_test_account_ids(self) -> List[int]:
        """获取所有测试账户ID列表"""
        current_time = datetime.now(timezone.utc)
        
        # 检查是否需要刷新缓存
        if (self.test_account_ids is None or 
            self.last_refresh is None or 
            (current_time - self.last_refresh).total_seconds() > self.refresh_interval):
            
            self._refresh_test_accounts()
        
        return self.test_account_ids or []
    
    def _refresh_test_accounts(self):
        """刷新测试账户列表 - 只包含active账户（用于新建操作）"""
        try:
            with database_session() as db:
                test_accounts = db.query(AdAccount).filter(
                    AdAccount.account_type == 'TEST',
                    AdAccount.status == 'active'
                ).all()

                self.test_account_ids = [acc.id for acc in test_accounts]
                self.last_refresh = datetime.now(timezone.utc)

                logger.info(f"🔄 刷新测试账户列表: 找到 {len(self.test_account_ids)} 个active测试账户")

        except Exception as e:
            logger.error(f"❌ 刷新测试账户列表失败: {e}")
            self.test_account_ids = []

    def get_test_account_ids_for_existing_operations(self) -> List[int]:
        """获取可用于已有操作（提审、收割）的测试账户ID列表"""
        try:
            with database_session() as db:
                # 🔧 [业务规则修正] 已有操作允许temporarily_blocked账户，但严禁deleted账户
                test_accounts = db.query(AdAccount).filter(
                    AdAccount.account_type == 'TEST',
                    AdAccount.status.in_(['active', 'temporarily_blocked'])  # 允许temporarily_blocked
                ).all()

                account_ids = [acc.id for acc in test_accounts]

                logger.info(f"🔄 获取已有操作账户列表: 找到 {len(account_ids)} 个可用测试账户")
                logger.info(f"   包含active和temporarily_blocked账户，严禁deleted账户")

                return account_ids

        except Exception as e:
            logger.error(f"❌ 获取已有操作账户列表失败: {e}")
            return []
    
    def filter_test_campaigns(self, campaigns: List[Campaign]) -> List[Campaign]:
        """过滤出只属于测试账户的计划"""
        test_account_ids = self.get_test_account_ids()
        if not test_account_ids:
            logger.warning("⚠️ 没有找到测试账户，返回空列表")
            return []
        
        filtered_campaigns = [c for c in campaigns if c.account_id in test_account_ids]
        
        if len(filtered_campaigns) != len(campaigns):
            filtered_count = len(campaigns) - len(filtered_campaigns)
            logger.info(f"🛡️ 过滤掉 {filtered_count} 个非测试账户的计划")
        
        return filtered_campaigns
    
    def filter_test_materials(self, materials: List[LocalCreative]) -> List[LocalCreative]:
        """过滤出只属于测试账户的素材"""
        test_account_ids = self.get_test_account_ids()
        if not test_account_ids:
            logger.warning("⚠️ 没有找到测试账户，返回空列表")
            return []
        
        filtered_materials = [m for m in materials if m.uploaded_to_account_id in test_account_ids]
        
        if len(filtered_materials) != len(materials):
            filtered_count = len(materials) - len(filtered_materials)
            logger.info(f"🛡️ 过滤掉 {filtered_count} 个非测试账户的素材")
        
        return filtered_materials
    
    def is_test_account(self, account_id: int) -> bool:
        """检查指定账户是否为测试账户"""
        test_account_ids = self.get_test_account_ids()
        return account_id in test_account_ids
    
    def validate_operation_scope(self, operation_name: str, account_ids: List[int]) -> bool:
        """验证操作范围是否仅限于测试账户"""
        test_account_ids = self.get_test_account_ids()
        
        non_test_accounts = [aid for aid in account_ids if aid not in test_account_ids]
        
        if non_test_accounts:
            logger.error(f"❌ {operation_name} 操作包含非测试账户: {non_test_accounts}")
            return False
        
        logger.info(f"✅ {operation_name} 操作范围验证通过，仅涉及测试账户")
        return True
    
    def get_test_campaigns_for_appeal(self) -> List[Campaign]:
        """获取需要提审的测试账户计划"""
        test_account_ids = self.get_test_account_ids()
        if not test_account_ids:
            return []
        
        try:
            with database_session() as db:
                campaigns = db.query(Campaign).filter(
                    Campaign.account_id.in_(test_account_ids),
                    Campaign.appeal_status == 'appeal_pending'
                ).all()
                
                logger.info(f"🎯 找到 {len(campaigns)} 个测试账户待提审计划")
                return campaigns
                
        except Exception as e:
            logger.error(f"❌ 获取测试账户待提审计划失败: {e}")
            return []
    
    def get_test_materials_for_harvest(self) -> List[LocalCreative]:
        """获取需要收割的测试账户素材"""
        test_account_ids = self.get_test_account_ids()
        if not test_account_ids:
            return []
        
        try:
            with database_session() as db:
                materials = db.query(LocalCreative).filter(
                    LocalCreative.uploaded_to_account_id.in_(test_account_ids),
                    LocalCreative.harvest_status == 'not_harvested'
                ).all()
                
                logger.info(f"🎯 找到 {len(materials)} 个测试账户待收割素材")
                return materials
                
        except Exception as e:
            logger.error(f"❌ 获取测试账户待收割素材失败: {e}")
            return []
    
    def log_operation_summary(self, operation_name: str, processed_count: int):
        """记录操作摘要"""
        test_account_count = len(self.get_test_account_ids())
        logger.info(f"📊 {operation_name} 操作摘要:")
        logger.info(f"   - 测试账户数量: {test_account_count}")
        logger.info(f"   - 处理项目数量: {processed_count}")
        logger.info(f"   - 操作时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# 全局实例
test_account_limiter = TestAccountLimiter()

def apply_test_account_filter(func):
    """装饰器：自动应用测试账户过滤"""
    def wrapper(*args, **kwargs):
        logger.info(f"🛡️ 应用测试账户过滤器到函数: {func.__name__}")
        
        # 获取测试账户ID列表
        test_account_ids = test_account_limiter.get_test_account_ids()
        if not test_account_ids:
            logger.warning("⚠️ 没有找到测试账户，跳过操作")
            return None
        
        # 在kwargs中添加测试账户限制
        kwargs['test_account_ids'] = test_account_ids
        
        result = func(*args, **kwargs)
        
        logger.info(f"✅ 测试账户过滤器应用完成: {func.__name__}")
        return result
    
    return wrapper

def ensure_test_account_only(campaigns: List[Campaign]) -> List[Campaign]:
    """确保计划列表只包含测试账户的计划"""
    return test_account_limiter.filter_test_campaigns(campaigns)

def ensure_test_materials_only(materials: List[LocalCreative]) -> List[LocalCreative]:
    """确保素材列表只包含测试账户的素材"""
    return test_account_limiter.filter_test_materials(materials)

def validate_test_account_operation(operation_name: str, account_ids: List[int]) -> bool:
    """验证操作是否仅涉及测试账户"""
    return test_account_limiter.validate_operation_scope(operation_name, account_ids)

def get_test_account_statistics() -> Dict[str, Any]:
    """获取测试账户统计信息"""
    try:
        with database_session() as db:
            test_account_ids = test_account_limiter.get_test_account_ids()
            
            if not test_account_ids:
                return {
                    'test_accounts': 0,
                    'campaigns': 0,
                    'materials': 0,
                    'pending_appeals': 0,
                    'pending_harvests': 0
                }
            
            # 统计计划数量
            campaign_count = db.query(Campaign).filter(
                Campaign.account_id.in_(test_account_ids)
            ).count()
            
            # 统计素材数量
            material_count = db.query(LocalCreative).filter(
                LocalCreative.uploaded_to_account_id.in_(test_account_ids)
            ).count()
            
            # 统计待提审计划
            pending_appeals = db.query(Campaign).filter(
                Campaign.account_id.in_(test_account_ids),
                Campaign.appeal_status == 'appeal_pending'
            ).count()
            
            # 统计待收割素材
            pending_harvests = db.query(LocalCreative).filter(
                LocalCreative.uploaded_to_account_id.in_(test_account_ids),
                LocalCreative.harvest_status == 'not_harvested'
            ).count()
            
            return {
                'test_accounts': len(test_account_ids),
                'campaigns': campaign_count,
                'materials': material_count,
                'pending_appeals': pending_appeals,
                'pending_harvests': pending_harvests
            }
            
    except Exception as e:
        logger.error(f"❌ 获取测试账户统计失败: {e}")
        return {}

def main():
    """测试函数"""
    logger.info("🧪 测试账户限制器功能测试")
    logger.info("=" * 50)
    
    # 获取统计信息
    stats = get_test_account_statistics()
    logger.info(f"📊 测试账户统计: {stats}")
    
    # 测试过滤功能
    test_campaigns = test_account_limiter.get_test_campaigns_for_appeal()
    logger.info(f"🎯 待提审测试计划: {len(test_campaigns)} 个")
    
    test_materials = test_account_limiter.get_test_materials_for_harvest()
    logger.info(f"🎯 待收割测试素材: {len(test_materials)} 个")
    
    logger.success("✅ 测试账户限制器功能正常")

if __name__ == "__main__":
    main()
