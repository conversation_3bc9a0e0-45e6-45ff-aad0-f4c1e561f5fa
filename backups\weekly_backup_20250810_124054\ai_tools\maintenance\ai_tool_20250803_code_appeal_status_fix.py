#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 修复所有创建Campaign的代码，确保明确设置appeal_status=None
清理条件: 代码修复完成后可归档，但建议保留作为维护工具
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger

class CampaignCodeFixer:
    """Campaign创建代码修复器"""
    
    def __init__(self):
        self.fix_start_time = datetime.now()
        self.files_to_fix = []
        self.fixes_applied = []
        self.issues_found = []
    
    def scan_campaign_creation_code(self) -> List[Dict]:
        """扫描所有创建Campaign的代码"""
        
        logger.info("🔍 扫描所有创建Campaign的代码...")
        
        # 需要扫描的目录
        scan_dirs = [
            project_root / "src" / "qianchuan_aw",
            project_root / "ai_tools",
        ]
        
        campaign_creations = []
        
        for scan_dir in scan_dirs:
            if scan_dir.exists():
                for py_file in scan_dir.rglob("*.py"):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 查找Campaign(构造函数调用
                        campaign_pattern = r'Campaign\s*\(\s*([^)]*)\s*\)'
                        matches = re.finditer(campaign_pattern, content, re.MULTILINE | re.DOTALL)
                        
                        for match in matches:
                            constructor_args = match.group(1)
                            line_number = content[:match.start()].count('\n') + 1
                            
                            # 检查是否包含appeal_status参数
                            has_appeal_status = 'appeal_status' in constructor_args
                            
                            campaign_info = {
                                'file': str(py_file),
                                'line_number': line_number,
                                'constructor_args': constructor_args.strip(),
                                'has_appeal_status': has_appeal_status,
                                'full_match': match.group(0),
                                'start_pos': match.start(),
                                'end_pos': match.end()
                            }
                            
                            campaign_creations.append(campaign_info)
                            
                            if not has_appeal_status:
                                self.issues_found.append(f"{py_file}:{line_number} - 缺少appeal_status参数")
                    
                    except Exception as e:
                        logger.warning(f"扫描文件 {py_file} 时出错: {e}")
        
        logger.info(f"📊 扫描结果:")
        logger.info(f"   找到 {len(campaign_creations)} 个Campaign创建")
        logger.info(f"   其中 {len(self.issues_found)} 个需要修复")
        
        return campaign_creations
    
    def analyze_campaign_creation(self, creation_info: Dict) -> Dict:
        """分析单个Campaign创建代码"""
        
        file_path = Path(creation_info['file'])
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 获取上下文（前后3行）
            line_num = creation_info['line_number']
            start_line = max(0, line_num - 4)
            end_line = min(len(lines), line_num + 3)
            
            context = ''.join(lines[start_line:end_line])
            
            analysis = {
                'file': creation_info['file'],
                'line_number': line_num,
                'context': context,
                'needs_fix': not creation_info['has_appeal_status'],
                'constructor_args': creation_info['constructor_args'],
                'full_match': creation_info['full_match']
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析文件 {file_path} 失败: {e}")
            return None
    
    def generate_fix_for_campaign_creation(self, creation_info: Dict) -> str:
        """为Campaign创建生成修复代码"""
        
        if creation_info['has_appeal_status']:
            return creation_info['full_match']  # 已经有appeal_status，不需要修复
        
        # 解析构造函数参数
        args = creation_info['constructor_args']
        
        if args.strip():
            # 有其他参数，添加appeal_status
            if args.strip().endswith(','):
                new_args = args + '\n            appeal_status=None'
            else:
                new_args = args + ',\n            appeal_status=None'
        else:
            # 没有其他参数，只添加appeal_status
            new_args = '\n            appeal_status=None\n        '
        
        fixed_code = f"Campaign({new_args})"
        
        return fixed_code
    
    def apply_fix_to_file(self, file_path: str, fixes: List[Dict], dry_run: bool = True) -> bool:
        """对单个文件应用修复"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 按位置倒序排序，避免位置偏移
            fixes_sorted = sorted(fixes, key=lambda x: x['start_pos'], reverse=True)
            
            modified_content = content
            
            for fix in fixes_sorted:
                start_pos = fix['start_pos']
                end_pos = fix['end_pos']
                new_code = fix['fixed_code']
                
                # 替换代码
                modified_content = (
                    modified_content[:start_pos] + 
                    new_code + 
                    modified_content[end_pos:]
                )
            
            if not dry_run:
                # 备份原文件
                backup_path = file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # 写入修复后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                logger.success(f"✅ 修复文件: {file_path}")
                logger.info(f"   备份文件: {backup_path}")
                self.fixes_applied.append(f"修复文件 {file_path}")
            else:
                logger.info(f"🔍 [DRY RUN] 将修复文件: {file_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复文件 {file_path} 失败: {e}")
            return False
    
    def fix_all_campaign_creations(self, dry_run: bool = True) -> bool:
        """修复所有Campaign创建代码"""
        
        logger.info(f"🔧 开始修复所有Campaign创建代码 (dry_run={dry_run})...")
        
        # 扫描所有Campaign创建
        campaign_creations = self.scan_campaign_creation_code()
        
        if not campaign_creations:
            logger.info("✅ 没有找到需要修复的Campaign创建代码")
            return True
        
        # 按文件分组
        files_to_fix = {}
        for creation in campaign_creations:
            if not creation['has_appeal_status']:
                file_path = creation['file']
                if file_path not in files_to_fix:
                    files_to_fix[file_path] = []
                
                # 生成修复代码
                fixed_code = self.generate_fix_for_campaign_creation(creation)
                
                fix_info = {
                    'start_pos': creation['start_pos'],
                    'end_pos': creation['end_pos'],
                    'original_code': creation['full_match'],
                    'fixed_code': fixed_code,
                    'line_number': creation['line_number']
                }
                
                files_to_fix[file_path].append(fix_info)
        
        # 显示修复预览
        logger.info(f"📋 需要修复的文件: {len(files_to_fix)} 个")
        for file_path, fixes in files_to_fix.items():
            logger.info(f"   📄 {file_path}: {len(fixes)} 处修复")
            for fix in fixes:
                logger.info(f"      行 {fix['line_number']}: {fix['original_code'][:50]}...")
        
        # 应用修复
        success_count = 0
        for file_path, fixes in files_to_fix.items():
            if self.apply_fix_to_file(file_path, fixes, dry_run):
                success_count += 1
        
        if not dry_run:
            logger.success(f"✅ 成功修复 {success_count}/{len(files_to_fix)} 个文件")
        else:
            logger.info(f"🔍 [DRY RUN] 预计修复 {success_count}/{len(files_to_fix)} 个文件")
        
        return success_count == len(files_to_fix)
    
    def verify_fixes(self) -> bool:
        """验证修复效果"""
        
        logger.info("🔍 验证修复效果...")
        
        # 重新扫描
        campaign_creations = self.scan_campaign_creation_code()
        
        missing_appeal_status = [
            creation for creation in campaign_creations 
            if not creation['has_appeal_status']
        ]
        
        if not missing_appeal_status:
            logger.success("✅ 验证通过: 所有Campaign创建都包含appeal_status参数")
            return True
        else:
            logger.warning(f"⚠️ 验证失败: 仍有 {len(missing_appeal_status)} 个Campaign创建缺少appeal_status")
            for creation in missing_appeal_status:
                logger.warning(f"   {creation['file']}:{creation['line_number']}")
            return False
    
    def generate_fix_report(self):
        """生成修复报告"""
        
        logger.info("📊 生成修复报告...")
        
        report = {
            'fix_time': self.fix_start_time,
            'completion_time': datetime.now(),
            'issues_found': len(self.issues_found),
            'fixes_applied': len(self.fixes_applied),
            'success': len(self.issues_found) == 0 or len(self.fixes_applied) > 0
        }
        
        logger.info("="*60)
        logger.info("📋 代码修复报告")
        logger.info("="*60)
        logger.info(f"开始时间: {report['fix_time']}")
        logger.info(f"完成时间: {report['completion_time']}")
        logger.info(f"发现问题: {report['issues_found']} 个")
        logger.info(f"应用修复: {report['fixes_applied']} 项")
        
        if report['success']:
            logger.success("✅ 代码修复成功完成")
        else:
            logger.warning("⚠️ 代码修复部分完成或存在问题")
        
        return report

def main():
    """主函数"""
    
    logger.info("🚀 开始修复Campaign创建代码...")
    
    fixer = CampaignCodeFixer()
    
    # 1. 扫描和分析
    logger.info("="*60)
    logger.info("第一步: 扫描Campaign创建代码")
    logger.info("="*60)
    
    campaign_creations = fixer.scan_campaign_creation_code()
    
    if not fixer.issues_found:
        logger.success("🎉 所有Campaign创建代码都已正确设置appeal_status!")
        return
    
    # 2. 显示需要修复的代码
    logger.info("\n" + "="*60)
    logger.info("第二步: 分析需要修复的代码")
    logger.info("="*60)
    
    for issue in fixer.issues_found[:5]:  # 只显示前5个
        logger.warning(f"   ❌ {issue}")
    
    if len(fixer.issues_found) > 5:
        logger.info(f"   ... 还有 {len(fixer.issues_found) - 5} 个问题")
    
    # 3. 执行修复（先dry run）
    logger.info("\n" + "="*60)
    logger.info("第三步: 执行代码修复")
    logger.info("="*60)
    
    # 先执行dry run
    logger.info("🔍 执行预演 (dry run)...")
    fixer.fix_all_campaign_creations(dry_run=True)
    
    # 询问是否执行实际修复
    response = input("\n是否执行实际的代码修复? (y/N): ").strip().lower()
    
    if response == 'y':
        logger.info("🔧 开始执行实际修复...")
        success = fixer.fix_all_campaign_creations(dry_run=False)
        
        if success:
            # 4. 验证修复效果
            logger.info("\n" + "="*60)
            logger.info("第四步: 验证修复效果")
            logger.info("="*60)
            
            verification_success = fixer.verify_fixes()
            
            if verification_success:
                logger.success("🎉 代码修复完成并验证成功！")
            else:
                logger.warning("⚠️ 修复完成但验证存在问题")
        else:
            logger.error("💥 代码修复失败")
    else:
        logger.info("❌ 用户取消修复操作")
    
    # 5. 生成报告
    logger.info("\n" + "="*60)
    logger.info("第五步: 生成修复报告")
    logger.info("="*60)
    
    fixer.generate_fix_report()
    
    print("\n💡 后续建议:")
    print("   1. 测试修复后的代码，确保功能正常")
    print("   2. 运行单元测试验证修改没有破坏现有功能")
    print("   3. 创建新的测试计划验证appeal_status正确设置")

if __name__ == "__main__":
    main()
