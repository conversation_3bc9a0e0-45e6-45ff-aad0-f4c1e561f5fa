"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 永久保留
创建目的: 紧急修复数据库中的G盘路径到D盘路径
清理条件: 修复完成后可保留作为历史记录
"""

import sys
import os
from pathlib import Path
from typing import List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative


class EmergencyPathFixer:
    """紧急路径修复工具"""
    
    def __init__(self):
        self.old_drive = "G:"
        self.new_drive = "D:"
        self.dry_run = True  # 默认为预览模式
        
    def analyze_path_issues(self) -> dict:
        """分析路径问题"""
        logger.info("🔍 分析数据库中的路径问题...")
        
        with database_session() as db:
            # 统计G盘路径
            g_paths_count = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('G:%')
            ).count()
            
            # 统计D盘路径
            d_paths_count = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('D:%')
            ).count()
            
            # 获取路径模式
            g_path_patterns = db.execute("""
                SELECT SUBSTRING(file_path FROM 1 FOR 50) as path_prefix, COUNT(*) as count 
                FROM local_creatives 
                WHERE file_path LIKE 'G:%' 
                GROUP BY SUBSTRING(file_path FROM 1 FOR 50) 
                ORDER BY count DESC 
                LIMIT 10
            """).fetchall()
            
            analysis = {
                'g_paths_count': g_paths_count,
                'd_paths_count': d_paths_count,
                'total_records': g_paths_count + d_paths_count,
                'g_path_patterns': [(row[0], row[1]) for row in g_path_patterns]
            }
            
            logger.info(f"📊 路径分析结果:")
            logger.info(f"   G盘路径记录: {g_paths_count}")
            logger.info(f"   D盘路径记录: {d_paths_count}")
            logger.info(f"   总记录数: {analysis['total_records']}")
            
            logger.info(f"📋 G盘路径模式:")
            for pattern, count in analysis['g_path_patterns']:
                logger.info(f"   {pattern}... ({count} 条)")
                
            return analysis
    
    def preview_path_changes(self, limit: int = 20) -> List[Tuple[int, str, str]]:
        """预览路径变更"""
        logger.info(f"👀 预览前 {limit} 条路径变更...")
        
        changes = []
        with database_session() as db:
            records = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('G:%')
            ).limit(limit).all()
            
            for record in records:
                old_path = record.file_path
                new_path = old_path.replace(self.old_drive, self.new_drive, 1)
                changes.append((record.id, old_path, new_path))
                
                logger.info(f"   ID {record.id}:")
                logger.info(f"     旧路径: {old_path}")
                logger.info(f"     新路径: {new_path}")
                
        return changes
    
    def fix_paths_batch(self, batch_size: int = 100) -> dict:
        """批量修复路径"""
        if self.dry_run:
            logger.warning("⚠️ 当前为预览模式，不会实际修改数据库")
            return self.preview_path_changes(batch_size)
        
        logger.info(f"🔧 开始批量修复路径 (批次大小: {batch_size})...")
        
        total_fixed = 0
        total_errors = 0
        
        with database_session() as db:
            try:
                # 分批处理
                offset = 0
                while True:
                    records = db.query(LocalCreative).filter(
                        LocalCreative.file_path.like('G:%')
                    ).offset(offset).limit(batch_size).all()
                    
                    if not records:
                        break
                    
                    batch_fixed = 0
                    for record in records:
                        try:
                            old_path = record.file_path
                            new_path = old_path.replace(self.old_drive, self.new_drive, 1)
                            
                            record.file_path = new_path
                            batch_fixed += 1
                            
                        except Exception as e:
                            logger.error(f"修复记录 {record.id} 失败: {e}")
                            total_errors += 1
                    
                    # 提交当前批次
                    db.commit()
                    total_fixed += batch_fixed
                    
                    logger.info(f"✅ 批次完成: 修复了 {batch_fixed} 条记录 (总计: {total_fixed})")
                    
                    offset += batch_size
                
                logger.success(f"🎉 路径修复完成!")
                logger.info(f"   成功修复: {total_fixed} 条记录")
                logger.info(f"   修复错误: {total_errors} 条记录")
                
                return {
                    'total_fixed': total_fixed,
                    'total_errors': total_errors,
                    'success': True
                }
                
            except Exception as e:
                db.rollback()
                logger.error(f"批量修复失败: {e}")
                return {
                    'total_fixed': total_fixed,
                    'total_errors': total_errors + 1,
                    'success': False,
                    'error': str(e)
                }
    
    def verify_fix_results(self) -> dict:
        """验证修复结果"""
        logger.info("🔍 验证修复结果...")
        
        with database_session() as db:
            # 重新统计
            g_paths_remaining = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('G:%')
            ).count()
            
            d_paths_count = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('D:%')
            ).count()
            
            # 检查是否还有问题记录
            problem_records = []
            if g_paths_remaining > 0:
                problem_records = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('G:%')
                ).limit(5).all()
            
            result = {
                'g_paths_remaining': g_paths_remaining,
                'd_paths_count': d_paths_count,
                'problem_records': [(r.id, r.file_path) for r in problem_records]
            }
            
            if g_paths_remaining == 0:
                logger.success("✅ 验证通过: 所有G盘路径已成功修复为D盘路径")
            else:
                logger.warning(f"⚠️ 仍有 {g_paths_remaining} 条G盘路径记录未修复")
                for record_id, path in result['problem_records']:
                    logger.warning(f"   ID {record_id}: {path}")
            
            logger.info(f"📊 当前状态:")
            logger.info(f"   D盘路径记录: {d_paths_count}")
            logger.info(f"   G盘路径记录: {g_paths_remaining}")
            
            return result
    
    def enable_real_mode(self):
        """启用实际修复模式"""
        self.dry_run = False
        logger.warning("⚠️ 已启用实际修复模式，将会修改数据库！")
    
    def create_backup_before_fix(self) -> bool:
        """修复前创建备份"""
        try:
            logger.info("💾 创建数据库备份...")
            
            # 这里可以添加数据库备份逻辑
            # 由于是紧急修复，暂时跳过备份步骤
            logger.warning("⚠️ 紧急修复模式，跳过备份步骤")
            
            return True
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("🚨 启动紧急路径修复工具...")
    logger.info("=" * 60)
    
    fixer = EmergencyPathFixer()
    
    # 1. 分析问题
    analysis = fixer.analyze_path_issues()
    
    if analysis['g_paths_count'] == 0:
        logger.success("✅ 没有发现G盘路径问题，无需修复")
        return
    
    # 2. 预览变更
    logger.info("\n" + "=" * 60)
    fixer.preview_path_changes(10)
    
    # 3. 询问是否继续
    print("\n" + "=" * 60)
    print("⚠️  警告: 即将修改数据库中的路径信息")
    print(f"📊 将修复 {analysis['g_paths_count']} 条G盘路径记录")
    print("🔄 G: -> D:")
    
    confirm = input("\n是否继续执行修复? (输入 'YES' 确认): ")
    
    if confirm != 'YES':
        logger.info("❌ 用户取消操作")
        return
    
    # 4. 执行修复
    logger.info("\n" + "=" * 60)
    fixer.enable_real_mode()
    
    result = fixer.fix_paths_batch(batch_size=200)
    
    if result.get('success', False):
        # 5. 验证结果
        logger.info("\n" + "=" * 60)
        fixer.verify_fix_results()
        
        logger.success("🎉 紧急路径修复完成!")
        logger.info("💡 建议重启相关服务以确保新路径生效")
    else:
        logger.error("❌ 路径修复失败，请检查错误信息")


if __name__ == "__main__":
    main()
