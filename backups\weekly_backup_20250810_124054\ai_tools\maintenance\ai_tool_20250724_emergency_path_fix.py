#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复千川自动化项目文件路径问题
清理条件: 问题解决后可删除
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Tuple

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from loguru import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.config_loader import load_settings

class EmergencyPathFixer:
    """紧急路径修复工具"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        self.process_dir = os.path.join(self.base_dir, '01_materials_to_process', '缇萃百货')
        
    def diagnose_path_issues(self):
        """诊断路径问题"""
        logger.info("🔍 开始诊断路径问题...")
        
        # 1. 检查基础目录
        logger.info(f"📁 检查基础目录: {self.base_dir}")
        if os.path.exists(self.base_dir):
            logger.success(f"✅ 基础目录存在: {self.base_dir}")
        else:
            logger.error(f"❌ 基础目录不存在: {self.base_dir}")
            return False
        
        # 2. 检查处理目录
        logger.info(f"📁 检查处理目录: {self.process_dir}")
        if os.path.exists(self.process_dir):
            logger.success(f"✅ 处理目录存在: {self.process_dir}")
        else:
            logger.error(f"❌ 处理目录不存在: {self.process_dir}")
            return False
        
        # 3. 检查实际文件
        actual_files = self.scan_actual_files()
        logger.info(f"📊 实际文件数量: {len(actual_files)}")
        
        # 4. 检查数据库记录
        db_records = self.scan_database_records()
        logger.info(f"📊 数据库记录数量: {len(db_records)}")
        
        # 5. 对比分析
        self.analyze_path_mismatches(actual_files, db_records)
        
        return True
    
    def scan_actual_files(self) -> List[str]:
        """扫描实际存在的文件"""
        logger.info("📋 扫描实际文件...")
        
        actual_files = []
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
        
        try:
            for file in os.listdir(self.process_dir):
                file_path = os.path.join(self.process_dir, file)
                if os.path.isfile(file_path):
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in video_extensions:
                        actual_files.append(file)
            
            logger.success(f"✅ 扫描完成，找到 {len(actual_files)} 个实际文件")
            
        except Exception as e:
            logger.error(f"❌ 扫描实际文件失败: {e}")
        
        return actual_files
    
    def scan_database_records(self) -> List[Dict]:
        """扫描数据库记录"""
        logger.info("📋 扫描数据库记录...")
        
        db_records = []
        
        try:
            with database_session() as db:
                creatives = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in creatives:
                    db_records.append({
                        'id': creative.id,
                        'filename': creative.filename,
                        'file_path': creative.file_path,
                        'status': creative.status
                    })
                
                logger.success(f"✅ 数据库扫描完成，找到 {len(db_records)} 条记录")
                
        except Exception as e:
            logger.error(f"❌ 扫描数据库记录失败: {e}")
        
        return db_records
    
    def analyze_path_mismatches(self, actual_files: List[str], db_records: List[Dict]):
        """分析路径不匹配问题"""
        logger.info("🔍 分析路径不匹配问题...")
        
        # 创建实际文件集合
        actual_files_set = set(actual_files)
        
        # 分析数据库记录
        missing_files = []
        path_format_issues = []
        
        for record in db_records:
            filename = record['filename']
            file_path = record['file_path']
            
            # 检查文件是否实际存在
            if filename not in actual_files_set:
                missing_files.append(record)
            
            # 检查路径格式问题
            if '\\' in file_path and '/' in file_path:
                path_format_issues.append(record)
        
        logger.critical(f"📊 分析结果:")
        logger.critical(f"   实际文件数量: {len(actual_files)}")
        logger.critical(f"   数据库记录数量: {len(db_records)}")
        logger.critical(f"   缺失文件数量: {len(missing_files)}")
        logger.critical(f"   路径格式问题: {len(path_format_issues)}")
        
        # 显示一些示例
        if missing_files:
            logger.warning("❌ 缺失文件示例:")
            for i, record in enumerate(missing_files[:5]):
                logger.warning(f"   {i+1}. {record['filename']} (ID: {record['id']})")
        
        if path_format_issues:
            logger.warning("⚠️ 路径格式问题示例:")
            for i, record in enumerate(path_format_issues[:5]):
                logger.warning(f"   {i+1}. {record['file_path']}")
        
        return missing_files, path_format_issues
    
    def fix_path_format_issues(self):
        """修复路径格式问题"""
        logger.info("🔧 开始修复路径格式问题...")
        
        fixed_count = 0
        
        try:
            with database_session() as db:
                # 查找所有路径格式有问题的记录
                creatives = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in creatives:
                    old_path = creative.file_path
                    
                    # 标准化路径格式
                    new_path = os.path.normpath(old_path)
                    
                    # 如果路径发生了变化，更新数据库
                    if new_path != old_path:
                        creative.file_path = new_path
                        fixed_count += 1
                        
                        if fixed_count <= 5:  # 只显示前5个示例
                            logger.info(f"🔧 修复路径: {old_path} -> {new_path}")
                
                if fixed_count > 0:
                    db.commit()
                    logger.success(f"✅ 路径格式修复完成，共修复 {fixed_count} 条记录")
                else:
                    logger.info("ℹ️ 没有发现需要修复的路径格式问题")
                
        except Exception as e:
            logger.error(f"❌ 修复路径格式失败: {e}")
            return False
        
        return True
    
    def test_file_access(self):
        """测试文件访问"""
        logger.info("🧪 测试文件访问...")
        
        test_files = [
            "7.21-蒿宏静-25.mp4",
            "7-22-曹晓敏-17.mp4",
            "7.17-王梦珂-1.mp4"
        ]
        
        for filename in test_files:
            # 测试不同的路径格式
            path_variants = [
                os.path.join(self.process_dir, filename),
                os.path.join(self.process_dir, filename).replace('\\', '/'),
                os.path.join(self.process_dir, filename).replace('/', '\\')
            ]
            
            logger.info(f"🔍 测试文件: {filename}")
            
            for i, path in enumerate(path_variants):
                exists = os.path.exists(path)
                logger.info(f"   格式{i+1}: {path} -> {'✅存在' if exists else '❌不存在'}")
                
                if exists:
                    break
    
    def emergency_fix(self):
        """执行紧急修复"""
        logger.critical("🚨 开始紧急修复...")
        
        # 1. 诊断问题
        if not self.diagnose_path_issues():
            logger.error("❌ 诊断失败，无法继续修复")
            return False
        
        # 2. 测试文件访问
        self.test_file_access()
        
        # 3. 修复路径格式
        if not self.fix_path_format_issues():
            logger.error("❌ 路径格式修复失败")
            return False
        
        # 4. 重新诊断验证
        logger.info("🔍 重新诊断验证修复效果...")
        self.diagnose_path_issues()
        
        logger.success("✅ 紧急修复完成！")
        return True

def main():
    """主函数"""
    try:
        fixer = EmergencyPathFixer()
        success = fixer.emergency_fix()
        
        if success:
            logger.success("🎉 紧急修复成功完成！")
            logger.info("💡 建议：重启工作流服务以应用修复")
        else:
            logger.error("❌ 紧急修复失败，需要人工干预")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 紧急修复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()
