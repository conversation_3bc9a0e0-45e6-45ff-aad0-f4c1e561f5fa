#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一提审状态管理器
================

本文件实现了千川自动化项目的统一提审状态管理器，
确保所有状态操作的原子性、一致性和并发安全。

版本: v1.0
创建时间: 2025-08-08
维护者: AI Assistant
"""

import redis
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from sqlalchemy.orm import Session
from sqlalchemy import text

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.appeal_status_definitions import (
    AppealStatus, AppealStage, VALID_STATE_TRANSITIONS,
    StateConsistencyRules, get_status_display_name
)
from qianchuan_aw.database.models import Campaign


class DistributedLock:
    """分布式锁实现"""
    
    def __init__(self, redis_client: redis.Redis, key: str, timeout: int = 60):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.acquired = False
    
    def acquire(self) -> bool:
        """获取锁"""
        result = self.redis.set(self.key, "locked", nx=True, ex=self.timeout)
        self.acquired = bool(result)
        return self.acquired
    
    def release(self):
        """释放锁"""
        if self.acquired:
            self.redis.delete(self.key)
            self.acquired = False
    
    @contextmanager
    def acquire_context(self):
        """锁的上下文管理器"""
        acquired = self.acquire()
        try:
            yield acquired
        finally:
            if acquired:
                self.release()


class UnifiedAppealStateManager:
    """
    统一提审状态管理器
    
    功能特性：
    1. 原子性状态转换
    2. 并发安全（分布式锁）
    3. 状态一致性检查
    4. 状态转换日志
    5. 错误恢复机制
    """
    
    def __init__(self, db_session: Session, redis_client: Optional[redis.Redis] = None):
        self.db = db_session
        self.redis = redis_client or self._get_redis_client()
    
    def _get_redis_client(self) -> redis.Redis:
        """获取Redis客户端"""
        try:
            # 这里应该从配置中获取Redis连接信息
            return redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存锁: {e}")
            return None
    
    def set_appeal_needed(self, campaign_id_qc: str, reason: str = "") -> Dict[str, Any]:
        """
        设置需要提审状态
        
        Args:
            campaign_id_qc: 计划ID
            reason: 设置原因
            
        Returns:
            操作结果
        """
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=None,  # 重置为未提审状态
            operation_type="set_appeal_needed",
            reason=reason,
            update_fields={
                'appeal_status': None,
                'appeal_attempt_count': 0
            }
        )
    
    def set_appeal_executing(self, campaign_id_qc: str) -> Dict[str, Any]:
        """设置提审执行中状态"""
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=AppealStatus.APPEAL_EXECUTING,
            operation_type="set_appeal_executing",
            update_fields={
                'appeal_status': AppealStatus.APPEAL_EXECUTING.value,
                'appeal_started_at': datetime.now(timezone.utc)
            }
        )
    
    def set_appeal_submitted(self, campaign_id_qc: str, message: str = "") -> Dict[str, Any]:
        """设置提审已提交状态"""
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=AppealStatus.APPEAL_SUBMITTED,
            operation_type="set_appeal_submitted",
            reason=message,
            update_fields={
                'appeal_status': AppealStatus.APPEAL_SUBMITTED.value
            }
        )
    
    def set_appeal_success(self, campaign_id_qc: str, message: str = "") -> Dict[str, Any]:
        """
        设置提审成功状态（原子操作）
        
        这是最重要的状态转换，必须确保所有相关字段同步更新
        """
        now = datetime.now(timezone.utc)
        
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=AppealStatus.APPEAL_PENDING,
            operation_type="set_appeal_success",
            reason=message,
            update_fields={
                'appeal_status': AppealStatus.APPEAL_PENDING.value,
                'last_appeal_at': now,
                'appeal_result': message
            },
            conditional_fields={
                # 只有首次提审成功时才设置first_appeal_at
                'first_appeal_at': ('IS NULL', now),
                # 增加尝试次数
                'appeal_attempt_count': ('INCREMENT', 1)
            }
        )
    
    def set_appeal_monitoring(self, campaign_id_qc: str) -> Dict[str, Any]:
        """设置申诉进度查询中状态"""
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=AppealStatus.APPEAL_MONITORING,
            operation_type="set_appeal_monitoring",
            update_fields={
                'appeal_status': AppealStatus.APPEAL_MONITORING.value,
                'last_appeal_check': datetime.now(timezone.utc)
            }
        )
    
    def set_appeal_final_result(self, campaign_id_qc: str, success: bool, message: str = "") -> Dict[str, Any]:
        """设置申诉最终结果"""
        final_status = AppealStatus.APPEAL_SUCCESS if success else AppealStatus.APPEAL_FAILED
        
        return self._execute_state_transition(
            campaign_id_qc=campaign_id_qc,
            to_status=final_status,
            operation_type="set_appeal_final_result",
            reason=message,
            update_fields={
                'appeal_status': final_status.value,
                'appeal_completed_at': datetime.now(timezone.utc),
                'appeal_result': message
            }
        )
    
    def _execute_state_transition(self, campaign_id_qc: str, to_status: Optional[AppealStatus],
                                 operation_type: str, reason: str = "",
                                 update_fields: Dict[str, Any] = None,
                                 conditional_fields: Dict[str, tuple] = None) -> Dict[str, Any]:
        """
        执行状态转换的核心方法
        
        Args:
            campaign_id_qc: 计划ID
            to_status: 目标状态
            operation_type: 操作类型
            reason: 操作原因
            update_fields: 需要更新的字段
            conditional_fields: 条件更新字段 {field: (condition, value)}
        """
        lock_key = f"appeal_state:{campaign_id_qc}"
        
        if self.redis:
            with DistributedLock(self.redis, lock_key, timeout=60).acquire_context() as acquired:
                if not acquired:
                    return {
                        'success': False,
                        'error': 'lock_failed',
                        'message': f'无法获取状态锁: {campaign_id_qc}'
                    }
                return self._do_state_transition(campaign_id_qc, to_status, operation_type, 
                                               reason, update_fields, conditional_fields)
        else:
            # 没有Redis时使用数据库事务保证原子性
            return self._do_state_transition(campaign_id_qc, to_status, operation_type,
                                           reason, update_fields, conditional_fields)
    
    def _do_state_transition(self, campaign_id_qc: str, to_status: Optional[AppealStatus],
                           operation_type: str, reason: str,
                           update_fields: Dict[str, Any] = None,
                           conditional_fields: Dict[str, tuple] = None) -> Dict[str, Any]:
        """执行实际的状态转换"""
        try:
            # 检查是否已经在事务中，如果没有则开始事务
            if not self.db.in_transaction():
                self.db.begin()
            
            # 获取当前计划
            campaign = self.db.query(Campaign).filter(
                Campaign.campaign_id_qc == campaign_id_qc
            ).first()
            
            if not campaign:
                self.db.rollback()
                return {
                    'success': False,
                    'error': 'campaign_not_found',
                    'message': f'计划不存在: {campaign_id_qc}'
                }
            
            # 记录当前状态
            current_status = AppealStatus(campaign.appeal_status) if campaign.appeal_status else None
            
            # 验证状态转换合法性
            if to_status and not self._is_valid_transition(current_status, to_status):
                self.db.rollback()
                return {
                    'success': False,
                    'error': 'invalid_transition',
                    'message': f'非法状态转换: {get_status_display_name(campaign.appeal_status)} → {get_status_display_name(to_status.value)}'
                }
            
            # 执行字段更新
            if update_fields:
                for field, value in update_fields.items():
                    setattr(campaign, field, value)
            
            # 执行条件字段更新
            if conditional_fields:
                for field, (condition, value) in conditional_fields.items():
                    if condition == 'IS NULL' and getattr(campaign, field) is None:
                        setattr(campaign, field, value)
                    elif condition == 'INCREMENT':
                        current_value = getattr(campaign, field) or 0
                        setattr(campaign, field, current_value + value)
            
            # 更新最后修改时间
            campaign.last_updated = datetime.now(timezone.utc)
            
            # 提交事务
            self.db.commit()
            
            # 记录状态转换日志
            self._log_state_transition(campaign_id_qc, current_status, to_status, operation_type, reason)
            
            return {
                'success': True,
                'operation_type': operation_type,
                'from_status': current_status.value if current_status else None,
                'to_status': to_status.value if to_status else None,
                'message': f'状态转换成功: {get_status_display_name(campaign.appeal_status if current_status else None)} → {get_status_display_name(to_status.value if to_status else None)}'
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"状态转换失败: {campaign_id_qc}, 错误: {e}", exc_info=True)
            return {
                'success': False,
                'error': 'transition_failed',
                'message': f'状态转换失败: {str(e)}'
            }
    
    def _is_valid_transition(self, from_status: Optional[AppealStatus], to_status: AppealStatus) -> bool:
        """检查状态转换是否合法"""
        allowed_transitions = VALID_STATE_TRANSITIONS.get(from_status, [])
        return to_status in allowed_transitions
    
    def _log_state_transition(self, campaign_id_qc: str, from_status: Optional[AppealStatus],
                            to_status: Optional[AppealStatus], operation_type: str, reason: str):
        """记录状态转换日志"""
        from_str = from_status.value if from_status else "NULL"
        to_str = to_status.value if to_status else "NULL"
        
        logger.info(f"🔄 提审状态转换: {campaign_id_qc} | {operation_type} | {from_str} → {to_str} | {reason}")
    
    def validate_state_consistency(self, campaign_id_qc: str) -> Dict[str, Any]:
        """验证计划状态一致性"""
        campaign = self.db.query(Campaign).filter(
            Campaign.campaign_id_qc == campaign_id_qc
        ).first()
        
        if not campaign:
            return {"valid": False, "error": "Campaign not found"}
        
        # 使用状态一致性规则进行验证
        issues = []
        
        # 检查appeal_pending状态一致性
        issues.extend(StateConsistencyRules.validate_appeal_pending_state(
            campaign.appeal_status, campaign.first_appeal_at, campaign.appeal_attempt_count
        ))
        
        # 检查首次提审时间一致性
        issues.extend(StateConsistencyRules.validate_first_appeal_consistency(
            campaign.first_appeal_at, campaign.appeal_status
        ))
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "campaign_id_qc": campaign_id_qc,
            "current_status": campaign.appeal_status,
            "first_appeal_at": campaign.first_appeal_at,
            "attempt_count": campaign.appeal_attempt_count
        }


# === 工厂函数和便捷接口 ===

def create_appeal_state_manager(db_session: Session) -> UnifiedAppealStateManager:
    """创建统一状态管理器实例"""
    return UnifiedAppealStateManager(db_session)


@contextmanager
def appeal_state_manager_context():
    """状态管理器上下文管理器"""
    from qianchuan_aw.database.database import SessionLocal

    db = SessionLocal()
    try:
        manager = UnifiedAppealStateManager(db)
        yield manager
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()


# === 使用示例 ===

def example_usage():
    """使用示例"""

    # 方式1：使用上下文管理器
    with appeal_state_manager_context() as manager:
        # 设置需要提审
        result = manager.set_appeal_needed("1839848221817219", "审核被拒绝，需要提审")
        print(f"设置需要提审: {result}")

        # 开始提审
        result = manager.set_appeal_executing("1839848221817219")
        print(f"开始提审: {result}")

        # 提审成功
        result = manager.set_appeal_success("1839848221817219", "提审表单提交成功")
        print(f"提审成功: {result}")

        # 验证状态一致性
        validation = manager.validate_state_consistency("1839848221817219")
        print(f"状态一致性检查: {validation}")


if __name__ == "__main__":
    example_usage()
