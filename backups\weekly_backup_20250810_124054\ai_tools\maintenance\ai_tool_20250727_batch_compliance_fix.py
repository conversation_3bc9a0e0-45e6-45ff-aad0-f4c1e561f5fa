#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 批量修复所有素材重复使用违规问题
清理条件: 系统完全合规后可删除

千川自动化批量合规性修复工具
========================

批量修复所有素材重复使用违规：
- 155个素材违规，445个重复计划
- 保留最早计划，删除重复计划
- 更新素材状态为已测试
- 确保系统完全合规
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))


def execute_sql_query(query: str):
    """执行SQL查询"""
    try:
        # 这里应该使用MCP工具执行SQL
        # 由于无法直接调用，我们提供SQL语句供手动执行
        print(f"执行SQL: {query}")
        return []
    except Exception as e:
        print(f"SQL执行失败: {e}")
        return []


def main():
    """主函数"""
    print("🚨 千川自动化批量合规性修复")
    print("=" * 50)
    
    # 生成修复SQL脚本
    fix_sql = """
-- 千川自动化批量合规性修复SQL脚本
-- 生成时间: {timestamp}
-- 目的: 修复所有素材重复使用违规问题

-- 第一步：查找所有违规素材和需要删除的计划
WITH violation_analysis AS (
    SELECT 
        lc.id as material_id,
        lc.filename,
        lc.file_hash,
        COUNT(DISTINCT c.id) as plan_count,
        MIN(c.id) as keep_plan_id,
        ARRAY_AGG(DISTINCT c.id ORDER BY c.created_at DESC) as all_plan_ids
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.id, lc.filename, lc.file_hash
    HAVING COUNT(DISTINCT c.id) > 1
),
plans_to_delete AS (
    SELECT 
        material_id,
        filename,
        keep_plan_id,
        UNNEST(all_plan_ids[2:]) as delete_plan_id
    FROM violation_analysis
)

-- 第二步：删除关联表记录
DELETE FROM campaign_platform_creative_association 
WHERE campaign_id IN (
    SELECT delete_plan_id FROM plans_to_delete
);

-- 第三步：删除重复计划
DELETE FROM campaigns 
WHERE id IN (
    SELECT delete_plan_id FROM plans_to_delete
);

-- 第四步：更新素材状态
UPDATE local_creatives 
SET status = MaterialStatus.ALREADY_TESTED.value
WHERE id IN (
    SELECT DISTINCT material_id FROM violation_analysis
);

-- 第五步：验证修复结果
SELECT 
    '修复验证' as check_type,
    COUNT(*) as remaining_violations
FROM (
    SELECT lc.id
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.id
    HAVING COUNT(DISTINCT c.id) > 1
) remaining_violations;
""".format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    # 保存SQL脚本
    sql_file = f"ai_tools/maintenance/compliance_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    with open(sql_file, 'w', encoding='utf-8') as f:
        f.write(fix_sql)
    
    print(f"📄 修复SQL脚本已生成: {sql_file}")
    
    # 生成分步执行的简化版本
    simple_fix_sql = """
-- 分步执行版本（更安全）

-- 1. 查看违规情况
SELECT 
    lc.filename,
    COUNT(DISTINCT c.id) as plan_count,
    STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids
FROM local_creatives lc
JOIN platform_creatives pc ON lc.id = pc.local_creative_id
JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
JOIN campaigns c ON cpca.campaign_id = c.id
JOIN ad_accounts aa ON c.account_id = aa.id
WHERE aa.account_type = 'TEST'
GROUP BY lc.filename
HAVING COUNT(DISTINCT c.id) > 1
ORDER BY plan_count DESC
LIMIT 10;

-- 2. 对于每个违规素材，手动执行以下步骤：
-- 2a. 查找该素材的所有计划（按创建时间排序）
-- 2b. 删除除最早计划外的所有计划
-- 2c. 更新素材状态为已测试

-- 示例（针对特定素材）：
-- DELETE FROM campaign_platform_creative_association WHERE campaign_id IN (计划ID列表);
-- DELETE FROM campaigns WHERE id IN (计划ID列表);
-- UPDATE local_creatives SET status = MaterialStatus.ALREADY_TESTED.value WHERE filename = '素材文件名';
"""
    
    simple_file = f"ai_tools/maintenance/compliance_fix_simple_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
    with open(simple_file, 'w', encoding='utf-8') as f:
        f.write(simple_fix_sql)
    
    print(f"📄 简化修复SQL脚本已生成: {simple_file}")
    
    # 生成修复报告
    report = f"""
千川自动化合规性修复计划
=====================
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚨 发现的问题:
- 155个素材违反"素材唯一性测试铁律"
- 445个重复计划被创建
- 严重违反系统合规性要求

🛠️ 修复策略:
1. 保留每个素材最早创建的测试计划
2. 删除所有重复创建的计划
3. 更新素材状态为"已测试"
4. 验证系统合规性

📋 执行步骤:
1. 使用MCP工具执行生成的SQL脚本
2. 验证修复结果
3. 确认系统完全合规

⚠️ 重要提醒:
- 这是系统合规性的核心问题
- 必须立即修复以确保业务规则遵循
- 建议加强计划创建前的重复检查
- 考虑添加数据库约束防止未来违规

📄 相关文件:
- 完整修复脚本: {sql_file}
- 简化修复脚本: {simple_file}
"""
    
    # 保存报告
    os.makedirs('ai_reports', exist_ok=True)
    report_file = f"ai_reports/compliance_fix_plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 修复计划报告已保存: {report_file}")
    
    print("\n🎯 下一步行动:")
    print("1. 使用MCP工具执行生成的SQL脚本")
    print("2. 验证修复结果")
    print("3. 确认系统完全合规")
    print("4. 加强未来的重复检查机制")
    
    return 0


if __name__ == '__main__':
    exit(main())
