#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 提审状态监控和保障机制
清理条件: 成为项目维护工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import Campaign
from qianchuan_aw.utils.unified_appeal_state_manager import create_appeal_state_manager
from qianchuan_aw.utils.appeal_status_definitions import (
    AppealStatus, StateConsistencyRules, get_status_display_name
)
from sqlalchemy import text


@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class AppealStateMonitor:
    """提审状态监控器"""
    
    def __init__(self):
        self.monitoring_enabled = True
        self.alert_thresholds = {
            'inconsistent_states': 5,           # 不一致状态超过5个时告警
            'stuck_appeals': 10,                # 卡住的提审超过10个时告警
            'failed_transitions': 3,            # 状态转换失败超过3次时告警
            'appeal_timeout_hours': 24          # 提审超过24小时未完成时告警
        }
    
    def run_comprehensive_monitoring(self) -> Dict[str, Any]:
        """运行全面的状态监控"""
        logger.info("🔍 开始提审状态全面监控")
        logger.info("="*60)
        
        monitoring_results = {
            'timestamp': datetime.now(timezone.utc),
            'consistency_check': self._check_state_consistency(),
            'stuck_appeals_check': self._check_stuck_appeals(),
            'timeout_appeals_check': self._check_timeout_appeals(),
            'performance_metrics': self._collect_performance_metrics(),
            'alerts': []
        }
        
        # 生成告警
        alerts = self._generate_alerts(monitoring_results)
        monitoring_results['alerts'] = alerts
        
        # 输出监控报告
        self._output_monitoring_report(monitoring_results)
        
        return monitoring_results
    
    def _check_state_consistency(self) -> Dict[str, Any]:
        """检查状态一致性"""
        logger.info("🔍 检查状态一致性...")
        
        with database_session() as db:
            # 查询所有可能不一致的状态
            consistency_query = text("""
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    appeal_attempt_count,
                    created_at,
                    CASE 
                        WHEN appeal_status = 'appeal_pending' AND first_appeal_at IS NULL THEN 'pending_without_time'
                        WHEN appeal_status = 'appeal_pending' AND (appeal_attempt_count IS NULL OR appeal_attempt_count = 0) THEN 'pending_without_count'
                        WHEN appeal_status IS NULL AND first_appeal_at IS NOT NULL THEN 'time_without_status'
                        WHEN appeal_status IS NOT NULL AND appeal_status NOT IN ('appeal_pending', 'appeal_executing', 'appeal_submitted', 'appeal_monitoring', 'appeal_success', 'appeal_failed', 'appeal_timeout') THEN 'invalid_status'
                        ELSE 'consistent'
                    END as consistency_issue
                FROM campaigns 
                WHERE created_at >= NOW() - INTERVAL '7 days'
            """)
            
            results = db.execute(consistency_query).fetchall()
            
            # 统计一致性问题
            consistency_stats = {
                'total_campaigns': len(results),
                'consistent': 0,
                'pending_without_time': 0,
                'pending_without_count': 0,
                'time_without_status': 0,
                'invalid_status': 0,
                'inconsistent_campaigns': []
            }
            
            for row in results:
                issue_type = row.consistency_issue
                consistency_stats[issue_type] += 1
                
                if issue_type != 'consistent':
                    consistency_stats['inconsistent_campaigns'].append({
                        'campaign_id_qc': row.campaign_id_qc,
                        'issue_type': issue_type,
                        'appeal_status': row.appeal_status,
                        'first_appeal_at': row.first_appeal_at,
                        'appeal_attempt_count': row.appeal_attempt_count
                    })
            
            total_inconsistent = len(consistency_stats['inconsistent_campaigns'])
            consistency_rate = (consistency_stats['consistent'] / consistency_stats['total_campaigns'] * 100) if consistency_stats['total_campaigns'] > 0 else 100
            
            logger.info(f"   总计划数: {consistency_stats['total_campaigns']}")
            logger.info(f"   一致性率: {consistency_rate:.1f}%")
            logger.info(f"   不一致数: {total_inconsistent}")
            
            return {
                'consistency_rate': consistency_rate,
                'total_inconsistent': total_inconsistent,
                'statistics': consistency_stats,
                'needs_alert': total_inconsistent > self.alert_thresholds['inconsistent_states']
            }
    
    def _check_stuck_appeals(self) -> Dict[str, Any]:
        """检查卡住的提审"""
        logger.info("🔍 检查卡住的提审...")
        
        with database_session() as db:
            # 查询长时间处于执行中状态的提审
            stuck_query = text("""
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    appeal_started_at,
                    NOW() - appeal_started_at as stuck_duration
                FROM campaigns 
                WHERE appeal_status = 'appeal_executing'
                  AND appeal_started_at < NOW() - INTERVAL '2 hours'
                ORDER BY appeal_started_at
            """)
            
            stuck_appeals = db.execute(stuck_query).fetchall()
            
            stuck_count = len(stuck_appeals)
            logger.info(f"   卡住的提审数: {stuck_count}")
            
            return {
                'stuck_count': stuck_count,
                'stuck_appeals': [
                    {
                        'campaign_id_qc': row.campaign_id_qc,
                        'stuck_duration': str(row.stuck_duration),
                        'appeal_started_at': row.appeal_started_at
                    }
                    for row in stuck_appeals
                ],
                'needs_alert': stuck_count > self.alert_thresholds['stuck_appeals']
            }
    
    def _check_timeout_appeals(self) -> Dict[str, Any]:
        """检查超时的提审"""
        logger.info("🔍 检查超时的提审...")
        
        timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=self.alert_thresholds['appeal_timeout_hours'])
        
        with database_session() as db:
            timeout_query = text("""
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    NOW() - first_appeal_at as appeal_duration
                FROM campaigns 
                WHERE first_appeal_at IS NOT NULL
                  AND first_appeal_at < :timeout_threshold
                  AND appeal_status NOT IN ('appeal_success', 'appeal_failed', 'appeal_timeout')
                ORDER BY first_appeal_at
            """)
            
            timeout_appeals = db.execute(timeout_query, {'timeout_threshold': timeout_threshold}).fetchall()
            
            timeout_count = len(timeout_appeals)
            logger.info(f"   超时提审数: {timeout_count}")
            
            return {
                'timeout_count': timeout_count,
                'timeout_appeals': [
                    {
                        'campaign_id_qc': row.campaign_id_qc,
                        'appeal_status': row.appeal_status,
                        'appeal_duration': str(row.appeal_duration),
                        'first_appeal_at': row.first_appeal_at
                    }
                    for row in timeout_appeals
                ],
                'needs_alert': timeout_count > 0
            }
    
    def _collect_performance_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        logger.info("📊 收集性能指标...")
        
        with database_session() as db:
            metrics_query = text("""
                SELECT 
                    COUNT(*) as total_campaigns,
                    COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as appealed_campaigns,
                    COUNT(CASE WHEN appeal_status = 'appeal_success' THEN 1 END) as successful_appeals,
                    COUNT(CASE WHEN appeal_status = 'appeal_failed' THEN 1 END) as failed_appeals,
                    COUNT(CASE WHEN appeal_status = 'appeal_pending' THEN 1 END) as pending_appeals,
                    AVG(CASE WHEN appeal_status = 'appeal_success' AND first_appeal_at IS NOT NULL 
                             THEN EXTRACT(EPOCH FROM (appeal_completed_at - first_appeal_at))/3600 END) as avg_success_hours
                FROM campaigns 
                WHERE created_at >= NOW() - INTERVAL '24 hours'
            """)
            
            result = db.execute(metrics_query).fetchone()
            
            # 计算成功率
            success_rate = (result.successful_appeals / result.appealed_campaigns * 100) if result.appealed_campaigns > 0 else 0
            
            metrics = {
                'total_campaigns': result.total_campaigns,
                'appealed_campaigns': result.appealed_campaigns,
                'successful_appeals': result.successful_appeals,
                'failed_appeals': result.failed_appeals,
                'pending_appeals': result.pending_appeals,
                'success_rate': round(success_rate, 2),
                'avg_success_hours': round(result.avg_success_hours or 0, 2)
            }
            
            logger.info(f"   24小时内总计划: {metrics['total_campaigns']}")
            logger.info(f"   已提审计划: {metrics['appealed_campaigns']}")
            logger.info(f"   提审成功率: {metrics['success_rate']}%")
            
            return metrics
    
    def _generate_alerts(self, monitoring_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成告警"""
        alerts = []
        
        # 状态一致性告警
        if monitoring_results['consistency_check']['needs_alert']:
            alerts.append({
                'type': 'CONSISTENCY_ALERT',
                'severity': 'HIGH',
                'message': f"发现 {monitoring_results['consistency_check']['total_inconsistent']} 个状态不一致的计划",
                'details': monitoring_results['consistency_check']['statistics']
            })
        
        # 卡住提审告警
        if monitoring_results['stuck_appeals_check']['needs_alert']:
            alerts.append({
                'type': 'STUCK_APPEALS_ALERT',
                'severity': 'MEDIUM',
                'message': f"发现 {monitoring_results['stuck_appeals_check']['stuck_count']} 个卡住的提审",
                'details': monitoring_results['stuck_appeals_check']['stuck_appeals']
            })
        
        # 超时提审告警
        if monitoring_results['timeout_appeals_check']['needs_alert']:
            alerts.append({
                'type': 'TIMEOUT_APPEALS_ALERT',
                'severity': 'MEDIUM',
                'message': f"发现 {monitoring_results['timeout_appeals_check']['timeout_count']} 个超时的提审",
                'details': monitoring_results['timeout_appeals_check']['timeout_appeals']
            })
        
        # 成功率告警
        success_rate = monitoring_results['performance_metrics']['success_rate']
        if success_rate < 80:  # 成功率低于80%时告警
            alerts.append({
                'type': 'LOW_SUCCESS_RATE_ALERT',
                'severity': 'HIGH',
                'message': f"提审成功率过低: {success_rate}%",
                'details': monitoring_results['performance_metrics']
            })
        
        return alerts
    
    def _output_monitoring_report(self, results: Dict[str, Any]):
        """输出监控报告"""
        logger.info("\n📋 提审状态监控报告")
        logger.info("="*60)
        
        # 一致性报告
        consistency = results['consistency_check']
        logger.info(f"🔍 状态一致性: {consistency['consistency_rate']:.1f}%")
        if consistency['total_inconsistent'] > 0:
            logger.warning(f"   ⚠️ 发现 {consistency['total_inconsistent']} 个不一致状态")
        
        # 性能报告
        metrics = results['performance_metrics']
        logger.info(f"📊 性能指标:")
        logger.info(f"   提审成功率: {metrics['success_rate']}%")
        logger.info(f"   平均处理时间: {metrics['avg_success_hours']} 小时")
        
        # 告警报告
        alerts = results['alerts']
        if alerts:
            logger.warning(f"🚨 发现 {len(alerts)} 个告警:")
            for alert in alerts:
                severity_icon = "🔴" if alert['severity'] == 'HIGH' else "🟡"
                logger.warning(f"   {severity_icon} {alert['type']}: {alert['message']}")
        else:
            logger.success("✅ 无告警，系统运行正常")


def main():
    """主函数"""
    monitor = AppealStateMonitor()
    results = monitor.run_comprehensive_monitoring()
    
    # 如果有高严重性告警，返回非零退出码
    high_severity_alerts = [a for a in results['alerts'] if a['severity'] == 'HIGH']
    if high_severity_alerts:
        logger.error(f"❌ 发现 {len(high_severity_alerts)} 个高严重性告警")
        return 1
    else:
        logger.success("✅ 监控完成，系统状态良好")
        return 0


if __name__ == "__main__":
    exit(main())
