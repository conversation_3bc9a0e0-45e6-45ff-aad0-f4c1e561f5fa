#!/usr/bin/env python3
"""
千川系统深度修复工具
解决仍然存在的素材跳过和账户限制问题
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class DeepSystemFixer:
    """深度系统修复器"""
    
    def __init__(self):
        logger.info("🔧 开始深度系统诊断和修复")
    
    def diagnose_current_status(self):
        """诊断当前状态"""
        logger.info("🔍 诊断当前素材状态")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询各状态素材数量
                status_query = text("""
                    SELECT status, COUNT(*) as count
                    FROM local_creatives 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                
                results = db.execute(status_query).fetchall()
                
                logger.info("📊 当前素材状态分布:")
                for row in results:
                    logger.info(f"  {row.status}: {row.count} 个")
                
                # 特别检查creating_plan状态的素材
                creating_plan_query = text("""
                    SELECT filename, updated_at, 
                           EXTRACT(EPOCH FROM (NOW() - updated_at))/60 as minutes_stuck
                    FROM local_creatives
                    WHERE status = 'creating_plan'
                    ORDER BY updated_at ASC
                    LIMIT 10
                """)
                
                creating_plan_results = db.execute(creating_plan_query).fetchall()
                
                if creating_plan_results:
                    logger.warning(f"🚨 发现 {len(creating_plan_results)} 个creating_plan状态素材:")
                    for row in creating_plan_results:
                        logger.warning(f"  {row.filename}: 卡住 {row.minutes_stuck:.1f} 分钟")
                
                return results
                
        except Exception as e:
            logger.error(f"❌ 诊断失败: {e}")
            return []
    
    def check_account_creative_requirements(self):
        """检查账户素材数量要求"""
        logger.info("\n🔍 检查账户素材数量要求")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询账户配置
                account_query = text("""
                    SELECT account_id_qc, account_type, creative_count,
                           (SELECT COUNT(*) FROM local_creatives lc 
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            WHERE pc.account_id = aa.id 
                            AND lc.status IN (MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.APPROVED.value, 'creating_plan')) as available_materials
                    FROM ad_accounts aa
                    WHERE status = 'active'
                    ORDER BY available_materials DESC
                """)
                
                results = db.execute(account_query).fetchall()
                
                logger.info("📊 账户素材配置:")
                for row in results:
                    status_icon = "✅" if row.available_materials >= row.creative_count else "❌"
                    logger.info(f"  {status_icon} {row.account_id_qc} ({row.account_type}):")
                    logger.info(f"    需要素材: {row.creative_count} 个")
                    logger.info(f"    可用素材: {row.available_materials} 个")
                    
                    if row.available_materials < row.creative_count:
                        logger.warning(f"    ⚠️ 素材不足，差 {row.creative_count - row.available_materials} 个")
                
                return results
                
        except Exception as e:
            logger.error(f"❌ 检查账户配置失败: {e}")
            return []
    
    def fix_creating_plan_status(self):
        """修复卡住的creating_plan状态"""
        logger.info("\n🔧 修复卡住的creating_plan状态")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 重置超过5分钟的creating_plan状态
                reset_query = text("""
                    UPDATE local_creatives 
                    SET status = MaterialStatus.APPROVED.value, 
                        updated_at = NOW()
                    WHERE status = 'creating_plan' 
                        AND updated_at < NOW() - INTERVAL '5 minutes'
                """)
                
                result = db.execute(reset_query)
                db.commit()
                
                if result.rowcount > 0:
                    logger.info(f"✅ 重置了 {result.rowcount} 个卡住的creating_plan状态素材")
                else:
                    logger.info("✅ 没有需要重置的creating_plan状态素材")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 重置creating_plan状态失败: {e}")
            return 0
    
    def adjust_account_creative_requirements(self):
        """调整账户素材数量要求"""
        logger.info("\n🔧 调整账户素材数量要求")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询当前配置
                current_config_query = text("""
                    SELECT account_id_qc, account_type, creative_count,
                           (SELECT COUNT(*) FROM local_creatives lc 
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            WHERE pc.account_id = aa.id 
                            AND lc.status IN (MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.APPROVED.value)) as available_materials
                    FROM ad_accounts aa
                    WHERE status = 'active'
                    AND creative_count > 1
                """)
                
                accounts = db.execute(current_config_query).fetchall()
                
                adjustments_made = 0
                
                for account in accounts:
                    if account.available_materials > 0 and account.available_materials < account.creative_count:
                        # 调整为可用素材数量，但至少为1
                        new_count = max(1, account.available_materials)
                        
                        update_query = text("""
                            UPDATE ad_accounts 
                            SET creative_count = :new_count
                            WHERE account_id_qc = :account_id
                        """)
                        
                        db.execute(update_query, {
                            'new_count': new_count,
                            'account_id': account.account_id_qc
                        })
                        
                        logger.info(f"✅ 调整账户 {account.account_id_qc}: {account.creative_count} → {new_count}")
                        adjustments_made += 1
                
                if adjustments_made > 0:
                    db.commit()
                    logger.info(f"✅ 总共调整了 {adjustments_made} 个账户的素材要求")
                else:
                    logger.info("✅ 所有账户配置都合理，无需调整")
                
                return adjustments_made
                
        except Exception as e:
            logger.error(f"❌ 调整账户配置失败: {e}")
            return 0
    
    def check_scheduler_logic(self):
        """检查调度器逻辑"""
        logger.info("\n🔍 检查调度器逻辑")
        logger.info("=" * 60)
        
        # 检查修复是否生效
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查修复是否存在
            fixes_present = []
            
            if "LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', MaterialStatus.APPROVED.value])" in content:
                fixes_present.append("状态筛选扩展")
            
            if "locked_creative.status not in [MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.APPROVED.value]" in content:
                fixes_present.append("状态检查逻辑")
            
            if fixes_present:
                logger.info("✅ 发现已应用的修复:")
                for fix in fixes_present:
                    logger.info(f"  - {fix}")
            else:
                logger.warning("⚠️ 修复可能未正确应用")
            
            # 检查是否还有旧的逻辑
            if "if locked_creative.status != MaterialStatus.UPLOADED_PENDING_PLAN.value:" in content:
                logger.warning("🚨 发现旧的状态检查逻辑，可能存在冲突")
                return False
            
            return len(fixes_present) > 0
            
        except Exception as e:
            logger.error(f"❌ 检查调度器逻辑失败: {e}")
            return False
    
    def create_emergency_config_fix(self):
        """创建紧急配置修复"""
        logger.info("\n🔧 创建紧急配置修复")
        logger.info("=" * 60)
        
        # 检查当前配置
        config_file = "config/settings.yml"
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            logger.info("📋 当前配置检查:")
            
            # 检查计划创建间隔
            if 'interval_seconds: 180' in config_content:
                logger.warning("⚠️ 计划创建间隔仍为180秒，建议调整为300秒")
                
                # 创建配置修复建议
                fix_suggestion = """
# 紧急配置修复建议
# 请在config/settings.yml中进行以下调整：

workflow:
  plan_creation:
    enabled: true
    interval_seconds: 300  # 从180调整为300，降低频率
  
  # 如果存在以下配置，请临时禁用
  file_ingestion:
    enabled: false  # 临时禁用，减少干扰
  
  material_monitoring:
    enabled: false  # 临时禁用，专注于计划创建

# 临时调整账户配置
# 建议将所有TEST账户的creative_count设置为1
"""
                
                with open("ai_tools/emergency_config_fix.txt", 'w', encoding='utf-8') as f:
                    f.write(fix_suggestion)
                
                logger.info("✅ 创建紧急配置修复建议: ai_tools/emergency_config_fix.txt")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置检查失败: {e}")
            return False
    
    def run_test_cycle(self):
        """运行测试周期"""
        logger.info("\n🧪 运行测试周期")
        logger.info("=" * 60)
        
        try:
            # 手动触发一次计划创建任务
            from src.qianchuan_aw.workflows.scheduler import handle_plan_creation
            from src.qianchuan_aw.utils.config_loader import load_settings
            
            app_settings = load_settings()
            
            with database_session() as db:
                logger.info("🚀 手动触发计划创建任务...")
                handle_plan_creation(db, app_settings)
                logger.info("✅ 测试周期完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试周期失败: {e}")
            return False
    
    def generate_action_plan(self):
        """生成行动计划"""
        logger.info("\n📋 生成紧急行动计划")
        logger.info("=" * 60)
        
        logger.info("🎯 立即执行步骤:")
        logger.info("1. 重置卡住的creating_plan状态素材")
        logger.info("2. 调整账户素材数量要求")
        logger.info("3. 优化配置参数")
        logger.info("4. 重启Celery服务")
        
        logger.info("\n⚡ 预期效果:")
        logger.info("- creating_plan跳过次数大幅减少")
        logger.info("- 账户素材数量要求合理化")
        logger.info("- 计划创建成功率提升")
        
        logger.info("\n🔍 监控指标:")
        logger.info("- 观察日志中的跳过次数")
        logger.info("- 检查是否有计划成功创建")
        logger.info("- 监控素材状态流转")

def main():
    """主修复函数"""
    try:
        fixer = DeepSystemFixer()
        
        # 1. 诊断当前状态
        current_status = fixer.diagnose_current_status()
        
        # 2. 检查账户配置
        account_config = fixer.check_account_creative_requirements()
        
        # 3. 修复卡住的状态
        reset_count = fixer.fix_creating_plan_status()
        
        # 4. 调整账户要求
        adjustment_count = fixer.adjust_account_creative_requirements()
        
        # 5. 检查调度器逻辑
        scheduler_ok = fixer.check_scheduler_logic()
        
        # 6. 创建配置修复
        config_ok = fixer.create_emergency_config_fix()
        
        # 7. 生成行动计划
        fixer.generate_action_plan()
        
        logger.info(f"\n🎉 深度修复完成!")
        logger.info(f"重置素材: {reset_count} 个")
        logger.info(f"调整账户: {adjustment_count} 个")
        logger.info(f"调度器状态: {'✅ 正常' if scheduler_ok else '❌ 异常'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 深度修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
