#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 简单紧急浏览器终止工具
清理条件: 长期保留，用于紧急情况处理

千川自动化项目简单紧急浏览器终止工具
==================================

不依赖项目环境，直接终止所有浏览器和Celery进程。
"""

import os
import psutil
import time
import subprocess
from datetime import datetime


def log(message):
    """简单日志函数"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"{timestamp} | {message}")


def check_system_status():
    """检查系统状态"""
    log("🔍 检查系统状态...")
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=2)
    log(f"CPU使用率: {cpu_percent}%")
    
    # 内存使用率
    memory = psutil.virtual_memory()
    log(f"内存使用率: {memory.percent}%")
    
    return cpu_percent, memory.percent


def find_browser_processes():
    """查找所有浏览器进程"""
    log("🔍 查找浏览器进程...")
    
    browser_processes = []
    browser_names = ['chrome', 'chromium', 'msedge', 'firefox', 'playwright']
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_name = proc.info['name'].lower()
            cmdline = ' '.join(proc.info['cmdline'] or []).lower()
            
            # 检查是否是浏览器进程
            if (any(browser in proc_name for browser in browser_names) or
                'headless' in cmdline or
                'qianchuan.jinritemai.com' in cmdline or
                '--remote-debugging-port' in cmdline):
                
                browser_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    log(f"找到 {len(browser_processes)} 个浏览器进程")
    for proc in browser_processes:
        log(f"  PID {proc['pid']}: {proc['name']}")
    
    return browser_processes


def find_celery_processes():
    """查找所有Celery进程"""
    log("🔍 查找Celery进程...")
    
    celery_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or []).lower()
            
            if 'celery' in cmdline and ('worker' in cmdline or 'beat' in cmdline):
                celery_processes.append({
                    'pid': proc.info['pid'],
                    'name': proc.info['name'],
                    'type': 'worker' if 'worker' in cmdline else 'beat'
                })
                
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    log(f"找到 {len(celery_processes)} 个Celery进程")
    for proc in celery_processes:
        log(f"  PID {proc['pid']}: {proc['name']} ({proc['type']})")
    
    return celery_processes


def kill_processes(processes, process_type):
    """终止进程列表"""
    log(f"🔪 终止所有{process_type}进程...")
    
    killed_count = 0
    
    # 第一轮：优雅终止
    for proc_info in processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            log(f"终止进程: PID {proc_info['pid']} ({proc_info['name']})")
            proc.terminate()
            killed_count += 1
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            log(f"无法终止进程 PID {proc_info['pid']}")
    
    # 等待进程终止
    log("等待进程终止...")
    time.sleep(5)
    
    # 第二轮：强制杀死
    for proc_info in processes:
        try:
            proc = psutil.Process(proc_info['pid'])
            if proc.is_running():
                log(f"强制杀死进程: PID {proc_info['pid']}")
                proc.kill()
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    log(f"✅ 已处理 {killed_count} 个{process_type}进程")
    return killed_count


def disable_appeal_in_config():
    """在配置文件中禁用提审模块"""
    log("🚫 禁用提审模块...")
    
    config_file = "config/settings.yml"
    
    if not os.path.exists(config_file):
        log(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        # 备份配置文件
        backup_file = f"{config_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(config_file, backup_file)
        log(f"✅ 已备份配置文件: {backup_file}")
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 禁用提审任务
        if 'appeal_plans:' in content and 'enabled: false' not in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'appeal_plans:' in line:
                    # 在下一行添加禁用配置
                    lines.insert(i + 1, '    enabled: false  # 紧急禁用 - 浏览器过载')
                    break
            
            # 写回文件
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            log("✅ 已禁用提审任务")
            return True
        else:
            log("⚠️ 提审任务已经被禁用或配置格式不匹配")
            return False
            
    except Exception as e:
        log(f"❌ 禁用提审模块失败: {e}")
        return False


def clean_temp_files():
    """清理临时文件"""
    log("🧹 清理临时文件...")
    
    cleaned_count = 0
    temp_dirs = [
        os.path.expanduser("~\\AppData\\Local\\Temp"),
        "C:\\Windows\\Temp"
    ]
    
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                for file in os.listdir(temp_dir):
                    if file.endswith(('.tmp', '.temp', '.log')) or 'chrome' in file.lower():
                        try:
                            file_path = os.path.join(temp_dir, file)
                            os.remove(file_path)
                            cleaned_count += 1
                        except:
                            pass
            except Exception as e:
                log(f"清理临时目录失败 {temp_dir}: {e}")
    
    log(f"✅ 已清理 {cleaned_count} 个临时文件")
    return cleaned_count


def main():
    """主函数"""
    log("🚨 开始千川自动化项目紧急浏览器终止...")
    
    try:
        # 1. 检查系统状态
        log("=" * 50)
        log("第一步: 检查系统状态")
        cpu_before, memory_before = check_system_status()
        
        # 2. 查找并终止浏览器进程
        log("=" * 50)
        log("第二步: 终止浏览器进程")
        browser_processes = find_browser_processes()
        if browser_processes:
            browser_killed = kill_processes(browser_processes, "浏览器")
        else:
            log("✅ 没有找到浏览器进程")
            browser_killed = 0
        
        # 3. 查找并终止Celery进程
        log("=" * 50)
        log("第三步: 终止Celery进程")
        celery_processes = find_celery_processes()
        if celery_processes:
            celery_killed = kill_processes(celery_processes, "Celery")
        else:
            log("✅ 没有找到Celery进程")
            celery_killed = 0
        
        # 4. 禁用提审模块
        log("=" * 50)
        log("第四步: 禁用提审模块")
        disable_appeal_in_config()
        
        # 5. 清理临时文件
        log("=" * 50)
        log("第五步: 清理临时文件")
        cleaned_files = clean_temp_files()
        
        # 6. 等待系统稳定
        log("=" * 50)
        log("第六步: 等待系统稳定")
        time.sleep(10)
        
        # 7. 检查处理效果
        log("=" * 50)
        log("第七步: 检查处理效果")
        cpu_after, memory_after = check_system_status()
        
        # 8. 生成报告
        log("=" * 50)
        log("🎉 紧急处理完成！")
        log(f"📊 处理结果:")
        log(f"  终止浏览器进程: {browser_killed} 个")
        log(f"  终止Celery进程: {celery_killed} 个")
        log(f"  清理临时文件: {cleaned_files} 个")
        log(f"  CPU使用率: {cpu_before:.1f}% -> {cpu_after:.1f}%")
        log(f"  内存使用率: {memory_before:.1f}% -> {memory_after:.1f}%")
        
        if cpu_after < 50:
            log("✅ CPU使用率已降低到正常范围")
        else:
            log("⚠️ CPU使用率仍然较高，建议重启系统")
        
        # 保存报告
        report_content = f"""
千川自动化项目紧急处理报告
========================

处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

处理结果:
- 终止浏览器进程: {browser_killed} 个
- 终止Celery进程: {celery_killed} 个  
- 清理临时文件: {cleaned_files} 个
- CPU使用率: {cpu_before:.1f}% -> {cpu_after:.1f}%
- 内存使用率: {memory_before:.1f}% -> {memory_after:.1f}%

执行的措施:
1. ✅ 终止所有浏览器进程
2. ✅ 终止所有Celery进程
3. ✅ 禁用提审模块
4. ✅ 清理临时文件

建议:
1. 检查系统是否恢复正常
2. 重新设计提审模块避免浏览器过载
3. 添加资源监控机制
4. 优化Celery配置
"""
        
        os.makedirs("ai_temp", exist_ok=True)
        report_file = f"ai_temp/emergency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        log(f"✅ 报告已保存: {report_file}")
        
        return 0
        
    except Exception as e:
        log(f"❌ 处理过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
