#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 测试账户验证和保护工具
清理条件: 成为项目安全组件，长期保留
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AdAccount, Campaign, Principal


class TestAccountValidator:
    """测试账户验证器 - 确保只对测试账户进行操作"""
    
    def __init__(self):
        self.violations = []
        self.fixes_applied = []
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """运行全面的测试账户验证"""
        logger.info("🛡️ 开始测试账户全面验证")
        logger.info("="*60)
        
        validation_results = {
            'timestamp': datetime.now(timezone.utc),
            'account_statistics': self._get_account_statistics(),
            'appeal_violations': self._check_appeal_violations(),
            'campaign_violations': self._check_campaign_violations(),
            'configuration_check': self._check_configuration(),
            'violations': self.violations,
            'fixes_applied': self.fixes_applied,
            'is_compliant': len(self.violations) == 0
        }
        
        # 输出验证报告
        self._output_validation_report(validation_results)
        
        return validation_results
    
    def _get_account_statistics(self) -> Dict[str, Any]:
        """获取账户统计信息"""
        logger.info("📊 收集账户统计信息...")
        
        with SessionLocal() as db:
            all_accounts = db.query(AdAccount).all()
            
            stats = {
                'total_accounts': len(all_accounts),
                'test_accounts': 0,
                'delivery_accounts': 0,
                'unset_accounts': 0,
                'active_accounts': 0,
                'test_account_details': []
            }
            
            for account in all_accounts:
                if account.account_type == 'TEST':
                    stats['test_accounts'] += 1
                    stats['test_account_details'].append({
                        'id': account.id,
                        'name': account.name,
                        'account_id_qc': account.account_id_qc,
                        'status': account.status,
                        'principal_name': account.principal.name if account.principal else 'Unknown'
                    })
                elif account.account_type == 'DELIVERY':
                    stats['delivery_accounts'] += 1
                else:
                    stats['unset_accounts'] += 1
                
                if account.status == 'active':
                    stats['active_accounts'] += 1
            
            logger.info(f"   总账户数: {stats['total_accounts']}")
            logger.info(f"   测试账户: {stats['test_accounts']}")
            logger.info(f"   投放账户: {stats['delivery_accounts']}")
            logger.info(f"   未设置类型: {stats['unset_accounts']}")
            logger.info(f"   活跃账户: {stats['active_accounts']}")
            
            return stats
    
    def _check_appeal_violations(self) -> Dict[str, Any]:
        """检查提审违规情况"""
        logger.info("🔍 检查提审违规情况...")
        
        with SessionLocal() as db:
            # 查询所有在提审状态的计划
            campaigns_in_appeal = db.query(Campaign, AdAccount).join(AdAccount).filter(
                Campaign.appeal_status.isnot(None)
            ).all()
            
            violations = []
            test_appeals = 0
            non_test_appeals = 0
            
            for campaign, account in campaigns_in_appeal:
                if account.account_type == 'TEST':
                    test_appeals += 1
                else:
                    non_test_appeals += 1
                    violations.append({
                        'campaign_id_qc': campaign.campaign_id_qc,
                        'account_type': account.account_type,
                        'account_name': account.name,
                        'appeal_status': campaign.appeal_status,
                        'first_appeal_at': campaign.first_appeal_at,
                        'violation_type': 'NON_TEST_ACCOUNT_APPEAL'
                    })
            
            if violations:
                self.violations.extend(violations)
                logger.error(f"🚨 发现 {len(violations)} 个非测试账户提审违规")
            else:
                logger.success("✅ 所有提审操作都限制在测试账户")
            
            return {
                'total_appeals': len(campaigns_in_appeal),
                'test_appeals': test_appeals,
                'non_test_appeals': non_test_appeals,
                'violations': violations
            }
    
    def _check_campaign_violations(self) -> Dict[str, Any]:
        """检查计划操作违规情况"""
        logger.info("🔍 检查计划操作违规情况...")
        
        with SessionLocal() as db:
            # 检查最近创建的计划是否都在测试账户
            recent_campaigns = db.query(Campaign, AdAccount).join(AdAccount).filter(
                Campaign.created_at >= datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
            ).all()
            
            violations = []
            test_campaigns = 0
            non_test_campaigns = 0
            
            for campaign, account in recent_campaigns:
                if account.account_type == 'TEST':
                    test_campaigns += 1
                else:
                    non_test_campaigns += 1
                    violations.append({
                        'campaign_id_qc': campaign.campaign_id_qc,
                        'account_type': account.account_type,
                        'account_name': account.name,
                        'created_at': campaign.created_at,
                        'violation_type': 'NON_TEST_ACCOUNT_CAMPAIGN'
                    })
            
            if violations:
                self.violations.extend(violations)
                logger.warning(f"⚠️ 发现 {len(violations)} 个非测试账户计划创建")
            else:
                logger.success("✅ 所有计划创建都限制在测试账户")
            
            return {
                'total_recent_campaigns': len(recent_campaigns),
                'test_campaigns': test_campaigns,
                'non_test_campaigns': non_test_campaigns,
                'violations': violations
            }
    
    def _check_configuration(self) -> Dict[str, Any]:
        """检查配置文件合规性"""
        logger.info("🔍 检查配置文件合规性...")
        
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            
            config_issues = []
            
            # 检查工作流配置
            workflow_config = settings.get('workflow', {})
            
            # 检查独立收割配置
            independent_harvest = workflow_config.get('independent_harvest', {})
            if independent_harvest.get('enabled', False):
                scope = independent_harvest.get('scope', {})
                account_type_filter = scope.get('account_type_filter', [])
                test_accounts_only = scope.get('test_accounts_only', False)
                
                if 'TEST' not in account_type_filter:
                    config_issues.append("independent_harvest.scope.account_type_filter 未包含 'TEST'")
                
                if not test_accounts_only:
                    config_issues.append("independent_harvest.scope.test_accounts_only 未设置为 true")
            
            # 检查提审策略
            appeal_strategy = settings.get('appeal_strategy', {})
            appeal_for_prod = appeal_strategy.get('appeal_for_prod_plans', True)
            
            if appeal_for_prod:
                config_issues.append("appeal_strategy.appeal_for_prod_plans 应设置为 false")
            
            if config_issues:
                self.violations.extend([{'type': 'CONFIG_ISSUE', 'issue': issue} for issue in config_issues])
                logger.warning(f"⚠️ 发现 {len(config_issues)} 个配置问题")
            else:
                logger.success("✅ 配置文件合规")
            
            return {
                'config_issues': config_issues,
                'independent_harvest_config': independent_harvest,
                'appeal_strategy_config': appeal_strategy
            }
            
        except Exception as e:
            logger.error(f"配置检查失败: {e}")
            return {'error': str(e)}
    
    def fix_violations(self, dry_run: bool = True) -> Dict[str, Any]:
        """修复违规问题"""
        logger.info(f"🔧 开始修复违规问题 (dry_run={dry_run})...")
        
        if not self.violations:
            logger.info("✅ 无违规问题需要修复")
            return {'fixes_applied': 0}
        
        fixes_count = 0
        
        with SessionLocal() as db:
            # 修复非测试账户的提审状态
            appeal_violations = [v for v in self.violations if v.get('violation_type') == 'NON_TEST_ACCOUNT_APPEAL']
            
            if appeal_violations:
                campaign_ids = [v['campaign_id_qc'] for v in appeal_violations]
                
                if not dry_run:
                    updated = db.query(Campaign).filter(
                        Campaign.campaign_id_qc.in_(campaign_ids)
                    ).update({
                        Campaign.appeal_status: None,
                        Campaign.appeal_attempt_count: 0,
                        Campaign.first_appeal_at: None,
                        Campaign.last_appeal_at: None,
                        Campaign.appeal_error_message: '违规修复：非测试账户不允许提审'
                    }, synchronize_session=False)
                    
                    db.commit()
                    fixes_count += updated
                    self.fixes_applied.append(f"重置了 {updated} 个非测试账户的提审状态")
                    logger.success(f"✅ 重置了 {updated} 个非测试账户的提审状态")
                else:
                    logger.info(f"📋 计划重置 {len(campaign_ids)} 个非测试账户的提审状态")
                    fixes_count += len(campaign_ids)
        
        return {'fixes_applied': fixes_count}
    
    def _output_validation_report(self, results: Dict[str, Any]):
        """输出验证报告"""
        logger.info("\n📋 测试账户验证报告")
        logger.info("="*60)
        
        # 账户统计
        stats = results['account_statistics']
        logger.info(f"📊 账户统计:")
        logger.info(f"   总账户: {stats['total_accounts']}")
        logger.info(f"   测试账户: {stats['test_accounts']}")
        logger.info(f"   投放账户: {stats['delivery_accounts']}")
        
        # 违规情况
        if results['violations']:
            logger.error(f"🚨 发现 {len(results['violations'])} 个违规:")
            for violation in results['violations'][:5]:  # 只显示前5个
                if violation.get('violation_type') == 'NON_TEST_ACCOUNT_APPEAL':
                    logger.error(f"   - 计划 {violation['campaign_id_qc']} 在非测试账户 {violation['account_name']} 中提审")
        else:
            logger.success("✅ 无违规问题")
        
        # 合规状态
        if results['is_compliant']:
            logger.success("🎉 测试账户验证通过，系统合规运行")
        else:
            logger.error("❌ 测试账户验证失败，存在违规操作")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试账户验证工具')
    parser.add_argument('--validate', action='store_true', help='运行验证')
    parser.add_argument('--fix', action='store_true', help='修复违规问题')
    parser.add_argument('--dry-run', action='store_true', help='预览模式')
    
    args = parser.parse_args()
    
    validator = TestAccountValidator()
    
    if args.fix or (not args.validate):
        # 默认运行验证和修复
        results = validator.run_comprehensive_validation()
        
        if results['violations']:
            logger.info("\n🔧 开始修复违规问题...")
            fix_results = validator.fix_violations(dry_run=args.dry_run)
            
            if args.dry_run:
                logger.info(f"📋 预览：将修复 {fix_results['fixes_applied']} 个问题")
            else:
                logger.success(f"✅ 修复完成：{fix_results['fixes_applied']} 个问题")
    
    elif args.validate:
        results = validator.run_comprehensive_validation()
    
    return 0 if validator.violations == [] else 1


if __name__ == "__main__":
    exit(main())
