#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 简单测试批量跨账户复制功能
依赖关系: streamlit, web_ui.py
清理条件: 测试完成后可删除
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

def test_batch_replication_ui():
    """测试批量跨账户复制UI组件"""
    st.title("🧪 批量跨账户复制功能测试")
    
    # 测试基本组件
    st.subheader("1. 基本组件测试")
    
    # 测试选项卡
    tab1, tab2 = st.tabs(["📋 单账户复制", "🚀 批量跨账户复制"])
    
    with tab1:
        st.info("单账户复制测试")
        st.write("这是单账户复制选项卡")
    
    with tab2:
        st.info("批量跨账户复制测试")
        st.write("这是批量跨账户复制选项卡")
        
        # 测试步骤指示器
        st.subheader("2. 步骤指示器测试")
        step_cols = st.columns(4)
        with step_cols[0]:
            st.success("✅ 1. 选择源账户")
        with step_cols[1]:
            st.info("2. 设置筛选条件")
        with step_cols[2]:
            st.info("3. 选择计划")
        with step_cols[3]:
            st.info("4. 执行复制")
        
        # 测试计划显示（不使用嵌套expander）
        st.subheader("3. 计划显示测试")
        
        # 模拟计划数据
        account_stats = {
            "测试账户1": ["计划A", "计划B", "计划C"],
            "测试账户2": ["计划D", "计划E"]
        }
        
        for account_name, campaigns in account_stats.items():
            st.markdown(f"**📁 {account_name} ({len(campaigns)} 个计划)**")
            
            if campaigns:
                # 将计划分成两列显示
                mid_point = len(campaigns) // 2
                col1, col2 = st.columns(2)
                
                with col1:
                    for campaign_name in campaigns[:mid_point]:
                        st.write(f"• {campaign_name}")
                
                with col2:
                    for campaign_name in campaigns[mid_point:]:
                        st.write(f"• {campaign_name}")
            
            st.markdown("---")
        
        # 测试任务控制按钮
        st.subheader("4. 任务控制测试")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("⏸️ 暂停任务", use_container_width=True):
                st.success("暂停按钮点击成功")
        
        with col2:
            if st.button("🛑 结束任务", use_container_width=True):
                st.success("结束按钮点击成功")
        
        with col3:
            st.info("任务状态: 🔄 运行中")
        
        # 测试进度显示
        st.subheader("5. 进度显示测试")
        progress_value = 0.6
        st.progress(progress_value, text=f"进度: 6/10 ({progress_value:.1%})")
        
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("已完成", 6)
        with col2:
            st.metric("成功", 5)
        with col3:
            st.metric("失败", 1)

def test_imports():
    """测试导入"""
    st.subheader("6. 导入测试")
    
    try:
        # 测试核心模块导入
        from tools import replicate_plan as replicate_plan_tool
        st.success("✅ replicate_plan_tool 导入成功")
        
        # 测试账户选择器导入
        from ai_tool_20250718_maintenance_unified_account_selector import (
            create_multi_account_selector
        )
        st.success("✅ 统一账户选择器导入成功")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    st.set_page_config(
        page_title="批量跨账户复制测试",
        page_icon="🧪",
        layout="wide"
    )
    
    # 测试UI组件
    test_batch_replication_ui()
    
    # 测试导入
    test_imports()
    
    st.markdown("---")
    st.success("🎉 如果以上所有组件都正常显示，说明修复成功！")
    st.info("💡 现在可以重新启动主应用：streamlit run web_ui.py")

if __name__ == "__main__":
    main()
