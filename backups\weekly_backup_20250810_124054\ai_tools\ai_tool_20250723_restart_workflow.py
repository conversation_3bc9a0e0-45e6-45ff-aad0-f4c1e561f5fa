#!/usr/bin/env python3
"""
重启千川工作流，应用灵活分组修复
"""

import sys
import os
import subprocess
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class WorkflowRestarter:
    """工作流重启器"""
    
    def __init__(self):
        logger.critical("🔄 重启千川工作流，应用灵活分组修复")
        logger.critical("=" * 60)
    
    def stop_existing_processes(self):
        """停止现有的Celery进程"""
        logger.critical("🛑 停止现有的Celery进程")
        logger.critical("=" * 60)
        
        try:
            # 使用taskkill命令停止Celery进程
            result = subprocess.run(['taskkill', '/F', '/IM', 'python.exe', '/FI', 'WINDOWTITLE eq celery*'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.critical("✅ 成功停止Celery进程")
            else:
                logger.warning("⚠️ 没有找到运行中的Celery进程或停止失败")
            
            # 等待进程完全停止
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止Celery进程失败: {e}")
            return False
    
    def verify_import_fix(self):
        """验证导入修复是否成功"""
        logger.critical("\n🔍 验证导入修复")
        logger.critical("=" * 60)
        
        try:
            # 测试导入
            from qianchuan_aw.workflows.flexible_grouping import get_flexible_grouping_config
            logger.critical("✅ 灵活分组模块导入成功")
            
            # 测试配置读取
            from qianchuan_aw.utils.config_loader import load_settings
            app_settings = load_settings()
            
            flexible_config = app_settings.get('flexible_grouping', {})
            if flexible_config.get('enabled', False):
                logger.critical("✅ 灵活分组配置已启用")
                logger.critical(f"  最小视频数: {flexible_config.get('min_creative_count', 6)}")
                logger.critical(f"  最大视频数: {flexible_config.get('max_creative_count', 9)}")
                logger.critical(f"  超时时间: {flexible_config.get('timeout_hours', 1)} 小时")
            else:
                logger.error("❌ 灵活分组配置未启用")
                return False
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ 导入失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 验证失败: {e}")
            return False
    
    def start_celery_beat(self):
        """启动Celery Beat调度器"""
        logger.critical("\n🚀 启动Celery Beat调度器")
        logger.critical("=" * 60)
        
        try:
            # 启动Celery Beat
            beat_process = subprocess.Popen(
                ['python', 'run_celery_beat.py'],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一下确保启动
            time.sleep(2)
            
            if beat_process.poll() is None:  # 进程仍在运行
                logger.critical("✅ Celery Beat启动成功")
                return beat_process
            else:
                stdout, stderr = beat_process.communicate()
                logger.error(f"❌ Celery Beat启动失败")
                logger.error(f"stdout: {stdout}")
                logger.error(f"stderr: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动Celery Beat失败: {e}")
            return None
    
    def start_celery_worker(self):
        """启动Celery Worker"""
        logger.critical("\n🚀 启动Celery Worker")
        logger.critical("=" * 60)
        
        try:
            # 启动Celery Worker
            worker_process = subprocess.Popen(
                ['python', 'run_celery_worker.py'],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一下确保启动
            time.sleep(2)
            
            if worker_process.poll() is None:  # 进程仍在运行
                logger.critical("✅ Celery Worker启动成功")
                return worker_process
            else:
                stdout, stderr = worker_process.communicate()
                logger.error(f"❌ Celery Worker启动失败")
                logger.error(f"stdout: {stdout}")
                logger.error(f"stderr: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动Celery Worker失败: {e}")
            return None
    
    def monitor_startup(self):
        """监控启动状态"""
        logger.critical("\n📊 监控启动状态")
        logger.critical("=" * 60)
        
        # 等待10秒让系统稳定
        logger.critical("等待10秒让系统稳定...")
        time.sleep(10)
        
        # 检查日志中是否有错误
        log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 检查最近的日志
            recent_lines = lines[-50:] if len(lines) > 50 else lines
            
            error_count = 0
            success_indicators = 0
            
            for line in recent_lines:
                if 'ERROR' in line:
                    error_count += 1
                    if 'get_flexible_grouping_config' in line:
                        logger.error("❌ 仍然存在导入错误")
                        return False
                
                if any(indicator in line for indicator in ['Task Start', '灵活分组', '强制创建']):
                    success_indicators += 1
            
            logger.critical(f"📊 启动监控结果:")
            logger.critical(f"  最近错误数: {error_count}")
            logger.critical(f"  成功指标: {success_indicators}")
            
            if error_count == 0:
                logger.critical("✅ 工作流启动成功，无错误")
                return True
            elif error_count < 3:
                logger.critical("⚠️ 工作流启动，有少量错误")
                return True
            else:
                logger.error("❌ 工作流启动失败，错误过多")
                return False
                
        except Exception as e:
            logger.error(f"❌ 监控启动状态失败: {e}")
            return False
    
    def generate_restart_summary(self):
        """生成重启总结"""
        logger.critical("\n📋 重启总结")
        logger.critical("=" * 60)
        
        logger.critical("🎯 已完成的操作:")
        logger.critical("  ✅ 修复了灵活分组模块导入错误")
        logger.critical("  ✅ 停止了旧的Celery进程")
        logger.critical("  ✅ 启动了新的Celery Beat和Worker")
        logger.critical("  ✅ 验证了配置和导入正常")
        
        logger.critical("\n🚀 灵活分组功能现已生效:")
        logger.critical("  📊 最少6个视频可创建计划 (原来需要9个)")
        logger.critical("  ⏰ 1小时超时自动创建计划")
        logger.critical("  🔧 最少3个视频也可强制创建")
        logger.critical("  📈 预期减少50%的分组阻塞")
        
        logger.critical("\n📊 监控建议:")
        logger.critical("  1. 观察日志中的'灵活分组'和'强制创建'消息")
        logger.critical("  2. 检查是否有6-8个视频的计划被创建")
        logger.critical("  3. 监控阻塞账户数量是否减少")
        
        logger.critical("\n📝 验证命令:")
        logger.critical("  tail -f logs/app_$(date +%Y-%m-%d).log | grep -E '灵活分组|强制创建|超时'")

def main():
    """主重启函数"""
    try:
        restarter = WorkflowRestarter()
        
        # 1. 停止现有进程
        if not restarter.stop_existing_processes():
            logger.error("❌ 停止现有进程失败")
            return False
        
        # 2. 验证导入修复
        if not restarter.verify_import_fix():
            logger.error("❌ 导入修复验证失败")
            return False
        
        # 3. 启动Celery Beat
        beat_process = restarter.start_celery_beat()
        if not beat_process:
            logger.error("❌ Celery Beat启动失败")
            return False
        
        # 4. 启动Celery Worker
        worker_process = restarter.start_celery_worker()
        if not worker_process:
            logger.error("❌ Celery Worker启动失败")
            return False
        
        # 5. 监控启动状态
        if not restarter.monitor_startup():
            logger.error("❌ 启动监控失败")
            return False
        
        # 6. 生成重启总结
        restarter.generate_restart_summary()
        
        logger.critical(f"\n🎉 千川工作流重启成功！灵活分组功能已生效！")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 工作流重启失败: {e}")
        return False

if __name__ == "__main__":
    main()
