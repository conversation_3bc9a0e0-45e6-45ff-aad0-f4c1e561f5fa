#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复多账户选择器的筛选功能问题
依赖关系: 统一账户选择器
清理条件: 功能被替代时删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_enhanced_filter_logic():
    """创建增强的筛选逻辑"""
    
    enhanced_code = '''
    # 筛选和统计区域
    if show_filter or show_stats:
        st.markdown("#### ⭐ 账户筛选与收藏管理")
        
        # 搜索框
        search_term = ""
        show_favorites_only = False
        show_authorized_only = False
        
        if show_filter:
            # 搜索框
            search_term = st.text_input(
                "🔍 搜索账户",
                placeholder="输入账户名称或千川ID搜索...",
                key=f"{key}_search_input",
                help="支持按账户名称或千川ID进行搜索"
            )
            
            # 筛选选项
            filter_col1, filter_col2 = st.columns(2)
            with filter_col1:
                show_favorites_only = st.checkbox(
                    "⭐ 仅显示收藏账户",
                    key=f"{key}_favorites_filter",
                    help="只显示已收藏的账户"
                )
            with filter_col2:
                show_authorized_only = st.checkbox(
                    "📱 仅显示已授权账户",
                    key=f"{key}_authorized_filter",
                    help="只显示已授权抖音号的账户"
                )
        
        # 统计信息
        if show_stats:
            total_count = len(accounts)
            favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
            authorized_count = len([acc for acc in accounts if getattr(acc, 'douyin_id', None)])
            
            stats_col1, stats_col2, stats_col3 = st.columns(3)
            with stats_col1:
                st.metric("总账户数", total_count)
            with stats_col2:
                st.metric("收藏账户", favorite_count)
            with stats_col3:
                st.metric("已授权账户", authorized_count)
        
        # 收藏管理按钮
        if show_filter and show_stats and not in_form:
            if st.button("🛠️ 管理收藏", key=f"{key}_manage_favorites"):
                st.session_state[f"{key}_show_favorite_manager"] = True
    
    # 根据筛选条件过滤账户
    filtered_accounts = []
    for account in accounts:
        # 搜索筛选
        if search_term:
            search_lower = search_term.lower()
            if (search_lower not in account.name.lower() and 
                search_term not in str(account.account_id_qc)):
                continue
        
        # 收藏筛选
        if show_favorites_only and not getattr(account, 'is_favorite', False):
            continue
            
        # 授权筛选
        if show_authorized_only and not getattr(account, 'douyin_id', None):
            continue
            
        filtered_accounts.append(account)
    
    # 显示筛选结果统计
    if show_filter and (search_term or show_favorites_only or show_authorized_only):
        if filtered_accounts:
            st.info(f"📊 筛选结果：显示 {len(filtered_accounts)} 个账户（共 {len(accounts)} 个）")
        else:
            st.warning("🔍 没有找到符合筛选条件的账户")
            return [] if multi_select else None
    
    # 按收藏状态排序（收藏的在前）
    filtered_accounts.sort(key=lambda x: (not getattr(x, 'is_favorite', False), x.name))
'''
    
    return enhanced_code

def apply_filter_fix():
    """应用筛选功能修复"""
    
    selector_path = project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_unified_account_selector.py'
    
    print("🔧 修复多账户选择器筛选功能...")
    print("💡 添加搜索框、收藏筛选、授权筛选功能")
    
    try:
        # 读取当前文件
        with open(selector_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成增强的筛选代码
        enhanced_code = create_enhanced_filter_logic()
        
        # 查找并替换筛选逻辑部分
        import re
        
        # 匹配从"筛选和统计区域"到"根据筛选条件过滤账户"的整个部分
        pattern = r'    # 筛选和统计区域.*?    # 按收藏状态排序（收藏的在前）\n    filtered_accounts\.sort\(key=lambda x: \(not getattr\(x, \'is_favorite\', False\), x\.name\)\)'
        
        if re.search(pattern, content, re.DOTALL):
            # 替换筛选逻辑
            new_content = re.sub(pattern, enhanced_code.strip(), content, flags=re.DOTALL)
            
            # 写入文件
            with open(selector_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 筛选功能修复应用成功")
            return True
        else:
            print("❌ 未找到筛选逻辑代码段")
            return False
            
    except Exception as e:
        print(f"❌ 修复应用失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 多账户选择器筛选功能修复")
    print("=" * 60)
    print("🎯 目标：修复批量账户复制页面中的筛选功能失效问题")
    print()
    
    print("🔍 问题分析:")
    print("  ❌ 缺少搜索框功能")
    print("  ❌ 缺少'仅已授权'筛选选项")
    print("  ❌ 筛选逻辑不完整")
    print("  ❌ 筛选结果统计不准确")
    print()
    
    print("💡 修复内容:")
    print("  ✅ 添加搜索框，支持按账户名称和千川ID搜索")
    print("  ✅ 添加'仅显示收藏账户'复选框")
    print("  ✅ 添加'仅显示已授权账户'复选框")
    print("  ✅ 实现多条件AND逻辑筛选")
    print("  ✅ 显示准确的筛选结果统计")
    print("  ✅ 优化筛选界面布局")
    print()
    
    print("🔧 技术改进:")
    print("  - 搜索支持大小写不敏感匹配")
    print("  - 筛选条件可以组合使用")
    print("  - 实时显示筛选结果统计")
    print("  - 优化用户界面布局和提示")
    print()
    
    # 应用修复
    if apply_filter_fix():
        print("🎉 多账户选择器筛选功能修复成功！")
        print()
        print("🔧 修复效果:")
        print("  ✅ 搜索框：输入账户名称或千川ID进行实时搜索")
        print("  ✅ 收藏筛选：勾选'仅显示收藏账户'只显示收藏的账户")
        print("  ✅ 授权筛选：勾选'仅显示已授权账户'只显示有抖音号的账户")
        print("  ✅ 组合筛选：多个条件可以同时生效")
        print("  ✅ 统计显示：准确显示筛选结果数量")
        print()
        print("🧪 测试建议:")
        print("  1. 重启 Streamlit 应用")
        print("  2. 进入批量账户复制页面第一步")
        print("  3. 测试搜索框功能：输入账户名称或ID")
        print("  4. 测试收藏筛选：勾选'仅显示收藏账户'")
        print("  5. 测试授权筛选：勾选'仅显示已授权账户'")
        print("  6. 测试组合筛选：同时使用多个筛选条件")
        print("  7. 验证筛选结果统计是否准确")
        print()
        print("⚡ 预期效果:")
        print("  - 搜索功能: 实时筛选，大小写不敏感")
        print("  - 收藏筛选: 只显示⭐标记的账户")
        print("  - 授权筛选: 只显示有📱标记的账户")
        print("  - 组合筛选: 多条件AND逻辑生效")
        print("  - 统计准确: 显示正确的筛选结果数量")
        
        return True
    else:
        print("❌ 修复应用失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
