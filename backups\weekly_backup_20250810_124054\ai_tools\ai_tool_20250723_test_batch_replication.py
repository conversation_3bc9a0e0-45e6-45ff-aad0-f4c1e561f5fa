#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 测试批量跨账户计划复制功能
依赖关系: web_ui.py, replicate_plan_tool
清理条件: 功能测试完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试导入...")
    
    try:
        # 测试核心模块导入
        from tools import replicate_plan as replicate_plan_tool
        print("✅ replicate_plan_tool 导入成功")
        
        # 测试账户选择器导入
        sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))
        from ai_tool_20250718_maintenance_unified_account_selector import (
            create_multi_account_selector,
            get_accounts_with_favorites
        )
        print("✅ 统一账户选择器导入成功")
        
        # 测试全局账户选择器导入
        from ai_tool_20250718_maintenance_global_account_selector import (
            get_global_selected_account,
            require_account_selection
        )
        print("✅ 全局账户选择器导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_replicate_plan_functions():
    """测试复制计划工具的核心函数"""
    print("\n🔧 测试复制计划工具函数...")
    
    try:
        from tools import replicate_plan as replicate_plan_tool
        
        # 检查核心函数是否存在
        required_functions = [
            'find_campaigns_by_criteria',
            'run_replication',
            'preview_name_cleaning'
        ]
        
        for func_name in required_functions:
            if hasattr(replicate_plan_tool, func_name):
                print(f"✅ {func_name} 函数存在")
            else:
                print(f"❌ {func_name} 函数不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试复制计划工具失败: {e}")
        return False

def test_account_selector_functions():
    """测试账户选择器函数"""
    print("\n👥 测试账户选择器函数...")
    
    try:
        sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))
        from ai_tool_20250718_maintenance_unified_account_selector import (
            get_accounts_with_favorites
        )
        
        # 获取账户列表
        accounts = get_accounts_with_favorites()
        print(f"✅ 成功获取 {len(accounts)} 个账户")
        
        if accounts:
            # 显示前3个账户信息
            print("📋 账户示例:")
            for i, account in enumerate(accounts[:3], 1):
                is_favorite = getattr(account, 'is_favorite', False)
                star = "⭐ " if is_favorite else ""
                print(f"  {i}. {star}{account.name} ({account.account_id_qc})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试账户选择器失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            # 查询账户数量
            account_count = db.query(AdAccount).count()
            print(f"✅ 数据库连接成功，共有 {account_count} 个广告账户")
            
            # 查询收藏账户数量
            favorite_count = db.query(AdAccount).filter(AdAccount.is_favorite == True).count()
            print(f"✅ 其中 {favorite_count} 个为收藏账户")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_web_ui_syntax():
    """测试web_ui.py语法"""
    print("\n🌐 测试web_ui.py语法...")
    
    try:
        import py_compile
        web_ui_path = project_root / 'web_ui.py'
        
        # 编译检查语法
        py_compile.compile(str(web_ui_path), doraise=True)
        print("✅ web_ui.py 语法检查通过")
        
        return True
        
    except Exception as e:
        print(f"❌ web_ui.py 语法错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 批量跨账户计划复制功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("复制工具函数测试", test_replicate_plan_functions),
        ("账户选择器测试", test_account_selector_functions),
        ("数据库连接测试", test_database_connection),
        ("Web UI语法测试", test_web_ui_syntax)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！批量跨账户复制功能准备就绪")
        print("\n💡 启动建议:")
        print("   conda activate qc_env")
        print("   streamlit run web_ui.py")
        print("   然后访问投放中心 → 批量跨账户计划复制")
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
