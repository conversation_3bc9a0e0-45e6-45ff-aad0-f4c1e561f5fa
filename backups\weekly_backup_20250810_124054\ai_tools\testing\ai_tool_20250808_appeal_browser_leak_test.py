#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 测试工具
生命周期: 临时（7天）
创建目的: 回归验证提审模块浏览器资源是否在流程后完全释放
清理条件: 验证通过后可删除
"""

import sys
import os
import time
from pathlib import Path

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

import psutil
from typing import Dict

TARGET_ACCOUNT = ****************
TARGET_PLAN = ****************
PRINCIPAL = "缇萃百货"


def count_browser_processes() -> Dict[str, int]:
    counts = {"chromium": 0, "chrome": 0, "playwright": 0}
    for p in psutil.process_iter(['name']):
        try:
            name = (p.info['name'] or '').lower()
            for k in list(counts.keys()):
                if k in name:
                    counts[k] += 1
        except psutil.Error:
            continue
    return counts


def main():
    from qianchuan_aw.utils.config_loader import load_settings
    from qianchuan_aw.services.appeal_browser_service import smart_appeal

    print("🎯 提审浏览器资源释放回归测试")
    app_settings = load_settings()

    before = count_browser_processes()
    print(f"启动前进程: {before}")

    success, result = smart_appeal(PRINCIPAL, TARGET_ACCOUNT, TARGET_PLAN, app_settings)
    print(f"申诉结果: {success}, {result}")

    # 立即检查
    after_immediate = count_browser_processes()
    print(f"流程结束后立即进程: {after_immediate}")

    # 等待 idle 超时最大值（环境可设置 QC_BROWSER_WORKER_IDLE_TIMEOUT，默认180s），此处+5s
    idle = int(os.environ.get('QC_BROWSER_WORKER_IDLE_TIMEOUT', '180'))
    print(f"等待空闲超时关闭: {idle}s ...")
    time.sleep(min(idle + 5, 190))

    after_wait = count_browser_processes()
    print(f"等待后进程: {after_wait}")

    # 简单判定：等待后浏览器相关进程计数应 <= 启动前
    ok = sum(after_wait.values()) <= sum(before.values())
    print("✅ 资源释放成功" if ok else "❌ 资源可能仍在占用")

    return 0 if ok else 1


if __name__ == "__main__":
    sys.exit(main())
