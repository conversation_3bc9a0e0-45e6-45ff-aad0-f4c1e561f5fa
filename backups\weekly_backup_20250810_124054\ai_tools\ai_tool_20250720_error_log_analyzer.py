#!/usr/bin/env python3
"""
千川项目错误日志分析工具
分析2025-07-20 20:00之后的错误日志
"""

import re
import sys
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import json

def analyze_error_logs():
    """分析错误日志"""
    print("🔍 千川项目错误日志精确分析")
    print("=" * 60)

    log_file = "logs/app_2025-07-20.log"

    if not os.path.exists(log_file):
        print(f"❌ 日志文件不存在: {log_file}")
        return

    # 定义时间范围：20:00之后
    target_date = "2025-07-20"
    start_time = datetime.strptime(f"{target_date} 20:00:00", "%Y-%m-%d %H:%M:%S")

    errors = []
    error_patterns = defaultdict(int)
    error_modules = defaultdict(int)
    error_types = defaultdict(int)
    api_errors = defaultdict(int)  # 新增：API错误详细分类
    api_urls = defaultdict(int)    # 新增：API URL统计
    appeal_errors = defaultdict(int)  # 新增：申诉错误详细分类

    print(f"📅 分析时间范围: {target_date} 20:00:00 至当前时间")
    print(f"📂 日志文件: {log_file}")

    try:
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            for line_num, line in enumerate(f, 1):
                # 匹配错误日志格式
                error_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| ERROR\s+\| ([^:]+):([^:]+):(\d+) - (.+)', line.strip())

                if error_match:
                    timestamp_str, module, function, line_no, message = error_match.groups()

                    try:
                        log_time = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")

                        # 只分析20:00之后的错误
                        if log_time >= start_time:
                            error_info = {
                                'timestamp': timestamp_str,
                                'module': module,
                                'function': function,
                                'line': line_no,
                                'message': message,
                                'line_num': line_num
                            }
                            errors.append(error_info)

                            # 统计错误模块
                            error_modules[module] += 1

                            # 精确分析API错误
                            if 'API请求失败' in message and 'URL:' in message:
                                # 提取API URL
                                url_match = re.search(r'URL: ([^,]+)', message)
                                if url_match:
                                    api_url = url_match.group(1)
                                    api_urls[api_url] += 1

                                    # 分析具体API接口
                                    if 'security/score_disposal_info/get' in api_url:
                                        api_errors['违规检测接口限流'] += 1
                                    elif 'Too many requests' in message:
                                        api_errors['其他接口限流'] += 1
                                    else:
                                        api_errors['其他API错误'] += 1

                            # 精确分析申诉错误
                            if 'appeal' in message.lower() or '申诉' in message:
                                if 'Playwright' in message and 'asyncio' in message:
                                    appeal_errors['Playwright异步冲突'] += 1
                                elif "Can't pickle" in message:
                                    appeal_errors['进程序列化失败'] += 1
                                elif '申诉失败' in message or '申诉异常' in message:
                                    appeal_errors['申诉业务逻辑错误'] += 1
                                else:
                                    appeal_errors['其他申诉错误'] += 1

                            # 分类错误类型
                            if 'Playwright' in message and 'asyncio' in message:
                                error_types['Playwright异步冲突'] += 1
                            elif 'Too many requests' in message:
                                error_types['API限流'] += 1
                            elif "Can't pickle" in message:
                                error_types['序列化错误'] += 1
                            elif '申诉' in message or 'appeal' in message.lower():
                                error_types['申诉系统错误'] += 1
                            elif '浏览器' in message or 'browser' in message.lower():
                                error_types['浏览器操作错误'] += 1
                            elif 'API' in message:
                                error_types['API调用错误'] += 1
                            else:
                                error_types['其他错误'] += 1

                            # 提取错误模式
                            if 'Playwright Sync API inside the asyncio loop' in message:
                                error_patterns['Playwright异步冲突'] += 1
                            elif 'Too many requests by this developer' in message:
                                error_patterns['千川API限流'] += 1
                            elif "Can't pickle local object" in message:
                                error_patterns['进程序列化失败'] += 1

                    except ValueError:
                        continue
        
        print(f"\n📊 错误统计概览")
        print(f"  总错误数: {len(errors)}")
        print(f"  时间范围: {len([e for e in errors if e['timestamp']])} 条")

        if not errors:
            print("✅ 在指定时间范围内未发现错误日志")
            return

        # 精确的API错误分析
        print(f"\n🔌 API调用失败精确分析:")
        if api_errors:
            for api_error, count in sorted(api_errors.items(), key=lambda x: x[1], reverse=True):
                print(f"  {api_error}: {count} 次")
        else:
            print("  ✅ 未发现API调用失败")

        print(f"\n🌐 具体API接口调用失败统计:")
        if api_urls:
            for url, count in sorted(api_urls.items(), key=lambda x: x[1], reverse=True):
                print(f"  {url}: {count} 次")
        else:
            print("  ✅ 未发现具体API接口失败")

        # 精确的申诉错误分析
        print(f"\n⚖️ 申诉模块错误精确分析:")
        if appeal_errors:
            for appeal_error, count in sorted(appeal_errors.items(), key=lambda x: x[1], reverse=True):
                print(f"  {appeal_error}: {count} 次")
        else:
            print("  ✅ 未发现申诉模块错误")

        # 按优先级分析错误
        print(f"\n🚨 错误类型分布:")
        for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
            print(f"  {error_type}: {count} 次")

        print(f"\n📍 错误模块分布:")
        for module, count in sorted(error_modules.items(), key=lambda x: x[1], reverse=True):
            print(f"  {module}: {count} 次")

        print(f"\n🔍 主要错误模式:")
        for pattern, count in sorted(error_patterns.items(), key=lambda x: x[1], reverse=True):
            print(f"  {pattern}: {count} 次")

        # 详细分析
        analyze_specific_errors(errors, api_errors, api_urls, appeal_errors)

        # 生成修复建议
        generate_fix_recommendations(error_types, error_patterns, errors, api_errors, appeal_errors)
        
    except Exception as e:
        print(f"❌ 分析日志文件时出错: {e}")

def analyze_specific_errors(errors, api_errors, api_urls, appeal_errors):
    """分析具体错误"""
    print(f"\n🔬 具体错误深度分析:")

    # 按错误类型分组
    playwright_errors = [e for e in errors if 'Playwright' in e['message'] and 'asyncio' in e['message']]
    api_limit_errors = [e for e in errors if 'Too many requests' in e['message']]
    pickle_errors = [e for e in errors if "Can't pickle" in e['message']]

    # 精确分析违规检测接口
    security_api_errors = [e for e in errors if 'security/score_disposal_info/get' in e['message']]

    if security_api_errors:
        print(f"\n  🛡️ 违规检测接口专项分析 ({len(security_api_errors)} 次):")
        print(f"    接口URL: https://api.oceanengine.com/open_api/v3.0/security/score_disposal_info/get/")
        print(f"    错误类型: Too many requests by this developer")
        print(f"    影响模块: {set(e['module'] for e in security_api_errors)}")
        print(f"    影响功能: 违规检测、账户健康度检查")
        print(f"    最新发生: {security_api_errors[-1]['timestamp']}")

        # 分析时间分布
        time_distribution = defaultdict(int)
        for error in security_api_errors:
            hour = error['timestamp'].split(' ')[1].split(':')[0]
            time_distribution[hour] += 1

        print(f"    时间分布:")
        for hour in sorted(time_distribution.keys()):
            print(f"      {hour}:xx - {time_distribution[hour]} 次")

        # 分析调用频率
        total_minutes = (len(security_api_errors) * 3) // 60  # 粗略估算
        if total_minutes > 0:
            avg_per_minute = len(security_api_errors) / total_minutes
            print(f"    平均调用频率: {avg_per_minute:.1f} 次/分钟")

    if playwright_errors:
        print(f"\n  🎭 Playwright异步冲突错误 ({len(playwright_errors)} 次):")
        print(f"    影响模块: {set(e['module'] for e in playwright_errors)}")
        print(f"    影响功能: 申诉系统浏览器操作")
        print(f"    最新发生: {playwright_errors[-1]['timestamp']}")

        # 分析申诉系统完全瘫痪的严重性
        appeal_functions = set()
        for error in playwright_errors:
            if 'copilot_service' in error['module']:
                appeal_functions.add('智能对话申诉')
            if 'appeal_browser_service' in error['module']:
                appeal_functions.add('浏览器自动申诉')

        print(f"    瘫痪功能: {', '.join(appeal_functions)}")
        print(f"    严重程度: 🔴 申诉系统完全无法工作")

    if pickle_errors:
        print(f"\n  📦 序列化错误 ({len(pickle_errors)} 次):")
        print(f"    影响模块: {set(e['module'] for e in pickle_errors)}")
        print(f"    影响功能: 进程间申诉操作")
        print(f"    最新发生: {pickle_errors[-1]['timestamp']}")
        print(f"    严重程度: 🔴 申诉系统备用方案失效")

def generate_fix_recommendations(error_types, error_patterns, errors, api_errors, appeal_errors):
    """生成精确的修复建议"""
    print(f"\n🛠️ 精确修复建议:")

    recommendations = []

    # 精确分析违规检测接口问题
    security_api_count = api_errors.get('违规检测接口限流', 0)
    if security_api_count > 0:
        recommendations.append({
            'priority': 'P1',
            'type': '违规检测接口限流',
            'count': security_api_count,
            'description': '违规检测接口(security/score_disposal_info/get)请求频率过高',
            'impact': '仅影响违规检测和账户健康度检查功能',
            'fix': '针对违规检测接口实施专门的频率控制',
            'code_location': 'qianchuan_aw.sdk_qc.client:_make_api_call:150',
            'related_to_optimization': False,  # 与调度优化无直接关系
            'scope': '单一接口问题，非整体API系统问题'
        })

    # P0 - 严重问题
    playwright_count = error_patterns.get('Playwright异步冲突', 0)
    if playwright_count > 0:
        recommendations.append({
            'priority': 'P0',
            'type': 'Playwright异步冲突',
            'count': playwright_count,
            'description': 'Playwright同步API在异步循环中使用',
            'impact': '申诉系统完全无法工作',
            'fix': '将Playwright同步API替换为异步API，或在独立线程中运行',
            'code_location': 'qianchuan_aw.services.copilot_service:__enter__:107',
            'related_to_optimization': False,
            'scope': '申诉系统架构缺陷'
        })

    pickle_count = error_patterns.get('进程序列化失败', 0)
    if pickle_count > 0:
        recommendations.append({
            'priority': 'P0',
            'type': '进程序列化错误',
            'count': pickle_count,
            'description': '进程间传递本地函数对象失败',
            'impact': '申诉系统进程模式无法工作',
            'fix': '重构申诉函数为可序列化的类方法',
            'code_location': 'qianchuan_aw.services.appeal_browser_service:execute_appeal_in_process:150',
            'related_to_optimization': False,
            'scope': '申诉系统架构缺陷'
        })
    
    # 按优先级排序输出
    for rec in sorted(recommendations, key=lambda x: (x['priority'], -x['count'])):
        print(f"\n  {rec['priority']} - {rec['type']} ({rec['count']} 次)")
        print(f"    问题描述: {rec['description']}")
        print(f"    影响范围: {rec['impact']}")
        print(f"    问题范围: {rec['scope']}")
        print(f"    代码位置: {rec['code_location']}")
        print(f"    修复方案: {rec['fix']}")
        print(f"    与今日优化相关: {'是' if rec['related_to_optimization'] else '否'}")

    # 生成精确的修复步骤
    print(f"\n📋 精确修复步骤:")

    # 申诉系统修复
    if playwright_count > 0 or pickle_count > 0:
        print(f"\n  🔴 P0 - 申诉系统架构修复 (预计4-6小时):")
        if playwright_count > 0:
            print(f"     1. Playwright异步冲突修复:")
            print(f"        a) 检查 qianchuan_aw/services/copilot_service.py:107")
            print(f"        b) 将同步Playwright调用改为异步API")
            print(f"        c) 或在独立线程中运行同步操作")

        if pickle_count > 0:
            print(f"     2. 进程序列化问题修复:")
            print(f"        a) 检查 qianchuan_aw/services/appeal_browser_service.py:150")
            print(f"        b) 重构本地函数为类的静态方法")
            print(f"        c) 或改用线程池替代进程池")

        print(f"     3. 申诉系统功能验证:")
        print(f"        a) 测试智能对话申诉功能")
        print(f"        b) 测试浏览器自动申诉功能")
        print(f"        c) 验证申诉成功率恢复")

    # 违规检测接口修复
    if security_api_count > 0:
        print(f"\n  🟡 P1 - 违规检测接口限流修复 (预计2-3小时):")
        print(f"     1. 针对性频率控制:")
        print(f"        a) 检查 qianchuan_aw/sdk_qc/client.py:150")
        print(f"        b) 为security/score_disposal_info/get接口单独设置频率限制")
        print(f"        c) 建议该接口每分钟最多调用30次")
        print(f"     2. 重试机制优化:")
        print(f"        a) 增加指数退避重试策略")
        print(f"        b) 限流时等待更长时间再重试")
        print(f"     3. 监控验证:")
        print(f"        a) 监控违规检测接口调用成功率")
        print(f"        b) 确认限流错误显著减少")

    # 澄清与优化的关系
    print(f"\n🔍 与今日工作流优化的关系澄清:")
    print(f"  📊 调度频率优化 (120秒→60秒):")
    print(f"    - 主要影响: 计划创建相关API调用")
    print(f"    - 违规检测接口: 独立调度，与计划创建无直接关系")
    print(f"    - 结论: 违规检测限流与今日优化无直接因果关系")

    print(f"\n  🎯 申诉系统问题:")
    print(f"    - 性质: 架构设计缺陷，非优化引入")
    print(f"    - 影响: 申诉功能完全瘫痪")
    print(f"    - 优先级: 最高，需立即修复")

    print(f"\n  📈 问题严重性重新评估:")
    print(f"    - 违规检测限流: 🟡 中等影响，单一功能受限")
    print(f"    - 申诉系统瘫痪: 🔴 严重影响，核心功能失效")
    print(f"    - 整体系统: 🟡 部分功能受影响，非全面故障")

if __name__ == "__main__":
    analyze_error_logs()
