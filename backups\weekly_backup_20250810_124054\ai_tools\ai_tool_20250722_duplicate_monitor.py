#!/usr/bin/env python3
"""
千川重复计划监控告警脚本
实时监控重复计划创建，立即告警
"""

import sys
import os
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def check_duplicate_campaigns():
    """检查重复计划创建"""
    logger.info("🔍 检查重复计划创建")
    
    try:
        with database_session() as db:
            # 检查最近1小时内的重复创建
            duplicate_query = text("""
                WITH recent_campaigns AS (
                    SELECT 
                        lc.filename,
                        lc.file_hash,
                        COUNT(c.id) as campaign_count,
                        STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE c.created_at > NOW() - INTERVAL '1 hour'
                    GROUP BY lc.filename, lc.file_hash
                    HAVING COUNT(c.id) > 1
                )
                SELECT * FROM recent_campaigns
            """)
            
            duplicates = db.execute(duplicate_query).fetchall()
            
            if duplicates:
                logger.critical(f"🚨 发现最近1小时内的重复计划创建!")
                for dup in duplicates:
                    logger.critical(f"  {dup.filename}: {dup.campaign_count}个计划 - {dup.campaign_ids}")
                return True
            else:
                logger.info("✅ 最近1小时内无重复计划创建")
                return False
                
    except Exception as e:
        logger.error(f"❌ 检查重复计划失败: {e}")
        return False

if __name__ == "__main__":
    if check_duplicate_campaigns():
        logger.critical("🚨 检测到重复计划创建，请立即处理！")
        exit(1)
    else:
        logger.info("✅ 监控正常")
        exit(0)
