#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 验证批量跨账户复制功能修复
依赖关系: web_ui.py
清理条件: 修复验证完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_expander_nesting():
    """检查web_ui.py中是否还有嵌套expander的问题"""
    print("🔍 检查嵌套expander问题...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        expander_depth = 0
        nested_expanders = []
        
        for i, line in enumerate(lines, 1):
            # 检查expander开始
            if 'st.expander(' in line or 'with st.expander(' in line:
                if expander_depth > 0:
                    nested_expanders.append((i, line.strip()))
                expander_depth += 1
            
            # 检查代码块结束（简单检测）
            if line.strip().startswith('with ') and 'st.expander(' in line:
                continue
            elif line.strip() == '' or (not line.startswith('    ') and expander_depth > 0):
                # 这是一个简化的检测，实际情况可能更复杂
                pass
        
        if nested_expanders:
            print("❌ 发现嵌套expander问题:")
            for line_num, line_content in nested_expanders:
                print(f"   第{line_num}行: {line_content}")
            return False
        else:
            print("✅ 未发现嵌套expander问题")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_syntax():
    """检查语法"""
    print("\n🔧 检查语法...")
    
    try:
        import py_compile
        web_ui_path = project_root / 'web_ui.py'
        py_compile.compile(str(web_ui_path), doraise=True)
        print("✅ 语法检查通过")
        return True
    except Exception as e:
        print(f"❌ 语法错误: {e}")
        return False

def check_function_structure():
    """检查批量跨账户复制函数结构"""
    print("\n📋 检查函数结构...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'render_batch_cross_account_replication',
            'execute_batch_replication_task',
            'execute_single_replication_task'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if f'def {func_name}(' not in content:
                missing_functions.append(func_name)
        
        if missing_functions:
            print("❌ 缺少函数:")
            for func in missing_functions:
                print(f"   - {func}")
            return False
        else:
            print("✅ 所有必需函数都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_session_state_variables():
    """检查session state变量使用"""
    print("\n🔄 检查session state变量...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查批量跨账户复制的session state变量
        batch_variables = [
            'batch_replication_step',
            'batch_found_campaigns',
            'batch_selected_campaigns',
            'batch_source_accounts',
            'batch_task_running',
            'batch_task_paused',
            'batch_progress'
        ]
        
        # 检查单账户复制的session state变量
        single_variables = [
            'single_replication_step',
            'single_found_campaigns',
            'single_selected_campaigns',
            'single_task_running',
            'single_task_paused',
            'single_progress'
        ]
        
        missing_batch = []
        missing_single = []
        
        for var in batch_variables:
            if var not in content:
                missing_batch.append(var)
        
        for var in single_variables:
            if var not in content:
                missing_single.append(var)
        
        if missing_batch or missing_single:
            print("❌ 缺少session state变量:")
            if missing_batch:
                print("   批量跨账户复制:")
                for var in missing_batch:
                    print(f"     - {var}")
            if missing_single:
                print("   单账户复制:")
                for var in missing_single:
                    print(f"     - {var}")
            return False
        else:
            print("✅ 所有session state变量都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔧 批量跨账户复制功能修复验证")
    print("=" * 50)
    
    checks = [
        ("嵌套expander检查", check_expander_nesting),
        ("语法检查", check_syntax),
        ("函数结构检查", check_function_structure),
        ("Session State变量检查", check_session_state_variables)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}")
        print("-" * 30)
        
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 修复验证通过！功能可以正常使用")
        print("\n💡 测试建议:")
        print("   1. 重新启动Streamlit: streamlit run web_ui.py")
        print("   2. 访问投放中心 → 批量跨账户计划复制")
        print("   3. 测试完整的四步流程")
    else:
        print("⚠️ 部分验证失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
