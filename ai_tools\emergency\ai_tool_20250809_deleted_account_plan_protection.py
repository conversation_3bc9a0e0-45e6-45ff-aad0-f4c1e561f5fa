#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 永久保留
创建目的: 处理deleted账户的活跃计划，严禁任何操作
清理条件: 成为账户保护工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class DeletedAccountPlanProtector:
    """deleted账户计划保护器"""
    
    def run_deleted_account_protection(self):
        """运行deleted账户保护"""
        logger.info("🚫 deleted账户计划保护")
        logger.info("="*100)
        logger.info("🎯 业务规则：已删除账户严禁任何操作")
        
        # 1. 检查deleted账户的活跃计划
        deleted_account_plans = self._check_deleted_account_plans()
        
        # 2. 安全处理deleted账户的计划
        protection_success = self._protect_deleted_account_plans(deleted_account_plans)
        
        # 3. 验证保护效果
        verification_success = self._verify_protection_results()
        
        # 4. 生成保护报告
        self._generate_protection_report({
            'deleted_account_plans': deleted_account_plans,
            'protection_success': protection_success,
            'verification_success': verification_success
        })
    
    def _check_deleted_account_plans(self):
        """检查deleted账户的活跃计划"""
        logger.info("🔍 检查deleted账户的活跃计划...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            
            with SessionLocal() as db:
                # 查找deleted账户的活跃计划
                deleted_account_plans = db.query(Campaign, AdAccount.name).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.status == 'deleted',
                    Campaign.status.in_(['MONITORING', 'AUDITING', 'CREATED'])
                ).all()
                
                logger.info(f"📊 发现 {len(deleted_account_plans)} 个deleted账户的活跃计划")
                
                plan_details = []
                for campaign, account_name in deleted_account_plans:
                    plan_detail = {
                        'campaign_id': campaign.id,
                        'campaign_id_qc': campaign.campaign_id_qc,
                        'account_name': account_name,
                        'status': campaign.status,
                        'appeal_status': campaign.appeal_status,
                        'created_at': campaign.created_at
                    }
                    plan_details.append(plan_detail)
                    
                    logger.warning(f"⚠️ deleted账户活跃计划: {campaign.campaign_id_qc}")
                    logger.warning(f"   账户: {account_name}")
                    logger.warning(f"   状态: {campaign.status} / {campaign.appeal_status}")
                    logger.warning(f"   创建时间: {campaign.created_at}")
                
                return plan_details
                
        except Exception as e:
            logger.error(f"❌ 检查deleted账户活跃计划失败: {e}")
            return []
    
    def _protect_deleted_account_plans(self, deleted_account_plans):
        """保护deleted账户的计划"""
        logger.info("🛡️ 保护deleted账户的计划...")
        
        if not deleted_account_plans:
            logger.success("✅ 没有deleted账户的活跃计划需要保护")
            return True
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign
            
            with SessionLocal() as db:
                protection_count = 0
                
                for plan_detail in deleted_account_plans:
                    campaign_id = plan_detail['campaign_id']
                    campaign_id_qc = plan_detail['campaign_id_qc']
                    account_name = plan_detail['account_name']
                    
                    # 🚫 [业务规则] 将deleted账户的计划标记为CANCELLED，停止所有操作
                    campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
                    if campaign:
                        old_status = campaign.status
                        campaign.status = 'CANCELLED'
                        campaign.updated_at = db.func.now()
                        
                        protection_count += 1
                        
                        logger.warning(f"🚫 [deleted账户保护] 计划 {campaign_id_qc} 已标记为CANCELLED")
                        logger.warning(f"   账户: {account_name} (deleted)")
                        logger.warning(f"   状态变更: {old_status} → CANCELLED")
                        logger.warning(f"   原因: 严禁对deleted账户进行任何操作")
                
                # 提交更改
                db.commit()
                
                logger.success(f"✅ 成功保护 {protection_count} 个deleted账户的计划")
                
                return protection_count > 0
                
        except Exception as e:
            logger.error(f"❌ 保护deleted账户计划失败: {e}")
            return False
    
    def _verify_protection_results(self):
        """验证保护结果"""
        logger.info("🔍 验证deleted账户保护结果...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            
            with SessionLocal() as db:
                # 检查是否还有deleted账户的活跃计划
                remaining_active_plans = db.query(Campaign).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.status == 'deleted',
                    Campaign.status.in_(['MONITORING', 'AUDITING', 'CREATED'])
                ).count()
                
                if remaining_active_plans == 0:
                    logger.success("✅ 验证通过：没有deleted账户的活跃计划")
                    return True
                else:
                    logger.error(f"❌ 验证失败：仍有 {remaining_active_plans} 个deleted账户的活跃计划")
                    return False
                
        except Exception as e:
            logger.error(f"❌ 验证保护结果失败: {e}")
            return False
    
    def _generate_protection_report(self, results):
        """生成保护报告"""
        logger.info("\n📋 deleted账户计划保护报告")
        logger.info("="*100)
        
        deleted_plans = results['deleted_account_plans']
        protection_success = results['protection_success']
        verification_success = results['verification_success']
        
        # 问题分析
        logger.info(f"📊 deleted账户活跃计划: 发现 {len(deleted_plans)} 个")
        
        for plan in deleted_plans:
            logger.info(f"   🚫 {plan['campaign_id_qc']} (账户: {plan['account_name']})")
        
        # 保护结果
        if protection_success:
            logger.success("✅ deleted账户保护: 执行成功")
        else:
            logger.error("❌ deleted账户保护: 执行失败")
        
        # 验证结果
        if verification_success:
            logger.success("✅ 验证结果: 所有deleted账户的计划已停止")
        else:
            logger.error("❌ 验证结果: 仍有deleted账户的活跃计划")
        
        # 业务规则确认
        logger.info(f"\n📋 业务规则确认:")
        logger.info(f"   🚫 deleted账户: 严禁任何操作（上传、创建、提审、收割）")
        logger.info(f"   ⚠️ temporarily_blocked账户: 禁止新建操作，允许已有计划的提审和收割")
        logger.info(f"   ✅ active账户: 允许所有操作")
        
        # 总体评估
        if protection_success and verification_success:
            logger.success("🎊 deleted账户保护成功")
            logger.success("💡 业务规则现在得到严格执行")
            return True
        else:
            logger.error("❌ deleted账户保护失败")
            logger.error("🔧 需要手动检查和修复")
            return False


def main():
    """主函数"""
    protector = DeletedAccountPlanProtector()
    
    logger.info("🚀 启动deleted账户计划保护")
    logger.info("🎯 目标：严禁对deleted账户进行任何操作")
    
    success = protector.run_deleted_account_protection()
    
    if success:
        logger.success("🎊 deleted账户保护完成")
        logger.success("💡 建议：重启Celery服务使保护生效")
        return 0
    else:
        logger.error("❌ deleted账户保护失败")
        logger.error("🔧 建议：检查错误并手动修复")
        return 1


if __name__ == "__main__":
    exit(main())
