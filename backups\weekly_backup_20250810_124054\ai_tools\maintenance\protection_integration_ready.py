
# 千川自动化素材唯一性保护集成代码
# ===============================
# 将此代码集成到计划创建逻辑中

from ai_tools.maintenance.ai_tool_20250727_code_level_protection import (
    material_uniqueness_required,
    EnhancedPlanCreationGuard,
    MaterialUniquenessViolationError,
    material_guard
)

# 1. 在 src/qianchuan_aw/workflows/common/plan_creation.py 中修改 create_ad_plan 函数

@material_uniqueness_required("广告计划创建")
def create_ad_plan(db, principal, account, platform_creatives, **kwargs):
    """增强的广告计划创建函数 - 带素材唯一性检查"""
    
    try:
        # 预检查所有素材的唯一性
        validation_result = EnhancedPlanCreationGuard.validate_materials_before_plan_creation(platform_creatives)
        
        if not validation_result['is_valid']:
            # 记录违规尝试
            logger.error("🚨 计划创建被阻止，素材唯一性验证失败")
            for violation in validation_result['violations']:
                logger.error(f"   违规素材: {violation}")
            
            raise MaterialUniquenessViolationError(
                f"素材唯一性验证失败，发现 {len(validation_result['violations'])} 个违规素材"
            )
        
        logger.info(f"✅ 素材唯一性验证通过，允许创建计划 ({len(validation_result['valid_materials'])} 个素材)")
        
        # 原有的计划创建逻辑保持不变...
        # [在这里插入原有的 create_ad_plan 函数体]
        
    except MaterialUniquenessViolationError:
        # 重新抛出唯一性违规异常
        raise
    except Exception as e:
        logger.error(f"计划创建过程中发生异常: {e}")
        raise

# 2. 在 src/qianchuan_aw/workflows/scheduler.py 中添加保护

def enhanced_plan_creation_with_protection(db, platform_creatives, **kwargs):
    """带保护的计划创建调用"""
    
    # 执行素材唯一性检查
    for pc in platform_creatives:
        local_creative = db.query(LocalCreative).filter(LocalCreative.id == pc.local_creative_id).first()
        if local_creative:
            # 强制执行唯一性检查
            material_guard.enforce_uniqueness(
                local_creative.file_hash, 
                local_creative.filename, 
                "调度器计划创建"
            )
    
    # 调用原有的计划创建逻辑
    return create_ad_plan(db, platform_creatives, **kwargs)

# 使用说明：
# 1. 将上述代码集成到相应的文件中
# 2. 确保所有计划创建路径都经过保护检查
# 3. 测试保护机制是否正常工作
# 4. 监控保护日志确保违规被正确阻止
