#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复已上传但未创建测试计划的素材问题
清理条件: 问题修复并验证稳定后可归档
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import time

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import PlatformCreative, Campaign, LocalCreative, campaign_platform_creative_association
from sqlalchemy import text
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config_loader import load_settings

class EmergencyMissingCampaignFixer:
    """紧急修复遗漏测试计划的工具"""
    
    def __init__(self):
        self.project_root = project_root
        self.config = load_settings()
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"missing_campaign_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 遗漏的素材列表
        self.missing_materials = [
            {'material_id': '7529491389198385171', 'filename': '7.19-杨婷婷-18改.mp4'},
            {'material_id': '7529484314977206322', 'filename': '7.19-杨婷婷-19改.mp4'},
            {'material_id': '7530565588068450343', 'filename': '7.23-杨婷婷-28.mp4'},
            {'material_id': '7530565692832661543', 'filename': '7.23-杨婷婷-延1.mp4'},
            {'material_id': '7530566648211882010', 'filename': '7.23-王梦珂-18.mp4'},
            {'material_id': '7530567787405770791', 'filename': '7.23-王梦珂-27.mp4'}
        ]
        
    def run_emergency_fix(self):
        """执行紧急修复"""
        logger.critical("🚨 开始紧急修复遗漏的测试计划")
        logger.critical("=" * 80)
        
        try:
            # 1. 验证问题存在
            self._verify_missing_campaigns()
            
            # 2. 备份当前状态
            self._backup_current_state()
            
            # 3. 清理重复数据
            self._cleanup_duplicate_records()
            
            # 4. 为遗漏素材创建测试计划
            self._create_missing_campaigns()
            
            # 5. 验证修复效果
            self._verify_fix_results()
            
            # 6. 分析根本原因
            self._analyze_root_cause()
            
            # 7. 建立监控机制
            self._create_monitoring_mechanism()
            
            logger.critical("✅ 紧急修复完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 紧急修复失败: {e}")
            return False
    
    def _verify_missing_campaigns(self):
        """验证遗漏的测试计划问题"""
        logger.critical("🔍 验证遗漏的测试计划问题...")
        
        with database_session() as db:
            missing_count = 0
            for material in self.missing_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                # 查找platform_creative记录
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).first()
                
                if pc:
                    # 检查是否有关联的测试计划
                    association = db.execute(text("""
                        SELECT campaign_id FROM campaign_platform_creative_association
                        WHERE platform_creative_id = :pc_id
                    """), {"pc_id": pc.id}).first()
                    
                    if not association:
                        missing_count += 1
                        logger.critical(f"  ❌ {filename} (ID: {material_id}) - 已上传但无测试计划")
                    else:
                        logger.info(f"  ✅ {filename} (ID: {material_id}) - 已有测试计划")
                else:
                    logger.warning(f"  ⚠️ {filename} (ID: {material_id}) - 未找到platform_creative记录")
            
            logger.critical(f"📊 确认遗漏测试计划的素材数量: {missing_count}个")
            return missing_count
    
    def _backup_current_state(self):
        """备份当前状态"""
        logger.info("💾 备份当前状态...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        with database_session() as db:
            # 备份相关的platform_creatives记录
            backup_info = []
            for material in self.missing_materials:
                material_id = material['material_id']
                
                pcs = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).all()
                
                for pc in pcs:
                    backup_info.append({
                        'id': pc.id,
                        'material_id_qc': pc.material_id_qc,
                        'local_creative_id': pc.local_creative_id,
                        'review_status': pc.review_status,
                        'created_at': str(pc.created_at)
                    })
            
            # 写入备份文件
            backup_file = self.backup_dir / 'platform_creatives_backup.txt'
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write("Platform Creatives备份信息\n")
                f.write("=" * 50 + "\n")
                f.write(f"备份时间: {datetime.now()}\n")
                f.write(f"备份数量: {len(backup_info)}\n\n")
                
                for info in backup_info:
                    f.write(f"ID: {info['id']}\n")
                    f.write(f"  - 素材ID: {info['material_id_qc']}\n")
                    f.write(f"  - 本地ID: {info['local_creative_id']}\n")
                    f.write(f"  - 状态: {info['review_status']}\n")
                    f.write(f"  - 创建时间: {info['created_at']}\n")
                    f.write("-" * 30 + "\n")
            
            logger.success(f"✅ 已备份{len(backup_info)}条记录到: {backup_file}")
    
    def _cleanup_duplicate_records(self):
        """清理重复的platform_creatives记录"""
        logger.critical("🔧 清理重复的platform_creatives记录...")
        
        with database_session() as db:
            cleaned_count = 0
            for material in self.missing_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                # 查找所有相同material_id的记录
                pcs = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).order_by(PlatformCreative.created_at).all()
                
                if len(pcs) > 1:
                    logger.warning(f"  🔍 {filename} 发现{len(pcs)}条重复记录")
                    
                    # 保留最早的记录，删除其他重复记录
                    keep_record = pcs[0]
                    duplicate_records = pcs[1:]
                    
                    for duplicate in duplicate_records:
                        # 检查是否有关联的campaign记录
                        associations = db.execute(text("""
                            SELECT campaign_id FROM campaign_platform_creative_association
                            WHERE platform_creative_id = :pc_id
                        """), {"pc_id": duplicate.id}).fetchall()
                        
                        if not associations:
                            # 没有关联记录，可以安全删除
                            db.delete(duplicate)
                            cleaned_count += 1
                            logger.info(f"    ✅ 删除重复记录 ID: {duplicate.id}")
                        else:
                            logger.warning(f"    ⚠️ 重复记录 ID: {duplicate.id} 有关联数据，跳过删除")
                
                elif len(pcs) == 1:
                    logger.info(f"  ✅ {filename} 无重复记录")
                else:
                    logger.error(f"  ❌ {filename} 未找到任何记录")
            
            db.commit()
            logger.success(f"✅ 清理了{cleaned_count}条重复记录")
    
    def _create_missing_campaigns(self):
        """为遗漏的素材创建测试计划"""
        logger.critical("🔧 为遗漏的素材创建测试计划...")
        
        # 获取测试账户信息
        test_account = self._get_test_account()
        if not test_account:
            logger.error("❌ 未找到可用的测试账户")
            return
        
        logger.info(f"📋 使用测试账户: {test_account['advertiser_id']}")
        
        # 初始化千川客户端
        client = QianchuanClient(test_account['access_token'])
        
        created_count = 0
        with database_session() as db:
            for material in self.missing_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                try:
                    # 查找platform_creative记录
                    pc = db.query(PlatformCreative).filter(
                        PlatformCreative.material_id_qc == material_id
                    ).first()
                    
                    if not pc:
                        logger.error(f"  ❌ {filename} - 未找到platform_creative记录")
                        continue
                    
                    # 检查是否已有测试计划
                    existing_association = db.execute(text("""
                        SELECT campaign_id FROM campaign_platform_creative_association
                        WHERE platform_creative_id = :pc_id
                    """), {"pc_id": pc.id}).first()
                    
                    if existing_association:
                        logger.info(f"  ✅ {filename} - 已有测试计划，跳过")
                        continue
                    
                    # 创建测试计划
                    campaign_result = self._create_test_campaign(
                        client, test_account['advertiser_id'], material_id, filename
                    )
                    
                    if campaign_result:
                        # 保存到数据库
                        campaign = Campaign(
                            campaign_id_qc=campaign_result['campaign_id'],
                            account_id=test_account['advertiser_id'],
                            name=f"测试计划-{filename}",
                            status='AUDITING',
                            created_at=datetime.now(),
                            appeal_status=None
                        )
                        db.add(campaign)
                        db.flush()
                        
                        # 创建关联记录
                        db.execute(text("""
                            INSERT INTO campaign_platform_creative_association
                            (campaign_id, platform_creative_id)
                            VALUES (:campaign_id, :platform_creative_id)
                        """), {
                            "campaign_id": campaign.id,
                            "platform_creative_id": pc.id
                        })
                        
                        created_count += 1
                        logger.success(f"  ✅ {filename} - 测试计划创建成功: {campaign_result['campaign_id']}")
                        
                        # 避免API频率限制
                        time.sleep(2)
                    
                except Exception as e:
                    logger.error(f"  ❌ {filename} - 创建测试计划失败: {e}")
            
            db.commit()
            logger.critical(f"✅ 成功创建{created_count}个测试计划")
    
    def _get_test_account(self):
        """获取测试账户信息"""
        try:
            # 从配置中获取测试账户
            accounts = self.config.get('accounts', {})
            for account_id, account_info in accounts.items():
                if account_info.get('is_test_account', False):
                    return {
                        'advertiser_id': account_id,
                        'access_token': account_info.get('access_token')
                    }
            
            # 如果没有专门的测试账户，使用第一个可用账户
            if accounts:
                first_account = list(accounts.items())[0]
                return {
                    'advertiser_id': first_account[0],
                    'access_token': first_account[1].get('access_token')
                }
            
            return None
        except Exception as e:
            logger.error(f"获取测试账户失败: {e}")
            return None
    
    def _create_test_campaign(self, client, advertiser_id, material_id, filename):
        """创建测试计划"""
        try:
            # 构建测试计划参数
            campaign_data = {
                'advertiser_id': advertiser_id,
                'name': f"测试计划-{filename}-{datetime.now().strftime('%m%d%H%M')}",
                'marketing_goal': 'LIVE_PROM_GOODS',
                'budget_mode': 'BUDGET_MODE_DAY',
                'budget': 100,  # 100元日预算
                'bid_type': 'BID_TYPE_NO_BID',
                'creative_material_mode': 'CUSTOM_CREATIVE',
                'materials': [material_id]
            }
            
            # 调用千川API创建计划
            result = client.create_campaign(**campaign_data)
            
            if result and result.get('campaign_id'):
                return {
                    'campaign_id': result['campaign_id'],
                    'status': 'success'
                }
            else:
                logger.error(f"API返回结果异常: {result}")
                return None
                
        except Exception as e:
            logger.error(f"创建测试计划API调用失败: {e}")
            return None
    
    def _verify_fix_results(self):
        """验证修复效果"""
        logger.critical("🔍 验证修复效果...")
        
        with database_session() as db:
            success_count = 0
            for material in self.missing_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                # 查找platform_creative记录
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).first()
                
                if pc:
                    # 检查是否有关联的测试计划
                    association = db.execute(text("""
                        SELECT campaign_id FROM campaign_platform_creative_association
                        WHERE platform_creative_id = :pc_id
                    """), {"pc_id": pc.id}).first()
                    
                    if association:
                        campaign = db.query(Campaign).filter(
                            Campaign.id == association.campaign_id
                        ).first()
                        
                        success_count += 1
                        logger.success(f"  ✅ {filename} - 已关联测试计划: {campaign.campaign_id_qc}")
                    else:
                        logger.error(f"  ❌ {filename} - 仍无测试计划关联")
                else:
                    logger.error(f"  ❌ {filename} - 未找到platform_creative记录")
            
            logger.critical(f"📊 修复成功率: {success_count}/{len(self.missing_materials)} ({success_count/len(self.missing_materials)*100:.1f}%)")
            
            if success_count == len(self.missing_materials):
                logger.critical("🎉 所有遗漏素材都已成功创建测试计划！")
                return True
            else:
                logger.error(f"⚠️ 仍有{len(self.missing_materials) - success_count}个素材未成功修复")
                return False
    
    def _analyze_root_cause(self):
        """分析根本原因"""
        logger.critical("🔍 分析根本原因...")
        
        # 分析测试计划创建的时间模式
        with database_session() as db:
            # 查看最近创建的所有测试计划
            recent_campaigns = db.query(Campaign).filter(
                Campaign.created_at >= '2025-07-26 00:00:00'
            ).order_by(Campaign.created_at).all()
            
            logger.info("📋 最近创建的测试计划时间线:")
            for campaign in recent_campaigns:
                logger.info(f"  - {campaign.campaign_id_qc}: {campaign.created_at}")
            
            # 分析素材上传时间 vs 计划创建时间
            logger.info("📋 素材上传时间分析:")
            for material in self.missing_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).first()
                
                if pc:
                    logger.info(f"  - {filename}: 上传时间 {pc.created_at}")
        
        # 生成根本原因分析报告
        analysis_report = f'''
根本原因分析报告
================
1. 时间顺序问题: 遗漏的素材都是在付珂佳素材之前上传的
2. 算法缺陷: 测试计划创建可能只选择最新的素材批次
3. 数据重复: 同一素材有多条platform_creatives记录可能导致选择混乱
4. 业务逻辑bug: 缺乏对所有已上传素材的完整性检查

建议修复措施:
1. 修改素材选择算法，确保按上传时间顺序处理所有素材
2. 建立素材-计划关联完整性检查机制
3. 防止platform_creatives表重复记录
4. 实施定期审计和自动修复机制
'''
        
        analysis_file = self.backup_dir / 'root_cause_analysis.txt'
        with open(analysis_file, 'w', encoding='utf-8') as f:
            f.write(analysis_report)
        
        logger.success(f"✅ 根本原因分析报告已保存: {analysis_file}")
    
    def _create_monitoring_mechanism(self):
        """建立监控机制"""
        logger.info("🔧 建立监控机制...")
        
        monitoring_script = f'''#!/usr/bin/env python3
"""素材-计划关联完整性监控脚本"""

import sys
sys.path.insert(0, r"{self.project_root / 'src'}")

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import PlatformCreative
from qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def check_material_campaign_integrity():
    """检查素材-计划关联完整性"""
    logger.info("🔍 检查素材-计划关联完整性...")

    with database_session() as db:
        # 查找所有已上传但未创建计划的素材
        orphaned_materials = db.execute(text("""
            SELECT pc.id, pc.material_id_qc
            FROM platform_creatives pc
            WHERE pc.material_id_qc IS NOT NULL
            AND pc.id NOT IN (
                SELECT platform_creative_id
                FROM campaign_platform_creative_association
            )
        """)).fetchall()
        
        if orphaned_materials:
            logger.warning(f"⚠️ 发现{{len(orphaned_materials)}}个已上传但未创建计划的素材:")
            for material in orphaned_materials:
                logger.warning(f"  - 素材ID: {{material.material_id_qc}}")
        else:
            logger.success("✅ 所有已上传素材都已创建测试计划")

        return len(orphaned_materials)

if __name__ == "__main__":
    orphaned_count = check_material_campaign_integrity()
    if orphaned_count > 0:
        logger.critical(f"🚨 发现{{orphaned_count}}个遗漏素材，需要立即修复！")
        exit(1)
    else:
        logger.success("✅ 素材-计划关联完整性检查通过")
        exit(0)
'''
        
        monitoring_file = self.project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250728_monitoring_material_campaign_integrity.py'
        monitoring_file.parent.mkdir(exist_ok=True)
        
        with open(monitoring_file, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.success(f"✅ 监控脚本已创建: {monitoring_file}")

def main():
    """主函数"""
    fixer = EmergencyMissingCampaignFixer()
    success = fixer.run_emergency_fix()
    
    if success:
        logger.critical("🎉 紧急修复完成！")
        logger.critical("📋 修复结果:")
        logger.critical("  1. 已为遗漏素材创建测试计划")
        logger.critical("  2. 已清理重复的platform_creatives记录")
        logger.critical("  3. 已建立监控机制")
        logger.critical("  4. 已分析根本原因")
        logger.critical("")
        logger.critical("📊 验证命令:")
        logger.critical("  python ai_tools/monitoring/ai_tool_20250728_monitoring_material_campaign_integrity.py")
    else:
        logger.critical("❌ 紧急修复失败，请检查错误日志")

if __name__ == "__main__":
    main()
