#!/usr/bin/env python3
"""
千川视频隔离机制深度分析工具
诊断误隔离问题并分析隔离模式
"""

import sys
import os
import re
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
import cv2
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

def analyze_quarantined_videos():
    """分析被隔离的视频文件"""
    logger.info("🔍 分析隔离目录中的视频文件")
    logger.info("=" * 60)
    
    quarantine_dir = Path("quarantine/invalid_videos")
    if not quarantine_dir.exists():
        logger.error("隔离目录不存在")
        return
    
    # 收集隔离信息
    quarantine_data = []
    video_files = list(quarantine_dir.glob("*.mp4"))
    
    logger.info(f"发现 {len(video_files)} 个隔离视频文件")
    
    for video_file in video_files:
        reason_file = video_file.with_suffix(video_file.suffix + ".reason.txt")
        
        video_info = {
            'filename': video_file.name,
            'size_mb': video_file.stat().st_size / (1024 * 1024),
            'quarantine_time': datetime.fromtimestamp(video_file.stat().st_mtime),
            'reason': 'Unknown',
            'original_path': 'Unknown',
            'actual_duration': None,
            'opencv_duration': None,
            'ffprobe_duration': None,
            'author': extract_author_from_filename(video_file.name),
            'is_misquarantined': False
        }
        
        # 读取隔离原因
        if reason_file.exists():
            try:
                with open(reason_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if '隔离原因:' in content:
                        video_info['reason'] = content.split('隔离原因:')[1].split('\n')[0].strip()
                    if '原路径:' in content:
                        video_info['original_path'] = content.split('原路径:')[1].split('\n')[0].strip()
            except Exception as e:
                logger.warning(f"读取隔离原因失败: {e}")
        
        # 使用多种方法检测视频时长
        video_info.update(detect_video_duration_multiple_methods(str(video_file)))
        
        # 判断是否误隔离
        video_info['is_misquarantined'] = is_video_misquarantined(video_info)
        
        quarantine_data.append(video_info)
    
    # 分析隔离模式
    analyze_quarantine_patterns(quarantine_data)
    
    # 生成修复建议
    generate_quarantine_fix_recommendations(quarantine_data)
    
    return quarantine_data

def extract_author_from_filename(filename):
    """从文件名提取作者信息"""
    patterns = [
        r'^\d+\.\d+-([^-]+)-',  # 格式: 7.19-作者名-
        r'-([^-]+)-\d+\.mp4$',  # 格式: -作者名-数字.mp4
        r'(\w+)-\d+\.mp4$',     # 格式: 作者名-数字.mp4
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
    
    return 'Unknown'

def detect_video_duration_multiple_methods(video_path):
    """使用多种方法检测视频时长"""
    results = {
        'opencv_duration': None,
        'ffprobe_duration': None,
        'actual_duration': None
    }
    
    # 方法1: OpenCV
    try:
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            if fps > 0 and frame_count > 0:
                results['opencv_duration'] = frame_count / fps
        cap.release()
    except Exception as e:
        logger.debug(f"OpenCV检测失败: {e}")
    
    # 方法2: FFprobe (如果可用)
    try:
        import subprocess
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', video_path
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')
        if result.returncode == 0:
            data = json.loads(result.stdout)
            if 'format' in data and 'duration' in data['format']:
                results['ffprobe_duration'] = float(data['format']['duration'])
    except Exception as e:
        logger.debug(f"FFprobe检测失败: {e}")
    
    # 选择最可靠的结果
    if results['ffprobe_duration'] is not None:
        results['actual_duration'] = results['ffprobe_duration']
    elif results['opencv_duration'] is not None:
        results['actual_duration'] = results['opencv_duration']
    
    return results

def is_video_misquarantined(video_info):
    """判断视频是否被误隔离"""
    # 如果能检测到有效时长且大于4秒，可能是误隔离
    if video_info['actual_duration'] is not None:
        if video_info['actual_duration'] >= 4.0:
            return True
    
    # 如果隔离原因是"无法获取视频时长"但实际能检测到时长
    if '无法获取视频时长' in video_info['reason']:
        if video_info['actual_duration'] is not None:
            return True
    
    # 如果文件大小正常（>1MB）但被隔离
    if video_info['size_mb'] > 1.0 and '视频过短' not in video_info['reason']:
        return True
    
    return False

def analyze_quarantine_patterns(quarantine_data):
    """分析隔离模式"""
    logger.info(f"\n📊 隔离模式分析:")
    
    # 按隔离原因统计
    reason_stats = Counter(item['reason'] for item in quarantine_data)
    logger.info(f"  隔离原因分布:")
    for reason, count in reason_stats.most_common():
        logger.info(f"    {reason}: {count}次")
    
    # 按作者统计
    author_stats = Counter(item['author'] for item in quarantine_data)
    logger.info(f"\n  按作者统计:")
    for author, count in author_stats.most_common():
        logger.info(f"    {author}: {count}个文件")
    
    # 误隔离统计
    misquarantined = [item for item in quarantine_data if item['is_misquarantined']]
    total_count = len(quarantine_data)
    misquarantine_rate = (len(misquarantined) / total_count * 100) if total_count > 0 else 0
    
    logger.info(f"\n  误隔离分析:")
    logger.info(f"    总隔离文件: {total_count}")
    logger.info(f"    疑似误隔离: {len(misquarantined)}")
    logger.info(f"    误隔离率: {misquarantine_rate:.1f}%")
    
    if misquarantined:
        logger.info(f"    误隔离文件示例:")
        for item in misquarantined[:5]:
            duration_str = f"{item['actual_duration']:.1f}s" if item['actual_duration'] is not None else "未检测到"
            logger.info(f"      {item['filename']}: {item['reason']} (实际时长: {duration_str})")
    
    # 时长检测准确性分析
    opencv_success = sum(1 for item in quarantine_data if item['opencv_duration'] is not None)
    ffprobe_success = sum(1 for item in quarantine_data if item['ffprobe_duration'] is not None)
    
    logger.info(f"\n  时长检测方法准确性:")
    logger.info(f"    OpenCV成功率: {opencv_success}/{total_count} ({opencv_success/total_count*100:.1f}%)")
    logger.info(f"    FFprobe成功率: {ffprobe_success}/{total_count} ({ffprobe_success/total_count*100:.1f}%)")

def generate_quarantine_fix_recommendations(quarantine_data):
    """生成隔离修复建议"""
    logger.info(f"\n🛠️ 隔离机制修复建议:")
    
    misquarantined = [item for item in quarantine_data if item['is_misquarantined']]
    
    if misquarantined:
        logger.info(f"  🔴 发现 {len(misquarantined)} 个疑似误隔离文件")
        logger.info(f"  建议措施:")
        logger.info(f"    1. 实施多重验证机制")
        logger.info(f"    2. 改进时长检测算法")
        logger.info(f"    3. 添加隔离前二次确认")
        logger.info(f"    4. 建立视频格式白名单")
        
        # 生成恢复脚本
        recovery_script = generate_recovery_script(misquarantined)
        logger.info(f"    5. 运行恢复脚本: {recovery_script}")
    else:
        logger.info(f"  ✅ 未发现明显误隔离，隔离机制工作正常")

def generate_recovery_script(misquarantined_files):
    """生成误隔离文件恢复脚本"""
    script_path = "ai_tools/ai_tool_20250722_recover_misquarantined.py"
    
    script_content = f'''#!/usr/bin/env python3
"""
误隔离视频文件恢复脚本
自动恢复被误隔离的视频文件
"""

import sys
import os
import shutil
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

def recover_misquarantined_videos():
    """恢复误隔离的视频文件"""
    logger.info("🔄 开始恢复误隔离的视频文件")
    
    quarantine_dir = Path("quarantine/invalid_videos")
    recovery_dir = Path("quarantine/recovered_videos")
    recovery_dir.mkdir(exist_ok=True)
    
    # 疑似误隔离文件列表
    misquarantined_files = {json.dumps([item['filename'] for item in misquarantined_files], indent=2)}
    
    recovered_count = 0
    for filename in misquarantined_files:
        src_path = quarantine_dir / filename
        dst_path = recovery_dir / filename
        
        if src_path.exists():
            try:
                shutil.move(str(src_path), str(dst_path))
                logger.info(f"恢复文件: {{filename}}")
                recovered_count += 1
                
                # 同时移动reason文件
                reason_src = src_path.with_suffix(src_path.suffix + ".reason.txt")
                if reason_src.exists():
                    reason_dst = dst_path.with_suffix(dst_path.suffix + ".reason.txt")
                    shutil.move(str(reason_src), str(reason_dst))
                    
            except Exception as e:
                logger.error(f"恢复文件失败 {{filename}}: {{e}}")
    
    logger.info(f"✅ 恢复完成，共恢复 {{recovered_count}} 个文件")

if __name__ == "__main__":
    recover_misquarantined_videos()
'''
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    return script_path

def main():
    """主函数"""
    try:
        quarantine_data = analyze_quarantined_videos()
        
        logger.info(f"\n✅ 隔离机制分析完成")
        logger.info(f"建议优先修复视频时长检测算法，减少误隔离")
        
        return quarantine_data
        
    except Exception as e:
        logger.error(f"❌ 分析过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
