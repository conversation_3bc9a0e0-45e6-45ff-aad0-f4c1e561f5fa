#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目11:00后系统日志全面健康状况分析
清理条件: 功能被替代时删除
"""

import os
import re
import json
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from loguru import logger

class ComprehensiveLogAnalyzer:
    """千川自动化项目综合日志分析器"""
    
    def __init__(self):
        self.project_root = project_root
        self.log_dir = os.path.join(project_root, 'logs')
        self.target_time = datetime(2025, 7, 24, 11, 0, 0)
        
        # 分析结果存储
        self.error_stats = defaultdict(int)
        self.warning_stats = defaultdict(int)
        self.workflow_status = defaultdict(list)
        self.api_performance = defaultdict(list)
        self.database_operations = defaultdict(list)
        self.system_health = {
            'errors': [],
            'warnings': [],
            'performance_issues': [],
            'workflow_failures': [],
            'api_failures': []
        }
        
        # 严重程度分级
        self.severity_levels = {
            'CRITICAL': [],
            'HIGH': [],
            'MEDIUM': [],
            'LOW': []
        }
        
    def analyze_logs_after_11am(self):
        """分析11:00之后的所有日志"""
        logger.info("🔍 开始分析2025-07-24 11:00之后的系统日志...")
        
        # 分析主日志文件
        main_log_file = os.path.join(self.log_dir, 'app_2025-07-24.log')
        if os.path.exists(main_log_file):
            self._analyze_main_log(main_log_file)
        
        # 分析其他专项日志
        self._analyze_specialized_logs()
        
        # 生成综合分析报告
        self._generate_comprehensive_report()
        
    def _analyze_main_log(self, log_file_path: str):
        """分析主日志文件"""
        logger.info(f"📋 分析主日志文件: {log_file_path}")
        
        try:
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 过滤11:00之后的日志
            filtered_lines = []
            for line in lines:
                if self._is_after_target_time(line):
                    filtered_lines.append(line)
            
            logger.info(f"📊 找到11:00后日志条目: {len(filtered_lines)} 条")
            
            # 分析各类问题
            self._analyze_errors(filtered_lines)
            self._analyze_warnings(filtered_lines)
            self._analyze_workflow_integrity(filtered_lines)
            self._analyze_api_performance(filtered_lines)
            self._analyze_database_operations(filtered_lines)
            self._analyze_system_stability(filtered_lines)
            
        except Exception as e:
            logger.error(f"❌ 分析主日志文件失败: {e}")
    
    def _is_after_target_time(self, line: str) -> bool:
        """检查日志行是否在目标时间之后"""
        # 匹配时间戳格式: 2025-07-24 HH:mm:ss
        time_pattern = r'2025-07-24 (\d{2}):(\d{2}):(\d{2})'
        match = re.search(time_pattern, line)
        
        if match:
            hour, minute, second = map(int, match.groups())
            log_time = datetime(2025, 7, 24, hour, minute, second)
            return log_time >= self.target_time
        
        return False
    
    def _analyze_errors(self, lines: List[str]):
        """分析ERROR级别日志"""
        logger.info("🔍 分析ERROR级别日志...")
        
        error_patterns = {
            '视频长度需大于4s小于300s': 0,
            '视频尺寸错误': 0,
            '文件不存在': 0,
            '数据库事务回滚': 0,
            'API请求失败': 0,
            '函数.*在.*次尝试后仍然失败': 0,
            '处理单个视频上传任务时发生严重错误': 0,
            '隔离视频文件失败': 0
        }
        
        for line in lines:
            if 'ERROR' in line:
                self.system_health['errors'].append(line.strip())
                
                # 统计错误模式
                for pattern, count in error_patterns.items():
                    if re.search(pattern, line):
                        error_patterns[pattern] += 1
        
        # 分类错误严重程度
        for pattern, count in error_patterns.items():
            if count > 0:
                if count > 1000:
                    self.severity_levels['CRITICAL'].append(f"{pattern}: {count}次")
                elif count > 100:
                    self.severity_levels['HIGH'].append(f"{pattern}: {count}次")
                elif count > 10:
                    self.severity_levels['MEDIUM'].append(f"{pattern}: {count}次")
                else:
                    self.severity_levels['LOW'].append(f"{pattern}: {count}次")
        
        self.error_stats.update(error_patterns)
        logger.info(f"📊 ERROR统计完成，发现 {len(self.system_health['errors'])} 个错误")
    
    def _analyze_warnings(self, lines: List[str]):
        """分析WARNING级别日志"""
        logger.info("⚠️ 分析WARNING级别日志...")
        
        for line in lines:
            if 'WARNING' in line:
                self.system_health['warnings'].append(line.strip())
        
        logger.info(f"📊 WARNING统计完成，发现 {len(self.system_health['warnings'])} 个警告")
    
    def _analyze_workflow_integrity(self, lines: List[str]):
        """分析工作流完整性"""
        logger.info("🔄 分析工作流完整性...")
        
        workflow_patterns = {
            'upload_single_video': {'start': 0, 'success': 0, 'failed': 0},
            'material_collection': {'start': 0, 'success': 0, 'failed': 0},
            'plan_creation': {'start': 0, 'success': 0, 'failed': 0},
            'file_ingestion': {'start': 0, 'success': 0, 'failed': 0}
        }
        
        for line in lines:
            # 视频上传工作流
            if '[Task Start] Uploading video:' in line:
                workflow_patterns['upload_single_video']['start'] += 1
            elif '[Task Failed] Error uploading video' in line:
                workflow_patterns['upload_single_video']['failed'] += 1
                self.system_health['workflow_failures'].append(line.strip())
            
            # 素材收集工作流
            if '素材收集任务执行完成' in line:
                workflow_patterns['material_collection']['success'] += 1
            elif '素材收集失败' in line:
                workflow_patterns['material_collection']['failed'] += 1
        
        self.workflow_status.update(workflow_patterns)
        logger.info("📊 工作流完整性分析完成")
    
    def _analyze_api_performance(self, lines: List[str]):
        """分析API性能"""
        logger.info("🌐 分析API性能...")
        
        api_calls = []
        response_times = []
        
        for line in lines:
            # API调用分析
            if 'API请求失败' in line:
                self.system_health['api_failures'].append(line.strip())
            
            # 响应时间分析
            if '收到千川 API 原始响应' in line:
                api_calls.append(line.strip())
            
            # 提取响应时间（从请求到响应的时间差）
            time_match = re.search(r'2025-07-24 (\d{2}):(\d{2}):(\d{2})', line)
            if time_match and 'API请求失败' in line:
                # 记录失败的API调用时间
                hour, minute, second = map(int, time_match.groups())
                response_times.append(f"{hour:02d}:{minute:02d}:{second:02d}")
        
        self.api_performance['total_calls'] = len(api_calls)
        self.api_performance['failed_calls'] = len(self.system_health['api_failures'])
        self.api_performance['success_rate'] = (
            (len(api_calls) - len(self.system_health['api_failures'])) / len(api_calls) * 100
            if len(api_calls) > 0 else 0
        )
        
        logger.info(f"📊 API性能分析完成，成功率: {self.api_performance['success_rate']:.2f}%")
    
    def _analyze_database_operations(self, lines: List[str]):
        """分析数据库操作"""
        logger.info("🗄️ 分析数据库操作...")
        
        db_operations = {
            'commits': 0,
            'rollbacks': 0,
            'connections': 0,
            'errors': 0
        }
        
        for line in lines:
            if '数据库事务提交成功' in line:
                db_operations['commits'] += 1
            elif '数据库事务回滚' in line:
                db_operations['rollbacks'] += 1
            elif '从统一数据库加载了Token' in line:
                db_operations['connections'] += 1
            elif 'database' in line.lower() and 'ERROR' in line:
                db_operations['errors'] += 1
        
        self.database_operations.update(db_operations)
        
        # 评估数据库健康状况
        rollback_rate = (db_operations['rollbacks'] / 
                        (db_operations['commits'] + db_operations['rollbacks']) * 100
                        if (db_operations['commits'] + db_operations['rollbacks']) > 0 else 0)
        
        if rollback_rate > 50:
            self.severity_levels['CRITICAL'].append(f"数据库回滚率过高: {rollback_rate:.2f}%")
        elif rollback_rate > 20:
            self.severity_levels['HIGH'].append(f"数据库回滚率较高: {rollback_rate:.2f}%")
        
        logger.info(f"📊 数据库操作分析完成，回滚率: {rollback_rate:.2f}%")
    
    def _analyze_system_stability(self, lines: List[str]):
        """分析系统稳定性"""
        logger.info("🔧 分析系统稳定性...")
        
        stability_indicators = {
            'service_restarts': 0,
            'connection_failures': 0,
            'timeout_errors': 0,
            'memory_issues': 0,
            'rate_limit_hits': 0
        }
        
        for line in lines:
            if '重新启动' in line or 'restart' in line.lower():
                stability_indicators['service_restarts'] += 1
            elif '连接失败' in line or 'connection failed' in line.lower():
                stability_indicators['connection_failures'] += 1
            elif 'timeout' in line.lower() or '超时' in line:
                stability_indicators['timeout_errors'] += 1
            elif 'memory' in line.lower() or '内存' in line:
                stability_indicators['memory_issues'] += 1
            elif 'rate limit' in line.lower() or '频率限制' in line:
                stability_indicators['rate_limit_hits'] += 1
        
        # 评估稳定性问题
        for indicator, count in stability_indicators.items():
            if count > 0:
                if count > 100:
                    self.severity_levels['HIGH'].append(f"{indicator}: {count}次")
                elif count > 10:
                    self.severity_levels['MEDIUM'].append(f"{indicator}: {count}次")
                else:
                    self.severity_levels['LOW'].append(f"{indicator}: {count}次")
        
        logger.info("📊 系统稳定性分析完成")
    
    def _analyze_specialized_logs(self):
        """分析专项日志文件"""
        logger.info("📋 分析专项日志文件...")
        
        specialized_logs = [
            'git_auto_commit.log',
            'project_protection.log',
            'system_health_check.log',
            'auto_rules_engine.log'
        ]
        
        for log_name in specialized_logs:
            log_path = os.path.join(self.log_dir, log_name)
            if os.path.exists(log_path):
                try:
                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'ERROR' in content:
                            self.system_health['errors'].extend([
                                f"[{log_name}] {line.strip()}" 
                                for line in content.split('\n') 
                                if 'ERROR' in line and line.strip()
                            ])
                except Exception as e:
                    logger.warning(f"⚠️ 无法读取专项日志 {log_name}: {e}")
    
    def _generate_comprehensive_report(self):
        """生成综合分析报告"""
        logger.info("📊 生成综合分析报告...")
        
        report = {
            'analysis_time': datetime.now().isoformat(),
            'target_time_range': f"2025-07-24 11:00:00 之后",
            'summary': self._generate_summary(),
            'error_analysis': dict(self.error_stats),
            'workflow_status': dict(self.workflow_status),
            'api_performance': dict(self.api_performance),
            'database_operations': dict(self.database_operations),
            'severity_assessment': dict(self.severity_levels),
            'recommendations': self._generate_recommendations(),
            'overall_health_score': self._calculate_health_score()
        }
        
        # 保存报告
        report_path = os.path.join(
            self.project_root, 
            'ai_reports', 
            'analysis', 
            f'ai_report_20250724_comprehensive_log_analysis_{datetime.now().strftime("%H%M%S")}.json'
        )
        
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.success(f"✅ 综合分析报告已保存: {report_path}")
        
        # 输出关键发现
        self._print_key_findings(report)
        
        return report
    
    def _generate_summary(self) -> Dict[str, Any]:
        """生成分析摘要"""
        total_errors = len(self.system_health['errors'])
        total_warnings = len(self.system_health['warnings'])
        
        return {
            'total_errors': total_errors,
            'total_warnings': total_warnings,
            'critical_issues': len(self.severity_levels['CRITICAL']),
            'high_priority_issues': len(self.severity_levels['HIGH']),
            'workflow_failures': len(self.system_health['workflow_failures']),
            'api_failures': len(self.system_health['api_failures'])
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成处理建议"""
        recommendations = []
        
        # 基于错误统计生成建议
        if self.error_stats.get('视频长度需大于4s小于300s', 0) > 100:
            recommendations.append("🎥 紧急建议：大量视频长度不符合要求，需要检查视频预处理流程")
        
        if self.error_stats.get('文件不存在', 0) > 50:
            recommendations.append("📁 高优先级：大量文件不存在错误，需要检查文件路径配置和存储状态")
        
        if self.api_performance.get('success_rate', 100) < 50:
            recommendations.append("🌐 严重问题：API成功率过低，需要检查网络连接和API配置")
        
        if len(self.severity_levels['CRITICAL']) > 0:
            recommendations.append("🚨 立即处理：发现严重级别问题，需要紧急干预")
        
        return recommendations
    
    def _calculate_health_score(self) -> int:
        """计算系统健康评分 (0-100)"""
        score = 100
        
        # 根据错误数量扣分
        total_errors = len(self.system_health['errors'])
        score -= min(total_errors // 10, 30)  # 每10个错误扣1分，最多扣30分
        
        # 根据严重程度扣分
        score -= len(self.severity_levels['CRITICAL']) * 20  # 每个严重问题扣20分
        score -= len(self.severity_levels['HIGH']) * 10      # 每个高优先级问题扣10分
        score -= len(self.severity_levels['MEDIUM']) * 5     # 每个中等问题扣5分
        
        # 根据API成功率调整
        api_success_rate = self.api_performance.get('success_rate', 100)
        if api_success_rate < 90:
            score -= (90 - api_success_rate)
        
        return max(score, 0)
    
    def _print_key_findings(self, report: Dict[str, Any]):
        """输出关键发现"""
        logger.critical("\n" + "="*80)
        logger.critical("🎯 千川自动化项目系统健康状况评估报告")
        logger.critical("="*80)
        
        summary = report['summary']
        logger.critical(f"📊 总体统计:")
        logger.critical(f"   错误总数: {summary['total_errors']}")
        logger.critical(f"   警告总数: {summary['total_warnings']}")
        logger.critical(f"   严重问题: {summary['critical_issues']}")
        logger.critical(f"   高优先级问题: {summary['high_priority_issues']}")
        logger.critical(f"   工作流失败: {summary['workflow_failures']}")
        logger.critical(f"   API失败: {summary['api_failures']}")
        
        logger.critical(f"\n🏥 系统健康评分: {report['overall_health_score']}/100")
        
        if report['overall_health_score'] < 60:
            logger.critical("🚨 系统状态: 严重异常，需要立即处理！")
        elif report['overall_health_score'] < 80:
            logger.critical("⚠️ 系统状态: 存在问题，需要关注")
        else:
            logger.critical("✅ 系统状态: 基本正常")
        
        # 输出严重问题
        if self.severity_levels['CRITICAL']:
            logger.critical(f"\n🚨 严重问题 ({len(self.severity_levels['CRITICAL'])}个):")
            for issue in self.severity_levels['CRITICAL']:
                logger.critical(f"   • {issue}")
        
        # 输出建议
        recommendations = report['recommendations']
        if recommendations:
            logger.critical(f"\n💡 处理建议:")
            for i, rec in enumerate(recommendations, 1):
                logger.critical(f"   {i}. {rec}")
        
        logger.critical("="*80)

def main():
    """主函数"""
    try:
        analyzer = ComprehensiveLogAnalyzer()
        analyzer.analyze_logs_after_11am()
        return True
    except Exception as e:
        logger.error(f"❌ 日志分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
