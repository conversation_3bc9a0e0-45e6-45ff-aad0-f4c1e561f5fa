#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具：智能计划名称清理和重命名机制
生命周期：永久工具
用途：为高级计划复制功能提供智能的名称清理和重命名功能

功能特点：
1. 识别并移除计划名称中的历史前缀
2. 保留原始的标准格式部分
3. 支持自定义新前缀和重名冲突处理
4. 提供预览功能和批量处理能力

创建时间：2025-07-20
作者：AI Assistant
"""

import re
import logging
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass

# 设置日志
logger = logging.getLogger(__name__)

@dataclass
class NameCleaningResult:
    """名称清理结果"""
    original_name: str
    cleaned_name: str
    detected_prefixes: List[str]
    is_standard_format: bool
    final_name: str
    has_conflict: bool
    conflict_suffix: str = ""

class PlanNameCleaner:
    """智能计划名称清理器"""
    
    def __init__(self):
        # 标准格式正则表达式：日期/策略/创作者/日期/时间标识
        self.standard_format_pattern = re.compile(
            r'(\d{1,2}\.\d{1,2}\/[^\/]+\/[^\/]+\/\d{1,2}\.\d{1,2}\/\d{2}-\d{2}-\d{4})'
        )
        
        # 常见前缀模式
        self.common_prefixes = [
            r'测试_+',
            r'复制_+',
            r'新_+',
            r'备份_+',
            r'临时_+',
            r'实验_+',
            r'试验_+',
            r'demo_+',
            r'test_+',
            r'copy_+',
            r'new_+',
            r'backup_+',
            r'temp_+',
            r'[a-zA-Z0-9]+_+',  # 通用字母数字前缀
            r'[\u4e00-\u9fff]+_+',  # 中文前缀
        ]
        
        # 编译前缀正则表达式
        self.prefix_patterns = [re.compile(f'^{pattern}', re.IGNORECASE) for pattern in self.common_prefixes]
    
    def detect_standard_format(self, name: str) -> Tuple[bool, str]:
        """
        检测计划名称是否符合标准格式
        
        Args:
            name: 计划名称
            
        Returns:
            (是否标准格式, 标准格式部分)
        """
        match = self.standard_format_pattern.search(name)
        if match:
            return True, match.group(1)
        return False, ""
    
    def detect_prefixes(self, name: str) -> List[str]:
        """
        检测计划名称中的前缀
        
        Args:
            name: 计划名称
            
        Returns:
            检测到的前缀列表
        """
        detected_prefixes = []
        current_name = name
        
        # 循环检测多层前缀
        max_iterations = 10  # 防止无限循环
        iteration = 0
        
        while iteration < max_iterations:
            found_prefix = False
            
            for pattern in self.prefix_patterns:
                match = pattern.match(current_name)
                if match:
                    prefix = match.group(0)
                    detected_prefixes.append(prefix.rstrip('_'))
                    current_name = current_name[len(prefix):]
                    found_prefix = True
                    break
            
            if not found_prefix:
                break
                
            iteration += 1
        
        return detected_prefixes
    
    def clean_name(self, name: str, preserve_standard_format: bool = True) -> NameCleaningResult:
        """
        清理计划名称
        
        Args:
            name: 原始计划名称
            preserve_standard_format: 是否保留标准格式
            
        Returns:
            清理结果
        """
        # 检测前缀
        detected_prefixes = self.detect_prefixes(name)
        
        # 移除前缀后的名称
        cleaned_name = name
        for prefix in detected_prefixes:
            # 移除前缀及其后的下划线
            pattern = re.compile(f'^{re.escape(prefix)}_*', re.IGNORECASE)
            cleaned_name = pattern.sub('', cleaned_name)
        
        # 检测标准格式
        is_standard_format, standard_part = self.detect_standard_format(cleaned_name)
        
        # 如果要求保留标准格式且检测到标准格式，使用标准部分
        if preserve_standard_format and is_standard_format:
            final_cleaned = standard_part
        else:
            final_cleaned = cleaned_name
        
        return NameCleaningResult(
            original_name=name,
            cleaned_name=final_cleaned,
            detected_prefixes=detected_prefixes,
            is_standard_format=is_standard_format,
            final_name=final_cleaned,
            has_conflict=False
        )
    
    def generate_unique_name(self, base_name: str, existing_names: Set[str], 
                           new_prefix: str = "", max_attempts: int = 100) -> str:
        """
        生成唯一的计划名称
        
        Args:
            base_name: 基础名称
            existing_names: 已存在的名称集合
            new_prefix: 新前缀
            max_attempts: 最大尝试次数
            
        Returns:
            唯一的计划名称
        """
        # 构建初始名称
        if new_prefix:
            candidate_name = f"{new_prefix}_{base_name}"
        else:
            candidate_name = base_name
        
        # 如果没有冲突，直接返回
        if candidate_name not in existing_names:
            return candidate_name
        
        # 处理冲突，添加数字后缀
        for i in range(1, max_attempts + 1):
            if new_prefix:
                candidate_name = f"{new_prefix}_{base_name}_{i}"
            else:
                candidate_name = f"{base_name}_{i}"
            
            if candidate_name not in existing_names:
                return candidate_name
        
        # 如果所有尝试都失败，使用时间戳
        import time
        timestamp = int(time.time())
        if new_prefix:
            return f"{new_prefix}_{base_name}_{timestamp}"
        else:
            return f"{base_name}_{timestamp}"
    
    def batch_clean_names(self, names: List[str], existing_names: Set[str] = None,
                         new_prefix: str = "", preserve_standard_format: bool = True) -> List[NameCleaningResult]:
        """
        批量清理计划名称
        
        Args:
            names: 要清理的名称列表
            existing_names: 已存在的名称集合
            new_prefix: 新前缀
            preserve_standard_format: 是否保留标准格式
            
        Returns:
            清理结果列表
        """
        if existing_names is None:
            existing_names = set()
        
        results = []
        used_names = existing_names.copy()
        
        for name in names:
            # 清理名称
            result = self.clean_name(name, preserve_standard_format)
            
            # 生成唯一名称
            unique_name = self.generate_unique_name(
                result.cleaned_name, used_names, new_prefix
            )
            
            # 检查是否有冲突
            if unique_name != (f"{new_prefix}_{result.cleaned_name}" if new_prefix else result.cleaned_name):
                result.has_conflict = True
                result.conflict_suffix = unique_name.split('_')[-1] if '_' in unique_name else ""
            
            result.final_name = unique_name
            used_names.add(unique_name)
            results.append(result)
        
        return results
    
    def preview_cleaning(self, names: List[str], new_prefix: str = "", 
                        preserve_standard_format: bool = True) -> Dict[str, any]:
        """
        预览清理结果
        
        Args:
            names: 要清理的名称列表
            new_prefix: 新前缀
            preserve_standard_format: 是否保留标准格式
            
        Returns:
            预览结果统计
        """
        results = self.batch_clean_names(names, set(), new_prefix, preserve_standard_format)
        
        # 统计信息
        total_count = len(results)
        prefix_detected_count = sum(1 for r in results if r.detected_prefixes)
        standard_format_count = sum(1 for r in results if r.is_standard_format)
        conflict_count = sum(1 for r in results if r.has_conflict)
        
        # 检测到的前缀统计
        all_prefixes = []
        for r in results:
            all_prefixes.extend(r.detected_prefixes)
        prefix_stats = {}
        for prefix in all_prefixes:
            prefix_stats[prefix] = prefix_stats.get(prefix, 0) + 1
        
        return {
            'results': results,
            'statistics': {
                'total_count': total_count,
                'prefix_detected_count': prefix_detected_count,
                'standard_format_count': standard_format_count,
                'conflict_count': conflict_count,
                'prefix_stats': prefix_stats
            }
        }

def test_name_cleaner():
    """测试名称清理器"""
    cleaner = PlanNameCleaner()
    
    # 测试用例
    test_names = [
        "测试_复制_07.18/托管成交-日常/谢莉/07.18/23-44-3867",
        "新_07.18/自定义成交-日常/杨婷婷/07.18/23-36-8538",
        "backup_test_07.18/托管ROI-日常/杨婷婷/07.18/23-46-3570",
        "q_mm_ql_07.18/托管成交-日常/谢莉/07.18/00-59-7294",
        "普通计划名称",
        "测试_测试_测试_最终计划"
    ]
    
    print("=== 计划名称清理测试 ===")
    
    # 单个清理测试
    for name in test_names:
        result = cleaner.clean_name(name)
        print(f"\n原名称: {result.original_name}")
        print(f"检测前缀: {result.detected_prefixes}")
        print(f"清理后: {result.cleaned_name}")
        print(f"标准格式: {result.is_standard_format}")
    
    # 批量清理测试
    print("\n=== 批量清理测试 ===")
    existing_names = {"07.18/托管成交-日常/谢莉/07.18/23-44-3867"}
    batch_results = cleaner.batch_clean_names(test_names, existing_names, "新测试")
    
    for result in batch_results:
        print(f"{result.original_name} -> {result.final_name} (冲突: {result.has_conflict})")
    
    # 预览测试
    print("\n=== 预览测试 ===")
    preview = cleaner.preview_cleaning(test_names, "预览")
    print(f"总数: {preview['statistics']['total_count']}")
    print(f"检测到前缀: {preview['statistics']['prefix_detected_count']}")
    print(f"标准格式: {preview['statistics']['standard_format_count']}")
    print(f"前缀统计: {preview['statistics']['prefix_stats']}")

if __name__ == "__main__":
    test_name_cleaner()
