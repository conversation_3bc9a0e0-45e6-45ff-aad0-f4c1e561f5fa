#!/usr/bin/env python3
"""
提审模块简化测试套件
验证核心功能是否可以正常工作
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

def test_imports():
    """测试关键模块导入"""
    logger.info("🧪 测试模块导入...")
    
    try:
        from src.qianchuan_aw.utils.sync_playwright_manager import sync_playwright_manager
        logger.info("  ✅ sync_playwright_manager 导入成功")
        
        from src.qianchuan_aw.services.unified_appeal_service import unified_appeal_service
        logger.info("  ✅ unified_appeal_service 导入成功")
        
        from src.qianchuan_aw.utils.appeal_state_manager import appeal_state_manager
        logger.info("  ✅ appeal_state_manager 导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基础功能"""
    logger.info("🧪 测试基础功能...")
    
    try:
        # 测试统一提审服务统计
        from src.qianchuan_aw.services.unified_appeal_service import unified_appeal_service
        stats = unified_appeal_service.get_stats()
        logger.info(f"  ✅ 统计功能正常: {stats}")
        
        # 测试配置加载
        from src.qianchuan_aw.utils.config_loader import load_settings
        config = load_settings()
        appeal_config = config.get('workflow', {}).get('plan_appeal', {})
        logger.info(f"  ✅ 配置加载正常: enabled={appeal_config.get('enabled')}")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 基础功能测试失败: {e}")
        return False

def test_workflow_integration():
    """测试工作流集成"""
    logger.info("🧪 测试工作流集成...")
    
    try:
        from ai_tools.appeal_workflow_integration_patch import handle_plans_awaiting_appeal_v2
        logger.info("  ✅ 工作流集成函数导入成功")
        
        # 注意：这里不实际执行，只测试导入
        logger.info("  ✅ 工作流集成测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"  ❌ 工作流集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始简化测试套件")
    logger.info("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("基础功能", test_basic_functionality),
        ("工作流集成", test_workflow_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"
📋 执行测试: {test_name}")
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    logger.info(f"\n📊 测试结果:")
    logger.info(f"  总测试数: {total}")
    logger.info(f"  通过测试: {passed}")
    logger.info(f"  失败测试: {total - passed}")
    logger.info(f"  通过率: {(passed/total*100):.1f}%")
    
    if passed == total:
        logger.info(f"\n🎉 所有测试通过！提审模块可以正常使用")
        return True
    else:
        logger.warning(f"\n⚠️ 部分测试失败，模块可能存在问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
