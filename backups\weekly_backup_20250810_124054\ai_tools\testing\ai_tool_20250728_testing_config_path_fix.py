#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 测试配置文件路径修复
清理条件: 路径问题解决后可删除
"""

import sys
from pathlib import Path
from loguru import logger

def test_config_path_resolution():
    """测试配置文件路径解析"""
    logger.info("🔍 测试配置文件路径解析...")
    
    try:
        # 模拟智能提审调度器页面的路径解析逻辑
        current_path = Path(__file__).resolve()
        
        # 测试不同的路径计算方法
        methods = [
            ("当前文件向上4级", current_path.parent.parent.parent.parent),
            ("当前文件向上3级", current_path.parent.parent.parent),
            ("当前工作目录", Path.cwd()),
            ("硬编码路径", Path('D:/Project/qianchuangzl'))
        ]
        
        logger.info(f"📋 当前文件路径: {current_path}")
        
        for method_name, test_root in methods:
            config_file = test_root / 'config' / 'settings.yml'
            exists = config_file.exists()
            
            logger.info(f"   {method_name}:")
            logger.info(f"     项目根路径: {test_root}")
            logger.info(f"     配置文件路径: {config_file}")
            logger.info(f"     文件存在: {'✅' if exists else '❌'}")
            
            if exists:
                logger.success(f"✅ 找到正确路径: {method_name}")
                return test_root
        
        logger.error("❌ 所有路径方法都失败")
        return None
        
    except Exception as e:
        logger.error(f"❌ 路径解析测试失败: {e}")
        return None

def test_config_loading():
    """测试配置文件加载"""
    logger.info("🔍 测试配置文件加载...")
    
    try:
        # 找到正确的项目根路径
        project_root = test_config_path_resolution()
        
        if not project_root:
            logger.error("❌ 无法找到项目根路径")
            return False
        
        # 尝试加载配置文件
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        logger.success("✅ 配置文件加载成功")
        
        # 检查关键配置项
        key_configs = [
            ('appeal_strategy', 'appeal_for_prod_plans'),
            ('appeal_scheduler', 'min_plan_age_minutes'),
            ('database', 'postgresql')
        ]
        
        for section, key in key_configs:
            value = config.get(section, {}).get(key, 'NOT_FOUND')
            logger.info(f"   📋 {section}.{key}: {value}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件加载失败: {e}")
        return False

def test_smart_scheduler_import():
    """测试智能调度器模块导入"""
    logger.info("🔍 测试智能调度器模块导入...")
    
    try:
        # 找到项目根路径
        project_root = Path.cwd()
        sys.path.insert(0, str(project_root))
        
        # 测试导入智能调度器页面
        sys.path.insert(0, str(project_root / 'src' / 'qianchuan_aw' / 'web_ui' / 'pages'))
        
        try:
            from simple_smart_appeal_scheduler import load_config, get_pending_plans_stats
            logger.success("✅ 智能调度器页面模块导入成功")
            
            # 测试配置加载
            config = load_config()
            if config:
                logger.success("✅ 配置加载功能正常")
            else:
                logger.error("❌ 配置加载功能异常")
                return False
            
            return True
            
        except ImportError as e:
            logger.error(f"❌ 智能调度器页面模块导入失败: {e}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 模块导入测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 配置文件路径修复验证")
    logger.info("="*60)
    logger.info("🔍 测试内容:")
    logger.info("1. 配置文件路径解析")
    logger.info("2. 配置文件加载")
    logger.info("3. 智能调度器模块导入")
    logger.info("="*60)
    
    try:
        tests = [
            ("配置文件路径解析", lambda: test_config_path_resolution() is not None),
            ("配置文件加载", test_config_loading),
            ("智能调度器模块导入", test_smart_scheduler_import)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            logger.info(f"\n🧪 执行测试: {test_name}")
            try:
                result = test_func()
                results.append((test_name, result))
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   结果: {status}")
            except Exception as e:
                results.append((test_name, False))
                logger.error(f"   结果: ❌ 异常 - {e}")
        
        # 生成测试报告
        logger.info("\n" + "="*60)
        logger.info("🎯 配置文件路径修复验证结果")
        logger.info("="*60)
        
        passed_count = sum(1 for _, result in results if result)
        total_count = len(results)
        
        logger.info(f"📊 测试结果: {passed_count}/{total_count} 项通过")
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
        
        if passed_count == total_count:
            logger.success("\n🎉 配置文件路径修复验证完全成功！")
            logger.info("💡 智能提审调度器Web页面现在可以正常使用")
        elif passed_count >= total_count * 0.67:
            logger.warning("\n⚠️ 配置文件路径修复基本成功，但有部分问题")
        else:
            logger.error("\n❌ 配置文件路径修复验证失败")
        
        return passed_count == total_count
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 配置文件路径问题已完全解决！")
        logger.info("💡 现在可以正常使用智能提审调度器Web界面")
    else:
        logger.error("\n❌ 配置文件路径问题仍需进一步修复")
    
    sys.exit(0 if success else 1)
