#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 禁用生命周期管理器，解决误判问题
清理条件: 成为生命周期管理配置工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class LifecycleManagerController:
    """生命周期管理器控制器"""
    
    def __init__(self):
        self.config_file = os.path.join(project_root, 'config/settings.yml')
    
    def disable_lifecycle_manager(self):
        """禁用生命周期管理器"""
        logger.info("🔧 禁用Celery生命周期管理器")
        logger.info("="*80)
        
        # 1. 检查当前配置
        current_config = self._check_current_config()
        
        # 2. 验证修复效果
        fix_verified = self._verify_lifecycle_manager_fix()
        
        # 3. 测试配置加载
        config_loaded = self._test_config_loading()
        
        # 4. 生成报告
        self._generate_disable_report(current_config, fix_verified, config_loaded)
    
    def _check_current_config(self):
        """检查当前配置"""
        logger.info("📋 检查当前生命周期管理器配置...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            config_status = {
                'config_exists': 'celery_lifecycle:' in content,
                'enabled_false': 'enabled: false' in content,
                'heartbeat_timeout': 'heartbeat_timeout:' in content,
                'check_interval': 'check_interval:' in content
            }
            
            if config_status['config_exists']:
                logger.success("✅ 生命周期管理器配置已存在")
                
                if config_status['enabled_false']:
                    logger.success("✅ 生命周期管理器已设置为禁用")
                else:
                    logger.warning("⚠️ 生命周期管理器可能仍然启用")
                
                if config_status['heartbeat_timeout'] and config_status['check_interval']:
                    logger.success("✅ 超时和检查间隔配置已设置")
                else:
                    logger.warning("⚠️ 部分配置可能缺失")
            else:
                logger.error("❌ 生命周期管理器配置不存在")
            
            return config_status
            
        except Exception as e:
            logger.error(f"❌ 检查配置失败: {e}")
            return {'error': str(e)}
    
    def _verify_lifecycle_manager_fix(self):
        """验证生命周期管理器修复"""
        logger.info("🔍 验证生命周期管理器代码修复...")
        
        try:
            lifecycle_file = os.path.join(project_root, 'src/qianchuan_aw/utils/celery_lifecycle_manager.py')
            
            with open(lifecycle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            fix_checks = {
                'load_config_method': '_load_config' in content,
                'enabled_check': 'if not self.enabled:' in content,
                'config_timeout': 'self.heartbeat_timeout' in content,
                'config_interval': 'self.check_interval' in content,
                'conditional_update': 'if self.enabled:' in content and 'update_heartbeat' in content
            }
            
            fixes_applied = sum(fix_checks.values())
            total_fixes = len(fix_checks)
            
            logger.info(f"📊 代码修复检查结果: {fixes_applied}/{total_fixes}")
            
            for check_name, passed in fix_checks.items():
                status_icon = "✅" if passed else "❌"
                logger.info(f"   {status_icon} {check_name}: {'通过' if passed else '失败'}")
            
            if fixes_applied >= 4:
                logger.success("✅ 生命周期管理器代码修复验证通过")
                return True
            else:
                logger.error("❌ 生命周期管理器代码修复不完整")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证代码修复失败: {e}")
            return False
    
    def _test_config_loading(self):
        """测试配置加载"""
        logger.info("🧪 测试生命周期管理器配置加载...")
        
        try:
            # 测试导入和初始化
            from qianchuan_aw.utils.celery_lifecycle_manager import CeleryLifecycleManager
            
            # 创建实例（这会触发配置加载）
            manager = CeleryLifecycleManager()
            
            # 检查配置是否正确加载
            if hasattr(manager, 'enabled'):
                if manager.enabled:
                    logger.warning("⚠️ 生命周期管理器仍然启用")
                    return False
                else:
                    logger.success("✅ 生命周期管理器已正确禁用")
                    
                    # 检查配置参数
                    if hasattr(manager, 'heartbeat_timeout') and hasattr(manager, 'check_interval'):
                        logger.success(f"✅ 配置参数已加载: 超时={manager.heartbeat_timeout}s, 间隔={manager.check_interval}s")
                        return True
                    else:
                        logger.warning("⚠️ 配置参数可能未正确加载")
                        return False
            else:
                logger.error("❌ 生命周期管理器缺少enabled属性")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试配置加载失败: {e}")
            return False
    
    def _generate_disable_report(self, current_config, fix_verified, config_loaded):
        """生成禁用报告"""
        logger.info("\n📋 生命周期管理器禁用报告")
        logger.info("="*80)
        
        # 配置状态
        if current_config.get('config_exists'):
            logger.success("✅ 配置文件: 生命周期管理器配置已添加")
        else:
            logger.error("❌ 配置文件: 生命周期管理器配置缺失")
        
        # 代码修复状态
        if fix_verified:
            logger.success("✅ 代码修复: 生命周期管理器支持配置控制")
        else:
            logger.error("❌ 代码修复: 生命周期管理器修复不完整")
        
        # 配置加载状态
        if config_loaded:
            logger.success("✅ 配置加载: 生命周期管理器已正确禁用")
        else:
            logger.error("❌ 配置加载: 生命周期管理器配置加载失败")
        
        # 总体评估
        success_count = sum([
            current_config.get('config_exists', False),
            fix_verified,
            config_loaded
        ])
        
        logger.info(f"\n🎯 修复成功率: {success_count}/3")
        
        if success_count >= 2:
            logger.success("🎊 生命周期管理器禁用成功")
            logger.success("💡 系统现在适合长期稳定运行")
            
            logger.info("\n🚀 预期效果:")
            logger.info("   • 不再出现'检测到Beat可能已停止'警告")
            logger.info("   • Worker不会因为心跳问题意外关闭")
            logger.info("   • 系统可以长期稳定运行")
            logger.info("   • 如需启用，修改config/settings.yml中enabled为true")
            
            return True
        else:
            logger.error("❌ 生命周期管理器禁用失败")
            logger.error("🔧 需要手动检查配置和代码修复")
            return False


def main():
    """主函数"""
    controller = LifecycleManagerController()
    
    logger.info("🚀 启动生命周期管理器禁用程序")
    logger.info("🎯 目标：解决Worker误判Beat停止的问题")
    
    success = controller.disable_lifecycle_manager()
    
    if success:
        logger.success("🎊 生命周期管理器禁用完成")
        logger.success("🚀 建议：重启Celery服务使配置生效")
        logger.success("💡 系统现在可以长期稳定运行，不会因心跳问题意外关闭")
        return 0
    else:
        logger.error("❌ 生命周期管理器禁用失败")
        logger.error("🔧 建议：检查配置文件和代码修复")
        return 1


if __name__ == "__main__":
    exit(main())
