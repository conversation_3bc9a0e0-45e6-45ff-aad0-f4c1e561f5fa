
# 违规检测频率控制修复补丁
# 在 handle_violation_detection 函数中添加频率控制

def handle_violation_detection_with_rate_limit():
    """带频率控制的违规检测处理"""
    from qianchuan_aw.utils.violation_rate_limiter import violation_rate_limiter
    
    try:
        # 检查是否可以进行违规检测
        api_endpoint = 'security/score_disposal_info/get'
        violation_rate_limiter.wait_if_needed(api_endpoint)
        
        # 执行原有的违规检测逻辑
        # ... (原有代码)
        
        # 记录成功调用
        violation_rate_limiter.record_call(api_endpoint, success=True)
        
    except Exception as e:
        # 记录失败调用
        violation_rate_limiter.record_call(api_endpoint, success=False)
        logger.error(f"违规检测失败: {e}")
        raise
