#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 临时使用
创建目的: 紧急修复上传失败问题，恢复系统正常运行
清理条件: 问题解决后可删除
"""

import sys
import time
import yaml
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class EmergencyUploadFixer:
    """紧急上传修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = project_root / 'config' / 'settings.yml'
        self.fixes_applied = []
        
    def check_current_issues(self):
        """检查当前问题状态"""
        print("🔍 检查当前问题状态...")
        
        issues = []
        
        # 1. 检查数据库连接池配置
        try:
            from qianchuan_aw.database.database import engine
            
            pool_size = engine.pool.size()
            max_overflow = getattr(engine.pool, '_max_overflow', 0)
            total_capacity = pool_size + max_overflow
            
            print(f"📊 数据库连接池状态:")
            print(f"  - 连接池大小: {pool_size}")
            print(f"  - 最大溢出: {max_overflow}")
            print(f"  - 总容量: {total_capacity}")
            
            if total_capacity < 20:
                issues.append(f"连接池容量不足: {total_capacity} < 20")
            else:
                print("  ✅ 连接池容量充足")
                
        except Exception as e:
            issues.append(f"无法检查数据库连接池: {e}")
        
        # 2. 检查并发配置
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            max_workers = config.get('workflow', {}).get('max_upload_workers', 0)
            print(f"📊 并发配置:")
            print(f"  - 最大上传工作线程: {max_workers}")
            
            if max_workers > 10:
                issues.append(f"并发度过高: {max_workers} > 10")
            elif max_workers <= 5:
                print("  ✅ 并发度合理")
            else:
                print("  🟡 并发度中等")
                
        except Exception as e:
            issues.append(f"无法检查配置文件: {e}")
        
        # 3. 检查当前连接使用情况
        try:
            from qianchuan_aw.database.database import engine
            
            pool = engine.pool
            checked_out = pool.checkedout()
            usage_rate = checked_out / total_capacity if total_capacity > 0 else 0
            
            print(f"📊 当前连接使用:")
            print(f"  - 已使用连接: {checked_out}")
            print(f"  - 使用率: {usage_rate*100:.1f}%")
            
            if usage_rate > 0.8:
                issues.append(f"连接使用率过高: {usage_rate*100:.1f}%")
            else:
                print("  ✅ 连接使用率正常")
                
        except Exception as e:
            issues.append(f"无法检查连接使用情况: {e}")
        
        return issues
    
    def apply_emergency_fixes(self):
        """应用紧急修复"""
        print("🚨 应用紧急修复...")
        
        fixes_needed = []
        
        # 1. 检查并修复配置文件
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            modified = False
            
            # 修复并发度
            current_workers = config.get('workflow', {}).get('max_upload_workers', 15)
            if current_workers > 5:
                config.setdefault('workflow', {})['max_upload_workers'] = 3
                fixes_needed.append(f"降低并发度: {current_workers} → 3")
                modified = True
            
            # 确保连接池配置存在
            if 'database' not in config:
                config['database'] = {}
            
            if 'connection_pool' not in config['database']:
                config['database']['connection_pool'] = {
                    'pool_size': 20,
                    'max_overflow': 30,
                    'pool_timeout': 30,
                    'pool_recycle': 1800,
                    'pool_pre_ping': True,
                    'echo': False
                }
                fixes_needed.append("添加连接池配置")
                modified = True
            
            # 保存修改
            if modified:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                print("✅ 配置文件已修复")
                self.fixes_applied.extend(fixes_needed)
            else:
                print("✅ 配置文件无需修复")
                
        except Exception as e:
            print(f"❌ 修复配置文件失败: {e}")
        
        # 2. 验证数据库引擎配置
        try:
            from qianchuan_aw.database.database import engine
            
            pool_size = engine.pool.size()
            max_overflow = getattr(engine.pool, '_max_overflow', 0)
            
            if pool_size < 20 or max_overflow < 30:
                print("⚠️ 数据库引擎配置未生效，需要重启服务")
                self.fixes_applied.append("需要重启服务以应用数据库配置")
            else:
                print("✅ 数据库引擎配置正确")
                
        except Exception as e:
            print(f"❌ 验证数据库引擎失败: {e}")
    
    def force_release_connections(self):
        """强制释放数据库连接"""
        print("🔄 尝试释放数据库连接...")
        
        try:
            from qianchuan_aw.database.database import engine
            
            # 获取当前状态
            pool = engine.pool
            checked_out_before = pool.checkedout()
            
            # 强制回收连接
            pool.dispose()
            
            # 等待一下让连接释放
            time.sleep(2)
            
            # 检查释放效果
            checked_out_after = pool.checkedout()
            
            print(f"📊 连接释放结果:")
            print(f"  - 释放前: {checked_out_before} 个连接")
            print(f"  - 释放后: {checked_out_after} 个连接")
            
            if checked_out_after < checked_out_before:
                print("✅ 成功释放部分连接")
                self.fixes_applied.append(f"释放了 {checked_out_before - checked_out_after} 个连接")
            else:
                print("⚠️ 连接释放效果不明显")
                
        except Exception as e:
            print(f"❌ 强制释放连接失败: {e}")
    
    def generate_restart_instructions(self):
        """生成重启指令"""
        print("\n🔄 服务重启指令:")
        print("="*50)
        print("为了确保所有修复生效，请按以下步骤重启服务:")
        print()
        print("1. 停止当前运行的工作流和Celery服务")
        print("2. 激活虚拟环境:")
        print("   conda activate qc_env")
        print()
        print("3. 重启服务以应用新配置")
        print("   (具体命令根据您的启动方式)")
        print()
        print("4. 验证修复效果:")
        print("   python ai_tools/monitoring/ai_tool_20250808_db_connection_monitor.py --mode quick")
        print()
        print("5. 如果问题仍然存在，请运行:")
        print("   python ai_tools/monitoring/ai_tool_20250808_db_connection_monitor.py --mode monitor --duration 10")
    
    def run_emergency_fix(self):
        """运行紧急修复"""
        print("🚨 千川自动化项目 - 紧急上传修复工具")
        print("📌 目标: 解决数据库连接池耗尽导致的上传失败问题")
        print("="*70)
        
        # 1. 检查当前问题
        issues = self.check_current_issues()
        
        if issues:
            print(f"\n❌ 发现 {len(issues)} 个问题:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
        else:
            print("\n✅ 未发现明显问题")
        
        print()
        
        # 2. 应用紧急修复
        self.apply_emergency_fixes()
        print()
        
        # 3. 强制释放连接
        self.force_release_connections()
        print()
        
        # 4. 最终检查
        print("🔍 最终状态检查...")
        final_issues = self.check_current_issues()
        
        if len(final_issues) < len(issues):
            print("✅ 部分问题已修复")
        
        # 5. 生成重启指令
        self.generate_restart_instructions()
        
        # 6. 总结
        print("\n" + "="*70)
        print("📊 紧急修复总结:")
        
        if self.fixes_applied:
            print("✅ 已应用的修复:")
            for fix in self.fixes_applied:
                print(f"  - {fix}")
        else:
            print("ℹ️ 未发现需要修复的问题")
        
        if final_issues:
            print(f"\n⚠️ 仍存在 {len(final_issues)} 个问题:")
            for issue in final_issues:
                print(f"  - {issue}")
            print("\n💡 建议: 重启服务后再次检查")
        else:
            print("\n🎉 所有检查项目都正常！")
        
        print("\n📋 下一步:")
        print("1. 重启服务以确保所有修复生效")
        print("2. 使用监控工具验证修复效果")
        print("3. 逐步恢复上传任务，观察系统状态")

def main():
    """主函数"""
    fixer = EmergencyUploadFixer()
    
    print("⚠️ 这是一个紧急修复工具，将对系统配置进行修改")
    print("请确保您有足够的权限，并已备份重要配置")
    print()
    
    try:
        response = input("是否继续执行紧急修复? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return
    
    fixer.run_emergency_fix()

if __name__ == "__main__":
    main()
