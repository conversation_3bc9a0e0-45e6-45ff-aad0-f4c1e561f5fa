#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 千川自动化工作流紧急修复
清理条件: 工作流修复完成后删除

千川自动化工作流紧急修复工具
==========================

修复目标：
1. 申诉模块启动问题
2. 收割机制失效问题  
3. 状态定义不一致问题
4. 状态流转逻辑断裂问题
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative
from qianchuan_aw.utils.config_manager import get_settings
from contextlib import contextmanager

@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class EmergencyWorkflowRepair:
    """紧急工作流修复器"""
    
    def __init__(self):
        self.repair_start_time = datetime.now()
        self.issues_found = []
        self.fixes_applied = []
        self.app_settings = get_settings()
    
    def diagnose_appeal_module_issue(self, db):
        """诊断申诉模块问题"""
        logger.info("🔍 诊断申诉模块问题...")
        
        # 查找状态为AUDITING但没有申诉状态的计划
        auditing_campaigns = db.query(Campaign).filter(
            Campaign.status == 'AUDITING',
            Campaign.appeal_status.is_(None)
        ).all()
        
        if auditing_campaigns:
            issue = {
                'type': 'appeal_module_not_triggered',
                'description': f'发现{len(auditing_campaigns)}个AUDITING状态的计划未触发申诉',
                'affected_campaigns': [c.campaign_id_qc for c in auditing_campaigns],
                'severity': 'HIGH'
            }
            self.issues_found.append(issue)
            logger.error(f"🚨 发现申诉模块问题: {len(auditing_campaigns)}个计划未触发申诉")
            
            return auditing_campaigns
        else:
            logger.success("✅ 申诉模块状态正常")
            return []
    
    def fix_appeal_module_issue(self, db, auditing_campaigns):
        """修复申诉模块问题 - 已修复：不再错误设置appeal_pending状态"""
        if not auditing_campaigns:
            return

        logger.info("🔧 修复申诉模块问题...")
        logger.warning("⚠️ 此方法已被禁用，因为会导致状态不一致问题")
        logger.warning("⚠️ 原逻辑错误：设置appeal_pending但不设置first_appeal_at")
        logger.info("✅ 建议：让这些计划保持NULL状态，由提审系统自动处理")

        # 不再执行任何状态修改，避免状态不一致
        return
                
                fixed_count += 1
                logger.info(f"✅ 修复计划 {campaign.campaign_id_qc}: 设置申诉状态为appeal_pending")
                
            except Exception as e:
                logger.error(f"❌ 修复计划 {campaign.campaign_id_qc} 失败: {e}")
        
        if fixed_count > 0:
            db.commit()
            fix_record = {
                'type': 'appeal_status_fix',
                'description': f'修复了{fixed_count}个计划的申诉状态',
                'fixed_campaigns': fixed_count,
                'timestamp': datetime.now()
            }
            self.fixes_applied.append(fix_record)
            logger.success(f"🎉 申诉模块修复完成: {fixed_count}个计划")
    
    def diagnose_harvest_issue(self, db):
        """诊断收割机制问题"""
        logger.info("🔍 诊断收割机制问题...")
        
        # 查找状态为approved但未收割的素材
        approved_materials = db.query(LocalCreative).filter(
            LocalCreative.status == MaterialStatus.APPROVED.value
        ).all()
        
        if approved_materials:
            issue = {
                'type': 'harvest_mechanism_failed',
                'description': f'发现{len(approved_materials)}个approved状态素材未被收割',
                'affected_materials': [m.filename for m in approved_materials],
                'severity': 'HIGH'
            }
            self.issues_found.append(issue)
            logger.error(f"🚨 发现收割机制问题: {len(approved_materials)}个素材未收割")
            
            return approved_materials
        else:
            logger.success("✅ 收割机制状态正常")
            return []
    
    def fix_harvest_issue(self, db, approved_materials):
        """修复收割机制问题"""
        if not approved_materials:
            return
        
        logger.info("🔧 修复收割机制问题...")
        
        fixed_count = 0
        for material in approved_materials:
            try:
                # 手动执行收割逻辑
                logger.info(f"🔄 手动收割素材: {material.filename}")
                
                # 检查是否已经在弹药库中
                if material.status == MaterialStatus.APPROVED.value:
                    # 更新状态为已收割
                    material.status = MaterialStatus.HARVESTED.value
                    material.updated_at = datetime.now(timezone.utc)
                    
                    fixed_count += 1
                    logger.success(f"✅ 收割素材 {material.filename}: approved → harvested")
                
            except Exception as e:
                logger.error(f"❌ 收割素材 {material.filename} 失败: {e}")
        
        if fixed_count > 0:
            db.commit()
            fix_record = {
                'type': 'harvest_status_fix',
                'description': f'手动收割了{fixed_count}个approved状态素材',
                'fixed_materials': fixed_count,
                'timestamp': datetime.now()
            }
            self.fixes_applied.append(fix_record)
            logger.success(f"🎉 收割机制修复完成: {fixed_count}个素材")
    
    def diagnose_status_consistency(self, db):
        """诊断状态一致性问题"""
        logger.info("🔍 诊断状态定义一致性...")
        
        # 检查数据库中实际使用的状态
        from sqlalchemy import text
        result = db.execute(text("""
            SELECT status, COUNT(*) as count 
            FROM local_creatives 
            GROUP BY status 
            ORDER BY count DESC
        """))
        
        db_statuses = {row[0]: row[1] for row in result.fetchall()}
        
        # 检查代码中定义的状态
        from qianchuan_aw.utils.workflow_status import WorkflowStatus
        code_statuses = {status.value for status in WorkflowStatus}
        
        # 找出不一致的状态
        db_only = set(db_statuses.keys()) - code_statuses
        code_only = code_statuses - set(db_statuses.keys())
        
        if db_only or code_only:
            issue = {
                'type': 'status_definition_inconsistency',
                'description': '状态定义不一致',
                'db_only_statuses': list(db_only),
                'code_only_statuses': list(code_only),
                'severity': 'MEDIUM'
            }
            self.issues_found.append(issue)
            logger.warning(f"⚠️ 状态定义不一致:")
            if db_only:
                logger.warning(f"   数据库独有状态: {db_only}")
            if code_only:
                logger.warning(f"   代码独有状态: {code_only}")
        else:
            logger.success("✅ 状态定义一致性正常")
        
        return db_statuses, code_statuses
    
    def check_celery_tasks(self):
        """检查Celery任务状态"""
        logger.info("🔍 检查Celery任务调度状态...")
        
        try:
            # 检查Celery配置
            from qianchuan_aw.celery_app import app
            
            # 检查Beat调度配置
            beat_schedule = app.conf.beat_schedule
            logger.info(f"📋 Beat调度配置: {len(beat_schedule)} 个任务")
            
            critical_tasks = ['tasks.create_plans', 'tasks.appeal_plans']
            missing_tasks = []
            
            for task_name in critical_tasks:
                if task_name not in beat_schedule:
                    missing_tasks.append(task_name)
                    logger.error(f"❌ 关键任务未配置: {task_name}")
                else:
                    logger.success(f"✅ 关键任务已配置: {task_name}")
            
            if missing_tasks:
                issue = {
                    'type': 'celery_task_missing',
                    'description': f'缺少关键Celery任务配置',
                    'missing_tasks': missing_tasks,
                    'severity': 'HIGH'
                }
                self.issues_found.append(issue)
            
            return len(missing_tasks) == 0
            
        except Exception as e:
            logger.error(f"❌ 检查Celery任务失败: {e}")
            return False
    
    def run_emergency_repair(self):
        """运行紧急修复"""
        logger.info("🚨 开始千川自动化工作流紧急修复...")
        
        with database_session() as db:
            # 1. 诊断申诉模块问题
            auditing_campaigns = self.diagnose_appeal_module_issue(db)
            
            # 2. 诊断收割机制问题
            approved_materials = self.diagnose_harvest_issue(db)
            
            # 3. 诊断状态一致性问题
            db_statuses, code_statuses = self.diagnose_status_consistency(db)
            
            # 4. 检查Celery任务
            celery_ok = self.check_celery_tasks()
            
            # 执行修复
            logger.info("🔧 开始执行修复...")
            
            # 修复申诉模块
            self.fix_appeal_module_issue(db, auditing_campaigns)
            
            # 修复收割机制
            self.fix_harvest_issue(db, approved_materials)
        
        # 生成修复报告
        self.generate_repair_report()
        
        return len(self.issues_found), len(self.fixes_applied)
    
    def generate_repair_report(self):
        """生成修复报告"""
        repair_duration = (datetime.now() - self.repair_start_time).total_seconds()
        
        logger.info("=" * 60)
        logger.info("🛠️ 千川自动化工作流紧急修复报告")
        logger.info("=" * 60)
        logger.info(f"修复时间: {self.repair_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"修复耗时: {repair_duration:.2f} 秒")
        logger.info(f"发现问题: {len(self.issues_found)} 个")
        logger.info(f"应用修复: {len(self.fixes_applied)} 个")
        
        if self.issues_found:
            logger.info("🚨 发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"   {i}. {issue['description']} (严重程度: {issue['severity']})")
        
        if self.fixes_applied:
            logger.info("🔧 应用的修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                logger.info(f"   {i}. {fix['description']}")
        
        if len(self.issues_found) == len(self.fixes_applied):
            logger.success("🎉 所有问题已修复！")
        else:
            logger.warning(f"⚠️ 仍有 {len(self.issues_found) - len(self.fixes_applied)} 个问题需要手动处理")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    repair_tool = EmergencyWorkflowRepair()
    
    print("🚨 千川自动化工作流紧急修复工具")
    print("=" * 50)
    print("修复目标:")
    print("1. 申诉模块启动问题")
    print("2. 收割机制失效问题")
    print("3. 状态定义不一致问题")
    print("4. Celery任务调度问题")
    print("=" * 50)
    
    # 运行紧急修复
    issues_count, fixes_count = repair_tool.run_emergency_repair()
    
    return 0 if issues_count == fixes_count else 1


if __name__ == '__main__':
    exit(main())
