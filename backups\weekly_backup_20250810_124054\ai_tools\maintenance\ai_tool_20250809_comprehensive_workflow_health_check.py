#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 全面工作流健康检查
清理条件: 成为系统健康检查工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class ComprehensiveWorkflowHealthChecker:
    """全面工作流健康检查器"""
    
    def __init__(self):
        self.issues = {
            'critical': [],
            'high': [],
            'medium': [],
            'low': []
        }
    
    def run_comprehensive_check(self):
        """运行全面健康检查"""
        logger.info("🏥 千川自动化系统全面健康检查")
        logger.info("="*100)
        
        # 1. 调度器核心逻辑检查
        self._check_scheduler_core_logic()
        
        # 2. Celery任务执行检查
        self._check_celery_task_execution()
        
        # 3. 数据库操作安全性检查
        self._check_database_operations()
        
        # 4. API集成稳定性检查
        self._check_api_integration()
        
        # 5. 配置和状态管理检查
        self._check_configuration_management()
        
        # 6. 生成健康检查报告
        self._generate_health_report()
    
    def _check_scheduler_core_logic(self):
        """检查调度器核心逻辑"""
        logger.info("\n🔍 第一部分：调度器核心逻辑检查")
        logger.info("-" * 80)
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import AdAccount, Principal, LocalCreative, PlatformCreative
            
            with SessionLocal() as db:
                # 1.1 检查handle_video_upload的账户选择
                logger.info("📊 1.1 检查视频上传的账户选择逻辑...")
                
                test_accounts = db.query(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.account_type == 'TEST',
                    AdAccount.status == 'active'
                ).all()
                
                if len(test_accounts) < 2:
                    self.issues['critical'].append({
                        'component': 'handle_video_upload',
                        'issue': f'可用TEST账户不足 ({len(test_accounts)}个)',
                        'impact': '无法实现负载均衡，可能导致单点故障',
                        'fix': '检查账户状态，解封或添加新的TEST账户'
                    })
                elif len(test_accounts) < 3:
                    self.issues['high'].append({
                        'component': 'handle_video_upload',
                        'issue': f'TEST账户数量有限 ({len(test_accounts)}个)',
                        'impact': '负载均衡效果有限',
                        'fix': '考虑添加更多TEST账户'
                    })
                else:
                    logger.success(f"✅ TEST账户数量充足: {len(test_accounts)} 个")
                
                # 1.2 检查handle_plan_creation的素材分组
                logger.info("📊 1.2 检查计划创建的素材分组逻辑...")
                
                uploaded_pending_materials = db.query(LocalCreative).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value
                ).count()
                
                if uploaded_pending_materials == 0:
                    self.issues['medium'].append({
                        'component': 'handle_plan_creation',
                        'issue': '没有uploaded_pending_plan状态的素材',
                        'impact': '计划创建工作流空闲',
                        'fix': '检查上传流程是否正常工作'
                    })
                else:
                    logger.success(f"✅ 有 {uploaded_pending_materials} 个素材等待创建计划")
                
                # 1.3 检查账户健康检查机制
                logger.info("📊 1.3 检查账户健康检查机制...")

                blocked_accounts = db.query(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.account_type == 'TEST',
                    AdAccount.status == 'temporarily_blocked'
                ).all()

                for account in blocked_accounts:
                    # 检查是否应该自动解封
                    from datetime import datetime, timezone
                    if account.blocked_until and datetime.now(timezone.utc) > account.blocked_until.astimezone(timezone.utc):
                        self.issues['high'].append({
                            'component': 'is_account_healthy',
                            'issue': f'账户 {account.name} 封禁已过期但未自动解封',
                            'impact': '可用账户减少，影响负载均衡',
                            'fix': '手动解封或检查自动解封逻辑'
                        })

                # 1.4 检查状态转换链路
                logger.info("📊 1.4 检查状态转换链路...")

                # 检查各状态的素材数量分布
                status_distribution = db.query(
                    LocalCreative.status,
                    db.func.count(LocalCreative.id).label('count')
                ).join(Principal).filter(
                    Principal.name == '缇萃百货'
                ).group_by(LocalCreative.status).all()

                status_dict = dict(status_distribution)

                # 检查是否有状态堆积
                if status_dict.get(MaterialStatus.PENDING_UPLOAD.value, 0) > 50:
                    self.issues['high'].append({
                        'component': 'status_transition',
                        'issue': f'pending_upload状态堆积 ({status_dict[MaterialStatus.PENDING_UPLOAD.value]} 个)',
                        'impact': '上传流程可能存在瓶颈',
                        'fix': '检查上传任务执行情况和错误日志'
                    })

                if status_dict.get(MaterialStatus.UPLOADED_PENDING_PLAN.value, 0) > 100:
                    self.issues['medium'].append({
                        'component': 'status_transition',
                        'issue': f'uploaded_pending_plan状态堆积 ({status_dict[MaterialStatus.UPLOADED_PENDING_PLAN.value]} 个)',
                        'impact': '计划创建流程可能存在瓶颈',
                        'fix': '检查计划创建任务执行情况'
                    })

                if status_dict.get(MaterialStatus.TESTING_PENDING_REVIEW.value, 0) > 200:
                    self.issues['medium'].append({
                        'component': 'status_transition',
                        'issue': f'testing_pending_review状态堆积 ({status_dict[MaterialStatus.TESTING_PENDING_REVIEW.value]} 个)',
                        'impact': '审核流程可能存在瓶颈',
                        'fix': '检查审核任务执行情况'
                    })

                logger.success("✅ 调度器核心逻辑检查完成")
                
        except Exception as e:
            self.issues['critical'].append({
                'component': 'scheduler_core',
                'issue': f'调度器核心逻辑检查失败: {e}',
                'impact': '无法评估调度器健康状态',
                'fix': '检查数据库连接和模块导入'
            })
    
    def _check_celery_task_execution(self):
        """检查Celery任务执行"""
        logger.info("\n🔍 第二部分：Celery任务执行检查")
        logger.info("-" * 80)
        
        try:
            # 2.1 检查任务定义和配置
            logger.info("📊 2.1 检查Celery任务定义...")
            
            from qianchuan_aw.workflows import tasks
            
            # 检查关键任务是否存在
            critical_tasks = [
                'batch_upload_videos',
                'batch_create_plans', 
                'batch_submit_plans',
                'batch_review_plans'
            ]
            
            for task_name in critical_tasks:
                if hasattr(tasks, task_name):
                    logger.success(f"✅ 任务 {task_name} 已定义")
                else:
                    self.issues['critical'].append({
                        'component': 'celery_tasks',
                        'issue': f'关键任务 {task_name} 未定义',
                        'impact': '工作流无法正常执行',
                        'fix': f'检查tasks.py中的{task_name}任务定义'
                    })
            
            # 2.2 检查任务重试机制
            logger.info("📊 2.2 检查任务重试机制...")
            
            # 这里可以添加更多的任务配置检查
            logger.success("✅ Celery任务执行检查完成")
            
        except Exception as e:
            self.issues['critical'].append({
                'component': 'celery_execution',
                'issue': f'Celery任务检查失败: {e}',
                'impact': '无法评估任务执行健康状态',
                'fix': '检查Celery配置和任务模块'
            })
    
    def _check_database_operations(self):
        """检查数据库操作安全性"""
        logger.info("\n🔍 第三部分：数据库操作安全性检查")
        logger.info("-" * 80)
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, Principal
            
            # 3.1 检查事务管理
            logger.info("📊 3.1 检查数据库事务管理...")
            
            with SessionLocal() as db:
                # 检查是否有长时间未提交的事务
                # 这里可以添加具体的事务检查逻辑
                logger.success("✅ 数据库连接正常")
                
                # 3.2 检查数据一致性
                logger.info("📊 3.2 检查数据一致性...")
                
                # 检查LocalCreative和PlatformCreative的一致性
                orphaned_pc = db.execute("""
                    SELECT COUNT(*) as count
                    FROM platform_creatives pc
                    LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE lc.id IS NULL
                """).fetchone()
                
                if orphaned_pc.count > 0:
                    self.issues['medium'].append({
                        'component': 'database_consistency',
                        'issue': f'发现 {orphaned_pc.count} 个孤立的PlatformCreative记录',
                        'impact': '数据不一致，可能影响计划创建',
                        'fix': '清理孤立记录或修复关联关系'
                    })
                
                logger.success("✅ 数据库操作安全性检查完成")
                
        except Exception as e:
            self.issues['critical'].append({
                'component': 'database_operations',
                'issue': f'数据库操作检查失败: {e}',
                'impact': '无法评估数据库健康状态',
                'fix': '检查数据库连接和权限'
            })
    
    def _check_api_integration(self):
        """检查API集成稳定性"""
        logger.info("\n🔍 第四部分：API集成稳定性检查")
        logger.info("-" * 80)
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            from qianchuan_aw.sdk_qc.client import QianchuanClient
            
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            
            # 4.1 检查API客户端配置
            logger.info("📊 4.1 检查API客户端配置...")
            
            client = QianchuanClient(app_settings)
            
            # 检查重试配置
            retry_config = app_settings.get('api', {}).get('retry', {})
            if not retry_config:
                self.issues['medium'].append({
                    'component': 'api_integration',
                    'issue': 'API重试配置缺失',
                    'impact': 'API调用失败时缺乏重试机制',
                    'fix': '在config/settings.yml中添加API重试配置'
                })
            
            # 4.2 检查熔断机制
            logger.info("📊 4.2 检查API熔断机制...")
            
            # 这里可以添加熔断器状态检查
            logger.success("✅ API集成稳定性检查完成")
            
        except Exception as e:
            self.issues['high'].append({
                'component': 'api_integration',
                'issue': f'API集成检查失败: {e}',
                'impact': 'API调用可能不稳定',
                'fix': '检查API配置和网络连接'
            })
    
    def _check_configuration_management(self):
        """检查配置和状态管理"""
        logger.info("\n🔍 第五部分：配置和状态管理检查")
        logger.info("-" * 80)
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            # 5.1 检查配置文件加载
            logger.info("📊 5.1 检查配置文件加载...")
            
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            
            # 检查关键配置项
            critical_configs = [
                'plan_creation_defaults.test_workflow.creative_count',
                'workflow.account_selection_strategy',
                'api.base_url'
            ]
            
            for config_path in critical_configs:
                keys = config_path.split('.')
                value = app_settings
                
                try:
                    for key in keys:
                        value = value[key]
                    logger.success(f"✅ 配置项 {config_path}: {value}")
                except KeyError:
                    self.issues['high'].append({
                        'component': 'configuration',
                        'issue': f'关键配置项 {config_path} 缺失',
                        'impact': '工作流可能使用默认值，行为不可预期',
                        'fix': f'在config/settings.yml中添加 {config_path} 配置'
                    })
            
            logger.success("✅ 配置和状态管理检查完成")
            
        except Exception as e:
            self.issues['critical'].append({
                'component': 'configuration_management',
                'issue': f'配置管理检查失败: {e}',
                'impact': '无法评估配置健康状态',
                'fix': '检查配置文件和配置管理器'
            })
    
    def _generate_health_report(self):
        """生成健康检查报告"""
        logger.info("\n📋 千川自动化系统健康检查报告")
        logger.info("="*100)
        
        # 统计问题数量
        total_issues = sum(len(issues) for issues in self.issues.values())
        
        logger.info(f"📊 问题统计:")
        logger.info(f"   🚨 Critical: {len(self.issues['critical'])} 个")
        logger.info(f"   🔴 High: {len(self.issues['high'])} 个")
        logger.info(f"   🟡 Medium: {len(self.issues['medium'])} 个")
        logger.info(f"   🟢 Low: {len(self.issues['low'])} 个")
        logger.info(f"   📈 总计: {total_issues} 个问题")
        
        # 详细问题列表
        for severity, issues in self.issues.items():
            if issues:
                severity_icon = {'critical': '🚨', 'high': '🔴', 'medium': '🟡', 'low': '🟢'}[severity]
                logger.info(f"\n{severity_icon} {severity.upper()} 级别问题:")
                
                for i, issue in enumerate(issues, 1):
                    logger.info(f"   {i}. 组件: {issue['component']}")
                    logger.info(f"      问题: {issue['issue']}")
                    logger.info(f"      影响: {issue['impact']}")
                    logger.info(f"      修复: {issue['fix']}")
                    logger.info("")
        
        # 健康评分
        health_score = max(0, 100 - (
            len(self.issues['critical']) * 25 +
            len(self.issues['high']) * 10 +
            len(self.issues['medium']) * 5 +
            len(self.issues['low']) * 1
        ))
        
        logger.info(f"🎯 系统健康评分: {health_score}/100")
        
        if health_score >= 90:
            logger.success("🎊 系统健康状态优秀")
        elif health_score >= 70:
            logger.warning("⚠️ 系统健康状态良好，有改进空间")
        elif health_score >= 50:
            logger.warning("⚠️ 系统健康状态一般，需要关注")
        else:
            logger.error("❌ 系统健康状态较差，需要立即修复")
        
        # 修复优先级建议
        logger.info(f"\n🚀 修复优先级建议:")
        
        if self.issues['critical']:
            logger.error("🚨 立即修复Critical级别问题，系统可能无法正常运行")
        
        if self.issues['high']:
            logger.warning("🔴 优先修复High级别问题，影响系统稳定性")
        
        if self.issues['medium']:
            logger.info("🟡 计划修复Medium级别问题，优化系统性能")
        
        if self.issues['low']:
            logger.info("🟢 有时间时修复Low级别问题，提升用户体验")
        
        return health_score


def main():
    """主函数"""
    checker = ComprehensiveWorkflowHealthChecker()
    
    logger.info("🚀 启动千川自动化系统全面健康检查")
    logger.info("🎯 目标：识别系统问题，提供修复建议")
    
    health_score = checker.run_comprehensive_check()
    
    if health_score >= 70:
        logger.success("🎊 系统健康检查完成，整体状态良好")
        return 0
    elif health_score >= 50:
        logger.warning("⚠️ 系统健康检查完成，需要关注部分问题")
        return 1
    else:
        logger.error("❌ 系统健康检查完成，发现严重问题")
        return 2


if __name__ == "__main__":
    exit(main())
