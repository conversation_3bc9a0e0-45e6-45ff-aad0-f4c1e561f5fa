#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化系统告警机制，实现Critical问题自动告警和预警
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import sys
import json
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
from dataclasses import dataclass, asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount


@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    alert_type: str
    severity: str  # CRITICAL, WARNING, INFO
    title: str
    message: str
    affected_entities: List[str]
    detected_at: datetime
    resolved: bool = False
    resolved_at: datetime = None
    auto_resolvable: bool = False
    resolution_suggestion: str = ""


class AlertSystem:
    """告警系统"""
    
    def __init__(self):
        self.alerts = []
        self.alert_thresholds = {
            'critical_health_score': 50,
            'warning_health_score': 70,
            'stuck_creatives_threshold': 10,
            'workflow_backlog_threshold': 50,
            'compliance_violations_threshold': 5,
            'deleted_account_activity_threshold': 1
        }
        
        # 告警配置
        self.alert_config = {
            'email_enabled': False,  # 邮件告警暂时禁用
            'log_enabled': True,     # 日志告警启用
            'file_enabled': True,    # 文件告警启用
            'console_enabled': True  # 控制台告警启用
        }
    
    def check_and_generate_alerts(self) -> List[Alert]:
        """检查系统状态并生成告警"""
        logger.info("🚨 开始检查系统状态并生成告警...")
        
        self.alerts = []
        
        try:
            with database_session() as db:
                # 检查1: 工作流堆积告警
                self._check_workflow_backlog_alerts(db)
                
                # 检查2: 账户状态异常告警
                self._check_account_status_alerts(db)
                
                # 检查3: 业务合规性告警
                self._check_compliance_alerts(db)
                
                # 检查4: 系统健康告警
                self._check_system_health_alerts()
                
                # 发送告警
                self._send_alerts()
                
                # 保存告警记录
                self._save_alert_history()
                
                logger.info(f"✅ 告警检查完成: 生成{len(self.alerts)}个告警")
                
                return self.alerts
                
        except Exception as e:
            logger.error(f"❌ 告警检查失败: {e}")
            return []
    
    def _check_workflow_backlog_alerts(self, db):
        """检查工作流堆积告警"""
        logger.debug("检查工作流堆积告警")
        
        try:
            # 检查长期未处理的素材
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=72)
            
            stuck_creatives = db.query(LocalCreative).filter(
                LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value]),
                LocalCreative.updated_at < cutoff_time
            ).count()
            
            if stuck_creatives >= self.alert_thresholds['stuck_creatives_threshold']:
                alert = Alert(
                    alert_id=f"workflow_backlog_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    alert_type='WORKFLOW_BACKLOG',
                    severity='CRITICAL' if stuck_creatives > 20 else 'WARNING',
                    title='工作流堆积告警',
                    message=f'发现{stuck_creatives}个素材长期未处理（超过72小时）',
                    affected_entities=[f'{stuck_creatives}个素材'],
                    detected_at=datetime.now(timezone.utc),
                    auto_resolvable=True,
                    resolution_suggestion='运行工作流堆积处理器清理卡住的素材'
                )
                self.alerts.append(alert)
            
            # 检查总体工作流堆积
            total_pending = db.query(LocalCreative).filter(
                LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.PROCESSING.value])
            ).count()
            
            if total_pending >= self.alert_thresholds['workflow_backlog_threshold']:
                alert = Alert(
                    alert_id=f"workflow_total_backlog_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    alert_type='WORKFLOW_TOTAL_BACKLOG',
                    severity='WARNING',
                    title='工作流总体堆积告警',
                    message=f'工作流中待处理素材总数过多: {total_pending}个',
                    affected_entities=[f'{total_pending}个待处理素材'],
                    detected_at=datetime.now(timezone.utc),
                    auto_resolvable=False,
                    resolution_suggestion='检查Celery任务队列状态，确保工作流正常运行'
                )
                self.alerts.append(alert)
                
        except Exception as e:
            logger.error(f"检查工作流堆积告警失败: {e}")
    
    def _check_account_status_alerts(self, db):
        """检查账户状态异常告警"""
        logger.debug("检查账户状态异常告警")
        
        try:
            # 检查deleted账户的活动
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=7)
            
            deleted_accounts_with_activity = db.query(AdAccount).join(
                Campaign, AdAccount.id == Campaign.account_id
            ).filter(
                AdAccount.status == 'deleted',
                Campaign.updated_at > cutoff_time
            ).distinct().count()
            
            if deleted_accounts_with_activity >= self.alert_thresholds['deleted_account_activity_threshold']:
                alert = Alert(
                    alert_id=f"deleted_account_activity_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    alert_type='DELETED_ACCOUNT_ACTIVITY',
                    severity='CRITICAL',
                    title='Deleted账户异常活动告警',
                    message=f'发现{deleted_accounts_with_activity}个deleted状态账户存在近期活动',
                    affected_entities=[f'{deleted_accounts_with_activity}个deleted账户'],
                    detected_at=datetime.now(timezone.utc),
                    auto_resolvable=True,
                    resolution_suggestion='停止deleted账户的所有操作或恢复账户状态'
                )
                self.alerts.append(alert)
            
            # 检查账户类型异常
            invalid_type_accounts = db.query(AdAccount).filter(
                ~AdAccount.account_type.in_(['TEST', 'DELIVERY', 'UNSET'])
            ).count()
            
            if invalid_type_accounts > 0:
                alert = Alert(
                    alert_id=f"invalid_account_type_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    alert_type='INVALID_ACCOUNT_TYPE',
                    severity='WARNING',
                    title='账户类型异常告警',
                    message=f'发现{invalid_type_accounts}个账户类型无效或为空',
                    affected_entities=[f'{invalid_type_accounts}个账户'],
                    detected_at=datetime.now(timezone.utc),
                    auto_resolvable=True,
                    resolution_suggestion='设置正确的账户类型: TEST, DELIVERY, 或 UNSET'
                )
                self.alerts.append(alert)
                
        except Exception as e:
            logger.error(f"检查账户状态异常告警失败: {e}")
    
    def _check_compliance_alerts(self, db):
        """检查业务合规性告警"""
        logger.debug("检查业务合规性告警")
        
        try:
            # 检查重复提审
            duplicate_appeals = db.query(Campaign).filter(
                Campaign.appeal_status == 'appeal_pending',
                Campaign.first_appeal_at.isnot(None),
                Campaign.last_appeal_at.isnot(None),
                Campaign.first_appeal_at != Campaign.last_appeal_at
            ).count()
            
            if duplicate_appeals >= self.alert_thresholds['compliance_violations_threshold']:
                alert = Alert(
                    alert_id=f"duplicate_appeals_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    alert_type='DUPLICATE_APPEALS',
                    severity='CRITICAL',
                    title='重复提审违规告警',
                    message=f'发现{duplicate_appeals}个计划可能存在重复提审',
                    affected_entities=[f'{duplicate_appeals}个计划'],
                    detected_at=datetime.now(timezone.utc),
                    auto_resolvable=True,
                    resolution_suggestion='运行重复提审清理工具'
                )
                self.alerts.append(alert)
                
        except Exception as e:
            logger.error(f"检查业务合规性告警失败: {e}")
    
    def _check_system_health_alerts(self):
        """检查系统健康告警"""
        logger.debug("检查系统健康告警")
        
        try:
            # 这里可以集成健康检查结果
            # 暂时使用简化的健康检查
            with database_session() as db:
                total_creatives = db.query(LocalCreative).count()
                stuck_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value]),
                    LocalCreative.updated_at < datetime.now(timezone.utc) - timedelta(hours=72)
                ).count()
                
                # 计算简化的健康分数
                if total_creatives > 0:
                    health_score = max(0, 100 - (stuck_creatives * 5))
                else:
                    health_score = 100
                
                if health_score < self.alert_thresholds['critical_health_score']:
                    alert = Alert(
                        alert_id=f"system_health_critical_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        alert_type='SYSTEM_HEALTH_CRITICAL',
                        severity='CRITICAL',
                        title='系统健康Critical告警',
                        message=f'系统健康分数过低: {health_score}/100',
                        affected_entities=['系统整体'],
                        detected_at=datetime.now(timezone.utc),
                        auto_resolvable=False,
                        resolution_suggestion='运行综合健康检查工具，分析具体问题'
                    )
                    self.alerts.append(alert)
                elif health_score < self.alert_thresholds['warning_health_score']:
                    alert = Alert(
                        alert_id=f"system_health_warning_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        alert_type='SYSTEM_HEALTH_WARNING',
                        severity='WARNING',
                        title='系统健康Warning告警',
                        message=f'系统健康分数偏低: {health_score}/100',
                        affected_entities=['系统整体'],
                        detected_at=datetime.now(timezone.utc),
                        auto_resolvable=False,
                        resolution_suggestion='关注系统状态变化，考虑运行维护工具'
                    )
                    self.alerts.append(alert)
                    
        except Exception as e:
            logger.error(f"检查系统健康告警失败: {e}")
    
    def _send_alerts(self):
        """发送告警"""
        
        for alert in self.alerts:
            try:
                # 控制台告警
                if self.alert_config['console_enabled']:
                    self._send_console_alert(alert)
                
                # 日志告警
                if self.alert_config['log_enabled']:
                    self._send_log_alert(alert)
                
                # 文件告警
                if self.alert_config['file_enabled']:
                    self._send_file_alert(alert)
                
                # 邮件告警（暂时禁用）
                if self.alert_config['email_enabled']:
                    self._send_email_alert(alert)
                    
            except Exception as e:
                logger.error(f"发送告警失败: {alert.alert_id}, 错误: {e}")
    
    def _send_console_alert(self, alert: Alert):
        """发送控制台告警"""
        severity_emoji = {
            'CRITICAL': '🚨',
            'WARNING': '⚠️',
            'INFO': 'ℹ️'
        }
        
        emoji = severity_emoji.get(alert.severity, '📢')
        print(f"\n{emoji} {alert.severity} 告警: {alert.title}")
        print(f"   消息: {alert.message}")
        print(f"   时间: {alert.detected_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   建议: {alert.resolution_suggestion}")
    
    def _send_log_alert(self, alert: Alert):
        """发送日志告警"""
        if alert.severity == 'CRITICAL':
            logger.critical(f"告警: {alert.title} - {alert.message}")
        elif alert.severity == 'WARNING':
            logger.warning(f"告警: {alert.title} - {alert.message}")
        else:
            logger.info(f"告警: {alert.title} - {alert.message}")
    
    def _send_file_alert(self, alert: Alert):
        """发送文件告警"""
        try:
            alerts_dir = project_root / 'ai_reports' / 'alerts'
            alerts_dir.mkdir(parents=True, exist_ok=True)
            
            alert_file = alerts_dir / f'alert_{alert.alert_id}.json'
            
            alert_data = asdict(alert)
            # 转换datetime为字符串
            alert_data['detected_at'] = alert.detected_at.isoformat()
            if alert.resolved_at:
                alert_data['resolved_at'] = alert.resolved_at.isoformat()
            
            with open(alert_file, 'w', encoding='utf-8') as f:
                json.dump(alert_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存文件告警失败: {e}")
    
    def _send_email_alert(self, alert: Alert):
        """发送邮件告警（暂时禁用）"""
        # 邮件告警功能预留，暂时不实现
        pass
    
    def _save_alert_history(self):
        """保存告警历史"""
        try:
            history_dir = project_root / 'ai_reports' / 'alerts'
            history_dir.mkdir(parents=True, exist_ok=True)
            
            history_file = history_dir / f'alert_history_{datetime.now().strftime("%Y%m%d")}.json'
            
            # 读取现有历史
            existing_alerts = []
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    existing_alerts = json.load(f)
            
            # 添加新告警
            for alert in self.alerts:
                alert_data = asdict(alert)
                alert_data['detected_at'] = alert.detected_at.isoformat()
                if alert.resolved_at:
                    alert_data['resolved_at'] = alert.resolved_at.isoformat()
                existing_alerts.append(alert_data)
            
            # 保存更新的历史
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(existing_alerts, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"保存告警历史失败: {e}")
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        
        try:
            # 统计最近24小时的告警
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            
            recent_alerts = [a for a in self.alerts if a.detected_at > cutoff_time]
            
            critical_alerts = [a for a in recent_alerts if a.severity == 'CRITICAL']
            warning_alerts = [a for a in recent_alerts if a.severity == 'WARNING']
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'total_alerts_24h': len(recent_alerts),
                'critical_alerts_24h': len(critical_alerts),
                'warning_alerts_24h': len(warning_alerts),
                'alert_types': list(set(a.alert_type for a in recent_alerts)),
                'auto_resolvable_count': len([a for a in recent_alerts if a.auto_resolvable]),
                'system_status': 'CRITICAL' if critical_alerts else ('WARNING' if warning_alerts else 'NORMAL')
            }
            
        except Exception as e:
            logger.error(f"获取告警摘要失败: {e}")
            return {'error': str(e)}


def main():
    """主函数"""
    alert_system = AlertSystem()
    
    import argparse
    parser = argparse.ArgumentParser(description='千川自动化系统告警机制')
    parser.add_argument('--check', action='store_true', help='检查并生成告警')
    parser.add_argument('--summary', action='store_true', help='获取告警摘要')
    
    args = parser.parse_args()
    
    if args.check:
        alerts = alert_system.check_and_generate_alerts()
        
        print(f"\n📊 告警检查结果:")
        print(f"生成告警数量: {len(alerts)}")
        
        for alert in alerts:
            severity_emoji = {'CRITICAL': '🚨', 'WARNING': '⚠️', 'INFO': 'ℹ️'}
            emoji = severity_emoji.get(alert.severity, '📢')
            print(f"{emoji} {alert.severity}: {alert.title}")
        
    elif args.summary:
        summary = alert_system.get_alert_summary()
        print(json.dumps(summary, indent=2, ensure_ascii=False, default=str))
    else:
        print("使用 --check 检查告警或 --summary 获取摘要")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
