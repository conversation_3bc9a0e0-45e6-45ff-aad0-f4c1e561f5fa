#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具: 完整版素材审核报表页面
生成时间: 2025-07-20
用途: 完全满足需求的素材审核数据分析报表
生命周期: 永久工具
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime, timedelta, time
import pytz
import io
import base64
from typing import Dict, List, Optional, Tuple
import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from sqlalchemy import text, and_, or_, func, desc, asc
from sqlalchemy.orm import joinedload

# 中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')

def render_complete_material_analytics_page():
    """完整版素材审核报表页面"""
    st.header("📊 素材审核报表 (完整版)")
    st.markdown("全面的素材审核数据分析，支持多维度分析、钻取查询和实时监控")

    # 业务范围说明
    st.info("🎯 **业务范围**: 本报表仅分析TEST账户的素材测试工作流数据，不包含DELIVERY账户（投放户）的数据。")
    
    # 页面布局
    with st.sidebar:
        st.markdown("### 📋 报表配置")
        
        # 时间范围选择器
        time_range = render_advanced_time_selector()
        
        # 分析维度选择
        analysis_mode = st.selectbox(
            "分析模式",
            ["📊 综合仪表板", "🏢 广告账户分析", "🎬 视频素材分析", "👤 作者效率分析", "🔍 深度追踪查询"],
            help="选择要分析的数据维度"
        )
        
        # 高级过滤器
        filters = render_advanced_filters()
        
        # 刷新和导出控制
        st.markdown("---")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🔄 刷新", use_container_width=True):
                st.rerun()
        with col2:
            if st.button("📥 导出", use_container_width=True):
                export_comprehensive_data(time_range, filters)
    
    # 根据选择的模式渲染对应页面
    if analysis_mode == "📊 综合仪表板":
        render_comprehensive_dashboard(time_range, filters)
    elif analysis_mode == "🏢 广告账户分析":
        render_account_analysis(time_range, filters)
    elif analysis_mode == "🎬 视频素材分析":
        render_material_analysis(time_range, filters)
    elif analysis_mode == "👤 作者效率分析":
        render_author_analysis(time_range, filters)
    elif analysis_mode == "🔍 深度追踪查询":
        render_deep_tracking(time_range, filters)

def render_advanced_time_selector() -> Dict:
    """高级时间范围选择器"""
    st.markdown("#### ⏰ 时间范围")
    
    # 快捷选项
    quick_options = {
        "今天": (
            datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0),
            datetime.now(CHINA_TZ)
        ),
        "昨天": (
            datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=1),
            datetime.now(CHINA_TZ).replace(hour=0, minute=0, second=0, microsecond=0)
        ),
        "最近7天": (
            datetime.now(CHINA_TZ) - timedelta(days=7),
            datetime.now(CHINA_TZ)
        ),
        "最近30天": (
            datetime.now(CHINA_TZ) - timedelta(days=30),
            datetime.now(CHINA_TZ)
        ),
        "本月": (
            datetime.now(CHINA_TZ).replace(day=1, hour=0, minute=0, second=0, microsecond=0),
            datetime.now(CHINA_TZ)
        ),
        "自定义": None
    }
    
    quick_select = st.selectbox("快捷选择", list(quick_options.keys()), index=2)
    
    if quick_select == "自定义":
        col1, col2 = st.columns(2)
        with col1:
            start_date = st.date_input(
                "开始日期",
                value=datetime.now(CHINA_TZ).date() - timedelta(days=7)
            )
            start_time = st.time_input("开始时间", value=time(0, 0))
        
        with col2:
            end_date = st.date_input(
                "结束日期", 
                value=datetime.now(CHINA_TZ).date()
            )
            end_time = st.time_input("结束时间", value=time(23, 59))
        
        start_datetime = CHINA_TZ.localize(datetime.combine(start_date, start_time))
        end_datetime = CHINA_TZ.localize(datetime.combine(end_date, end_time))
    else:
        start_datetime, end_datetime = quick_options[quick_select]
    
    # 显示选择的时间范围
    st.info(f"📅 {start_datetime.strftime('%Y-%m-%d %H:%M')} 至 {end_datetime.strftime('%Y-%m-%d %H:%M')}")
    
    return {
        'start': start_datetime,
        'end': end_datetime,
        'quick_select': quick_select
    }

def render_advanced_filters() -> Dict:
    """高级过滤器"""
    st.markdown("#### 🔍 高级过滤")

    filters = {}

    # 状态过滤
    with st.expander("📊 状态过滤"):
        # 素材状态中英文映射
        material_status_options = {
            MaterialStatus.NEW.value: '新建',
            MaterialStatus.PENDING_UPLOAD.value: '待上传',
            MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划',
            'creating_plan': '创建计划中',
            MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核',
            MaterialStatus.APPROVED.value: '审核通过',
            MaterialStatus.REJECTED.value: '审核拒绝',
            MaterialStatus.UPLOAD_FAILED.value: '上传失败'
        }

        selected_material_statuses = st.multiselect(
            "素材状态",
            options=list(material_status_options.keys()),
            format_func=lambda x: f"{material_status_options[x]} ({x})",
            help="选择要包含的素材状态"
        )
        filters['material_statuses'] = selected_material_statuses

        # 计划状态中英文映射
        campaign_status_options = {
            'COMPLETED': '已完成',
            'AUDITING': '审核中',
            'APPEAL_TIMEOUT': '申诉超时',
            'DELETED': '已删除'
        }

        selected_campaign_statuses = st.multiselect(
            "计划状态",
            options=list(campaign_status_options.keys()),
            format_func=lambda x: f"{campaign_status_options[x]} ({x})",
            help="选择要包含的计划状态"
        )
        filters['campaign_statuses'] = selected_campaign_statuses
    
    # 账户和作者过滤
    with st.expander("👥 账户和作者"):
        try:
            with database_session() as db:
                # 获取账户列表
                accounts = db.query(AdAccount).all()
                account_options = {f"{acc.name} ({acc.account_id_qc})": acc.id for acc in accounts}

                selected_accounts = st.multiselect(
                    "选择账户",
                    options=list(account_options.keys()),
                    help="选择要分析的广告账户"
                )
                filters['account_ids'] = [account_options[acc] for acc in selected_accounts]

                # 从文件名中提取作者列表（仅TEST账户）
                author_results = db.execute(text("""
                    SELECT DISTINCT
                        CASE
                            WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                                substring(lc.filename from '^\d+\.\d+-([^-]+)-')
                            WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                                substring(lc.filename from '-([^-]+)-\d+\.mp4')
                            ELSE 'Unknown'
                        END as author_name
                    FROM local_creatives lc
                    JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                        AND lc.filename IS NOT NULL
                        AND lc.filename != ''
                    ORDER BY author_name
                """)).fetchall()

                author_options = [row.author_name for row in author_results if row.author_name != 'Unknown']

                selected_authors = st.multiselect(
                    "选择作者",
                    options=author_options,
                    help="选择要分析的作者（从文件名提取）"
                )
                filters['author_names'] = selected_authors

        except Exception as e:
            st.error(f"获取过滤选项失败: {e}")
            filters['account_ids'] = []
            filters['author_names'] = []
    
    return filters

def render_comprehensive_dashboard(time_range: Dict, filters: Dict):
    """综合仪表板"""
    st.markdown("### 📊 综合数据仪表板")
    
    try:
        with database_session() as db:
            # 获取核心指标
            metrics = get_core_metrics(db, time_range, filters)
            
            # 关键指标卡片
            render_metric_cards(metrics)
            
            # 趋势图表
            col1, col2 = st.columns(2)
            with col1:
                render_daily_trend_chart(db, time_range, filters)
            with col2:
                render_status_distribution_chart(db, time_range, filters)
            
            # 实时状态监控
            render_realtime_status(db)
            
            # 性能指标表格
            render_performance_table(db, time_range, filters)
            
    except Exception as e:
        st.error(f"❌ 获取仪表板数据失败: {e}")

def render_account_analysis(time_range: Dict, filters: Dict):
    """广告账户维度分析"""
    st.markdown("### 🏢 广告账户维度分析")
    
    try:
        with database_session() as db:
            # 账户性能对比
            account_performance = get_account_performance_data(db, time_range, filters)
            
            if account_performance:
                # 性能对比表格
                st.markdown("#### 📊 账户性能对比")
                df = pd.DataFrame(account_performance)
                st.dataframe(df, use_container_width=True)
                
                # 可视化图表
                col1, col2 = st.columns(2)
                with col1:
                    fig = px.bar(df, x='账户名称', y='素材数量', title="各账户素材分配数量")
                    st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    fig = px.bar(df, x='账户名称', y='成功率(%)', title="各账户成功率对比")
                    st.plotly_chart(fig, use_container_width=True)
                
                # 账户钻取功能
                render_account_drilldown(db, time_range, filters)
            else:
                st.info("📭 当前筛选条件下无账户数据")
                
    except Exception as e:
        st.error(f"❌ 获取账户分析数据失败: {e}")

def render_material_analysis(time_range: Dict, filters: Dict):
    """视频素材维度分析"""
    st.markdown("### 🎬 视频素材维度分析")
    
    try:
        with database_session() as db:
            # 状态流转可视化
            render_status_flow_visualization(db, time_range, filters)

            # 素材状态分析
            render_material_status_analysis(db, time_range, filters)

            # 素材处理时间线
            render_material_timeline(db, time_range, filters)

            # 素材来源分析
            render_material_source_analysis(db, time_range, filters)
            
    except Exception as e:
        st.error(f"❌ 获取素材分析数据失败: {e}")

def render_author_analysis(time_range: Dict, filters: Dict):
    """作者效率分析"""
    st.markdown("### 👤 作者效率分析")
    
    try:
        with database_session() as db:
            # 作者产出统计
            author_stats = get_author_productivity_stats(db, time_range, filters)
            
            if author_stats:
                # 产出统计表格
                st.markdown("#### 📊 作者产出统计")
                df = pd.DataFrame(author_stats)
                st.dataframe(df, use_container_width=True)
                
                # 可视化图表
                col1, col2 = st.columns(2)
                with col1:
                    fig = px.bar(df.head(10), x='作者', y='总素材数', title="Top 10 作者产出量")
                    st.plotly_chart(fig, use_container_width=True)
                
                with col2:
                    fig = px.scatter(df, x='总素材数', y='通过率(%)', 
                                   size='通过数量', hover_name='作者',
                                   title="作者产出量 vs 质量分析")
                    st.plotly_chart(fig, use_container_width=True)
            else:
                st.info("📭 当前筛选条件下无作者数据")
                
    except Exception as e:
        st.error(f"❌ 获取作者分析数据失败: {e}")

def render_deep_tracking(time_range: Dict, filters: Dict):
    """深度追踪查询"""
    st.markdown("### 🔍 深度追踪查询")
    
    # 全局搜索
    search_term = st.text_input(
        "🔍 全局搜索",
        placeholder="输入文件名、计划ID、账户名进行搜索...",
        help="支持模糊搜索素材文件名、计划ID、账户名称"
    )
    
    if search_term:
        render_global_search_results(search_term, time_range, filters)
    
    # 专项分析
    st.markdown("#### 📋 专项分析")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("❌ 失败素材分析", use_container_width=True):
            render_failure_analysis(time_range, filters)

        if st.button("📈 素材利用率分析", use_container_width=True):
            render_material_utilization_analysis(time_range, filters)

    with col2:
        if st.button("⏰ 异常计划监控", use_container_width=True):
            render_abnormal_campaign_monitoring(time_range, filters)

        if st.button("🎯 测试计划合规检查", use_container_width=True):
            render_test_campaign_compliance_check(time_range, filters)

# 数据获取函数
def get_core_metrics(db, time_range: Dict, filters: Dict) -> Dict:
    """获取核心指标"""
    try:
        # 构建基础查询条件
        material_conditions = ["created_at >= :start_time", "created_at <= :end_time"]
        campaign_conditions = ["created_at >= :start_time", "created_at <= :end_time"]
        params = {'start_time': time_range['start'], 'end_time': time_range['end']}
        
        # 添加过滤条件
        if filters.get('material_statuses'):
            placeholders = ','.join([f"':status_{i}'" for i in range(len(filters['material_statuses']))])
            material_conditions.append(f"status IN ({placeholders})")
            for i, status in enumerate(filters['material_statuses']):
                params[f'status_{i}'] = status
        
        if filters.get('principal_ids'):
            placeholders = ','.join([f":principal_{i}" for i in range(len(filters['principal_ids']))])
            material_conditions.append(f"principal_id IN ({placeholders})")
            for i, pid in enumerate(filters['principal_ids']):
                params[f'principal_{i}'] = pid
        
        # 素材统计（仅TEST账户）
        material_query = f"""
            SELECT
                COUNT(*) as total_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.REJECTED.value THEN 1 ELSE 0 END) as rejected_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOAD_FAILED.value THEN 1 ELSE 0 END) as failed_materials
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND {' AND '.join(['lc.' + cond if not cond.startswith('lc.') else cond for cond in material_conditions])}
        """
        
        material_result = db.execute(text(material_query), params).fetchone()
        
        # 计划统计（仅TEST账户）
        campaign_query = f"""
            SELECT
                COUNT(*) as total_campaigns,
                SUM(CASE WHEN c.status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_campaigns,
                SUM(CASE WHEN c.status = 'APPEAL_TIMEOUT' THEN 1 ELSE 0 END) as timeout_campaigns
            FROM campaigns c
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND {' AND '.join(['c.' + cond if not cond.startswith('c.') else cond for cond in campaign_conditions])}
        """
        
        campaign_result = db.execute(text(campaign_query), params).fetchone()
        
        return {
            'total_materials': material_result.total_materials or 0,
            'approved_materials': material_result.approved_materials or 0,
            'rejected_materials': material_result.rejected_materials or 0,
            'failed_materials': material_result.failed_materials or 0,
            'total_campaigns': campaign_result.total_campaigns or 0,
            'completed_campaigns': campaign_result.completed_campaigns or 0,
            'timeout_campaigns': campaign_result.timeout_campaigns or 0
        }
        
    except Exception as e:
        st.error(f"获取核心指标失败: {e}")
        return {}

def render_metric_cards(metrics: Dict):
    """渲染指标卡片"""
    if not metrics:
        return
    
    st.markdown("#### 📈 核心指标")
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.metric("📁 总素材数", f"{metrics['total_materials']:,}")
    
    with col2:
        success_rate = (metrics['approved_materials'] / metrics['total_materials'] * 100) if metrics['total_materials'] > 0 else 0
        st.metric("✅ 审核通过", f"{metrics['approved_materials']:,}", f"{success_rate:.1f}%")
    
    with col3:
        rejection_rate = (metrics['rejected_materials'] / metrics['total_materials'] * 100) if metrics['total_materials'] > 0 else 0
        st.metric("❌ 审核拒绝", f"{metrics['rejected_materials']:,}", f"{rejection_rate:.1f}%")
    
    with col4:
        st.metric("🎯 总计划数", f"{metrics['total_campaigns']:,}")
    
    with col5:
        completion_rate = (metrics['completed_campaigns'] / metrics['total_campaigns'] * 100) if metrics['total_campaigns'] > 0 else 0
        st.metric("🏆 计划完成", f"{metrics['completed_campaigns']:,}", f"{completion_rate:.1f}%")

def render_daily_trend_chart(db, time_range, filters):
    """渲染每日趋势图表"""
    try:
        # 获取每日趋势数据（仅TEST账户）
        daily_data = db.execute(text("""
            SELECT
                DATE(lc.created_at) as date,
                COUNT(*) as materials,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            GROUP BY DATE(lc.created_at)
            ORDER BY DATE(lc.created_at)
        """), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        if daily_data:
            dates = [row.date.strftime('%m-%d') for row in daily_data]
            materials = [row.materials for row in daily_data]
            approved = [row.approved for row in daily_data]

            fig = go.Figure()
            fig.add_trace(go.Scatter(x=dates, y=materials, name='总素材', line=dict(color='blue')))
            fig.add_trace(go.Scatter(x=dates, y=approved, name='通过素材', line=dict(color='green')))

            fig.update_layout(title="每日素材处理趋势", xaxis_title="日期", yaxis_title="数量")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📭 当前时间范围内无趋势数据")

    except Exception as e:
        st.error(f"获取趋势数据失败: {e}")

def render_status_distribution_chart(db, time_range, filters):
    """渲染状态分布图表"""
    try:
        # 获取状态分布数据（仅TEST账户）
        status_data = db.execute(text("""
            SELECT lc.status, COUNT(*) as count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            GROUP BY lc.status
            ORDER BY count DESC
        """), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        if status_data:
            # 状态中文映射
            status_map = {
                MaterialStatus.NEW.value: '新建', MaterialStatus.PENDING_UPLOAD.value: '待上传', MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划',
                'creating_plan': '创建计划中', MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核',
                MaterialStatus.APPROVED.value: '审核通过', MaterialStatus.REJECTED.value: '审核拒绝', MaterialStatus.UPLOAD_FAILED.value: '上传失败'
            }

            labels = [status_map.get(row.status, row.status) for row in status_data]
            values = [row.count for row in status_data]

            fig = px.pie(values=values, names=labels, title="素材状态分布")
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📭 当前时间范围内无状态数据")

    except Exception as e:
        st.error(f"获取状态分布失败: {e}")

def render_realtime_status(db):
    """渲染实时状态"""
    st.markdown("#### 🔄 实时处理状态")

    try:
        # 获取实时处理数据（仅TEST账户）
        realtime_data = db.execute(text("""
            SELECT
                SUM(CASE WHEN lc.status IN (MaterialStatus.PENDING_UPLOAD.value, 'creating_plan', MaterialStatus.TESTING_PENDING_REVIEW.value) THEN 1 ELSE 0 END) as processing_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOAD_FAILED.value THEN 1 ELSE 0 END) as failed_materials,
                (SELECT COUNT(*) FROM campaigns c JOIN ad_accounts aa ON c.account_id = aa.id
                 WHERE c.status = 'AUDITING' AND aa.account_type = 'TEST') as auditing_campaigns,
                (SELECT COUNT(*) FROM campaigns c JOIN ad_accounts aa ON c.account_id = aa.id
                 WHERE c.status = 'APPEAL_TIMEOUT' AND aa.account_type = 'TEST') as timeout_campaigns,
                -- 状态停滞时长统计
                AVG(CASE WHEN lc.status = MaterialStatus.TESTING_PENDING_REVIEW.value
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600 ELSE NULL END) as avg_review_hours,
                AVG(CASE WHEN lc.status = 'creating_plan'
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/60 ELSE NULL END) as avg_creating_minutes,
                AVG(CASE WHEN lc.status = MaterialStatus.UPLOADED_PENDING_PLAN.value
                    THEN EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/60 ELSE NULL END) as avg_pending_minutes
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= NOW() - INTERVAL '24 hours'
        """)).fetchone()

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("🔄 处理中素材", realtime_data.processing_materials or 0)
        with col2:
            st.metric("❌ 失败素材", realtime_data.failed_materials or 0)
        with col3:
            st.metric("⏳ 审核中计划", realtime_data.auditing_campaigns or 0)
        with col4:
            st.metric("⏰ 超时计划", realtime_data.timeout_campaigns or 0)

        # 状态停滞时长显示
        if any([realtime_data.avg_review_hours, realtime_data.avg_creating_minutes, realtime_data.avg_pending_minutes]):
            st.markdown("#### ⏱️ 状态停滞时长统计")
            col1, col2, col3 = st.columns(3)

            with col1:
                if realtime_data.avg_review_hours:
                    st.metric("📋 平均审核等待", f"{realtime_data.avg_review_hours:.1f}小时")
                else:
                    st.metric("📋 平均审核等待", "无数据")

            with col2:
                if realtime_data.avg_creating_minutes:
                    st.metric("⚙️ 平均创建计划", f"{realtime_data.avg_creating_minutes:.1f}分钟")
                else:
                    st.metric("⚙️ 平均创建计划", "无数据")

            with col3:
                if realtime_data.avg_pending_minutes:
                    st.metric("📤 平均待建计划", f"{realtime_data.avg_pending_minutes:.1f}分钟")
                else:
                    st.metric("📤 平均待建计划", "无数据")

        # 监控预警机制
        render_monitoring_alerts(db, realtime_data)

    except Exception as e:
        st.error(f"获取实时状态失败: {e}")

def render_monitoring_alerts(db, realtime_data):
    """监控预警机制"""
    alerts = []

    try:
        # 1. 积压预警检查
        if realtime_data.processing_materials and realtime_data.processing_materials > 50:
            alerts.append({
                'level': '🔴',
                'type': '积压预警',
                'message': f'处理中素材积压严重: {realtime_data.processing_materials}个 (阈值: 50)',
                'action': '建议检查工作流调度器状态'
            })
        elif realtime_data.processing_materials and realtime_data.processing_materials > 25:
            alerts.append({
                'level': '🟡',
                'type': '积压预警',
                'message': f'处理中素材积压较多: {realtime_data.processing_materials}个 (阈值: 25)',
                'action': '建议关注工作流处理进度'
            })

        # 2. 审核超时预警
        if realtime_data.avg_review_hours and realtime_data.avg_review_hours > 24:
            alerts.append({
                'level': '🔴',
                'type': '审核超时',
                'message': f'平均审核等待时间过长: {realtime_data.avg_review_hours:.1f}小时 (阈值: 24小时)',
                'action': '建议检查千川平台审核状态'
            })
        elif realtime_data.avg_review_hours and realtime_data.avg_review_hours > 12:
            alerts.append({
                'level': '🟡',
                'type': '审核延迟',
                'message': f'平均审核等待时间较长: {realtime_data.avg_review_hours:.1f}小时 (阈值: 12小时)',
                'action': '建议关注审核进度'
            })

        # 3. 创建计划超时预警
        if realtime_data.avg_creating_minutes and realtime_data.avg_creating_minutes > 30:
            alerts.append({
                'level': '🟡',
                'type': '创建延迟',
                'message': f'平均创建计划时间过长: {realtime_data.avg_creating_minutes:.1f}分钟 (阈值: 30分钟)',
                'action': '建议检查计划创建API状态'
            })

        # 4. 失败素材预警
        if realtime_data.failed_materials and realtime_data.failed_materials > 10:
            alerts.append({
                'level': '🔴',
                'type': '失败预警',
                'message': f'失败素材过多: {realtime_data.failed_materials}个 (阈值: 10)',
                'action': '建议检查上传失败原因'
            })
        elif realtime_data.failed_materials and realtime_data.failed_materials > 5:
            alerts.append({
                'level': '🟡',
                'type': '失败预警',
                'message': f'失败素材较多: {realtime_data.failed_materials}个 (阈值: 5)',
                'action': '建议关注失败素材处理'
            })

        # 5. 超时计划预警
        if realtime_data.timeout_campaigns and realtime_data.timeout_campaigns > 20:
            alerts.append({
                'level': '🔴',
                'type': '超时预警',
                'message': f'超时计划过多: {realtime_data.timeout_campaigns}个 (阈值: 20)',
                'action': '建议检查申诉系统状态'
            })

        # 6. 查询长期停滞的素材
        stale_query = text("""
            SELECT
                lc.status,
                COUNT(*) as count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.status IN ('creating_plan', MaterialStatus.UPLOADED_PENDING_PLAN.value)
                AND EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600 > 2
            GROUP BY lc.status
        """)

        stale_results = db.execute(stale_query).fetchall()

        for row in stale_results:
            if row.status == 'creating_plan' and row.count > 5:
                alerts.append({
                    'level': '🟡',
                    'type': '状态停滞',
                    'message': f'{row.count}个素材在创建计划状态停滞超过2小时',
                    'action': '建议检查计划创建工作流'
                })
            elif row.status == MaterialStatus.UPLOADED_PENDING_PLAN.value and row.count > 10:
                alerts.append({
                    'level': '🟡',
                    'type': '状态停滞',
                    'message': f'{row.count}个素材在待建计划状态停滞超过2小时',
                    'action': '建议检查调度器配置'
                })

        # 显示预警信息
        if alerts:
            st.markdown("#### 🚨 系统预警")

            # 按严重程度分组显示
            critical_alerts = [a for a in alerts if a['level'] == '🔴']
            warning_alerts = [a for a in alerts if a['level'] == '🟡']

            if critical_alerts:
                st.error("🔴 **严重预警**")
                for alert in critical_alerts:
                    st.error(f"**{alert['type']}**: {alert['message']}")
                    st.info(f"💡 建议措施: {alert['action']}")

            if warning_alerts:
                st.warning("🟡 **注意预警**")
                for alert in warning_alerts:
                    st.warning(f"**{alert['type']}**: {alert['message']}")
                    st.info(f"💡 建议措施: {alert['action']}")
        else:
            st.success("✅ 系统运行正常，无预警信息")

    except Exception as e:
        st.error(f"预警检查失败: {e}")

def get_account_performance_data(db, time_range, filters):
    """获取账户性能数据"""
    try:
        # 构建查询
        query = """
            SELECT
                a.name as account_name,
                a.account_id_qc,
                COUNT(DISTINCT lc.id) as material_count,
                COUNT(DISTINCT c.id) as campaign_count,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOAD_FAILED.value THEN 1 ELSE 0 END) as failed_count
            FROM ad_accounts a
            LEFT JOIN local_creatives lc ON lc.uploaded_to_account_id = a.id
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            LEFT JOIN campaigns c ON c.account_id = a.id
                AND c.created_at >= :start_time AND c.created_at <= :end_time
            GROUP BY a.id, a.name, a.account_id_qc
            HAVING COUNT(DISTINCT lc.id) > 0 OR COUNT(DISTINCT c.id) > 0
            ORDER BY material_count DESC
        """

        results = db.execute(text(query), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        performance_data = []
        for row in results:
            success_rate = (row.approved_count / row.material_count * 100) if row.material_count > 0 else 0
            performance_data.append({
                '账户名称': row.account_name,
                '账户ID': row.account_id_qc,
                '素材数量': row.material_count or 0,
                '计划数量': row.campaign_count or 0,
                '成功素材': row.approved_count or 0,
                '失败素材': row.failed_count or 0,
                '成功率(%)': f"{success_rate:.1f}"
            })

        return performance_data

    except Exception as e:
        st.error(f"获取账户性能数据失败: {e}")
        return []

def get_author_productivity_stats(db, time_range, filters):
    """获取作者产出统计"""
    try:
        query = """
            SELECT
                p.name as author_name,
                COUNT(lc.id) as total_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved_materials,
                SUM(CASE WHEN lc.status = MaterialStatus.REJECTED.value THEN 1 ELSE 0 END) as rejected_materials
            FROM principals p
            JOIN local_creatives lc ON lc.principal_id = p.id
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            GROUP BY p.id, p.name
            HAVING COUNT(lc.id) > 0
            ORDER BY total_materials DESC
        """

        results = db.execute(text(query), {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        author_stats = []
        for row in results:
            success_rate = (row.approved_materials / row.total_materials * 100) if row.total_materials > 0 else 0
            author_stats.append({
                '作者': row.author_name,
                '总素材数': row.total_materials,
                '通过数量': row.approved_materials,
                '拒绝数量': row.rejected_materials,
                '通过率(%)': f"{success_rate:.1f}"
            })

        return author_stats

    except Exception as e:
        st.error(f"获取作者统计失败: {e}")
        return []

def render_global_search_results(search_term, time_range, filters):
    """渲染全局搜索结果"""
    try:
        with database_session() as db:
            # 搜索素材（从文件名提取作者，仅TEST账户）
            material_results = db.execute(text("""
                SELECT
                    lc.id,
                    lc.filename,
                    lc.status,
                    CASE
                        WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                            substring(lc.filename from '^\d+\.\d+-([^-]+)-')
                        WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                            substring(lc.filename from '-([^-]+)-\d+\.mp4')
                        ELSE 'Unknown'
                    END as author_name,
                    lc.created_at,
                    lc.updated_at,
                    lc.material_id_qc,
                    lc.uploaded_to_account_id,
                    -- 计算状态停滞时长
                    EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600 as hours_since_update
                FROM local_creatives lc
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND (lc.filename ILIKE :search_term OR CAST(lc.id AS TEXT) ILIKE :search_term)
                    AND lc.created_at >= :start_time AND lc.created_at <= :end_time
                ORDER BY lc.created_at DESC
                LIMIT 20
            """), {
                'search_term': f'%{search_term}%',
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            # 搜索关联的计划（通过正确的关联表，仅TEST账户）
            campaign_results = db.execute(text("""
                SELECT DISTINCT
                    c.campaign_id_qc,
                    c.status,
                    a.name as account_name,
                    c.created_at,
                    lc.filename as related_material,
                    pc.material_id_qc
                FROM campaigns c
                LEFT JOIN ad_accounts a ON c.account_id = a.id
                LEFT JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                LEFT JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                WHERE a.account_type = 'TEST' AND a.status = 'active'
                    AND (c.campaign_id_qc ILIKE :search_term
                       OR a.name ILIKE :search_term
                       OR lc.filename ILIKE :search_term)
                    AND c.created_at >= :start_time AND c.created_at <= :end_time
                ORDER BY c.created_at DESC
                LIMIT 20
            """), {
                'search_term': f'%{search_term}%',
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            # 状态中文映射
            status_map = {
                MaterialStatus.NEW.value: '新建', MaterialStatus.PENDING_UPLOAD.value: '待上传', MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划',
                'creating_plan': '创建计划中', MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核',
                MaterialStatus.APPROVED.value: '审核通过', MaterialStatus.REJECTED.value: '审核拒绝', MaterialStatus.UPLOAD_FAILED.value: '上传失败',
                MaterialStatus.PENDING_GROUPING.value: '待分组', MaterialStatus.PROCESSING.value: '处理中', MaterialStatus.ALREADY_TESTED.value: '已测试',
                'COMPLETED': '已完成', 'AUDITING': '审核中', 'APPEAL_TIMEOUT': '申诉超时', 'DELETED': '已删除'
            }

            # 显示搜索结果
            if material_results or campaign_results:
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("#### 📁 素材搜索结果")
                    if material_results:
                        for result in material_results:
                            with st.expander(f"📁 {result.filename or f'ID: {result.id}'}"):
                                status_cn = status_map.get(result.status, result.status)
                                st.write(f"**状态**: {status_cn} ({result.status})")

                                # 显示状态停滞时长
                                if result.hours_since_update:
                                    if result.hours_since_update < 1:
                                        duration_str = f"{result.hours_since_update * 60:.0f}分钟"
                                    elif result.hours_since_update < 24:
                                        duration_str = f"{result.hours_since_update:.1f}小时"
                                    else:
                                        duration_str = f"{result.hours_since_update / 24:.1f}天"

                                    # 根据状态和时长显示不同颜色
                                    if result.status == MaterialStatus.TESTING_PENDING_REVIEW.value and result.hours_since_update > 12:
                                        st.write(f"**停滞时长**: 🔴 {duration_str} (审核时间较长)")
                                    elif result.status == 'creating_plan' and result.hours_since_update > 0.5:
                                        st.write(f"**停滞时长**: 🟡 {duration_str} (创建时间较长)")
                                    else:
                                        st.write(f"**停滞时长**: ⏱️ {duration_str}")

                                st.write(f"**作者**: {result.author_name or 'N/A'}")
                                st.write(f"**创建时间**: {result.created_at}")
                                if result.material_id_qc:
                                    st.write(f"**素材ID**: {result.material_id_qc}")

                                # 查找真正关联的计划（通过素材ID精确匹配）
                                if result.material_id_qc:
                                    related_campaigns = db.execute(text("""
                                        SELECT DISTINCT c.campaign_id_qc, c.status, c.created_at
                                        FROM campaigns c
                                        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                                        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                                        WHERE pc.material_id_qc = :material_id_qc
                                        ORDER BY c.created_at DESC
                                    """), {'material_id_qc': result.material_id_qc}).fetchall()

                                    if related_campaigns:
                                        st.write("**关联计划**:")
                                        for camp in related_campaigns:
                                            camp_status_cn = status_map.get(camp.status, camp.status)
                                            st.write(f"  - {camp.campaign_id_qc} ({camp_status_cn})")
                                    else:
                                        st.write("**关联计划**: 无")
                                else:
                                    st.write("**关联计划**: 素材未上传，无关联计划")
                    else:
                        st.info("无匹配的素材")

                with col2:
                    st.markdown("#### 🎯 计划搜索结果")
                    if campaign_results:
                        for result in campaign_results:
                            with st.expander(f"🎯 {result.campaign_id_qc}"):
                                status_cn = status_map.get(result.status, result.status)
                                st.write(f"**状态**: {status_cn} ({result.status})")
                                st.write(f"**账户**: {result.account_name or 'N/A'}")
                                st.write(f"**创建时间**: {result.created_at}")
                                if result.related_material:
                                    st.write(f"**关联素材**: {result.related_material}")
                    else:
                        st.info("无匹配的计划")
            else:
                st.info(f"🔍 未找到包含 '{search_term}' 的结果")

    except Exception as e:
        st.error(f"搜索失败: {e}")

def render_failure_analysis(time_range, filters):
    """渲染失败分析"""
    st.markdown("#### ❌ 失败素材分析")

    try:
        with database_session() as db:
            # 获取失败素材（仅TEST账户）
            failed_materials = db.execute(text("""
                SELECT
                    lc.filename, lc.status, p.name as author_name, lc.created_at,
                    lc.harvest_attempt_count, lc.last_harvest_attempt
                FROM local_creatives lc
                LEFT JOIN principals p ON lc.principal_id = p.id
                JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                    AND lc.status IN (MaterialStatus.UPLOAD_FAILED.value, MaterialStatus.REJECTED.value)
                    AND lc.created_at >= :start_time AND lc.created_at <= :end_time
                ORDER BY lc.created_at DESC
                LIMIT 50
            """), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            if failed_materials:
                # 失败统计
                upload_failed = len([m for m in failed_materials if m.status == MaterialStatus.UPLOAD_FAILED.value])
                rejected = len([m for m in failed_materials if m.status == MaterialStatus.REJECTED.value])

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("📤 上传失败", upload_failed)
                with col2:
                    st.metric("❌ 审核拒绝", rejected)
                with col3:
                    st.metric("📊 总失败数", len(failed_materials))

                # 失败详情表格
                failure_data = []
                for material in failed_materials:
                    failure_data.append({
                        '文件名': material.filename or 'N/A',
                        '失败类型': '上传失败' if material.status == MaterialStatus.UPLOAD_FAILED.value else '审核拒绝',
                        '作者': material.author_name or 'N/A',
                        '失败时间': material.created_at.strftime('%Y-%m-%d %H:%M') if material.created_at else 'N/A',
                        '重试次数': material.harvest_attempt_count or 0
                    })

                df = pd.DataFrame(failure_data)
                st.dataframe(df, use_container_width=True)

                # 按作者统计失败
                author_failures = {}
                for material in failed_materials:
                    author = material.author_name or 'Unknown'
                    author_failures[author] = author_failures.get(author, 0) + 1

                if author_failures:
                    st.markdown("**按作者统计失败数量:**")
                    for author, count in sorted(author_failures.items(), key=lambda x: x[1], reverse=True):
                        st.write(f"- {author}: {count} 个")
            else:
                st.success("✅ 当前时间范围内无失败素材")

    except Exception as e:
        st.error(f"失败分析失败: {e}")

def export_comprehensive_data(time_range, filters):
    """导出综合数据"""
    try:
        with database_session() as db:
            # 获取素材数据（仅TEST账户）
            materials_data = db.execute(text("""
                SELECT
                    lc.id, lc.filename, lc.status, p.name as author_name,
                    lc.created_at, lc.updated_at, a.name as account_name
                FROM local_creatives lc
                LEFT JOIN principals p ON lc.principal_id = p.id
                LEFT JOIN ad_accounts a ON lc.uploaded_to_account_id = a.id
                WHERE a.account_type = 'TEST' AND a.status = 'active'
                    AND lc.created_at >= :start_time AND lc.created_at <= :end_time
                ORDER BY lc.created_at DESC
            """), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            # 获取计划数据（仅TEST账户）
            campaigns_data = db.execute(text("""
                SELECT
                    c.campaign_id_qc, c.status, c.appeal_status, c.created_at,
                    a.name as account_name
                FROM campaigns c
                LEFT JOIN ad_accounts a ON c.account_id = a.id
                WHERE a.account_type = 'TEST' AND a.status = 'active'
                    AND c.created_at >= :start_time AND c.created_at <= :end_time
                ORDER BY c.created_at DESC
            """), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            # 创建Excel文件
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 素材数据表
                if materials_data:
                    materials_df = pd.DataFrame([{
                        '素材ID': m.id,
                        '文件名': m.filename or 'N/A',
                        '状态': m.status,
                        '作者': m.author_name or 'N/A',
                        '账户': m.account_name or 'N/A',
                        '创建时间': m.created_at.strftime('%Y-%m-%d %H:%M:%S') if m.created_at else 'N/A',
                        '更新时间': m.updated_at.strftime('%Y-%m-%d %H:%M:%S') if m.updated_at else 'N/A'
                    } for m in materials_data])
                    materials_df.to_excel(writer, sheet_name='素材数据', index=False)

                # 计划数据表
                if campaigns_data:
                    campaigns_df = pd.DataFrame([{
                        '计划ID': c.campaign_id_qc,
                        '状态': c.status,
                        '申诉状态': c.appeal_status or 'N/A',
                        '账户': c.account_name or 'N/A',
                        '创建时间': c.created_at.strftime('%Y-%m-%d %H:%M:%S') if c.created_at else 'N/A'
                    } for c in campaigns_data])
                    campaigns_df.to_excel(writer, sheet_name='计划数据', index=False)

            # 提供下载
            excel_data = output.getvalue()
            b64 = base64.b64encode(excel_data).decode()

            filename = f"完整素材审核报表_{time_range['start'].strftime('%Y%m%d')}_{time_range['end'].strftime('%Y%m%d')}.xlsx"
            href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="{filename}">📥 下载完整报表</a>'
            st.markdown(href, unsafe_allow_html=True)
            st.success("✅ 完整报表已生成，点击上方链接下载")

    except Exception as e:
        st.error(f"导出失败: {e}")

def render_status_flow_visualization(db, time_range, filters):
    """状态流转可视化"""
    st.markdown("#### 🔄 状态流转可视化")

    try:
        # 获取状态流转数据
        flow_query = text("""
            SELECT
                lc.status,
                COUNT(*) as count,
                AVG(EXTRACT(EPOCH FROM (NOW() - lc.updated_at))/3600) as avg_duration_hours
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
            GROUP BY lc.status
            ORDER BY
                CASE lc.status
                    WHEN MaterialStatus.NEW.value THEN 1
                    WHEN MaterialStatus.PENDING_GROUPING.value THEN 2
                    WHEN MaterialStatus.PROCESSING.value THEN 3
                    WHEN MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 4
                    WHEN 'creating_plan' THEN 5
                    WHEN MaterialStatus.TESTING_PENDING_REVIEW.value THEN 6
                    WHEN MaterialStatus.APPROVED.value THEN 7
                    WHEN MaterialStatus.REJECTED.value THEN 8
                    WHEN MaterialStatus.ALREADY_TESTED.value THEN 9
                    WHEN MaterialStatus.UPLOAD_FAILED.value THEN 10
                    ELSE 11
                END
        """)

        flow_results = db.execute(flow_query, {
            'start_time': time_range['start'],
            'end_time': time_range['end']
        }).fetchall()

        if flow_results:
            # 状态中文映射和颜色映射
            status_map = {
                MaterialStatus.NEW.value: '新建', MaterialStatus.PENDING_GROUPING.value: '待分组', MaterialStatus.PROCESSING.value: '处理中',
                MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划', 'creating_plan': '创建计划中',
                MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核', MaterialStatus.APPROVED.value: '审核通过',
                MaterialStatus.REJECTED.value: '审核拒绝', MaterialStatus.ALREADY_TESTED.value: '已测试', MaterialStatus.UPLOAD_FAILED.value: '上传失败'
            }

            # 创建流转图表
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("##### 📊 状态分布")

                # 准备数据
                labels = [status_map.get(row.status, row.status) for row in flow_results]
                values = [row.count for row in flow_results]

                # 创建饼图
                fig_pie = px.pie(
                    values=values,
                    names=labels,
                    title="素材状态分布"
                )
                fig_pie.update_traces(textposition='inside', textinfo='percent+label')
                st.plotly_chart(fig_pie, use_container_width=True)

            with col2:
                st.markdown("##### ⏱️ 平均停滞时长")

                # 准备停滞时长数据
                duration_labels = []
                duration_values = []

                for row in flow_results:
                    if row.avg_duration_hours and row.avg_duration_hours > 0:
                        duration_labels.append(status_map.get(row.status, row.status))
                        duration_values.append(row.avg_duration_hours)

                if duration_values:
                    # 创建柱状图
                    fig_bar = px.bar(
                        x=duration_labels,
                        y=duration_values,
                        title="各状态平均停滞时长(小时)",
                        color=duration_values,
                        color_continuous_scale=['#27AE60', '#F39C12', '#E74C3C']
                    )
                    fig_bar.update_layout(
                        xaxis_title="状态",
                        yaxis_title="平均停滞时长(小时)",
                        xaxis_tickangle=45
                    )
                    st.plotly_chart(fig_bar, use_container_width=True)
                else:
                    st.info("📭 暂无停滞时长数据")

            # 状态流转表格
            st.markdown("##### 📋 详细状态信息")
            flow_data = []
            for row in flow_results:
                status_cn = status_map.get(row.status, row.status)
                duration_str = f"{row.avg_duration_hours:.1f}小时" if row.avg_duration_hours else "N/A"

                # 状态健康度评估
                if row.status in [MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, MaterialStatus.ALREADY_TESTED.value]:
                    health = "✅ 终态"
                elif row.status == MaterialStatus.TESTING_PENDING_REVIEW.value and row.avg_duration_hours and row.avg_duration_hours > 24:
                    health = "🔴 审核超时"
                elif row.status == 'creating_plan' and row.avg_duration_hours and row.avg_duration_hours > 1:
                    health = "🟡 创建较慢"
                else:
                    health = "✅ 正常"

                flow_data.append({
                    '状态': status_cn,
                    '数量': row.count,
                    '平均停滞': duration_str,
                    '健康度': health
                })

            df_flow = pd.DataFrame(flow_data)
            st.dataframe(df_flow, use_container_width=True)
        else:
            st.info("📭 当前时间范围内无状态流转数据")

    except Exception as e:
        st.error(f"状态流转可视化失败: {e}")

def render_material_status_analysis(db, time_range, filters):
    """素材状态分析"""
    st.markdown("#### 📊 素材状态分布分析")

    try:
        # 获取状态分布数据（仅TEST账户）
        status_query = """
            SELECT
                lc.status,
                COUNT(*) as count,
                COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
        """

        # 添加过滤条件
        params = {'start_time': time_range['start'], 'end_time': time_range['end']}

        if filters.get('material_statuses'):
            placeholders = ','.join([f"'{status}'" for status in filters['material_statuses']])
            status_query += f" AND lc.status IN ({placeholders})"

        if filters.get('author_names'):
            author_conditions = []
            for i, author in enumerate(filters['author_names']):
                author_conditions.append(f"lc.filename ILIKE :author_{i}")
                params[f'author_{i}'] = f'%{author}%'
            status_query += f" AND ({' OR '.join(author_conditions)})"

        status_query += " GROUP BY lc.status ORDER BY count DESC"

        status_results = db.execute(text(status_query), params).fetchall()

        if status_results:
            # 状态中文映射
            status_map = {
                MaterialStatus.NEW.value: '新建', MaterialStatus.PENDING_UPLOAD.value: '待上传', MaterialStatus.UPLOADED_PENDING_PLAN.value: '已上传待建计划',
                'creating_plan': '创建计划中', MaterialStatus.TESTING_PENDING_REVIEW.value: '测试待审核',
                MaterialStatus.APPROVED.value: '审核通过', MaterialStatus.REJECTED.value: '审核拒绝', MaterialStatus.UPLOAD_FAILED.value: '上传失败',
                MaterialStatus.PENDING_GROUPING.value: '待分组', MaterialStatus.PROCESSING.value: '处理中', MaterialStatus.ALREADY_TESTED.value: '已测试'
            }

            # 创建数据表格
            status_data = []
            for row in status_results:
                status_cn = status_map.get(row.status, row.status)
                status_data.append({
                    '状态': f"{status_cn} ({row.status})",
                    '数量': row.count,
                    '占比(%)': f"{row.percentage:.1f}%"
                })

            col1, col2 = st.columns(2)

            with col1:
                # 状态分布表格
                df = pd.DataFrame(status_data)
                st.dataframe(df, use_container_width=True)

            with col2:
                # 状态分布饼图
                labels = [data['状态'] for data in status_data]
                values = [data['数量'] for data in status_data]

                fig = px.pie(values=values, names=labels, title="素材状态分布")
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📭 当前筛选条件下无状态数据")

    except Exception as e:
        st.error(f"获取状态分析失败: {e}")

def render_material_source_analysis(db, time_range, filters):
    """素材来源分析"""
    st.markdown("#### 👤 素材来源分析（按作者）")

    try:
        # 按作者统计素材（优化作者名称标准化）
        author_query = """
            SELECT
                CASE
                    WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                        -- 提取作者名并去除数字和字母后缀
                        regexp_replace(
                            substring(lc.filename from '^\d+\.\d+-([^-]+)-'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                        -- 提取作者名并去除数字和字母后缀
                        regexp_replace(
                            substring(lc.filename from '-([^-]+)-\d+\.mp4'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    ELSE 'Unknown'
                END as author_name,
                COUNT(*) as total_count,
                SUM(CASE WHEN lc.status = MaterialStatus.APPROVED.value THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN lc.status = MaterialStatus.REJECTED.value THEN 1 ELSE 0 END) as rejected_count,
                SUM(CASE WHEN lc.status = MaterialStatus.PENDING_GROUPING.value THEN 1 ELSE 0 END) as pending_grouping_count,
                SUM(CASE WHEN lc.status = MaterialStatus.PROCESSING.value THEN 1 ELSE 0 END) as processing_count,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 1 ELSE 0 END) as uploaded_pending_plan_count,
                SUM(CASE WHEN lc.status = 'creating_plan' THEN 1 ELSE 0 END) as creating_plan_count,
                SUM(CASE WHEN lc.status = MaterialStatus.TESTING_PENDING_REVIEW.value THEN 1 ELSE 0 END) as testing_pending_review_count,
                SUM(CASE WHEN lc.status = MaterialStatus.UPLOAD_FAILED.value THEN 1 ELSE 0 END) as upload_failed_count,
                SUM(CASE WHEN lc.status = MaterialStatus.ALREADY_TESTED.value THEN 1 ELSE 0 END) as already_tested_count,
                SUM(CASE WHEN lc.status NOT IN (MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, MaterialStatus.PENDING_GROUPING.value, MaterialStatus.PROCESSING.value,
                                           MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', MaterialStatus.TESTING_PENDING_REVIEW.value,
                                           MaterialStatus.UPLOAD_FAILED.value, MaterialStatus.ALREADY_TESTED.value) THEN 1 ELSE 0 END) as other_status_count
            FROM local_creatives lc
            JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
            WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                AND lc.created_at >= :start_time AND lc.created_at <= :end_time
                AND lc.filename IS NOT NULL
        """

        params = {'start_time': time_range['start'], 'end_time': time_range['end']}

        if filters.get('author_names'):
            author_conditions = []
            for i, author in enumerate(filters['author_names']):
                author_conditions.append(f"lc.filename ILIKE :author_{i}")
                params[f'author_{i}'] = f'%{author}%'
            author_query += f" AND ({' OR '.join(author_conditions)})"

        author_query += """
            GROUP BY
                CASE
                    WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                        regexp_replace(
                            substring(lc.filename from '^\d+\.\d+-([^-]+)-'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                        regexp_replace(
                            substring(lc.filename from '-([^-]+)-\d+\.mp4'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    ELSE 'Unknown'
                END
            HAVING
                CASE
                    WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                        regexp_replace(
                            substring(lc.filename from '^\d+\.\d+-([^-]+)-'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                        regexp_replace(
                            substring(lc.filename from '-([^-]+)-\d+\.mp4'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    ELSE 'Unknown'
                END != 'Unknown' AND
                CASE
                    WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                        regexp_replace(
                            substring(lc.filename from '^\d+\.\d+-([^-]+)-'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                        regexp_replace(
                            substring(lc.filename from '-([^-]+)-\d+\.mp4'),
                            '[0-9]+[a-zA-Z]*$|[a-zA-Z]+[0-9]*$',
                            '',
                            'g'
                        )
                    ELSE 'Unknown'
                END != ''
            ORDER BY total_count DESC
            LIMIT 20
        """

        author_results = db.execute(text(author_query), params).fetchall()

        if author_results:
            # 创建作者统计表格
            author_data = []
            for row in author_results:
                success_rate = (row.approved_count / row.total_count * 100) if row.total_count > 0 else 0

                # 计算其他状态的总数（不包括creating_plan）
                other_count = (row.pending_grouping_count + row.processing_count +
                             row.uploaded_pending_plan_count + row.testing_pending_review_count +
                             row.upload_failed_count + row.already_tested_count + row.other_status_count)

                author_data.append({
                    '作者': row.author_name,
                    '总素材数': row.total_count,
                    '通过数量': row.approved_count,
                    '拒绝数量': row.rejected_count,
                    '创建计划中': row.creating_plan_count,
                    '其他状态': other_count,
                    '通过率(%)': f"{success_rate:.1f}%"
                })

                # 如果是代朋飞，显示详细状态分布
                if row.author_name == '代朋飞':
                    st.info(f"""
                    **代朋飞详细状态分布**:
                    - 总素材: {row.total_count}
                    - 审核通过: {row.approved_count}
                    - 审核拒绝: {row.rejected_count}
                    - 待分组: {row.pending_grouping_count}
                    - 处理中: {row.processing_count}
                    - 已上传待建计划: {row.uploaded_pending_plan_count}
                    - 创建计划中: {row.creating_plan_count}
                    - 测试待审核: {row.testing_pending_review_count}
                    - 上传失败: {row.upload_failed_count}
                    - 已测试: {row.already_tested_count}
                    - 其他状态: {row.other_status_count}
                    """)

                    # 验证总数
                    calculated_total = (row.approved_count + row.rejected_count +
                                      row.pending_grouping_count + row.processing_count +
                                      row.uploaded_pending_plan_count + row.creating_plan_count +
                                      row.testing_pending_review_count + row.upload_failed_count +
                                      row.already_tested_count + row.other_status_count)
                    if calculated_total != row.total_count:
                        st.warning(f"⚠️ 状态统计不一致: 计算总数 {calculated_total} ≠ 实际总数 {row.total_count}")

            col1, col2 = st.columns(2)

            with col1:
                # 作者统计表格
                df = pd.DataFrame(author_data)
                st.dataframe(df, use_container_width=True)

            with col2:
                # Top 10 作者产出图
                top_authors = author_data[:10]
                if top_authors:
                    authors = [data['作者'] for data in top_authors]
                    counts = [data['总素材数'] for data in top_authors]

                    fig = px.bar(x=authors, y=counts, title="Top 10 作者素材产出")
                    fig.update_layout(xaxis_title="作者", yaxis_title="素材数量")
                    st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("📭 当前筛选条件下无作者数据")

    except Exception as e:
        st.error(f"获取来源分析失败: {e}")

def render_abnormal_campaign_monitoring(time_range, filters):
    """异常计划监控"""
    st.markdown("#### ⏰ 异常计划监控")

    try:
        with database_session() as db:
            # 查找异常计划（仅TEST账户）
            abnormal_query = """
                SELECT
                    c.campaign_id_qc,
                    c.status,
                    c.appeal_status,
                    c.created_at,
                    c.last_appeal_at,
                    a.name as account_name,
                    EXTRACT(EPOCH FROM (NOW() - c.created_at))/3600 as hours_since_created
                FROM campaigns c
                LEFT JOIN ad_accounts a ON c.account_id = a.id
                WHERE a.account_type = 'TEST' AND a.status = 'active'
                    AND c.created_at >= :start_time AND c.created_at <= :end_time
                    AND (
                        (c.status = 'AUDITING' AND EXTRACT(EPOCH FROM (NOW() - c.created_at))/3600 > 24) OR
                        (c.status = 'APPEAL_TIMEOUT') OR
                        (c.appeal_status = 'appeal_pending' AND c.last_appeal_at IS NOT NULL
                         AND EXTRACT(EPOCH FROM (NOW() - c.last_appeal_at))/3600 > 48)
                    )
                ORDER BY c.created_at DESC
                LIMIT 50
            """

            abnormal_results = db.execute(text(abnormal_query), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            if abnormal_results:
                # 异常统计
                long_auditing = len([r for r in abnormal_results if r.status == 'AUDITING'])
                appeal_timeout = len([r for r in abnormal_results if r.status == 'APPEAL_TIMEOUT'])
                long_appeal = len([r for r in abnormal_results if r.appeal_status == 'appeal_pending'])

                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("⏳ 长时间审核", long_auditing)
                with col2:
                    st.metric("⏰ 申诉超时", appeal_timeout)
                with col3:
                    st.metric("🔄 申诉中", long_appeal)

                # 异常详情表格
                abnormal_data = []
                for result in abnormal_results:
                    abnormal_type = ""
                    if result.status == 'AUDITING' and result.hours_since_created > 24:
                        abnormal_type = "长时间审核"
                    elif result.status == 'APPEAL_TIMEOUT':
                        abnormal_type = "申诉超时"
                    elif result.appeal_status == 'appeal_pending':
                        abnormal_type = "申诉中"

                    abnormal_data.append({
                        '计划ID': result.campaign_id_qc,
                        '异常类型': abnormal_type,
                        '状态': result.status,
                        '账户': result.account_name or 'N/A',
                        '创建时间': result.created_at.strftime('%Y-%m-%d %H:%M') if result.created_at else 'N/A',
                        '已等待(小时)': f"{result.hours_since_created:.1f}"
                    })

                df = pd.DataFrame(abnormal_data)
                st.dataframe(df, use_container_width=True)
            else:
                st.success("✅ 当前时间范围内无异常计划")

    except Exception as e:
        st.error(f"异常监控失败: {e}")

def render_test_campaign_compliance_check(time_range, filters):
    """测试计划合规检查 - 检查百分百上传成功的视频是否都有测试计划"""
    st.markdown("#### 🎯 测试计划合规检查")
    st.info("📋 检查今日更新要求：百分百上传成功的视频必须要搭建测试计划")

    try:
        with database_session() as db:
            # 查找上传成功但没有测试计划的视频（仅TEST账户）
            compliance_query = """
                WITH approved_materials AS (
                    SELECT
                        lc.id,
                        lc.filename,
                        lc.material_id_qc,
                        lc.uploaded_to_account_id,
                        lc.created_at,
                        CASE
                            WHEN lc.filename ~ '^\d+\.\d+-([^-]+)-' THEN
                                substring(lc.filename from '^\d+\.\d+-([^-]+)-')
                            WHEN lc.filename ~ '-([^-]+)-\d+\.mp4' THEN
                                substring(lc.filename from '-([^-]+)-\d+\.mp4')
                            ELSE 'Unknown'
                        END as author_name
                    FROM local_creatives lc
                    JOIN ad_accounts aa ON lc.uploaded_to_account_id = aa.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                        AND lc.status = MaterialStatus.APPROVED.value
                        AND lc.material_id_qc IS NOT NULL
                        AND lc.uploaded_to_account_id IS NOT NULL
                        AND lc.created_at >= :start_time
                        AND lc.created_at <= :end_time
                ),
                test_campaigns AS (
                    SELECT DISTINCT
                        c.account_id,
                        c.campaign_id_qc,
                        c.created_at as campaign_created_at
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE aa.account_type = 'TEST' AND aa.status = 'active'
                        AND c.created_at >= :start_time
                        AND c.created_at <= :end_time
                        AND (
                            LOWER(c.campaign_id_qc) LIKE '%测试%' OR
                            LOWER(c.campaign_id_qc) LIKE '%test%'
                        )
                )
                SELECT
                    am.id,
                    am.filename,
                    am.material_id_qc,
                    am.author_name,
                    am.created_at,
                    aa.name as account_name,
                    CASE
                        WHEN tc.account_id IS NOT NULL THEN '有测试计划'
                        ELSE '缺少测试计划'
                    END as compliance_status,
                    tc.campaign_id_qc as test_campaign_id
                FROM approved_materials am
                LEFT JOIN ad_accounts aa ON am.uploaded_to_account_id = aa.id
                LEFT JOIN test_campaigns tc ON tc.account_id = am.uploaded_to_account_id
                ORDER BY
                    CASE WHEN tc.account_id IS NULL THEN 0 ELSE 1 END,
                    am.created_at DESC
            """

            compliance_results = db.execute(text(compliance_query), {
                'start_time': time_range['start'],
                'end_time': time_range['end']
            }).fetchall()

            if compliance_results:
                # 合规统计
                total_approved = len(compliance_results)
                with_test_plan = len([r for r in compliance_results if r.compliance_status == '有测试计划'])
                without_test_plan = total_approved - with_test_plan
                compliance_rate = (with_test_plan / total_approved * 100) if total_approved > 0 else 0

                # 显示统计指标
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    st.metric("✅ 已通过素材", total_approved)
                with col2:
                    st.metric("🎯 有测试计划", with_test_plan)
                with col3:
                    st.metric("❌ 缺少测试计划", without_test_plan)
                with col4:
                    color = "normal" if compliance_rate >= 100 else "inverse"
                    st.metric("📊 合规率", f"{compliance_rate:.1f}%", delta=f"目标: 100%")

                # 合规状态提示
                if compliance_rate >= 100:
                    st.success("🎉 完全合规！所有上传成功的视频都有测试计划")
                elif compliance_rate >= 80:
                    st.warning(f"⚠️ 部分不合规，还有 {without_test_plan} 个视频缺少测试计划")
                else:
                    st.error(f"❌ 严重不合规！{without_test_plan} 个视频缺少测试计划，需要立即处理")

                # 详细列表
                st.markdown("#### 📋 详细合规检查结果")

                # 分别显示合规和不合规的
                tab1, tab2 = st.tabs(["❌ 缺少测试计划", "✅ 已有测试计划"])

                with tab1:
                    non_compliant = [r for r in compliance_results if r.compliance_status == '缺少测试计划']
                    if non_compliant:
                        st.error(f"发现 {len(non_compliant)} 个视频缺少测试计划，需要立即处理！")

                        non_compliant_data = []
                        for result in non_compliant:
                            non_compliant_data.append({
                                '文件名': result.filename or 'N/A',
                                '作者': result.author_name or 'Unknown',
                                '账户': result.account_name or 'N/A',
                                '素材ID': result.material_id_qc or 'N/A',
                                '上传时间': result.created_at.strftime('%Y-%m-%d %H:%M') if result.created_at else 'N/A',
                                '状态': '🚨 需要创建测试计划'
                            })

                        df_non_compliant = pd.DataFrame(non_compliant_data)
                        st.dataframe(df_non_compliant, use_container_width=True)

                        # 提供操作建议
                        st.markdown("**🔧 处理建议:**")
                        st.markdown("1. 立即为这些视频创建测试计划")
                        st.markdown("2. 确保计划名称包含'测试'或'test'关键词")
                        st.markdown("3. 验证测试计划创建成功后重新检查")
                    else:
                        st.success("✅ 所有视频都有测试计划")

                with tab2:
                    compliant = [r for r in compliance_results if r.compliance_status == '有测试计划']
                    if compliant:
                        st.success(f"✅ {len(compliant)} 个视频已有测试计划")

                        compliant_data = []
                        for result in compliant:
                            compliant_data.append({
                                '文件名': result.filename or 'N/A',
                                '作者': result.author_name or 'Unknown',
                                '账户': result.account_name or 'N/A',
                                '测试计划ID': result.test_campaign_id or 'N/A',
                                '上传时间': result.created_at.strftime('%Y-%m-%d %H:%M') if result.created_at else 'N/A',
                                '状态': '✅ 合规'
                            })

                        df_compliant = pd.DataFrame(compliant_data)
                        st.dataframe(df_compliant, use_container_width=True)
                    else:
                        st.info("当前无合规视频")

            else:
                st.info("📭 当前时间范围内无已通过的素材")

    except Exception as e:
        st.error(f"合规检查失败: {e}")

# 其他占位符函数
def render_performance_table(db, time_range, filters): pass
def render_account_drilldown(db, time_range, filters): pass
def render_material_timeline(db, time_range, filters): pass
def render_material_utilization_analysis(time_range, filters): pass

if __name__ == "__main__":
    render_complete_material_analytics_page()
