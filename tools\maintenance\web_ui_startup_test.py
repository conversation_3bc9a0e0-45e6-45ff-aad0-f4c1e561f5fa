#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web UI启动测试工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 测试Web UI是否可以正常启动，检查所有导入和依赖
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
import importlib.util

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class WebUIStartupTester:
    """Web UI启动测试器"""
    
    def __init__(self):
        self.web_ui_file = project_root / "web_ui.py"
        self.test_results = {}
        self.errors_found = []
    
    def test_basic_imports(self) -> bool:
        """测试基础导入"""
        print("🧪 测试基础导入...")
        
        basic_imports = [
            'streamlit',
            'pandas',
            'datetime',
            'pathlib',
            'typing'
        ]
        
        failed_imports = []
        
        for module_name in basic_imports:
            try:
                __import__(module_name)
                print(f"  ✅ {module_name}")
            except ImportError as e:
                failed_imports.append(f"{module_name}: {e}")
                print(f"  ❌ {module_name}: {e}")
        
        if failed_imports:
            self.errors_found.extend(failed_imports)
            return False
        
        return True
    
    def test_project_imports(self) -> bool:
        """测试项目导入"""
        print("\n🧪 测试项目导入...")
        
        project_imports = [
            'qianchuan_aw.utils.db_utils',
            'qianchuan_aw.database.models',
            'qianchuan_aw.utils.unified_material_status',
            'qianchuan_aw.database.database'
        ]
        
        failed_imports = []
        
        for module_name in project_imports:
            try:
                __import__(module_name)
                print(f"  ✅ {module_name}")
            except ImportError as e:
                failed_imports.append(f"{module_name}: {e}")
                print(f"  ❌ {module_name}: {e}")
        
        if failed_imports:
            self.errors_found.extend(failed_imports)
            return False
        
        return True
    
    def test_ai_tools_imports(self) -> bool:
        """测试AI工具导入"""
        print("\n🧪 测试AI工具导入...")
        
        ai_tools_imports = [
            'ai_tools.maintenance.ai_tool_20250718_maintenance_unified_account_selector',
            'ai_tools.maintenance.ai_tool_20250718_maintenance_global_account_selector',
            'ai_tools.ai_tool_20250720_system_monitoring_page',
            'ai_tools.ai_tool_20250720_enhanced_material_analytics',
            'ai_tools.ai_tool_20250720_complete_material_analytics',
            'ai_tools.analytics.ai_tool_20250803_analytics_test_material_report'
        ]
        
        failed_imports = []
        
        for module_name in ai_tools_imports:
            try:
                __import__(module_name)
                print(f"  ✅ {module_name}")
            except ImportError as e:
                failed_imports.append(f"{module_name}: {e}")
                print(f"  ❌ {module_name}: {e}")
        
        if failed_imports:
            self.errors_found.extend(failed_imports)
            return False
        
        return True
    
    def test_web_ui_compilation(self) -> bool:
        """测试Web UI编译"""
        print("\n🧪 测试Web UI编译...")
        
        try:
            with open(self.web_ui_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 编译检查
            compile(content, str(self.web_ui_file), 'exec')
            print("  ✅ Web UI编译成功")
            return True
            
        except SyntaxError as e:
            error_msg = f"语法错误 (第{e.lineno}行): {e.msg}"
            self.errors_found.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False
        except Exception as e:
            error_msg = f"编译错误: {e}"
            self.errors_found.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False
    
    def test_database_connection(self) -> bool:
        """测试数据库连接"""
        print("\n🧪 测试数据库连接...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            
            with database_session() as db:
                count = db.query(LocalCreative).count()
                print(f"  ✅ 数据库连接成功，素材数量: {count}")
                return True
                
        except Exception as e:
            error_msg = f"数据库连接失败: {e}"
            self.errors_found.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False
    
    def test_streamlit_compatibility(self) -> bool:
        """测试Streamlit兼容性"""
        print("\n🧪 测试Streamlit兼容性...")
        
        try:
            import streamlit as st
            
            # 测试基本功能
            test_functions = [
                'title', 'sidebar', 'columns', 'dataframe', 
                'selectbox', 'multiselect', 'button', 'form'
            ]
            
            for func_name in test_functions:
                if hasattr(st, func_name):
                    print(f"  ✅ st.{func_name}")
                else:
                    print(f"  ❌ st.{func_name} 不存在")
                    return False
            
            return True
            
        except Exception as e:
            error_msg = f"Streamlit兼容性测试失败: {e}"
            self.errors_found.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False
    
    def simulate_web_ui_startup(self) -> bool:
        """模拟Web UI启动"""
        print("\n🧪 模拟Web UI启动...")
        
        try:
            # 模拟导入web_ui模块（不执行）
            spec = importlib.util.spec_from_file_location("web_ui", self.web_ui_file)
            if spec is None:
                raise ImportError("无法创建模块规范")
            
            web_ui_module = importlib.util.module_from_spec(spec)
            
            # 检查模块是否可以加载
            if web_ui_module is None:
                raise ImportError("无法创建模块")
            
            print("  ✅ Web UI模块可以正常加载")
            return True
            
        except Exception as e:
            error_msg = f"Web UI启动模拟失败: {e}"
            self.errors_found.append(error_msg)
            print(f"  ❌ {error_msg}")
            return False
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        from datetime import datetime
        
        passed_tests = sum(1 for result in self.test_results.values() if result)
        total_tests = len(self.test_results)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = f"""
{'='*60}
🧪 Web UI启动测试报告
{'='*60}
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
目标文件: {self.web_ui_file}

📊 测试结果统计:
通过测试: {passed_tests}/{total_tests}
成功率: {success_rate:.1f}%

"""
        
        if self.test_results:
            report += "📋 详细测试结果:\n"
            for test_name, result in self.test_results.items():
                status = "✅ 通过" if result else "❌ 失败"
                report += f"  - {test_name}: {status}\n"
            report += "\n"
        
        if self.errors_found:
            report += "❌ 发现的错误:\n"
            for i, error in enumerate(self.errors_found, 1):
                report += f"  {i}. {error}\n"
            report += "\n"
        
        # 生成建议
        report += "💡 建议:\n"
        if success_rate >= 90:
            report += "  - Web UI可以正常启动\n"
            report += "  - 运行: streamlit run web_ui.py\n"
        elif success_rate >= 70:
            report += "  - Web UI基本可以启动，但有一些问题需要关注\n"
            report += "  - 建议修复上述错误后再启动\n"
        else:
            report += "  - Web UI存在严重问题，无法正常启动\n"
            report += "  - 必须修复所有错误才能启动\n"
        
        report += f"\n{'='*60}\n"
        
        return report
    
    def run_comprehensive_test(self) -> dict:
        """运行综合测试"""
        print("🚀 开始Web UI启动综合测试...")
        
        # 运行所有测试
        tests = [
            ("基础导入测试", self.test_basic_imports),
            ("项目导入测试", self.test_project_imports),
            ("AI工具导入测试", self.test_ai_tools_imports),
            ("Web UI编译测试", self.test_web_ui_compilation),
            ("数据库连接测试", self.test_database_connection),
            ("Streamlit兼容性测试", self.test_streamlit_compatibility),
            ("Web UI启动模拟", self.simulate_web_ui_startup),
        ]
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"🔍 {test_name}")
            print(f"{'='*50}")
            
            try:
                result = test_func()
                self.test_results[test_name] = result
            except Exception as e:
                print(f"❌ {test_name}异常: {e}")
                self.test_results[test_name] = False
                self.errors_found.append(f"{test_name}异常: {e}")
        
        # 生成报告
        test_report = self.generate_test_report()
        print(test_report)
        
        return {
            'test_results': self.test_results,
            'errors_found': self.errors_found,
            'test_report': test_report
        }

def main():
    """主函数"""
    print("🧪 Web UI启动测试工具")
    print("=" * 60)
    
    tester = WebUIStartupTester()
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_test()
        
        # 保存报告
        from datetime import datetime
        report_file = Path("logs") / f"web_ui_startup_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results['test_report'])
        
        print(f"📄 测试报告已保存到: {report_file}")
        
        # 返回状态
        passed_tests = sum(1 for result in results['test_results'].values() if result)
        total_tests = len(results['test_results'])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        if success_rate >= 90:
            print(f"\n🎉 Web UI启动测试通过！可以正常启动")
            return 0
        elif success_rate >= 70:
            print(f"\n⚠️ Web UI基本可以启动，但建议修复发现的问题")
            return 1
        else:
            print(f"\n❌ Web UI存在严重问题，无法正常启动")
            return 2
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return 3

if __name__ == "__main__":
    exit(main())
