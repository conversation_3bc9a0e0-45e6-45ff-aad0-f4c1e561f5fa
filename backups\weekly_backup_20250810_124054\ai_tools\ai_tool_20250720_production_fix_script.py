#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具: 千川自动化系统生产环境修复脚本
生成时间: 2025-07-20
用途: 修复生产环境中发现的问题，恢复正常工作流
生命周期: 永久工具
"""

import sys
import os
import time
import subprocess
from pathlib import Path
from datetime import datetime
import json

# 添加项目路径
project_root = Path(__file__).parent.parent
src_path = project_root / 'src'
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

class ProductionFixManager:
    def __init__(self):
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': [],
            'errors_encountered': [],
            'recommendations': []
        }
        
    def log_result(self, action, status, details=None, error=None):
        """记录修复结果"""
        result = {
            'action': action,
            'status': status,
            'details': details or '',
            'error': error or '',
            'timestamp': datetime.now().isoformat()
        }
        
        if status == 'SUCCESS':
            self.results['fixes_applied'].append(result)
        else:
            self.results['errors_encountered'].append(result)
        
        # 实时输出
        status_icon = "✅" if status == "SUCCESS" else "❌"
        print(f"{status_icon} {action}: {status}")
        if error:
            print(f"   错误: {error}")
        if details:
            print(f"   详情: {details}")
    
    def fix_platform_creatives_sync(self):
        """修复平台素材同步问题"""
        print("\n🔧 修复平台素材同步问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from sqlalchemy import text
            
            with database_session() as db:
                # 更新所有null的last_checked字段
                result = db.execute(text("""
                    UPDATE platform_creatives 
                    SET last_checked = NOW() 
                    WHERE last_checked IS NULL
                """))
                
                updated_count = result.rowcount
                db.commit()
                
                self.log_result(
                    'platform_creatives_sync_fix',
                    'SUCCESS',
                    f'已更新{updated_count}条平台素材的检查时间'
                )
                
        except Exception as e:
            self.log_result(
                'platform_creatives_sync_fix',
                'FAILED',
                error=str(e)
            )
    
    def trigger_backlog_tasks(self):
        """触发积压的任务"""
        print("\n🚀 触发积压的工作流任务...")
        
        try:
            # 导入任务模块
            from qianchuan_aw.workflows.tasks import (
                task_ingest_and_upload,
                task_group_and_dispatch,
                task_monitor_materials
            )
            
            # 触发文件摄取任务
            try:
                task_ingest_and_upload.delay()
                self.log_result(
                    'trigger_file_ingestion',
                    'SUCCESS',
                    '文件摄取任务已触发'
                )
            except Exception as e:
                self.log_result(
                    'trigger_file_ingestion',
                    'FAILED',
                    error=str(e)
                )
            
            # 触发分组派发任务
            try:
                task_group_and_dispatch.delay()
                self.log_result(
                    'trigger_group_dispatch',
                    'SUCCESS',
                    '分组派发任务已触发'
                )
            except Exception as e:
                self.log_result(
                    'trigger_group_dispatch',
                    'FAILED',
                    error=str(e)
                )
            
            # 触发素材监控任务
            try:
                task_monitor_materials.delay()
                self.log_result(
                    'trigger_material_monitoring',
                    'SUCCESS',
                    '素材监控任务已触发'
                )
            except Exception as e:
                self.log_result(
                    'trigger_material_monitoring',
                    'FAILED',
                    error=str(e)
                )
                
        except ImportError as e:
            self.log_result(
                'import_tasks',
                'FAILED',
                error=f'无法导入任务模块: {str(e)}'
            )
    
    def check_celery_services(self):
        """检查Celery服务状态"""
        print("\n🔍 检查Celery服务状态...")
        
        try:
            from qianchuan_aw.celery_app import app as celery_app
            
            # 检查Celery应用状态
            if celery_app:
                self.log_result(
                    'celery_app_check',
                    'SUCCESS',
                    'Celery应用正常加载'
                )
                
                # 检查已注册的任务
                registered_tasks = list(celery_app.tasks.keys())
                core_tasks = [
                    'tasks.ingest_and_upload',
                    'tasks.group_and_dispatch',
                    'tasks.monitor_materials'
                ]
                
                missing_tasks = [task for task in core_tasks if task not in registered_tasks]
                if missing_tasks:
                    self.log_result(
                        'task_registration_check',
                        'PARTIAL',
                        f'缺少任务: {missing_tasks}'
                    )
                else:
                    self.log_result(
                        'task_registration_check',
                        'SUCCESS',
                        '所有核心任务已注册'
                    )
            else:
                self.log_result(
                    'celery_app_check',
                    'FAILED',
                    error='Celery应用加载失败'
                )
                
        except Exception as e:
            self.log_result(
                'celery_services_check',
                'FAILED',
                error=str(e)
            )
    
    def verify_database_status(self):
        """验证数据库状态"""
        print("\n🗄️ 验证数据库状态...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from sqlalchemy import text
            
            with database_session() as db:
                # 检查最新数据
                result = db.execute(text("""
                    SELECT 
                        'local_creatives' as table_name,
                        COUNT(*) as total_count,
                        MAX(created_at) as latest_time
                    FROM local_creatives
                    UNION ALL
                    SELECT 
                        'campaigns' as table_name,
                        COUNT(*) as total_count,
                        MAX(created_at) as latest_time
                    FROM campaigns
                """)).fetchall()
                
                for row in result:
                    self.log_result(
                        f'database_status_{row.table_name}',
                        'SUCCESS',
                        f'{row.table_name}: {row.total_count}条记录, 最新: {row.latest_time}'
                    )
                
        except Exception as e:
            self.log_result(
                'database_status_check',
                'FAILED',
                error=str(e)
            )
    
    def check_source_directories(self):
        """检查素材源目录状态"""
        print("\n📁 检查素材源目录状态...")
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            config_manager = get_config_manager()
            config = config_manager.get_config()
            
            material_config = config.get('workflow', {}).get('material_collection', {})
            source_dirs = material_config.get('source_dirs', [])
            
            accessible_dirs = []
            inaccessible_dirs = []
            
            for source_dir in source_dirs:
                if os.path.exists(source_dir):
                    # 检查目录中的文件数量
                    try:
                        file_count = len([f for f in os.listdir(source_dir) 
                                        if f.lower().endswith(('.mp4', '.mov', '.avi'))])
                        accessible_dirs.append((source_dir, file_count))
                    except PermissionError:
                        inaccessible_dirs.append((source_dir, "权限不足"))
                else:
                    inaccessible_dirs.append((source_dir, "目录不存在"))
            
            if accessible_dirs:
                for dir_path, file_count in accessible_dirs:
                    self.log_result(
                        'source_directory_check',
                        'SUCCESS',
                        f'{dir_path}: {file_count}个视频文件'
                    )
            
            if inaccessible_dirs:
                for dir_path, reason in inaccessible_dirs:
                    self.log_result(
                        'source_directory_check',
                        'FAILED',
                        f'{dir_path}: {reason}'
                    )
                    
        except Exception as e:
            self.log_result(
                'source_directories_check',
                'FAILED',
                error=str(e)
            )
    
    def generate_recommendations(self):
        """生成修复建议"""
        print("\n💡 生成修复建议...")
        
        # 基于修复结果生成建议
        if len(self.results['errors_encountered']) > 0:
            self.results['recommendations'].append(
                "存在修复失败的项目，建议手动检查相关服务状态"
            )
        
        if len(self.results['fixes_applied']) > 0:
            self.results['recommendations'].append(
                "已应用修复，建议监控系统运行状态30分钟"
            )
        
        # 通用建议
        self.results['recommendations'].extend([
            "定期检查Celery worker和beat服务状态",
            "监控数据库连接和查询性能",
            "设置自动化监控和告警机制",
            "建立定期备份和恢复流程"
        ])
    
    def run_production_fix(self):
        """运行生产环境修复"""
        print("🚀 开始千川自动化系统生产环境修复...")
        print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行修复步骤
        self.verify_database_status()
        self.check_celery_services()
        self.check_source_directories()
        self.fix_platform_creatives_sync()
        self.trigger_backlog_tasks()
        self.generate_recommendations()
        
        # 生成修复报告
        self.generate_fix_report()
    
    def generate_fix_report(self):
        """生成修复报告"""
        print("\n📊 生成修复报告...")
        
        # 统计结果
        total_fixes = len(self.results['fixes_applied'])
        total_errors = len(self.results['errors_encountered'])
        
        # 保存详细报告
        report_path = project_root / 'ai_tools' / f'production_fix_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        # 打印总结
        print(f"\n{'='*60}")
        print("🎯 生产环境修复总结")
        print(f"{'='*60}")
        print(f"成功修复: {total_fixes} 项 ✅")
        print(f"修复失败: {total_errors} 项 ❌")
        
        if self.results['recommendations']:
            print(f"\n💡 修复建议:")
            for i, rec in enumerate(self.results['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        print(f"\n📄 详细报告已保存: {report_path}")
        
        # 评估修复效果
        if total_errors == 0:
            print("\n🎉 所有修复项目执行成功！系统应该已恢复正常运行。")
        elif total_fixes > total_errors:
            print("\n⚠️ 大部分修复成功，但仍有部分问题需要手动处理。")
        else:
            print("\n❌ 修复过程中遇到较多问题，建议检查系统配置和服务状态。")

if __name__ == "__main__":
    fixer = ProductionFixManager()
    fixer.run_production_fix()
