#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时测试工具
生命周期: 7天
创建目的: 最终验证全局账户选择器功能
依赖关系: Streamlit, 全局账户选择器组件
清理条件: 测试完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

def test_web_ui_syntax():
    """测试Web UI语法正确性"""
    print("🔍 测试Web UI语法...")
    
    try:
        import py_compile
        web_ui_path = project_root / "web_ui.py"
        py_compile.compile(str(web_ui_path), doraise=True)
        print("✅ Web UI语法检查通过")
        return True
    except Exception as e:
        print(f"❌ Web UI语法错误: {e}")
        return False

def test_web_ui_import():
    """测试Web UI导入"""
    print("🔍 测试Web UI导入...")
    
    try:
        import web_ui
        print("✅ Web UI导入成功")
        return True
    except Exception as e:
        print(f"❌ Web UI导入失败: {e}")
        return False

def test_global_selector_functions():
    """测试全局选择器函数"""
    print("🔍 测试全局选择器函数...")
    
    try:
        from ai_tool_20250718_maintenance_global_account_selector import (
            get_global_selected_account,
            set_global_selected_account,
            get_current_account_info,
            require_account_selection,
            get_accounts_with_favorites
        )
        
        print("✅ 全局选择器函数导入成功")
        
        # 测试获取账户
        accounts = get_accounts_with_favorites()
        print(f"✅ 成功获取 {len(accounts)} 个账户")
        
        if accounts:
            # 测试设置和获取全局账户
            test_account = accounts[0]
            set_global_selected_account(test_account)
            
            selected = get_global_selected_account()
            if selected and selected.id == test_account.id:
                print("✅ 全局账户设置和获取功能正常")
            else:
                print("⚠️ 全局账户状态管理可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 全局选择器函数测试失败: {e}")
        return False

def test_new_launch_center():
    """测试新版投放中心"""
    print("🔍 测试新版投放中心...")
    
    try:
        from ai_tool_20250718_maintenance_new_launch_center import render_new_launch_center_page
        print("✅ 新版投放中心导入成功")
        return True
    except Exception as e:
        print(f"❌ 新版投放中心测试失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            count = db.query(AdAccount).count()
            print(f"✅ 数据库连接成功，找到 {count} 个广告账户")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_streamlit_compatibility():
    """测试Streamlit兼容性"""
    print("🔍 测试Streamlit兼容性...")
    
    try:
        import streamlit as st
        print("✅ Streamlit导入成功")
        
        # 测试session_state功能
        if hasattr(st, 'session_state'):
            print("✅ Streamlit session_state功能可用")
        else:
            print("⚠️ Streamlit session_state功能不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit兼容性测试失败: {e}")
        return False

def check_file_integrity():
    """检查文件完整性"""
    print("🔍 检查文件完整性...")
    
    required_files = [
        "web_ui.py",
        "ai_tools/maintenance/ai_tool_20250718_maintenance_global_account_selector.py",
        "ai_tools/maintenance/ai_tool_20250718_maintenance_new_launch_center.py",
        "ai_tools/maintenance/ai_tool_20250718_maintenance_unified_account_selector.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def main():
    """主验证函数"""
    print("🧪 全局账户选择器最终验证")
    print("=" * 50)
    
    tests = [
        ("文件完整性检查", check_file_integrity),
        ("Web UI语法检查", test_web_ui_syntax),
        ("Web UI导入测试", test_web_ui_import),
        ("全局选择器函数测试", test_global_selector_functions),
        ("新版投放中心测试", test_new_launch_center),
        ("数据库连接测试", test_database_connection),
        ("Streamlit兼容性测试", test_streamlit_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 最终验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有验证测试通过！")
        print("\n🚀 系统状态:")
        print("✅ Web UI语法正确，可以正常启动")
        print("✅ 全局账户选择器功能完整")
        print("✅ 数据库连接正常")
        print("✅ 所有组件导入成功")
        
        print("\n🌐 可以启动Web UI:")
        print("   streamlit run web_ui.py")
        print("   访问: http://localhost:8501")
        
        print("\n🎯 功能验证建议:")
        print("1. 在左侧栏选择账户")
        print("2. 切换到不同功能页面")
        print("3. 验证账户状态保持")
        print("4. 测试各功能的正常工作")
        
        return True
    else:
        print("⚠️ 部分验证失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
