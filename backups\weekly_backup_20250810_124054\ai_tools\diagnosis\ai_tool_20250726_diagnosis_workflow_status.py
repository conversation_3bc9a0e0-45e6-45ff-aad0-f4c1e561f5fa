#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 诊断工作流运行状态
清理条件: 问题解决后可删除

工作流状态诊断工具
================

检查Celery任务调度和文件摄取状态。
"""

import os
import sys
import time
import glob
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


class WorkflowDiagnostic:
    """工作流诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.material_dir = os.path.join(project_root, 'workflow_assets', '01_materials_to_process', '缇萃百货')
        
    def check_file_directory(self):
        """检查文件目录状态"""
        logger.info("🔍 检查素材目录状态...")
        
        if not os.path.exists(self.material_dir):
            logger.error(f"❌ 素材目录不存在: {self.material_dir}")
            return False, []
        
        # 获取所有视频文件
        video_patterns = ['*.mp4', '*.mov', '*.avi', '*.mkv', '*.wmv']
        video_files = []
        
        for pattern in video_patterns:
            video_files.extend(glob.glob(os.path.join(self.material_dir, pattern)))
        
        logger.info(f"📊 发现 {len(video_files)} 个视频文件")
        
        if video_files:
            logger.info("📋 前5个文件示例:")
            for i, file_path in enumerate(video_files[:5]):
                filename = os.path.basename(file_path)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                logger.info(f"   {i+1}. {filename} ({size_mb:.1f}MB)")
        
        return True, video_files
    
    def check_database_records(self):
        """检查数据库记录状态"""
        logger.info("🔍 检查数据库记录状态...")
        
        try:
            with database_session() as db:
                # 统计各状态的记录数
                status_counts = db.query(
                    LocalCreative.status,
                    db.func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).all()
                
                logger.info("📊 数据库记录状态分布:")
                total_records = 0
                for status, count in status_counts:
                    logger.info(f"   - {status}: {count} 条记录")
                    total_records += count
                
                logger.info(f"📋 总记录数: {total_records}")
                
                # 检查最近创建的记录
                recent_records = db.query(LocalCreative).filter(
                    LocalCreative.created_at >= db.func.date('now', '-1 day')
                ).order_by(LocalCreative.created_at.desc()).limit(5).all()
                
                if recent_records:
                    logger.info("📋 最近24小时创建的记录:")
                    for record in recent_records:
                        logger.info(f"   - ID {record.id}: {record.filename} ({record.status}) - {record.created_at}")
                else:
                    logger.warning("⚠️ 最近24小时没有新记录创建")
                
                return status_counts
                
        except Exception as e:
            logger.error(f"❌ 检查数据库记录失败: {e}")
            return []
    
    def check_file_database_sync(self, video_files):
        """检查文件与数据库同步状态"""
        logger.info("🔍 检查文件与数据库同步状态...")
        
        try:
            with database_session() as db:
                # 获取所有数据库中的文件名
                db_filenames = set()
                all_records = db.query(LocalCreative.filename).filter(
                    LocalCreative.filename.isnot(None)
                ).all()
                
                for record in all_records:
                    db_filenames.add(record.filename)
                
                # 获取目录中的文件名
                dir_filenames = set()
                for file_path in video_files:
                    filename = os.path.basename(file_path)
                    dir_filenames.add(filename)
                
                # 分析同步状态
                in_db_not_dir = db_filenames - dir_filenames  # 数据库有，目录没有
                in_dir_not_db = dir_filenames - db_filenames  # 目录有，数据库没有
                in_both = db_filenames & dir_filenames        # 两者都有
                
                logger.info(f"📊 文件同步状态分析:")
                logger.info(f"   - 两者都有: {len(in_both)} 个文件")
                logger.info(f"   - 仅在数据库: {len(in_db_not_dir)} 个文件")
                logger.info(f"   - 仅在目录: {len(in_dir_not_db)} 个文件")
                
                if in_dir_not_db:
                    logger.info("📋 需要摄取的新文件（前10个）:")
                    for i, filename in enumerate(list(in_dir_not_db)[:10]):
                        logger.info(f"   {i+1}. {filename}")
                
                if in_db_not_dir:
                    logger.warning("⚠️ 数据库中存在但文件不存在的记录（前10个）:")
                    for i, filename in enumerate(list(in_db_not_dir)[:10]):
                        logger.warning(f"   {i+1}. {filename}")
                
                return {
                    'in_both': len(in_both),
                    'in_db_not_dir': len(in_db_not_dir),
                    'in_dir_not_db': len(in_dir_not_db),
                    'new_files': list(in_dir_not_db)
                }
                
        except Exception as e:
            logger.error(f"❌ 检查文件同步状态失败: {e}")
            return {}
    
    def check_celery_processes(self):
        """检查Celery进程状态"""
        logger.info("🔍 检查Celery进程状态...")
        
        try:
            # 检查Python进程
            import subprocess
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            
            python_processes = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'python.exe' in line:
                        python_processes.append(line.strip())
            
            logger.info(f"📊 发现 {len(python_processes)} 个Python进程")
            
            # 检查是否有celery相关进程
            celery_worker_running = False
            celery_beat_running = False
            
            for process in python_processes:
                if 'celery' in process.lower():
                    if 'worker' in process.lower():
                        celery_worker_running = True
                    elif 'beat' in process.lower():
                        celery_beat_running = True
            
            logger.info(f"📋 Celery进程状态:")
            logger.info(f"   - Celery Worker: {'✅ 运行中' if celery_worker_running else '❌ 未运行'}")
            logger.info(f"   - Celery Beat: {'✅ 运行中' if celery_beat_running else '❌ 未运行'}")
            
            if not celery_beat_running:
                logger.warning("⚠️ Celery Beat未运行！这是定时任务调度器，必须运行才能执行文件摄取")
                logger.info("💡 请运行: python run_celery_beat.py")
            
            return {
                'worker_running': celery_worker_running,
                'beat_running': celery_beat_running,
                'total_python_processes': len(python_processes)
            }
            
        except Exception as e:
            logger.error(f"❌ 检查Celery进程失败: {e}")
            return {}
    
    def generate_diagnosis_report(self, file_status, db_status, sync_status, process_status):
        """生成诊断报告"""
        report = f"""
工作流状态诊断报告
================

诊断时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

文件目录状态:
- 素材目录: {self.material_dir}
- 视频文件数量: {len(file_status[1]) if file_status[0] else 0}

数据库状态:
"""
        
        if db_status:
            for status, count in db_status:
                report += f"- {status}: {count} 条记录\n"
        
        report += f"""
文件同步状态:
- 两者都有: {sync_status.get('in_both', 0)} 个文件
- 仅在数据库: {sync_status.get('in_db_not_dir', 0)} 个文件
- 仅在目录: {sync_status.get('in_dir_not_db', 0)} 个文件

Celery进程状态:
- Celery Worker: {'运行中' if process_status.get('worker_running') else '未运行'}
- Celery Beat: {'运行中' if process_status.get('beat_running') else '未运行'}

问题诊断:
"""
        
        issues = []
        solutions = []
        
        if not process_status.get('beat_running'):
            issues.append("❌ Celery Beat未运行 - 定时任务无法调度")
            solutions.append("运行: python run_celery_beat.py")
        
        if sync_status.get('in_dir_not_db', 0) > 0:
            issues.append(f"⚠️ 有 {sync_status.get('in_dir_not_db')} 个文件未入库")
            solutions.append("等待文件摄取任务运行，或手动触发摄取")
        
        if sync_status.get('in_db_not_dir', 0) > 0:
            issues.append(f"⚠️ 有 {sync_status.get('in_db_not_dir')} 个僵尸记录")
            solutions.append("清理数据库中文件不存在的记录")
        
        if not issues:
            report += "✅ 未发现明显问题\n"
        else:
            for issue in issues:
                report += f"{issue}\n"
        
        report += f"""
解决方案:
"""
        for solution in solutions:
            report += f"💡 {solution}\n"
        
        report += f"""
下一步建议:
1. 确保Celery Beat和Worker都在运行
2. 观察文件摄取任务是否正常执行
3. 监控新文件是否被正确入库
4. 清理僵尸记录避免无效处理
"""
        
        return report


def main():
    """主函数"""
    logger.info("🚀 开始工作流状态诊断...")
    
    diagnostic = WorkflowDiagnostic()
    
    try:
        # 1. 检查文件目录
        file_status = diagnostic.check_file_directory()
        
        # 2. 检查数据库记录
        db_status = diagnostic.check_database_records()
        
        # 3. 检查文件与数据库同步
        sync_status = diagnostic.check_file_database_sync(file_status[1] if file_status[0] else [])
        
        # 4. 检查Celery进程
        process_status = diagnostic.check_celery_processes()
        
        # 5. 生成诊断报告
        report = diagnostic.generate_diagnosis_report(file_status, db_status, sync_status, process_status)
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'workflow_diagnosis_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 诊断报告已保存: {report_file}")
        logger.info("🎯 工作流诊断完成！")
        
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
