#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式锁管理器
提供Redis基础的分布式锁机制，确保并发操作安全
"""

import redis
import time
import uuid
from typing import Optional, Dict, Any, List
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger


class DistributedLock:
    """分布式锁实现"""
    
    def __init__(self, redis_client: redis.Redis, key: str, timeout: int = 60, retry_delay: float = 0.1):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.retry_delay = retry_delay
        self.identifier = str(uuid.uuid4())
        self.acquired = False
    
    def acquire(self, blocking: bool = True, timeout: Optional[int] = None) -> bool:
        """获取锁"""
        
        end_time = time.time() + (timeout or self.timeout)
        
        while True:
            # 尝试获取锁
            if self.redis.set(self.key, self.identifier, nx=True, ex=self.timeout):
                self.acquired = True
                logger.debug(f"🔒 获取分布式锁成功: {self.key}")
                return True
            
            if not blocking or time.time() > end_time:
                logger.warning(f"⏰ 获取分布式锁超时: {self.key}")
                return False
            
            time.sleep(self.retry_delay)
    
    def release(self) -> bool:
        """释放锁"""
        
        if not self.acquired:
            return False
        
        # 使用Lua脚本确保原子性释放
        lua_script = """
        if redis.call("get", KEYS[1]) == ARGV[1] then
            return redis.call("del", KEYS[1])
        else
            return 0
        end
        """
        
        try:
            result = self.redis.eval(lua_script, 1, self.key, self.identifier)
            if result:
                self.acquired = False
                logger.debug(f"🔓 释放分布式锁成功: {self.key}")
                return True
            else:
                logger.warning(f"⚠️ 释放分布式锁失败，锁可能已过期: {self.key}")
                return False
        except Exception as e:
            logger.error(f"❌ 释放分布式锁异常: {self.key}, 错误: {e}")
            return False
    
    @contextmanager
    def acquire_context(self, blocking: bool = True, timeout: Optional[int] = None):
        """上下文管理器方式使用锁"""
        
        acquired = self.acquire(blocking=blocking, timeout=timeout)
        try:
            yield acquired
        finally:
            if acquired:
                self.release()


class DistributedLockManager:
    """分布式锁管理器"""
    
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.active_locks = {}
    
    def create_lock(self, key: str, timeout: int = 60) -> DistributedLock:
        """创建分布式锁"""
        return DistributedLock(self.redis, key, timeout)
    
    @contextmanager
    def lock_context(self, key: str, timeout: int = 60, blocking: bool = True):
        """便捷的锁上下文管理器"""
        
        lock = self.create_lock(key, timeout)
        
        with lock.acquire_context(blocking=blocking, timeout=timeout) as acquired:
            if not acquired:
                raise RuntimeError(f"无法获取分布式锁: {key}")
            yield lock
    
    def get_lock_info(self, key: str) -> Dict[str, Any]:
        """获取锁信息"""
        
        lock_key = f"lock:{key}"
        
        try:
            value = self.redis.get(lock_key)
            ttl = self.redis.ttl(lock_key)
            
            if value is None:
                return {
                    'exists': False,
                    'key': key
                }
            
            return {
                'exists': True,
                'key': key,
                'identifier': value.decode('utf-8'),
                'ttl': ttl,
                'expires_at': time.time() + ttl if ttl > 0 else None
            }
            
        except Exception as e:
            logger.error(f"获取锁信息失败: {key}, 错误: {e}")
            return {
                'exists': False,
                'key': key,
                'error': str(e)
            }
    
    def cleanup_expired_locks(self) -> int:
        """清理过期锁"""
        
        try:
            # 获取所有锁键
            lock_keys = self.redis.keys("lock:*")
            cleaned_count = 0
            
            for key in lock_keys:
                ttl = self.redis.ttl(key)
                if ttl == -1:  # 没有过期时间的锁
                    self.redis.delete(key)
                    cleaned_count += 1
                    logger.info(f"🧹 清理无过期时间的锁: {key.decode('utf-8')}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期锁失败: {e}")
            return 0
    
    def force_release_lock(self, key: str) -> bool:
        """强制释放锁（管理员操作）"""
        
        lock_key = f"lock:{key}"
        
        try:
            result = self.redis.delete(lock_key)
            if result:
                logger.warning(f"🔓 强制释放锁: {key}")
                return True
            else:
                logger.info(f"锁不存在或已释放: {key}")
                return False
                
        except Exception as e:
            logger.error(f"强制释放锁失败: {key}, 错误: {e}")
            return False
    
    def get_all_locks(self) -> List[Dict[str, Any]]:
        """获取所有锁的信息"""
        
        try:
            lock_keys = self.redis.keys("lock:*")
            locks_info = []
            
            for key in lock_keys:
                key_str = key.decode('utf-8')
                lock_name = key_str.replace('lock:', '')
                lock_info = self.get_lock_info(lock_name)
                locks_info.append(lock_info)
            
            return locks_info
            
        except Exception as e:
            logger.error(f"获取所有锁信息失败: {e}")
            return []
    
    def test_redis_connection(self) -> Dict[str, Any]:
        """测试Redis连接"""
        
        try:
            # 测试基本连接
            self.redis.ping()
            
            # 测试设置和获取
            test_key = "test:connection"
            test_value = "test_value"
            
            self.redis.set(test_key, test_value, ex=10)
            retrieved_value = self.redis.get(test_key)
            
            if retrieved_value and retrieved_value.decode('utf-8') == test_value:
                self.redis.delete(test_key)
                return {
                    'success': True,
                    'message': 'Redis连接正常',
                    'redis_info': {
                        'version': self.redis.info()['redis_version'],
                        'connected_clients': self.redis.info()['connected_clients']
                    }
                }
            else:
                return {
                    'success': False,
                    'message': 'Redis读写测试失败'
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'Redis连接失败: {str(e)}'
            }


class WorkflowLockManager:
    """工作流专用锁管理器"""
    
    def __init__(self, distributed_lock_manager: DistributedLockManager):
        self.lock_manager = distributed_lock_manager
    
    @contextmanager
    def creative_lock(self, creative_id: int, timeout: int = 30):
        """素材操作锁"""
        with self.lock_manager.lock_context(f"creative:{creative_id}", timeout=timeout):
            yield
    
    @contextmanager
    def campaign_lock(self, campaign_id: str, timeout: int = 30):
        """计划操作锁"""
        with self.lock_manager.lock_context(f"campaign:{campaign_id}", timeout=timeout):
            yield
    
    @contextmanager
    def account_lock(self, account_id: str, timeout: int = 30):
        """账户操作锁"""
        with self.lock_manager.lock_context(f"account:{account_id}", timeout=timeout):
            yield
    
    @contextmanager
    def batch_operation_lock(self, operation_type: str, batch_id: str, timeout: int = 60):
        """批量操作锁"""
        with self.lock_manager.lock_context(f"batch:{operation_type}:{batch_id}", timeout=timeout):
            yield
    
    @contextmanager
    def workflow_stage_lock(self, stage_name: str, timeout: int = 120):
        """工作流阶段锁"""
        with self.lock_manager.lock_context(f"workflow:{stage_name}", timeout=timeout):
            yield


def create_redis_client(host: str = 'localhost', port: int = 6379, db: int = 0, 
                       password: Optional[str] = None) -> redis.Redis:
    """创建Redis客户端"""
    
    try:
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password,
            decode_responses=False,  # 保持字节格式以支持Lua脚本
            socket_timeout=5,
            socket_connect_timeout=5,
            retry_on_timeout=True
        )
        
        # 测试连接
        client.ping()
        logger.info(f"✅ Redis连接成功: {host}:{port}")
        
        return client
        
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {host}:{port}, 错误: {e}")
        raise


def get_default_lock_manager() -> Optional[DistributedLockManager]:
    """获取默认的分布式锁管理器"""
    
    try:
        # 尝试连接本地Redis
        redis_client = create_redis_client()
        return DistributedLockManager(redis_client)
        
    except Exception as e:
        logger.warning(f"无法创建默认锁管理器: {e}")
        return None
