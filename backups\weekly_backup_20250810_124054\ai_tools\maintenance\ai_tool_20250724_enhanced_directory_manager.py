#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 为web_ui.py提供增强的批量目录配置管理功能
清理条件: 功能集成完成后可归档，建议保留作为参考
"""

import os
import yaml
import pandas as pd
from typing import List, Dict, Tuple, Optional
from pathlib import Path
import streamlit as st

def validate_directory_path(path: str) -> Tuple[bool, str]:
    """
    验证目录路径的有效性
    
    Args:
        path: 目录路径
        
    Returns:
        (是否有效, 错误信息)
    """
    if not path or not path.strip():
        return False, "路径不能为空"
    
    path = path.strip()
    
    # 检查路径格式
    if path.startswith('\\\\'):
        # 网络路径
        if len(path.split('\\')) < 4:
            return False, "网络路径格式不正确，应为 \\\\server\\share\\path"
    elif ':' in path:
        # 本地路径
        if not (len(path) >= 3 and path[1] == ':'):
            return False, "本地路径格式不正确，应为 C:\\path\\to\\directory"
    else:
        return False, "路径格式不正确，应为网络路径(\\\\server\\share)或本地路径(C:\\path)"
    
    # 检查路径中的非法字符
    illegal_chars = ['<', '>', '|', '*', '?', '"']
    for char in illegal_chars:
        if char in path:
            return False, f"路径包含非法字符: {char}"
    
    return True, ""

def batch_validate_directories(directories: List[str]) -> Dict[str, Dict]:
    """
    批量验证目录路径
    
    Args:
        directories: 目录路径列表
        
    Returns:
        验证结果字典
    """
    results = {}
    
    for i, directory in enumerate(directories):
        is_valid, error_msg = validate_directory_path(directory)
        
        results[f"目录_{i+1}"] = {
            "路径": directory,
            "状态": "✅ 有效" if is_valid else "❌ 无效",
            "错误信息": error_msg if not is_valid else "",
            "类型": "网络路径" if directory.startswith('\\\\') else "本地路径" if ':' in directory else "未知"
        }
    
    return results

def render_enhanced_directory_manager(current_dirs: List[str], update_callback) -> None:
    """
    渲染增强的目录管理界面
    
    Args:
        current_dirs: 当前目录列表
        update_callback: 更新配置的回调函数
    """
    st.markdown("### 📁 增强目录配置管理")
    
    # 选择管理模式
    management_mode = st.radio(
        "选择管理模式",
        ["📝 表格批量编辑", "📋 文本批量导入", "🔧 逐个编辑", "📊 配置分析"],
        horizontal=True
    )
    
    if management_mode == "📝 表格批量编辑":
        render_table_batch_editor(current_dirs, update_callback)
    elif management_mode == "📋 文本批量导入":
        render_text_batch_importer(current_dirs, update_callback)
    elif management_mode == "🔧 逐个编辑":
        render_individual_editor(current_dirs, update_callback)
    elif management_mode == "📊 配置分析":
        render_configuration_analysis(current_dirs)

def render_table_batch_editor(current_dirs: List[str], update_callback) -> None:
    """渲染表格批量编辑界面"""
    st.markdown("#### 📝 表格批量编辑模式")
    st.info("💡 在表格中直接编辑目录路径，支持添加、删除和修改多个目录")
    
    # 准备表格数据
    if current_dirs:
        df_data = []
        for i, directory in enumerate(current_dirs):
            is_valid, error_msg = validate_directory_path(directory)
            df_data.append({
                "序号": i + 1,
                "目录路径": directory,
                "状态": "✅ 有效" if is_valid else "❌ 无效",
                "类型": "网络路径" if directory.startswith('\\\\') else "本地路径",
                "备注": error_msg if not is_valid else "正常"
            })
        
        # 显示当前配置表格
        st.markdown("##### 📋 当前目录配置")
        df = pd.DataFrame(df_data)
        st.dataframe(df, use_container_width=True, hide_index=True)
    else:
        st.warning("⚠️ 当前未配置任何源目录")
    
    # 批量编辑区域
    st.markdown("##### ✏️ 批量编辑区域")
    
    # 使用文本区域进行批量编辑
    edit_text = st.text_area(
        "编辑目录列表（每行一个目录）",
        value="\n".join(current_dirs),
        height=200,
        help="每行输入一个目录路径，支持网络路径和本地路径"
    )
    
    # 实时验证
    if edit_text.strip():
        new_dirs = [line.strip() for line in edit_text.split('\n') if line.strip()]
        
        # 显示验证结果
        if new_dirs != current_dirs:
            st.markdown("##### 🔍 变更预览")
            validation_results = batch_validate_directories(new_dirs)
            
            # 统计信息
            total_dirs = len(new_dirs)
            valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
            invalid_dirs = total_dirs - valid_dirs
            
            col_stats1, col_stats2, col_stats3 = st.columns(3)
            with col_stats1:
                st.metric("总目录数", total_dirs, total_dirs - len(current_dirs))
            with col_stats2:
                st.metric("有效目录", valid_dirs)
            with col_stats3:
                st.metric("无效目录", invalid_dirs)
            
            # 显示验证结果表格
            if validation_results:
                validation_df = pd.DataFrame.from_dict(validation_results, orient='index')
                st.dataframe(validation_df, use_container_width=True)
            
            # 保存按钮
            col_save, col_cancel = st.columns(2)
            with col_save:
                if st.button("💾 保存更改", use_container_width=True, type="primary"):
                    if invalid_dirs == 0:
                        if update_callback(new_dirs):
                            st.success(f"✅ 成功更新 {total_dirs} 个目录配置")
                            st.rerun()
                        else:
                            st.error("❌ 保存失败")
                    else:
                        st.error(f"❌ 存在 {invalid_dirs} 个无效目录，请修正后再保存")
            
            with col_cancel:
                if st.button("🔄 重置", use_container_width=True):
                    st.rerun()

def render_text_batch_importer(current_dirs: List[str], update_callback) -> None:
    """渲染文本批量导入界面"""
    st.markdown("#### 📋 文本批量导入模式")
    st.info("💡 支持从文本文件导入、Excel导入或直接粘贴目录列表")
    
    import_method = st.selectbox(
        "选择导入方式",
        ["📝 直接粘贴", "📄 文本文件上传", "📊 Excel文件上传"]
    )
    
    new_dirs = []
    
    if import_method == "📝 直接粘贴":
        paste_text = st.text_area(
            "粘贴目录列表",
            height=150,
            placeholder="每行一个目录路径，例如：\n\\\\server\\share\\path1\n\\\\server\\share\\path2\nD:\\local\\path"
        )
        
        if paste_text.strip():
            new_dirs = [line.strip() for line in paste_text.split('\n') if line.strip()]
    
    elif import_method == "📄 文本文件上传":
        uploaded_file = st.file_uploader(
            "上传文本文件",
            type=['txt'],
            help="文本文件中每行一个目录路径"
        )
        
        if uploaded_file is not None:
            try:
                content = uploaded_file.read().decode('utf-8')
                new_dirs = [line.strip() for line in content.split('\n') if line.strip()]
                st.success(f"✅ 成功读取 {len(new_dirs)} 个目录路径")
            except Exception as e:
                st.error(f"❌ 文件读取失败: {e}")
    
    elif import_method == "📊 Excel文件上传":
        uploaded_file = st.file_uploader(
            "上传Excel文件",
            type=['xlsx', 'xls'],
            help="Excel文件第一列应包含目录路径"
        )
        
        if uploaded_file is not None:
            try:
                df = pd.read_excel(uploaded_file)
                if not df.empty:
                    # 取第一列作为目录路径
                    new_dirs = [str(path).strip() for path in df.iloc[:, 0].tolist() if str(path).strip() != 'nan']
                    st.success(f"✅ 成功读取 {len(new_dirs)} 个目录路径")
                    
                    # 显示预览
                    st.markdown("##### 📋 导入预览")
                    preview_df = pd.DataFrame({"目录路径": new_dirs[:10]})  # 只显示前10个
                    st.dataframe(preview_df, use_container_width=True)
                    if len(new_dirs) > 10:
                        st.info(f"显示前10个目录，总共 {len(new_dirs)} 个")
            except Exception as e:
                st.error(f"❌ Excel文件读取失败: {e}")
    
    # 处理导入的目录
    if new_dirs:
        st.markdown("##### 🔍 导入验证")
        
        # 验证导入的目录
        validation_results = batch_validate_directories(new_dirs)
        
        # 统计信息
        total_dirs = len(new_dirs)
        valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
        invalid_dirs = total_dirs - valid_dirs
        duplicate_dirs = len(new_dirs) - len(set(new_dirs))
        
        col_stats1, col_stats2, col_stats3, col_stats4 = st.columns(4)
        with col_stats1:
            st.metric("导入目录数", total_dirs)
        with col_stats2:
            st.metric("有效目录", valid_dirs)
        with col_stats3:
            st.metric("无效目录", invalid_dirs)
        with col_stats4:
            st.metric("重复目录", duplicate_dirs)
        
        # 显示验证结果
        if validation_results:
            validation_df = pd.DataFrame.from_dict(validation_results, orient='index')
            st.dataframe(validation_df, use_container_width=True)
        
        # 导入选项
        st.markdown("##### ⚙️ 导入选项")
        col_option1, col_option2 = st.columns(2)
        
        with col_option1:
            import_mode = st.radio(
                "导入模式",
                ["🔄 替换现有配置", "➕ 追加到现有配置"],
                help="替换：清空现有配置并导入新配置\n追加：在现有配置基础上添加新目录"
            )
        
        with col_option2:
            auto_dedupe = st.checkbox("🔧 自动去重", value=True, help="自动移除重复的目录路径")
            skip_invalid = st.checkbox("⚠️ 跳过无效目录", value=True, help="只导入有效的目录路径")
        
        # 执行导入
        col_import, col_cancel = st.columns(2)
        with col_import:
            if st.button("📥 执行导入", use_container_width=True, type="primary"):
                try:
                    # 处理导入的目录
                    processed_dirs = new_dirs.copy()
                    
                    # 去重
                    if auto_dedupe:
                        processed_dirs = list(dict.fromkeys(processed_dirs))  # 保持顺序的去重
                    
                    # 跳过无效目录
                    if skip_invalid:
                        valid_processed_dirs = []
                        for directory in processed_dirs:
                            is_valid, _ = validate_directory_path(directory)
                            if is_valid:
                                valid_processed_dirs.append(directory)
                        processed_dirs = valid_processed_dirs
                    
                    # 根据导入模式处理
                    if import_mode == "🔄 替换现有配置":
                        final_dirs = processed_dirs
                    else:  # 追加模式
                        final_dirs = current_dirs + processed_dirs
                        if auto_dedupe:
                            final_dirs = list(dict.fromkeys(final_dirs))
                    
                    # 执行更新
                    if update_callback(final_dirs):
                        st.success(f"✅ 成功导入 {len(processed_dirs)} 个目录，当前总计 {len(final_dirs)} 个目录")
                        st.rerun()
                    else:
                        st.error("❌ 导入失败")
                        
                except Exception as e:
                    st.error(f"❌ 导入过程中发生错误: {e}")
        
        with col_cancel:
            if st.button("❌ 取消导入", use_container_width=True):
                st.rerun()

def render_individual_editor(current_dirs: List[str], update_callback) -> None:
    """渲染逐个编辑界面（改进版）"""
    st.markdown("#### 🔧 逐个编辑模式")
    st.info("💡 逐个编辑每个目录，支持拖拽排序和快速操作")
    
    if not current_dirs:
        st.warning("⚠️ 当前未配置任何源目录")
        return
    
    # 使用会话状态管理编辑状态
    if 'enhanced_edit_mode' not in st.session_state:
        st.session_state.enhanced_edit_mode = {}
    
    # 显示目录列表
    st.markdown("##### 📋 目录列表")
    
    for i, directory in enumerate(current_dirs):
        with st.container():
            col_index, col_path, col_status, col_actions = st.columns([0.5, 4, 1, 2])
            
            with col_index:
                st.markdown(f"**{i+1}**")
            
            with col_path:
                if st.session_state.enhanced_edit_mode.get(i, False):
                    # 编辑模式
                    edited_dir = st.text_input(
                        f"编辑目录 {i+1}",
                        value=directory,
                        key=f"enhanced_edit_input_{i}",
                        label_visibility="collapsed"
                    )
                else:
                    # 显示模式
                    st.code(directory, language=None)
            
            with col_status:
                is_valid, _ = validate_directory_path(directory)
                st.markdown("✅" if is_valid else "❌")
            
            with col_actions:
                if st.session_state.enhanced_edit_mode.get(i, False):
                    # 编辑模式的操作按钮
                    col_save, col_cancel = st.columns(2)
                    with col_save:
                        if st.button("💾", key=f"enhanced_save_{i}", help="保存"):
                            edited_dir = st.session_state.get(f"enhanced_edit_input_{i}", directory)
                            if edited_dir.strip() != directory:
                                new_dirs = current_dirs.copy()
                                new_dirs[i] = edited_dir.strip()
                                if update_callback(new_dirs):
                                    st.success(f"✅ 已更新目录 {i+1}")
                                    st.session_state.enhanced_edit_mode[i] = False
                                    st.rerun()
                    with col_cancel:
                        if st.button("❌", key=f"enhanced_cancel_{i}", help="取消"):
                            st.session_state.enhanced_edit_mode[i] = False
                            st.rerun()
                else:
                    # 显示模式的操作按钮
                    col_edit, col_delete, col_move = st.columns(3)
                    with col_edit:
                        if st.button("✏️", key=f"enhanced_edit_{i}", help="编辑"):
                            st.session_state.enhanced_edit_mode[i] = True
                            st.rerun()
                    with col_delete:
                        if st.button("🗑️", key=f"enhanced_delete_{i}", help="删除"):
                            new_dirs = [d for j, d in enumerate(current_dirs) if j != i]
                            if update_callback(new_dirs):
                                st.success(f"✅ 已删除目录 {i+1}")
                                st.rerun()
                    with col_move:
                        # 移动按钮（上移/下移）
                        if i > 0:
                            if st.button("⬆️", key=f"enhanced_up_{i}", help="上移"):
                                new_dirs = current_dirs.copy()
                                new_dirs[i], new_dirs[i-1] = new_dirs[i-1], new_dirs[i]
                                if update_callback(new_dirs):
                                    st.rerun()
                        if i < len(current_dirs) - 1:
                            if st.button("⬇️", key=f"enhanced_down_{i}", help="下移"):
                                new_dirs = current_dirs.copy()
                                new_dirs[i], new_dirs[i+1] = new_dirs[i+1], new_dirs[i]
                                if update_callback(new_dirs):
                                    st.rerun()
            
            st.divider()

def render_configuration_analysis(current_dirs: List[str]) -> None:
    """渲染配置分析界面"""
    st.markdown("#### 📊 配置分析模式")
    st.info("💡 分析当前目录配置的统计信息和潜在问题")
    
    if not current_dirs:
        st.warning("⚠️ 当前未配置任何源目录")
        return
    
    # 基本统计
    st.markdown("##### 📈 基本统计")
    col_stats1, col_stats2, col_stats3, col_stats4 = st.columns(4)
    
    total_dirs = len(current_dirs)
    network_dirs = sum(1 for d in current_dirs if d.startswith('\\\\'))
    local_dirs = total_dirs - network_dirs
    
    validation_results = batch_validate_directories(current_dirs)
    valid_dirs = sum(1 for result in validation_results.values() if "✅" in result["状态"])
    invalid_dirs = total_dirs - valid_dirs
    
    with col_stats1:
        st.metric("总目录数", total_dirs)
    with col_stats2:
        st.metric("网络路径", network_dirs)
    with col_stats3:
        st.metric("本地路径", local_dirs)
    with col_stats4:
        st.metric("有效目录", valid_dirs)
    
    # 详细分析表格
    st.markdown("##### 📋 详细分析")
    if validation_results:
        analysis_df = pd.DataFrame.from_dict(validation_results, orient='index')
        st.dataframe(analysis_df, use_container_width=True)
    
    # 问题检测
    st.markdown("##### ⚠️ 问题检测")
    issues = []
    
    if invalid_dirs > 0:
        issues.append(f"发现 {invalid_dirs} 个无效目录路径")
    
    # 检查重复目录
    duplicates = len(current_dirs) - len(set(current_dirs))
    if duplicates > 0:
        issues.append(f"发现 {duplicates} 个重复目录")
    
    # 检查空目录
    empty_dirs = sum(1 for d in current_dirs if not d.strip())
    if empty_dirs > 0:
        issues.append(f"发现 {empty_dirs} 个空目录")
    
    if issues:
        for issue in issues:
            st.error(f"❌ {issue}")
    else:
        st.success("✅ 未发现配置问题")
    
    # 建议
    st.markdown("##### 💡 优化建议")
    suggestions = []
    
    if network_dirs == 0:
        suggestions.append("考虑添加网络路径以支持多机器素材收集")
    
    if local_dirs == 0:
        suggestions.append("考虑添加本地路径作为备用素材源")
    
    if total_dirs < 3:
        suggestions.append("建议配置更多素材源目录以提高收集效率")
    
    if total_dirs > 20:
        suggestions.append("目录数量较多，建议定期清理不活跃的目录")
    
    if suggestions:
        for suggestion in suggestions:
            st.info(f"💡 {suggestion}")
    else:
        st.success("✅ 当前配置较为合理")

# 导出功能
def export_directory_config(directories: List[str], format_type: str = "txt") -> str:
    """
    导出目录配置
    
    Args:
        directories: 目录列表
        format_type: 导出格式 (txt, csv, json)
        
    Returns:
        导出内容
    """
    if format_type == "txt":
        return "\n".join(directories)
    elif format_type == "csv":
        import io
        output = io.StringIO()
        pd.DataFrame({"目录路径": directories}).to_csv(output, index=False)
        return output.getvalue()
    elif format_type == "json":
        import json
        return json.dumps({"source_directories": directories}, indent=2, ensure_ascii=False)
    else:
        return "\n".join(directories)
