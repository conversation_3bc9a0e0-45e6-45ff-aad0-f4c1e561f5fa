#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 智能提审调度器 - 按广告户分组和延迟提审优化
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Tuple
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class SmartAppealScheduler:
    """智能提审调度器"""
    
    def __init__(self, app_settings: dict):
        self.app_settings = app_settings
        self.min_plan_age_minutes = app_settings.get('appeal_scheduler', {}).get('min_plan_age_minutes', 60)
        self.batch_size_per_account = app_settings.get('appeal_scheduler', {}).get('batch_size_per_account', 10)
        
    def get_plans_ready_for_appeal(self) -> Dict[str, List[dict]]:
        """
        获取准备提审的计划，按广告户分组
        
        Returns:
            Dict[account_key, List[plan_info]]: 按广告户分组的计划列表
        """
        logger.info("🔍 查找准备提审的计划...")
        
        try:
            sys.path.insert(0, str(project_root / 'src'))
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            from sqlalchemy.orm import joinedload
            from sqlalchemy import or_, and_
            
            db = SessionLocal()
            try:
                # 计算时间阈值
                now = datetime.now(timezone.utc)
                min_creation_time = now - timedelta(minutes=self.min_plan_age_minutes)
                
                logger.info(f"⏰ 时间阈值: 只处理 {self.min_plan_age_minutes} 分钟前创建的计划")
                logger.info(f"   当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"   最早创建时间: {min_creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 查询条件：
                # 1. 状态为AUDITING
                # 2. 未提审过（appeal_status为空且first_appeal_at为空）
                # 3. 创建时间超过阈值
                # 4. 排除提审失败次数过多的计划
                plans_query = db.query(Campaign).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).filter(
                    Campaign.status == 'AUDITING',
                    # 确保未提审过
                    Campaign.appeal_status.is_(None),
                    Campaign.first_appeal_at.is_(None),
                    or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0),
                    # 创建时间超过阈值
                    Campaign.created_at <= min_creation_time
                ).order_by(
                    # 按创建时间排序
                    Campaign.created_at
                )
                
                plans = plans_query.all()
                
                logger.info(f"📊 找到 {len(plans)} 个符合条件的计划")
                
                # 按广告户分组
                grouped_plans = {}
                
                for plan in plans:
                    principal_name = plan.account.principal.name
                    account_id = plan.account.account_id_qc
                    account_key = f"{principal_name}_{account_id}"
                    
                    if account_key not in grouped_plans:
                        grouped_plans[account_key] = []
                    
                    # 计算计划年龄
                    plan_age_minutes = (now - plan.created_at.replace(tzinfo=timezone.utc)).total_seconds() / 60
                    
                    plan_info = {
                        'campaign_id': plan.campaign_id_qc,
                        'principal_name': principal_name,
                        'account_id': account_id,
                        'created_at': plan.created_at,
                        'age_minutes': int(plan_age_minutes)
                    }
                    
                    grouped_plans[account_key].append(plan_info)
                
                # 统计信息
                logger.info(f"📋 按广告户分组结果:")
                total_plans = 0
                for account_key, account_plans in grouped_plans.items():
                    principal_name = account_plans[0]['principal_name']
                    account_id = account_plans[0]['account_id']
                    logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划")
                    total_plans += len(account_plans)
                
                logger.info(f"📊 总计: {len(grouped_plans)} 个广告户，{total_plans} 个计划")
                
                return grouped_plans
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 查找准备提审的计划失败: {e}")
            return {}
    
    def get_failed_plans_for_retry(self) -> Dict[str, List[dict]]:
        """
        获取需要重试的失败计划，按广告户分组
        
        Returns:
            Dict[account_key, List[plan_info]]: 按广告户分组的失败计划列表
        """
        logger.info("🔍 查找需要重试的失败计划...")
        
        try:
            sys.path.insert(0, str(project_root / 'src'))
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            from sqlalchemy.orm import joinedload
            
            db = SessionLocal()
            try:
                # 查询提审失败的计划
                # 排除"查询到该内容暂无广告审核建议"这种可能是计划太新的错误
                failed_plans = db.query(Campaign).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).filter(
                    Campaign.status == 'AUDITING',
                    Campaign.appeal_status == 'submission_failed',
                    # 排除明显是计划太新导致的失败
                    ~Campaign.appeal_error_message.like('%暂无广告审核建议%'),
                    # 只重试失败次数不超过3次的
                    or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count <= 3)
                ).order_by(
                    Campaign.account.has(AdAccount.principal.has(Principal.name)),
                    Campaign.account.has(AdAccount.account_id_qc),
                    Campaign.created_at
                ).all()
                
                logger.info(f"📊 找到 {len(failed_plans)} 个需要重试的失败计划")
                
                # 按广告户分组
                grouped_plans = {}
                
                for plan in failed_plans:
                    principal_name = plan.account.principal.name
                    account_id = plan.account.account_id_qc
                    account_key = f"{principal_name}_{account_id}"
                    
                    if account_key not in grouped_plans:
                        grouped_plans[account_key] = []
                    
                    plan_info = {
                        'campaign_id': plan.campaign_id_qc,
                        'principal_name': principal_name,
                        'account_id': account_id,
                        'created_at': plan.created_at,
                        'error_message': plan.appeal_error_message,
                        'attempt_count': plan.appeal_attempt_count or 0
                    }
                    
                    grouped_plans[account_key].append(plan_info)
                
                # 统计信息
                if grouped_plans:
                    logger.info(f"📋 失败计划分组结果:")
                    for account_key, account_plans in grouped_plans.items():
                        principal_name = account_plans[0]['principal_name']
                        account_id = account_plans[0]['account_id']
                        logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个失败计划")
                
                return grouped_plans
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 查找失败计划失败: {e}")
            return {}
    
    def execute_smart_appeal_batch(self, grouped_plans: Dict[str, List[dict]]) -> Dict[str, int]:
        """
        执行智能批量提审
        
        Args:
            grouped_plans: 按广告户分组的计划
            
        Returns:
            Dict[account_key, success_count]: 每个广告户的成功提审数量
        """
        logger.info("🚀 开始执行智能批量提审...")
        
        if not grouped_plans:
            logger.info("✅ 没有需要提审的计划")
            return {}
        
        try:
            sys.path.insert(0, str(project_root / 'src'))
            from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
            from qianchuan_aw.database.database import SessionLocal
            
            # 创建提审服务
            appeal_service = create_production_appeal_service(self.app_settings)
            
            results = {}
            total_accounts = len(grouped_plans)
            
            for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
                principal_name = account_plans[0]['principal_name']
                account_id = account_plans[0]['account_id']
                
                logger.info(f"\n📋 处理第 {i}/{total_accounts} 个广告户")
                logger.info(f"🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划")
                logger.info("="*60)
                
                # 限制每批次的计划数量
                batch_plans = account_plans[:self.batch_size_per_account]
                if len(account_plans) > self.batch_size_per_account:
                    logger.info(f"⚠️ 计划数量超过批次限制，本次处理前 {self.batch_size_per_account} 个")
                
                # 准备提审数据
                plans_data = []
                for plan in batch_plans:
                    plans_data.append({
                        'campaign_id': plan['campaign_id'],
                        'principal_name': plan['principal_name'],
                        'account_id': plan['account_id']
                    })
                
                try:
                    # 执行批量提审（同一广告户的所有计划在一个浏览器会话中处理）
                    appeal_results = appeal_service.batch_appeal_all_plans(plans_data)
                    
                    # 更新数据库
                    db = SessionLocal()
                    try:
                        updated_count = appeal_service.update_database_with_results(db, appeal_results)
                        
                        # 统计成功数量
                        success_count = sum(1 for result in appeal_results if result['success'])
                        results[account_key] = success_count
                        
                        logger.info(f"📊 广告户 {principal_name} 处理完成: {success_count}/{len(batch_plans)} 个成功")
                        
                    finally:
                        db.close()
                    
                except Exception as e:
                    logger.error(f"❌ 广告户 {principal_name} 处理失败: {e}")
                    results[account_key] = 0
                
                # 广告户之间稍微等待，避免过于频繁
                if i < total_accounts:
                    logger.info("⏳ 等待5秒后处理下一个广告户...")
                    time.sleep(5)
            
            # 生成总结报告
            total_success = sum(results.values())
            total_plans = sum(len(plans) for plans in grouped_plans.values())
            
            logger.info(f"\n🎉 智能批量提审完成!")
            logger.info(f"📊 总体结果: {total_success}/{total_plans} 个计划提审成功")
            logger.info(f"📊 广告户处理: {len(results)}/{total_accounts} 个广告户")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 执行智能批量提审失败: {e}")
            return {}

def main():
    """主函数"""
    logger.info("🎯 智能提审调度器")
    logger.info("="*80)
    logger.info("🔧 优化特性:")
    logger.info("1. 按广告户分组批量提审，减少浏览器启动次数")
    logger.info("2. 新建计划延迟提审，避免'无审核建议'错误")
    logger.info("3. 智能重试失败的提审")
    logger.info("="*80)
    
    try:
        # 加载配置
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 创建智能调度器
        scheduler = SmartAppealScheduler(app_settings)
        
        # 1. 处理新计划
        logger.info("\n🆕 第一阶段: 处理新建计划")
        logger.info("-" * 40)
        
        new_plans = scheduler.get_plans_ready_for_appeal()
        if new_plans:
            new_results = scheduler.execute_smart_appeal_batch(new_plans)
            logger.info(f"✅ 新计划处理完成: {sum(new_results.values())} 个成功")
        else:
            logger.info("✅ 没有符合条件的新计划")
        
        # 2. 处理失败重试
        logger.info("\n🔄 第二阶段: 重试失败计划")
        logger.info("-" * 40)
        
        failed_plans = scheduler.get_failed_plans_for_retry()
        if failed_plans:
            retry_results = scheduler.execute_smart_appeal_batch(failed_plans)
            logger.info(f"✅ 失败重试完成: {sum(retry_results.values())} 个成功")
        else:
            logger.info("✅ 没有需要重试的失败计划")
        
        # 3. 生成总结报告
        total_new = sum(new_results.values()) if new_plans else 0
        total_retry = sum(retry_results.values()) if failed_plans else 0
        total_success = total_new + total_retry
        
        logger.success(f"\n🎉 智能提审调度完成!")
        logger.info(f"📊 新计划提审: {total_new} 个成功")
        logger.info(f"📊 失败重试: {total_retry} 个成功")
        logger.info(f"📊 总计: {total_success} 个计划提审成功")
        
        if total_success > 0:
            logger.info("\n💡 优化效果:")
            logger.info("- 按广告户分组，减少浏览器启动次数")
            logger.info("- 延迟提审，避免'无审核建议'错误")
            logger.info("- 智能重试，提高成功率")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能提审调度失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 智能提审调度器执行成功！")
        logger.info("💡 系统现在更加智能和高效")
    else:
        logger.error("\n❌ 智能提审调度器执行失败")
    
    sys.exit(0 if success else 1)
