#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化系统自动化运维流程，实现定期清理、备份和故障恢复
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import os
import sys
import json
import shutil
import schedule
import time
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session


class AutomatedOperations:
    """自动化运维系统"""
    
    def __init__(self):
        self.backup_dir = project_root / 'backups'
        self.backup_dir.mkdir(exist_ok=True)
        
        self.maintenance_config = {
            'daily_cleanup_enabled': True,
            'weekly_backup_enabled': True,
            'health_check_enabled': True,
            'auto_recovery_enabled': True,
            'retention_days': {
                'ai_temp': 7,
                'ai_reports': 30,
                'backups': 30,
                'logs': 14
            }
        }
    
    def run_daily_maintenance(self) -> Dict[str, Any]:
        """运行日常维护任务"""
        logger.info("🔧 开始执行日常维护任务...")
        
        results = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'tasks_executed': [],
            'tasks_failed': [],
            'total_tasks': 0,
            'success_rate': 0
        }
        
        maintenance_tasks = [
            ('AI文件清理', self._cleanup_ai_files),
            ('日志文件清理', self._cleanup_log_files),
            ('健康检查', self._run_health_check),
            ('告警检查', self._run_alert_check),
            ('工作流堆积检查', self._check_workflow_backlog)
        ]
        
        results['total_tasks'] = len(maintenance_tasks)
        
        for task_name, task_func in maintenance_tasks:
            try:
                logger.info(f"执行任务: {task_name}")
                task_result = task_func()
                results['tasks_executed'].append({
                    'task': task_name,
                    'status': 'SUCCESS',
                    'result': task_result
                })
                logger.info(f"✅ 任务完成: {task_name}")
                
            except Exception as e:
                logger.error(f"❌ 任务失败: {task_name}, 错误: {e}")
                results['tasks_failed'].append({
                    'task': task_name,
                    'status': 'FAILED',
                    'error': str(e)
                })
        
        # 计算成功率
        success_count = len(results['tasks_executed'])
        results['success_rate'] = (success_count / results['total_tasks']) * 100 if results['total_tasks'] > 0 else 0
        
        logger.info(f"✅ 日常维护任务完成: 成功{success_count}/{results['total_tasks']}个任务")
        
        # 保存维护报告
        self._save_maintenance_report(results)
        
        return results
    
    def _cleanup_ai_files(self) -> Dict[str, Any]:
        """清理AI文件"""
        logger.debug("清理AI文件...")
        
        try:
            # 运行AI文件管理器清理
            result = subprocess.run([
                sys.executable, 
                str(project_root / 'tools' / 'ai_file_manager.py'),
                'cleanup'
            ], capture_output=True, text=True, cwd=str(project_root))
            
            if result.returncode == 0:
                return {
                    'status': 'SUCCESS',
                    'message': 'AI文件清理完成',
                    'output': result.stdout
                }
            else:
                return {
                    'status': 'FAILED',
                    'message': 'AI文件清理失败',
                    'error': result.stderr
                }
                
        except Exception as e:
            return {
                'status': 'FAILED',
                'message': f'AI文件清理异常: {str(e)}'
            }
    
    def _cleanup_log_files(self) -> Dict[str, Any]:
        """清理日志文件"""
        logger.debug("清理日志文件...")
        
        try:
            logs_dir = project_root / 'logs'
            if not logs_dir.exists():
                return {'status': 'SKIPPED', 'message': '日志目录不存在'}
            
            cutoff_date = datetime.now() - timedelta(days=self.maintenance_config['retention_days']['logs'])
            cleaned_files = 0
            
            for log_file in logs_dir.rglob('*.log'):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    cleaned_files += 1
            
            return {
                'status': 'SUCCESS',
                'message': f'清理了{cleaned_files}个过期日志文件',
                'cleaned_files': cleaned_files
            }
            
        except Exception as e:
            return {
                'status': 'FAILED',
                'message': f'日志文件清理失败: {str(e)}'
            }
    
    def _run_health_check(self) -> Dict[str, Any]:
        """运行健康检查"""
        logger.debug("运行健康检查...")
        
        try:
            # 运行健康检查工具
            result = subprocess.run([
                sys.executable, 
                str(project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250810_comprehensive_health_monitor.py')
            ], capture_output=True, text=True, cwd=str(project_root))
            
            if result.returncode == 0:
                # 从输出中提取健康分数
                output_lines = result.stdout.split('\n')
                health_score = 0
                
                for line in output_lines:
                    if '健康分数:' in line:
                        import re
                        match = re.search(r'健康分数:\s*(\d+)/100', line)
                        if match:
                            health_score = int(match.group(1))
                            break
                
                return {
                    'status': 'SUCCESS',
                    'message': f'健康检查完成，分数: {health_score}/100',
                    'health_score': health_score
                }
            else:
                return {
                    'status': 'FAILED',
                    'message': '健康检查失败',
                    'error': result.stderr
                }
                
        except Exception as e:
            return {
                'status': 'FAILED',
                'message': f'健康检查异常: {str(e)}'
            }
    
    def _run_alert_check(self) -> Dict[str, Any]:
        """运行告警检查"""
        logger.debug("运行告警检查...")
        
        try:
            # 运行告警系统
            result = subprocess.run([
                sys.executable, 
                str(project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250810_alert_system.py'),
                '--check'
            ], capture_output=True, text=True, cwd=str(project_root))
            
            if result.returncode == 0:
                # 从输出中提取告警数量
                output_lines = result.stdout.split('\n')
                alert_count = 0
                
                for line in output_lines:
                    if '生成告警数量:' in line:
                        import re
                        match = re.search(r'生成告警数量:\s*(\d+)', line)
                        if match:
                            alert_count = int(match.group(1))
                            break
                
                return {
                    'status': 'SUCCESS',
                    'message': f'告警检查完成，发现{alert_count}个告警',
                    'alert_count': alert_count
                }
            else:
                return {
                    'status': 'FAILED',
                    'message': '告警检查失败',
                    'error': result.stderr
                }
                
        except Exception as e:
            return {
                'status': 'FAILED',
                'message': f'告警检查异常: {str(e)}'
            }
    
    def _check_workflow_backlog(self) -> Dict[str, Any]:
        """检查工作流堆积"""
        logger.debug("检查工作流堆积...")
        
        try:
            with database_session() as db:
                from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.unified_material_status import MaterialStatus
                
                # 检查长期未处理的素材
                cutoff_time = datetime.now(timezone.utc) - timedelta(hours=72)
                
                stuck_count = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value]),
                    LocalCreative.updated_at < cutoff_time
                ).count()
                
                total_pending = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.PROCESSING.value])
                ).count()
                
                # 如果堆积严重，自动运行处理器
                if stuck_count > 10:
                    logger.warning(f"发现{stuck_count}个长期卡住的素材，尝试自动处理...")
                    
                    processor_result = subprocess.run([
                        sys.executable, 
                        str(project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250810_workflow_backlog_processor.py')
                    ], capture_output=True, text=True, cwd=str(project_root))
                    
                    auto_processed = processor_result.returncode == 0
                else:
                    auto_processed = False
                
                return {
                    'status': 'SUCCESS',
                    'message': f'工作流检查完成: {stuck_count}个卡住, {total_pending}个待处理',
                    'stuck_count': stuck_count,
                    'total_pending': total_pending,
                    'auto_processed': auto_processed
                }
                
        except Exception as e:
            return {
                'status': 'FAILED',
                'message': f'工作流堆积检查失败: {str(e)}'
            }
    
    def run_weekly_backup(self) -> Dict[str, Any]:
        """运行周备份"""
        logger.info("💾 开始执行周备份任务...")
        
        try:
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            weekly_backup_dir = self.backup_dir / f'weekly_backup_{backup_timestamp}'
            weekly_backup_dir.mkdir(exist_ok=True)
            
            backup_results = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'backup_dir': str(weekly_backup_dir),
                'backed_up_items': [],
                'failed_items': [],
                'total_size_mb': 0
            }
            
            # 备份重要配置文件
            config_files = [
                'config/settings.yml',
                'requirements.txt',
                'main.py',
                'web_ui.py'
            ]
            
            for config_file in config_files:
                source_path = project_root / config_file
                if source_path.exists():
                    try:
                        dest_path = weekly_backup_dir / config_file
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(source_path, dest_path)
                        
                        file_size = source_path.stat().st_size / (1024 * 1024)  # MB
                        backup_results['backed_up_items'].append({
                            'file': config_file,
                            'size_mb': round(file_size, 2)
                        })
                        backup_results['total_size_mb'] += file_size
                        
                    except Exception as e:
                        backup_results['failed_items'].append({
                            'file': config_file,
                            'error': str(e)
                        })
            
            # 备份AI工具目录
            ai_tools_dir = project_root / 'ai_tools'
            if ai_tools_dir.exists():
                try:
                    dest_ai_tools = weekly_backup_dir / 'ai_tools'
                    shutil.copytree(ai_tools_dir, dest_ai_tools, ignore=shutil.ignore_patterns('__pycache__', '*.pyc'))
                    
                    ai_tools_size = sum(f.stat().st_size for f in ai_tools_dir.rglob('*') if f.is_file()) / (1024 * 1024)
                    backup_results['backed_up_items'].append({
                        'directory': 'ai_tools',
                        'size_mb': round(ai_tools_size, 2)
                    })
                    backup_results['total_size_mb'] += ai_tools_size
                    
                except Exception as e:
                    backup_results['failed_items'].append({
                        'directory': 'ai_tools',
                        'error': str(e)
                    })
            
            # 清理旧备份
            self._cleanup_old_backups()
            
            backup_results['total_size_mb'] = round(backup_results['total_size_mb'], 2)
            
            logger.info(f"✅ 周备份完成: 备份{len(backup_results['backed_up_items'])}项, 总大小{backup_results['total_size_mb']}MB")
            
            return backup_results
            
        except Exception as e:
            logger.error(f"❌ 周备份失败: {e}")
            return {
                'status': 'FAILED',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
    
    def _cleanup_old_backups(self):
        """清理旧备份"""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.maintenance_config['retention_days']['backups'])
            
            for backup_item in self.backup_dir.iterdir():
                if backup_item.is_dir() and backup_item.stat().st_mtime < cutoff_date.timestamp():
                    shutil.rmtree(backup_item)
                    logger.info(f"清理旧备份: {backup_item.name}")
                    
        except Exception as e:
            logger.error(f"清理旧备份失败: {e}")
    
    def _save_maintenance_report(self, results: Dict[str, Any]):
        """保存维护报告"""
        try:
            reports_dir = project_root / 'ai_reports' / 'maintenance'
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            report_file = reports_dir / f'daily_maintenance_{datetime.now().strftime("%Y%m%d")}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"保存维护报告失败: {e}")
    
    def setup_scheduled_operations(self):
        """设置定时运维任务"""
        
        # 每天凌晨1点执行日常维护
        schedule.every().day.at("01:00").do(self.run_daily_maintenance)
        
        # 每周日凌晨2点执行周备份
        schedule.every().sunday.at("02:00").do(self.run_weekly_backup)
        
        logger.info("✅ 定时运维任务已设置")
        logger.info("  - 每天01:00: 日常维护任务")
        logger.info("  - 每周日02:00: 周备份任务")
    
    def run_operations_daemon(self):
        """运行运维守护进程"""
        logger.info("🚀 启动自动化运维守护进程...")
        
        # 设置定时任务
        self.setup_scheduled_operations()
        
        # 立即执行一次日常维护
        self.run_daily_maintenance()
        
        # 开始监控循环
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("🛑 自动化运维守护进程已停止")
        except Exception as e:
            logger.error(f"❌ 运维守护进程异常: {e}")


def main():
    """主函数"""
    ops = AutomatedOperations()
    
    import argparse
    parser = argparse.ArgumentParser(description='千川自动化系统运维工具')
    parser.add_argument('--daily', action='store_true', help='执行日常维护')
    parser.add_argument('--backup', action='store_true', help='执行备份')
    parser.add_argument('--daemon', action='store_true', help='运行守护进程')
    
    args = parser.parse_args()
    
    if args.daily:
        result = ops.run_daily_maintenance()
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    elif args.backup:
        result = ops.run_weekly_backup()
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    elif args.daemon:
        ops.run_operations_daemon()
    else:
        print("使用 --daily 执行日常维护, --backup 执行备份, 或 --daemon 运行守护进程")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
