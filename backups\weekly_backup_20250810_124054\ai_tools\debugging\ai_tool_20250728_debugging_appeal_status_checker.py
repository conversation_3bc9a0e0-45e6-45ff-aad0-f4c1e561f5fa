#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 调试提审状态检查工具 - 分析当前数据库中的计划状态
清理条件: 调试完成后可删除
"""

import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta
from loguru import logger

# 添加项目路径
project_root = Path.cwd()
sys.path.insert(0, str(project_root))

def analyze_campaign_status():
    """分析计划状态"""
    logger.info("🔍 分析当前数据库中的计划状态...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from sqlalchemy.orm import joinedload
        from sqlalchemy import func, or_
        
        db = SessionLocal()
        try:
            # 1. 总体统计
            total_campaigns = db.query(Campaign).count()
            auditing_campaigns = db.query(Campaign).filter(Campaign.status == 'AUDITING').count()
            
            logger.info(f"📊 总体统计:")
            logger.info(f"   📋 总计划数: {total_campaigns}")
            logger.info(f"   📋 审核中计划: {auditing_campaigns}")
            
            # 2. 按账户类型统计
            logger.info(f"\n📊 按账户类型统计:")
            
            account_type_stats = db.query(
                AdAccount.account_type,
                func.count(Campaign.id).label('count')
            ).join(Campaign).filter(
                Campaign.status == 'AUDITING'
            ).group_by(AdAccount.account_type).all()
            
            for account_type, count in account_type_stats:
                logger.info(f"   📋 {account_type or 'NULL'}: {count} 个计划")
            
            # 3. 按提审状态统计
            logger.info(f"\n📊 按提审状态统计:")
            
            appeal_status_stats = db.query(
                Campaign.appeal_status,
                func.count(Campaign.id).label('count')
            ).filter(
                Campaign.status == 'AUDITING'
            ).group_by(Campaign.appeal_status).all()
            
            for appeal_status, count in appeal_status_stats:
                status_name = appeal_status or '未提审'
                logger.info(f"   📋 {status_name}: {count} 个计划")
            
            # 4. 测试账户详细分析
            logger.info(f"\n📊 测试账户详细分析:")
            
            test_campaigns = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).join(AdAccount).filter(
                Campaign.status == 'AUDITING',
                AdAccount.account_type == 'TEST'
            ).all()
            
            if test_campaigns:
                logger.info(f"   📋 测试账户计划总数: {len(test_campaigns)}")
                
                # 按提审状态分组
                test_status_groups = {}
                for campaign in test_campaigns:
                    status = campaign.appeal_status or '未提审'
                    if status not in test_status_groups:
                        test_status_groups[status] = []
                    test_status_groups[status].append(campaign)
                
                for status, campaigns in test_status_groups.items():
                    logger.info(f"   📋 {status}: {len(campaigns)} 个计划")
                    
                    # 显示前3个计划的详细信息
                    for i, campaign in enumerate(campaigns[:3]):
                        age_minutes = (datetime.now(timezone.utc) - campaign.created_at.replace(tzinfo=timezone.utc)).total_seconds() / 60
                        logger.info(f"      - 计划 {campaign.campaign_id_qc}: 年龄 {int(age_minutes)} 分钟, 重试次数 {campaign.appeal_attempt_count or 0}")
                    
                    if len(campaigns) > 3:
                        logger.info(f"      ... 还有 {len(campaigns) - 3} 个计划")
            else:
                logger.info(f"   📋 没有找到测试账户的计划")
            
            # 5. 符合智能调度器条件的计划
            logger.info(f"\n📊 符合智能调度器条件的计划:")
            
            # 新建计划条件
            min_age_minutes = 60
            min_creation_time = datetime.now(timezone.utc) - timedelta(minutes=min_age_minutes)
            
            new_plans = db.query(Campaign).join(AdAccount).filter(
                Campaign.status == 'AUDITING',
                AdAccount.account_type == 'TEST',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None),
                or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0),
                Campaign.created_at <= min_creation_time
            ).all()
            
            logger.info(f"   📋 符合新建计划条件: {len(new_plans)} 个")
            
            # 失败重试计划条件
            max_retry_attempts = 3
            
            failed_plans = db.query(Campaign).join(AdAccount).filter(
                Campaign.status == 'AUDITING',
                AdAccount.account_type == 'TEST',
                Campaign.appeal_status == 'submission_failed',
                or_(Campaign.appeal_attempt_count.is_(None), 
                    Campaign.appeal_attempt_count < max_retry_attempts)
            ).all()
            
            logger.info(f"   📋 符合失败重试条件: {len(failed_plans)} 个")
            
            # 6. 账户详细信息
            logger.info(f"\n📊 测试账户详细信息:")
            
            test_accounts = db.query(AdAccount).options(
                joinedload(AdAccount.principal)
            ).filter(AdAccount.account_type == 'TEST').all()
            
            for account in test_accounts:
                campaign_count = db.query(Campaign).filter(
                    Campaign.account_id == account.id,
                    Campaign.status == 'AUDITING'
                ).count()
                
                logger.info(f"   🏢 {account.principal.name} ({account.account_id_qc}): {campaign_count} 个审核中计划")
            
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 分析计划状态失败: {e}")
        return False

def check_appeal_conditions():
    """检查提审条件"""
    logger.info("🔍 检查智能调度器的提审条件...")
    
    try:
        # 导入智能调度器
        sys.path.insert(0, str(project_root / 'ai_tools' / 'optimization'))
        from ai_tool_20250728_optimization_simple_smart_scheduler import (
            get_plans_grouped_by_account_simple,
            get_failed_plans_simple
        )
        
        # 加载配置
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 检查新建计划
        logger.info("📋 检查新建计划:")
        new_plans = get_plans_grouped_by_account_simple(60, app_settings)
        
        if new_plans:
            logger.info(f"   ✅ 找到 {len(new_plans)} 个广告户的新建计划")
            for account_key, plans in new_plans.items():
                logger.info(f"      🏢 {account_key}: {len(plans)} 个计划")
        else:
            logger.info("   ℹ️ 没有符合条件的新建计划")
        
        # 检查失败计划
        logger.info("📋 检查失败计划:")
        failed_plans = get_failed_plans_simple(3, app_settings)
        
        if failed_plans:
            logger.info(f"   ✅ 找到 {len(failed_plans)} 个广告户的失败计划")
            for account_key, plans in failed_plans.items():
                logger.info(f"      🏢 {account_key}: {len(plans)} 个计划")
        else:
            logger.info("   ℹ️ 没有符合条件的失败计划")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查提审条件失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 提审状态调试工具")
    logger.info("="*60)
    logger.info("🔍 分析内容:")
    logger.info("1. 数据库中的计划状态分析")
    logger.info("2. 智能调度器的提审条件检查")
    logger.info("3. 解释为什么没有启动浏览器")
    logger.info("="*60)
    
    try:
        # 分析数据库状态
        logger.info("\n🔍 第一步：分析数据库中的计划状态")
        analyze_success = analyze_campaign_status()
        
        # 检查提审条件
        logger.info("\n🔍 第二步：检查智能调度器的提审条件")
        condition_success = check_appeal_conditions()
        
        # 生成结论
        logger.info("\n" + "="*60)
        logger.info("🎯 调试结论")
        logger.info("="*60)
        
        if analyze_success and condition_success:
            logger.success("✅ 调试分析完成")
            logger.info("💡 如果智能调度器没有启动浏览器，说明：")
            logger.info("1. 测试账户中确实没有符合条件的计划需要提审")
            logger.info("2. 系统工作正常，这是预期行为")
            logger.info("3. Web界面显示的统计可能包含所有账户类型")
        else:
            logger.error("❌ 调试分析过程中出现问题")
        
        return analyze_success and condition_success
        
    except Exception as e:
        logger.error(f"❌ 调试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 提审状态调试完成！")
        logger.info("💡 现在您应该清楚为什么没有启动浏览器了")
    else:
        logger.error("\n❌ 提审状态调试失败")
    
    sys.exit(0 if success else 1)
