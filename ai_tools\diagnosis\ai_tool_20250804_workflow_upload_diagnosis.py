"""
AI生成文件信息
================
文件类型: 诊断工具
生命周期: 临时使用
创建目的: 诊断工作流上传任务失败的根本原因
清理条件: 问题解决后可删除
"""

import os
import sys
from datetime import datetime, timezone
from typing import List, Dict, Any
from loguru import logger

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

# 添加src目录到路径
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Principal, AdAccount, LocalCreative
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config import load_app_settings
from qianchuan_aw.workflows.scheduler import is_account_healthy
from qianchuan_aw.utils.unified_material_status import MaterialStatus
from sqlalchemy.orm import joinedload

def check_test_accounts_health():
    """检查测试账户健康状态"""
    logger.info("🔍 开始检查测试账户健康状态")
    
    try:
        app_settings = load_app_settings()
        
        with database_session() as db:
            # 获取缇萃百货的TEST类型账户
            titui_principal = db.query(Principal).filter(Principal.name == '缇萃百货').first()
            if not titui_principal:
                logger.error("❌ 未找到缇萃百货主体")
                return
            
            test_accounts = db.query(AdAccount).filter(
                AdAccount.principal_id == titui_principal.id,
                AdAccount.account_type == 'TEST',
                AdAccount.status == 'active'
            ).all()
            
            logger.info(f"📊 找到 {len(test_accounts)} 个active状态的TEST账户")
            
            if not test_accounts:
                logger.error("❌ 没有找到active状态的TEST账户")
                return
            
            # 检查每个账户的健康状态
            client = QianchuanClient(
                app_id=app_settings['api_credentials']['app_id'], 
                secret=app_settings['api_credentials']['secret'], 
                principal_id=titui_principal.id
            )
            
            healthy_count = 0
            for account in test_accounts:
                logger.info(f"🔍 检查账户: {account.name} ({account.account_id_qc})")
                
                try:
                    is_healthy = is_account_healthy(db, client, account)
                    if is_healthy:
                        logger.success(f"✅ 账户 {account.name} 健康检查通过")
                        healthy_count += 1
                    else:
                        logger.warning(f"⚠️ 账户 {account.name} 健康检查失败")
                        
                except Exception as e:
                    logger.error(f"❌ 账户 {account.name} 健康检查异常: {e}")
            
            logger.info(f"📊 健康账户统计: {healthy_count}/{len(test_accounts)}")
            
            if healthy_count == 0:
                logger.error("❌ 没有健康的测试账户，这就是上传任务无法派发的原因！")
            else:
                logger.success(f"✅ 有 {healthy_count} 个健康测试账户")
                
    except Exception as e:
        logger.error(f"❌ 检查测试账户健康状态失败: {e}")
        import traceback
        traceback.print_exc()

def check_processing_files_status():
    """检查processing状态文件的详细情况"""
    logger.info("🔍 检查processing状态文件")
    
    try:
        with database_session() as db:
            processing_files = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value
            ).order_by(LocalCreative.created_at.desc()).limit(20).all()
            
            logger.info(f"📊 找到 {len(processing_files)} 个processing状态文件（显示最新20个）")
            
            for file in processing_files:
                logger.info(f"📄 文件: {file.filename}")
                logger.info(f"   状态: {file.status}")
                logger.info(f"   创建时间: {file.created_at}")
                logger.info(f"   更新时间: {file.updated_at}")
                logger.info(f"   material_id_qc: {file.material_id_qc}")
                logger.info(f"   video_id: {file.video_id}")
                logger.info(f"   相似组: {file.similarity_group}")
                logger.info("   " + "-" * 50)
                
    except Exception as e:
        logger.error(f"❌ 检查processing文件失败: {e}")

def check_workflow_dispatch_logic():
    """检查工作流派发逻辑"""
    logger.info("🔍 检查工作流派发逻辑")
    
    try:
        with database_session() as db:
            # 检查pending_grouping状态的文件
            pending_grouping_files = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PENDING_GROUPING.value
            ).all()
            
            logger.info(f"📊 pending_grouping状态文件: {len(pending_grouping_files)} 个")
            
            # 按主体分组统计
            principal_stats = {}
            for file in pending_grouping_files:
                principal_name = db.query(Principal).filter(Principal.id == file.principal_id).first().name
                if principal_name not in principal_stats:
                    principal_stats[principal_name] = 0
                principal_stats[principal_name] += 1
            
            logger.info("📊 按主体分组的pending_grouping文件统计:")
            for principal_name, count in principal_stats.items():
                logger.info(f"   {principal_name}: {count} 个")
                
            # 检查processing状态的文件
            processing_files = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value
            ).all()
            
            logger.info(f"📊 processing状态文件: {len(processing_files)} 个")
            
            # 按主体分组统计processing文件
            processing_stats = {}
            for file in processing_files:
                principal_name = db.query(Principal).filter(Principal.id == file.principal_id).first().name
                if principal_name not in processing_stats:
                    processing_stats[principal_name] = 0
                processing_stats[principal_name] += 1
            
            logger.info("📊 按主体分组的processing文件统计:")
            for principal_name, count in processing_stats.items():
                logger.info(f"   {principal_name}: {count} 个")
                
    except Exception as e:
        logger.error(f"❌ 检查工作流派发逻辑失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 开始工作流上传诊断")
    logger.info("=" * 60)
    
    # 1. 检查测试账户健康状态
    check_test_accounts_health()
    logger.info("")
    
    # 2. 检查processing文件状态
    check_processing_files_status()
    logger.info("")
    
    # 3. 检查工作流派发逻辑
    check_workflow_dispatch_logic()
    logger.info("")
    
    logger.success("✅ 工作流上传诊断完成")

if __name__ == "__main__":
    main()
