#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实现千川自动化项目缺失的计划提审功能
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import yaml
import shutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config):
    """保存配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    
    # 备份原配置
    backup_path = config_path.with_suffix(f'.yml.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(config_path, backup_path)
    logger.info(f"📋 配置文件已备份到: {backup_path}")
    
    # 保存新配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    logger.success(f"✅ 配置文件已更新: {config_path}")

def add_plan_submission_task():
    """添加计划提审任务到tasks.py"""
    logger.info("📝 添加计划提审任务...")
    
    tasks_file = project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'tasks.py'
    
    # 读取现有文件
    with open(tasks_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已存在提审任务
    if 'task_submit_plans' in content:
        logger.info("✅ 计划提审任务已存在")
        return True
    
    # 添加提审任务
    submission_task = '''
@app.task(name="tasks.submit_plans")
def task_submit_plans():
    """提交新创建的计划进行审核"""
    if not app_settings.get('workflow', {}).get('plan_submission', {}).get('enabled', True):
        logger.info("[Task Skip] Plan Submission is disabled.")
        return
    logger.info("[Task Start] Submit Plans")
    _run_task(scheduler.handle_plan_submission)
    logger.info("[Task End] Submit Plans")
'''
    
    # 在task_appeal_plans之前插入
    if '@app.task(name="tasks.appeal_plans")' in content:
        content = content.replace(
            '@app.task(name="tasks.appeal_plans")',
            submission_task + '\<EMAIL>(name="tasks.appeal_plans")'
        )
    else:
        # 如果没有appeal_plans任务，在文件末尾添加
        content += submission_task
    
    # 备份原文件
    backup_file = tasks_file.with_suffix(f'.py.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(tasks_file, backup_file)
    logger.info(f"📋 tasks.py已备份到: {backup_file}")
    
    # 写入修改后的内容
    with open(tasks_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ 计划提审任务已添加到tasks.py")
    return True

def add_plan_submission_handler():
    """添加计划提审处理函数到scheduler.py"""
    logger.info("📝 添加计划提审处理函数...")
    
    scheduler_file = project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'scheduler.py'
    
    # 读取现有文件
    with open(scheduler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已存在提审处理函数
    if 'handle_plan_submission' in content:
        logger.info("✅ 计划提审处理函数已存在")
        return True
    
    # 创建提审处理函数
    submission_handler = '''

def handle_plan_submission(db: Session, app_settings: Dict[str, Any]):
    """处理新创建计划的提审"""
    logger.info("--- [工作流1.5] 开始：提交新创建的计划 ---")
    
    try:
        # 查找状态为AUDITING但未提审的计划
        plans_to_submit = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(
            Campaign.status == 'AUDITING',
            Campaign.appeal_status.is_(None),
            Campaign.first_appeal_at.is_(None)
        ).all()
        
        if not plans_to_submit:
            logger.info("没有找到需要提审的新计划")
            return
        
        logger.info(f"发现 {len(plans_to_submit)} 个新计划需要提审")
        
        # 按账户分组处理
        plans_by_account = defaultdict(list)
        for plan in plans_to_submit:
            plans_by_account[plan.account].append(plan)
        
        submission_count = 0
        
        for account, account_plans in plans_by_account.items():
            try:
                principal = account.principal
                if not principal:
                    logger.warning(f"账户 {account.account_id_qc} 没有关联的主体，跳过")
                    continue
                
                logger.info(f"处理账户 {account.account_id_qc} 的 {len(account_plans)} 个计划")
                
                # 为每个计划执行提审
                for plan in account_plans:
                    try:
                        # 使用现有的提审服务
                        from qianchuan_aw.services.async_appeal_adapter import AsyncAppealAdapter
                        
                        adapter = AsyncAppealAdapter()
                        result = adapter.appeal_plan(
                            principal_name=principal.name,
                            account_id=account.account_id_qc,
                            plan_id=plan.campaign_id_qc
                        )
                        
                        if result.get('success'):
                            # 更新计划状态
                            plan.appeal_status = 'appeal_pending'
                            plan.first_appeal_at = datetime.utcnow()
                            submission_count += 1
                            
                            logger.success(f"✅ 计划 {plan.campaign_id_qc} 提审成功")
                        else:
                            error_msg = result.get('error', '未知错误')
                            logger.error(f"❌ 计划 {plan.campaign_id_qc} 提审失败: {error_msg}")
                            
                            # 记录提审失败
                            plan.appeal_status = 'submission_failed'
                            plan.appeal_error_message = error_msg
                        
                        # 避免过于频繁的API调用
                        import time
                        time.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"❌ 处理计划 {plan.campaign_id_qc} 提审时发生错误: {e}")
                        plan.appeal_status = 'submission_error'
                        plan.appeal_error_message = str(e)
                
            except Exception as e:
                logger.error(f"❌ 处理账户 {account.account_id_qc} 时发生错误: {e}")
        
        # 提交数据库更改
        db.commit()
        
        logger.success(f"✅ 计划提审完成，成功提审 {submission_count} 个计划")
        
    except Exception as e:
        logger.error(f"❌ 计划提审处理失败: {e}")
        db.rollback()
        raise
    finally:
        logger.info("--- [工作流1.5] 结束：提交新创建的计划 ---")
'''
    
    # 在handle_plans_awaiting_appeal函数之前插入
    if 'def handle_plans_awaiting_appeal(' in content:
        content = content.replace(
            'def handle_plans_awaiting_appeal(',
            submission_handler + '\n\ndef handle_plans_awaiting_appeal('
        )
    else:
        # 如果没有找到合适的位置，在文件末尾添加
        content += submission_handler
    
    # 备份原文件
    backup_file = scheduler_file.with_suffix(f'.py.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(scheduler_file, backup_file)
    logger.info(f"📋 scheduler.py已备份到: {backup_file}")
    
    # 写入修改后的内容
    with open(scheduler_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ 计划提审处理函数已添加到scheduler.py")
    return True

def add_plan_submission_to_celery_config():
    """添加计划提审任务到Celery配置"""
    logger.info("⚙️ 添加计划提审任务到Celery配置...")
    
    config = load_config()
    workflow = config.get('workflow', {})
    
    # 添加计划提审配置
    workflow['plan_submission'] = {
        'enabled': True,
        'interval_seconds': 90,  # 90秒间隔，在计划创建(60秒)和申诉(600秒)之间
        'description': '提交新创建的计划进行审核'
    }
    
    config['workflow'] = workflow
    save_config(config)
    
    logger.success("✅ 计划提审配置已添加")
    return True

def add_plan_submission_to_celery_beat():
    """添加计划提审任务到Celery Beat调度"""
    logger.info("📅 添加计划提审任务到Celery Beat调度...")
    
    celery_app_file = project_root / 'src' / 'qianchuan_aw' / 'celery_app.py'
    
    # 读取现有文件
    with open(celery_app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已存在提审任务调度
    if 'plan-submission-configurable' in content:
        logger.info("✅ 计划提审任务调度已存在")
        return True
    
    # 查找beat_schedule配置位置
    if 'beat_schedule = {' in content:
        # 找到合适的插入位置（在plan-creation之后）
        insertion_point = content.find("'plan-creation-configurable'")
        if insertion_point != -1:
            # 找到该任务配置的结束位置
            brace_count = 0
            pos = insertion_point
            while pos < len(content):
                if content[pos] == '{':
                    brace_count += 1
                elif content[pos] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        pos += 1
                        break
                pos += 1
            
            # 在此位置插入新的任务配置
            submission_config = '''    'plan-submission-configurable': {
        'task': 'tasks.submit_plans',
        'schedule': crontab(minute='*'),
        'options': {'expires': 60}
    },
'''
            
            content = content[:pos] + ',\n' + submission_config + content[pos+1:]
        else:
            # 如果没找到plan-creation，在beat_schedule末尾添加
            beat_end = content.find('}', content.find('beat_schedule = {'))
            submission_config = '''    'plan-submission-configurable': {
        'task': 'tasks.submit_plans',
        'schedule': crontab(minute='*'),
        'options': {'expires': 60}
    },
'''
            content = content[:beat_end] + submission_config + '\n' + content[beat_end:]
    
    # 备份原文件
    backup_file = celery_app_file.with_suffix(f'.py.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(celery_app_file, backup_file)
    logger.info(f"📋 celery_app.py已备份到: {backup_file}")
    
    # 写入修改后的内容
    with open(celery_app_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ 计划提审任务已添加到Celery Beat调度")
    return True

def create_batch_submission_script():
    """创建批量提审现有计划的脚本"""
    logger.info("📝 创建批量提审脚本...")
    
    batch_script = '''#!/usr/bin/env python3
"""
批量提审现有未提审计划脚本
"""

import sys
from pathlib import Path
from datetime import datetime

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.database.connection import database_session
from qianchuan_aw.database.models import Campaign, AdAccount
from qianchuan_aw.services.async_appeal_adapter import AsyncAppealAdapter
from qianchuan_aw.utils.logger import logger

def batch_submit_unsubmitted_plans():
    """批量提审未提审的计划"""
    logger.info("🚀 开始批量提审未提审的计划...")
    
    try:
        with database_session() as db:
            # 查找未提审的计划
            unsubmitted_plans = db.query(Campaign).join(
                AdAccount, Campaign.account_id == AdAccount.id
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None)
            ).all()
            
            if not unsubmitted_plans:
                logger.info("✅ 没有发现未提审的计划")
                return
            
            logger.info(f"📊 发现 {len(unsubmitted_plans)} 个未提审的计划")
            
            adapter = AsyncAppealAdapter()
            success_count = 0
            
            for plan in unsubmitted_plans:
                try:
                    account = plan.account
                    principal = account.principal
                    
                    if not principal:
                        logger.warning(f"⚠️ 计划 {plan.campaign_id_qc} 没有关联主体，跳过")
                        continue
                    
                    logger.info(f"📤 提审计划: {plan.campaign_id_qc}")
                    
                    result = adapter.appeal_plan(
                        principal_name=principal.name,
                        account_id=account.account_id_qc,
                        plan_id=plan.campaign_id_qc
                    )
                    
                    if result.get('success'):
                        plan.appeal_status = 'appeal_pending'
                        plan.first_appeal_at = datetime.utcnow()
                        success_count += 1
                        logger.success(f"✅ 计划 {plan.campaign_id_qc} 提审成功")
                    else:
                        error_msg = result.get('error', '未知错误')
                        plan.appeal_status = 'submission_failed'
                        plan.appeal_error_message = error_msg
                        logger.error(f"❌ 计划 {plan.campaign_id_qc} 提审失败: {error_msg}")
                    
                    # 避免API限流
                    import time
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"❌ 处理计划 {plan.campaign_id_qc} 时发生错误: {e}")
                    plan.appeal_status = 'submission_error'
                    plan.appeal_error_message = str(e)
            
            # 提交更改
            db.commit()
            
            logger.success(f"🎉 批量提审完成！成功提审 {success_count}/{len(unsubmitted_plans)} 个计划")
            
    except Exception as e:
        logger.error(f"❌ 批量提审失败: {e}")

if __name__ == "__main__":
    batch_submit_unsubmitted_plans()
'''
    
    script_path = project_root / 'ai_tools' / 'repair' / 'ai_tool_20250728_repair_batch_submit_plans.py'
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(batch_script)
    
    logger.success(f"✅ 批量提审脚本已创建: {script_path}")
    return script_path

def main():
    """主函数"""
    logger.info("🚀 开始实现计划提审功能...")
    
    try:
        results = {
            'task_added': False,
            'handler_added': False,
            'config_added': False,
            'beat_added': False,
            'batch_script_created': False
        }
        
        # 1. 添加计划提审任务
        results['task_added'] = add_plan_submission_task()
        
        # 2. 添加计划提审处理函数
        results['handler_added'] = add_plan_submission_handler()
        
        # 3. 添加配置
        results['config_added'] = add_plan_submission_to_celery_config()
        
        # 4. 添加到Celery Beat调度
        results['beat_added'] = add_plan_submission_to_celery_beat()
        
        # 5. 创建批量提审脚本
        batch_script_path = create_batch_submission_script()
        results['batch_script_created'] = batch_script_path is not None
        
        # 总结结果
        logger.info("\n" + "="*60)
        logger.info("🎯 计划提审功能实现完成")
        logger.info("="*60)
        
        success_count = sum(results.values())
        total_count = len(results)
        
        for action, success in results.items():
            status = "✅" if success else "❌"
            action_name = action.replace('_', ' ').title()
            logger.info(f"{status} {action_name}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"\n📈 实现成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.success("🎉 计划提审功能实现成功！")
            
            logger.info("\n📋 后续必要操作:")
            logger.info("1. 重启Celery Worker和Beat进程以加载新任务")
            logger.info("2. 运行批量提审脚本处理现有未提审计划:")
            logger.info(f"   python {batch_script_path}")
            logger.info("3. 监控新的工作流是否正常运行")
            
            logger.info("\n🎯 预期效果:")
            logger.info("- 新创建的计划将在90秒内自动提审")
            logger.info("- 现有124个未提审计划将被批量处理")
            logger.info("- 工作流将恢复完整的：创建→提审→监控→收割流程")
            
        else:
            logger.error("❌ 计划提审功能实现存在问题")
        
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ 实现过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
