"""
AI生成文件信息
================
文件类型: 诊断工具
生命周期: 永久保留
创建目的: 诊断上传验证失败和视频文件问题
清理条件: 作为诊断工具长期保留
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config_manager import get_config
from qianchuan_aw.utils.unified_material_status import MaterialStatus
import yaml


class UploadVerificationDiagnosis:
    """上传验证诊断工具"""

    def __init__(self):
        # 直接加载配置文件
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        self.issues_found = []
        
    def diagnose_all(self) -> Dict[str, Any]:
        """执行完整诊断"""
        logger.info("🔍 开始上传验证诊断...")
        
        results = {
            'file_system_check': self.check_file_system(),
            'database_status_check': self.check_database_status(),
            'api_verification_check': self.check_api_verification(),
            'video_file_integrity': self.check_video_file_integrity(),
            'upload_verification_logic': self.check_upload_verification_logic(),
            'issues_summary': self.issues_found
        }
        
        self.generate_diagnosis_report(results)
        return results
    
    def check_file_system(self) -> Dict[str, Any]:
        """检查文件系统状态"""
        logger.info("📁 检查文件系统状态...")
        
        workflow_dir = self.config.get('custom_workflow_assets_dir', 'D:/workflow_assets')
        process_dir = os.path.join(workflow_dir, '01_materials_to_process', '缇萃百货')
        
        result = {
            'workflow_dir_exists': os.path.exists(workflow_dir),
            'process_dir_exists': os.path.exists(process_dir),
            'video_files': [],
            'file_count': 0,
            'total_size_mb': 0
        }
        
        if os.path.exists(process_dir):
            video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
            for file in os.listdir(process_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    file_path = os.path.join(process_dir, file)
                    file_size = os.path.getsize(file_path)
                    
                    result['video_files'].append({
                        'filename': file,
                        'size_bytes': file_size,
                        'size_mb': round(file_size / (1024*1024), 2),
                        'is_empty': file_size == 0
                    })
                    
                    if file_size == 0:
                        self.issues_found.append(f"空文件: {file}")
            
            result['file_count'] = len(result['video_files'])
            result['total_size_mb'] = sum(f['size_mb'] for f in result['video_files'])
            
            # 检查空文件
            empty_files = [f for f in result['video_files'] if f['is_empty']]
            if empty_files:
                logger.warning(f"⚠️ 发现 {len(empty_files)} 个空文件")
                result['empty_files_count'] = len(empty_files)
        
        logger.info(f"📊 文件系统检查结果: {result['file_count']} 个视频文件, {result['total_size_mb']} MB")
        return result
    
    def check_database_status(self) -> Dict[str, Any]:
        """检查数据库状态"""
        logger.info("🗄️ 检查数据库状态...")
        
        result = {
            'total_records': 0,
            'status_distribution': {},
            'recent_uploads': [],
            'pending_files': 0
        }
        
        try:
            with database_session() as db:
                # 总记录数
                result['total_records'] = db.query(LocalCreative).count()
                
                # 状态分布
                status_query = db.query(LocalCreative.status, 
                                      db.func.count(LocalCreative.id).label('count')).group_by(LocalCreative.status).all()
                result['status_distribution'] = {status: count for status, count in status_query}
                
                # 最近上传的记录
                recent = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.PROCESSING.value])
                ).order_by(LocalCreative.updated_at.desc()).limit(10).all()
                
                result['recent_uploads'] = [{
                    'id': r.id,
                    'filename': r.filename,
                    'status': r.status,
                    'material_id': r.material_id_qc,
                    'video_id': r.video_id,
                    'updated_at': r.updated_at.isoformat() if r.updated_at else None
                } for r in recent]
                
                # 待处理文件数
                result['pending_files'] = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.NEW.value, MaterialStatus.PROCESSING.value, MaterialStatus.PENDING_GROUPING.value])
                ).count()
                
        except Exception as e:
            logger.error(f"数据库检查失败: {e}")
            self.issues_found.append(f"数据库连接错误: {e}")
        
        logger.info(f"📊 数据库状态: {result['total_records']} 条记录, {result['pending_files']} 个待处理")
        return result
    
    def check_api_verification(self) -> Dict[str, Any]:
        """检查API验证逻辑"""
        logger.info("🔌 检查API验证逻辑...")
        
        result = {
            'client_connection': False,
            'get_library_videos_test': False,
            'api_delay_issue': False,
            'test_results': []
        }
        
        try:
            # 测试API连接
            client = QianchuanClient(advertiser_id=1)
            result['client_connection'] = True
            
            # 测试get_library_videos
            logger.info("测试get_library_videos API...")
            videos = client.get_library_videos()
            result['get_library_videos_test'] = True
            result['library_videos_count'] = len(videos.get('list', []))
            
            # 检查是否有延迟问题
            # 上传后立即查询可能查不到，需要等待
            logger.info("测试API延迟问题...")
            start_time = time.time()
            videos1 = client.get_library_videos()
            time.sleep(2)  # 等待2秒
            videos2 = client.get_library_videos()
            end_time = time.time()
            
            result['api_response_time'] = round(end_time - start_time, 2)
            result['videos_count_before'] = len(videos1.get('list', []))
            result['videos_count_after'] = len(videos2.get('list', []))
            
            if result['videos_count_before'] != result['videos_count_after']:
                result['api_delay_issue'] = True
                self.issues_found.append("API存在延迟问题，上传后需要等待才能查询到")
            
        except Exception as e:
            logger.error(f"API验证失败: {e}")
            self.issues_found.append(f"API连接错误: {e}")
        
        return result
    
    def check_video_file_integrity(self) -> Dict[str, Any]:
        """检查视频文件完整性"""
        logger.info("🎬 检查视频文件完整性...")
        
        result = {
            'checked_files': 0,
            'corrupted_files': [],
            'empty_files': [],
            'large_files': [],
            'recommendations': []
        }
        
        workflow_dir = self.config.get('custom_workflow_assets_dir', 'D:/workflow_assets')
        process_dir = os.path.join(workflow_dir, '01_materials_to_process', '缇萃百货')
        
        if not os.path.exists(process_dir):
            self.issues_found.append(f"处理目录不存在: {process_dir}")
            return result
        
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
        for file in os.listdir(process_dir):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                file_path = os.path.join(process_dir, file)
                file_size = os.path.getsize(file_path)
                result['checked_files'] += 1
                
                # 检查空文件
                if file_size == 0:
                    result['empty_files'].append(file)
                    self.issues_found.append(f"空文件需要删除: {file}")
                
                # 检查过大文件
                elif file_size > 500 * 1024 * 1024:  # 500MB
                    result['large_files'].append({
                        'filename': file,
                        'size_mb': round(file_size / (1024*1024), 2)
                    })
                
                # 简单的文件头检查
                try:
                    with open(file_path, 'rb') as f:
                        header = f.read(8)
                        # MP4文件应该有特定的文件头
                        if file.lower().endswith('.mp4') and not any(
                            header.startswith(sig) for sig in [b'ftyp', b'\x00\x00\x00']
                        ):
                            result['corrupted_files'].append(file)
                            self.issues_found.append(f"可能损坏的文件: {file}")
                except Exception as e:
                    result['corrupted_files'].append(file)
                    self.issues_found.append(f"无法读取文件: {file} - {e}")
        
        # 生成建议
        if result['empty_files']:
            result['recommendations'].append(f"删除 {len(result['empty_files'])} 个空文件")
        if result['corrupted_files']:
            result['recommendations'].append(f"检查 {len(result['corrupted_files'])} 个可能损坏的文件")
        
        logger.info(f"📊 文件完整性检查: {result['checked_files']} 个文件, {len(result['empty_files'])} 个空文件")
        return result
    
    def check_upload_verification_logic(self) -> Dict[str, Any]:
        """检查上传验证逻辑"""
        logger.info("🔍 检查上传验证逻辑...")
        
        result = {
            'verification_enabled': False,
            'verification_delay': 0,
            'retry_logic': {},
            'recommendations': []
        }
        
        # 检查配置
        upload_reliability = self.config.get('robustness', {}).get('upload_reliability', {})
        result['verification_enabled'] = upload_reliability.get('verification_enabled', False)
        
        if result['verification_enabled']:
            # 建议增加验证延迟
            result['recommendations'].append("建议在上传验证前增加3-5秒延迟")
            result['recommendations'].append("建议增加重试次数和验证逻辑")
            
            # 检查重试配置
            result['retry_logic'] = {
                'max_retries': self.config.get('robustness', {}).get('max_retries_for_upload', 5),
                'retry_delay': self.config.get('robustness', {}).get('upload_retry_delay', 60),
                'smart_retry': upload_reliability.get('smart_retry_enabled', True)
            }
        
        return result
    
    def generate_diagnosis_report(self, results: Dict[str, Any]):
        """生成诊断报告"""
        logger.info("\n" + "="*60)
        logger.info("📋 上传验证诊断报告")
        logger.info("="*60)
        
        # 文件系统状态
        fs = results['file_system_check']
        logger.info(f"📁 文件系统: {fs['file_count']} 个视频文件 ({fs['total_size_mb']} MB)")
        if 'empty_files_count' in fs:
            logger.warning(f"⚠️ 发现 {fs['empty_files_count']} 个空文件")
        
        # 数据库状态
        db = results['database_status_check']
        logger.info(f"🗄️ 数据库: {db['total_records']} 条记录, {db['pending_files']} 个待处理")
        
        # API状态
        api = results['api_verification_check']
        if api['client_connection']:
            logger.info(f"🔌 API连接: 正常 ({api.get('library_videos_count', 0)} 个素材)")
        else:
            logger.error("❌ API连接失败")
        
        # 问题总结
        if self.issues_found:
            logger.warning(f"\n⚠️ 发现 {len(self.issues_found)} 个问题:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.warning(f"   {i}. {issue}")
        else:
            logger.success("✅ 未发现明显问题")
        
        logger.info("="*60)


def main():
    """主函数"""
    diagnosis = UploadVerificationDiagnosis()
    results = diagnosis.diagnose_all()
    
    # 输出关键建议
    print("\n💡 关键建议:")
    print("1. 删除所有空文件（0字节的视频文件）")
    print("2. 在上传验证前增加3-5秒延迟等待API同步")
    print("3. 增加验证重试次数和更智能的重试逻辑")
    print("4. 监控API响应时间和素材库同步延迟")


if __name__ == "__main__":
    main()
