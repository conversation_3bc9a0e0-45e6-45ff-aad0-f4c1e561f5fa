#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川项目语法错误检查和代码质量审计工具
清理条件: 项目代码质量管理完全重构时可删除

千川项目语法错误检查和代码质量审计工具
====================================

功能：
1. Python语法错误检查（使用ast模块）
2. 导入错误检查
3. 代码质量分析（使用flake8风格检查）
4. 工作流调用链分析
5. 项目结构完整性验证

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_syntax_checker.py [action]

Actions:
- check: 执行语法检查
- analyze: 分析调用链
- report: 生成完整报告
"""

import os
import sys
import ast
import json
import importlib.util
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from src.qianchuan_aw.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


class ErrorSeverity(Enum):
    """错误严重程度"""
    CRITICAL = "critical"    # 阻塞性错误，影响系统运行
    HIGH = "high"           # 高优先级错误，可能影响功能
    MEDIUM = "medium"       # 中等错误，影响代码质量
    LOW = "low"            # 低优先级警告


class FileImportance(Enum):
    """文件重要性级别"""
    CORE = "core"          # 核心文件，系统运行必需
    IMPORTANT = "important" # 重要文件，主要功能
    UTILITY = "utility"    # 工具文件，辅助功能
    TEST = "test"          # 测试文件
    DEPRECATED = "deprecated" # 废弃文件


@dataclass
class SyntaxError:
    """语法错误信息"""
    file_path: str
    line_number: int
    column: int
    error_type: str
    message: str
    severity: str  # 改为字符串类型
    code_snippet: Optional[str] = None


@dataclass
class ImportError:
    """导入错误信息"""
    file_path: str
    line_number: int
    module_name: str
    error_message: str
    severity: str  # 改为字符串类型


@dataclass
class FileAnalysis:
    """文件分析结果"""
    file_path: str
    importance: str  # 改为字符串类型
    syntax_errors: List[SyntaxError]
    import_errors: List[ImportError]
    is_parseable: bool
    line_count: int
    function_count: int
    class_count: int
    import_count: int


class SyntaxChecker:
    """语法检查器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.results = {}
        
        # 定义核心文件模式
        self.core_files = {
            'main.py',
            'web_ui.py',
            'src/qianchuan_aw/workflows/scheduler.py',
            'src/qianchuan_aw/database/models.py',
            'src/qianchuan_aw/sdk_qc/client.py',
            'src/qianchuan_aw/database/database.py',
            'src/qianchuan_aw/utils/logger.py',
        }
        
        # 定义重要文件模式
        self.important_patterns = [
            'src/qianchuan_aw/workflows/',
            'src/qianchuan_aw/services/',
            'src/qianchuan_aw/utils/',
            'tools/',
        ]
        
        # 定义测试文件模式
        self.test_patterns = [
            'test_',
            'tests/',
            '_test.py',
            'ai_temp/',
        ]
        
        # 定义废弃文件模式
        self.deprecated_patterns = [
            '_backup',
            '_old',
            'backup_',
            'deprecated_',
        ]
    
    def classify_file_importance(self, file_path: str) -> FileImportance:
        """分类文件重要性"""
        rel_path = os.path.relpath(file_path, self.project_root)
        
        # 检查是否为核心文件
        if rel_path.replace('\\', '/') in self.core_files:
            return FileImportance.CORE
        
        # 检查是否为废弃文件
        for pattern in self.deprecated_patterns:
            if pattern in rel_path.lower():
                return FileImportance.DEPRECATED
        
        # 检查是否为测试文件
        for pattern in self.test_patterns:
            if pattern in rel_path.lower():
                return FileImportance.TEST
        
        # 检查是否为重要文件
        for pattern in self.important_patterns:
            if pattern in rel_path.replace('\\', '/'):
                return FileImportance.IMPORTANT
        
        return FileImportance.UTILITY
    
    def check_syntax(self, file_path: str) -> Tuple[bool, List[SyntaxError]]:
        """检查Python文件语法"""
        errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用ast模块解析语法
            try:
                ast.parse(content, filename=file_path)
                return True, []
            except SyntaxError as e:
                # 获取错误代码片段
                lines = content.split('\n')
                if e.lineno and e.lineno <= len(lines):
                    code_snippet = lines[e.lineno - 1] if e.lineno > 0 else ""
                else:
                    code_snippet = None
                
                error = SyntaxError(
                    file_path=file_path,
                    line_number=e.lineno or 0,
                    column=e.offset or 0,
                    error_type="SyntaxError",
                    message=str(e.msg),
                    severity=ErrorSeverity.CRITICAL.value,
                    code_snippet=code_snippet
                )
                errors.append(error)
                return False, errors
                
        except Exception as e:
            error = SyntaxError(
                file_path=file_path,
                line_number=0,
                column=0,
                error_type="FileError",
                message=f"无法读取文件: {str(e)}",
                severity=ErrorSeverity.HIGH.value,
                code_snippet=None
            )
            errors.append(error)
            return False, errors
    
    def check_imports(self, file_path: str) -> List[ImportError]:
        """检查导入错误"""
        import_errors = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                tree = ast.parse(content, filename=file_path)
            except SyntaxError:
                # 如果语法错误，跳过导入检查
                return import_errors
            
            # 分析导入语句
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        self._check_module_import(file_path, node.lineno, alias.name, import_errors)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        self._check_module_import(file_path, node.lineno, node.module, import_errors)
                        
        except Exception as e:
            logger.error(f"检查导入时出错 {file_path}: {e}")
        
        return import_errors
    
    def _check_module_import(self, file_path: str, line_number: int, module_name: str, import_errors: List[ImportError]):
        """检查单个模块导入"""
        try:
            # 跳过标准库和相对导入的检查
            if (module_name.startswith('.') or 
                module_name in ['os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'typing', 
                               'collections', 'functools', 'itertools', 'logging', 'traceback',
                               'ast', 'importlib', 'enum', 'dataclasses']):
                return
            
            # 尝试导入模块
            spec = importlib.util.find_spec(module_name)
            if spec is None:
                severity = ErrorSeverity.HIGH if self.classify_file_importance(file_path) == FileImportance.CORE else ErrorSeverity.MEDIUM
                
                import_errors.append(ImportError(
                    file_path=file_path,
                    line_number=line_number,
                    module_name=module_name,
                    error_message=f"模块 '{module_name}' 未找到",
                    severity=severity.value
                ))
                
        except Exception as e:
            # 导入检查本身出错，记录为低优先级
            import_errors.append(ImportError(
                file_path=file_path,
                line_number=line_number,
                module_name=module_name,
                error_message=f"导入检查失败: {str(e)}",
                severity=ErrorSeverity.LOW.value
            ))
    
    def analyze_file(self, file_path: str) -> FileAnalysis:
        """分析单个文件"""
        logger.info(f"分析文件: {file_path}")
        
        importance = self.classify_file_importance(file_path)
        is_parseable, syntax_errors = self.check_syntax(file_path)
        import_errors = self.check_imports(file_path)
        
        # 统计文件信息
        line_count = 0
        function_count = 0
        class_count = 0
        import_count = 0
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                line_count = len(content.split('\n'))
            
            if is_parseable:
                tree = ast.parse(content, filename=file_path)
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        function_count += 1
                    elif isinstance(node, ast.ClassDef):
                        class_count += 1
                    elif isinstance(node, (ast.Import, ast.ImportFrom)):
                        import_count += 1
                        
        except Exception as e:
            logger.error(f"统计文件信息时出错 {file_path}: {e}")
        
        return FileAnalysis(
            file_path=file_path,
            importance=importance.value,
            syntax_errors=syntax_errors,
            import_errors=import_errors,
            is_parseable=is_parseable,
            line_count=line_count,
            function_count=function_count,
            class_count=class_count,
            import_count=import_count
        )
    
    def find_python_files(self) -> List[str]:
        """查找所有Python文件"""
        python_files = []
        
        # 遍历项目目录
        for root, dirs, files in os.walk(self.project_root):
            # 跳过一些不需要检查的目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    python_files.append(file_path)
        
        return python_files
    
    def check_all_files(self) -> Dict[str, Any]:
        """检查所有Python文件"""
        logger.info("🔍 开始检查所有Python文件...")
        
        python_files = self.find_python_files()
        logger.info(f"找到 {len(python_files)} 个Python文件")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_files': len(python_files),
            'files': [],
            'summary': {
                'by_importance': {},
                'by_severity': {},
                'total_errors': 0,
                'parseable_files': 0,
                'unparseable_files': 0
            }
        }
        
        # 分析每个文件
        for file_path in python_files:
            try:
                analysis = self.analyze_file(file_path)
                results['files'].append(asdict(analysis))
                
                # 更新统计
                importance_key = analysis.importance.value
                if importance_key not in results['summary']['by_importance']:
                    results['summary']['by_importance'][importance_key] = 0
                results['summary']['by_importance'][importance_key] += 1
                
                if analysis.is_parseable:
                    results['summary']['parseable_files'] += 1
                else:
                    results['summary']['unparseable_files'] += 1
                
                # 统计错误
                all_errors = analysis.syntax_errors + analysis.import_errors
                results['summary']['total_errors'] += len(all_errors)

                for error in all_errors:
                    severity_key = error.severity  # 已经是字符串了
                    if severity_key not in results['summary']['by_severity']:
                        results['summary']['by_severity'][severity_key] = 0
                    results['summary']['by_severity'][severity_key] += 1
                    
            except Exception as e:
                logger.error(f"分析文件失败 {file_path}: {e}")
        
        logger.info(f"✅ 文件检查完成，发现 {results['summary']['total_errors']} 个问题")
        return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川项目语法检查工具')
    parser.add_argument('action', choices=['check', 'analyze', 'report'], 
                       help='执行的操作')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    checker = SyntaxChecker()
    
    if args.action in ['check', 'analyze', 'report']:
        results = checker.check_all_files()
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 检查结果已保存到: {args.output}")
        else:
            print(json.dumps(results, ensure_ascii=False, indent=2))
        
        # 显示摘要
        summary = results['summary']
        print(f"\n📊 检查摘要:")
        print(f"   总文件数: {results['total_files']}")
        print(f"   可解析文件: {summary['parseable_files']}")
        print(f"   语法错误文件: {summary['unparseable_files']}")
        print(f"   总问题数: {summary['total_errors']}")
        
        print(f"\n📋 按重要性分布:")
        for importance, count in summary['by_importance'].items():
            print(f"   {importance}: {count} 个文件")
        
        print(f"\n⚠️ 按严重程度分布:")
        for severity, count in summary['by_severity'].items():
            print(f"   {severity}: {count} 个问题")


if __name__ == '__main__':
    main()
