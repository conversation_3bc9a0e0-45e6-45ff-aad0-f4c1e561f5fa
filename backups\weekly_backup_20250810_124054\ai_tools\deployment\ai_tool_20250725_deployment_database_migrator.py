#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目数据库迁移工具，处理SQLite和PostgreSQL之间的数据迁移
清理条件: 项目不再需要数据库迁移时可删除
"""

import os
import sys
import json
import sqlite3
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import shutil

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

try:
    from qianchuan_aw.utils.config_loader import load_settings
    from qianchuan_aw.database.database import get_database_url, get_database_path
    from qianchuan_aw.database.models import Base
    from sqlalchemy import create_engine, text
    from sqlalchemy.orm import sessionmaker
except ImportError as e:
    print(f"警告: 无法导入项目模块: {e}")
    print("请确保在项目根目录运行此脚本")

class QianchuanDatabaseMigrator:
    """千川自动化项目数据库迁移器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.migration_log = []
        self.backup_dir = self.project_root / "ai_temp" / "db_backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def backup_sqlite_database(self, sqlite_path: str) -> Optional[str]:
        """备份SQLite数据库"""
        print(f"💾 备份SQLite数据库: {sqlite_path}")
        
        try:
            sqlite_file = Path(sqlite_path)
            if not sqlite_file.exists():
                print(f"❌ SQLite文件不存在: {sqlite_path}")
                return None
            
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            backup_file = self.backup_dir / f"backup_{sqlite_file.name}"
            
            shutil.copy2(sqlite_file, backup_file)
            self.migration_log.append(f"备份SQLite数据库: {sqlite_path} -> {backup_file}")
            
            print(f"✅ SQLite数据库已备份到: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            print(f"❌ SQLite备份失败: {e}")
            return None
    
    def export_sqlite_to_sql(self, sqlite_path: str) -> Optional[str]:
        """将SQLite数据库导出为SQL文件"""
        print(f"📤 导出SQLite数据库为SQL文件...")
        
        try:
            sqlite_file = Path(sqlite_path)
            if not sqlite_file.exists():
                print(f"❌ SQLite文件不存在: {sqlite_path}")
                return None
            
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            sql_file = self.backup_dir / f"export_{sqlite_file.stem}.sql"
            
            # 使用sqlite3命令行工具导出
            with open(sql_file, 'w', encoding='utf-8') as f:
                result = subprocess.run([
                    'sqlite3', str(sqlite_file), '.dump'
                ], stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode != 0:
                print(f"❌ SQLite导出失败: {result.stderr}")
                return None
            
            self.migration_log.append(f"导出SQLite为SQL: {sqlite_path} -> {sql_file}")
            print(f"✅ SQLite数据已导出到: {sql_file}")
            return str(sql_file)
            
        except Exception as e:
            print(f"❌ SQLite导出失败: {e}")
            return None
    
    def create_postgresql_database(self, db_config: Dict[str, Any]) -> bool:
        """创建PostgreSQL数据库"""
        print("🗄️ 创建PostgreSQL数据库...")
        
        try:
            # 连接到postgres数据库创建目标数据库
            admin_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/postgres"
            admin_engine = create_engine(admin_url)
            
            with admin_engine.connect() as conn:
                # 检查数据库是否存在
                result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{db_config['dbname']}'"))
                if result.fetchone():
                    print(f"✅ 数据库 {db_config['dbname']} 已存在")
                else:
                    # 创建数据库
                    conn.execute(text("COMMIT"))  # 结束当前事务
                    conn.execute(text(f"CREATE DATABASE {db_config['dbname']}"))
                    print(f"✅ 数据库 {db_config['dbname']} 创建成功")
            
            admin_engine.dispose()
            
            # 连接到新数据库并创建表结构
            target_url = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['dbname']}"
            target_engine = create_engine(target_url)
            
            # 创建所有表
            Base.metadata.create_all(target_engine)
            target_engine.dispose()
            
            self.migration_log.append(f"创建PostgreSQL数据库: {db_config['dbname']}")
            print("✅ PostgreSQL数据库和表结构创建完成")
            return True
            
        except Exception as e:
            print(f"❌ PostgreSQL数据库创建失败: {e}")
            return False
    
    def migrate_data_sqlite_to_postgresql(self, sqlite_path: str, pg_config: Dict[str, Any]) -> bool:
        """从SQLite迁移数据到PostgreSQL"""
        print("🔄 迁移数据从SQLite到PostgreSQL...")
        
        try:
            # 连接SQLite
            sqlite_conn = sqlite3.connect(sqlite_path)
            sqlite_conn.row_factory = sqlite3.Row
            
            # 连接PostgreSQL
            pg_url = f"postgresql://{pg_config['user']}:{pg_config['password']}@{pg_config['host']}:{pg_config['port']}/{pg_config['dbname']}"
            pg_engine = create_engine(pg_url)
            SessionLocal = sessionmaker(bind=pg_engine)
            
            # 获取所有表名
            cursor = sqlite_conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            migrated_tables = []
            
            with SessionLocal() as pg_session:
                for table_name in tables:
                    try:
                        print(f"  迁移表: {table_name}")
                        
                        # 从SQLite读取数据
                        cursor.execute(f"SELECT * FROM {table_name}")
                        rows = cursor.fetchall()
                        
                        if not rows:
                            print(f"    表 {table_name} 为空，跳过")
                            continue
                        
                        # 获取列名
                        columns = [description[0] for description in cursor.description]
                        
                        # 构建插入语句
                        placeholders = ', '.join(['%s'] * len(columns))
                        insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
                        
                        # 批量插入到PostgreSQL
                        data_to_insert = []
                        for row in rows:
                            data_to_insert.append(tuple(row))
                        
                        if data_to_insert:
                            pg_session.execute(text(insert_sql), data_to_insert)
                            pg_session.commit()
                            migrated_tables.append(table_name)
                            print(f"    ✅ 表 {table_name} 迁移完成 ({len(data_to_insert)} 行)")
                        
                    except Exception as e:
                        print(f"    ❌ 表 {table_name} 迁移失败: {e}")
                        pg_session.rollback()
                        continue
            
            sqlite_conn.close()
            pg_engine.dispose()
            
            self.migration_log.append(f"数据迁移完成: {len(migrated_tables)} 个表")
            print(f"✅ 数据迁移完成，成功迁移 {len(migrated_tables)} 个表")
            return True
            
        except Exception as e:
            print(f"❌ 数据迁移失败: {e}")
            return False
    
    def initialize_empty_database(self, db_type: str, db_config: Dict[str, Any]) -> bool:
        """初始化空数据库"""
        print(f"🔧 初始化空{db_type}数据库...")
        
        try:
            if db_type == 'sqlite':
                # SQLite初始化
                db_path = db_config.get('db_path', 'qianchuan_aw.db')
                db_dir = Path(db_path).parent
                db_dir.mkdir(parents=True, exist_ok=True)
                
                engine = create_engine(f"sqlite:///{db_path}")
                Base.metadata.create_all(engine)
                engine.dispose()
                
                self.migration_log.append(f"初始化SQLite数据库: {db_path}")
                
            elif db_type == 'postgresql':
                # PostgreSQL初始化
                if not self.create_postgresql_database(db_config):
                    return False
            
            print(f"✅ {db_type}数据库初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ {db_type}数据库初始化失败: {e}")
            return False
    
    def verify_migration(self, source_path: str, target_config: Dict[str, Any]) -> bool:
        """验证迁移结果"""
        print("🔍 验证迁移结果...")
        
        try:
            # 连接源数据库
            sqlite_conn = sqlite3.connect(source_path)
            cursor = sqlite_conn.cursor()
            
            # 连接目标数据库
            pg_url = f"postgresql://{target_config['user']}:{target_config['password']}@{target_config['host']}:{target_config['port']}/{target_config['dbname']}"
            pg_engine = create_engine(pg_url)
            
            # 获取表列表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            verification_results = []
            
            for table_name in tables:
                try:
                    # 统计源表行数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    source_count = cursor.fetchone()[0]
                    
                    # 统计目标表行数
                    with pg_engine.connect() as pg_conn:
                        result = pg_conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        target_count = result.fetchone()[0]
                    
                    verification_results.append({
                        'table': table_name,
                        'source_count': source_count,
                        'target_count': target_count,
                        'match': source_count == target_count
                    })
                    
                    if source_count == target_count:
                        print(f"  ✅ {table_name}: {source_count} 行")
                    else:
                        print(f"  ❌ {table_name}: 源 {source_count} 行, 目标 {target_count} 行")
                
                except Exception as e:
                    print(f"  ❌ {table_name}: 验证失败 - {e}")
                    verification_results.append({
                        'table': table_name,
                        'error': str(e)
                    })
            
            sqlite_conn.close()
            pg_engine.dispose()
            
            # 统计验证结果
            successful_tables = sum(1 for r in verification_results if r.get('match', False))
            total_tables = len(verification_results)
            
            print(f"\n📊 验证结果: {successful_tables}/{total_tables} 个表验证成功")
            
            return successful_tables == total_tables
            
        except Exception as e:
            print(f"❌ 迁移验证失败: {e}")
            return False
    
    def interactive_migration(self) -> bool:
        """交互式数据库迁移"""
        print("🔧 交互式数据库迁移")
        print("=" * 50)
        
        try:
            # 检测当前数据库配置
            settings = load_settings()
            current_db = settings.get('database', {})
            current_type = current_db.get('type', 'sqlite')
            
            print(f"\n当前数据库类型: {current_type}")
            
            if current_type == 'sqlite':
                sqlite_path = get_database_path()
                if sqlite_path and Path(sqlite_path).exists():
                    print(f"SQLite文件位置: {sqlite_path}")
                    
                    # 询问是否迁移到PostgreSQL
                    migrate_choice = input("\n是否要迁移到PostgreSQL? (y/n): ").strip().lower()
                    
                    if migrate_choice == 'y':
                        print("\n请提供PostgreSQL配置:")
                        pg_config = {
                            'host': input("主机 (默认: localhost): ").strip() or "localhost",
                            'port': int(input("端口 (默认: 5432): ").strip() or "5432"),
                            'dbname': input("数据库名称 (默认: qianchuan_analytics): ").strip() or "qianchuan_analytics",
                            'user': input("用户名: ").strip(),
                            'password': input("密码: ").strip()
                        }
                        
                        # 执行迁移
                        print("\n🚀 开始数据库迁移...")
                        
                        # 1. 备份SQLite
                        backup_path = self.backup_sqlite_database(sqlite_path)
                        if not backup_path:
                            return False
                        
                        # 2. 创建PostgreSQL数据库
                        if not self.create_postgresql_database(pg_config):
                            return False
                        
                        # 3. 迁移数据
                        if not self.migrate_data_sqlite_to_postgresql(sqlite_path, pg_config):
                            return False
                        
                        # 4. 验证迁移
                        if not self.verify_migration(sqlite_path, pg_config):
                            print("⚠️ 迁移验证失败，请检查数据完整性")
                        
                        print("\n✅ 数据库迁移完成!")
                        print("📝 请记得更新config/settings.yml中的数据库配置")
                        
                        return True
                    else:
                        print("保持使用SQLite数据库")
                        return True
                else:
                    print("❌ 未找到SQLite数据库文件")
                    
                    # 询问是否初始化新数据库
                    init_choice = input("是否初始化新数据库? (y/n): ").strip().lower()
                    if init_choice == 'y':
                        db_type = input("数据库类型 (sqlite/postgresql): ").strip().lower()
                        
                        if db_type == 'sqlite':
                            db_config = {'db_path': sqlite_path or 'qianchuan_aw.db'}
                            return self.initialize_empty_database('sqlite', db_config)
                        elif db_type == 'postgresql':
                            pg_config = {
                                'host': input("主机: ").strip(),
                                'port': int(input("端口: ").strip()),
                                'dbname': input("数据库名称: ").strip(),
                                'user': input("用户名: ").strip(),
                                'password': input("密码: ").strip()
                            }
                            return self.initialize_empty_database('postgresql', pg_config)
            
            elif current_type == 'postgresql':
                print("当前使用PostgreSQL数据库")
                print("如需迁移，请手动备份数据库")
                return True
            
            return False
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消迁移")
            return False
        except Exception as e:
            print(f"\n❌ 迁移失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 千川自动化项目数据库迁移工具")
    print("=" * 60)
    
    migrator = QianchuanDatabaseMigrator()
    
    # 交互式迁移
    success = migrator.interactive_migration()
    
    if success:
        print("\n🎉 数据库迁移成功完成!")
        print("\n📋 迁移日志:")
        for log_entry in migrator.migration_log:
            print(f"  - {log_entry}")
        
        if migrator.backup_dir.exists():
            print(f"\n💾 备份文件位置: {migrator.backup_dir}")
    else:
        print("\n❌ 数据库迁移失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
