#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 修复被拒绝素材在数据库中的错误文件路径
清理条件: 成为项目永久维护工具，不删除
"""

import os
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from loguru import logger

def fix_rejected_material_paths():
    """修复被拒绝素材的数据库文件路径"""
    
    logger.info("🔧 开始修复被拒绝素材的数据库文件路径")
    
    # 需要修复的文件路径映射
    path_fixes = [
        {
            'id': 7910,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\8.1-谢莉-(27).mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-谢莉-(27).mp4",
            'filename': "8.1-谢莉-(27).mp4"
        },
        {
            'id': 7839,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\8.1-王梦珂-17.mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-王梦珂-17.mp4",
            'filename': "8.1-王梦珂-17.mp4"
        },
        {
            'id': 7813,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\8.1-杨婷婷-15.mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-杨婷婷-15.mp4",
            'filename': "8.1-杨婷婷-15.mp4"
        },
        {
            'id': 7757,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\8.1-张明鑫-19.mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-张明鑫-19.mp4",
            'filename': "8.1-张明鑫-19.mp4"
        },
        {
            'id': 7694,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\7.31-郭世攀-16.mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/7.31-郭世攀-16.mp4",
            'filename': "7.31-郭世攀-16.mp4"
        },
        {
            'id': 7923,
            'old_path': "D:/workflow_assets\\03_materials_approved\\缇萃百货\\2025-08-02\\8.1-郭世攀-1.mp4",
            'new_path': "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-郭世攀-1.mp4",
            'filename': "8.1-郭世攀-1.mp4"
        }
    ]
    
    logger.info(f"准备修复 {len(path_fixes)} 个文件路径")
    
    # 首先验证新路径是否存在
    valid_fixes = []
    for fix in path_fixes:
        new_path_normalized = os.path.normpath(fix['new_path'])
        if os.path.exists(new_path_normalized):
            valid_fixes.append(fix)
            logger.success(f"✅ 验证通过: {fix['filename']} -> {new_path_normalized}")
        else:
            logger.error(f"❌ 新路径不存在: {fix['filename']} -> {new_path_normalized}")
    
    logger.info(f"有 {len(valid_fixes)} 个路径可以安全修复")
    
    if len(valid_fixes) == 0:
        logger.warning("没有可以修复的路径，退出")
        return
    
    # 生成SQL更新语句
    logger.info("生成SQL更新语句:")
    for fix in valid_fixes:
        sql = f"UPDATE local_creatives SET file_path = '{fix['new_path']}', updated_at = NOW() WHERE id = {fix['id']};"
        logger.info(f"SQL: {sql}")
    
    logger.info("请手动执行上述SQL语句来修复数据库路径")
    
    return valid_fixes

def verify_fix_effectiveness():
    """验证修复效果"""
    
    logger.info("🔍 验证修复效果")
    
    # 这些是修复后应该存在的路径
    expected_paths = [
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-谢莉-(27).mp4",
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-王梦珂-17.mp4",
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-杨婷婷-15.mp4",
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-张明鑫-19.mp4",
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/7.31-郭世攀-16.mp4",
        "D:/workflow_assets/00_uploaded_archive/缇萃百货/20250802/8.1-郭世攀-1.mp4"
    ]
    
    all_exist = True
    for path in expected_paths:
        normalized_path = os.path.normpath(path)
        if os.path.exists(normalized_path):
            file_size = os.path.getsize(normalized_path) / (1024 * 1024)
            logger.success(f"✅ 文件存在: {os.path.basename(path)} ({file_size:.1f}MB)")
        else:
            logger.error(f"❌ 文件不存在: {path}")
            all_exist = False
    
    if all_exist:
        logger.success("🎉 所有文件都存在，修复后将能正确删除被拒绝的素材！")
    else:
        logger.warning("⚠️ 部分文件不存在，需要进一步调查")
    
    return all_exist

if __name__ == "__main__":
    logger.info("🚀 启动被拒绝素材路径修复工具")
    
    try:
        # 生成修复方案
        valid_fixes = fix_rejected_material_paths()
        
        # 验证修复效果
        verify_fix_effectiveness()
        
        logger.info("✅ 修复工具执行完成")
        
        if valid_fixes:
            logger.info("📋 下一步操作:")
            logger.info("1. 手动执行上述SQL语句修复数据库路径")
            logger.info("2. 重新运行千川调度器测试被拒绝素材清理功能")
            logger.info("3. 验证不再出现'文件不存在'的警告")
        
    except Exception as e:
        logger.error(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
