#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川项目深度工作流异常诊断工具
清理条件: 项目工作流监控完全重构时可删除

千川项目深度工作流异常诊断工具
============================

专门用于分析特定时间段的工作流异常，包括文件入库、上传流程、状态转换等问题。

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py [start_time] [end_time]

示例:
python ai_tools/maintenance/ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py "2025-07-25 00:00:00" "2025-07-25 05:00:00"
"""

import os
import sys
import re
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from src.qianchuan_aw.utils.logger import logger
    from src.qianchuan_aw.database.database import SessionLocal
    from src.qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign
    from sqlalchemy import text, func
except ImportError as e:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)
    logger.error(f"导入失败: {e}")


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: datetime
    level: str
    module: str
    function: str
    line: int
    message: str
    raw_line: str


@dataclass
class AnomalyEvent:
    """异常事件"""
    timestamp: datetime
    event_type: str
    severity: str
    description: str
    details: Dict[str, Any]
    related_files: List[str]
    impact_scope: str


class WorkflowAnomalyDiagnostic:
    """工作流异常诊断器"""
    
    def __init__(self, start_time: str, end_time: str):
        self.start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        self.end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
        self.project_root = Path(project_root)
        self.log_dir = self.project_root / "logs"
        
        # 分析结果存储
        self.log_entries = []
        self.anomaly_events = []
        self.file_operations = defaultdict(list)
        self.status_transitions = defaultdict(list)
        self.api_calls = defaultdict(list)
        self.error_patterns = Counter()
        
        # 日志解析正则表达式
        self.log_pattern = re.compile(
            r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) \| (\w+)\s+\| ([^:]+):([^:]+):(\d+) - (.+)'
        )
        
        logger.info(f"🔍 初始化工作流异常诊断器")
        logger.info(f"📅 分析时间段: {self.start_time} - {self.end_time}")
    
    def parse_log_file(self, log_file_path: Path) -> List[LogEntry]:
        """解析日志文件"""
        entries = []
        
        if not log_file_path.exists():
            logger.warning(f"日志文件不存在: {log_file_path}")
            return entries
        
        logger.info(f"📋 解析日志文件: {log_file_path}")
        
        try:
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    match = self.log_pattern.match(line)
                    if match:
                        timestamp_str, level, module, function, line_no, message = match.groups()
                        timestamp = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S")
                        
                        # 只保留指定时间段的日志
                        if self.start_time <= timestamp <= self.end_time:
                            entry = LogEntry(
                                timestamp=timestamp,
                                level=level,
                                module=module,
                                function=function,
                                line=int(line_no),
                                message=message,
                                raw_line=line
                            )
                            entries.append(entry)
                    else:
                        # 处理多行日志（如异常堆栈）
                        if entries and line.startswith(' '):
                            entries[-1].message += '\n' + line
                            entries[-1].raw_line += '\n' + line
        
        except Exception as e:
            logger.error(f"解析日志文件失败 {log_file_path}: {e}")
        
        logger.info(f"📊 解析到 {len(entries)} 条时间段内的日志")
        return entries
    
    def analyze_file_operations(self):
        """分析文件操作异常"""
        logger.info("📁 分析文件操作异常...")
        
        file_keywords = [
            '文件入库', '文件移动', '文件删除', '文件复制', '文件重命名',
            'ingest', 'move', 'delete', 'copy', 'rename',
            '00_', 'workflow_assets', 'materials_to_process'
        ]
        
        for entry in self.log_entries:
            for keyword in file_keywords:
                if keyword in entry.message.lower():
                    self.file_operations[keyword].append(entry)
        
        # 检测文件操作异常模式
        self._detect_file_anomalies()
    
    def _detect_file_anomalies(self):
        """检测文件操作异常"""
        # 检测文件数量异常波动
        file_counts = defaultdict(list)
        
        for entry in self.log_entries:
            if '入库' in entry.message and '个文件' in entry.message:
                # 提取文件数量
                import re
                match = re.search(r'(\d+)个文件', entry.message)
                if match:
                    count = int(match.group(1))
                    file_counts['ingest'].append((entry.timestamp, count))
        
        # 分析数量波动
        if file_counts['ingest']:
            counts = [count for _, count in file_counts['ingest']]
            if len(counts) > 1:
                # 检测异常波动（减少后又增加）
                for i in range(1, len(counts)):
                    if counts[i-1] > counts[i] and i < len(counts) - 1 and counts[i+1] > counts[i]:
                        timestamp = file_counts['ingest'][i][0]
                        self.anomaly_events.append(AnomalyEvent(
                            timestamp=timestamp,
                            event_type='file_count_fluctuation',
                            severity='medium',
                            description=f'文件入库数量异常波动: {counts[i-1]} → {counts[i]} → {counts[i+1]}',
                            details={'counts': counts[i-1:i+2], 'index': i},
                            related_files=[],
                            impact_scope='file_ingestion'
                        ))
    
    def analyze_upload_anomalies(self):
        """分析上传流程异常"""
        logger.info("📤 分析上传流程异常...")
        
        upload_keywords = [
            '上传', 'upload', '重试', 'retry', '失败', 'failed',
            'timeout', '超时', 'api调用', 'api call'
        ]
        
        upload_events = []
        for entry in self.log_entries:
            for keyword in upload_keywords:
                if keyword in entry.message.lower():
                    upload_events.append(entry)
                    break
        
        # 分析上传时间分布
        upload_times = [entry.timestamp for entry in upload_events]
        if upload_times:
            duration = (max(upload_times) - min(upload_times)).total_seconds() / 3600
            if duration > 4:  # 超过4小时的上传
                self.anomaly_events.append(AnomalyEvent(
                    timestamp=min(upload_times),
                    event_type='prolonged_upload',
                    severity='high',
                    description=f'上传流程异常延长: {duration:.1f}小时',
                    details={'duration_hours': duration, 'event_count': len(upload_events)},
                    related_files=[],
                    impact_scope='upload_workflow'
                ))
        
        # 检测重复API调用
        api_calls = defaultdict(list)
        for entry in upload_events:
            if 'api' in entry.message.lower():
                # 提取API相关信息
                api_calls[entry.function].append(entry)
        
        for function, calls in api_calls.items():
            if len(calls) > 100:  # 超过100次调用
                self.anomaly_events.append(AnomalyEvent(
                    timestamp=calls[0].timestamp,
                    event_type='excessive_api_calls',
                    severity='high',
                    description=f'API调用异常频繁: {function} 调用{len(calls)}次',
                    details={'function': function, 'call_count': len(calls)},
                    related_files=[],
                    impact_scope='api_performance'
                ))
    
    def analyze_status_transitions(self):
        """分析状态转换异常"""
        logger.info("🔄 分析状态转换异常...")
        
        status_keywords = [
            '状态', 'status', '转换', 'transition', '变更', 'change',
            'pending', MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADED.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value
        ]
        
        for entry in self.log_entries:
            for keyword in status_keywords:
                if keyword in entry.message.lower():
                    self.status_transitions[keyword].append(entry)
        
        # 检测状态回滚
        status_changes = []
        for entry in self.log_entries:
            if '状态已变更' in entry.message or 'status changed' in entry.message.lower():
                status_changes.append(entry)
        
        # 分析状态转换模式
        if len(status_changes) > 50:  # 状态变更过于频繁
            self.anomaly_events.append(AnomalyEvent(
                timestamp=status_changes[0].timestamp,
                event_type='excessive_status_changes',
                severity='medium',
                description=f'状态转换异常频繁: {len(status_changes)}次变更',
                details={'change_count': len(status_changes)},
                related_files=[],
                impact_scope='status_management'
            ))
    
    def analyze_error_patterns(self):
        """分析错误模式"""
        logger.info("❌ 分析错误模式...")
        
        error_entries = [entry for entry in self.log_entries if entry.level in ['ERROR', 'CRITICAL']]
        warning_entries = [entry for entry in self.log_entries if entry.level == 'WARNING']
        
        # 统计错误类型
        for entry in error_entries:
            # 提取错误关键词
            message_lower = entry.message.lower()
            if 'timeout' in message_lower or '超时' in message_lower:
                self.error_patterns['timeout'] += 1
            elif 'connection' in message_lower or '连接' in message_lower:
                self.error_patterns['connection'] += 1
            elif 'api' in message_lower:
                self.error_patterns['api_error'] += 1
            elif 'database' in message_lower or '数据库' in message_lower:
                self.error_patterns['database_error'] += 1
            else:
                self.error_patterns['other_error'] += 1
        
        # 检测错误集中爆发
        if len(error_entries) > 10:
            # 按小时分组统计错误
            hourly_errors = defaultdict(int)
            for entry in error_entries:
                hour_key = entry.timestamp.strftime("%Y-%m-%d %H:00")
                hourly_errors[hour_key] += 1
            
            # 找出错误集中的小时
            for hour, count in hourly_errors.items():
                if count > 5:
                    self.anomaly_events.append(AnomalyEvent(
                        timestamp=datetime.strptime(hour, "%Y-%m-%d %H:00"),
                        event_type='error_burst',
                        severity='high',
                        description=f'错误集中爆发: {hour} 发生{count}个错误',
                        details={'hour': hour, 'error_count': count},
                        related_files=[],
                        impact_scope='system_stability'
                    ))
    
    def query_database_status_changes(self) -> Dict[str, Any]:
        """查询数据库中的状态变化"""
        logger.info("🗄️ 查询数据库状态变化...")
        
        try:
            db = SessionLocal()
            
            # 查询指定时间段内的状态变化
            status_changes = db.execute(text("""
                SELECT 
                    id, filename, status, updated_at, file_path,
                    LAG(status) OVER (PARTITION BY id ORDER BY updated_at) as prev_status
                FROM local_creatives 
                WHERE updated_at BETWEEN :start_time AND :end_time
                ORDER BY updated_at
            """), {
                'start_time': self.start_time,
                'end_time': self.end_time
            }).fetchall()
            
            # 统计状态分布
            status_distribution = db.execute(text("""
                SELECT status, COUNT(*) as count
                FROM local_creatives 
                WHERE updated_at BETWEEN :start_time AND :end_time
                GROUP BY status
                ORDER BY count DESC
            """), {
                'start_time': self.start_time,
                'end_time': self.end_time
            }).fetchall()
            
            db.close()
            
            return {
                'status_changes': [dict(row) for row in status_changes],
                'status_distribution': [dict(row) for row in status_distribution]
            }
            
        except Exception as e:
            logger.error(f"查询数据库失败: {e}")
            return {'status_changes': [], 'status_distribution': []}
    
    def generate_diagnosis_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        logger.info("📋 生成诊断报告...")
        
        # 按严重程度排序异常事件
        self.anomaly_events.sort(key=lambda x: {
            'critical': 0, 'high': 1, 'medium': 2, 'low': 3
        }.get(x.severity, 4))
        
        # 按时间排序异常事件
        timeline_events = sorted(self.anomaly_events, key=lambda x: x.timestamp)
        
        # 查询数据库状态
        db_status = self.query_database_status_changes()
        
        report = {
            'diagnosis_info': {
                'time_range': f"{self.start_time} - {self.end_time}",
                'total_log_entries': len(self.log_entries),
                'total_anomalies': len(self.anomaly_events),
                'analysis_timestamp': datetime.now().isoformat()
            },
            'anomaly_summary': {
                'critical': len([e for e in self.anomaly_events if e.severity == 'critical']),
                'high': len([e for e in self.anomaly_events if e.severity == 'high']),
                'medium': len([e for e in self.anomaly_events if e.severity == 'medium']),
                'low': len([e for e in self.anomaly_events if e.severity == 'low'])
            },
            'timeline_events': [asdict(event) for event in timeline_events],
            'error_patterns': dict(self.error_patterns),
            'database_status': db_status,
            'file_operations_summary': {
                key: len(entries) for key, entries in self.file_operations.items()
            },
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[Dict[str, str]]:
        """生成修复建议"""
        recommendations = []
        
        # 基于异常事件生成建议
        for event in self.anomaly_events:
            if event.event_type == 'prolonged_upload':
                recommendations.append({
                    'priority': 'high',
                    'category': 'upload_optimization',
                    'description': '优化上传流程，增加并发控制和超时处理',
                    'action': '检查网络连接，调整上传超时参数，实现断点续传'
                })
            elif event.event_type == 'excessive_api_calls':
                recommendations.append({
                    'priority': 'high',
                    'category': 'api_optimization',
                    'description': '优化API调用频率，实现请求去重和缓存',
                    'action': '添加API调用限流器，实现请求合并和结果缓存'
                })
            elif event.event_type == 'error_burst':
                recommendations.append({
                    'priority': 'medium',
                    'category': 'error_handling',
                    'description': '改进错误处理机制，防止错误集中爆发',
                    'action': '实现熔断器模式，增加错误恢复机制'
                })
        
        # 基于错误模式生成建议
        if self.error_patterns['timeout'] > 5:
            recommendations.append({
                'priority': 'medium',
                'category': 'timeout_handling',
                'description': '优化超时处理机制',
                'action': '调整超时参数，实现指数退避重试'
            })
        
        return recommendations
    
    def run_diagnosis(self) -> Dict[str, Any]:
        """运行完整诊断"""
        logger.info("🚀 开始工作流异常诊断...")
        
        # 确定需要分析的日志文件
        log_files = []
        current_date = self.start_time.date()
        end_date = self.end_time.date()
        
        while current_date <= end_date:
            log_file = self.log_dir / f"app_{current_date.strftime('%Y-%m-%d')}.log"
            if log_file.exists():
                log_files.append(log_file)
            current_date += timedelta(days=1)
        
        # 解析所有相关日志文件
        for log_file in log_files:
            entries = self.parse_log_file(log_file)
            self.log_entries.extend(entries)
        
        logger.info(f"📊 总共解析到 {len(self.log_entries)} 条日志")
        
        # 执行各项分析
        self.analyze_file_operations()
        self.analyze_upload_anomalies()
        self.analyze_status_transitions()
        self.analyze_error_patterns()
        
        # 生成诊断报告
        report = self.generate_diagnosis_report()
        
        logger.info("✅ 工作流异常诊断完成")
        return report


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川项目工作流异常诊断工具')
    parser.add_argument('start_time', help='开始时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('end_time', help='结束时间 (YYYY-MM-DD HH:MM:SS)')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    try:
        # 创建诊断器并运行
        diagnostic = WorkflowAnomalyDiagnostic(args.start_time, args.end_time)
        report = diagnostic.run_diagnosis()
        
        # 输出结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"📄 诊断报告已保存到: {args.output}")
        
        # 显示摘要
        print(f"\n📊 工作流异常诊断摘要:")
        print(f"   时间范围: {args.start_time} - {args.end_time}")
        print(f"   日志条目: {report['diagnosis_info']['total_log_entries']}")
        print(f"   异常事件: {report['diagnosis_info']['total_anomalies']}")
        print(f"   严重异常: {report['anomaly_summary']['critical'] + report['anomaly_summary']['high']}")
        print(f"   修复建议: {len(report['recommendations'])}")
        
        if args.verbose:
            print(f"\n🔍 异常事件详情:")
            for event in report['timeline_events']:
                severity_icon = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}.get(event['severity'], '⚪')
                print(f"   {severity_icon} {event['timestamp']}: {event['description']}")
        
        return report
        
    except Exception as e:
        logger.error(f"诊断失败: {e}")
        return None


if __name__ == '__main__':
    main()
