#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急停止Celery并修复状态问题
清理条件: 成为紧急修复工具，长期保留
"""

import os
import sys
import time
import signal
from datetime import datetime, timezone, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative


class EmergencyStopAndFix:
    """紧急停止和修复工具"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
    
    def run_emergency_procedures(self):
        """运行紧急程序"""
        logger.error("🚨 检测到严重问题，启动紧急修复程序")
        logger.error("="*100)
        
        # 1. 诊断当前问题
        self._diagnose_current_issues()
        
        # 2. 停止Celery进程
        self._stop_celery_processes()
        
        # 3. 清理Redis队列
        self._clear_redis_queues()
        
        # 4. 修复状态不一致
        self._fix_state_inconsistencies()
        
        # 5. 修复原子状态管理器
        self._fix_atomic_state_manager()
        
        # 6. 生成修复报告
        self._generate_emergency_report()
    
    def _diagnose_current_issues(self):
        """诊断当前问题"""
        logger.error("🔍 诊断当前问题...")
        
        with SessionLocal() as db:
            # 检查processing状态积压
            processing_count = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value
            ).count()
            
            # 检查最近的processing状态
            recent_processing = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value,
                LocalCreative.updated_at >= datetime.now(timezone.utc) - timedelta(minutes=30)
            ).count()
            
            logger.error(f"❌ 发现 {processing_count} 个processing状态素材")
            logger.error(f"❌ 其中 {recent_processing} 个是最近30分钟内产生的")
            
            self.issues_found.append(f"Processing状态积压: {processing_count}个")
            self.issues_found.append(f"最近产生的积压: {recent_processing}个")
            
            # 检查是否有重复处理
            duplicate_processing = db.execute("""
                SELECT file_path, COUNT(*) as count
                FROM local_creatives 
                WHERE status = MaterialStatus.PROCESSING.value
                GROUP BY file_path
                HAVING COUNT(*) > 1
            """).fetchall()
            
            if duplicate_processing:
                logger.error(f"❌ 发现 {len(duplicate_processing)} 个文件被重复处理")
                self.issues_found.append(f"重复处理文件: {len(duplicate_processing)}个")
    
    def _stop_celery_processes(self):
        """停止Celery进程"""
        logger.error("🛑 停止所有Celery进程...")
        
        try:
            import psutil
            
            celery_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info.get('cmdline', []))
                    if 'celery' in cmdline.lower() and ('worker' in cmdline or 'beat' in cmdline):
                        celery_processes.append({
                            'pid': proc.info['pid'],
                            'cmdline': cmdline
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            logger.error(f"🔍 发现 {len(celery_processes)} 个Celery进程")
            
            for proc_info in celery_processes:
                try:
                    pid = proc_info['pid']
                    logger.error(f"🛑 终止进程 PID {pid}")
                    os.kill(pid, signal.SIGTERM)
                    time.sleep(1)
                    
                    # 如果进程仍然存在，强制终止
                    try:
                        os.kill(pid, signal.SIGKILL)
                        logger.error(f"💀 强制终止进程 PID {pid}")
                    except ProcessLookupError:
                        pass  # 进程已经终止
                        
                except Exception as e:
                    logger.error(f"❌ 终止进程失败: {e}")
            
            self.fixes_applied.append(f"停止了 {len(celery_processes)} 个Celery进程")
            
        except ImportError:
            logger.error("❌ psutil不可用，请手动停止Celery进程")
            self.issues_found.append("无法自动停止Celery进程")
    
    def _clear_redis_queues(self):
        """清理Redis队列"""
        logger.error("🧹 清理Redis队列...")
        
        try:
            import redis
            
            # 连接Redis
            r = redis.Redis(host='localhost', port=6379, db=0)
            
            # 清理Celery相关队列
            celery_keys = r.keys('celery*')
            if celery_keys:
                r.delete(*celery_keys)
                logger.error(f"🗑️ 清理了 {len(celery_keys)} 个Celery队列")
                self.fixes_applied.append(f"清理了 {len(celery_keys)} 个Redis队列")
            else:
                logger.info("ℹ️ Redis队列已清空")
            
        except Exception as e:
            logger.error(f"❌ 清理Redis队列失败: {e}")
            self.issues_found.append(f"Redis队列清理失败: {e}")
    
    def _fix_state_inconsistencies(self):
        """修复状态不一致"""
        logger.error("🔧 修复状态不一致...")
        
        with SessionLocal() as db:
            # 重置所有最近的processing状态
            recent_threshold = datetime.now(timezone.utc) - timedelta(minutes=30)
            
            stuck_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value,
                LocalCreative.updated_at >= recent_threshold
            ).all()
            
            logger.error(f"🔄 重置 {len(stuck_creatives)} 个最近的processing状态")
            
            reset_count = 0
            for creative in stuck_creatives:
                try:
                    creative.status = MaterialStatus.PENDING_UPLOAD.value
                    creative.updated_at = datetime.now(timezone.utc)
                    reset_count += 1
                except Exception as e:
                    logger.error(f"重置素材 {creative.id} 失败: {e}")
            
            db.commit()
            
            self.fixes_applied.append(f"重置了 {reset_count} 个processing状态")
            logger.error(f"✅ 重置了 {reset_count} 个processing状态")
    
    def _fix_atomic_state_manager(self):
        """修复原子状态管理器"""
        logger.error("🔧 修复原子状态管理器逻辑错误...")
        
        # 修复safe_dispatch_upload_task方法的逻辑
        fixed_code = '''
    def safe_dispatch_upload_task(self, creative_id: int, account_id: int, file_path: str, principal_name: str):
        """安全派发上传任务 - 修复版本"""
        try:
            # 首先验证素材存在且状态正确
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id
            ).first()
            
            if not creative:
                raise ValueError(f"素材 {creative_id} 不存在")
            
            if creative.status != MaterialStatus.PENDING_UPLOAD.value:
                logger.warning(f"⚠️ 素材 {creative_id} 状态不是pending_upload (当前: {creative.status})，跳过处理")
                return False
            
            # 使用原子状态转换
            with self.atomic_state_transition(creative_id, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value) as processing_creative:
                # 状态转换成功后才派发任务
                from qianchuan_aw.workflows.tasks import upload_single_video
from qianchuan_aw.utils.unified_material_status import MaterialStatus
                upload_single_video.delay(creative_id, account_id, file_path, principal_name)
                logger.info(f"✅ 安全派发上传任务: {os.path.basename(file_path)}")
                return True
                
        except Exception as e:
            logger.error(f"❌ 安全派发失败: {e}")
            return False
'''
        
        # 写入修复后的代码
        manager_file = os.path.join(project_root, 'src/qianchuan_aw/utils/atomic_state_manager.py')
        
        try:
            # 读取当前文件
            with open(manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换safe_dispatch_upload_task方法
            import re
            
            # 找到方法定义的开始和结束
            pattern = r'def safe_dispatch_upload_task\(self.*?\n        logger\.info\(f"安全派发上传任务.*?\n'
            
            if re.search(pattern, content, re.DOTALL):
                # 替换方法实现
                new_content = re.sub(
                    pattern,
                    fixed_code.strip() + '\n',
                    content,
                    flags=re.DOTALL
                )
                
                # 写回文件
                with open(manager_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                self.fixes_applied.append("修复了原子状态管理器逻辑")
                logger.error("✅ 原子状态管理器逻辑已修复")
            else:
                logger.error("❌ 无法找到safe_dispatch_upload_task方法")
                self.issues_found.append("无法修复原子状态管理器")
                
        except Exception as e:
            logger.error(f"❌ 修复原子状态管理器失败: {e}")
            self.issues_found.append(f"修复原子状态管理器失败: {e}")
    
    def _generate_emergency_report(self):
        """生成紧急修复报告"""
        logger.error("\n📋 紧急修复报告")
        logger.error("="*100)
        
        logger.error(f"🚨 发现问题 ({len(self.issues_found)} 个):")
        for issue in self.issues_found:
            logger.error(f"   ❌ {issue}")
        
        logger.error(f"\n🔧 应用修复 ({len(self.fixes_applied)} 个):")
        for fix in self.fixes_applied:
            logger.error(f"   ✅ {fix}")
        
        logger.error("\n💡 下一步建议:")
        logger.error("   1. 确认所有Celery进程已停止")
        logger.error("   2. 验证processing状态已重置")
        logger.error("   3. 修复原子状态管理器后重新测试")
        logger.error("   4. 小批量测试后再全面重启")


def main():
    """主函数"""
    emergency_fixer = EmergencyStopAndFix()
    emergency_fixer.run_emergency_procedures()
    
    logger.error("🚨 紧急修复程序执行完成")
    logger.error("⚠️ 请检查修复结果，确认问题解决后再重启系统")
    
    return 0


if __name__ == "__main__":
    exit(main())
