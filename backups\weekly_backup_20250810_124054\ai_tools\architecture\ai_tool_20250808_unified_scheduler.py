#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 统一调度器，解决架构混乱问题
清理条件: 成为项目核心组件，长期保留
"""

import os
import sys
import time
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Callable
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_loader import load_settings
from qianchuan_aw.database.database import SessionLocal


class UnifiedScheduler:
    """统一调度器 - 解决模块间阻塞和资源冲突问题"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.workflow_settings = self.app_settings.get('workflow', {})
        self.running = False
        self.modules = {}
        self.module_locks = {}
        self.last_run_times = {}
        
        # 模块独立性保证
        self.max_module_runtime = 300  # 每个模块最大运行时间5分钟
        self.module_cooldown = 30      # 模块间冷却时间30秒
        
        self._register_modules()
    
    def _register_modules(self):
        """注册所有工作流模块"""
        
        # 模块配置：每个模块独立运行，互不阻塞
        self.modules = {
            'file_ingestion': {
                'function': self._safe_file_ingestion,
                'interval': self.workflow_settings.get('file_ingestion', {}).get('interval_seconds', 150),
                'enabled': self.workflow_settings.get('file_ingestion', {}).get('enabled', True),
                'description': '文件摄取和上传',
                'priority': 1  # 高优先级
            },
            'plan_creation': {
                'function': self._safe_plan_creation,
                'interval': self.workflow_settings.get('plan_creation', {}).get('interval_seconds', 60),
                'enabled': self.workflow_settings.get('plan_creation', {}).get('enabled', True),
                'description': '计划创建',
                'priority': 2
            },
            'plan_submission': {
                'function': self._safe_plan_submission,
                'interval': self.workflow_settings.get('plan_submission', {}).get('interval_seconds', 90),
                'enabled': self.workflow_settings.get('plan_submission', {}).get('enabled', True),
                'description': '计划提交',
                'priority': 3
            },
            'plan_appeal': {
                'function': self._safe_plan_appeal,
                'interval': self.workflow_settings.get('plan_appeal', {}).get('interval_seconds', 900),
                'enabled': self.workflow_settings.get('plan_appeal', {}).get('enabled', True),
                'description': '计划提审（仅测试账户）',
                'priority': 4
            },
            'material_monitoring': {
                'function': self._safe_material_monitoring,
                'interval': self.workflow_settings.get('material_monitoring', {}).get('interval_seconds', 120),
                'enabled': self.workflow_settings.get('material_monitoring', {}).get('enabled', True),
                'description': '素材监控',
                'priority': 5
            }
        }
        
        # 为每个模块创建锁
        for module_name in self.modules:
            self.module_locks[module_name] = threading.Lock()
            self.last_run_times[module_name] = 0
        
        logger.info("📋 已注册工作流模块:")
        for name, config in self.modules.items():
            status = "启用" if config['enabled'] else "禁用"
            logger.info(f"   {name}: {config['description']} - {status} (间隔: {config['interval']}s)")
    
    @contextmanager
    def _module_execution_context(self, module_name: str):
        """模块执行上下文，确保独立性和超时控制"""
        lock = self.module_locks[module_name]
        
        # 尝试获取锁，如果获取不到说明模块正在运行
        if not lock.acquire(blocking=False):
            logger.warning(f"⚠️ 模块 {module_name} 正在运行，跳过本次执行")
            yield False
            return
        
        start_time = time.time()
        try:
            logger.info(f"🚀 开始执行模块: {module_name}")
            yield True
            
        except Exception as e:
            logger.error(f"❌ 模块 {module_name} 执行失败: {e}", exc_info=True)
            
        finally:
            execution_time = time.time() - start_time
            lock.release()
            
            if execution_time > self.max_module_runtime:
                logger.warning(f"⚠️ 模块 {module_name} 执行时间过长: {execution_time:.1f}s")
            else:
                logger.success(f"✅ 模块 {module_name} 执行完成: {execution_time:.1f}s")
    
    def _safe_file_ingestion(self):
        """安全的文件摄取模块"""
        try:
            from qianchuan_aw.workflows.scheduler import handle_file_ingestion_and_upload
            
            with SessionLocal() as db:
                handle_file_ingestion_and_upload(db, self.app_settings)
                
        except Exception as e:
            logger.error(f"文件摄取模块执行失败: {e}")
    
    def _safe_plan_creation(self):
        """安全的计划创建模块"""
        try:
            from qianchuan_aw.workflows.scheduler import handle_plan_creation
            
            with SessionLocal() as db:
                handle_plan_creation(db, self.app_settings)
                
        except Exception as e:
            logger.error(f"计划创建模块执行失败: {e}")
    
    def _safe_plan_submission(self):
        """安全的计划提交模块"""
        try:
            from qianchuan_aw.workflows.scheduler import handle_plan_submission
            
            with SessionLocal() as db:
                handle_plan_submission(db, self.app_settings)
                
        except Exception as e:
            logger.error(f"计划提交模块执行失败: {e}")
    
    def _safe_plan_appeal(self):
        """安全的计划提审模块（仅测试账户）"""
        try:
            # 🛡️ 强制测试账户限制
            from ai_tools.workflow.ai_tool_20250803_workflow_test_account_limiter import test_account_limiter
            
            test_account_ids = test_account_limiter.get_test_account_ids()
            if not test_account_ids:
                logger.warning("⚠️ 没有找到测试账户，跳过提审操作")
                return
            
            # 限制浏览器进程数量
            self._cleanup_excess_browsers()
            
            from qianchuan_aw.workflows.scheduler import handle_plans_awaiting_appeal
            
            with SessionLocal() as db:
                handle_plans_awaiting_appeal(db, self.app_settings)
                
        except Exception as e:
            logger.error(f"计划提审模块执行失败: {e}")
    
    def _safe_material_monitoring(self):
        """安全的素材监控模块"""
        try:
            # 限制浏览器进程数量
            self._cleanup_excess_browsers()
            
            from qianchuan_aw.workflows.scheduler import handle_harvest
            
            with SessionLocal() as db:
                handle_harvest(db, self.app_settings)
                
        except Exception as e:
            logger.error(f"素材监控模块执行失败: {e}")
    
    def _cleanup_excess_browsers(self):
        """清理多余的浏览器进程"""
        try:
            import psutil
            
            browser_count = 0
            for proc in psutil.process_iter(['name', 'cmdline']):
                try:
                    name = proc.info['name'].lower()
                    if any(browser in name for browser in ['chrome', 'chromium']):
                        cmdline = ' '.join(proc.info.get('cmdline', []))
                        if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage']):
                            browser_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 如果浏览器进程过多，清理
            if browser_count > 3:
                logger.warning(f"⚠️ 浏览器进程过多: {browser_count}，执行清理...")
                from ai_tools.maintenance.ai_tool_20250808_browser_process_optimizer import BrowserProcessOptimizer
                optimizer = BrowserProcessOptimizer()
                optimizer.cleanup_excess_processes(dry_run=False)
                
        except Exception as e:
            logger.error(f"浏览器进程清理失败: {e}")
    
    def run(self):
        """运行统一调度器"""
        logger.info("🚀 启动统一调度器")
        logger.info("="*80)
        logger.info("🎯 核心特性:")
        logger.info("1. 模块独立运行，互不阻塞")
        logger.info("2. 自动资源管理和清理")
        logger.info("3. 强制测试账户限制")
        logger.info("4. 浏览器进程控制")
        logger.info("="*80)
        
        self.running = True
        
        try:
            while self.running:
                current_time = time.time()
                
                # 按优先级排序执行模块
                sorted_modules = sorted(
                    self.modules.items(), 
                    key=lambda x: x[1]['priority']
                )
                
                for module_name, config in sorted_modules:
                    if not config['enabled']:
                        continue
                    
                    # 检查是否到了执行时间
                    last_run = self.last_run_times[module_name]
                    if current_time - last_run < config['interval']:
                        continue
                    
                    # 执行模块
                    with self._module_execution_context(module_name) as can_execute:
                        if can_execute:
                            try:
                                config['function']()
                                self.last_run_times[module_name] = current_time
                                
                                # 模块间冷却时间
                                time.sleep(self.module_cooldown)
                                
                            except Exception as e:
                                logger.error(f"模块 {module_name} 执行异常: {e}")
                
                # 主循环休眠
                time.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("检测到中断信号，正在停止调度器...")
            self.running = False
        except Exception as e:
            logger.error(f"调度器运行异常: {e}", exc_info=True)
        finally:
            logger.info("统一调度器已停止")
    
    def stop(self):
        """停止调度器"""
        self.running = False


def main():
    """主函数"""
    scheduler = UnifiedScheduler()
    
    try:
        scheduler.run()
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        scheduler.stop()


if __name__ == "__main__":
    main()
