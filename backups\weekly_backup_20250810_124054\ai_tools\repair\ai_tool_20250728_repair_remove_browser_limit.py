#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 移除千川自动化项目中不合理的浏览器进程数量限制
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def backup_file(file_path):
    """备份文件"""
    backup_path = file_path.with_suffix(f'{file_path.suffix}.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(file_path, backup_path)
    logger.info(f"📋 文件已备份: {backup_path}")
    return backup_path

def remove_browser_limit_from_async_appeal_service():
    """移除AsyncAppealService中的浏览器进程限制"""
    logger.info("🔧 移除AsyncAppealService中的浏览器进程限制...")
    
    file_path = project_root / 'src' / 'qianchuan_aw' / 'services' / 'async_appeal_service.py'
    
    # 备份文件
    backup_file(file_path)
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改浏览器进程数阈值为一个很大的值，实际上禁用这个检查
    content = content.replace(
        'self.max_browser_processes = 5  # 浏览器进程数阈值',
        'self.max_browser_processes = 999  # 浏览器进程数阈值（已禁用限制）'
    )
    
    # 或者直接注释掉浏览器进程检查
    content = content.replace(
        '''            # 检查浏览器进程数
            browser_count = self._count_browser_processes()
            if browser_count > self.max_browser_processes:
                logger.warning(f"⚠️ 浏览器进程过多: {browser_count} > {self.max_browser_processes}")
                return False''',
        '''            # 检查浏览器进程数 - 已禁用，因为用户可能需要大量浏览器进程
            # browser_count = self._count_browser_processes()
            # if browser_count > self.max_browser_processes:
            #     logger.warning(f"⚠️ 浏览器进程过多: {browser_count} > {self.max_browser_processes}")
            #     return False'''
    )
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ AsyncAppealService浏览器限制已移除")
    return True

def update_resource_thresholds():
    """更新资源阈值配置"""
    logger.info("⚙️ 更新资源阈值配置...")
    
    file_path = project_root / 'src' / 'qianchuan_aw' / 'services' / 'resource_monitor.py'
    
    # 备份文件
    backup_file(file_path)
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改浏览器进程阈值
    content = content.replace(
        'browser_warning: int = 5       # 浏览器进程数警告阈值',
        'browser_warning: int = 50      # 浏览器进程数警告阈值（已放宽）'
    )
    
    content = content.replace(
        'browser_critical: int = 8      # 浏览器进程数严重阈值',
        'browser_critical: int = 100    # 浏览器进程数严重阈值（已放宽）'
    )
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ 资源阈值配置已更新")
    return True

def update_emergency_tools():
    """更新紧急工具中的浏览器限制"""
    logger.info("🛠️ 更新紧急工具中的浏览器限制...")
    
    # 更新紧急停止工具
    emergency_stop_file = project_root / 'ai_tools' / 'emergency' / 'ai_tool_20250725_emergency_stop_browser_overload.py'
    if emergency_stop_file.exists():
        backup_file(emergency_stop_file)
        
        with open(emergency_stop_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        content = content.replace(
            'len(browser_processes) > 10',
            'len(browser_processes) > 100  # 已放宽限制'
        )
        
        with open(emergency_stop_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.success("✅ 紧急停止工具已更新")
    
    # 更新浏览器清理工具
    cleanup_file = project_root / 'ai_tools' / 'emergency' / 'ai_tool_20250725_emergency_browser_cleanup.py'
    if cleanup_file.exists():
        backup_file(cleanup_file)
        
        with open(cleanup_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        content = content.replace(
            'browser_processes > 5',
            'browser_processes > 50  # 已放宽限制'
        )
        
        with open(cleanup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.success("✅ 浏览器清理工具已更新")
    
    return True

def update_system_protection():
    """更新系统保护中的浏览器限制"""
    logger.info("🛡️ 更新系统保护中的浏览器限制...")
    
    file_path = project_root / 'src' / 'qianchuan_aw' / 'services' / 'system_protection.py'
    
    # 备份文件
    backup_file(file_path)
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改浏览器进程清理逻辑，提高阈值
    content = content.replace(
        '# 如果超过5个，终止较老的进程',
        '# 如果超过50个，终止较老的进程（已放宽限制）'
    )
    
    content = content.replace(
        'if len(browser_processes) > 5:',
        'if len(browser_processes) > 50:'
    )
    
    content = content.replace(
        'to_kill = browser_processes[5:]  # 保留最新的5个',
        'to_kill = browser_processes[50:]  # 保留最新的50个'
    )
    
    # 写入修改后的内容
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.success("✅ 系统保护浏览器限制已更新")
    return True

def create_browser_limit_config():
    """创建浏览器限制配置文件"""
    logger.info("📝 创建浏览器限制配置文件...")
    
    config_content = '''# 浏览器进程管理配置
# 此配置文件用于控制千川自动化项目中的浏览器进程限制

browser_management:
  # 是否启用浏览器进程数量限制
  enable_process_limit: false
  
  # 浏览器进程数量阈值（仅在enable_process_limit为true时生效）
  max_browser_processes: 999
  
  # 警告阈值
  warning_threshold: 50
  
  # 是否在超过阈值时自动清理
  auto_cleanup: false
  
  # 清理策略
  cleanup_strategy:
    # 保留最新的进程数量
    keep_recent_count: 50
    
    # 清理间隔（秒）
    cleanup_interval: 3600
    
    # 是否只清理项目相关的浏览器进程
    cleanup_project_only: true

# 系统资源监控配置
resource_monitoring:
  # CPU使用率阈值
  cpu_threshold: 85
  
  # 内存使用率阈值
  memory_threshold: 90
  
  # 是否启用资源监控
  enabled: true
  
  # 监控间隔（秒）
  check_interval: 60

# 提审服务配置
appeal_service:
  # 是否跳过系统资源检查
  skip_resource_check: true
  
  # 最大并发提审数
  max_concurrent: 3
  
  # 提审超时时间（秒）
  timeout: 300

# 说明：
# 由于用户工作环境可能需要大量浏览器进程，
# 默认禁用了浏览器进程数量限制。
# 如需启用，请将enable_process_limit设置为true，
# 并根据实际需求调整max_browser_processes值。
'''
    
    config_file = project_root / 'config' / 'browser_management.yml'
    
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    logger.success(f"✅ 浏览器限制配置文件已创建: {config_file}")
    return config_file

def test_appeal_service():
    """测试提审服务是否正常工作"""
    logger.info("🧪 测试提审服务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_appeal_adapter import AsyncAppealAdapter
        
        # 创建适配器实例
        adapter = AsyncAppealAdapter()
        
        logger.info("✅ AsyncAppealAdapter创建成功")
        
        # 检查是否还有浏览器限制
        from qianchuan_aw.services.async_appeal_service import AsyncAppealService
        
        # 模拟创建服务实例
        service = AsyncAppealService({})
        
        logger.info(f"📊 当前浏览器进程阈值: {service.max_browser_processes}")
        
        if service.max_browser_processes >= 999:
            logger.success("✅ 浏览器进程限制已成功移除")
            return True
        else:
            logger.warning(f"⚠️ 浏览器进程限制仍然存在: {service.max_browser_processes}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试提审服务失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始移除不合理的浏览器进程限制...")
    logger.info("="*60)
    
    results = {
        'async_appeal_service_fixed': False,
        'resource_thresholds_updated': False,
        'emergency_tools_updated': False,
        'system_protection_updated': False,
        'config_created': False,
        'service_tested': False
    }
    
    try:
        # 1. 移除AsyncAppealService中的限制
        results['async_appeal_service_fixed'] = remove_browser_limit_from_async_appeal_service()
        
        # 2. 更新资源阈值
        results['resource_thresholds_updated'] = update_resource_thresholds()
        
        # 3. 更新紧急工具
        results['emergency_tools_updated'] = update_emergency_tools()
        
        # 4. 更新系统保护
        results['system_protection_updated'] = update_system_protection()
        
        # 5. 创建配置文件
        config_file = create_browser_limit_config()
        results['config_created'] = config_file is not None
        
        # 6. 测试服务
        results['service_tested'] = test_appeal_service()
        
        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("🎯 浏览器限制移除结果")
        logger.info("="*60)
        
        success_count = sum(results.values())
        total_count = len(results)
        
        for action, success in results.items():
            status = "✅" if success else "❌"
            action_name = action.replace('_', ' ').title()
            logger.info(f"{status} {action_name}")
        
        success_rate = (success_count / total_count) * 100
        logger.info(f"\n📈 修复成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.success("🎉 浏览器进程限制移除成功！")
            
            logger.info("\n📋 修复效果:")
            logger.info("✅ 提审服务不再因浏览器进程数量而失败")
            logger.info("✅ 系统资源检查已放宽限制")
            logger.info("✅ 紧急清理工具阈值已调整")
            logger.info("✅ 用户可以正常使用大量浏览器进程")
            
            logger.info("\n🔄 后续操作:")
            logger.info("1. 重启Celery Worker和Beat进程以应用更改")
            logger.info("2. 观察提审任务是否不再因浏览器进程而失败")
            logger.info("3. 监控系统资源使用情况")
            
        else:
            logger.error("❌ 浏览器进程限制移除存在问题")
        
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ 移除过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
