#!/usr/bin/env python3
"""
千川Celery服务重启脚本
安全地重启Celery Beat和Worker服务
"""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

def kill_celery_processes():
    """终止所有Celery进程"""
    print("🔄 正在停止Celery进程...")
    
    try:
        # Windows系统
        if os.name == 'nt':
            subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                         capture_output=True, text=True)
        else:
            # Linux/Mac系统
            subprocess.run(['pkill', '-f', 'celery'], 
                         capture_output=True, text=True)
        
        print("✅ Celery进程已停止")
        time.sleep(2)  # 等待进程完全停止
        
    except Exception as e:
        print(f"⚠️ 停止进程时出现警告: {e}")

def start_celery_beat():
    """启动Celery Beat"""
    print("🚀 启动Celery Beat...")
    
    try:
        # 检查run_celery_beat.py是否存在
        if not Path("run_celery_beat.py").exists():
            print("❌ run_celery_beat.py文件不存在")
            return False
        
        print("请在新的终端窗口中运行: python run_celery_beat.py")
        print("等待5秒后启动Worker...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ 启动Beat失败: {e}")
        return False

def start_celery_worker():
    """启动Celery Worker"""
    print("🚀 启动Celery Worker...")
    
    try:
        # 检查run_celery_worker.py是否存在
        if not Path("run_celery_worker.py").exists():
            print("❌ run_celery_worker.py文件不存在")
            return False
        
        print("请在另一个新的终端窗口中运行: python run_celery_worker.py")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动Worker失败: {e}")
        return False

def main():
    """主重启函数"""
    print("🔄 千川Celery服务重启脚本")
    print("=" * 50)
    
    # 1. 停止现有进程
    kill_celery_processes()
    
    # 2. 启动Beat
    if not start_celery_beat():
        print("❌ Beat启动失败，请手动启动")
        return False
    
    # 3. 启动Worker
    if not start_celery_worker():
        print("❌ Worker启动失败，请手动启动")
        return False
    
    print("\n✅ 服务重启完成!")
    print("\n📋 验证步骤:")
    print("  1. 检查两个终端是否都有日志输出")
    print("  2. 运行监控工具: python ai_tools/ai_tool_20250722_celery_monitor.py")
    print("  3. 观察素材处理是否恢复正常")
    
    return True

if __name__ == "__main__":
    main()
