#!/usr/bin/env python3
"""
AI生成文件管理器

功能：
1. 自动识别和分类AI生成文件
2. 根据生命周期规则清理过期文件
3. 提供文件统计和管理功能
4. 确保项目结构不被AI临时文件污染

使用方法：
python tools/ai_file_manager.py status          # 显示AI文件统计
python tools/ai_file_manager.py cleanup         # 清理过期文件
python tools/ai_file_manager.py organize        # 整理现有AI文件
python tools/ai_file_manager.py init           # 初始化目录结构
"""

import os
import sys
import argparse
import shutil
from pathlib import Path
from datetime import datetime, timedelta
import re
import json

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# AI文件管理配置
AI_FILE_CONFIG = {
    "directories": {
        "ai_tools": {
            "path": "ai_tools",
            "subdirs": ["maintenance", "analysis", "cleanup", "migration", "optimization"],
            "retention_days": -1,  # 永久保留
            "description": "长期保留的AI工具"
        },
        "ai_temp": {
            "path": "ai_temp", 
            "subdirs": ["cleanup", "debug", "analysis", "experiment"],
            "retention_days": 7,
            "description": "临时AI文件"
        },
        "ai_reports": {
            "path": "ai_reports",
            "subdirs": ["audit", "analysis", "security", "performance"],
            "retention_days": 30,
            "description": "AI生成报告"
        },
        "ai_templates": {
            "path": "ai_templates",
            "subdirs": ["config", "scripts", "deploy"],
            "retention_days": -1,  # 永久保留
            "description": "AI生成模板"
        }
    },
    "prefixes": {
        "ai_tool_": "ai_tools",
        "ai_temp_": "ai_temp", 
        "ai_report_": "ai_reports",
        "ai_template_": "ai_templates",
        "ai_debug_": "ai_temp",
        "ai_analysis_": "ai_temp"
    },
    "patterns": {
        "ai_generated": [
            r"^ai_\w+_\d{8}_.*",  # ai_前缀_日期_描述
            r"^deep_\w+_.*\.py$",  # deep_开头的脚本
            r".*_audit_report\.md$",  # 审计报告
            r".*_cleanup_.*\.py$",  # 清理脚本
        ]
    }
}

class AIFileManager:
    def __init__(self):
        self.project_root = project_root
        self.config = AI_FILE_CONFIG
        
    def init_directories(self):
        """初始化AI文件管理目录结构"""
        print("🚀 初始化AI文件管理目录结构...")
        
        created_dirs = []
        
        for dir_name, dir_config in self.config["directories"].items():
            base_dir = self.project_root / dir_config["path"]
            
            # 创建主目录
            if not base_dir.exists():
                base_dir.mkdir(parents=True, exist_ok=True)
                created_dirs.append(str(base_dir))
                print(f"✅ 创建目录: {base_dir}")
            
            # 创建子目录
            for subdir in dir_config["subdirs"]:
                sub_path = base_dir / subdir
                if not sub_path.exists():
                    sub_path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(str(sub_path))
                    print(f"✅ 创建子目录: {sub_path}")
            
            # 创建README文件
            readme_path = base_dir / "README.md"
            if not readme_path.exists():
                self._create_directory_readme(readme_path, dir_name, dir_config)
                print(f"✅ 创建说明文件: {readme_path}")
        
        # 更新.gitignore
        self._update_gitignore()
        
        print(f"\n📊 初始化完成，共创建 {len(created_dirs)} 个目录")
        return created_dirs
    
    def _create_directory_readme(self, readme_path, dir_name, dir_config):
        """创建目录说明文件"""
        content = f"""# {dir_name.upper()} 目录

## 用途
{dir_config['description']}

## 保留期限
{'永久保留' if dir_config['retention_days'] == -1 else f"{dir_config['retention_days']}天"}

## 子目录说明
"""
        for subdir in dir_config['subdirs']:
            content += f"- `{subdir}/`: {subdir}相关文件\n"
        
        content += f"""
## 文件命名规范
- 使用统一前缀标识AI生成文件
- 包含生成日期 (YYYYMMDD格式)
- 描述文件用途和功能

## 管理命令
```bash
# 查看此目录统计
python tools/ai_file_manager.py status --dir {dir_name}

# 清理过期文件
python tools/ai_file_manager.py cleanup --dir {dir_name}
```

---
*此文件由AI文件管理器自动生成*
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(content)
    
    def _update_gitignore(self):
        """更新.gitignore文件"""
        gitignore_path = self.project_root / ".gitignore"
        
        ai_ignore_rules = [
            "",
            "# AI Generated Files",
            "# ==================",
            "# Temporary AI files should not be committed",
            "ai_temp/",
            "",
            "# AI reports can be selectively committed", 
            "ai_reports/*.json",
            "ai_reports/temp_*",
            "",
            "# AI tools and templates should be reviewed before commit",
            "# (remove these lines if you want to commit them)",
            "# ai_tools/",
            "# ai_templates/",
            ""
        ]
        
        if gitignore_path.exists():
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "# AI Generated Files" not in content:
                with open(gitignore_path, 'a', encoding='utf-8') as f:
                    f.write('\n'.join(ai_ignore_rules))
                print("✅ 已更新.gitignore文件")
        else:
            with open(gitignore_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(ai_ignore_rules))
            print("✅ 已创建.gitignore文件")
    
    def scan_ai_files(self):
        """扫描项目中的AI生成文件"""
        ai_files = {
            "categorized": {},
            "uncategorized": [],
            "total_count": 0,
            "total_size": 0
        }
        
        # 初始化分类
        for dir_name in self.config["directories"]:
            ai_files["categorized"][dir_name] = []
        
        # 扫描项目根目录和AI目录
        scan_paths = [self.project_root]
        for dir_config in self.config["directories"].values():
            dir_path = self.project_root / dir_config["path"]
            if dir_path.exists():
                scan_paths.append(dir_path)
        
        for scan_path in scan_paths:
            for file_path in scan_path.rglob("*"):
                if file_path.is_file() and self._is_ai_generated_file(file_path):
                    file_info = self._get_file_info(file_path)
                    
                    # 分类文件
                    categorized = False
                    for prefix, target_dir in self.config["prefixes"].items():
                        if file_path.name.startswith(prefix):
                            ai_files["categorized"][target_dir].append(file_info)
                            categorized = True
                            break
                    
                    if not categorized:
                        # 检查是否在AI目录中
                        for dir_name, dir_config in self.config["directories"].items():
                            ai_dir = self.project_root / dir_config["path"]
                            if ai_dir in file_path.parents:
                                ai_files["categorized"][dir_name].append(file_info)
                                categorized = True
                                break
                    
                    if not categorized:
                        ai_files["uncategorized"].append(file_info)
                    
                    ai_files["total_count"] += 1
                    ai_files["total_size"] += file_info["size"]
        
        return ai_files
    
    def _is_ai_generated_file(self, file_path):
        """判断是否为AI生成文件"""
        filename = file_path.name
        
        # 检查前缀
        for prefix in self.config["prefixes"]:
            if filename.startswith(prefix):
                return True
        
        # 检查模式
        for pattern in self.config["patterns"]["ai_generated"]:
            if re.match(pattern, filename):
                return True
        
        # 检查是否在AI目录中
        for dir_config in self.config["directories"].values():
            ai_dir = self.project_root / dir_config["path"]
            if ai_dir in file_path.parents:
                return True
        
        return False
    
    def _get_file_info(self, file_path):
        """获取文件信息"""
        stat = file_path.stat()
        return {
            "path": str(file_path),
            "name": file_path.name,
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime),
            "modified": datetime.fromtimestamp(stat.st_mtime),
            "relative_path": str(file_path.relative_to(self.project_root))
        }
    
    def show_status(self, target_dir=None):
        """显示AI文件统计状态"""
        print("📊 AI生成文件统计报告")
        print("=" * 60)
        
        ai_files = self.scan_ai_files()
        
        if target_dir:
            if target_dir in ai_files["categorized"]:
                files = ai_files["categorized"][target_dir]
                print(f"📁 {target_dir} 目录统计:")
                self._show_file_list(files)
            else:
                print(f"❌ 目录 {target_dir} 不存在")
            return
        
        # 显示总体统计
        print(f"📈 总体统计:")
        print(f"   总文件数: {ai_files['total_count']}")
        print(f"   总大小: {ai_files['total_size'] / 1024:.1f} KB")
        
        # 显示分类统计
        print(f"\n📂 分类统计:")
        for dir_name, files in ai_files["categorized"].items():
            if files:
                total_size = sum(f["size"] for f in files)
                print(f"   {dir_name}: {len(files)} 个文件, {total_size / 1024:.1f} KB")
        
        # 显示未分类文件
        if ai_files["uncategorized"]:
            print(f"\n⚠️  未分类文件 ({len(ai_files['uncategorized'])} 个):")
            for file_info in ai_files["uncategorized"][:5]:
                print(f"   📄 {file_info['relative_path']}")
            if len(ai_files["uncategorized"]) > 5:
                print(f"   ... 还有 {len(ai_files['uncategorized']) - 5} 个文件")
    
    def _show_file_list(self, files):
        """显示文件列表"""
        if not files:
            print("   (无文件)")
            return
        
        files.sort(key=lambda x: x["modified"], reverse=True)
        
        for file_info in files[:10]:
            age_days = (datetime.now() - file_info["modified"]).days
            size_kb = file_info["size"] / 1024
            print(f"   📄 {file_info['name']} ({size_kb:.1f}KB, {age_days}天前)")
        
        if len(files) > 10:
            print(f"   ... 还有 {len(files) - 10} 个文件")
    
    def cleanup_expired_files(self, target_dir=None, dry_run=False):
        """清理过期的AI文件"""
        print("🧹 清理过期AI文件...")
        if dry_run:
            print("🔍 预览模式 - 不会实际删除文件")
        
        ai_files = self.scan_ai_files()
        deleted_count = 0
        total_size_freed = 0
        
        # 确定要清理的目录
        dirs_to_clean = [target_dir] if target_dir else self.config["directories"].keys()
        
        for dir_name in dirs_to_clean:
            if dir_name not in self.config["directories"]:
                continue
                
            dir_config = self.config["directories"][dir_name]
            retention_days = dir_config["retention_days"]
            
            if retention_days == -1:  # 永久保留
                continue
            
            files = ai_files["categorized"].get(dir_name, [])
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            print(f"\n📁 清理 {dir_name} 目录 (保留期: {retention_days}天)")
            
            for file_info in files:
                if file_info["modified"] < cutoff_date:
                    file_path = Path(file_info["path"])
                    
                    if dry_run:
                        print(f"   🗑️  将删除: {file_info['relative_path']}")
                    else:
                        try:
                            file_path.unlink()
                            print(f"   ✅ 已删除: {file_info['relative_path']}")
                            deleted_count += 1
                            total_size_freed += file_info["size"]
                        except Exception as e:
                            print(f"   ❌ 删除失败: {file_info['relative_path']} - {e}")
        
        # 清理未分类的AI文件
        if not target_dir:
            print(f"\n📁 清理未分类AI文件")
            cutoff_date = datetime.now() - timedelta(days=7)  # 未分类文件7天清理
            
            for file_info in ai_files["uncategorized"]:
                if file_info["modified"] < cutoff_date:
                    file_path = Path(file_info["path"])
                    
                    if dry_run:
                        print(f"   🗑️  将删除: {file_info['relative_path']}")
                    else:
                        try:
                            file_path.unlink()
                            print(f"   ✅ 已删除: {file_info['relative_path']}")
                            deleted_count += 1
                            total_size_freed += file_info["size"]
                        except Exception as e:
                            print(f"   ❌ 删除失败: {file_info['relative_path']} - {e}")
        
        print(f"\n📊 清理统计:")
        print(f"   删除文件数: {deleted_count}")
        print(f"   释放空间: {total_size_freed / 1024:.1f} KB")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI生成文件管理器')
    parser.add_argument('action', choices=['init', 'status', 'cleanup', 'organize'],
                       help='执行的操作')
    parser.add_argument('--dir', help='指定目录')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际执行')
    
    args = parser.parse_args()
    
    manager = AIFileManager()
    
    if args.action == 'init':
        manager.init_directories()
    
    elif args.action == 'status':
        manager.show_status(args.dir)
    
    elif args.action == 'cleanup':
        manager.cleanup_expired_files(args.dir, args.dry_run)
    
    elif args.action == 'organize':
        print("🔄 整理功能开发中...")
        # TODO: 实现文件整理功能

if __name__ == "__main__":
    main()
