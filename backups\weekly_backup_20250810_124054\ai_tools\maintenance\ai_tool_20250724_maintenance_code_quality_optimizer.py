#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川项目代码质量优化工具
清理条件: 项目代码质量管理完全重构时可删除

千川项目代码质量优化工具
========================

基于语法检查结果，提供具体的代码质量优化建议和自动化修复功能。

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_code_quality_optimizer.py [action]

Actions:
- analyze: 分析代码质量问题
- optimize: 执行自动化优化
- report: 生成优化建议报告
"""

import os
import sys
import ast
import json
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

try:
    from src.qianchuan_aw.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


class CodeQualityOptimizer:
    """代码质量优化器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.core_files = [
            'main.py',
            'web_ui.py',
            'src/qianchuan_aw/workflows/scheduler.py',
            'src/qianchuan_aw/database/models.py',
            'src/qianchuan_aw/sdk_qc/client.py',
        ]
    
    def analyze_dependencies(self) -> Dict[str, Any]:
        """分析项目依赖"""
        logger.info("🔍 分析项目依赖...")
        
        dependencies = {
            'installed': [],
            'missing': [],
            'requirements_exists': False,
            'recommendations': []
        }
        
        # 检查requirements.txt是否存在
        requirements_path = self.project_root / 'requirements.txt'
        dependencies['requirements_exists'] = requirements_path.exists()
        
        # 检查关键依赖
        key_dependencies = [
            'streamlit', 'pandas', 'sqlalchemy', 'celery', 'redis',
            'playwright', 'requests', 'loguru', 'pydantic'
        ]
        
        try:
            result = subprocess.run(['pip', 'list'], capture_output=True, text=True)
            installed_packages = result.stdout.lower()
            
            for dep in key_dependencies:
                if dep in installed_packages:
                    dependencies['installed'].append(dep)
                else:
                    dependencies['missing'].append(dep)
        except Exception as e:
            logger.error(f"检查依赖失败: {e}")
        
        # 生成建议
        if not dependencies['requirements_exists']:
            dependencies['recommendations'].append({
                'type': 'create_requirements',
                'priority': 'high',
                'description': '创建requirements.txt文件',
                'command': 'pip freeze > requirements.txt'
            })
        
        if dependencies['missing']:
            dependencies['recommendations'].append({
                'type': 'install_missing',
                'priority': 'medium',
                'description': f'安装缺失的依赖: {", ".join(dependencies["missing"])}',
                'command': f'pip install {" ".join(dependencies["missing"])}'
            })
        
        return dependencies
    
    def analyze_code_style(self) -> Dict[str, Any]:
        """分析代码风格"""
        logger.info("🎨 分析代码风格...")
        
        style_analysis = {
            'files_analyzed': 0,
            'style_issues': [],
            'recommendations': []
        }
        
        for file_rel_path in self.core_files:
            file_path = self.project_root / file_rel_path
            if not file_path.exists():
                continue
            
            style_analysis['files_analyzed'] += 1
            
            try:
                # 检查文件长度
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    line_count = len(lines)
                
                if line_count > 1000:
                    style_analysis['style_issues'].append({
                        'file': file_rel_path,
                        'type': 'large_file',
                        'severity': 'medium',
                        'description': f'文件过大 ({line_count} 行)，建议拆分',
                        'line_count': line_count
                    })
                
                # 检查长行
                long_lines = []
                for i, line in enumerate(lines, 1):
                    if len(line.rstrip()) > 120:
                        long_lines.append(i)
                
                if long_lines:
                    style_analysis['style_issues'].append({
                        'file': file_rel_path,
                        'type': 'long_lines',
                        'severity': 'low',
                        'description': f'发现 {len(long_lines)} 行超过120字符',
                        'lines': long_lines[:5]  # 只显示前5行
                    })
                
            except Exception as e:
                logger.error(f"分析文件风格失败 {file_path}: {e}")
        
        # 生成建议
        if style_analysis['style_issues']:
            style_analysis['recommendations'].append({
                'type': 'format_code',
                'priority': 'low',
                'description': '使用black格式化代码',
                'command': 'black web_ui.py main.py src/qianchuan_aw/'
            })
        
        return style_analysis
    
    def analyze_documentation(self) -> Dict[str, Any]:
        """分析文档完整性"""
        logger.info("📚 分析文档完整性...")
        
        doc_analysis = {
            'files_analyzed': 0,
            'functions_without_docstring': 0,
            'classes_without_docstring': 0,
            'total_functions': 0,
            'total_classes': 0,
            'documentation_coverage': 0.0,
            'recommendations': []
        }
        
        for file_rel_path in self.core_files:
            file_path = self.project_root / file_rel_path
            if not file_path.exists():
                continue
            
            doc_analysis['files_analyzed'] += 1
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content, filename=str(file_path))
                
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        doc_analysis['total_functions'] += 1
                        if not ast.get_docstring(node):
                            doc_analysis['functions_without_docstring'] += 1
                    
                    elif isinstance(node, ast.ClassDef):
                        doc_analysis['total_classes'] += 1
                        if not ast.get_docstring(node):
                            doc_analysis['classes_without_docstring'] += 1
                
            except Exception as e:
                logger.error(f"分析文档失败 {file_path}: {e}")
        
        # 计算文档覆盖率
        total_items = doc_analysis['total_functions'] + doc_analysis['total_classes']
        missing_items = doc_analysis['functions_without_docstring'] + doc_analysis['classes_without_docstring']
        
        if total_items > 0:
            doc_analysis['documentation_coverage'] = (total_items - missing_items) / total_items * 100
        
        # 生成建议
        if doc_analysis['documentation_coverage'] < 80:
            doc_analysis['recommendations'].append({
                'type': 'improve_documentation',
                'priority': 'medium',
                'description': f'文档覆盖率仅 {doc_analysis["documentation_coverage"]:.1f}%，建议添加docstring',
                'details': {
                    'missing_function_docs': doc_analysis['functions_without_docstring'],
                    'missing_class_docs': doc_analysis['classes_without_docstring']
                }
            })
        
        return doc_analysis
    
    def generate_optimization_plan(self) -> Dict[str, Any]:
        """生成优化计划"""
        logger.info("📋 生成优化计划...")
        
        # 执行各项分析
        dependencies = self.analyze_dependencies()
        code_style = self.analyze_code_style()
        documentation = self.analyze_documentation()
        
        # 汇总所有建议
        all_recommendations = []
        all_recommendations.extend(dependencies.get('recommendations', []))
        all_recommendations.extend(code_style.get('recommendations', []))
        all_recommendations.extend(documentation.get('recommendations', []))
        
        # 按优先级排序
        priority_order = {'high': 0, 'medium': 1, 'low': 2}
        all_recommendations.sort(key=lambda x: priority_order.get(x['priority'], 3))
        
        optimization_plan = {
            'timestamp': datetime.now().isoformat(),
            'analysis_results': {
                'dependencies': dependencies,
                'code_style': code_style,
                'documentation': documentation
            },
            'recommendations': all_recommendations,
            'summary': {
                'total_recommendations': len(all_recommendations),
                'high_priority': len([r for r in all_recommendations if r['priority'] == 'high']),
                'medium_priority': len([r for r in all_recommendations if r['priority'] == 'medium']),
                'low_priority': len([r for r in all_recommendations if r['priority'] == 'low']),
                'estimated_effort': self._estimate_effort(all_recommendations)
            }
        }
        
        return optimization_plan
    
    def _estimate_effort(self, recommendations: List[Dict]) -> str:
        """估算优化工作量"""
        high_count = len([r for r in recommendations if r['priority'] == 'high'])
        medium_count = len([r for r in recommendations if r['priority'] == 'medium'])
        low_count = len([r for r in recommendations if r['priority'] == 'low'])
        
        total_hours = high_count * 2 + medium_count * 1 + low_count * 0.5
        
        if total_hours <= 2:
            return "1-2小时"
        elif total_hours <= 8:
            return "半天"
        elif total_hours <= 16:
            return "1天"
        else:
            return "2-3天"
    
    def execute_safe_optimizations(self) -> Dict[str, Any]:
        """执行安全的自动化优化"""
        logger.info("🔧 执行安全优化...")
        
        results = {
            'executed': [],
            'skipped': [],
            'errors': []
        }
        
        # 1. 创建requirements.txt（如果不存在）
        requirements_path = self.project_root / 'requirements.txt'
        if not requirements_path.exists():
            try:
                result = subprocess.run(['pip', 'freeze'], capture_output=True, text=True, cwd=self.project_root)
                if result.returncode == 0:
                    with open(requirements_path, 'w') as f:
                        f.write(result.stdout)
                    results['executed'].append('创建requirements.txt文件')
                else:
                    results['errors'].append('创建requirements.txt失败')
            except Exception as e:
                results['errors'].append(f'创建requirements.txt出错: {e}')
        else:
            results['skipped'].append('requirements.txt已存在')
        
        # 2. 检查是否可以安装black进行代码格式化
        try:
            subprocess.run(['black', '--version'], capture_output=True, check=True)
            # black已安装，可以格式化代码（但这里只是检查，不实际执行）
            results['skipped'].append('代码格式化工具可用，但需要手动执行')
        except (subprocess.CalledProcessError, FileNotFoundError):
            results['skipped'].append('black未安装，跳过代码格式化')
        
        return results


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川项目代码质量优化工具')
    parser.add_argument('action', choices=['analyze', 'optimize', 'report'], 
                       help='执行的操作')
    parser.add_argument('--output', '-o', help='输出文件路径')
    
    args = parser.parse_args()
    
    optimizer = CodeQualityOptimizer()
    
    if args.action == 'analyze':
        results = optimizer.generate_optimization_plan()
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 分析结果已保存到: {args.output}")
        else:
            print(json.dumps(results, ensure_ascii=False, indent=2))
    
    elif args.action == 'optimize':
        results = optimizer.execute_safe_optimizations()
        print(f"✅ 执行的优化: {len(results['executed'])}")
        print(f"⏭️ 跳过的操作: {len(results['skipped'])}")
        print(f"❌ 错误数量: {len(results['errors'])}")
        
        for item in results['executed']:
            print(f"  ✅ {item}")
        for item in results['skipped']:
            print(f"  ⏭️ {item}")
        for item in results['errors']:
            print(f"  ❌ {item}")
    
    elif args.action == 'report':
        results = optimizer.generate_optimization_plan()
        
        print(f"\n📊 代码质量优化报告")
        print(f"=" * 50)
        print(f"总建议数: {results['summary']['total_recommendations']}")
        print(f"高优先级: {results['summary']['high_priority']}")
        print(f"中优先级: {results['summary']['medium_priority']}")
        print(f"低优先级: {results['summary']['low_priority']}")
        print(f"预估工作量: {results['summary']['estimated_effort']}")
        
        print(f"\n📋 优化建议:")
        for i, rec in enumerate(results['recommendations'], 1):
            priority_icon = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(rec['priority'], '⚪')
            print(f"{i}. {priority_icon} {rec['description']}")
            if 'command' in rec:
                print(f"   命令: {rec['command']}")


if __name__ == '__main__':
    main()
