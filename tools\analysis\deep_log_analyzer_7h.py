#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化项目7小时深度日志分析工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 深度分析7小时内的系统日志，识别重复性错误、预期不符问题和稳定性问题
维护团队: 技术团队
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class DeepLogAnalyzer7H:
    """7小时深度日志分析器"""
    
    def __init__(self):
        self.analysis_window = timedelta(hours=7)
        self.current_time = datetime.now()
        self.start_time = self.current_time - self.analysis_window
        
        # 重复性错误模式定义
        self.repetitive_error_patterns = {
            'file_not_found': {
                'pattern': r'❌ 文件不存在: (.+)',
                'category': 'FILE_SYSTEM',
                'severity': 'HIGH',
                'description': '文件不存在错误'
            },
            'api_call_failed': {
                'pattern': r'API请求失败.*Code: (\d+).*Message: (.+)',
                'category': 'API_INTEGRATION',
                'severity': 'HIGH',
                'description': 'API调用失败'
            },
            'video_format_error': {
                'pattern': r'文件格式错误|视频尺寸错误|moov atom not found',
                'category': 'MEDIA_PROCESSING',
                'severity': 'MEDIUM',
                'description': '视频格式/质量错误'
            },
            'database_error': {
                'pattern': r'数据库.*失败|❌ 状态转换失败|数据库事务回滚',
                'category': 'DATABASE',
                'severity': 'HIGH',
                'description': '数据库操作错误'
            },
            'task_retry': {
                'pattern': r'🔄.*Will retry|Task.*retry.*attempt',
                'category': 'TASK_MANAGEMENT',
                'severity': 'MEDIUM',
                'description': '任务重试'
            },
            'upload_failure': {
                'pattern': r'❌ 上传失败|upload.*failed',
                'category': 'UPLOAD_PROCESS',
                'severity': 'HIGH',
                'description': '上传失败'
            },
            'worker_error': {
                'pattern': r'Worker.*crashed|Celery.*ERROR',
                'category': 'WORKER_SYSTEM',
                'severity': 'CRITICAL',
                'description': 'Worker系统错误'
            }
        }
        
        # 预期不符问题模式
        self.expectation_mismatch_patterns = {
            'unexpected_status': {
                'pattern': r'⚠️.*素材.*状态异常|状态.*跳过',
                'category': 'BUSINESS_LOGIC',
                'severity': 'MEDIUM',
                'description': '状态异常，与预期不符'
            },
            'config_mismatch': {
                'pattern': r'配置.*不匹配|设置.*错误',
                'category': 'CONFIGURATION',
                'severity': 'MEDIUM',
                'description': '配置与预期不符'
            },
            'account_issue': {
                'pattern': r'账户.*异常|Token.*权限不足',
                'category': 'ACCOUNT_MANAGEMENT',
                'severity': 'HIGH',
                'description': '账户状态与预期不符'
            }
        }
        
        self.analysis_results = {
            'repetitive_errors': defaultdict(list),
            'expectation_mismatches': defaultdict(list),
            'stability_metrics': {},
            'timeline_analysis': [],
            'impact_assessment': {},
            'recommendations': []
        }
    
    def analyze_log_content(self, content: str, source_file: str) -> None:
        """分析日志内容"""
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            if not line.strip():
                continue
            
            # 提取时间戳
            timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
            timestamp = None
            
            if timestamp_match:
                try:
                    timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                    # 只分析时间窗口内的日志
                    if timestamp < self.start_time:
                        continue
                except:
                    pass
            
            # 分析重复性错误
            self._analyze_repetitive_errors(line, timestamp, source_file, line_num)
            
            # 分析预期不符问题
            self._analyze_expectation_mismatches(line, timestamp, source_file, line_num)
    
    def _analyze_repetitive_errors(self, line: str, timestamp: Optional[datetime], 
                                 source_file: str, line_num: int) -> None:
        """分析重复性错误"""
        for error_type, config in self.repetitive_error_patterns.items():
            matches = re.findall(config['pattern'], line, re.IGNORECASE)
            if matches:
                self.analysis_results['repetitive_errors'][error_type].append({
                    'timestamp': timestamp or self.current_time,
                    'line': line.strip(),
                    'matches': matches,
                    'source_file': source_file,
                    'line_number': line_num,
                    'severity': config['severity'],
                    'category': config['category']
                })
    
    def _analyze_expectation_mismatches(self, line: str, timestamp: Optional[datetime],
                                      source_file: str, line_num: int) -> None:
        """分析预期不符问题"""
        for mismatch_type, config in self.expectation_mismatch_patterns.items():
            matches = re.findall(config['pattern'], line, re.IGNORECASE)
            if matches:
                self.analysis_results['expectation_mismatches'][mismatch_type].append({
                    'timestamp': timestamp or self.current_time,
                    'line': line.strip(),
                    'matches': matches,
                    'source_file': source_file,
                    'line_number': line_num,
                    'severity': config['severity'],
                    'category': config['category']
                })
    
    def calculate_stability_metrics(self) -> Dict:
        """计算系统稳定性指标"""
        print("📊 计算系统稳定性指标...")
        
        metrics = {
            'error_frequency': {},
            'error_distribution': {},
            'critical_error_rate': 0,
            'system_health_score': 100
        }
        
        # 计算错误频率
        total_errors = 0
        critical_errors = 0
        
        for error_type, errors in self.analysis_results['repetitive_errors'].items():
            error_count = len(errors)
            total_errors += error_count
            
            # 计算每小时错误率
            hourly_rate = error_count / 7  # 7小时窗口
            metrics['error_frequency'][error_type] = {
                'total_count': error_count,
                'hourly_rate': hourly_rate,
                'severity': self.repetitive_error_patterns[error_type]['severity']
            }
            
            # 统计关键错误
            if self.repetitive_error_patterns[error_type]['severity'] in ['CRITICAL', 'HIGH']:
                critical_errors += error_count
        
        # 计算关键错误率
        if total_errors > 0:
            metrics['critical_error_rate'] = (critical_errors / total_errors) * 100
        
        # 计算系统健康评分
        health_score = 100
        if total_errors > 1000:
            health_score -= 30
        elif total_errors > 500:
            health_score -= 20
        elif total_errors > 100:
            health_score -= 10
        
        if metrics['critical_error_rate'] > 50:
            health_score -= 25
        elif metrics['critical_error_rate'] > 30:
            health_score -= 15
        
        metrics['system_health_score'] = max(0, health_score)
        metrics['total_errors'] = total_errors
        metrics['critical_errors'] = critical_errors
        
        return metrics
    
    def assess_business_impact(self) -> Dict:
        """评估业务影响"""
        print("🎯 评估业务影响...")
        
        impact_assessment = {
            'high_impact': [],
            'medium_impact': [],
            'low_impact': [],
            'business_continuity_risk': 'LOW'
        }
        
        # 评估重复性错误的业务影响
        for error_type, errors in self.analysis_results['repetitive_errors'].items():
            error_count = len(errors)
            severity = self.repetitive_error_patterns[error_type]['severity']
            category = self.repetitive_error_patterns[error_type]['category']
            
            impact_item = {
                'type': error_type,
                'count': error_count,
                'severity': severity,
                'category': category,
                'description': self.repetitive_error_patterns[error_type]['description'],
                'business_impact': self._calculate_business_impact(error_type, error_count, severity)
            }
            
            if severity == 'CRITICAL' or error_count > 500:
                impact_assessment['high_impact'].append(impact_item)
            elif severity == 'HIGH' or error_count > 100:
                impact_assessment['medium_impact'].append(impact_item)
            else:
                impact_assessment['low_impact'].append(impact_item)
        
        # 评估业务连续性风险
        high_impact_count = len(impact_assessment['high_impact'])
        if high_impact_count >= 3:
            impact_assessment['business_continuity_risk'] = 'HIGH'
        elif high_impact_count >= 2:
            impact_assessment['business_continuity_risk'] = 'MEDIUM'
        
        return impact_assessment
    
    def _calculate_business_impact(self, error_type: str, count: int, severity: str) -> str:
        """计算具体的业务影响"""
        if error_type == 'file_not_found' and count > 100:
            return "严重影响素材上传流程，导致大量任务失败"
        elif error_type == 'api_call_failed' and count > 50:
            return "影响与千川平台的集成，可能导致功能异常"
        elif error_type == 'upload_failure' and count > 100:
            return "直接影响核心业务流程，降低系统可用性"
        elif error_type == 'worker_error':
            return "影响任务处理能力，可能导致系统不稳定"
        elif error_type == 'database_error' and count > 20:
            return "影响数据一致性，可能导致业务数据异常"
        else:
            return "对业务流程有一定影响，建议关注"
    
    def generate_progressive_solutions(self) -> List[Dict]:
        """生成渐进式解决方案"""
        print("💡 生成渐进式解决方案...")
        
        solutions = []
        
        # 基于影响评估生成解决方案
        impact_assessment = self.analysis_results.get('impact_assessment', {})
        
        # 第一阶段：紧急修复（高影响问题）
        phase1_solutions = []
        for item in impact_assessment.get('high_impact', []):
            solution = self._generate_solution_for_error(item, 'PHASE1')
            if solution:
                phase1_solutions.append(solution)
        
        if phase1_solutions:
            solutions.append({
                'phase': 'PHASE1_EMERGENCY',
                'priority': 'CRITICAL',
                'timeline': '立即执行（1-2小时内）',
                'description': '紧急修复高影响问题',
                'solutions': phase1_solutions,
                'verification': '监控错误日志，确认高频错误显著减少'
            })
        
        # 第二阶段：系统优化（中等影响问题）
        phase2_solutions = []
        for item in impact_assessment.get('medium_impact', []):
            solution = self._generate_solution_for_error(item, 'PHASE2')
            if solution:
                phase2_solutions.append(solution)
        
        if phase2_solutions:
            solutions.append({
                'phase': 'PHASE2_OPTIMIZATION',
                'priority': 'HIGH',
                'timeline': '24小时内完成',
                'description': '系统优化和稳定性改进',
                'solutions': phase2_solutions,
                'verification': '系统健康评分提升，错误率下降50%以上'
            })
        
        # 第三阶段：预防性维护（低影响问题）
        phase3_solutions = []
        for item in impact_assessment.get('low_impact', []):
            solution = self._generate_solution_for_error(item, 'PHASE3')
            if solution:
                phase3_solutions.append(solution)
        
        if phase3_solutions:
            solutions.append({
                'phase': 'PHASE3_PREVENTION',
                'priority': 'MEDIUM',
                'timeline': '1周内完成',
                'description': '预防性维护和长期优化',
                'solutions': phase3_solutions,
                'verification': '建立监控机制，确保问题不再复现'
            })
        
        return solutions
    
    def _generate_solution_for_error(self, error_item: Dict, phase: str) -> Optional[Dict]:
        """为特定错误生成解决方案"""
        error_type = error_item['type']
        count = error_item['count']
        
        solutions_map = {
            'file_not_found': {
                'title': '修复文件路径同步问题',
                'steps': [
                    '运行文件路径同步修复工具',
                    '更新数据库中的文件路径',
                    '实施文件移动后的自动同步机制'
                ],
                'command': 'python tools/maintenance/file_path_sync_fixer.py',
                'rollback': '备份数据库，如有问题可回滚',
                'risk_level': 'LOW'
            },
            'api_call_failed': {
                'title': '修复API调用问题',
                'steps': [
                    '检查Token有效性',
                    '验证API权限配置',
                    '实施智能重试机制'
                ],
                'command': 'python tools/maintenance/api_health_checker.py',
                'rollback': '恢复原有API配置',
                'risk_level': 'MEDIUM'
            },
            'upload_failure': {
                'title': '优化上传流程',
                'steps': [
                    '实施视频质量预检查',
                    '优化上传重试逻辑',
                    '修复文件路径问题'
                ],
                'command': 'python tools/quality/video_quality_precheck.py',
                'rollback': '保持原有上传逻辑',
                'risk_level': 'LOW'
            },
            'database_error': {
                'title': '数据库稳定性优化',
                'steps': [
                    '优化数据库连接池',
                    '修复事务处理逻辑',
                    '添加数据库健康检查'
                ],
                'command': 'python tools/maintenance/database_optimizer.py',
                'rollback': '恢复数据库配置文件',
                'risk_level': 'HIGH'
            }
        }
        
        if error_type in solutions_map:
            solution = solutions_map[error_type].copy()
            solution['error_count'] = count
            solution['phase'] = phase
            return solution
        
        return None

def main():
    """主函数"""
    print("🚀 千川自动化项目7小时深度日志分析")
    print("=" * 60)
    print(f"📅 分析时间窗口: 近7小时")
    print(f"🕐 开始时间: {datetime.now() - timedelta(hours=7)}")
    print(f"🕐 结束时间: {datetime.now()}")
    
    analyzer = DeepLogAnalyzer7H()
    
    try:
        # 1. 查找并分析日志文件
        log_files_found = 0
        
        # 尝试从多个可能的日志位置读取
        possible_log_locations = [
            Path("logs"),
            Path("../logs"),
            Path("./"),
        ]
        
        for log_dir in possible_log_locations:
            if log_dir.exists():
                for log_file in log_dir.glob("*.log"):
                    try:
                        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            analyzer.analyze_log_content(content, log_file.name)
                            log_files_found += 1
                    except Exception as e:
                        print(f"⚠️ 读取日志文件失败 {log_file}: {e}")
        
        print(f"📄 分析了 {log_files_found} 个日志文件")
        
        # 2. 计算稳定性指标
        stability_metrics = analyzer.calculate_stability_metrics()
        analyzer.analysis_results['stability_metrics'] = stability_metrics
        
        # 3. 评估业务影响
        impact_assessment = analyzer.assess_business_impact()
        analyzer.analysis_results['impact_assessment'] = impact_assessment
        
        # 4. 生成解决方案
        progressive_solutions = analyzer.generate_progressive_solutions()
        analyzer.analysis_results['progressive_solutions'] = progressive_solutions
        
        # 5. 输出分析结果
        print(f"\n{'='*60}")
        print("📊 7小时深度日志分析结果")
        print(f"{'='*60}")
        
        # 系统稳定性指标
        print(f"\n🏥 系统稳定性指标:")
        print(f"  - 系统健康评分: {stability_metrics['system_health_score']}/100")
        print(f"  - 总错误数: {stability_metrics['total_errors']}")
        print(f"  - 关键错误数: {stability_metrics['critical_errors']}")
        print(f"  - 关键错误率: {stability_metrics['critical_error_rate']:.1f}%")
        
        # 重复性错误分析
        print(f"\n🔄 重复性错误分析:")
        for error_type, errors in analyzer.analysis_results['repetitive_errors'].items():
            if errors:
                severity = analyzer.repetitive_error_patterns[error_type]['severity']
                description = analyzer.repetitive_error_patterns[error_type]['description']
                print(f"  - {error_type}: {len(errors)} 次 [{severity}] - {description}")
        
        # 业务影响评估
        print(f"\n🎯 业务影响评估:")
        print(f"  - 业务连续性风险: {impact_assessment['business_continuity_risk']}")
        print(f"  - 高影响问题: {len(impact_assessment['high_impact'])} 个")
        print(f"  - 中等影响问题: {len(impact_assessment['medium_impact'])} 个")
        print(f"  - 低影响问题: {len(impact_assessment['low_impact'])} 个")
        
        # 渐进式解决方案
        if progressive_solutions:
            print(f"\n💡 渐进式解决方案:")
            for phase_solution in progressive_solutions:
                print(f"  📋 {phase_solution['phase']} - {phase_solution['description']}")
                print(f"     优先级: {phase_solution['priority']}")
                print(f"     时间线: {phase_solution['timeline']}")
                print(f"     解决方案数: {len(phase_solution['solutions'])} 个")
        
        # 保存详细报告
        report_file = Path("logs") / f"deep_log_analysis_7h_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(analyzer.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 详细分析报告已保存到: {report_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 2

if __name__ == "__main__":
    exit(main())
