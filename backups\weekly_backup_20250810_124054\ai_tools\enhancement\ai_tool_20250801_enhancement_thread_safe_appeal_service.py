#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 解决浏览器自动化技术问题，实现线程安全的智能提审服务
清理条件: 功能被替代时删除
"""

"""
线程安全的智能提审服务 - 第二阶段核心组件
===========================================

解决多线程/进程环境下的浏览器自动化问题：
1. 修复聊天输入框定位失败问题
2. 解决进程隔离pickle序列化问题  
3. 实现线程安全的浏览器会话管理
4. 集成第一阶段的智能状态检查机制

技术特性：
- Celery多进程环境兼容
- 线程安全的资源管理
- 智能重试和降级机制
- 长期稳定运行保障

创建时间: 2025-08-01
版本: v2.0 - 多线程稳定版
"""

import time
import threading
import multiprocessing
from typing import Dict, Any, List, Tuple, Optional
from contextlib import contextmanager
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from loguru import logger

# 导入第一阶段的智能组件
from ai_tool_20250801_enhancement_plan_status_checker import PlanStatusChecker
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign, AdAccount
from qianchuan_aw.services.copilot_service import SimpleCopilotSession


class ThreadSafeBrowserManager:
    """线程安全的浏览器管理器"""
    
    def __init__(self, max_concurrent_browsers: int = 2):
        self.max_concurrent_browsers = max_concurrent_browsers
        self.active_sessions = {}
        self.session_lock = threading.RLock()
        self.account_locks = {}
        self.account_locks_lock = threading.Lock()
        
        # 浏览器会话信号量，限制并发数量
        self.browser_semaphore = threading.Semaphore(max_concurrent_browsers)
        
        logger.info(f"🔧 线程安全浏览器管理器初始化，最大并发: {max_concurrent_browsers}")
    
    def get_account_lock(self, account_key: str) -> threading.Lock:
        """获取账户专用锁，确保同一账户只能被一个线程访问"""
        with self.account_locks_lock:
            if account_key not in self.account_locks:
                self.account_locks[account_key] = threading.Lock()
            return self.account_locks[account_key]
    
    @contextmanager
    def get_browser_session(self, principal_name: str, account_id: int, app_settings: Dict[str, Any]):
        """获取线程安全的浏览器会话"""
        account_key = f"{principal_name}_{account_id}"
        session = None
        
        # 获取浏览器资源信号量
        self.browser_semaphore.acquire()
        
        try:
            # 获取账户专用锁
            account_lock = self.get_account_lock(account_key)
            
            with account_lock:
                logger.info(f"🔒 获取账户 {account_key} 的独占锁")
                
                # 创建浏览器会话
                session = SimpleCopilotSession(principal_name, account_id, app_settings)
                session.__enter__()
                
                with self.session_lock:
                    self.active_sessions[account_key] = {
                        'session': session,
                        'created_at': datetime.now(),
                        'thread_id': threading.current_thread().ident
                    }
                
                logger.success(f"✅ 浏览器会话创建成功: {account_key}")
                yield session
                
        except Exception as e:
            logger.error(f"❌ 浏览器会话创建失败: {e}")
            raise
        finally:
            # 清理会话
            if session:
                try:
                    session.__exit__(None, None, None)
                except Exception as e:
                    logger.error(f"⚠️ 浏览器会话清理失败: {e}")
            
            # 从活跃会话中移除
            with self.session_lock:
                if account_key in self.active_sessions:
                    del self.active_sessions[account_key]
            
            # 释放浏览器资源信号量
            self.browser_semaphore.release()
            logger.info(f"🔓 释放账户 {account_key} 的资源")


class EnhancedChatInputLocator:
    """增强的聊天输入框定位器 - 解决定位失败问题"""
    
    @staticmethod
    def find_chat_input(page, timeout: int = 10000) -> Optional[Any]:
        """使用多种策略定位聊天输入框"""
        
        # 策略1: 基于placeholder的选择器（最常用）
        placeholder_selectors = [
            'textarea[placeholder*="请描述"]',
            'input[placeholder*="请描述"]', 
            'textarea[placeholder*="请输入"]',
            'input[placeholder*="请输入"]',
            'textarea[placeholder*="问题"]',
            'input[placeholder*="问题"]'
        ]
        
        # 策略2: 基于CSS类的选择器
        class_selectors = [
            '.copilot-input textarea',
            '.copilot-input input',
            '.chat-input textarea',
            '.chat-input input',
            '.message-input textarea',
            '.message-input input'
        ]
        
        # 策略3: 基于角色的选择器
        role_selectors = [
            '[role="textbox"]',
            '[contenteditable="true"]'
        ]
        
        # 策略4: 通用选择器（最后尝试）
        generic_selectors = [
            'textarea',
            'input[type="text"]'
        ]
        
        all_selectors = placeholder_selectors + class_selectors + role_selectors + generic_selectors
        
        logger.info(f"🔍 开始定位聊天输入框，尝试 {len(all_selectors)} 种选择器...")
        
        for i, selector in enumerate(all_selectors, 1):
            try:
                logger.debug(f"尝试选择器 {i}/{len(all_selectors)}: {selector}")
                
                # 查找所有匹配的元素
                elements = page.locator(selector).all()
                
                for j, element in enumerate(elements):
                    try:
                        # 检查元素是否可见且可交互
                        if element.is_visible(timeout=3000) and element.is_enabled():
                            logger.success(f"✅ 找到可用输入框: {selector} (第{j+1}个)")
                            return element
                    except Exception as e:
                        logger.debug(f"元素检查失败: {e}")
                        continue
                        
            except Exception as e:
                logger.debug(f"选择器 {selector} 失败: {e}")
                continue
        
        logger.error("❌ 所有选择器都无法定位到可用的聊天输入框")
        return None
    
    @staticmethod
    def send_message_with_fallback(page, message: str, input_element) -> bool:
        """使用多种方式发送消息"""
        
        try:
            # 清空并填入消息
            input_element.clear()
            input_element.fill(message)
            time.sleep(0.5)
            
            # 方法1: 使用Enter键发送（最稳定）
            try:
                input_element.press("Enter")
                logger.success(f"✅ 使用Enter键发送成功: {message[:50]}...")
                return True
            except Exception as e1:
                logger.warning(f"Enter键发送失败: {e1}")
                
                # 方法2: 查找并点击发送按钮
                send_button_selectors = [
                    "button:has-text('发送')",
                    ".copilot-input-sender",
                    ".send-button",
                    "[data-testid='send-button']",
                    "button[type='submit']"
                ]
                
                for selector in send_button_selectors:
                    try:
                        send_btn = page.locator(selector).first
                        if send_btn.is_visible(timeout=2000):
                            send_btn.click()
                            logger.success(f"✅ 点击发送按钮成功: {message[:50]}...")
                            return True
                    except Exception as e2:
                        logger.debug(f"发送按钮 {selector} 失败: {e2}")
                        continue
                
                # 方法3: 使用键盘快捷键
                try:
                    page.keyboard.press("Control+Enter")
                    logger.success(f"✅ 使用Ctrl+Enter发送成功: {message[:50]}...")
                    return True
                except Exception as e3:
                    logger.warning(f"Ctrl+Enter发送失败: {e3}")
                
                logger.error("❌ 所有发送方法都失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ 消息发送过程失败: {e}")
            return False


class ThreadSafeSmartAppealService:
    """线程安全的智能提审服务 - 集成第一阶段成果"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.browser_manager = ThreadSafeBrowserManager(max_concurrent_browsers=2)
        self.status_checker = PlanStatusChecker(app_settings)
        self.chat_locator = EnhancedChatInputLocator()
        
        # 性能统计
        self.stats = {
            'total_appeals': 0,
            'successful_appeals': 0,
            'failed_appeals': 0,
            'browser_errors': 0,
            'status_check_errors': 0,
            'input_location_errors': 0
        }
        
        logger.info("🚀 线程安全智能提审服务初始化完成")
    
    def smart_appeal_single_plan(self, db: Session, campaign: Campaign) -> Tuple[bool, str, Dict]:
        """智能提审单个计划 - 集成状态检查和线程安全浏览器"""
        
        try:
            self.stats['total_appeals'] += 1
            
            # 第一步：检查计划是否准备好提审（使用第一阶段成果）
            logger.info(f"📋 步骤1: 检查计划 {campaign.campaign_id_qc} 是否准备好提审")
            
            is_ready, status_msg, api_status = self.status_checker.check_plan_ready_for_appeal(db, campaign)
            
            if not is_ready:
                logger.warning(f"⚠️ 计划 {campaign.campaign_id_qc} 未准备好: {status_msg}")
                return False, f"计划未准备好: {status_msg}", {'api_status': api_status}
            
            logger.success(f"✅ 计划 {campaign.campaign_id_qc} 已准备好提审: {status_msg}")
            
            # 第二步：使用线程安全浏览器执行提审
            logger.info(f"📋 步骤2: 执行线程安全提审")
            
            principal_name = campaign.account.principal.name
            account_id = campaign.account.account_id_qc
            
            with self.browser_manager.get_browser_session(principal_name, account_id, self.app_settings) as session:
                
                # 使用增强的聊天输入框定位
                success, message = self._execute_appeal_with_enhanced_input(session, campaign.campaign_id_qc)
                
                if success:
                    self.stats['successful_appeals'] += 1
                    
                    # 更新数据库状态
                    campaign.appeal_status = 'appeal_submitted'
                    campaign.first_appeal_at = datetime.now(timezone.utc)
                    campaign.appeal_attempt_count = (campaign.appeal_attempt_count or 0) + 1
                    campaign.updated_at = datetime.now(timezone.utc)
                    
                    logger.success(f"✅ 计划 {campaign.campaign_id_qc} 提审成功")
                    return True, message, {'api_status': api_status}
                else:
                    self.stats['failed_appeals'] += 1
                    logger.error(f"❌ 计划 {campaign.campaign_id_qc} 提审失败: {message}")
                    return False, message, {'api_status': api_status}
                    
        except Exception as e:
            self.stats['failed_appeals'] += 1
            logger.error(f"❌ 智能提审过程异常: {e}", exc_info=True)
            return False, f"提审过程异常: {str(e)}", {}
    
    def _execute_appeal_with_enhanced_input(self, session, plan_id: int) -> Tuple[bool, str]:
        """使用增强的输入框定位执行提审"""
        
        try:
            # 获取页面对象
            page = session.page
            
            # 使用增强的输入框定位器
            chat_input = self.chat_locator.find_chat_input(page)
            
            if not chat_input:
                self.stats['input_location_errors'] += 1
                return False, "无法定位聊天输入框"
            
            # 构造提审命令
            appeal_command = f"自助申诉表单{plan_id}"
            
            # 使用增强的消息发送方法
            send_success = self.chat_locator.send_message_with_fallback(page, appeal_command, chat_input)
            
            if not send_success:
                return False, "消息发送失败"
            
            # 等待回复
            logger.info("⏳ 等待智投星回复...")
            time.sleep(8)
            
            # 获取回复
            reply_text = self._get_latest_reply(page)
            
            if not reply_text:
                return False, "未收到回复"
            
            # 分析回复内容
            if self._is_successful_appeal_reply(reply_text):
                return True, f"提审成功: {reply_text[:100]}..."
            else:
                return False, f"提审失败: {reply_text[:100]}..."
                
        except Exception as e:
            self.stats['browser_errors'] += 1
            logger.error(f"❌ 浏览器操作失败: {e}")
            return False, f"浏览器操作失败: {str(e)}"
    
    def _get_latest_reply(self, page) -> Optional[str]:
        """获取最新回复"""
        try:
            # 多种回复选择器
            reply_selectors = [
                ".role-robot:last-child",
                ".copilot-message:last-child .message-content", 
                ".message-item:last-child .content",
                ".chat-message:last-child"
            ]
            
            for selector in reply_selectors:
                try:
                    reply_elements = page.locator(selector).all()
                    if reply_elements:
                        latest_reply = reply_elements[-1]
                        reply_text = latest_reply.inner_text()
                        if reply_text.strip():
                            return reply_text
                except Exception:
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"获取回复失败: {e}")
            return None
    
    def _is_successful_appeal_reply(self, reply_text: str) -> bool:
        """判断回复是否表示提审成功"""
        success_indicators = [
            "申诉已提交",
            "已收到申诉",
            "申诉成功",
            "审核中",
            "已转人工"
        ]
        
        failure_indicators = [
            "查询到该内容暂无广告审核建议",
            "请检查下计划/订单状态",
            "无法找到",
            "不存在",
            "错误"
        ]
        
        reply_lower = reply_text.lower()
        
        # 检查失败指标
        for indicator in failure_indicators:
            if indicator in reply_lower:
                return False
        
        # 检查成功指标
        for indicator in success_indicators:
            if indicator in reply_lower:
                return True
        
        # 默认认为是成功（保守策略）
        return True
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        total = self.stats['total_appeals']
        if total == 0:
            return self.stats
        
        success_rate = (self.stats['successful_appeals'] / total) * 100
        
        return {
            **self.stats,
            'success_rate': success_rate,
            'browser_error_rate': (self.stats['browser_errors'] / total) * 100,
            'input_location_error_rate': (self.stats['input_location_errors'] / total) * 100
        }


def create_thread_safe_smart_appeal_service(app_settings: Dict[str, Any]) -> ThreadSafeSmartAppealService:
    """创建线程安全的智能提审服务实例"""
    return ThreadSafeSmartAppealService(app_settings)


# 测试和验证函数
def test_thread_safe_appeal_service():
    """测试线程安全提审服务"""
    logger.info("🧪 开始测试线程安全提审服务")
    
    try:
        from qianchuan_aw.utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        service = create_thread_safe_smart_appeal_service(app_settings)
        
        with database_session() as db:
            # 查找需要提审的计划
            campaigns = db.query(Campaign).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None)
            ).limit(3).all()
            
            if not campaigns:
                logger.info("✅ 没有找到需要测试的计划")
                return
            
            logger.info(f"🎯 找到 {len(campaigns)} 个计划进行测试")
            
            for campaign in campaigns:
                logger.info(f"📋 测试计划: {campaign.campaign_id_qc}")
                
                success, message, details = service.smart_appeal_single_plan(db, campaign)
                
                if success:
                    logger.success(f"✅ 测试成功: {message}")
                else:
                    logger.warning(f"⚠️ 测试失败: {message}")
            
            # 显示性能统计
            stats = service.get_performance_stats()
            logger.info(f"📊 测试统计: {stats}")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    test_thread_safe_appeal_service()
