#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 测试视频素材搜索功能
清理条件: 功能验证完成后可删除

视频素材搜索功能测试工具
======================

测试新恢复的视频素材搜索功能是否正常工作。
"""

import os
import sys
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import Local<PERSON>reative, Principal, Campaign
from sqlalchemy.orm import joinedload
from sqlalchemy import func


class MaterialSearchTester:
    """素材搜索功能测试器"""
    
    def __init__(self):
        self.test_results = []
    
    def log_result(self, test: str, status: str, message: str, details: dict = None):
        """记录测试结果"""
        result = {
            'test': test,
            'status': status,
            'message': message,
            'details': details or {},
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        
        if status == 'pass':
            logger.info(f"✅ {test}: {message}")
        elif status == 'warning':
            logger.warning(f"⚠️ {test}: {message}")
        else:
            logger.error(f"❌ {test}: {message}")
    
    def test_database_connection(self):
        """测试数据库连接"""
        logger.info("🧪 测试1: 数据库连接...")
        
        try:
            with database_session() as db:
                # 测试基本查询
                material_count = db.query(LocalCreative).count()
                principal_count = db.query(Principal).count()
                
                details = {
                    'material_count': material_count,
                    'principal_count': principal_count
                }
                
                if material_count > 0:
                    self.log_result("数据库连接", "pass", 
                                  f"连接成功，找到 {material_count} 个素材，{principal_count} 个主体",
                                  details)
                    return True
                else:
                    self.log_result("数据库连接", "warning", 
                                  "连接成功但没有素材数据",
                                  details)
                    return True
                    
        except Exception as e:
            self.log_result("数据库连接", "fail", f"连接失败: {e}")
            return False
    
    def test_material_search_by_name(self):
        """测试按名称搜索素材"""
        logger.info("🧪 测试2: 按名称搜索素材...")
        
        try:
            with database_session() as db:
                # 获取一个示例素材名称
                sample_material = db.query(LocalCreative).filter(
                    LocalCreative.filename.isnot(None)
                ).first()
                
                if not sample_material:
                    self.log_result("按名称搜索", "warning", "没有找到有文件名的素材进行测试")
                    return True
                
                # 使用部分文件名进行搜索
                search_term = sample_material.filename[:5] if len(sample_material.filename) > 5 else sample_material.filename
                
                # 执行搜索
                results = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    LocalCreative.filename.ilike(f'%{search_term}%')
                ).limit(10).all()
                
                details = {
                    'search_term': search_term,
                    'result_count': len(results),
                    'sample_filename': sample_material.filename
                }
                
                if results:
                    self.log_result("按名称搜索", "pass", 
                                  f"搜索 '{search_term}' 找到 {len(results)} 个结果",
                                  details)
                    return True
                else:
                    self.log_result("按名称搜索", "warning", 
                                  f"搜索 '{search_term}' 没有找到结果",
                                  details)
                    return True
                    
        except Exception as e:
            self.log_result("按名称搜索", "fail", f"搜索失败: {e}")
            return False
    
    def test_material_search_by_id(self):
        """测试按ID搜索素材"""
        logger.info("🧪 测试3: 按ID搜索素材...")
        
        try:
            with database_session() as db:
                # 获取一个示例素材ID
                sample_material = db.query(LocalCreative).first()
                
                if not sample_material:
                    self.log_result("按ID搜索", "warning", "没有找到素材进行测试")
                    return True
                
                # 按ID搜索
                result = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    LocalCreative.id == sample_material.id
                ).first()
                
                details = {
                    'search_id': sample_material.id,
                    'found': result is not None
                }
                
                if result:
                    self.log_result("按ID搜索", "pass", 
                                  f"按ID {sample_material.id} 搜索成功",
                                  details)
                    return True
                else:
                    self.log_result("按ID搜索", "fail", 
                                  f"按ID {sample_material.id} 搜索失败",
                                  details)
                    return False
                    
        except Exception as e:
            self.log_result("按ID搜索", "fail", f"搜索失败: {e}")
            return False
    
    def test_material_status_filter(self):
        """测试按状态筛选素材"""
        logger.info("🧪 测试4: 按状态筛选素材...")
        
        try:
            with database_session() as db:
                # 统计各状态的素材数量
                status_counts = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).all()
                
                details = {
                    'status_distribution': {status: count for status, count in status_counts}
                }
                
                if status_counts:
                    # 测试筛选最常见的状态
                    most_common_status = max(status_counts, key=lambda x: x.count)
                    
                    filtered_results = db.query(LocalCreative).filter(
                        LocalCreative.status == most_common_status.status
                    ).limit(5).all()
                    
                    details['test_status'] = most_common_status.status
                    details['filtered_count'] = len(filtered_results)
                    
                    self.log_result("按状态筛选", "pass", 
                                  f"状态筛选正常，'{most_common_status.status}' 状态有 {most_common_status.count} 个素材",
                                  details)
                    return True
                else:
                    self.log_result("按状态筛选", "warning", "没有找到素材状态数据", details)
                    return True
                    
        except Exception as e:
            self.log_result("按状态筛选", "fail", f"状态筛选失败: {e}")
            return False
    
    def test_principal_filter(self):
        """测试按主体筛选素材"""
        logger.info("🧪 测试5: 按主体筛选素材...")
        
        try:
            with database_session() as db:
                # 获取主体列表
                principals = db.query(Principal.name).distinct().all()
                
                if not principals:
                    self.log_result("按主体筛选", "warning", "没有找到主体数据")
                    return True
                
                # 测试第一个主体的筛选
                test_principal = principals[0].name
                
                filtered_results = db.query(LocalCreative).options(
                    joinedload(LocalCreative.principal)
                ).filter(
                    LocalCreative.principal.has(Principal.name == test_principal)
                ).limit(5).all()
                
                details = {
                    'total_principals': len(principals),
                    'test_principal': test_principal,
                    'filtered_count': len(filtered_results)
                }
                
                self.log_result("按主体筛选", "pass", 
                              f"主体筛选正常，'{test_principal}' 主体有 {len(filtered_results)} 个素材",
                              details)
                return True
                
        except Exception as e:
            self.log_result("按主体筛选", "fail", f"主体筛选失败: {e}")
            return False
    
    def test_related_campaigns_query(self):
        """测试相关计划查询"""
        logger.info("🧪 测试6: 相关计划查询...")
        
        try:
            with database_session() as db:
                # 查找有上传账户ID的素材
                material_with_account = db.query(LocalCreative).filter(
                    LocalCreative.uploaded_to_account_id.isnot(None)
                ).first()
                
                if not material_with_account:
                    self.log_result("相关计划查询", "warning", "没有找到有上传账户ID的素材")
                    return True
                
                # 查询相关计划
                campaigns = db.query(Campaign).filter(
                    Campaign.account_id == material_with_account.uploaded_to_account_id
                ).limit(5).all()
                
                details = {
                    'material_id': material_with_account.id,
                    'account_id': material_with_account.uploaded_to_account_id,
                    'campaign_count': len(campaigns)
                }
                
                self.log_result("相关计划查询", "pass", 
                              f"素材 {material_with_account.id} 找到 {len(campaigns)} 个相关计划",
                              details)
                return True
                
        except Exception as e:
            self.log_result("相关计划查询", "fail", f"计划查询失败: {e}")
            return False
    
    def test_page_import(self):
        """测试页面导入"""
        logger.info("🧪 测试7: 页面导入...")
        
        try:
            # 尝试导入素材搜索页面
            from qianchuan_aw.ui.pages.material_search import show_material_search_page
            
            # 检查函数是否可调用
            if callable(show_material_search_page):
                self.log_result("页面导入", "pass", "素材搜索页面导入成功")
                return True
            else:
                self.log_result("页面导入", "fail", "导入的函数不可调用")
                return False
                
        except ImportError as e:
            self.log_result("页面导入", "fail", f"页面导入失败: {e}")
            return False
        except Exception as e:
            self.log_result("页面导入", "fail", f"页面测试失败: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 生成素材搜索功能测试报告...")
        
        pass_count = sum(1 for r in self.test_results if r['status'] == 'pass')
        warning_count = sum(1 for r in self.test_results if r['status'] == 'warning')
        fail_count = sum(1 for r in self.test_results if r['status'] == 'fail')
        total_count = len(self.test_results)
        
        report = f"""
视频素材搜索功能测试报告
======================

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

测试结果概览:
- 总测试项: {total_count}
- 通过项: {pass_count}
- 警告项: {warning_count}
- 失败项: {fail_count}
- 通过率: {pass_count/max(total_count, 1)*100:.1f}%

详细测试结果:
"""
        
        for result in self.test_results:
            if result['status'] == 'pass':
                status_icon = "✅"
            elif result['status'] == 'warning':
                status_icon = "⚠️"
            else:
                status_icon = "❌"
            
            report += f"{status_icon} {result['test']}: {result['message']}\n"
            
            # 添加详细信息
            if result['details']:
                for key, value in result['details'].items():
                    report += f"   - {key}: {value}\n"
        
        report += f"""
功能状态评估:
"""
        
        if fail_count == 0:
            if warning_count == 0:
                report += "🎉 视频素材搜索功能完全正常！可以在WebUI中使用。\n"
            else:
                report += "✅ 视频素材搜索功能基本正常，有少量警告需要关注。\n"
        elif fail_count <= total_count * 0.2:
            report += "⚠️ 视频素材搜索功能大部分正常，少量问题需要修复。\n"
        else:
            report += "❌ 视频素材搜索功能存在较多问题，需要进一步修复。\n"
        
        report += f"""
使用说明:
1. 在WebUI中选择 "🎬 素材管理" -> "🔍 视频素材搜索"
2. 支持按文件名、ID、主体、状态等条件搜索
3. 可以查看素材详细信息和相关计划
4. 提供快速搜索功能（最近上传、失败素材等）
"""
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'material_search_test_report_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 素材搜索测试报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚀 开始视频素材搜索功能测试...")
    
    tester = MaterialSearchTester()
    
    try:
        logger.info("=" * 60)
        logger.info("视频素材搜索功能测试")
        logger.info("=" * 60)
        
        # 执行所有测试
        tester.test_database_connection()
        tester.test_material_search_by_name()
        tester.test_material_search_by_id()
        tester.test_material_status_filter()
        tester.test_principal_filter()
        tester.test_related_campaigns_query()
        tester.test_page_import()
        
        # 生成测试报告
        logger.info("=" * 60)
        report = tester.generate_test_report()
        
        logger.info("🎯 素材搜索功能测试完成！")
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
