#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强批量任务处理器
提供事务安全的批量操作
"""

from typing import List, Dict, Any, Callable
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger


class EnhancedBatchProcessor:
    """增强批量任务处理器 - 提供事务安全保证"""
    
    def __init__(self, db_session):
        self.db = db_session
        # 延迟导入避免循环依赖
        from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager
        self.state_manager = AtomicStateManager(db_session)
    
    @contextmanager
    def batch_transaction(self, batch_name: str):
        """批量事务上下文管理器"""
        logger.info(f"🚀 开始批量事务: {batch_name}")
        
        savepoint = None
        try:
            savepoint = self.db.begin_nested()
            
            yield self
            
            savepoint.commit()
            self.db.commit()
            
            logger.success(f"✅ 批量事务完成: {batch_name}")
            
        except Exception as e:
            if savepoint:
                savepoint.rollback()
            self.db.rollback()
            
            logger.error(f"❌ 批量事务失败: {batch_name}, 错误: {e}")
            raise
    
    def process_batch_with_rollback(self, items: List[Any], processor: Callable, batch_size: int = 10):
        """带回滚保护的批量处理"""
        total_processed = 0
        total_failed = 0
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            try:
                with self.batch_transaction(f"batch_{i//batch_size + 1}"):
                    for item in batch:
                        processor(item)
                        total_processed += 1
                        
            except Exception as e:
                total_failed += len(batch)
                logger.error(f"批次 {i//batch_size + 1} 处理失败: {e}")
                continue
        
        return {
            'total_processed': total_processed,
            'total_failed': total_failed,
            'success_rate': total_processed / len(items) if items else 0
        }
