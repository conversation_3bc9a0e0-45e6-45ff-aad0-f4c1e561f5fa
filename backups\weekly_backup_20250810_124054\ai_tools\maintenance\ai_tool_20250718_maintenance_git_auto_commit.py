#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 千川项目Git自动提交和项目保护系统
依赖关系: 与ai_file_manager.py集成，依赖config/settings.yml
清理条件: 项目不再需要Git自动化管理时
"""

import os
import sys
import subprocess
import json
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Set, Optional, Tuple
import hashlib
import shutil
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class QianchuanGitAutoCommit:
    """千川项目Git自动提交和保护系统"""
    
    def __init__(self):
        self.project_root = project_root
        self.config = self._load_config()
        self.logger = self._setup_logger()
        
        # Git自动提交配置
        self.auto_commit_config = {
            "enabled": True,
            "triggers": {
                "file_count_threshold": 5,  # 修改文件数量阈值
                "time_interval_hours": 24,  # 定时提交间隔
                "critical_files_changed": True,  # 关键文件变更立即提交
                "api_changes": True,  # API变更立即提交
                "config_changes": True,  # 配置文件变更立即提交
            },
            "commit_scope": {
                "include_patterns": [
                    "src/**/*.py",
                    "config/*.yml",
                    "config/*.json",
                    "tools/*.py",
                    "ai_tools/**/*",
                    "ai_templates/**/*",
                    "requirements.txt",
                    "main.py",
                    "web_ui.py",
                    "run_celery_*.py"
                ],
                "exclude_patterns": [
                    "ai_temp/**/*",
                    "logs/**/*",
                    "debug_screenshots/**/*",
                    "**/__pycache__/**",
                    "*.pyc",
                    "*.log",
                    "config/auth_tokens.json",
                    "config/browser_cookies.json",
                    "config/codegen_state.json",
                    "workflow_assets/**/*",
                    "snapshots/**/*"
                ],
                "ai_reports_selective": True  # AI报告选择性提交
            }
        }
        
        # 关键文件列表
        self.critical_files = {
            "config/settings.yml": "主配置文件",
            "src/qianchuan_aw/sdk_qc/client.py": "千川API客户端",
            "src/qianchuan_aw/database/models.py": "数据库模型",
            "src/qianchuan_aw/workflows/": "核心工作流",
            "main.py": "主程序入口",
            "web_ui.py": "Web界面",
            "requirements.txt": "依赖包列表"
        }
        
    def _load_config(self) -> Dict:
        """加载项目配置"""
        config_path = self.project_root / "config" / "settings.yml"
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        return {}
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('git_auto_commit')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "git_auto_commit.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def check_git_status(self) -> Dict:
        """检查Git状态"""
        try:
            # 检查是否在Git仓库中
            result = subprocess.run(
                ["git", "rev-parse", "--is-inside-work-tree"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                return {"error": "不在Git仓库中"}
            
            # 获取状态信息
            status_result = subprocess.run(
                ["git", "status", "--porcelain"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if status_result.returncode != 0:
                return {"error": "无法获取Git状态"}
            
            # 解析状态
            status_lines = status_result.stdout.strip().split('\n')
            if status_lines == ['']:
                status_lines = []
            
            modified_files = []
            added_files = []
            deleted_files = []
            untracked_files = []
            
            for line in status_lines:
                if len(line) < 3:
                    continue
                    
                status_code = line[:2]
                file_path = line[3:]
                
                if status_code[0] == 'M' or status_code[1] == 'M':
                    modified_files.append(file_path)
                elif status_code[0] == 'A' or status_code[1] == 'A':
                    added_files.append(file_path)
                elif status_code[0] == 'D' or status_code[1] == 'D':
                    deleted_files.append(file_path)
                elif status_code == '??':
                    untracked_files.append(file_path)
            
            return {
                "clean": len(status_lines) == 0,
                "modified_files": modified_files,
                "added_files": added_files,
                "deleted_files": deleted_files,
                "untracked_files": untracked_files,
                "total_changes": len(status_lines)
            }
            
        except Exception as e:
            return {"error": f"检查Git状态失败: {e}"}
    
    def analyze_changes(self, git_status: Dict) -> Dict:
        """分析变更内容"""
        if "error" in git_status:
            return {"error": git_status["error"]}
        
        analysis = {
            "critical_files_changed": False,
            "api_files_changed": False,
            "config_files_changed": False,
            "ai_files_changed": False,
            "core_business_changed": False,
            "change_categories": [],
            "commit_priority": "normal"
        }
        
        all_changed_files = (
            git_status.get("modified_files", []) +
            git_status.get("added_files", []) +
            git_status.get("deleted_files", []) +
            git_status.get("untracked_files", [])
        )
        
        for file_path in all_changed_files:
            # 检查关键文件
            for critical_pattern, description in self.critical_files.items():
                if file_path.startswith(critical_pattern.rstrip('/')):
                    analysis["critical_files_changed"] = True
                    analysis["change_categories"].append(f"关键文件: {description}")
                    analysis["commit_priority"] = "high"
            
            # 检查API文件
            if "sdk_qc" in file_path or "api" in file_path:
                analysis["api_files_changed"] = True
                analysis["change_categories"].append("API接口")
            
            # 检查配置文件
            if file_path.startswith("config/"):
                analysis["config_files_changed"] = True
                analysis["change_categories"].append("配置文件")
                analysis["commit_priority"] = "high"
            
            # 检查AI文件
            if any(file_path.startswith(prefix) for prefix in ["ai_tools/", "ai_templates/", "ai_reports/"]):
                analysis["ai_files_changed"] = True
                analysis["change_categories"].append("AI生成文件")
            
            # 检查核心业务文件
            if any(pattern in file_path for pattern in ["workflows/", "services/", "database/"]):
                analysis["core_business_changed"] = True
                analysis["change_categories"].append("核心业务逻辑")
                analysis["commit_priority"] = "high"
        
        # 去重分类
        analysis["change_categories"] = list(set(analysis["change_categories"]))
        
        return analysis
    
    def should_auto_commit(self, git_status: Dict, analysis: Dict) -> Tuple[bool, str]:
        """判断是否应该自动提交"""
        if not self.auto_commit_config["enabled"]:
            return False, "自动提交功能已禁用"
        
        if git_status.get("clean", True):
            return False, "没有变更需要提交"
        
        triggers = self.auto_commit_config["triggers"]
        
        # 检查文件数量阈值
        if git_status.get("total_changes", 0) >= triggers["file_count_threshold"]:
            return True, f"变更文件数量达到阈值 ({git_status['total_changes']}/{triggers['file_count_threshold']})"
        
        # 检查关键文件变更
        if triggers["critical_files_changed"] and analysis.get("critical_files_changed", False):
            return True, "关键文件发生变更"
        
        # 检查API变更
        if triggers["api_changes"] and analysis.get("api_files_changed", False):
            return True, "API文件发生变更"
        
        # 检查配置变更
        if triggers["config_changes"] and analysis.get("config_files_changed", False):
            return True, "配置文件发生变更"
        
        # 检查定时提交
        last_commit_time = self._get_last_commit_time()
        if last_commit_time:
            time_since_last = datetime.now() - last_commit_time
            if time_since_last.total_seconds() >= triggers["time_interval_hours"] * 3600:
                return True, f"距离上次提交已超过 {triggers['time_interval_hours']} 小时"
        
        return False, "未满足自动提交条件"
    
    def _get_last_commit_time(self) -> Optional[datetime]:
        """获取最后一次提交时间"""
        try:
            result = subprocess.run(
                ["git", "log", "-1", "--format=%ct"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0 and result.stdout.strip():
                timestamp = int(result.stdout.strip())
                return datetime.fromtimestamp(timestamp)
                
        except Exception as e:
            self.logger.warning(f"获取最后提交时间失败: {e}")
        
        return None
    
    def generate_commit_message(self, analysis: Dict, git_status: Dict) -> str:
        """生成标准化提交消息"""
        # 确定emoji前缀
        emoji_map = {
            "API接口": "🔌",
            "核心业务逻辑": "⚡",
            "配置文件": "🔧",
            "AI生成文件": "🤖",
            "关键文件": "🚀",
            "数据库": "🗄️",
            "Web界面": "🌐",
            "工具脚本": "🛠️",
            "文档": "📝"
        }
        
        # 选择主要emoji
        main_emoji = "📝"  # 默认
        for category in analysis.get("change_categories", []):
            if category in emoji_map:
                main_emoji = emoji_map[category]
                break
        
        # 构建提交消息
        categories = analysis.get("change_categories", [])
        if not categories:
            categories = ["常规更新"]
        
        # 主标题
        title = f"{main_emoji} {', '.join(categories[:2])}"  # 最多显示2个分类
        if len(categories) > 2:
            title += f" 等{len(categories)}项更新"
        
        # 详细信息
        details = []
        
        # 文件统计
        modified_count = len(git_status.get("modified_files", []))
        added_count = len(git_status.get("added_files", []))
        deleted_count = len(git_status.get("deleted_files", []))
        
        if modified_count > 0:
            details.append(f"修改: {modified_count}个文件")
        if added_count > 0:
            details.append(f"新增: {added_count}个文件")
        if deleted_count > 0:
            details.append(f"删除: {deleted_count}个文件")
        
        # 重要变更说明
        if analysis.get("critical_files_changed"):
            details.append("⚠️ 包含关键文件变更")
        if analysis.get("api_files_changed"):
            details.append("🔌 API接口更新")
        if analysis.get("config_files_changed"):
            details.append("🔧 配置文件更新")
        
        # 组合消息
        message = title
        if details:
            message += "\n\n" + "\n".join(f"- {detail}" for detail in details)
        
        # 添加时间戳
        message += f"\n\n自动提交时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return message

    def run_safety_checks(self, git_status: Dict) -> Dict:
        """运行提交前安全检查"""
        checks = {
            "python_syntax": {"passed": True, "errors": []},
            "yaml_syntax": {"passed": True, "errors": []},
            "sensitive_files": {"passed": True, "warnings": []},
            "dependency_consistency": {"passed": True, "errors": []},
            "overall_passed": True
        }

        all_files = (
            git_status.get("modified_files", []) +
            git_status.get("added_files", []) +
            git_status.get("untracked_files", [])
        )

        # Python语法检查
        python_files = [f for f in all_files if f.endswith('.py')]
        for py_file in python_files:
            file_path = self.project_root / py_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        compile(f.read(), py_file, 'exec')
                except SyntaxError as e:
                    checks["python_syntax"]["passed"] = False
                    checks["python_syntax"]["errors"].append(f"{py_file}: {e}")
                    checks["overall_passed"] = False

        # YAML语法检查
        yaml_files = [f for f in all_files if f.endswith(('.yml', '.yaml'))]
        for yaml_file in yaml_files:
            file_path = self.project_root / yaml_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        yaml.safe_load(f)
                except yaml.YAMLError as e:
                    checks["yaml_syntax"]["passed"] = False
                    checks["yaml_syntax"]["errors"].append(f"{yaml_file}: {e}")
                    checks["overall_passed"] = False

        # 敏感文件检查
        sensitive_patterns = [
            "auth_tokens.json",
            "browser_cookies.json",
            "codegen_state.json",
            "password",
            "secret",
            "token"
        ]

        for file_path in all_files:
            if any(pattern in file_path.lower() for pattern in sensitive_patterns):
                checks["sensitive_files"]["warnings"].append(f"可能包含敏感信息: {file_path}")

        # 依赖一致性检查
        if "requirements.txt" in all_files:
            # 这里可以添加更复杂的依赖检查逻辑
            pass

        return checks

    def execute_commit(self, message: str, git_status: Dict) -> Dict:
        """执行Git提交"""
        try:
            # 添加文件到暂存区
            include_patterns = self.auto_commit_config["commit_scope"]["include_patterns"]
            exclude_patterns = self.auto_commit_config["commit_scope"]["exclude_patterns"]

            all_files = (
                git_status.get("modified_files", []) +
                git_status.get("added_files", []) +
                git_status.get("untracked_files", [])
            )

            # 过滤要提交的文件
            files_to_commit = []
            for file_path in all_files:
                # 检查包含模式
                should_include = any(
                    self._match_pattern(file_path, pattern)
                    for pattern in include_patterns
                )

                # 检查排除模式
                should_exclude = any(
                    self._match_pattern(file_path, pattern)
                    for pattern in exclude_patterns
                )

                # AI报告特殊处理
                if file_path.startswith("ai_reports/") and self.auto_commit_config["commit_scope"]["ai_reports_selective"]:
                    # 只提交重要的AI报告
                    if any(keyword in file_path.lower() for keyword in ["final", "important", "summary", "audit"]):
                        should_include = True
                        should_exclude = False
                    else:
                        should_exclude = True

                if should_include and not should_exclude:
                    files_to_commit.append(file_path)

            if not files_to_commit:
                return {"success": False, "message": "没有符合提交条件的文件"}

            # 添加文件到暂存区
            for file_path in files_to_commit:
                result = subprocess.run(
                    ["git", "add", file_path],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True
                )

                if result.returncode != 0:
                    return {"success": False, "message": f"添加文件失败: {file_path}\n{result.stderr}"}

            # 执行提交
            result = subprocess.run(
                ["git", "commit", "-m", message],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                return {"success": False, "message": f"提交失败:\n{result.stderr}"}

            return {
                "success": True,
                "message": "提交成功",
                "files_committed": files_to_commit,
                "commit_hash": self._get_latest_commit_hash()
            }

        except Exception as e:
            return {"success": False, "message": f"执行提交时发生错误: {e}"}

    def _match_pattern(self, file_path: str, pattern: str) -> bool:
        """匹配文件路径模式"""
        import fnmatch
        return fnmatch.fnmatch(file_path, pattern)

    def _get_latest_commit_hash(self) -> Optional[str]:
        """获取最新提交的哈希值"""
        try:
            result = subprocess.run(
                ["git", "rev-parse", "HEAD"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                return result.stdout.strip()

        except Exception:
            pass

        return None

    def create_backup(self) -> Dict:
        """创建项目备份"""
        try:
            backup_dir = self.project_root / "backups"
            backup_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"qianchuan_backup_{timestamp}"
            backup_path = backup_dir / backup_name

            # 创建Git存档
            result = subprocess.run(
                ["git", "archive", "--format=zip", f"--output={backup_path}.zip", "HEAD"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )

            if result.returncode != 0:
                return {"success": False, "message": f"创建Git存档失败: {result.stderr}"}

            # 备份配置文件
            config_backup_dir = backup_path.with_suffix(".config")
            config_backup_dir.mkdir(exist_ok=True)

            config_files = [
                "config/settings.yml",
                "config/banned_terms.yml",
                "requirements.txt"
            ]

            for config_file in config_files:
                src_path = self.project_root / config_file
                if src_path.exists():
                    dst_path = config_backup_dir / config_file
                    dst_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(src_path, dst_path)

            return {
                "success": True,
                "backup_path": str(backup_path.with_suffix(".zip")),
                "config_backup_path": str(config_backup_dir),
                "timestamp": timestamp
            }

        except Exception as e:
            return {"success": False, "message": f"创建备份失败: {e}"}

    def auto_commit_workflow(self) -> Dict:
        """自动提交工作流"""
        self.logger.info("开始Git自动提交工作流")

        # 1. 检查Git状态
        git_status = self.check_git_status()
        if "error" in git_status:
            return {"success": False, "step": "git_status", "error": git_status["error"]}

        # 2. 分析变更
        analysis = self.analyze_changes(git_status)
        if "error" in analysis:
            return {"success": False, "step": "analyze_changes", "error": analysis["error"]}

        # 3. 判断是否需要提交
        should_commit, reason = self.should_auto_commit(git_status, analysis)
        if not should_commit:
            return {"success": True, "action": "skip", "reason": reason}

        self.logger.info(f"触发自动提交: {reason}")

        # 4. 运行安全检查
        safety_checks = self.run_safety_checks(git_status)
        if not safety_checks["overall_passed"]:
            return {
                "success": False,
                "step": "safety_checks",
                "error": "安全检查未通过",
                "details": safety_checks
            }

        # 5. 创建备份
        backup_result = self.create_backup()
        if not backup_result["success"]:
            self.logger.warning(f"备份创建失败: {backup_result['message']}")

        # 6. 生成提交消息
        commit_message = self.generate_commit_message(analysis, git_status)

        # 7. 执行提交
        commit_result = self.execute_commit(commit_message, git_status)

        if commit_result["success"]:
            self.logger.info(f"自动提交成功: {commit_result.get('commit_hash', 'unknown')}")
            return {
                "success": True,
                "action": "committed",
                "commit_hash": commit_result.get("commit_hash"),
                "files_committed": commit_result.get("files_committed", []),
                "backup_created": backup_result["success"],
                "commit_message": commit_message
            }
        else:
            self.logger.error(f"自动提交失败: {commit_result['message']}")
            return {
                "success": False,
                "step": "commit",
                "error": commit_result["message"]
            }

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='千川项目Git自动提交工具')
    parser.add_argument('action', choices=['status', 'check', 'commit', 'backup'],
                       help='执行的操作')
    parser.add_argument('--force', action='store_true', help='强制执行')
    parser.add_argument('--dry-run', action='store_true', help='预览模式')

    args = parser.parse_args()

    git_manager = QianchuanGitAutoCommit()

    if args.action == 'status':
        status = git_manager.check_git_status()
        print(json.dumps(status, indent=2, ensure_ascii=False))

    elif args.action == 'check':
        status = git_manager.check_git_status()
        if "error" not in status:
            analysis = git_manager.analyze_changes(status)
            should_commit, reason = git_manager.should_auto_commit(status, analysis)
            print(f"是否应该提交: {should_commit}")
            print(f"原因: {reason}")
            if should_commit:
                message = git_manager.generate_commit_message(analysis, status)
                print(f"提交消息预览:\n{message}")

    elif args.action == 'commit':
        if args.dry_run:
            print("预览模式 - 不会实际提交")
        result = git_manager.auto_commit_workflow()
        print(json.dumps(result, indent=2, ensure_ascii=False))

    elif args.action == 'backup':
        result = git_manager.create_backup()
        print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
