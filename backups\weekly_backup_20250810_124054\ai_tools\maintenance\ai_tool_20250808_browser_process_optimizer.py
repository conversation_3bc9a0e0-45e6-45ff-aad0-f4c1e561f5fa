#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 浏览器进程优化和管理
清理条件: 成为项目维护工具，长期保留
"""

import os
import sys
import psutil
import time
from datetime import datetime, timezone
from typing import List, Dict, Any
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class BrowserProcessOptimizer:
    """浏览器进程优化器"""
    
    def __init__(self):
        self.max_browser_processes = 3  # 最大允许的浏览器进程数
        self.max_memory_per_process = 800  # 每个进程最大内存(MB)
        self.process_timeout = 1800  # 进程超时时间(30分钟)
    
    def get_browser_processes(self) -> List[Dict[str, Any]]:
        """获取所有浏览器进程信息"""
        browser_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'create_time', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info['name'].lower()
                    
                    # 检查是否为浏览器进程
                    if any(browser in name for browser in ['chrome', 'chromium', 'msedge']):
                        # 过滤掉系统浏览器，只关注我们启动的
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage', '--remote-debugging-port']):
                            memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                            age_seconds = time.time() - proc_info['create_time']
                            
                            browser_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'memory_mb': round(memory_mb, 1),
                                'age_seconds': round(age_seconds),
                                'age_minutes': round(age_seconds / 60, 1),
                                'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            logger.error(f"获取浏览器进程信息失败: {e}")
        
        return browser_processes
    
    def analyze_browser_usage(self) -> Dict[str, Any]:
        """分析浏览器使用情况"""
        processes = self.get_browser_processes()
        
        if not processes:
            return {
                'total_processes': 0,
                'total_memory_mb': 0,
                'average_memory_mb': 0,
                'oldest_process_minutes': 0,
                'processes': [],
                'recommendations': ['✅ 当前无浏览器进程运行']
            }
        
        total_memory = sum(p['memory_mb'] for p in processes)
        average_memory = total_memory / len(processes)
        oldest_process = max(processes, key=lambda p: p['age_seconds'])
        
        # 生成建议
        recommendations = []
        
        if len(processes) > self.max_browser_processes:
            recommendations.append(f"⚠️ 浏览器进程过多: {len(processes)} > {self.max_browser_processes}")
        
        high_memory_processes = [p for p in processes if p['memory_mb'] > self.max_memory_per_process]
        if high_memory_processes:
            recommendations.append(f"⚠️ 发现 {len(high_memory_processes)} 个高内存进程")
        
        old_processes = [p for p in processes if p['age_seconds'] > self.process_timeout]
        if old_processes:
            recommendations.append(f"⚠️ 发现 {len(old_processes)} 个超时进程")
        
        if not recommendations:
            recommendations.append("✅ 浏览器进程使用正常")
        
        return {
            'total_processes': len(processes),
            'total_memory_mb': round(total_memory, 1),
            'average_memory_mb': round(average_memory, 1),
            'oldest_process_minutes': oldest_process['age_minutes'],
            'processes': processes,
            'recommendations': recommendations
        }
    
    def cleanup_excess_processes(self, dry_run: bool = False) -> Dict[str, Any]:
        """清理多余的浏览器进程"""
        logger.info(f"🧹 开始清理多余浏览器进程 (dry_run={dry_run})...")
        
        processes = self.get_browser_processes()
        if not processes:
            logger.info("✅ 当前无浏览器进程需要清理")
            return {'cleaned': 0, 'total': 0}
        
        # 按年龄排序，优先清理最老的进程
        processes.sort(key=lambda p: p['age_seconds'], reverse=True)
        
        cleaned_count = 0
        cleanup_reasons = []
        
        for i, proc in enumerate(processes):
            should_cleanup = False
            reason = ""
            
            # 规则1：进程数量超限，清理最老的
            if i >= self.max_browser_processes:
                should_cleanup = True
                reason = f"进程数量超限 ({len(processes)} > {self.max_browser_processes})"
            
            # 规则2：内存使用过高
            elif proc['memory_mb'] > self.max_memory_per_process:
                should_cleanup = True
                reason = f"内存使用过高 ({proc['memory_mb']}MB > {self.max_memory_per_process}MB)"
            
            # 规则3：进程运行时间过长
            elif proc['age_seconds'] > self.process_timeout:
                should_cleanup = True
                reason = f"运行时间过长 ({proc['age_minutes']}分钟 > {self.process_timeout/60}分钟)"
            
            if should_cleanup:
                if not dry_run:
                    if self._kill_process(proc['pid']):
                        cleaned_count += 1
                        logger.info(f"  ✅ 清理进程 PID:{proc['pid']} - {reason}")
                    else:
                        logger.warning(f"  ❌ 清理进程 PID:{proc['pid']} 失败")
                else:
                    logger.info(f"  📋 计划清理进程 PID:{proc['pid']} - {reason}")
                    cleaned_count += 1
                
                cleanup_reasons.append(f"PID:{proc['pid']} - {reason}")
        
        result = {
            'cleaned': cleaned_count,
            'total': len(processes),
            'cleanup_reasons': cleanup_reasons
        }
        
        if cleaned_count > 0:
            logger.success(f"🧹 清理完成: {cleaned_count}/{len(processes)} 个进程")
        else:
            logger.info("✅ 无需清理，所有进程都在正常范围内")
        
        return result
    
    def _kill_process(self, pid: int) -> bool:
        """安全地终止进程"""
        try:
            proc = psutil.Process(pid)
            proc.terminate()  # 先尝试优雅终止
            
            # 等待进程终止
            try:
                proc.wait(timeout=5)
                return True
            except psutil.TimeoutExpired:
                # 如果优雅终止失败，强制终止
                proc.kill()
                proc.wait(timeout=3)
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            logger.debug(f"终止进程 {pid} 时出错: {e}")
            return False
        except Exception as e:
            logger.error(f"终止进程 {pid} 失败: {e}")
            return False
    
    def optimize_browser_settings(self) -> Dict[str, Any]:
        """优化浏览器启动参数"""
        optimized_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-images',  # 禁用图片加载，提升性能
            '--disable-javascript',  # 对于简单页面操作可以禁用JS
            '--memory-pressure-off',
            '--max_old_space_size=512',  # 限制V8内存使用
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding'
        ]
        
        browser_config = {
            'headless': True,
            'args': optimized_args,
            'timeout': 30000,  # 30秒超时
            'slow_mo': 0,  # 无延迟
        }
        
        return {
            'browser_config': browser_config,
            'optimizations': [
                '禁用图片和插件加载',
                '限制内存使用',
                '禁用后台处理',
                '优化超时设置'
            ]
        }
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """生成优化报告"""
        logger.info("📊 生成浏览器进程优化报告...")
        
        # 分析当前状态
        usage_analysis = self.analyze_browser_usage()
        
        # 获取优化建议
        optimization_settings = self.optimize_browser_settings()
        
        # 生成清理预览
        cleanup_preview = self.cleanup_excess_processes(dry_run=True)
        
        report = {
            'timestamp': datetime.now(timezone.utc),
            'current_usage': usage_analysis,
            'cleanup_preview': cleanup_preview,
            'optimization_settings': optimization_settings,
            'summary': {
                'total_processes': usage_analysis['total_processes'],
                'total_memory_mb': usage_analysis['total_memory_mb'],
                'processes_to_cleanup': cleanup_preview['cleaned'],
                'optimization_available': len(optimization_settings['optimizations'])
            }
        }
        
        # 输出报告
        logger.info("📋 浏览器进程优化报告:")
        logger.info(f"   当前进程数: {usage_analysis['total_processes']}")
        logger.info(f"   总内存使用: {usage_analysis['total_memory_mb']}MB")
        logger.info(f"   可清理进程: {cleanup_preview['cleaned']}")
        
        for recommendation in usage_analysis['recommendations']:
            logger.info(f"   {recommendation}")
        
        return report


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='浏览器进程优化工具')
    parser.add_argument('--analyze', action='store_true', help='分析当前浏览器使用情况')
    parser.add_argument('--cleanup', action='store_true', help='清理多余的浏览器进程')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不执行实际清理')
    parser.add_argument('--report', action='store_true', help='生成完整优化报告')
    
    args = parser.parse_args()
    
    optimizer = BrowserProcessOptimizer()
    
    if args.report or (not args.analyze and not args.cleanup):
        # 默认生成完整报告
        report = optimizer.generate_optimization_report()
        
        if args.cleanup and not args.dry_run:
            logger.info("\n🧹 执行实际清理...")
            cleanup_result = optimizer.cleanup_excess_processes(dry_run=False)
            logger.success(f"✅ 清理完成: {cleanup_result['cleaned']} 个进程")
    
    elif args.analyze:
        analysis = optimizer.analyze_browser_usage()
        logger.info(f"📊 浏览器使用分析: {analysis['summary']}")
    
    elif args.cleanup:
        cleanup_result = optimizer.cleanup_excess_processes(dry_run=args.dry_run)
        if args.dry_run:
            logger.info(f"📋 清理预览: 将清理 {cleanup_result['cleaned']} 个进程")
        else:
            logger.success(f"✅ 清理完成: {cleanup_result['cleaned']} 个进程")


if __name__ == "__main__":
    main()
