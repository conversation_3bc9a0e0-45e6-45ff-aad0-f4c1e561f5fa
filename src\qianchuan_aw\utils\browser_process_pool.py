#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器进程池
统一管理浏览器进程，提升上传效率
"""


import asyncio
import threading
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext
from qianchuan_aw.utils.logger import logger


class BrowserProcessPool:
    """浏览器进程池 - 统一管理浏览器进程，提升效率"""
    
    def __init__(self, min_instances: int = 2, max_instances: int = 5, idle_timeout: int = 300):
        self.min_instances = min_instances
        self.max_instances = max_instances
        self.idle_timeout = idle_timeout
        
        self.browsers: List[Dict[str, Any]] = []
        self.available_browsers: List[int] = []
        self.busy_browsers: List[int] = []
        self.lock = threading.Lock()
        self.playwright = None
        self.initialized = False
    
    async def initialize(self):
        """初始化浏览器进程池"""
        if self.initialized:
            return
        
        logger.info(f"🚀 初始化浏览器进程池 (min={self.min_instances}, max={self.max_instances})")
        
        self.playwright = await async_playwright().start()
        
        # 创建最小数量的浏览器实例
        for i in range(self.min_instances):
            await self._create_browser_instance()
        
        self.initialized = True
        logger.success(f"✅ 浏览器进程池初始化完成，创建了 {len(self.browsers)} 个实例")
    
    async def _create_browser_instance(self) -> int:
        """创建浏览器实例 - 增强错误处理"""
        browser = await self.playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--memory-pressure-off'
            ]
        )
        
        browser_info = {
            'id': len(self.browsers),
            'browser': browser,
            'created_at': datetime.now(),
            'last_used': datetime.now(),
            'usage_count': 0
        }
        
        self.browsers.append(browser_info)
        self.available_browsers.append(browser_info['id'])
        
        logger.debug(f"创建浏览器实例 #{browser_info['id']}")
        return browser_info['id']
    
    @asynccontextmanager
    async def get_browser_context(self, principal_name: str):
        """获取浏览器上下文"""
        browser_id = await self._acquire_browser()
        
        try:
            browser_info = self.browsers[browser_id]
            browser = browser_info['browser']
            
            # 创建新的上下文
            context = await browser.new_context()
            
            # 加载cookies等初始化操作
            await self._setup_browser_context(context, principal_name)
            
            # 更新使用统计
            browser_info['last_used'] = datetime.now()
            browser_info['usage_count'] += 1
            
            yield context
            
        finally:
            await context.close()
            await self._release_browser(browser_id)
    
    async def _acquire_browser(self) -> int:
        """获取可用的浏览器实例"""
        with self.lock:
            if self.available_browsers:
                browser_id = self.available_browsers.pop(0)
                self.busy_browsers.append(browser_id)
                return browser_id
            
            # 如果没有可用实例且未达到最大数量，创建新实例
            if len(self.browsers) < self.max_instances:
                browser_id = await self._create_browser_instance()
                self.available_browsers.remove(browser_id)
                self.busy_browsers.append(browser_id)
                return browser_id
            
            # 等待可用实例
            # 这里可以实现更复杂的等待逻辑
            raise RuntimeError("浏览器进程池已满，请稍后重试")
    
    async def _release_browser(self, browser_id: int):
        """释放浏览器实例"""
        with self.lock:
            if browser_id in self.busy_browsers:
                self.busy_browsers.remove(browser_id)
                self.available_browsers.append(browser_id)
    
    async def _setup_browser_context(self, context: BrowserContext, principal_name: str):
        """设置浏览器上下文"""
        # 这里实现cookies加载等初始化逻辑
        pass
    
    async def cleanup(self):
        """清理浏览器进程池"""
        logger.info("🧹 清理浏览器进程池...")
        
        for browser_info in self.browsers:
            try:
                await browser_info['browser'].close()
            except Exception as e:
                logger.error(f"关闭浏览器实例失败: {e}")
        
        if self.playwright:
            await self.playwright.stop()
        
        self.browsers.clear()
        self.available_browsers.clear()
        self.busy_browsers.clear()
        self.initialized = False
        
        logger.success("✅ 浏览器进程池清理完成")


# 全局浏览器进程池实例
_browser_pool = None

async def get_browser_pool() -> BrowserProcessPool:
    """获取全局浏览器进程池实例"""
    global _browser_pool
    
    if _browser_pool is None:
        _browser_pool = BrowserProcessPool()
        await _browser_pool.initialize()
    
    return _browser_pool
