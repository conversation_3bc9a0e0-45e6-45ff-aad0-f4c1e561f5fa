"""
千川申诉触发器 - 精准参数获取
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 通过精准的申诉流程触发获取真实有效的callback参数
依赖关系: 基于调试发现的智投星交互模式
清理条件: 功能被官方API替代时可删除

核心策略：
1. 不仅点击智投星图标，还要进入具体的申诉流程
2. 寻找"计划审核不通过"或"申诉"相关的按钮
3. 触发真正的申诉对话，产生callback请求
4. 从真实的callback请求中提取参数
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanAppealTrigger:
    """千川申诉触发器 - 精准参数获取"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的callback请求
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_callback_capture(self, page: Page):
        """设置callback请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                if 'copilot/api/v1/agw/card/callback' in url and request.method == 'POST':
                    logger.success(f"🎯 捕获到目标callback请求: {url}")
                    
                    try:
                        post_data = request.post_data
                        if post_data:
                            data = json.loads(post_data)
                            
                            # 记录完整的请求信息
                            callback_info = {
                                'url': url,
                                'method': request.method,
                                'data': data,
                                'headers': dict(request.headers),
                                'timestamp': time.time()
                            }
                            
                            self.callback_requests.append(callback_info)
                            logger.success(f"✅ 成功捕获callback参数: {list(data.keys())}")
                            
                    except Exception as e:
                        logger.warning(f"解析callback数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        page.on("request", handle_request)
    
    def trigger_appeal_flow(self, advertiser_id: str, plan_id: str = None) -> Dict[str, Any]:
        """触发申诉流程并获取参数"""
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 开始触发申诉流程获取参数...")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            
            # 设置callback捕获
            self._setup_callback_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url, wait_until="networkidle", timeout=60000)
            
            # 等待页面完全加载
            page.wait_for_timeout(5000)
            logger.info("✅ 页面加载完成")
            
            # 策略1: 寻找智投星并点击
            logger.info("🔍 策略1: 寻找智投星图标...")
            copilot_clicked = self._click_copilot_icon(page)
            
            if copilot_clicked:
                # 等待智投星界面出现
                page.wait_for_timeout(3000)
                
                # 策略2: 在智投星中寻找申诉相关选项
                logger.info("🔍 策略2: 寻找申诉相关选项...")
                appeal_triggered = self._trigger_appeal_in_copilot(page)
                
                if appeal_triggered:
                    # 等待callback请求
                    page.wait_for_timeout(5000)
                    
                    if self.callback_requests:
                        logger.success(f"🎉 成功获取 {len(self.callback_requests)} 个callback请求")
                        return self._extract_best_params()
            
            # 策略3: 直接寻找页面上的申诉按钮
            logger.info("🔍 策略3: 寻找页面申诉按钮...")
            direct_appeal = self._find_direct_appeal_buttons(page)
            
            if direct_appeal:
                page.wait_for_timeout(5000)
                if self.callback_requests:
                    logger.success(f"🎉 通过直接申诉获取 {len(self.callback_requests)} 个callback请求")
                    return self._extract_best_params()
            
            # 策略4: 模拟文本输入触发
            logger.info("🔍 策略4: 模拟申诉文本输入...")
            text_triggered = self._trigger_by_text_input(page)
            
            if text_triggered:
                page.wait_for_timeout(5000)
                if self.callback_requests:
                    logger.success(f"🎉 通过文本输入获取 {len(self.callback_requests)} 个callback请求")
                    return self._extract_best_params()
            
            logger.warning("⚠️ 所有策略都未能触发callback请求")
            return {"success": False, "error": "未能触发申诉流程"}
            
        except Exception as e:
            logger.error(f"❌ 触发申诉流程失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 清理资源
            try:
                if context:
                    context.close()
                if browser:
                    browser.close()
                if playwright:
                    playwright.stop()
            except:
                pass
    
    def _click_copilot_icon(self, page: Page) -> bool:
        """点击智投星图标"""
        try:
            copilot_selectors = [
                ".copilot-icon",
                "[class*='copilot-icon']",
                "button[class*='copilot']",
                "[data-testid*='copilot']",
                "[title*='智投星']",
                "[aria-label*='智投星']"
            ]
            
            for selector in copilot_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible() and element.is_enabled():
                            logger.info(f"✅ 找到智投星图标: {selector}")
                            element.click(timeout=5000)
                            logger.info("✅ 智投星图标点击成功")
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"点击智投星图标失败: {e}")
            return False
    
    def _trigger_appeal_in_copilot(self, page: Page) -> bool:
        """在智投星中触发申诉"""
        try:
            # 寻找申诉相关的按钮或选项
            appeal_keywords = [
                "申诉", "审核不通过", "计划审核", "结果申诉", 
                "审核离线工单", "工单", "申请", "反馈"
            ]
            
            # 方法1: 寻找包含申诉关键词的按钮
            for keyword in appeal_keywords:
                try:
                    buttons = page.locator(f"button:has-text('{keyword}')").all()
                    for button in buttons:
                        if button.is_visible() and button.is_enabled():
                            logger.info(f"✅ 找到申诉按钮: {keyword}")
                            button.click(timeout=3000)
                            page.wait_for_timeout(2000)
                            return True
                except:
                    continue
            
            # 方法2: 寻找输入框并输入申诉内容
            input_selectors = [
                "input[placeholder*='输入']",
                "textarea[placeholder*='输入']",
                ".copilot-input input",
                "[class*='input'][placeholder]"
            ]
            
            for selector in input_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到输入框: {selector}")
                            element.fill("计划审核不通过申诉")
                            page.wait_for_timeout(1000)
                            
                            # 尝试按回车或点击发送
                            element.press("Enter")
                            page.wait_for_timeout(2000)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"在智投星中触发申诉失败: {e}")
            return False
    
    def _find_direct_appeal_buttons(self, page: Page) -> bool:
        """寻找页面上的直接申诉按钮"""
        try:
            # 寻找页面上所有可能的申诉按钮
            appeal_selectors = [
                "button:has-text('申诉')",
                "button:has-text('审核不通过')",
                "button:has-text('申请')",
                "[class*='appeal']",
                "[data-testid*='appeal']",
                "a:has-text('申诉')"
            ]
            
            for selector in appeal_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible() and element.is_enabled():
                            logger.info(f"✅ 找到直接申诉按钮: {selector}")
                            element.click(timeout=3000)
                            page.wait_for_timeout(2000)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"寻找直接申诉按钮失败: {e}")
            return False
    
    def _trigger_by_text_input(self, page: Page) -> bool:
        """通过文本输入触发申诉"""
        try:
            # 在页面中寻找任何可能的输入框
            all_inputs = page.locator("input, textarea").all()
            
            for input_element in all_inputs:
                try:
                    if input_element.is_visible() and input_element.is_enabled():
                        # 尝试输入申诉相关内容
                        input_element.fill("计划审核不通过申诉")
                        page.wait_for_timeout(1000)
                        
                        # 尝试提交
                        input_element.press("Enter")
                        page.wait_for_timeout(2000)
                        
                        # 检查是否有callback请求
                        if self.callback_requests:
                            return True
                            
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"文本输入触发失败: {e}")
            return False
    
    def _extract_best_params(self) -> Dict[str, Any]:
        """从捕获的请求中提取最佳参数"""
        try:
            if not self.callback_requests:
                return {"success": False, "error": "没有捕获到callback请求"}
            
            # 选择最新的请求
            latest_request = self.callback_requests[-1]
            data = latest_request['data']
            
            # 提取关键参数
            params = {
                "success": True,
                "sessionId": data.get('sessionId'),
                "windowId": data.get('windowId'),
                "messageId": data.get('messageId'),
                "callValue": data.get('callValue'),
                "applicationCode": data.get('applicationCode', 'QC'),
                "callBackCode": data.get('callBackCode'),
                "captured_at": latest_request['timestamp'],
                "total_requests": len(self.callback_requests)
            }
            
            logger.success(f"✅ 成功提取参数: {list(params.keys())}")
            return params
            
        except Exception as e:
            logger.error(f"提取参数失败: {e}")
            return {"success": False, "error": str(e)}


def trigger_qianchuan_appeal_params(advertiser_id: str, principal_name: str = "缇萃百货", plan_id: str = None) -> Dict[str, Any]:
    """
    便捷函数：触发千川申诉流程获取真实参数
    
    Args:
        advertiser_id: 广告户ID
        principal_name: 主体名称
        plan_id: 计划ID（可选）
        
    Returns:
        包含真实callback参数的字典
    """
    trigger = QianchuanAppealTrigger(principal_name)
    return trigger.trigger_appeal_flow(advertiser_id, plan_id)


if __name__ == "__main__":
    # 测试申诉触发
    test_advertiser_id = "1836333804939273"
    
    print("🎯 测试千川申诉流程触发")
    print("=" * 50)
    
    try:
        result = trigger_qianchuan_appeal_params(test_advertiser_id)
        
        if result.get('success'):
            print("✅ 申诉流程触发成功:")
            for key, value in result.items():
                if key == 'callValue':
                    print(f"  {key}: {value[:100]}...")
                else:
                    print(f"  {key}: {value}")
        else:
            print(f"❌ 申诉流程触发失败: {result.get('error')}")
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_appeal_trigger import trigger_qianchuan_appeal_params")
    print("params = trigger_qianchuan_appeal_params('1836333804939273')")
