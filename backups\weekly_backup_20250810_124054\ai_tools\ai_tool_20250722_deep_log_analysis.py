#!/usr/bin/env python3
"""
千川工作流系统深度日志分析器
分析2025-07-22 21:00:00到当前时间的所有日志
"""

import sys
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class DeepLogAnalyzer:
    """深度日志分析器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流系统深度日志分析")
        logger.critical("=" * 60)
        self.analysis_start_time = datetime(2025, 7, 22, 21, 0, 0)
        self.current_time = datetime.now()
        self.log_dir = Path("logs")
        
        # 状态流转链路定义
        self.expected_status_flow = [
            MaterialStatus.NEW.value,
            MaterialStatus.PENDING_GROUPING.value, 
            MaterialStatus.UPLOADED_PENDING_PLAN.value,
            'creating_plan',
            MaterialStatus.TESTING_PENDING_REVIEW.value,
            MaterialStatus.APPROVED.value,  # 或 MaterialStatus.REJECTED.value
        ]
        
        # 分析结果存储
        self.analysis_results = {
            'workflow_integrity': {},
            'system_stability': {},
            'api_frequency': {},
            'specific_issues': {},
            'overall_health': 'unknown'
        }
    
    def read_logs_in_timerange(self):
        """读取指定时间范围内的日志"""
        logger.critical("📊 读取指定时间范围内的日志")
        logger.critical("=" * 60)
        
        target_logs = []
        
        # 需要检查的日志文件
        log_files_to_check = [
            f"app_{self.analysis_start_time.strftime('%Y-%m-%d')}.log",
            f"app_{self.current_time.strftime('%Y-%m-%d')}.log"
        ]
        
        # 如果跨天，添加中间的日期
        current_date = self.analysis_start_time.date()
        end_date = self.current_time.date()
        while current_date <= end_date:
            log_file = f"app_{current_date.strftime('%Y-%m-%d')}.log"
            if log_file not in log_files_to_check:
                log_files_to_check.append(log_file)
            current_date += timedelta(days=1)
        
        logger.critical(f"📊 检查日志文件: {log_files_to_check}")
        
        for log_file in log_files_to_check:
            log_path = self.log_dir / log_file
            if not log_path.exists():
                logger.warning(f"日志文件不存在: {log_path}")
                continue
            
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line in lines:
                    # 提取时间戳
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        try:
                            log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if self.analysis_start_time <= log_time <= self.current_time:
                                target_logs.append((log_time, line.strip()))
                        except ValueError:
                            continue
                            
            except Exception as e:
                logger.error(f"读取日志文件 {log_path} 失败: {e}")
        
        # 按时间排序
        target_logs.sort(key=lambda x: x[0])
        
        logger.critical(f"📊 读取日志统计:")
        logger.critical(f"  分析时间范围: {self.analysis_start_time} - {self.current_time}")
        logger.critical(f"  总日志行数: {len(target_logs)}")
        
        return target_logs
    
    def analyze_workflow_integrity(self, log_lines):
        """分析工作流完整性"""
        logger.critical("\n🔍 1. 工作流完整性验证")
        logger.critical("=" * 60)
        
        # 状态流转统计
        status_transitions = defaultdict(int)
        status_durations = defaultdict(list)
        status_anomalies = []
        
        # 提取状态变更日志
        status_pattern = r'状态.*?从\s*(\w+)\s*变更为\s*(\w+)|状态.*?更新.*?(\w+)\s*→\s*(\w+)|(\w+)\s*→\s*(\w+)'
        
        for log_time, line in log_lines:
            # 查找状态变更
            status_matches = re.findall(status_pattern, line, re.IGNORECASE)
            for match in status_matches:
                # 处理不同的匹配组
                if match[0] and match[1]:  # 从 X 变更为 Y
                    from_status, to_status = match[0], match[1]
                elif match[2] and match[3]:  # X → Y
                    from_status, to_status = match[2], match[3]
                elif match[4] and match[5]:  # X → Y
                    from_status, to_status = match[4], match[5]
                else:
                    continue
                
                transition = f"{from_status} → {to_status}"
                status_transitions[transition] += 1
                
                # 检查是否为异常状态跳跃
                if from_status in self.expected_status_flow and to_status in self.expected_status_flow:
                    from_idx = self.expected_status_flow.index(from_status)
                    to_idx = self.expected_status_flow.index(to_status)
                    
                    if to_idx < from_idx:  # 状态回退
                        status_anomalies.append({
                            'type': 'status_rollback',
                            'transition': transition,
                            'time': log_time,
                            'line': line
                        })
                    elif to_idx - from_idx > 1:  # 状态跳跃
                        status_anomalies.append({
                            'type': 'status_jump',
                            'transition': transition,
                            'time': log_time,
                            'line': line
                        })
        
        # 分析处理时间
        processing_times = self.analyze_processing_times(log_lines)
        
        logger.critical("📊 状态流转分析:")
        logger.critical(f"  发现状态变更: {len(status_transitions)} 种")
        logger.critical(f"  状态异常: {len(status_anomalies)} 个")
        
        if status_transitions:
            logger.critical("  主要状态变更:")
            for transition, count in sorted(status_transitions.items(), key=lambda x: x[1], reverse=True)[:10]:
                logger.critical(f"    {transition}: {count} 次")
        
        if status_anomalies:
            logger.critical("  状态异常详情:")
            for anomaly in status_anomalies[:5]:  # 显示前5个
                logger.critical(f"    {anomaly['type']}: {anomaly['transition']} at {anomaly['time']}")
        
        self.analysis_results['workflow_integrity'] = {
            'status_transitions': dict(status_transitions),
            'status_anomalies': status_anomalies,
            'processing_times': processing_times,
            'integrity_score': max(0, 100 - len(status_anomalies) * 5)
        }
    
    def analyze_processing_times(self, log_lines):
        """分析处理时间"""
        processing_times = defaultdict(list)
        
        # 查找任务开始和结束的日志
        task_starts = {}
        task_ends = {}
        
        for log_time, line in log_lines:
            if 'Task Start' in line or '任务开始' in line:
                task_match = re.search(r'(Task Start|任务开始).*?(\w+)', line)
                if task_match:
                    task_type = task_match.group(2)
                    task_starts[task_type] = log_time
            
            elif 'Task End' in line or '任务结束' in line:
                task_match = re.search(r'(Task End|任务结束).*?(\w+)', line)
                if task_match:
                    task_type = task_match.group(2)
                    if task_type in task_starts:
                        duration = (log_time - task_starts[task_type]).total_seconds()
                        processing_times[task_type].append(duration)
                        del task_starts[task_type]
        
        return dict(processing_times)
    
    def analyze_system_stability(self, log_lines):
        """分析系统稳定性"""
        logger.critical("\n🔍 2. 系统稳定性评估")
        logger.critical("=" * 60)
        
        error_stats = {
            'ERROR': 0,
            'WARNING': 0,
            'CRITICAL': 0,
            'INFO': 0
        }
        
        error_patterns = defaultdict(int)
        repeated_errors = defaultdict(list)
        
        for log_time, line in log_lines:
            # 统计日志级别
            for level in error_stats.keys():
                if f' {level} ' in line:
                    error_stats[level] += 1
                    break
            
            # 提取错误模式
            if 'ERROR' in line or 'CRITICAL' in line:
                # 提取错误的关键部分（去除时间戳和变量）
                error_pattern = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', line)
                error_pattern = re.sub(r'\d+', 'N', error_pattern)  # 替换数字
                error_pattern = error_pattern[:100]  # 限制长度
                
                error_patterns[error_pattern] += 1
                repeated_errors[error_pattern].append(log_time)
        
        # 计算错误率
        total_logs = len(log_lines)
        error_rate = (error_stats['ERROR'] + error_stats['CRITICAL']) / total_logs if total_logs > 0 else 0
        
        # 系统健康评分
        health_score = 100
        if error_rate > 0.1:  # 错误率超过10%
            health_score -= 40
        elif error_rate > 0.05:  # 错误率超过5%
            health_score -= 20
        
        if error_stats['CRITICAL'] > 10:
            health_score -= 30
        
        logger.critical("📊 系统稳定性统计:")
        logger.critical(f"  总日志数: {total_logs}")
        logger.critical(f"  ERROR: {error_stats['ERROR']} 条")
        logger.critical(f"  WARNING: {error_stats['WARNING']} 条")
        logger.critical(f"  CRITICAL: {error_stats['CRITICAL']} 条")
        logger.critical(f"  错误率: {error_rate:.2%}")
        logger.critical(f"  健康评分: {health_score}/100")
        
        if error_patterns:
            logger.critical("  主要错误模式:")
            for pattern, count in sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.critical(f"    {count}次: {pattern[:80]}...")
        
        self.analysis_results['system_stability'] = {
            'error_stats': error_stats,
            'error_rate': error_rate,
            'health_score': health_score,
            'error_patterns': dict(error_patterns),
            'repeated_errors': dict(repeated_errors)
        }
    
    def analyze_api_frequency(self, log_lines):
        """分析API调用频率"""
        logger.critical("\n🔍 3. API调用频率分析")
        logger.critical("=" * 60)
        
        api_calls = defaultdict(list)
        too_many_requests = []
        security_calls = []
        
        for log_time, line in log_lines:
            # 查找API调用
            if 'API' in line or 'api' in line.lower():
                # 提取API接口名
                api_match = re.search(r'(GET|POST|PUT|DELETE)\s+([^\s]+)|调用.*?(\w+).*?接口', line)
                if api_match:
                    if api_match.group(2):
                        api_name = api_match.group(2)
                    elif api_match.group(3):
                        api_name = api_match.group(3)
                    else:
                        api_name = 'unknown'
                    
                    api_calls[api_name].append(log_time)
                    
                    # 特别关注security接口
                    if 'security' in api_name.lower() or 'score_disposal_info' in line:
                        security_calls.append(log_time)
            
            # 查找频率限制错误
            if 'Too many requests' in line or '请求过于频繁' in line:
                too_many_requests.append({
                    'time': log_time,
                    'line': line
                })
        
        # 计算API调用频率
        api_frequencies = {}
        for api_name, call_times in api_calls.items():
            if len(call_times) > 1:
                time_span = (call_times[-1] - call_times[0]).total_seconds()
                frequency = len(call_times) / time_span if time_span > 0 else 0
                api_frequencies[api_name] = {
                    'total_calls': len(call_times),
                    'frequency_per_second': frequency,
                    'frequency_per_minute': frequency * 60
                }
        
        # 分析security接口调用模式
        security_frequency = 0
        if len(security_calls) > 1:
            security_span = (security_calls[-1] - security_calls[0]).total_seconds()
            security_frequency = len(security_calls) / security_span if security_span > 0 else 0
        
        logger.critical("📊 API调用频率分析:")
        logger.critical(f"  发现API接口: {len(api_calls)} 个")
        logger.critical(f"  频率限制错误: {len(too_many_requests)} 次")
        logger.critical(f"  Security接口调用: {len(security_calls)} 次")
        logger.critical(f"  Security接口频率: {security_frequency:.2f} 次/秒")
        
        if api_frequencies:
            logger.critical("  高频API接口:")
            sorted_apis = sorted(api_frequencies.items(), key=lambda x: x[1]['frequency_per_second'], reverse=True)
            for api_name, freq_data in sorted_apis[:5]:
                logger.critical(f"    {api_name}: {freq_data['frequency_per_second']:.2f} 次/秒")
        
        if too_many_requests:
            logger.critical("  频率限制错误详情:")
            for error in too_many_requests[:3]:
                logger.critical(f"    {error['time']}: {error['line'][:100]}...")
        
        self.analysis_results['api_frequency'] = {
            'api_calls': dict(api_calls),
            'api_frequencies': api_frequencies,
            'too_many_requests': too_many_requests,
            'security_calls': security_calls,
            'security_frequency': security_frequency
        }
    
    def analyze_specific_issues(self, log_lines):
        """分析具体问题"""
        logger.critical("\n🔍 4. 具体问题定位")
        logger.critical("=" * 60)
        
        specific_issues = {
            'duplicate_check_failures': [],
            'plan_creation_failures': [],
            'appeal_process_issues': [],
            'database_sync_issues': [],
            'resource_leaks': []
        }
        
        for log_time, line in log_lines:
            # 重复检查问题
            if '重复' in line and ('检查' in line or '创建' in line):
                specific_issues['duplicate_check_failures'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 计划创建失败
            if '计划创建' in line and ('失败' in line or 'ERROR' in line):
                specific_issues['plan_creation_failures'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 提审流程问题
            if '提审' in line and ('失败' in line or 'ERROR' in line or '超时' in line):
                specific_issues['appeal_process_issues'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 数据库同步问题
            if '数据库' in line and ('同步' in line or '不一致' in line):
                specific_issues['database_sync_issues'].append({
                    'time': log_time,
                    'line': line
                })
            
            # 资源泄露
            if '内存' in line or '连接' in line or '超时' in line:
                if 'ERROR' in line or 'WARNING' in line:
                    specific_issues['resource_leaks'].append({
                        'time': log_time,
                        'line': line
                    })
        
        logger.critical("📊 具体问题统计:")
        for issue_type, issues in specific_issues.items():
            logger.critical(f"  {issue_type}: {len(issues)} 个")
            if issues:
                logger.critical(f"    最新: {issues[-1]['line'][:80]}...")
        
        self.analysis_results['specific_issues'] = specific_issues
    
    def generate_optimization_recommendations(self):
        """生成优化建议"""
        logger.critical("\n💡 优化建议")
        logger.critical("=" * 60)
        
        recommendations = []
        
        # 基于API频率分析的建议
        api_analysis = self.analysis_results['api_frequency']
        if api_analysis['security_frequency'] > 0.1:  # 超过0.1次/秒
            recommendations.append({
                'priority': 'HIGH',
                'category': 'API频率优化',
                'issue': f"Security接口调用频率过高: {api_analysis['security_frequency']:.2f} 次/秒",
                'solution': '建议改为每账户1小时调用一次，避免频率限制',
                'implementation': '''
# 在scheduler.py中添加频率控制
last_security_check = {}
def should_check_security(account_id):
    now = datetime.now()
    if account_id not in last_security_check:
        last_security_check[account_id] = now
        return True
    
    time_diff = (now - last_security_check[account_id]).total_seconds()
    if time_diff >= 3600:  # 1小时
        last_security_check[account_id] = now
        return True
    return False
'''
            })
        
        # 基于系统稳定性的建议
        stability = self.analysis_results['system_stability']
        if stability['error_rate'] > 0.05:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': '错误处理优化',
                'issue': f"系统错误率过高: {stability['error_rate']:.2%}",
                'solution': '增强错误处理和重试机制',
                'implementation': '添加指数退避重试，改进异常捕获'
            })
        
        # 基于工作流完整性的建议
        workflow = self.analysis_results['workflow_integrity']
        if workflow['integrity_score'] < 80:
            recommendations.append({
                'priority': 'HIGH',
                'category': '工作流优化',
                'issue': f"工作流完整性评分低: {workflow['integrity_score']}/100",
                'solution': '修复状态流转异常，确保状态机正确运行',
                'implementation': '添加状态验证和自动修复机制'
            })
        
        logger.critical("🎯 优化建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.critical(f"  {i}. [{rec['priority']}] {rec['category']}")
            logger.critical(f"     问题: {rec['issue']}")
            logger.critical(f"     解决方案: {rec['solution']}")
        
        return recommendations
    
    def evaluate_overall_health(self):
        """评估整体健康状态"""
        logger.critical("\n🏥 整体健康状态评估")
        logger.critical("=" * 60)
        
        # 计算综合评分
        stability_score = self.analysis_results['system_stability']['health_score']
        workflow_score = self.analysis_results['workflow_integrity']['integrity_score']
        
        # API频率评分
        api_score = 100
        if self.analysis_results['api_frequency']['security_frequency'] > 0.1:
            api_score -= 30
        if len(self.analysis_results['api_frequency']['too_many_requests']) > 10:
            api_score -= 20
        
        # 综合评分
        overall_score = (stability_score + workflow_score + api_score) / 3
        
        # 确定健康状态
        if overall_score >= 80:
            health_status = '健康'
            health_icon = '✅'
        elif overall_score >= 60:
            health_status = '警告'
            health_icon = '⚠️'
        else:
            health_status = '严重'
            health_icon = '🚨'
        
        logger.critical(f"📊 综合评估结果:")
        logger.critical(f"  系统稳定性: {stability_score}/100")
        logger.critical(f"  工作流完整性: {workflow_score}/100")
        logger.critical(f"  API调用合规性: {api_score}/100")
        logger.critical(f"  综合评分: {overall_score:.1f}/100")
        logger.critical(f"  整体状态: {health_icon} {health_status}")
        
        self.analysis_results['overall_health'] = {
            'score': overall_score,
            'status': health_status,
            'components': {
                'stability': stability_score,
                'workflow': workflow_score,
                'api': api_score
            }
        }
        
        return health_status

def main():
    """主分析函数"""
    try:
        analyzer = DeepLogAnalyzer()
        
        # 1. 读取指定时间范围的日志
        log_lines = analyzer.read_logs_in_timerange()
        
        if not log_lines:
            logger.error("❌ 没有找到指定时间范围内的日志")
            return False
        
        # 2. 工作流完整性验证
        analyzer.analyze_workflow_integrity(log_lines)
        
        # 3. 系统稳定性评估
        analyzer.analyze_system_stability(log_lines)
        
        # 4. API调用频率分析
        analyzer.analyze_api_frequency(log_lines)
        
        # 5. 具体问题定位
        analyzer.analyze_specific_issues(log_lines)
        
        # 6. 生成优化建议
        recommendations = analyzer.generate_optimization_recommendations()
        
        # 7. 评估整体健康状态
        health_status = analyzer.evaluate_overall_health()
        
        logger.critical(f"\n🎉 千川工作流系统深度日志分析完成!")
        logger.critical(f"整体状态: {health_status}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 深度日志分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
