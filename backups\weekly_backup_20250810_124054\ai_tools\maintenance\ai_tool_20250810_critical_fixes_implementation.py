#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实施千川自动化系统Critical级别问题的修复
清理条件: 修复完成后可归档，但建议保留作为参考
"""

import os
import sys
import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional
from contextlib import contextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount
from qianchuan_aw.utils.db_utils import database_session


class CriticalFixesImplementation:
    """Critical级别问题修复实施器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.fixes_failed = []
        
    def implement_all_critical_fixes(self) -> Dict[str, Any]:
        """实施所有Critical级别修复"""
        logger.info("🚨 开始实施Critical级别问题修复...")
        
        results = {
            'total_fixes': 3,
            'successful_fixes': 0,
            'failed_fixes': 0,
            'fixes_applied': [],
            'fixes_failed': []
        }
        
        # C1: 修复原子状态管理器事务边界
        try:
            self._fix_atomic_state_manager()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('C1: 原子状态管理器事务边界修复')
        except Exception as e:
            logger.error(f"C1修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'C1: {str(e)}')
        
        # C2: 修复批量任务事务管理
        try:
            self._fix_batch_task_transactions()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('C2: 批量任务事务管理修复')
        except Exception as e:
            logger.error(f"C2修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'C2: {str(e)}')
        
        # C3: 完善素材唯一性检查
        try:
            self._fix_material_uniqueness_check()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('C3: 素材唯一性检查完善')
        except Exception as e:
            logger.error(f"C3修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'C3: {str(e)}')
        
        logger.info(f"✅ Critical修复完成: {results['successful_fixes']}/{results['total_fixes']}")
        return results
    
    def _fix_atomic_state_manager(self):
        """修复原子状态管理器事务边界问题"""
        logger.info("🔧 修复原子状态管理器事务边界...")
        
        # 创建修复后的原子状态管理器
        fixed_atomic_manager_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子状态管理器 - 修复版本
确保状态转换的原子性，防止状态不一致
"""

import os
from datetime import datetime, timezone
from contextlib import contextmanager
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.logger import logger


class AtomicStateManager:
    """原子状态管理器 - 确保状态转换的原子性"""
    
    def __init__(self, db_session):
        self.db = db_session
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str):
        """原子状态转换上下文管理器 - 修复版本"""
        creative = None
        savepoint = None
        
        try:
            # 创建保存点
            savepoint = self.db.begin_nested()
            
            # 获取并锁定记录
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id,
                LocalCreative.status == from_status
            ).with_for_update().first()
            
            if not creative:
                raise ValueError(f"素材 {creative_id} 不存在或状态不匹配 (期望: {from_status})")
            
            # 更新状态
            original_status = creative.status
            creative.status = to_status
            creative.updated_at = datetime.now(timezone.utc)
            
            # 提供给调用者使用
            yield creative
            
            # 只有在yield成功完成后才提交保存点
            savepoint.commit()
            
            # 最后提交主事务
            self.db.commit()
            
            logger.debug(f"✅ 状态转换成功: {creative_id} {from_status} → {to_status}")
            
        except Exception as e:
            # 回滚到保存点
            if savepoint:
                savepoint.rollback()
            
            # 恢复原始状态
            if creative:
                creative.status = original_status if 'original_status' in locals() else from_status
            
            # 回滚主事务
            self.db.rollback()
            
            logger.error(f"❌ 状态转换失败: {creative_id} {from_status} → {to_status}: {e}")
            raise
    
    @contextmanager
    def batch_atomic_transition(self, transitions: List[Dict[str, Any]]):
        """批量原子状态转换"""
        savepoint = None
        updated_creatives = []
        
        try:
            savepoint = self.db.begin_nested()
            
            for transition in transitions:
                creative_id = transition['creative_id']
                from_status = transition['from_status']
                to_status = transition['to_status']
                
                creative = self.db.query(LocalCreative).filter(
                    LocalCreative.id == creative_id,
                    LocalCreative.status == from_status
                ).with_for_update().first()
                
                if not creative:
                    raise ValueError(f"素材 {creative_id} 不存在或状态不匹配")
                
                creative.status = to_status
                creative.updated_at = datetime.now(timezone.utc)
                updated_creatives.append(creative)
            
            yield updated_creatives
            
            savepoint.commit()
            self.db.commit()
            
            logger.info(f"✅ 批量状态转换成功: {len(transitions)} 个素材")
            
        except Exception as e:
            if savepoint:
                savepoint.rollback()
            self.db.rollback()
            
            logger.error(f"❌ 批量状态转换失败: {e}")
            raise
'''
        
        # 备份原文件
        original_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'atomic_state_manager.py'
        backup_file = original_file.with_suffix('.py.backup_20250810_critical_fix')
        
        if original_file.exists():
            import shutil
            shutil.copy2(original_file, backup_file)
            logger.info(f"📁 原文件已备份到: {backup_file}")
        
        # 写入修复后的代码
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(fixed_atomic_manager_code)
        
        logger.success("✅ 原子状态管理器事务边界修复完成")
    
    def _fix_batch_task_transactions(self):
        """修复批量任务事务管理问题"""
        logger.info("🔧 修复批量任务事务管理...")
        
        # 创建增强的批量任务处理器
        enhanced_batch_processor_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强批量任务处理器
提供事务安全的批量操作
"""

from typing import List, Dict, Any, Callable
from contextlib import contextmanager
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager


class EnhancedBatchProcessor:
    """增强批量任务处理器 - 提供事务安全保证"""
    
    def __init__(self, db_session):
        self.db = db_session
        self.state_manager = AtomicStateManager(db_session)
    
    @contextmanager
    def batch_transaction(self, batch_name: str):
        """批量事务上下文管理器"""
        logger.info(f"🚀 开始批量事务: {batch_name}")
        
        savepoint = None
        try:
            savepoint = self.db.begin_nested()
            
            yield self
            
            savepoint.commit()
            self.db.commit()
            
            logger.success(f"✅ 批量事务完成: {batch_name}")
            
        except Exception as e:
            if savepoint:
                savepoint.rollback()
            self.db.rollback()
            
            logger.error(f"❌ 批量事务失败: {batch_name}, 错误: {e}")
            raise
    
    def process_batch_with_rollback(self, items: List[Any], processor: Callable, batch_size: int = 10):
        """带回滚保护的批量处理"""
        total_processed = 0
        total_failed = 0
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            try:
                with self.batch_transaction(f"batch_{i//batch_size + 1}"):
                    for item in batch:
                        processor(item)
                        total_processed += 1
                        
            except Exception as e:
                total_failed += len(batch)
                logger.error(f"批次 {i//batch_size + 1} 处理失败: {e}")
                continue
        
        return {
            'total_processed': total_processed,
            'total_failed': total_failed,
            'success_rate': total_processed / len(items) if items else 0
        }
'''
        
        # 保存增强批量处理器
        enhanced_processor_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'enhanced_batch_processor.py'
        with open(enhanced_processor_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_batch_processor_code)
        
        logger.success("✅ 增强批量任务处理器创建完成")
    
    def _fix_material_uniqueness_check(self):
        """完善素材唯一性检查"""
        logger.info("🔧 完善素材唯一性检查...")
        
        # 创建增强的唯一性检查器
        uniqueness_checker_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材唯一性检查器 - 增强版本
确保严格执行测试视频工作流铁律
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount, PlatformCreative


class MaterialUniquenessChecker:
    """素材唯一性检查器 - 确保测试视频全局唯一性"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def check_test_video_global_uniqueness(self, file_hash: str, account_type: str) -> Dict[str, Any]:
        """检查测试视频全局唯一性 - 铁律2实施"""
        
        if account_type != 'TEST':
            return {'is_unique': True, 'reason': 'non_test_account'}
        
        try:
            # 查询是否已存在相同file_hash的测试计划
            existing_test_plans = self.db.execute(text("""
                SELECT 
                    c.campaign_id_qc,
                    aa.name as account_name,
                    lc.filename,
                    c.created_at
                FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                JOIN local_creatives lc ON pc.local_creative_id = lc.id
                WHERE aa.account_type = 'TEST'
                AND lc.file_hash = :file_hash
                LIMIT 1
            """), {'file_hash': file_hash}).fetchone()
            
            if existing_test_plans:
                return {
                    'is_unique': False,
                    'reason': 'duplicate_test_video',
                    'existing_plan': {
                        'campaign_id': existing_test_plans.campaign_id_qc,
                        'account_name': existing_test_plans.account_name,
                        'filename': existing_test_plans.filename,
                        'created_at': existing_test_plans.created_at.isoformat()
                    },
                    'violation_rule': '铁律2：素材唯一性测试约束'
                }
            
            return {'is_unique': True, 'reason': 'no_duplicate_found'}
            
        except Exception as e:
            logger.error(f"唯一性检查失败: {e}")
            # 安全起见，检查失败时返回不唯一，阻止创建
            return {
                'is_unique': False,
                'reason': 'check_failed',
                'error': str(e)
            }
    
    def validate_account_operation_permissions(self, account: AdAccount, operation: str) -> Dict[str, Any]:
        """验证账户操作权限 - 铁律3实施"""
        
        # 严禁deleted账户的任何操作
        if account.status == 'deleted':
            return {
                'is_allowed': False,
                'reason': 'deleted_account_forbidden',
                'violation_rule': '铁律3：账户状态操作权限 - deleted账户严禁任何操作'
            }
        
        # temporarily_blocked账户的操作限制
        if account.status == 'temporarily_blocked':
            forbidden_operations = ['upload', 'create_plan']
            allowed_operations = ['submit_plan', 'harvest']
            
            if operation in forbidden_operations:
                return {
                    'is_allowed': False,
                    'reason': 'temporarily_blocked_operation_forbidden',
                    'violation_rule': f'铁律3：temporarily_blocked账户禁止{operation}操作'
                }
            elif operation in allowed_operations:
                return {
                    'is_allowed': True,
                    'reason': 'temporarily_blocked_operation_allowed'
                }
        
        # active账户允许所有操作
        if account.status == 'active':
            return {
                'is_allowed': True,
                'reason': 'active_account_full_permission'
            }
        
        # 未知状态，安全起见拒绝
        return {
            'is_allowed': False,
            'reason': 'unknown_account_status',
            'account_status': account.status
        }
'''
        
        # 保存唯一性检查器
        uniqueness_checker_file = project_root / 'src' / 'qianchuan_aw' / 'utils' / 'material_uniqueness_checker.py'
        with open(uniqueness_checker_file, 'w', encoding='utf-8') as f:
            f.write(uniqueness_checker_code)
        
        logger.success("✅ 素材唯一性检查器创建完成")


def main():
    """主函数"""
    fixer = CriticalFixesImplementation()
    
    try:
        results = fixer.implement_all_critical_fixes()
        
        print("\n" + "="*60)
        print("🚨 Critical级别问题修复结果")
        print("="*60)
        print(f"总修复项目: {results['total_fixes']}")
        print(f"成功修复: {results['successful_fixes']}")
        print(f"修复失败: {results['failed_fixes']}")
        
        if results['fixes_applied']:
            print("\n✅ 成功修复的问题:")
            for fix in results['fixes_applied']:
                print(f"  - {fix}")
        
        if results['fixes_failed']:
            print("\n❌ 修复失败的问题:")
            for fix in results['fixes_failed']:
                print(f"  - {fix}")
        
        print("\n📋 后续步骤:")
        print("1. 重启Celery服务以加载新的代码")
        print("2. 运行集成测试验证修复效果")
        print("3. 监控系统运行状态")
        print("4. 继续修复High级别问题")
        
    except Exception as e:
        logger.error(f"修复过程发生错误: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
