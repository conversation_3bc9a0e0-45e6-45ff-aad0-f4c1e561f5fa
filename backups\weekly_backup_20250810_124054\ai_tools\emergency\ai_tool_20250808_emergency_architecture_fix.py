#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 7天
创建目的: 紧急修复架构混乱和模块阻塞问题
清理条件: 修复完成后删除
"""

import os
import sys
import psutil
import time
from datetime import datetime, timezone
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AdAccount, Campaign


class EmergencyArchitectureFixer:
    """紧急架构修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.critical_issues = []
    
    def run_emergency_fix(self) -> Dict[str, Any]:
        """运行紧急修复"""
        logger.error("🚨 检测到严重架构问题，启动紧急修复...")
        logger.error("="*80)
        
        try:
            # 1. 立即停止所有浏览器进程
            self._emergency_browser_cleanup()
            
            # 2. 验证测试账户限制
            self._verify_test_account_limits()
            
            # 3. 检查模块独立性
            self._check_module_independence()
            
            # 4. 修复调度器冲突
            self._fix_scheduler_conflicts()
            
            # 5. 生成修复报告
            return self._generate_fix_report()
            
        except Exception as e:
            logger.error(f"❌ 紧急修复失败: {e}", exc_info=True)
            self.critical_issues.append(f"紧急修复执行失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _emergency_browser_cleanup(self):
        """紧急浏览器进程清理"""
        logger.error("🧹 紧急清理浏览器进程...")
        
        killed_count = 0
        browser_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info['name'].lower()
                    
                    # 检查是否为我们启动的浏览器进程
                    if any(browser in name for browser in ['chrome', 'chromium']):
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage']):
                            memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                            browser_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'memory_mb': round(memory_mb, 1)
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if browser_processes:
                logger.error(f"🚨 发现 {len(browser_processes)} 个浏览器进程，立即清理...")
                
                for proc_info in browser_processes:
                    try:
                        proc = psutil.Process(proc_info['pid'])
                        proc.terminate()
                        
                        # 等待进程终止
                        try:
                            proc.wait(timeout=3)
                            killed_count += 1
                            logger.info(f"  ✅ 终止进程 PID:{proc_info['pid']} ({proc_info['memory_mb']}MB)")
                        except psutil.TimeoutExpired:
                            proc.kill()
                            proc.wait(timeout=2)
                            killed_count += 1
                            logger.info(f"  ⚡ 强制终止进程 PID:{proc_info['pid']}")
                            
                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        logger.warning(f"  ⚠️ 无法终止进程 PID:{proc_info['pid']}: {e}")
                
                self.fixes_applied.append(f"清理了 {killed_count} 个浏览器进程")
                logger.success(f"✅ 浏览器进程清理完成: {killed_count}/{len(browser_processes)}")
            else:
                logger.info("✅ 当前无需清理的浏览器进程")
                
        except Exception as e:
            logger.error(f"❌ 浏览器进程清理失败: {e}")
            self.critical_issues.append(f"浏览器进程清理失败: {e}")
    
    def _verify_test_account_limits(self):
        """验证测试账户限制"""
        logger.error("🛡️ 验证测试账户限制...")
        
        try:
            with SessionLocal() as db:
                # 检查所有账户类型
                all_accounts = db.query(AdAccount).all()
                test_accounts = [acc for acc in all_accounts if acc.account_type == 'TEST']
                prod_accounts = [acc for acc in all_accounts if acc.account_type != 'TEST']
                
                logger.info(f"📊 账户统计:")
                logger.info(f"   总账户数: {len(all_accounts)}")
                logger.info(f"   测试账户: {len(test_accounts)}")
                logger.info(f"   生产账户: {len(prod_accounts)}")
                
                # 检查是否有生产账户的计划在提审
                prod_campaigns_in_appeal = db.query(Campaign).join(AdAccount).filter(
                    AdAccount.account_type != 'TEST',
                    Campaign.appeal_status.isnot(None)
                ).count()
                
                if prod_campaigns_in_appeal > 0:
                    self.critical_issues.append(f"发现 {prod_campaigns_in_appeal} 个生产账户计划在提审状态")
                    logger.error(f"🚨 严重违规: {prod_campaigns_in_appeal} 个生产账户计划在提审状态！")
                    
                    # 立即重置生产账户的提审状态
                    reset_count = db.query(Campaign).join(AdAccount).filter(
                        AdAccount.account_type != 'TEST',
                        Campaign.appeal_status.isnot(None)
                    ).update({
                        Campaign.appeal_status: None,
                        Campaign.appeal_attempt_count: 0,
                        Campaign.first_appeal_at: None,
                        Campaign.last_appeal_at: None
                    }, synchronize_session=False)
                    
                    db.commit()
                    
                    self.fixes_applied.append(f"重置了 {reset_count} 个生产账户计划的提审状态")
                    logger.success(f"✅ 已重置 {reset_count} 个生产账户计划的提审状态")
                else:
                    logger.success("✅ 测试账户限制正常，无生产账户计划在提审")
                
        except Exception as e:
            logger.error(f"❌ 测试账户限制验证失败: {e}")
            self.critical_issues.append(f"测试账户限制验证失败: {e}")
    
    def _check_module_independence(self):
        """检查模块独立性"""
        logger.error("🔍 检查模块独立性...")
        
        # 检查是否有长时间运行的任务阻塞其他模块
        try:
            # 这里可以检查Celery任务状态、数据库锁等
            # 暂时记录检查项
            independence_issues = []
            
            # 检查数据库连接池
            try:
                with SessionLocal() as db:
                    # 简单查询测试数据库连接
                    db.execute("SELECT 1").fetchone()
                logger.success("✅ 数据库连接正常")
            except Exception as e:
                independence_issues.append(f"数据库连接异常: {e}")
            
            # 检查配置文件
            config_path = os.path.join(project_root, 'config', 'settings.yml')
            if os.path.exists(config_path):
                logger.success("✅ 配置文件存在")
            else:
                independence_issues.append("配置文件不存在")
            
            if independence_issues:
                self.critical_issues.extend(independence_issues)
                logger.error(f"🚨 发现 {len(independence_issues)} 个模块独立性问题")
            else:
                logger.success("✅ 模块独立性检查通过")
                
        except Exception as e:
            logger.error(f"❌ 模块独立性检查失败: {e}")
            self.critical_issues.append(f"模块独立性检查失败: {e}")
    
    def _fix_scheduler_conflicts(self):
        """修复调度器冲突"""
        logger.error("🔧 检查调度器冲突...")
        
        try:
            # 检查是否有多个Python进程运行main.py
            main_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    if proc_info['name'].lower() in ['python.exe', 'python']:
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if 'main.py' in cmdline:
                            main_processes.append({
                                'pid': proc_info['pid'],
                                'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if len(main_processes) > 1:
                logger.error(f"🚨 发现 {len(main_processes)} 个main.py进程，可能存在调度器冲突")
                for proc in main_processes:
                    logger.error(f"   PID:{proc['pid']} - {proc['cmdline']}")
                self.critical_issues.append(f"发现 {len(main_processes)} 个main.py进程冲突")
            elif len(main_processes) == 1:
                logger.info(f"✅ 发现1个main.py进程，正常运行")
            else:
                logger.warning("⚠️ 未发现main.py进程，可能未启动")
            
        except Exception as e:
            logger.error(f"❌ 调度器冲突检查失败: {e}")
            self.critical_issues.append(f"调度器冲突检查失败: {e}")
    
    def _generate_fix_report(self) -> Dict[str, Any]:
        """生成修复报告"""
        logger.error("\n📋 紧急修复报告")
        logger.error("="*80)
        
        report = {
            'timestamp': datetime.now(timezone.utc),
            'fixes_applied': self.fixes_applied,
            'critical_issues': self.critical_issues,
            'success': len(self.critical_issues) == 0,
            'recommendations': []
        }
        
        # 应用的修复
        if self.fixes_applied:
            logger.success(f"✅ 已应用 {len(self.fixes_applied)} 个修复:")
            for fix in self.fixes_applied:
                logger.success(f"   - {fix}")
        else:
            logger.warning("⚠️ 未应用任何修复")
        
        # 关键问题
        if self.critical_issues:
            logger.error(f"🚨 发现 {len(self.critical_issues)} 个关键问题:")
            for issue in self.critical_issues:
                logger.error(f"   - {issue}")
        else:
            logger.success("✅ 未发现关键问题")
        
        # 生成建议
        recommendations = [
            "立即检查系统资源使用情况",
            "确认只有一个调度器进程在运行",
            "验证所有操作都限制在测试账户",
            "监控浏览器进程数量，及时清理",
            "检查模块间是否存在阻塞"
        ]
        
        report['recommendations'] = recommendations
        
        logger.error("💡 建议措施:")
        for rec in recommendations:
            logger.error(f"   - {rec}")
        
        # 总结
        if report['success']:
            logger.success("🎉 紧急修复成功，系统应该可以正常运行")
        else:
            logger.error("❌ 紧急修复未完全成功，需要人工干预")
        
        return report


def main():
    """主函数"""
    fixer = EmergencyArchitectureFixer()
    report = fixer.run_emergency_fix()
    
    if report['success']:
        logger.success("✅ 紧急修复完成")
        return 0
    else:
        logger.error("❌ 紧急修复失败，需要人工处理")
        return 1


if __name__ == "__main__":
    exit(main())
