#!/usr/bin/env python3
"""
千川项目深度问题诊断工具
基于错误日志进行精确的问题分析和修复方案制定
"""

import sys
import os
import re
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from pathlib import Path
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.logger import logger
from sqlalchemy import text

class DeepDiagnosisAnalyzer:
    """深度诊断分析器"""
    
    def __init__(self):
        self.analysis_results = {
            'submission_issues': {},
            'violation_detection_issues': {},
            'appeal_system_issues': {},
            'other_bugs': {},
            'timeline_analysis': {},
            'priority_fixes': []
        }
        self.log_files = self._get_log_files()
    
    def _get_log_files(self):
        """获取今日日志文件"""
        log_dir = Path("logs")
        today = datetime.now().strftime("%Y-%m-%d")
        
        log_files = []
        if log_dir.exists():
            for log_file in log_dir.glob(f"*{today}*.log"):
                log_files.append(log_file)
        
        logger.info(f"找到 {len(log_files)} 个今日日志文件")
        return log_files
    
    def analyze_submission_issues(self):
        """任务1: 分析提审环节问题"""
        logger.info("🔍 任务1: 提审环节问题深度分析")
        logger.info("=" * 60)
        
        submission_errors = []
        submission_patterns = [
            r'提审.*失败',
            r'submit.*failed',
            r'审核.*错误',
            r'review.*error',
            r'提审队列',
            r'submission.*queue',
            r'提审重试',
            r'submission.*retry'
        ]
        
        # 分析0:00-8:00时间段的提审相关错误
        for log_file in self.log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        # 检查时间范围 (0:00-8:00)
                        time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                        if time_match:
                            log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if not (0 <= log_time.hour <= 8):
                                continue
                        
                        # 检查提审相关错误
                        for pattern in submission_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                submission_errors.append({
                                    'file': log_file.name,
                                    'line': line_num,
                                    'time': time_match.group(1) if time_match else 'Unknown',
                                    'content': line.strip(),
                                    'pattern': pattern
                                })
                                break
            except Exception as e:
                logger.warning(f"读取日志文件失败 {log_file}: {e}")
        
        # 统计分析
        logger.info(f"📊 提审环节错误统计 (0:00-8:00):")
        logger.info(f"  总错误数: {len(submission_errors)}")
        
        if submission_errors:
            # 按模式分类
            pattern_stats = Counter(error['pattern'] for error in submission_errors)
            logger.info(f"  错误模式分布:")
            for pattern, count in pattern_stats.most_common():
                logger.info(f"    {pattern}: {count}次")
            
            # 时间分布分析
            hour_stats = defaultdict(int)
            for error in submission_errors:
                if error['time'] != 'Unknown':
                    try:
                        hour = datetime.strptime(error['time'], '%Y-%m-%d %H:%M:%S').hour
                        hour_stats[hour] += 1
                    except:
                        pass
            
            if hour_stats:
                logger.info(f"  时间分布:")
                for hour in sorted(hour_stats.keys()):
                    logger.info(f"    {hour:02d}:00-{hour:02d}:59: {hour_stats[hour]}次")
            
            # 显示典型错误
            logger.info(f"  典型错误示例:")
            for error in submission_errors[:3]:
                logger.info(f"    [{error['time']}] {error['content'][:100]}...")
        else:
            logger.info("  ✅ 0:00-8:00时间段未发现提审相关错误")
        
        self.analysis_results['submission_issues'] = {
            'total_errors': len(submission_errors),
            'pattern_distribution': dict(pattern_stats) if submission_errors else {},
            'time_distribution': dict(hour_stats) if submission_errors else {},
            'sample_errors': submission_errors[:5]
        }
        
        return submission_errors
    
    def analyze_violation_detection_issues(self):
        """任务2: 分析账户违规检测系统问题"""
        logger.info(f"\n🔍 任务2: 账户违规检测系统问题诊断")
        logger.info("=" * 60)
        
        violation_errors = []
        violation_patterns = [
            r'security/score_disposal_info/get',
            r'security/score_violation_event/get',
            r'违规检测.*失败',
            r'violation.*detection.*error',
            r'限流',
            r'rate.*limit',
            r'频率.*限制',
            r'frequency.*limit'
        ]
        
        # 分析违规检测相关错误
        for log_file in self.log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        # 检查时间范围 (0:00-8:00)
                        time_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                        if time_match:
                            log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                            if not (0 <= log_time.hour <= 8):
                                continue
                        
                        # 检查违规检测相关错误
                        for pattern in violation_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                violation_errors.append({
                                    'file': log_file.name,
                                    'line': line_num,
                                    'time': time_match.group(1) if time_match else 'Unknown',
                                    'content': line.strip(),
                                    'pattern': pattern,
                                    'is_rate_limit': 'limit' in pattern.lower() or '限流' in line
                                })
                                break
            except Exception as e:
                logger.warning(f"读取日志文件失败 {log_file}: {e}")
        
        # 统计分析
        logger.info(f"📊 违规检测系统错误统计 (0:00-8:00):")
        logger.info(f"  总错误数: {len(violation_errors)}")
        
        if violation_errors:
            # API接口分类
            api_stats = defaultdict(int)
            rate_limit_count = 0
            
            for error in violation_errors:
                if 'score_disposal_info' in error['content']:
                    api_stats['score_disposal_info'] += 1
                elif 'score_violation_event' in error['content']:
                    api_stats['score_violation_event'] += 1
                else:
                    api_stats['other'] += 1
                
                if error['is_rate_limit']:
                    rate_limit_count += 1
            
            logger.info(f"  API接口错误分布:")
            for api, count in api_stats.items():
                logger.info(f"    {api}: {count}次")
            
            logger.info(f"  限流错误: {rate_limit_count}次 ({rate_limit_count/len(violation_errors)*100:.1f}%)")
            
            # 时间分布分析
            hour_stats = defaultdict(int)
            for error in violation_errors:
                if error['time'] != 'Unknown':
                    try:
                        hour = datetime.strptime(error['time'], '%Y-%m-%d %H:%M:%S').hour
                        hour_stats[hour] += 1
                    except:
                        pass
            
            if hour_stats:
                logger.info(f"  时间分布:")
                for hour in sorted(hour_stats.keys()):
                    logger.info(f"    {hour:02d}:00-{hour:02d}:59: {hour_stats[hour]}次")
            
            # 影响评估
            total_workflow_impact = len(violation_errors) / 1206 * 100  # 基于总错误数1206
            logger.info(f"  对整体工作流影响: {total_workflow_impact:.1f}%")
            
            if total_workflow_impact > 10:
                logger.warning("🔴 违规检测问题对工作流影响严重")
            elif total_workflow_impact > 5:
                logger.warning("🟡 违规检测问题对工作流有一定影响")
            else:
                logger.info("🟢 违规检测问题对工作流影响较小")
        else:
            logger.info("  ✅ 0:00-8:00时间段未发现违规检测相关错误")
        
        self.analysis_results['violation_detection_issues'] = {
            'total_errors': len(violation_errors),
            'api_distribution': dict(api_stats) if violation_errors else {},
            'rate_limit_errors': rate_limit_count if violation_errors else 0,
            'workflow_impact_percent': total_workflow_impact if violation_errors else 0,
            'sample_errors': violation_errors[:5]
        }
        
        return violation_errors
    
    def analyze_appeal_system_issues(self):
        """任务3: 分析申诉系统架构缺陷"""
        logger.info(f"\n🔍 任务3: 申诉系统架构缺陷分析")
        logger.info("=" * 60)
        
        appeal_errors = []
        playwright_errors = []
        serialization_errors = []
        
        appeal_patterns = [
            r'Playwright.*异步',
            r'Playwright.*async',
            r'进程序列化',
            r'process.*serialization',
            r'申诉.*失败',
            r'appeal.*failed',
            r'浏览器.*自动化',
            r'browser.*automation'
        ]
        
        # 分析申诉系统相关错误
        for log_file in self.log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        # 检查申诉相关错误
                        for pattern in appeal_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                error_info = {
                                    'file': log_file.name,
                                    'line': line_num,
                                    'content': line.strip(),
                                    'pattern': pattern
                                }
                                
                                appeal_errors.append(error_info)
                                
                                # 分类错误类型
                                if 'playwright' in line.lower() and ('异步' in line or 'async' in line):
                                    playwright_errors.append(error_info)
                                elif '序列化' in line or 'serialization' in line:
                                    serialization_errors.append(error_info)
                                
                                break
            except Exception as e:
                logger.warning(f"读取日志文件失败 {log_file}: {e}")
        
        # 统计分析
        logger.info(f"📊 申诉系统错误统计:")
        logger.info(f"  总申诉错误: {len(appeal_errors)}")
        logger.info(f"  Playwright异步冲突: {len(playwright_errors)}")
        logger.info(f"  进程序列化失败: {len(serialization_errors)}")
        
        # 验证与报告中的数据对比
        expected_playwright = 176
        expected_serialization = 190
        expected_total = 366
        
        logger.info(f"\n📋 与错误报告对比:")
        logger.info(f"  预期Playwright错误: {expected_playwright}, 实际发现: {len(playwright_errors)}")
        logger.info(f"  预期序列化错误: {expected_serialization}, 实际发现: {len(serialization_errors)}")
        logger.info(f"  预期总错误: {expected_total}, 实际发现: {len(appeal_errors)}")
        
        if len(appeal_errors) > 0:
            logger.info(f"\n  典型错误示例:")
            for error in appeal_errors[:3]:
                logger.info(f"    {error['content'][:100]}...")
        
        self.analysis_results['appeal_system_issues'] = {
            'total_errors': len(appeal_errors),
            'playwright_async_errors': len(playwright_errors),
            'serialization_errors': len(serialization_errors),
            'expected_vs_actual': {
                'playwright': {'expected': expected_playwright, 'actual': len(playwright_errors)},
                'serialization': {'expected': expected_serialization, 'actual': len(serialization_errors)},
                'total': {'expected': expected_total, 'actual': len(appeal_errors)}
            },
            'sample_errors': appeal_errors[:5]
        }
        
        return appeal_errors, playwright_errors, serialization_errors
    
    def identify_other_bugs(self):
        """任务4: 识别其他系统性BUG"""
        logger.info(f"\n🔍 任务4: 系统性BUG识别")
        logger.info("=" * 60)
        
        other_bugs = []
        bug_patterns = [
            r'ERROR.*数据库',
            r'ERROR.*database',
            r'ERROR.*连接',
            r'ERROR.*connection',
            r'ERROR.*内存',
            r'ERROR.*memory',
            r'ERROR.*超时',
            r'ERROR.*timeout',
            r'CRITICAL',
            r'FATAL',
            r'异常.*终止',
            r'exception.*terminate'
        ]
        
        # 分析其他严重错误
        for log_file in self.log_files:
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        # 跳过已分析的错误类型
                        if any(keyword in line.lower() for keyword in ['提审', 'submit', '违规', 'violation', 'playwright', '序列化']):
                            continue
                        
                        # 检查其他严重错误
                        for pattern in bug_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                other_bugs.append({
                                    'file': log_file.name,
                                    'line': line_num,
                                    'content': line.strip(),
                                    'pattern': pattern,
                                    'severity': self._assess_severity(line)
                                })
                                break
            except Exception as e:
                logger.warning(f"读取日志文件失败 {log_file}: {e}")
        
        # 统计分析
        logger.info(f"📊 其他系统性BUG统计:")
        logger.info(f"  总发现BUG: {len(other_bugs)}")
        
        if other_bugs:
            # 按严重程度分类
            severity_stats = Counter(bug['severity'] for bug in other_bugs)
            logger.info(f"  严重程度分布:")
            for severity, count in severity_stats.most_common():
                logger.info(f"    {severity}: {count}次")
            
            # 按模式分类
            pattern_stats = Counter(bug['pattern'] for bug in other_bugs)
            logger.info(f"  错误模式分布:")
            for pattern, count in pattern_stats.most_common():
                logger.info(f"    {pattern}: {count}次")
            
            # 显示高严重性错误
            critical_bugs = [bug for bug in other_bugs if bug['severity'] in ['CRITICAL', 'HIGH']]
            if critical_bugs:
                logger.warning(f"🔴 发现 {len(critical_bugs)} 个高严重性BUG:")
                for bug in critical_bugs[:3]:
                    logger.warning(f"    {bug['content'][:100]}...")
        else:
            logger.info("  ✅ 未发现其他明显的系统性BUG")
        
        self.analysis_results['other_bugs'] = {
            'total_bugs': len(other_bugs),
            'severity_distribution': dict(severity_stats) if other_bugs else {},
            'pattern_distribution': dict(pattern_stats) if other_bugs else {},
            'critical_bugs': len([bug for bug in other_bugs if bug['severity'] in ['CRITICAL', 'HIGH']]),
            'sample_bugs': other_bugs[:5]
        }
        
        return other_bugs
    
    def _assess_severity(self, log_line):
        """评估错误严重程度"""
        line_lower = log_line.lower()
        
        if any(keyword in line_lower for keyword in ['critical', 'fatal', '致命', '严重']):
            return 'CRITICAL'
        elif any(keyword in line_lower for keyword in ['error', '错误', '失败']):
            return 'HIGH'
        elif any(keyword in line_lower for keyword in ['warning', '警告', '异常']):
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def generate_priority_fix_plan(self):
        """生成优先级修复计划"""
        logger.info(f"\n📋 优先级修复计划制定")
        logger.info("=" * 60)
        
        fixes = []
        
        # P0 - 申诉系统架构缺陷 (影响最大)
        if self.analysis_results['appeal_system_issues']['total_errors'] > 0:
            fixes.append({
                'priority': 'P0',
                'issue': '申诉系统架构缺陷',
                'impact': 'HIGH',
                'errors': self.analysis_results['appeal_system_issues']['total_errors'],
                'estimated_hours': 8,
                'description': 'Playwright异步冲突和进程序列化失败导致申诉功能完全瘫痪'
            })
        
        # P1 - 违规检测系统限流
        if self.analysis_results['violation_detection_issues']['rate_limit_errors'] > 0:
            fixes.append({
                'priority': 'P1',
                'issue': '违规检测接口限流',
                'impact': 'MEDIUM',
                'errors': self.analysis_results['violation_detection_issues']['rate_limit_errors'],
                'estimated_hours': 4,
                'description': '违规检测接口频率控制问题，影响账户监控功能'
            })
        
        # P1 - 提审环节问题
        if self.analysis_results['submission_issues']['total_errors'] > 0:
            fixes.append({
                'priority': 'P1',
                'issue': '提审环节异常',
                'impact': 'MEDIUM',
                'errors': self.analysis_results['submission_issues']['total_errors'],
                'estimated_hours': 6,
                'description': '提审流程中的各种异常，影响计划审核效率'
            })
        
        # P2 - 其他系统性BUG
        critical_bugs = self.analysis_results['other_bugs']['critical_bugs']
        if critical_bugs > 0:
            fixes.append({
                'priority': 'P2',
                'issue': '其他系统性BUG',
                'impact': 'MEDIUM',
                'errors': critical_bugs,
                'estimated_hours': 4,
                'description': '数据库连接、内存管理等系统级问题'
            })
        
        # 输出修复计划
        if fixes:
            logger.info(f"🎯 修复优先级排序:")
            total_hours = 0
            
            for fix in sorted(fixes, key=lambda x: x['priority']):
                priority_icon = "🔴" if fix['priority'] == 'P0' else "🟡" if fix['priority'] == 'P1' else "🟢"
                logger.info(f"  {priority_icon} {fix['priority']} - {fix['issue']}")
                logger.info(f"     错误数: {fix['errors']}, 影响: {fix['impact']}, 预估: {fix['estimated_hours']}小时")
                logger.info(f"     描述: {fix['description']}")
                total_hours += fix['estimated_hours']
            
            logger.info(f"\n⏱️ 总修复时间估算: {total_hours} 小时")
            
            # 修复顺序建议
            logger.info(f"\n📅 建议修复顺序:")
            logger.info(f"  第1阶段 (今日): P0问题 - 申诉系统架构修复")
            logger.info(f"  第2阶段 (明日): P1问题 - 违规检测和提审环节")
            logger.info(f"  第3阶段 (本周): P2问题 - 其他系统性BUG")
        else:
            logger.info("✅ 未发现需要立即修复的严重问题")
        
        self.analysis_results['priority_fixes'] = fixes
        return fixes

def main():
    """主函数"""
    try:
        analyzer = DeepDiagnosisAnalyzer()
        
        # 执行四个分析任务
        submission_errors = analyzer.analyze_submission_issues()
        violation_errors = analyzer.analyze_violation_detection_issues()
        appeal_errors = analyzer.analyze_appeal_system_issues()
        other_bugs = analyzer.identify_other_bugs()
        
        # 生成修复计划
        priority_fixes = analyzer.generate_priority_fix_plan()
        
        logger.info(f"\n✅ 深度问题诊断完成")
        logger.info(f"建议立即开始P0问题修复: 申诉系统架构缺陷")
        
        return analyzer.analysis_results
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
