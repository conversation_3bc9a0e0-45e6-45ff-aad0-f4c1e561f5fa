#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 手动执行素材收割操作
清理条件: 收割完成后可删除

手动素材收割工具
==============

手动收割已测试但未收割的素材到approved目录。
"""

import os
import sys
import shutil
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


def manual_harvest():
    """手动执行收割操作"""
    logger.info("🚀 开始手动收割操作...")
    
    # 目标收割目录
    today = datetime.now().strftime('%Y-%m-%d')
    approved_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货', today)
    
    try:
        with database_session() as db:
            # 查找需要收割的素材
            unharvested_materials = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.ALREADY_TESTED.value,
                LocalCreative.harvest_status == 'not_harvested'
            ).all()
            
            logger.info(f"📊 发现 {len(unharvested_materials)} 个需要收割的素材")
            
            if not unharvested_materials:
                logger.info("✅ 没有需要收割的素材")
                return True
            
            # 创建收割目录
            os.makedirs(approved_dir, exist_ok=True)
            logger.info(f"✅ 创建收割目录: {approved_dir}")
            
            # 执行收割
            harvested_count = 0
            for material in unharvested_materials:
                try:
                    logger.info(f"🔄 处理素材: {material.filename}")
                    
                    # 检查源文件是否存在
                    if not material.file_path or not os.path.exists(material.file_path):
                        logger.warning(f"⚠️ 源文件不存在: {material.file_path}")
                        continue
                    
                    # 构建目标文件路径
                    target_path = os.path.join(approved_dir, material.filename)
                    
                    # 复制文件到收割目录
                    shutil.copy2(material.file_path, target_path)
                    logger.info(f"📁 文件已复制到: {target_path}")
                    
                    # 更新数据库状态
                    material.harvest_status = MaterialStatus.HARVESTED.value
                    material.updated_at = datetime.now()
                    
                    harvested_count += 1
                    logger.info(f"✅ 收割完成: {material.filename}")
                    
                except Exception as e:
                    logger.error(f"❌ 收割失败 {material.filename}: {e}")
            
            # 提交数据库更改
            db.commit()
            
            logger.info(f"🎯 收割操作完成！成功收割 {harvested_count} 个素材")
            logger.info(f"📁 收割目录: {approved_dir}")
            
            # 验证收割结果
            if os.path.exists(approved_dir):
                files = [f for f in os.listdir(approved_dir) if f.endswith('.mp4')]
                logger.info(f"📊 收割目录中现有 {len(files)} 个视频文件")
            
            return harvested_count > 0
            
    except Exception as e:
        logger.error(f"❌ 收割操作失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始手动素材收割...")
    
    try:
        success = manual_harvest()
        
        if success:
            logger.info("🎉 手动收割操作成功完成！")
            return 0
        else:
            logger.warning("⚠️ 没有素材需要收割或操作失败")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 手动收割过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
