#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 提审模块代码完整性和可靠性审计
清理条件: 成为项目维护工具，长期保留
"""

import os
import sys
import ast
import importlib.util
from pathlib import Path
from typing import Dict, Any, List, Set
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class ComprehensiveCodeAuditor:
    """提审模块代码完整性和可靠性审计器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.audit_results = {
            'timestamp': datetime.now(timezone.utc),
            'unified_state_manager': {},
            'database_consistency': {},
            'workflow_integration': {},
            'monitoring_mechanisms': {},
            'concurrency_safety': {},
            'overall_score': 0,
            'critical_issues': [],
            'recommendations': []
        }
    
    def run_comprehensive_audit(self) -> Dict[str, Any]:
        """运行全面的代码审计"""
        logger.info("🔍 开始提审模块代码完整性和可靠性审计")
        logger.info("="*80)
        
        try:
            # 1. 审计统一状态管理器
            self._audit_unified_state_manager()
            
            # 2. 审计数据库一致性
            self._audit_database_consistency()
            
            # 3. 审计工作流集成
            self._audit_workflow_integration()
            
            # 4. 审计监控机制
            self._audit_monitoring_mechanisms()
            
            # 5. 审计并发安全性
            self._audit_concurrency_safety()
            
            # 6. 计算总体评分
            self._calculate_overall_score()
            
            # 7. 生成审计报告
            self._generate_audit_report()
            
            return self.audit_results
            
        except Exception as e:
            logger.error(f"❌ 代码审计失败: {e}", exc_info=True)
            self.audit_results['critical_issues'].append(f"审计执行失败: {e}")
            return self.audit_results
    
    def _audit_unified_state_manager(self):
        """审计统一状态管理器"""
        logger.info("🔍 审计统一状态管理器...")
        
        audit_result = {
            'file_exists': False,
            'required_methods': [],
            'missing_methods': [],
            'error_handling': False,
            'transaction_safety': False,
            'logging_integration': False,
            'score': 0
        }
        
        # 检查文件是否存在
        state_manager_file = self.project_root / 'src/qianchuan_aw/utils/unified_appeal_state_manager.py'
        if state_manager_file.exists():
            audit_result['file_exists'] = True
            logger.info("   ✅ 统一状态管理器文件存在")
            
            # 检查必需的方法
            required_methods = [
                'set_appeal_needed',
                'set_appeal_executing', 
                'set_appeal_success',
                'set_appeal_monitoring',
                'set_appeal_final_result',
                'validate_state_consistency'
            ]
            
            try:
                with open(state_manager_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for method in required_methods:
                    if f'def {method}(' in content:
                        audit_result['required_methods'].append(method)
                    else:
                        audit_result['missing_methods'].append(method)
                
                # 检查错误处理
                if 'try:' in content and 'except' in content:
                    audit_result['error_handling'] = True
                
                # 检查事务安全
                if 'db.commit()' in content or 'db.rollback()' in content:
                    audit_result['transaction_safety'] = True
                
                # 检查日志集成
                if 'logger.' in content:
                    audit_result['logging_integration'] = True
                
                logger.info(f"   ✅ 找到 {len(audit_result['required_methods'])}/{len(required_methods)} 个必需方法")
                
            except Exception as e:
                logger.error(f"   ❌ 读取文件失败: {e}")
                audit_result['missing_methods'] = required_methods
        else:
            logger.error("   ❌ 统一状态管理器文件不存在")
            audit_result['missing_methods'] = [
                'set_appeal_needed', 'set_appeal_executing', 'set_appeal_success',
                'set_appeal_monitoring', 'set_appeal_final_result', 'validate_state_consistency'
            ]
        
        # 计算评分
        method_score = len(audit_result['required_methods']) / 6 * 40  # 40分
        safety_score = (audit_result['error_handling'] + audit_result['transaction_safety'] + audit_result['logging_integration']) / 3 * 30  # 30分
        existence_score = 30 if audit_result['file_exists'] else 0  # 30分
        
        audit_result['score'] = method_score + safety_score + existence_score
        
        self.audit_results['unified_state_manager'] = audit_result
        logger.info(f"   📊 统一状态管理器评分: {audit_result['score']:.1f}/100")
    
    def _audit_database_consistency(self):
        """审计数据库一致性"""
        logger.info("🔍 审计数据库一致性...")
        
        audit_result = {
            'required_fields': [],
            'missing_fields': [],
            'field_types_correct': True,
            'constraints_exist': False,
            'indexes_optimized': False,
            'score': 0
        }
        
        # 检查必需的数据库字段
        required_fields = [
            'appeal_status', 'first_appeal_at', 'last_appeal_at',
            'appeal_attempt_count', 'appeal_started_at', 'appeal_completed_at',
            'appeal_result', 'appeal_error_message', 'last_appeal_check'
        ]
        
        try:
            # 这里应该连接数据库检查字段，简化为文件检查
            models_file = self.project_root / 'src/qianchuan_aw/database/models.py'
            if models_file.exists():
                with open(models_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for field in required_fields:
                    if field in content:
                        audit_result['required_fields'].append(field)
                    else:
                        audit_result['missing_fields'].append(field)
                
                # 检查约束
                if 'nullable=' in content or 'unique=' in content:
                    audit_result['constraints_exist'] = True
                
                logger.info(f"   ✅ 找到 {len(audit_result['required_fields'])}/{len(required_fields)} 个必需字段")
            else:
                logger.error("   ❌ 数据库模型文件不存在")
                audit_result['missing_fields'] = required_fields
        
        except Exception as e:
            logger.error(f"   ❌ 检查数据库模型失败: {e}")
        
        # 计算评分
        field_score = len(audit_result['required_fields']) / len(required_fields) * 60  # 60分
        constraint_score = 20 if audit_result['constraints_exist'] else 0  # 20分
        index_score = 20 if audit_result['indexes_optimized'] else 0  # 20分
        
        audit_result['score'] = field_score + constraint_score + index_score
        
        self.audit_results['database_consistency'] = audit_result
        logger.info(f"   📊 数据库一致性评分: {audit_result['score']:.1f}/100")
    
    def _audit_workflow_integration(self):
        """审计工作流集成"""
        logger.info("🔍 审计工作流集成...")
        
        audit_result = {
            'refactored_files': [],
            'non_refactored_files': [],
            'backward_compatibility': False,
            'error_handling': False,
            'score': 0
        }
        
        # 检查关键文件是否已重构
        key_files = [
            'ai_tools/enhancement/ai_tool_20250801_enhancement_workflow_integration.py',
            'src/qianchuan_aw/workflows/appeal_and_monitor.py',
            'src/qianchuan_aw/services/unified_appeal_service.py'
        ]
        
        for file_path in key_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否使用了统一状态管理器
                    if 'unified_appeal_state_manager' in content or 'create_appeal_state_manager' in content:
                        audit_result['refactored_files'].append(file_path)
                        logger.info(f"   ✅ 已重构: {file_path}")
                    else:
                        audit_result['non_refactored_files'].append(file_path)
                        logger.warning(f"   ⚠️ 未重构: {file_path}")
                    
                    # 检查向后兼容性
                    if 'try:' in content and 'except' in content:
                        audit_result['backward_compatibility'] = True
                    
                except Exception as e:
                    logger.error(f"   ❌ 检查文件失败 {file_path}: {e}")
                    audit_result['non_refactored_files'].append(file_path)
            else:
                logger.warning(f"   ⚠️ 文件不存在: {file_path}")
                audit_result['non_refactored_files'].append(file_path)
        
        # 计算评分
        refactor_score = len(audit_result['refactored_files']) / len(key_files) * 70  # 70分
        compatibility_score = 30 if audit_result['backward_compatibility'] else 0  # 30分
        
        audit_result['score'] = refactor_score + compatibility_score
        
        self.audit_results['workflow_integration'] = audit_result
        logger.info(f"   📊 工作流集成评分: {audit_result['score']:.1f}/100")
    
    def _audit_monitoring_mechanisms(self):
        """审计监控机制"""
        logger.info("🔍 审计监控机制...")
        
        audit_result = {
            'monitoring_file_exists': False,
            'alert_mechanisms': [],
            'consistency_checks': False,
            'performance_metrics': False,
            'score': 0
        }
        
        # 检查监控文件
        monitoring_file = self.project_root / 'ai_tools/maintenance/ai_tool_20250808_appeal_state_monitoring.py'
        if monitoring_file.exists():
            audit_result['monitoring_file_exists'] = True
            
            try:
                with open(monitoring_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查告警机制
                alert_types = ['CONSISTENCY_ALERT', 'STUCK_APPEALS_ALERT', 'TIMEOUT_APPEALS_ALERT', 'LOW_SUCCESS_RATE_ALERT']
                for alert_type in alert_types:
                    if alert_type in content:
                        audit_result['alert_mechanisms'].append(alert_type)
                
                # 检查一致性检查
                if '_check_state_consistency' in content:
                    audit_result['consistency_checks'] = True
                
                # 检查性能指标
                if '_collect_performance_metrics' in content:
                    audit_result['performance_metrics'] = True
                
                logger.info(f"   ✅ 找到 {len(audit_result['alert_mechanisms'])}/4 个告警机制")
                
            except Exception as e:
                logger.error(f"   ❌ 检查监控文件失败: {e}")
        else:
            logger.error("   ❌ 监控文件不存在")
        
        # 计算评分
        file_score = 30 if audit_result['monitoring_file_exists'] else 0  # 30分
        alert_score = len(audit_result['alert_mechanisms']) / 4 * 40  # 40分
        check_score = (audit_result['consistency_checks'] + audit_result['performance_metrics']) / 2 * 30  # 30分
        
        audit_result['score'] = file_score + alert_score + check_score
        
        self.audit_results['monitoring_mechanisms'] = audit_result
        logger.info(f"   📊 监控机制评分: {audit_result['score']:.1f}/100")
    
    def _audit_concurrency_safety(self):
        """审计并发安全性"""
        logger.info("🔍 审计并发安全性...")
        
        audit_result = {
            'distributed_lock': False,
            'transaction_management': False,
            'atomic_operations': False,
            'error_recovery': False,
            'score': 0
        }
        
        # 检查统一状态管理器的并发安全性
        state_manager_file = self.project_root / 'src/qianchuan_aw/utils/unified_appeal_state_manager.py'
        if state_manager_file.exists():
            try:
                with open(state_manager_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查分布式锁
                if 'DistributedLock' in content or 'redis' in content.lower():
                    audit_result['distributed_lock'] = True
                
                # 检查事务管理
                if 'db.begin()' in content or 'db.commit()' in content:
                    audit_result['transaction_management'] = True
                
                # 检查原子操作
                if 'atomic' in content.lower() or '_execute_state_transition' in content:
                    audit_result['atomic_operations'] = True
                
                # 检查错误恢复
                if 'rollback' in content or 'recovery' in content.lower():
                    audit_result['error_recovery'] = True
                
            except Exception as e:
                logger.error(f"   ❌ 检查并发安全性失败: {e}")
        
        # 计算评分
        safety_features = [
            audit_result['distributed_lock'],
            audit_result['transaction_management'], 
            audit_result['atomic_operations'],
            audit_result['error_recovery']
        ]
        
        audit_result['score'] = sum(safety_features) / len(safety_features) * 100
        
        self.audit_results['concurrency_safety'] = audit_result
        logger.info(f"   📊 并发安全性评分: {audit_result['score']:.1f}/100")
    
    def _calculate_overall_score(self):
        """计算总体评分"""
        scores = [
            self.audit_results['unified_state_manager']['score'],
            self.audit_results['database_consistency']['score'],
            self.audit_results['workflow_integration']['score'],
            self.audit_results['monitoring_mechanisms']['score'],
            self.audit_results['concurrency_safety']['score']
        ]
        
        # 加权平均（统一状态管理器和工作流集成权重更高）
        weights = [0.3, 0.2, 0.3, 0.1, 0.1]
        weighted_score = sum(score * weight for score, weight in zip(scores, weights))
        
        self.audit_results['overall_score'] = weighted_score
        
        # 根据评分生成建议
        if weighted_score >= 90:
            self.audit_results['recommendations'].append("✅ 代码质量优秀，系统稳定可靠")
        elif weighted_score >= 80:
            self.audit_results['recommendations'].append("⚠️ 代码质量良好，建议优化监控机制")
        elif weighted_score >= 70:
            self.audit_results['recommendations'].append("⚠️ 代码质量一般，需要完善工作流集成")
        else:
            self.audit_results['recommendations'].append("❌ 代码质量较差，需要全面重构")
            self.audit_results['critical_issues'].append("整体代码质量评分过低")
    
    def _generate_audit_report(self):
        """生成审计报告"""
        logger.info("\n📋 提审模块代码审计报告")
        logger.info("="*80)
        
        # 总体评分
        overall_score = self.audit_results['overall_score']
        logger.info(f"🎯 总体评分: {overall_score:.1f}/100")
        
        # 各模块评分
        logger.info(f"📊 详细评分:")
        logger.info(f"   统一状态管理器: {self.audit_results['unified_state_manager']['score']:.1f}/100")
        logger.info(f"   数据库一致性: {self.audit_results['database_consistency']['score']:.1f}/100")
        logger.info(f"   工作流集成: {self.audit_results['workflow_integration']['score']:.1f}/100")
        logger.info(f"   监控机制: {self.audit_results['monitoring_mechanisms']['score']:.1f}/100")
        logger.info(f"   并发安全性: {self.audit_results['concurrency_safety']['score']:.1f}/100")
        
        # 关键问题
        if self.audit_results['critical_issues']:
            logger.error(f"🚨 关键问题:")
            for issue in self.audit_results['critical_issues']:
                logger.error(f"   - {issue}")
        
        # 建议
        logger.info(f"💡 建议:")
        for recommendation in self.audit_results['recommendations']:
            logger.info(f"   - {recommendation}")


def main():
    """主函数"""
    auditor = ComprehensiveCodeAuditor()
    results = auditor.run_comprehensive_audit()
    
    overall_score = results['overall_score']
    if overall_score >= 80:
        logger.success(f"✅ 代码审计通过，总体评分: {overall_score:.1f}/100")
        return 0
    else:
        logger.error(f"❌ 代码审计未通过，总体评分: {overall_score:.1f}/100")
        return 1


if __name__ == "__main__":
    exit(main())
