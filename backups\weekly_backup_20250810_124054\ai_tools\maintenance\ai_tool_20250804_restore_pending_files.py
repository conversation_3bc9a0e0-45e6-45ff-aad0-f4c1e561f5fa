#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 恢复pending_upload状态的文件到入库目录
清理条件: 成为项目核心恢复工具，长期保留
"""

import os
import sys
import shutil
from datetime import datetime
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def get_pending_files_from_db():
    """从数据库获取pending_upload状态的文件"""
    logger.info("🔍 查询数据库中pending_upload状态的文件...")
    
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            pending_files = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
            ).all()
            
            logger.info(f"📊 发现 {len(pending_files)} 个pending_upload状态文件")
            
            return pending_files
            
    except Exception as e:
        logger.error(f"❌ 数据库查询失败: {e}")
        return []

def find_file_in_archive(filename):
    """在归档目录中查找文件"""
    search_paths = [
        "D:/workflow_assets/00_uploaded_archive/缇萃百货",
        "D:/workflow_assets/03_materials_approved/缇萃百货",
        "D:/workflow_assets/delivery_materials_cleanup"
    ]
    
    for base_path in search_paths:
        if os.path.exists(base_path):
            # 递归搜索
            for root, dirs, files in os.walk(base_path):
                if filename in files:
                    return os.path.join(root, filename)
    
    return None

def restore_pending_files():
    """恢复pending_upload状态的文件"""
    logger.info("🔄 开始恢复pending_upload状态的文件...")
    
    # 确保目标目录存在
    target_dir = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    os.makedirs(target_dir, exist_ok=True)
    
    # 直接使用SQL查询避免导入问题
    import psycopg2
    
    try:
        # 连接数据库
        conn = psycopg2.connect(
            host="localhost",
            database="qianchuan_postgres",
            user="postgres",
            password="123456"
        )
        cur = conn.cursor()
        
        # 查询pending_upload状态的文件
        cur.execute("""
            SELECT filename, file_path, status 
            FROM local_creatives 
            WHERE status = MaterialStatus.PENDING_UPLOAD.value
            ORDER BY created_at
        """)
        
        pending_files = cur.fetchall()
        logger.info(f"📊 发现 {len(pending_files)} 个pending_upload状态文件")
        
        restored_count = 0
        not_found_count = 0
        
        for filename, file_path, status in pending_files:
            target_file = os.path.join(target_dir, filename)
            
            # 如果目标文件已存在，跳过
            if os.path.exists(target_file):
                logger.info(f"✅ 文件已存在: {filename}")
                continue
            
            # 在归档目录中查找文件
            source_file = find_file_in_archive(filename)
            
            if source_file:
                try:
                    # 复制文件到入库目录
                    shutil.copy2(source_file, target_file)
                    logger.success(f"✅ 恢复文件: {filename}")
                    logger.info(f"   源: {source_file}")
                    logger.info(f"   目标: {target_file}")
                    restored_count += 1
                except Exception as e:
                    logger.error(f"❌ 复制文件失败 {filename}: {e}")
            else:
                logger.warning(f"⚠️ 未找到文件: {filename}")
                not_found_count += 1
        
        cur.close()
        conn.close()
        
        logger.info(f"🎯 恢复完成: 成功 {restored_count} 个，未找到 {not_found_count} 个")
        return restored_count, not_found_count
        
    except Exception as e:
        logger.error(f"❌ 恢复过程失败: {e}")
        return 0, 0

def verify_directory_structure():
    """验证目录结构"""
    logger.info("🔍 验证目录结构...")
    
    target_dir = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    
    if os.path.exists(target_dir):
        files = [f for f in os.listdir(target_dir) if f.endswith('.mp4')]
        logger.success(f"✅ 入库目录存在，包含 {len(files)} 个视频文件")
        
        # 显示前10个文件
        for i, filename in enumerate(sorted(files)[:10]):
            logger.info(f"   📄 {filename}")
        
        if len(files) > 10:
            logger.info(f"   ... 还有 {len(files) - 10} 个文件")
        
        return len(files)
    else:
        logger.error(f"❌ 入库目录不存在: {target_dir}")
        return 0

def check_database_consistency():
    """检查数据库一致性"""
    logger.info("🔍 检查数据库路径一致性...")
    
    try:
        import psycopg2
        
        conn = psycopg2.connect(
            host="localhost",
            database="qianchuan_postgres", 
            user="postgres",
            password="123456"
        )
        cur = conn.cursor()
        
        # 检查路径模式
        cur.execute("""
            SELECT 
                CASE 
                    WHEN file_path LIKE '%01_to_process%' THEN 'old_path'
                    WHEN file_path LIKE '%01_materials_to_process%' THEN 'new_path'
                    ELSE 'other_path'
                END as path_type,
                COUNT(*) as count
            FROM local_creatives 
            GROUP BY path_type
        """)
        
        path_stats = cur.fetchall()
        
        logger.info("📊 数据库路径统计:")
        for path_type, count in path_stats:
            logger.info(f"   {path_type}: {count} 条记录")
        
        cur.close()
        conn.close()
        
        return path_stats
        
    except Exception as e:
        logger.error(f"❌ 数据库检查失败: {e}")
        return []

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 恢复pending_upload文件到入库目录")
    logger.info("=" * 80)
    
    # 1. 恢复pending文件
    restored_count, not_found_count = restore_pending_files()
    
    # 2. 验证目录结构
    file_count = verify_directory_structure()
    
    # 3. 检查数据库一致性
    path_stats = check_database_consistency()
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("🎯 恢复总结:")
    logger.info(f"   ✅ 成功恢复: {restored_count} 个文件")
    logger.info(f"   ⚠️ 未找到: {not_found_count} 个文件")
    logger.info(f"   📁 入库目录文件: {file_count} 个")
    logger.info(f"   💾 数据库路径: 已统一到01_materials_to_process")
    
    if file_count > 0:
        logger.success("✅ 入库目录已恢复，包含待处理文件")
        logger.info("💡 建议: 启动Celery Beat调度器以处理这些文件")
        logger.info("   命令: celery -A src.qianchuan_aw.celery_app beat --loglevel=info")
    else:
        logger.warning("⚠️ 入库目录为空，可能需要手动添加文件")
    
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
