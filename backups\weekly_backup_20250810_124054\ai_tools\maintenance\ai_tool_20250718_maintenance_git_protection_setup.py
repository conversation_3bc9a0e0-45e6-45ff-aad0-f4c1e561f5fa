#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: Git自动提交和项目保护机制一键安装配置工具
依赖关系: 整合所有Git保护组件
清理条件: 系统完全部署后可选择性保留
"""

import os
import sys
import yaml
import json
import shutil
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class GitProtectionSetup:
    """Git自动提交和项目保护机制安装配置工具"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_path = self.project_root / "config" / "settings.yml"
        self.template_config_path = self.project_root / "ai_templates" / "config" / "ai_template_20250718_config_git_protection_settings.yml"
        
    def load_existing_config(self) -> Dict:
        """加载现有配置"""
        if self.config_path.exists():
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        return {}
    
    def load_template_config(self) -> Dict:
        """加载模板配置"""
        if self.template_config_path.exists():
            with open(self.template_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 移除注释行
                lines = []
                for line in content.split('\n'):
                    if not line.strip().startswith('#'):
                        lines.append(line)
                clean_content = '\n'.join(lines)
                return yaml.safe_load(clean_content) or {}
        return {}
    
    def merge_configs(self, existing: Dict, template: Dict) -> Dict:
        """合并配置文件"""
        def deep_merge(base: Dict, update: Dict) -> Dict:
            """深度合并字典"""
            result = base.copy()
            for key, value in update.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = deep_merge(result[key], value)
                else:
                    result[key] = value
            return result
        
        return deep_merge(existing, template)
    
    def backup_config(self) -> str:
        """备份现有配置文件"""
        if not self.config_path.exists():
            return ""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.config_path.with_suffix(f'.backup_{timestamp}.yml')
        
        shutil.copy2(self.config_path, backup_path)
        print(f"✅ 配置文件已备份: {backup_path}")
        return str(backup_path)
    
    def update_config_file(self) -> Dict:
        """更新配置文件"""
        try:
            # 1. 备份现有配置
            backup_path = self.backup_config()
            
            # 2. 加载配置
            existing_config = self.load_existing_config()
            template_config = self.load_template_config()
            
            if not template_config:
                return {"success": False, "error": "无法加载模板配置"}
            
            # 3. 合并配置
            merged_config = self.merge_configs(existing_config, template_config)
            
            # 4. 写入新配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(merged_config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            print(f"✅ 配置文件已更新: {self.config_path}")
            
            return {
                "success": True,
                "backup_path": backup_path,
                "config_path": str(self.config_path)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def install_git_hooks(self) -> Dict:
        """安装Git hooks"""
        try:
            hooks_script = self.project_root / "ai_templates" / "scripts" / "ai_template_20250718_scripts_git_hooks.py"
            
            if not hooks_script.exists():
                return {"success": False, "error": "Git hooks脚本不存在"}
            
            # 执行安装
            result = subprocess.run(
                [sys.executable, str(hooks_script), "install"],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print("✅ Git hooks安装成功")
                return {"success": True, "output": result.stdout}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def create_backup_directories(self) -> Dict:
        """创建备份目录结构"""
        try:
            backup_dirs = [
                "backups/daily",
                "backups/weekly",
                "backups/monthly",
                "backups/critical_files",
                "backups/database",
                "backups/emergency"
            ]
            
            created_dirs = []
            for backup_dir in backup_dirs:
                dir_path = self.project_root / backup_dir
                dir_path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(str(dir_path))
            
            print(f"✅ 创建备份目录: {len(created_dirs)} 个")
            return {"success": True, "directories": created_dirs}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_system_components(self) -> Dict:
        """测试系统组件"""
        tests = {
            "git_auto_commit": {"passed": False, "error": None},
            "project_protection": {"passed": False, "error": None},
            "git_hooks": {"passed": False, "error": None},
            "config_file": {"passed": False, "error": None}
        }
        
        # 测试Git自动提交工具
        try:
            git_commit_tool = self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_git_auto_commit.py"
            if git_commit_tool.exists():
                result = subprocess.run(
                    [sys.executable, str(git_commit_tool), "status"],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                tests["git_auto_commit"]["passed"] = result.returncode == 0
                if result.returncode != 0:
                    tests["git_auto_commit"]["error"] = result.stderr
            else:
                tests["git_auto_commit"]["error"] = "工具文件不存在"
        except Exception as e:
            tests["git_auto_commit"]["error"] = str(e)
        
        # 测试项目保护工具
        try:
            protection_tool = self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_project_protection.py"
            if protection_tool.exists():
                result = subprocess.run(
                    [sys.executable, str(protection_tool), "check"],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                tests["project_protection"]["passed"] = result.returncode == 0
                if result.returncode != 0:
                    tests["project_protection"]["error"] = result.stderr
            else:
                tests["project_protection"]["error"] = "工具文件不存在"
        except Exception as e:
            tests["project_protection"]["error"] = str(e)
        
        # 测试Git hooks
        try:
            hooks_script = self.project_root / "ai_templates" / "scripts" / "ai_template_20250718_scripts_git_hooks.py"
            if hooks_script.exists():
                result = subprocess.run(
                    [sys.executable, str(hooks_script), "test"],
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                tests["git_hooks"]["passed"] = result.returncode == 0
                if result.returncode != 0:
                    tests["git_hooks"]["error"] = result.stderr
            else:
                tests["git_hooks"]["error"] = "脚本文件不存在"
        except Exception as e:
            tests["git_hooks"]["error"] = str(e)
        
        # 测试配置文件
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    
                # 检查关键配置项
                required_keys = ["git_auto_commit", "project_protection"]
                missing_keys = [key for key in required_keys if key not in config]
                
                if not missing_keys:
                    tests["config_file"]["passed"] = True
                else:
                    tests["config_file"]["error"] = f"缺少配置项: {missing_keys}"
            else:
                tests["config_file"]["error"] = "配置文件不存在"
        except Exception as e:
            tests["config_file"]["error"] = str(e)
        
        return tests
    
    def full_setup(self) -> Dict:
        """完整安装流程"""
        print("🚀 开始Git自动提交和项目保护机制安装...")
        print("=" * 60)
        
        results = {
            "config_update": None,
            "git_hooks_install": None,
            "backup_directories": None,
            "system_test": None,
            "overall_success": False
        }
        
        # 1. 更新配置文件
        print("📝 步骤1: 更新配置文件...")
        results["config_update"] = self.update_config_file()
        
        # 2. 安装Git hooks
        print("🔗 步骤2: 安装Git hooks...")
        results["git_hooks_install"] = self.install_git_hooks()
        
        # 3. 创建备份目录
        print("📁 步骤3: 创建备份目录...")
        results["backup_directories"] = self.create_backup_directories()
        
        # 4. 测试系统组件
        print("🧪 步骤4: 测试系统组件...")
        results["system_test"] = self.test_system_components()
        
        # 5. 评估整体成功状态
        success_count = 0
        total_count = 0
        
        for key, result in results.items():
            if key == "overall_success":
                continue
                
            total_count += 1
            if isinstance(result, dict) and result.get("success", False):
                success_count += 1
            elif key == "system_test":
                # 对于系统测试，检查通过的测试数量
                if isinstance(result, dict):
                    passed_tests = sum(1 for test in result.values() if test.get("passed", False))
                    if passed_tests >= len(result) * 0.75:  # 75%的测试通过就算成功
                        success_count += 1
        
        results["overall_success"] = success_count >= total_count * 0.75
        
        # 6. 显示结果
        print("\n" + "=" * 60)
        print("📊 安装结果总结")
        print("=" * 60)
        
        if results["overall_success"]:
            print("✅ Git自动提交和项目保护机制安装成功！")
        else:
            print("⚠️ 安装过程中遇到一些问题，请检查详细信息")
        
        print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        return results
    
    def generate_setup_report(self, results: Dict) -> str:
        """生成安装报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.project_root / "ai_reports" / "audit" / f"ai_report_{timestamp}_audit_git_protection_setup.md"
        
        # 确保目录存在
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        report_content = f"""# Git自动提交和项目保护机制安装报告

**安装时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**安装状态**: {'✅ 成功' if results['overall_success'] else '⚠️ 部分成功'}

## 安装步骤结果

### 1. 配置文件更新
- **状态**: {'✅ 成功' if results['config_update']['success'] else '❌ 失败'}
- **备份路径**: {results['config_update'].get('backup_path', 'N/A')}
- **错误信息**: {results['config_update'].get('error', 'N/A')}

### 2. Git Hooks安装
- **状态**: {'✅ 成功' if results['git_hooks_install']['success'] else '❌ 失败'}
- **错误信息**: {results['git_hooks_install'].get('error', 'N/A')}

### 3. 备份目录创建
- **状态**: {'✅ 成功' if results['backup_directories']['success'] else '❌ 失败'}
- **创建目录数**: {len(results['backup_directories'].get('directories', []))}
- **错误信息**: {results['backup_directories'].get('error', 'N/A')}

### 4. 系统组件测试
"""
        
        if results['system_test']:
            for component, test_result in results['system_test'].items():
                status = '✅ 通过' if test_result['passed'] else '❌ 失败'
                error = test_result.get('error', 'N/A')
                report_content += f"- **{component}**: {status} - {error}\n"
        
        report_content += f"""
## 下一步操作

### 如果安装成功
1. 运行系统测试验证功能
2. 配置定时任务（可选）
3. 阅读操作手册了解日常使用

### 如果安装失败
1. 检查错误信息并解决问题
2. 重新运行安装程序
3. 查看详细日志文件

## 相关文件
- 操作手册: `ai_reports/audit/ai_report_20250718_audit_git_protection_manual.md`
- 配置模板: `ai_templates/config/ai_template_20250718_config_git_protection_settings.yml`
- Git工具: `ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py`
- 保护工具: `ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py`

---
*此报告由Git保护机制安装工具自动生成*
"""
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📋 安装报告已生成: {report_path}")
        return str(report_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Git自动提交和项目保护机制安装工具')
    parser.add_argument('action', choices=['setup', 'config', 'hooks', 'test'],
                       help='执行的操作')
    parser.add_argument('--force', action='store_true', help='强制执行')
    
    args = parser.parse_args()
    
    setup = GitProtectionSetup()
    
    if args.action == 'setup':
        results = setup.full_setup()
        report_path = setup.generate_setup_report(results)
        print(f"\n📋 详细报告: {report_path}")
    
    elif args.action == 'config':
        result = setup.update_config_file()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif args.action == 'hooks':
        result = setup.install_git_hooks()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    
    elif args.action == 'test':
        results = setup.test_system_components()
        print(json.dumps(results, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
