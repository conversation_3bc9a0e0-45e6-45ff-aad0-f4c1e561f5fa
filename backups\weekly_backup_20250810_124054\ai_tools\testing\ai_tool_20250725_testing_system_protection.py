#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 测试系统保护机制
清理条件: 长期保留，用于验证保护

系统保护机制测试工具
==================

测试资源监控、自动保护和紧急停止机制，验证是否能有效防止浏览器过载。
"""

import os
import sys
import time
import psutil

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.services.resource_monitor import ResourceThresholds
from src.qianchuan_aw.services.system_protection import (
    get_protection_manager,
    initialize_system_protection,
    start_system_protection,
    stop_system_protection,
    force_emergency_stop,
    get_protection_status
)


class SystemProtectionTester:
    """系统保护测试器"""
    
    def __init__(self):
        self.test_results = []
        self.protection_manager = None
    
    def test_protection_initialization(self):
        """测试保护机制初始化"""
        logger.info("🧪 测试1: 保护机制初始化...")
        
        try:
            # 设置测试阈值（较低的阈值便于测试）
            test_thresholds = ResourceThresholds(
                cpu_warning=30.0,
                cpu_critical=50.0,
                memory_warning=40.0,
                memory_critical=60.0,
                browser_warning=3,
                browser_critical=5
            )
            
            # 初始化保护机制
            success = initialize_system_protection(test_thresholds)
            
            if success:
                logger.info("✅ 保护机制初始化成功")
                self.protection_manager = get_protection_manager()
                
                self.test_results.append({
                    'test': 'protection_initialization',
                    'status': 'success',
                    'message': '保护机制初始化成功'
                })
                return True
            else:
                logger.error("❌ 保护机制初始化失败")
                self.test_results.append({
                    'test': 'protection_initialization',
                    'status': 'failed',
                    'message': '保护机制初始化失败'
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 保护机制初始化测试失败: {e}")
            self.test_results.append({
                'test': 'protection_initialization',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def test_resource_monitoring(self):
        """测试资源监控"""
        logger.info("🧪 测试2: 资源监控...")
        
        try:
            # 启动保护（短间隔便于测试）
            success = start_system_protection(monitor_interval=2)
            
            if success:
                logger.info("✅ 资源监控启动成功")
                
                # 等待几个监控周期
                logger.info("等待监控数据收集...")
                time.sleep(6)
                
                # 检查监控状态
                status = get_protection_status()
                monitor_stats = status.get('monitor_stats', {})
                
                if monitor_stats.get('total_checks', 0) > 0:
                    logger.info(f"✅ 监控正常运行，已执行 {monitor_stats['total_checks']} 次检查")
                    
                    current_metrics = monitor_stats.get('current_metrics')
                    if current_metrics:
                        logger.info(f"   当前CPU: {current_metrics['cpu_percent']:.1f}%")
                        logger.info(f"   当前内存: {current_metrics['memory_percent']:.1f}%")
                        logger.info(f"   浏览器进程: {current_metrics['browser_processes']}")
                    
                    self.test_results.append({
                        'test': 'resource_monitoring',
                        'status': 'success',
                        'message': f'监控正常，已执行 {monitor_stats["total_checks"]} 次检查'
                    })
                    return True
                else:
                    logger.warning("⚠️ 监控数据收集异常")
                    self.test_results.append({
                        'test': 'resource_monitoring',
                        'status': 'warning',
                        'message': '监控启动但数据收集异常'
                    })
                    return True
            else:
                logger.error("❌ 资源监控启动失败")
                self.test_results.append({
                    'test': 'resource_monitoring',
                    'status': 'failed',
                    'message': '资源监控启动失败'
                })
                return False
                
        except Exception as e:
            logger.error(f"❌ 资源监控测试失败: {e}")
            self.test_results.append({
                'test': 'resource_monitoring',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def test_threshold_detection(self):
        """测试阈值检测"""
        logger.info("🧪 测试3: 阈值检测...")
        
        try:
            if not self.protection_manager:
                logger.error("❌ 保护管理器未初始化")
                return False
            
            # 强制执行一次检查
            check_result = self.protection_manager.resource_monitor.force_check()
            
            metrics = check_result['metrics']
            alerts = check_result['alerts']
            
            logger.info(f"强制检查结果:")
            logger.info(f"   CPU: {metrics['cpu_percent']:.1f}%")
            logger.info(f"   内存: {metrics['memory_percent']:.1f}%")
            logger.info(f"   浏览器进程: {metrics['browser_processes']}")
            logger.info(f"   警告: {alerts['is_warning']}")
            logger.info(f"   严重: {alerts['is_critical']}")
            
            if alerts['is_warning'] or alerts['is_critical']:
                logger.info(f"✅ 阈值检测正常，触发了告警")
                alert_messages = alerts['warning'] + alerts['critical']
                self.test_results.append({
                    'test': 'threshold_detection',
                    'status': 'success',
                    'message': f'阈值检测正常，触发告警: {", ".join(alert_messages)}'
                })
            else:
                logger.info("✅ 阈值检测正常，当前系统状态良好")
                self.test_results.append({
                    'test': 'threshold_detection',
                    'status': 'success',
                    'message': '阈值检测正常，系统状态良好'
                })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 阈值检测测试失败: {e}")
            self.test_results.append({
                'test': 'threshold_detection',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def test_emergency_stop_mechanism(self):
        """测试紧急停止机制"""
        logger.info("🧪 测试4: 紧急停止机制...")
        
        try:
            # 记录测试前状态
            before_status = get_protection_status()
            before_emergency_stops = before_status['protection_stats']['emergency_stops']
            
            logger.info("触发紧急停止测试...")
            force_emergency_stop()
            
            # 等待紧急停止处理
            time.sleep(3)
            
            # 检查测试后状态
            after_status = get_protection_status()
            after_emergency_stops = after_status['protection_stats']['emergency_stops']
            
            if after_emergency_stops > before_emergency_stops:
                logger.info("✅ 紧急停止机制正常工作")
                logger.info(f"   紧急停止次数: {before_emergency_stops} -> {after_emergency_stops}")
                
                if after_status['emergency_mode']:
                    logger.info("   系统已进入紧急模式")
                
                self.test_results.append({
                    'test': 'emergency_stop_mechanism',
                    'status': 'success',
                    'message': f'紧急停止机制正常，触发了 {after_emergency_stops - before_emergency_stops} 次紧急停止'
                })
                return True
            else:
                logger.warning("⚠️ 紧急停止机制可能未正常工作")
                self.test_results.append({
                    'test': 'emergency_stop_mechanism',
                    'status': 'warning',
                    'message': '紧急停止机制触发但统计未更新'
                })
                return True
                
        except Exception as e:
            logger.error(f"❌ 紧急停止机制测试失败: {e}")
            self.test_results.append({
                'test': 'emergency_stop_mechanism',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def test_protection_statistics(self):
        """测试保护统计"""
        logger.info("🧪 测试5: 保护统计...")
        
        try:
            status = get_protection_status()
            
            logger.info("保护统计信息:")
            logger.info(f"   保护启用: {status['protection_enabled']}")
            logger.info(f"   紧急模式: {status['emergency_mode']}")
            
            protection_stats = status['protection_stats']
            logger.info(f"   警告操作: {protection_stats['warning_actions']}")
            logger.info(f"   严重操作: {protection_stats['critical_actions']}")
            logger.info(f"   紧急停止: {protection_stats['emergency_stops']}")
            
            monitor_stats = status.get('monitor_stats', {})
            if monitor_stats:
                logger.info(f"   监控检查: {monitor_stats.get('total_checks', 0)}")
                logger.info(f"   警告触发: {monitor_stats.get('warning_triggers', 0)}")
                logger.info(f"   严重触发: {monitor_stats.get('critical_triggers', 0)}")
            
            # 检查统计数据的完整性
            required_fields = ['protection_enabled', 'emergency_mode', 'protection_stats']
            missing_fields = [field for field in required_fields if field not in status]
            
            if not missing_fields:
                logger.info("✅ 保护统计数据完整")
                self.test_results.append({
                    'test': 'protection_statistics',
                    'status': 'success',
                    'message': '保护统计数据完整且正常'
                })
                return True
            else:
                logger.warning(f"⚠️ 保护统计数据缺少字段: {missing_fields}")
                self.test_results.append({
                    'test': 'protection_statistics',
                    'status': 'warning',
                    'message': f'统计数据缺少字段: {missing_fields}'
                })
                return True
                
        except Exception as e:
            logger.error(f"❌ 保护统计测试失败: {e}")
            self.test_results.append({
                'test': 'protection_statistics',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def test_system_recovery(self):
        """测试系统恢复"""
        logger.info("🧪 测试6: 系统恢复...")
        
        try:
            # 检查当前系统状态
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            logger.info(f"当前系统状态:")
            logger.info(f"   CPU使用率: {cpu_percent:.1f}%")
            logger.info(f"   内存使用率: {memory.percent:.1f}%")
            
            # 检查是否有浏览器进程泄露
            browser_count = self._count_browser_processes()
            logger.info(f"   浏览器进程数: {browser_count}")
            
            # 评估系统恢复状态
            recovery_good = (
                cpu_percent < 50 and
                memory.percent < 70 and
                browser_count < 10
            )
            
            if recovery_good:
                logger.info("✅ 系统恢复状态良好")
                self.test_results.append({
                    'test': 'system_recovery',
                    'status': 'success',
                    'message': f'系统恢复良好: CPU={cpu_percent:.1f}%, 内存={memory.percent:.1f}%, 浏览器={browser_count}'
                })
            else:
                logger.warning("⚠️ 系统恢复状态需要关注")
                self.test_results.append({
                    'test': 'system_recovery',
                    'status': 'warning',
                    'message': f'系统恢复需关注: CPU={cpu_percent:.1f}%, 内存={memory.percent:.1f}%, 浏览器={browser_count}'
                })
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统恢复测试失败: {e}")
            self.test_results.append({
                'test': 'system_recovery',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def _count_browser_processes(self) -> int:
        """统计浏览器进程"""
        try:
            browser_count = 0
            browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
            
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        browser_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return browser_count
        except:
            return 0
    
    def cleanup_test(self):
        """清理测试"""
        logger.info("🧹 清理测试环境...")
        
        try:
            # 停止保护机制
            stop_system_protection()
            logger.info("✅ 保护机制已停止")
            
        except Exception as e:
            logger.error(f"清理测试环境失败: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 生成系统保护测试报告...")
        
        success_count = sum(1 for r in self.test_results if r['status'] == 'success')
        warning_count = sum(1 for r in self.test_results if r['status'] == 'warning')
        failed_count = sum(1 for r in self.test_results if r['status'] == 'failed')
        total_count = len(self.test_results)
        
        report = f"""
系统保护机制测试报告
==================

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

测试结果概览:
- 总测试数: {total_count}
- 成功测试: {success_count}
- 警告测试: {warning_count}
- 失败测试: {failed_count}
- 成功率: {success_count/total_count*100:.1f}%

详细测试结果:
"""
        
        for result in self.test_results:
            if result['status'] == 'success':
                status_icon = "✅"
            elif result['status'] == 'warning':
                status_icon = "⚠️"
            else:
                status_icon = "❌"
            
            report += f"- {status_icon} {result['test']}: {result['message']}\n"
        
        report += f"""
系统保护评估:
"""
        
        if failed_count == 0:
            if warning_count == 0:
                report += "🎉 所有测试通过，系统保护机制完全正常！\n"
                report += "✅ 可以安全启用提审功能，系统具备完善的过载保护。\n"
            else:
                report += "✅ 核心保护功能正常，有少量警告项需要关注。\n"
                report += "✅ 可以启用提审功能，但需要监控警告项。\n"
        elif failed_count <= total_count * 0.2:
            report += "⚠️ 大部分保护功能正常，少量失败项需要修复。\n"
            report += "⚠️ 建议修复失败项后再启用提审功能。\n"
        else:
            report += "❌ 多个保护功能失败，系统保护机制需要进一步修复。\n"
            report += "❌ 不建议启用提审功能，直到保护机制完全修复。\n"
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'system_protection_test_report_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 系统保护测试报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚀 开始系统保护机制测试...")
    
    tester = SystemProtectionTester()
    
    try:
        # 执行所有测试
        logger.info("=" * 50)
        logger.info("开始执行系统保护测试...")
        
        tester.test_protection_initialization()
        tester.test_resource_monitoring()
        tester.test_threshold_detection()
        tester.test_emergency_stop_mechanism()
        tester.test_protection_statistics()
        tester.test_system_recovery()
        
        # 清理测试环境
        tester.cleanup_test()
        
        # 生成报告
        logger.info("=" * 50)
        report = tester.generate_test_report()
        
        logger.info("🎯 系统保护测试完成！")
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
