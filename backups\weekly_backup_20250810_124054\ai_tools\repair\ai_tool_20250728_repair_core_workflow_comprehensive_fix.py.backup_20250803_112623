#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 修复工具
生命周期: 永久保留
创建目的: 千川自动化项目核心工作流全面修复方案
清理条件: 核心工作流稳定运行后可归档
"""

import os
import sys
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign, AdAccount, Principal
from qianchuan_aw.utils.config_loader import load_settings
from qianchuan_aw.sdk_qc.client import QianchuanClient
from sqlalchemy import text, func
from sqlalchemy.orm import joinedload


class RepairPriority(Enum):
    """修复优先级"""
    CRITICAL = 1    # 严重问题，立即修复
    HIGH = 2        # 高优先级，优先修复
    MEDIUM = 3      # 中等优先级，计划修复
    LOW = 4         # 低优先级，可延后修复


@dataclass
class RepairTask:
    """修复任务"""
    name: str
    priority: RepairPriority
    description: str
    affected_count: int
    estimated_time: str
    repair_function: str
    dependencies: List[str] = None


class CoreWorkflowRepair:
    """核心工作流修复器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.repair_stats = {
            'total_repaired': 0,
            'campaigns_created': 0,
            'status_fixed': 0,
            'errors_encountered': 0
        }
        
        # 定义修复任务
        self.repair_tasks = [
            RepairTask(
                name="修复遗漏的测试计划",
                priority=RepairPriority.CRITICAL,
                description="为3177个已上传但未创建测试计划的素材创建测试计划",
                affected_count=3177,
                estimated_time="2-4小时",
                repair_function="repair_missing_campaigns"
            ),
            RepairTask(
                name="修复状态不一致问题",
                priority=RepairPriority.HIGH,
                description="修复64个testing_pending_review状态但无测试计划的素材",
                affected_count=64,
                estimated_time="30分钟",
                repair_function="repair_status_inconsistency"
            ),
            RepairTask(
                name="重试失败的上传",
                priority=RepairPriority.MEDIUM,
                description="重新处理28个上传失败的素材",
                affected_count=28,
                estimated_time="1小时",
                repair_function="retry_failed_uploads"
            ),
            RepairTask(
                name="验证工作流完整性",
                priority=RepairPriority.HIGH,
                description="验证修复后的工作流完整性",
                affected_count=0,
                estimated_time="30分钟",
                repair_function="verify_workflow_integrity"
            )
        ]
    
    def run_comprehensive_repair(self) -> Dict[str, Any]:
        """运行全面修复"""
        logger.critical("🔧 开始千川自动化项目核心工作流全面修复")
        logger.critical("=" * 80)
        
        # 按优先级排序任务
        sorted_tasks = sorted(self.repair_tasks, key=lambda x: x.priority.value)
        
        repair_results = []
        
        for task in sorted_tasks:
            logger.critical(f"🎯 执行修复任务: {task.name}")
            logger.info(f"   描述: {task.description}")
            logger.info(f"   影响数量: {task.affected_count}")
            logger.info(f"   预计时间: {task.estimated_time}")
            
            try:
                # 执行修复函数
                repair_function = getattr(self, task.repair_function)
                result = repair_function()
                
                repair_results.append({
                    'task': task.name,
                    'status': 'SUCCESS',
                    'result': result
                })
                
                logger.success(f"✅ 修复任务完成: {task.name}")
                
            except Exception as e:
                logger.error(f"❌ 修复任务失败: {task.name} - {e}", exc_info=True)
                repair_results.append({
                    'task': task.name,
                    'status': 'FAILED',
                    'error': str(e)
                })
                
                # 对于关键任务，如果失败则停止后续修复
                if task.priority == RepairPriority.CRITICAL:
                    logger.critical(f"🚨 关键修复任务失败，停止后续修复")
                    break
        
        # 生成修复报告
        return self._generate_repair_report(repair_results)
    
    def repair_missing_campaigns(self) -> Dict[str, Any]:
        """修复遗漏的测试计划"""
        logger.info("🔧 开始修复遗漏的测试计划...")
        
        with SessionLocal() as db:
            # 查找所有已上传但未创建测试计划的素材
            missing_campaigns_query = text("""
                SELECT lc.id, lc.file_path, lc.status, pc.material_id_qc, pc.account_id
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE cpc.campaign_id IS NULL
                ORDER BY lc.created_at DESC
                LIMIT 100  -- 分批处理，避免超时
            """)
            
            missing_materials = db.execute(missing_campaigns_query).fetchall()
            
            if not missing_materials:
                return {'message': '没有发现遗漏的测试计划', 'created_count': 0}
            
            logger.info(f"发现 {len(missing_materials)} 个遗漏的测试计划，开始修复...")
            
            # 获取测试账户
            test_account = self._get_test_account(db)
            if not test_account:
                raise Exception("未找到可用的测试账户")
            
            # 初始化千川客户端
            app_id = self.app_settings['api_credentials']['app_id']
            secret = self.app_settings['api_credentials']['secret']

            client = QianchuanClient(
                app_id=app_id,
                secret=secret,
                principal_id=test_account.principal.id
            )
            
            created_count = 0
            error_count = 0
            
            for material in missing_materials:
                try:
                    # 创建测试计划
                    campaign_result = self._create_test_campaign_for_material(
                        client, db, material, test_account
                    )
                    
                    if campaign_result:
                        created_count += 1
                        logger.info(f"✅ 为素材 {material.material_id_qc} 创建测试计划: {campaign_result['campaign_id']}")
                    
                    # 添加延迟避免API限流
                    time.sleep(2)
                    
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 为素材 {material.material_id_qc} 创建测试计划失败: {e}")
                    
                    # 如果错误过多，停止处理
                    if error_count > 10:
                        logger.warning("错误过多，停止批量处理")
                        break
            
            self.repair_stats['campaigns_created'] += created_count
            
            return {
                'message': f'成功创建 {created_count} 个测试计划',
                'created_count': created_count,
                'error_count': error_count,
                'processed_count': len(missing_materials)
            }
    
    def repair_status_inconsistency(self) -> Dict[str, Any]:
        """修复状态不一致问题"""
        logger.info("🔧 开始修复状态不一致问题...")
        
        with SessionLocal() as db:
            # 查找testing_pending_review状态但无测试计划的素材
            inconsistent_materials = db.execute(text("""
                SELECT lc.id, lc.file_path, pc.id as pc_id
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL
            """)).fetchall()
            
            if not inconsistent_materials:
                return {'message': '没有发现状态不一致问题', 'fixed_count': 0}
            
            logger.info(f"发现 {len(inconsistent_materials)} 个状态不一致的素材，开始修复...")
            
            # 将这些素材状态重置为uploaded_pending_plan，让正常工作流处理
            fixed_count = 0
            for material in inconsistent_materials:
                try:
                    db.execute(text("""
                        UPDATE local_creatives 
                        SET status = 'uploaded_pending_plan', updated_at = NOW()
                        WHERE id = :material_id
                    """), {"material_id": material.id})
                    
                    fixed_count += 1
                    
                except Exception as e:
                    logger.error(f"修复素材 {material.id} 状态失败: {e}")
            
            db.commit()
            self.repair_stats['status_fixed'] += fixed_count
            
            return {
                'message': f'成功修复 {fixed_count} 个状态不一致问题',
                'fixed_count': fixed_count
            }
    
    def retry_failed_uploads(self) -> Dict[str, Any]:
        """重试失败的上传"""
        logger.info("🔧 开始重试失败的上传...")
        
        with SessionLocal() as db:
            # 查找upload_failed状态的素材
            failed_uploads = db.execute(text("""
                SELECT id, file_path, status
                FROM local_creatives
                WHERE status = 'upload_failed'
                ORDER BY updated_at DESC
                LIMIT 50
            """)).fetchall()
            
            if not failed_uploads:
                return {'message': '没有发现失败的上传', 'retried_count': 0}
            
            logger.info(f"发现 {len(failed_uploads)} 个失败的上传，开始重试...")
            
            # 将状态重置为pending_upload，让工作流重新处理
            retried_count = 0
            for upload in failed_uploads:
                try:
                    db.execute(text("""
                        UPDATE local_creatives 
                        SET status = 'pending_upload', updated_at = NOW()
                        WHERE id = :material_id
                    """), {"material_id": upload.id})
                    
                    retried_count += 1
                    
                except Exception as e:
                    logger.error(f"重置素材 {upload.id} 状态失败: {e}")
            
            db.commit()
            
            return {
                'message': f'成功重置 {retried_count} 个失败上传的状态',
                'retried_count': retried_count
            }
    
    def verify_workflow_integrity(self) -> Dict[str, Any]:
        """验证工作流完整性"""
        logger.info("🔧 开始验证工作流完整性...")
        
        with SessionLocal() as db:
            # 重新统计遗漏的测试计划
            remaining_missing = db.execute(text("""
                SELECT COUNT(*) as count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE cpc.campaign_id IS NULL
            """)).scalar()
            
            # 统计状态不一致问题
            status_inconsistent = db.execute(text("""
                SELECT COUNT(*) as count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL
            """)).scalar()
            
            # 计算修复效果
            original_missing = 3177  # 从诊断报告获取
            fixed_campaigns = original_missing - remaining_missing
            fix_rate = (fixed_campaigns / original_missing) * 100 if original_missing > 0 else 0
            
            integrity_result = {
                'original_missing_campaigns': original_missing,
                'remaining_missing_campaigns': remaining_missing,
                'fixed_campaigns': fixed_campaigns,
                'fix_rate_percentage': round(fix_rate, 2),
                'remaining_status_inconsistent': status_inconsistent,
                'workflow_health': 'GOOD' if remaining_missing < 100 else 'NEEDS_ATTENTION'
            }
            
            logger.info(f"工作流完整性验证完成:")
            logger.info(f"  原始遗漏计划: {original_missing}")
            logger.info(f"  剩余遗漏计划: {remaining_missing}")
            logger.info(f"  修复成功率: {fix_rate:.2f}%")
            logger.info(f"  工作流健康状态: {integrity_result['workflow_health']}")
            
            return integrity_result
    
    def _get_test_account(self, db) -> Optional[AdAccount]:
        """获取测试账户"""
        # 查找可用的测试账户
        test_account = db.query(AdAccount).filter(
            AdAccount.account_id_qc == '****************'  # 使用之前成功的测试账户
        ).options(joinedload(AdAccount.principal)).first()
        
        return test_account
    
    def _create_test_campaign_for_material(self, client, db, material, test_account) -> Optional[Dict[str, Any]]:
        """为素材创建测试计划"""
        try:
            # 构建测试计划参数（使用之前成功的参数格式）
            plan_config = {
                'advertiser_id': int(test_account.account_id_qc),
                'name': f"修复-{material.material_id_qc}-{datetime.now().strftime('%m%d%H%M')}",
                'operation_type': 'LIVE_PROM',
                'marketing_goal': 'LIVE_PROM_GOODS',
                'campaign_scene': 'DAILY_SALE',
                'marketing_scene': 'FEED',
                'lab_ad_type': 'NOT_LAB_AD',
                'creative_material_mode': 'PROGRAMMATIC_CREATIVE',
                'is_homepage_hide': 1,
                'aweme_id': 7410578812859860027,
                'delivery_setting': {
                    "budget_mode": "BUDGET_MODE_DAY",
                    "budget": 5000,
                    "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY",
                    "live_schedule_type": "SCHEDULE_FROM_NOW",
                    "smart_bid_type": "SMART_BID_CUSTOM",
                    "cpa_bid": 1000
                },
                'audience': {
                    "gender": "GENDER_UNLIMITED",
                    "age": ["AGE_BETWEEN_18_23", "AGE_BETWEEN_24_30", "AGE_BETWEEN_31_40"],
                    "region_version": "REGION_VERSION_NEW",
                    "city": [],
                    "location_type": "CURRENT"
                },
                'programmatic_creative_media_list': [{
                    "video_id": material.material_id_qc,
                    "image_mode": "CREATIVE_IMAGE_MODE_VIDEO_TYPE"
                }],
                'programmatic_creative_title_list': [{
                    "title": f"精选推荐好物，直播间限时优惠！",
                    "title_type": "CUSTOM"
                }]
            }
            
            # 调用API创建计划
            result = client.create_ad_plan(plan_config=plan_config)
            
            if result and result.get('ad_id'):
                # 保存到数据库
                campaign = Campaign(
                    campaign_id_qc=result['ad_id'],
                    account_id=test_account.id,
                    status='AUDITING',
                    created_at=datetime.now()
                )
                db.add(campaign)
                
                # 创建关联关系
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material.material_id_qc
                ).first()
                
                if pc:
                    db.execute(text("""
                        INSERT INTO campaign_platform_creative_association (campaign_id, platform_creative_id)
                        VALUES (:campaign_id, :pc_id)
                    """), {"campaign_id": campaign.id, "pc_id": pc.id})
                
                db.commit()
                
                return {
                    'campaign_id': result['ad_id'],
                    'status': 'success'
                }
            
            return None
            
        except Exception as e:
            logger.error(f"创建测试计划失败: {e}")
            db.rollback()
            return None
    
    def _generate_repair_report(self, repair_results: List[Dict]) -> Dict[str, Any]:
        """生成修复报告"""
        successful_repairs = [r for r in repair_results if r['status'] == 'SUCCESS']
        failed_repairs = [r for r in repair_results if r['status'] == 'FAILED']
        
        report = {
            'repair_time': datetime.now().isoformat(),
            'total_tasks': len(repair_results),
            'successful_tasks': len(successful_repairs),
            'failed_tasks': len(failed_repairs),
            'repair_stats': self.repair_stats,
            'task_results': repair_results,
            'overall_status': 'SUCCESS' if len(failed_repairs) == 0 else 'PARTIAL_SUCCESS' if len(successful_repairs) > 0 else 'FAILED'
        }
        
        return report


def main():
    """主函数"""
    try:
        repair = CoreWorkflowRepair()
        report = repair.run_comprehensive_repair()
        
        # 输出修复结果
        logger.critical("🎯 核心工作流修复完成")
        logger.critical("=" * 80)
        logger.critical(f"📊 总计执行 {report['total_tasks']} 个修复任务")
        logger.critical(f"✅ 成功任务: {report['successful_tasks']} 个")
        logger.critical(f"❌ 失败任务: {report['failed_tasks']} 个")
        logger.critical(f"🎯 整体状态: {report['overall_status']}")
        
        # 保存详细报告
        report_path = f"ai_reports/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_core_workflow_repair.json"
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.success(f"📄 详细修复报告已保存: {report_path}")
        
        return report
        
    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}", exc_info=True)
        return None


if __name__ == "__main__":
    main()
