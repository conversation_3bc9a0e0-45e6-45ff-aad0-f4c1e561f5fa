#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 清理Redis队列中的残留Celery任务
清理条件: 成为紧急清理工具，长期保留
"""

import os
import sys
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class RedisCeleryQueueCleaner:
    """Redis Celery队列清理器"""
    
    def __init__(self):
        self.redis_client = None
        self.cleared_keys = []
        self.errors = []
    
    def clear_all_celery_queues(self):
        """清理所有Celery队列"""
        logger.info("🧹 清理Redis中的Celery队列")
        logger.info("="*80)
        
        try:
            # 连接Redis
            import redis
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            
            # 测试连接
            self.redis_client.ping()
            logger.success("✅ Redis连接成功")
            
            # 清理Celery相关队列
            self._clear_celery_queues()
            
            # 清理任务结果
            self._clear_task_results()
            
            # 清理Beat调度状态
            self._clear_beat_schedule()
            
            # 生成清理报告
            self._generate_cleanup_report()
            
        except redis.ConnectionError:
            logger.error("❌ 无法连接到Redis服务器")
            self.errors.append("Redis连接失败")
        except ImportError:
            logger.error("❌ Redis模块不可用")
            self.errors.append("Redis模块不可用")
        except Exception as e:
            logger.error(f"❌ 清理队列失败: {e}")
            self.errors.append(f"清理失败: {e}")
    
    def _clear_celery_queues(self):
        """清理Celery任务队列"""
        logger.info("🗑️ 清理Celery任务队列...")
        
        # Celery默认队列名
        celery_queues = [
            'celery',           # 默认队列
            'celery.pidbox',    # 管理队列
            '_kombu.binding.*', # Kombu绑定
        ]
        
        # 获取所有匹配的键
        all_keys = self.redis_client.keys('*')
        celery_keys = []
        
        for key in all_keys:
            if any(queue in key.lower() for queue in ['celery', 'kombu']):
                celery_keys.append(key)
        
        if celery_keys:
            # 删除所有Celery相关键
            deleted_count = self.redis_client.delete(*celery_keys)
            logger.success(f"✅ 删除了 {deleted_count} 个Celery队列键")
            self.cleared_keys.extend(celery_keys)
        else:
            logger.info("ℹ️ 没有找到Celery队列键")
    
    def _clear_task_results(self):
        """清理任务结果"""
        logger.info("🗑️ 清理任务结果...")
        
        # 查找任务结果键
        result_keys = self.redis_client.keys('celery-task-meta-*')
        
        if result_keys:
            deleted_count = self.redis_client.delete(*result_keys)
            logger.success(f"✅ 删除了 {deleted_count} 个任务结果")
            self.cleared_keys.extend(result_keys)
        else:
            logger.info("ℹ️ 没有找到任务结果")
    
    def _clear_beat_schedule(self):
        """清理Beat调度状态"""
        logger.info("🗑️ 清理Beat调度状态...")
        
        # 查找Beat相关键
        beat_keys = self.redis_client.keys('*beat*')
        
        if beat_keys:
            deleted_count = self.redis_client.delete(*beat_keys)
            logger.success(f"✅ 删除了 {deleted_count} 个Beat调度键")
            self.cleared_keys.extend(beat_keys)
        else:
            logger.info("ℹ️ 没有找到Beat调度键")
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        logger.info("\n📋 Redis队列清理报告")
        logger.info("="*80)
        
        if self.cleared_keys:
            logger.success(f"✅ 成功清理 {len(self.cleared_keys)} 个Redis键")
            logger.info("🗑️ 清理的键类型:")
            
            # 按类型分组显示
            queue_keys = [k for k in self.cleared_keys if 'celery' in k and 'meta' not in k]
            result_keys = [k for k in self.cleared_keys if 'meta' in k]
            beat_keys = [k for k in self.cleared_keys if 'beat' in k]
            
            if queue_keys:
                logger.info(f"   📤 任务队列: {len(queue_keys)} 个")
            if result_keys:
                logger.info(f"   📊 任务结果: {len(result_keys)} 个")
            if beat_keys:
                logger.info(f"   ⏰ Beat调度: {len(beat_keys)} 个")
        else:
            logger.info("ℹ️ 没有需要清理的键")
        
        if self.errors:
            logger.error(f"❌ 清理过程中的错误 ({len(self.errors)} 个):")
            for error in self.errors:
                logger.error(f"   ❌ {error}")
        
        # 验证清理效果
        remaining_keys = self.redis_client.keys('*celery*')
        if remaining_keys:
            logger.warning(f"⚠️ 仍有 {len(remaining_keys)} 个Celery相关键")
        else:
            logger.success("✅ 所有Celery队列已完全清理")
        
        return {
            'cleared_count': len(self.cleared_keys),
            'error_count': len(self.errors),
            'remaining_keys': len(remaining_keys) if 'remaining_keys' in locals() else 0,
            'cleanup_success': len(self.errors) == 0
        }


def main():
    """主函数"""
    cleaner = RedisCeleryQueueCleaner()
    cleaner.clear_all_celery_queues()
    
    if cleaner.errors:
        logger.error("❌ Redis队列清理遇到问题")
        return 1
    else:
        logger.success("✅ Redis队列清理完成")
        logger.success("🚀 现在可以安全重启Celery服务")
        return 0


if __name__ == "__main__":
    exit(main())
