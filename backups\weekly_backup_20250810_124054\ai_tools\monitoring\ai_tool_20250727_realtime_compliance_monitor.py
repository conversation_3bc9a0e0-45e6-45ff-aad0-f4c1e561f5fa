#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 实时合规性监控系统
清理条件: 集成到主系统后可删除

千川自动化实时合规性监控系统
========================

基于发现的155个违规素材、445个重复计划的严重问题，
建立实时监控系统，24/7监控素材唯一性测试铁律的执行情况。
"""

import sys
import os
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount
from sqlalchemy import text


class ComplianceAlert:
    """合规性警报"""
    
    def __init__(self, alert_type: str, severity: str, message: str, details: Dict[str, Any]):
        self.alert_type = alert_type
        self.severity = severity  # CRITICAL, HIGH, MEDIUM, LOW
        self.message = message
        self.details = details
        self.timestamp = datetime.now()
        self.alert_id = f"{alert_type}_{int(self.timestamp.timestamp())}"


class RealTimeComplianceMonitor:
    """实时合规性监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.alert_history = []
        self.last_check_time = None
        self.check_interval = 60  # 检查间隔（秒）
        self.alert_threshold = {
            'new_violations': 1,  # 发现新违规立即报警
            'total_violations': 5,  # 总违规数超过5个报警
            'violation_growth_rate': 0.1  # 违规增长率超过10%报警
        }
        self.notification_config = {
            'email_enabled': False,
            'webhook_enabled': False,
            'log_enabled': True
        }
    
    def detect_violations(self) -> List[Dict[str, Any]]:
        """检测违规情况"""
        try:
            with database_session() as db:
                # 检测素材重复使用违规
                query = text("""
                    SELECT 
                        lc.id as material_id,
                        lc.filename,
                        lc.file_hash,
                        lc.status as material_status,
                        lc.created_at as material_created,
                        COUNT(DISTINCT c.id) as plan_count,
                        STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids,
                        STRING_AGG(DISTINCT c.status, ', ') as plan_statuses,
                        MIN(c.created_at) as first_plan_created,
                        MAX(c.created_at) as last_plan_created
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE aa.account_type = 'TEST'
                    GROUP BY lc.id, lc.filename, lc.file_hash, lc.status, lc.created_at
                    HAVING COUNT(DISTINCT c.id) > 1
                    ORDER BY plan_count DESC, last_plan_created DESC
                """)
                
                result = db.execute(query)
                violations = []
                
                for row in result:
                    violations.append({
                        'violation_type': '素材重复使用违规',
                        'material_id': row.material_id,
                        'filename': row.filename,
                        'file_hash': row.file_hash,
                        'material_status': row.material_status,
                        'material_created': row.material_created,
                        'plan_count': row.plan_count,
                        'plan_ids': row.plan_ids.split(', ') if row.plan_ids else [],
                        'plan_statuses': row.plan_statuses.split(', ') if row.plan_statuses else [],
                        'first_plan_created': row.first_plan_created,
                        'last_plan_created': row.last_plan_created,
                        'violation_duration': (row.last_plan_created - row.first_plan_created).total_seconds() if row.last_plan_created and row.first_plan_created else 0
                    })
                
                return violations
                
        except Exception as e:
            logger.error(f"违规检测失败: {e}")
            return []
    
    def analyze_violation_trends(self, current_violations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析违规趋势"""
        current_count = len(current_violations)
        
        # 获取历史数据进行对比
        historical_data = self.get_historical_violation_data()
        
        trend_analysis = {
            'current_violations': current_count,
            'previous_violations': historical_data.get('count', 0),
            'change': current_count - historical_data.get('count', 0),
            'change_rate': 0,
            'trend': 'stable',
            'severity': 'LOW'
        }
        
        if historical_data.get('count', 0) > 0:
            trend_analysis['change_rate'] = trend_analysis['change'] / historical_data['count']
        
        # 确定趋势
        if trend_analysis['change'] > 0:
            trend_analysis['trend'] = 'increasing'
            if trend_analysis['change_rate'] > 0.2:
                trend_analysis['severity'] = 'CRITICAL'
            elif trend_analysis['change_rate'] > 0.1:
                trend_analysis['severity'] = 'HIGH'
            else:
                trend_analysis['severity'] = 'MEDIUM'
        elif trend_analysis['change'] < 0:
            trend_analysis['trend'] = 'decreasing'
            trend_analysis['severity'] = 'LOW'
        
        # 检查是否有新的违规
        new_violations = []
        for violation in current_violations:
            if violation['last_plan_created'] and self.last_check_time:
                if violation['last_plan_created'] > self.last_check_time:
                    new_violations.append(violation)
        
        trend_analysis['new_violations'] = new_violations
        trend_analysis['new_violation_count'] = len(new_violations)
        
        return trend_analysis
    
    def get_historical_violation_data(self) -> Dict[str, Any]:
        """获取历史违规数据"""
        # 这里应该从历史记录中获取数据
        # 简化实现，返回模拟数据
        return {
            'count': 0,
            'timestamp': datetime.now() - timedelta(minutes=self.check_interval/60)
        }
    
    def generate_alerts(self, violations: List[Dict[str, Any]], trend_analysis: Dict[str, Any]) -> List[ComplianceAlert]:
        """生成警报"""
        alerts = []
        
        # 检查新违规
        if trend_analysis['new_violation_count'] >= self.alert_threshold['new_violations']:
            alerts.append(ComplianceAlert(
                alert_type='NEW_VIOLATIONS',
                severity='CRITICAL',
                message=f"🚨 检测到 {trend_analysis['new_violation_count']} 个新的素材重复使用违规！",
                details={
                    'new_violations': trend_analysis['new_violations'],
                    'total_violations': len(violations)
                }
            ))
        
        # 检查总违规数
        if len(violations) >= self.alert_threshold['total_violations']:
            alerts.append(ComplianceAlert(
                alert_type='HIGH_VIOLATION_COUNT',
                severity='HIGH',
                message=f"⚠️ 系统存在 {len(violations)} 个素材重复使用违规，超过警戒线！",
                details={
                    'total_violations': len(violations),
                    'threshold': self.alert_threshold['total_violations']
                }
            ))
        
        # 检查违规增长率
        if trend_analysis['change_rate'] >= self.alert_threshold['violation_growth_rate']:
            alerts.append(ComplianceAlert(
                alert_type='VIOLATION_GROWTH',
                severity='HIGH',
                message=f"📈 违规数量增长率达到 {trend_analysis['change_rate']:.1%}，需要立即关注！",
                details={
                    'growth_rate': trend_analysis['change_rate'],
                    'change': trend_analysis['change'],
                    'threshold': self.alert_threshold['violation_growth_rate']
                }
            ))
        
        # 检查长期违规
        long_term_violations = [v for v in violations if v['violation_duration'] > 3600]  # 超过1小时
        if long_term_violations:
            alerts.append(ComplianceAlert(
                alert_type='LONG_TERM_VIOLATIONS',
                severity='MEDIUM',
                message=f"⏰ 发现 {len(long_term_violations)} 个长期存在的违规（超过1小时）",
                details={
                    'long_term_violations': long_term_violations
                }
            ))
        
        return alerts
    
    def send_alert(self, alert: ComplianceAlert):
        """发送警报"""
        # 记录到日志
        if self.notification_config['log_enabled']:
            logger.warning(f"🚨 合规性警报 [{alert.severity}]: {alert.message}")
            logger.warning(f"   警报ID: {alert.alert_id}")
            logger.warning(f"   时间: {alert.timestamp}")
            logger.warning(f"   详情: {json.dumps(alert.details, ensure_ascii=False, indent=2)}")
        
        # 发送邮件（如果启用）
        if self.notification_config['email_enabled']:
            self.send_email_alert(alert)
        
        # 发送Webhook（如果启用）
        if self.notification_config['webhook_enabled']:
            self.send_webhook_alert(alert)
        
        # 保存到警报历史
        self.alert_history.append(alert)
        
        # 保持警报历史在合理范围内
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-500:]
    
    def send_email_alert(self, alert: ComplianceAlert):
        """发送邮件警报"""
        # 邮件发送逻辑（需要配置SMTP服务器）
        pass
    
    def send_webhook_alert(self, alert: ComplianceAlert):
        """发送Webhook警报"""
        # Webhook发送逻辑（如钉钉、企业微信等）
        pass
    
    def monitor_loop(self):
        """监控循环"""
        logger.info("🔍 启动实时合规性监控...")
        
        while self.monitoring:
            try:
                # 检测违规
                violations = self.detect_violations()
                
                # 分析趋势
                trend_analysis = self.analyze_violation_trends(violations)
                
                # 生成警报
                alerts = self.generate_alerts(violations, trend_analysis)
                
                # 发送警报
                for alert in alerts:
                    self.send_alert(alert)
                
                # 记录检查状态
                if violations:
                    logger.warning(f"⚠️ 合规性检查完成，发现 {len(violations)} 个违规")
                else:
                    logger.info("✅ 合规性检查完成，系统合规")
                
                # 更新最后检查时间
                self.last_check_time = datetime.now()
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(self.check_interval)
    
    def start_monitoring(self):
        """启动监控"""
        if self.monitoring:
            logger.warning("监控已在运行中")
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("🚀 实时合规性监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        logger.info("⏹️ 实时合规性监控已停止")
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'last_check_time': self.last_check_time,
            'check_interval': self.check_interval,
            'alert_count': len(self.alert_history),
            'recent_alerts': self.alert_history[-5:] if self.alert_history else []
        }
    
    def generate_compliance_report(self) -> str:
        """生成合规性报告"""
        violations = self.detect_violations()
        status = self.get_monitoring_status()
        
        report = f"""
千川自动化实时合规性监控报告
==========================
报告时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
监控状态: {'🟢 运行中' if self.monitoring else '🔴 已停止'}

📊 当前合规状态:
- 违规素材数量: {len(violations)} 个
- 系统状态: {'❌ 违规' if violations else '✅ 合规'}
- 最后检查时间: {status['last_check_time']}

🚨 警报统计:
- 总警报数量: {status['alert_count']} 个
- 最近警报: {len(status['recent_alerts'])} 个

{'📋 违规详情:' if violations else '🎉 系统完全合规！'}
"""
        
        if violations:
            for i, violation in enumerate(violations[:10], 1):
                report += f"""
{i}. 素材: {violation['filename']}
   - 素材ID: {violation['material_id']}
   - 重复计划数: {violation['plan_count']}
   - 计划状态: {', '.join(set(violation['plan_statuses']))}
   - 违规持续时间: {violation['violation_duration']:.0f} 秒
"""
            
            if len(violations) > 10:
                report += f"\n... 还有 {len(violations) - 10} 个违规素材\n"
        
        report += f"""
⚙️ 监控配置:
- 检查间隔: {self.check_interval} 秒
- 新违规阈值: {self.alert_threshold['new_violations']} 个
- 总违规阈值: {self.alert_threshold['total_violations']} 个
- 增长率阈值: {self.alert_threshold['violation_growth_rate']:.1%}

🎯 建议行动:
{'1. 立即修复现有违规' if violations else '1. 保持当前合规状态'}
2. 加强计划创建前检查
3. 定期审查监控配置
4. 持续关注违规趋势

报告生成完成。
"""
        
        return report


def main():
    """主函数"""
    print("🔍 千川自动化实时合规性监控系统")
    print("=" * 60)
    
    monitor = RealTimeComplianceMonitor()
    
    try:
        # 执行一次检查
        print("\n🧪 执行合规性检查...")
        violations = monitor.detect_violations()
        print(f"发现 {len(violations)} 个违规")
        
        # 生成报告
        report = monitor.generate_compliance_report()
        
        # 保存报告
        os.makedirs('ai_reports', exist_ok=True)
        report_file = f"ai_reports/realtime_compliance_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 监控报告已保存: {report_file}")
        
        # 演示启动监控（实际使用时取消注释）
        # print("\n🚀 启动实时监控...")
        # monitor.start_monitoring()
        # 
        # # 运行一段时间后停止
        # time.sleep(30)
        # monitor.stop_monitoring()
        
        print("✅ 实时合规性监控系统准备完成")
        return 0
        
    except Exception as e:
        print(f"❌ 监控系统测试失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
