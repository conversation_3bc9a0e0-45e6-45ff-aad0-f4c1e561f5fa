#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急修复素材收集重复问题
清理条件: 问题修复完成后删除

千川自动化素材收集重复问题紧急修复
==============================

问题描述：
- 素材收集时未正确排除"已处理"文件夹
- 导致已处理的素材被重复收集
- 严重影响工作流效率和数据准确性

修复目标：
1. 修复素材收集逻辑中的已处理文件夹排除机制
2. 检查并清理重复收集的素材
3. 验证修复效果
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.config_manager import get_settings
from contextlib import contextmanager

@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class MaterialCollectionFixer:
    """素材收集重复问题修复器"""
    
    def __init__(self):
        self.fix_start_time = datetime.now()
        self.app_settings = get_settings()
        self.issues_found = []
        self.fixes_applied = []
    
    def analyze_current_logic(self):
        """分析当前素材收集逻辑的问题"""
        logger.info("🔍 分析当前素材收集逻辑...")
        
        # 读取当前的素材收集代码
        scheduler_path = os.path.join(project_root, 'src', 'qianchuan_aw', 'workflows', 'scheduler.py')
        
        try:
            with open(scheduler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找问题代码
            if 'if archive_folder_name in root:' in content:
                issue = {
                    'type': 'incorrect_archive_exclusion',
                    'description': '使用不精确的字符串包含判断来排除已处理文件夹',
                    'current_logic': 'if archive_folder_name in root:',
                    'problem': '可能误判包含"已处理"字符的其他路径',
                    'severity': 'CRITICAL'
                }
                self.issues_found.append(issue)
                logger.error("🚨 发现关键问题: 已处理文件夹排除逻辑不精确")
                return True
            else:
                logger.info("✅ 未发现明显的逻辑问题")
                return False
                
        except Exception as e:
            logger.error(f"❌ 分析代码失败: {e}")
            return False
    
    def create_fixed_logic(self):
        """创建修复后的逻辑"""
        logger.info("🔧 创建修复后的素材收集逻辑...")
        
        fixed_logic = '''
def process_source_dir_fixed(source_dir, dest_dir, archive_folder_name, video_extensions, safe_log):
    """修复版：处理单个源目录"""
    try:
        if not os.path.exists(source_dir):
            safe_log(f"源目录不存在，跳过: {source_dir}")
            return 0

        copied_count = 0
        archive_path = os.path.join(source_dir, archive_folder_name)

        for root, dirs, files in os.walk(source_dir):
            # 修复：精确判断是否为已处理文件夹
            # 检查当前目录是否就是已处理文件夹或其子目录
            relative_path = os.path.relpath(root, source_dir)
            path_parts = relative_path.split(os.sep)
            
            # 如果路径中包含已处理文件夹，跳过
            if archive_folder_name in path_parts:
                safe_log(f"跳过已处理文件夹: {root}")
                continue
            
            # 额外检查：如果当前目录名就是已处理文件夹名
            if os.path.basename(root) == archive_folder_name:
                safe_log(f"跳过已处理文件夹: {root}")
                continue

            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                if file_ext in video_extensions:
                    try:
                        dest_file_path = os.path.join(dest_dir, file)

                        # 避免重复复制
                        if os.path.exists(dest_file_path):
                            safe_log(f"文件已存在，跳过: {file}")
                            continue

                        # 复制文件
                        import shutil
                        shutil.copy2(file_path, dest_file_path)
                        safe_log(f"复制成功: {file}")
                        copied_count += 1

                        # 移动原文件到归档文件夹
                        os.makedirs(archive_path, exist_ok=True)
                        archive_file_path = os.path.join(archive_path, file)
                        shutil.move(file_path, archive_file_path)
                        safe_log(f"归档成功: {file}")

                    except Exception as e:
                        safe_log(f"处理文件失败 {file}: {e}")

        return copied_count

    except Exception as e:
        safe_log(f"处理源目录失败 {source_dir}: {e}")
        return 0
'''
        
        # 保存修复逻辑到临时文件
        fix_file_path = os.path.join(project_root, 'ai_temp', 'material_collection_fixed_logic.py')
        os.makedirs(os.path.dirname(fix_file_path), exist_ok=True)
        
        with open(fix_file_path, 'w', encoding='utf-8') as f:
            f.write(fixed_logic)
        
        logger.success(f"✅ 修复逻辑已保存到: {fix_file_path}")
        return fix_file_path
    
    def check_duplicate_materials(self, db):
        """检查重复收集的素材"""
        logger.info("🔍 检查重复收集的素材...")
        
        from sqlalchemy import text
        
        # 查找可能重复的素材（相同文件名但不同路径）
        duplicate_query = text("""
            SELECT
                file_hash,
                COUNT(*) as count,
                string_agg(file_path, ', ') as paths,
                string_agg(status, ', ') as statuses
            FROM local_creatives
            GROUP BY file_hash
            HAVING COUNT(*) > 1
            ORDER BY count DESC
            LIMIT 20
        """)
        
        result = db.execute(duplicate_query)
        duplicates = result.fetchall()
        
        if duplicates:
            logger.warning(f"⚠️ 发现 {len(duplicates)} 组重复素材:")
            for dup in duplicates:
                file_hash, count, paths, statuses = dup
                logger.warning(f"   哈希: {file_hash[:8]}... | 数量: {count} | 状态: {statuses}")
            
            issue = {
                'type': 'duplicate_materials_detected',
                'description': f'发现{len(duplicates)}组重复素材',
                'duplicate_count': len(duplicates),
                'severity': 'HIGH'
            }
            self.issues_found.append(issue)
            return duplicates
        else:
            logger.success("✅ 未发现重复素材")
            return []
    
    def apply_scheduler_fix(self):
        """应用调度器修复"""
        logger.info("🔧 应用素材收集调度器修复...")
        
        scheduler_path = os.path.join(project_root, 'src', 'qianchuan_aw', 'workflows', 'scheduler.py')
        backup_path = f"{scheduler_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        try:
            # 备份原文件
            import shutil
            shutil.copy2(scheduler_path, backup_path)
            logger.info(f"📋 原文件已备份到: {backup_path}")
            
            # 读取原文件
            with open(scheduler_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换问题代码
            old_logic = '''                    # 跳过归档文件夹
                    if archive_folder_name in root:
                        continue'''
            
            new_logic = '''                    # 修复：精确判断是否为已处理文件夹
                    relative_path = os.path.relpath(root, source_dir)
                    path_parts = relative_path.split(os.sep)
                    
                    # 如果路径中包含已处理文件夹，跳过
                    if archive_folder_name in path_parts:
                        continue
                    
                    # 额外检查：如果当前目录名就是已处理文件夹名
                    if os.path.basename(root) == archive_folder_name:
                        continue'''
            
            if old_logic in content:
                content = content.replace(old_logic, new_logic)
                
                # 写入修复后的文件
                with open(scheduler_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fix_record = {
                    'type': 'scheduler_logic_fix',
                    'description': '修复了素材收集中的已处理文件夹排除逻辑',
                    'backup_path': backup_path,
                    'timestamp': datetime.now()
                }
                self.fixes_applied.append(fix_record)
                logger.success("✅ 调度器修复完成")
                return True
            else:
                logger.warning("⚠️ 未找到需要修复的代码段")
                return False
                
        except Exception as e:
            logger.error(f"❌ 应用修复失败: {e}")
            # 如果修复失败，恢复备份
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, scheduler_path)
                logger.info("📋 已恢复原文件")
            return False
    
    def run_emergency_fix(self):
        """运行紧急修复"""
        logger.info("🚨 开始素材收集重复问题紧急修复...")
        
        # 1. 分析当前逻辑问题
        has_logic_issue = self.analyze_current_logic()
        
        # 2. 检查重复素材
        with database_session() as db:
            duplicates = self.check_duplicate_materials(db)
        
        # 3. 如果发现逻辑问题，应用修复
        if has_logic_issue:
            fix_success = self.apply_scheduler_fix()
            if not fix_success:
                logger.error("❌ 修复失败，请手动检查")
                return False
        
        # 4. 创建修复后的逻辑示例
        self.create_fixed_logic()
        
        # 生成修复报告
        self.generate_fix_report()
        
        return len(self.issues_found), len(self.fixes_applied)
    
    def generate_fix_report(self):
        """生成修复报告"""
        fix_duration = (datetime.now() - self.fix_start_time).total_seconds()
        
        logger.info("=" * 60)
        logger.info("🛠️ 素材收集重复问题紧急修复报告")
        logger.info("=" * 60)
        logger.info(f"修复时间: {self.fix_start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"修复耗时: {fix_duration:.2f} 秒")
        logger.info(f"发现问题: {len(self.issues_found)} 个")
        logger.info(f"应用修复: {len(self.fixes_applied)} 个")
        
        if self.issues_found:
            logger.info("🚨 发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"   {i}. {issue['description']} (严重程度: {issue['severity']})")
        
        if self.fixes_applied:
            logger.info("🔧 应用的修复:")
            for i, fix in enumerate(self.fixes_applied, 1):
                logger.info(f"   {i}. {fix['description']}")
        
        logger.info("📋 修复建议:")
        logger.info("   1. 重启素材收集任务以应用修复")
        logger.info("   2. 监控后续收集是否还有重复问题")
        logger.info("   3. 清理已存在的重复素材（如有必要）")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    fixer = MaterialCollectionFixer()
    
    print("🚨 千川自动化素材收集重复问题紧急修复")
    print("=" * 50)
    print("修复目标:")
    print("1. 修复已处理文件夹排除逻辑")
    print("2. 检查重复收集的素材")
    print("3. 应用代码修复")
    print("4. 验证修复效果")
    print("=" * 50)
    
    # 运行紧急修复
    issues_count, fixes_count = fixer.run_emergency_fix()
    
    return 0 if issues_count <= fixes_count else 1


if __name__ == '__main__':
    exit(main())
