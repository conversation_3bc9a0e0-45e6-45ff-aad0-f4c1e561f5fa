#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 优化的批量提审服务（按广告户分组）
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
from typing import List, Dict, Any, Tuple
from collections import defaultdict
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class OptimizedBatchAppealService:
    """优化的批量提审服务 - 按广告户分组处理"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        
    def group_plans_by_account(self, plans: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按广告户分组计划"""
        logger.info("📊 按广告户分组计划...")
        
        grouped_plans = defaultdict(list)
        
        for plan in plans:
            account_key = f"{plan['principal_name']}_{plan['account_id']}"
            grouped_plans[account_key].append(plan)
        
        logger.info(f"✅ 分组完成，共 {len(grouped_plans)} 个广告户:")
        for account_key, account_plans in grouped_plans.items():
            principal_name = account_plans[0]['principal_name']
            account_id = account_plans[0]['account_id']
            logger.info(f"   📋 {principal_name} (账户: {account_id}): {len(account_plans)} 个计划")
        
        return dict(grouped_plans)
    
    def batch_appeal_for_account(self, principal_name: str, account_id: int, plans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为单个广告户批量提审"""
        logger.info(f"🎯 开始为广告户 {principal_name} (账户: {account_id}) 批量提审 {len(plans)} 个计划")
        
        try:
            from qianchuan_aw.services.copilot_service import SimpleCopilotSession
            
            results = []
            
            # 使用一个浏览器会话处理该广告户的所有计划
            with SimpleCopilotSession(principal_name, account_id, self.app_settings) as session:
                logger.success(f"✅ 广告户 {principal_name} 的浏览器会话已创建")
                
                for i, plan in enumerate(plans, 1):
                    logger.info(f"📋 处理第 {i}/{len(plans)} 个计划: {plan['campaign_id']}")
                    
                    try:
                        # 在同一个智投星对话中提审
                        success, message = session.appeal_via_text_command(plan['campaign_id'])
                        
                        result = {
                            'campaign_id': plan['campaign_id'],
                            'principal_name': principal_name,
                            'account_id': account_id,
                            'success': success,
                            'message': message,
                            'order': i
                        }
                        
                        results.append(result)
                        
                        if success:
                            logger.success(f"✅ 计划 {plan['campaign_id']} 提审成功")
                        else:
                            logger.error(f"❌ 计划 {plan['campaign_id']} 提审失败: {message}")
                        
                        # 同一对话中的多个操作之间稍微等待
                        if i < len(plans):
                            logger.info("⏳ 等待2秒后继续下一个计划...")
                            time.sleep(2)
                            
                    except Exception as e:
                        logger.error(f"❌ 计划 {plan['campaign_id']} 提审异常: {e}")
                        result = {
                            'campaign_id': plan['campaign_id'],
                            'principal_name': principal_name,
                            'account_id': account_id,
                            'success': False,
                            'message': str(e),
                            'order': i
                        }
                        results.append(result)
                
                logger.success(f"✅ 广告户 {principal_name} 的 {len(plans)} 个计划处理完成")
                
            return results
            
        except Exception as e:
            logger.error(f"❌ 广告户 {principal_name} 批量提审失败: {e}")
            # 返回所有计划的失败结果
            return [{
                'campaign_id': plan['campaign_id'],
                'principal_name': principal_name,
                'account_id': account_id,
                'success': False,
                'message': str(e),
                'order': i
            } for i, plan in enumerate(plans, 1)]
    
    def batch_query_appeal_status_for_account(self, principal_name: str, account_id: int, plans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为单个广告户批量查询申诉进度"""
        logger.info(f"🔍 开始为广告户 {principal_name} (账户: {account_id}) 批量查询 {len(plans)} 个计划的申诉进度")
        
        try:
            from qianchuan_aw.services.copilot_service import SimpleCopilotSession
            
            results = []
            
            # 使用一个浏览器会话处理该广告户的所有计划
            with SimpleCopilotSession(principal_name, account_id, self.app_settings) as session:
                logger.success(f"✅ 广告户 {principal_name} 的浏览器会话已创建")
                
                for i, plan in enumerate(plans, 1):
                    logger.info(f"📋 查询第 {i}/{len(plans)} 个计划: {plan['campaign_id']}")
                    
                    try:
                        # 在同一个智投星对话中查询申诉进度
                        success, message = session.query_appeal_status(plan['campaign_id'])
                        
                        result = {
                            'campaign_id': plan['campaign_id'],
                            'principal_name': principal_name,
                            'account_id': account_id,
                            'success': success,
                            'message': message,
                            'order': i
                        }
                        
                        results.append(result)
                        
                        if success:
                            logger.success(f"✅ 计划 {plan['campaign_id']} 查询成功")
                        else:
                            logger.warning(f"⚠️ 计划 {plan['campaign_id']} 查询失败: {message}")
                        
                        # 同一对话中的多个操作之间稍微等待
                        if i < len(plans):
                            logger.info("⏳ 等待2秒后继续下一个计划...")
                            time.sleep(2)
                            
                    except Exception as e:
                        logger.error(f"❌ 计划 {plan['campaign_id']} 查询异常: {e}")
                        result = {
                            'campaign_id': plan['campaign_id'],
                            'principal_name': principal_name,
                            'account_id': account_id,
                            'success': False,
                            'message': str(e),
                            'order': i
                        }
                        results.append(result)
                
                logger.success(f"✅ 广告户 {principal_name} 的 {len(plans)} 个计划查询完成")
                
            return results
            
        except Exception as e:
            logger.error(f"❌ 广告户 {principal_name} 批量查询失败: {e}")
            # 返回所有计划的失败结果
            return [{
                'campaign_id': plan['campaign_id'],
                'principal_name': principal_name,
                'account_id': account_id,
                'success': False,
                'message': str(e),
                'order': i
            } for i, plan in enumerate(plans, 1)]
    
    def batch_appeal_all_plans(self, plans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量提审所有计划（按广告户分组优化）"""
        logger.info(f"🚀 开始优化的批量提审，共 {len(plans)} 个计划")
        
        # 1. 按广告户分组
        grouped_plans = self.group_plans_by_account(plans)
        
        # 2. 逐个广告户处理
        all_results = []
        
        for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
            principal_name = account_plans[0]['principal_name']
            account_id = account_plans[0]['account_id']
            
            logger.info(f"\n📋 处理第 {i}/{len(grouped_plans)} 个广告户")
            logger.info("="*60)
            
            # 为该广告户批量提审
            account_results = self.batch_appeal_for_account(principal_name, account_id, account_plans)
            all_results.extend(account_results)
            
            # 广告户之间等待一下
            if i < len(grouped_plans):
                logger.info("⏳ 等待5秒后处理下一个广告户...")
                time.sleep(5)
        
        logger.success(f"🎉 优化的批量提审完成，共处理 {len(all_results)} 个计划")
        
        return all_results
    
    def batch_query_all_plans(self, plans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量查询所有计划的申诉进度（按广告户分组优化）"""
        logger.info(f"🔍 开始优化的批量查询，共 {len(plans)} 个计划")
        
        # 1. 按广告户分组
        grouped_plans = self.group_plans_by_account(plans)
        
        # 2. 逐个广告户处理
        all_results = []
        
        for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
            principal_name = account_plans[0]['principal_name']
            account_id = account_plans[0]['account_id']
            
            logger.info(f"\n📋 查询第 {i}/{len(grouped_plans)} 个广告户")
            logger.info("="*60)
            
            # 为该广告户批量查询
            account_results = self.batch_query_appeal_status_for_account(principal_name, account_id, account_plans)
            all_results.extend(account_results)
            
            # 广告户之间等待一下
            if i < len(grouped_plans):
                logger.info("⏳ 等待5秒后查询下一个广告户...")
                time.sleep(5)
        
        logger.success(f"🎉 优化的批量查询完成，共处理 {len(all_results)} 个计划")
        
        return all_results
    
    def get_performance_statistics(self, plans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取性能统计信息"""
        grouped_plans = self.group_plans_by_account(plans)
        
        total_plans = len(plans)
        total_accounts = len(grouped_plans)
        
        # 计算资源节约
        old_browser_sessions = total_plans  # 旧方式：每个计划一个会话
        new_browser_sessions = total_accounts  # 新方式：每个广告户一个会话
        
        resource_savings = ((old_browser_sessions - new_browser_sessions) / old_browser_sessions) * 100
        
        # 计算时间节约（估算）
        old_time_estimate = total_plans * 30  # 旧方式：每个计划30秒（包括启动浏览器）
        new_time_estimate = total_accounts * 20 + (total_plans - total_accounts) * 5  # 新方式：每个广告户20秒启动 + 每个额外计划5秒
        
        time_savings = ((old_time_estimate - new_time_estimate) / old_time_estimate) * 100
        
        return {
            'total_plans': total_plans,
            'total_accounts': total_accounts,
            'old_browser_sessions': old_browser_sessions,
            'new_browser_sessions': new_browser_sessions,
            'resource_savings_percent': resource_savings,
            'old_time_estimate_seconds': old_time_estimate,
            'new_time_estimate_seconds': new_time_estimate,
            'time_savings_percent': time_savings,
            'plans_per_account': {
                account_key: len(account_plans) 
                for account_key, account_plans in grouped_plans.items()
            }
        }


def create_optimized_batch_appeal_service(app_settings: Dict[str, Any]) -> OptimizedBatchAppealService:
    """创建优化的批量提审服务"""
    return OptimizedBatchAppealService(app_settings)
