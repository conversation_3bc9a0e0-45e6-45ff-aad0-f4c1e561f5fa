#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证配置重载和强制重启Celery
清理条件: 成为配置管理工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class ConfigReloadVerifier:
    """配置重载验证器"""
    
    def __init__(self):
        self.verification_results = {}
    
    def run_comprehensive_verification(self):
        """运行全面验证"""
        logger.info("🔄 配置重载验证")
        logger.info("="*100)
        
        # 1. 验证配置文件内容
        file_config_correct = self._verify_config_file()
        
        # 2. 验证运行时配置加载
        runtime_config_correct = self._verify_runtime_config()
        
        # 3. 强制清理配置缓存
        cache_cleared = self._clear_config_cache()
        
        # 4. 重新验证运行时配置
        reloaded_config_correct = self._verify_runtime_config_after_reload()
        
        # 5. 生成验证报告
        self._generate_verification_report({
            'file_config_correct': file_config_correct,
            'runtime_config_correct': runtime_config_correct,
            'cache_cleared': cache_cleared,
            'reloaded_config_correct': reloaded_config_correct
        })
    
    def _verify_config_file(self):
        """验证配置文件内容"""
        logger.info("📄 验证配置文件内容...")
        
        try:
            config_file = os.path.join(project_root, 'config/settings.yml')
            
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置
            checks = {
                'test_workflow_exists': 'test_workflow:' in content,
                'creative_count_3': 'creative_count: 3' in content,
                'test_workflow_comment': '测试工作流配置' in content,
                'creative_count_comment': '从9个降低到3个' in content
            }
            
            passed_checks = sum(checks.values())
            total_checks = len(checks)
            
            logger.info(f"📊 配置文件检查: {passed_checks}/{total_checks}")
            
            for check_name, passed in checks.items():
                status_icon = "✅" if passed else "❌"
                logger.info(f"   {status_icon} {check_name}: {'通过' if passed else '失败'}")
            
            if passed_checks >= 3:
                logger.success("✅ 配置文件内容正确")
                return True
            else:
                logger.error("❌ 配置文件内容有问题")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证配置文件失败: {e}")
            return False
    
    def _verify_runtime_config(self):
        """验证运行时配置加载"""
        logger.info("⚙️ 验证运行时配置加载...")
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            # 强制重新加载配置
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            
            # 检查test_workflow配置
            plan_defaults = app_settings.get('plan_creation_defaults', {})
            test_workflow = plan_defaults.get('test_workflow', {})
            creative_count = test_workflow.get('creative_count', 9)
            
            logger.info(f"📋 运行时配置:")
            logger.info(f"   plan_creation_defaults存在: {'✅' if plan_defaults else '❌'}")
            logger.info(f"   test_workflow存在: {'✅' if test_workflow else '❌'}")
            logger.info(f"   creative_count值: {creative_count}")
            
            if creative_count == 3:
                logger.success("✅ 运行时配置正确：creative_count = 3")
                return True
            else:
                logger.error(f"❌ 运行时配置错误：creative_count = {creative_count} (应该是3)")
                return False
                
        except Exception as e:
            logger.error(f"❌ 验证运行时配置失败: {e}")
            return False
    
    def _clear_config_cache(self):
        """清理配置缓存"""
        logger.info("🧹 清理配置缓存...")
        
        try:
            # 清理Python模块缓存
            import sys
            modules_to_clear = [
                'qianchuan_aw.utils.config_manager',
                'qianchuan_aw.utils.config_loader'
            ]
            
            for module_name in modules_to_clear:
                if module_name in sys.modules:
                    del sys.modules[module_name]
                    logger.info(f"   清理模块缓存: {module_name}")
            
            # 强制重新导入
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            # 获取新的配置管理器实例
            config_manager = get_config_manager()
            
            logger.success("✅ 配置缓存已清理")
            return True
            
        except Exception as e:
            logger.error(f"❌ 清理配置缓存失败: {e}")
            return False
    
    def _verify_runtime_config_after_reload(self):
        """重新验证运行时配置"""
        logger.info("🔄 重新验证运行时配置...")
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            
            # 检查test_workflow配置
            test_workflow = app_settings.get('plan_creation_defaults', {}).get('test_workflow', {})
            creative_count = test_workflow.get('creative_count', 9)
            
            logger.info(f"📋 重载后配置:")
            logger.info(f"   creative_count值: {creative_count}")
            
            if creative_count == 3:
                logger.success("✅ 重载后配置正确：creative_count = 3")
                return True
            else:
                logger.error(f"❌ 重载后配置仍然错误：creative_count = {creative_count}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 重新验证配置失败: {e}")
            return False
    
    def _generate_verification_report(self, results):
        """生成验证报告"""
        logger.info("\n📋 配置重载验证报告")
        logger.info("="*100)
        
        file_config_correct = results['file_config_correct']
        runtime_config_correct = results['runtime_config_correct']
        cache_cleared = results['cache_cleared']
        reloaded_config_correct = results['reloaded_config_correct']
        
        # 各项检查结果
        if file_config_correct:
            logger.success("✅ 配置文件: test_workflow配置正确")
        else:
            logger.error("❌ 配置文件: test_workflow配置有问题")
        
        if runtime_config_correct:
            logger.success("✅ 运行时配置: 初始加载正确")
        else:
            logger.error("❌ 运行时配置: 初始加载错误")
        
        if cache_cleared:
            logger.success("✅ 缓存清理: 配置缓存已清理")
        else:
            logger.error("❌ 缓存清理: 配置缓存清理失败")
        
        if reloaded_config_correct:
            logger.success("✅ 重载验证: 配置重载成功")
        else:
            logger.error("❌ 重载验证: 配置重载失败")
        
        # 总体评估
        success_count = sum([
            file_config_correct,
            cache_cleared,
            reloaded_config_correct
        ])
        
        logger.info(f"\n🎯 验证成功率: {success_count}/3")
        
        if success_count >= 2:
            logger.success("🎊 配置重载验证基本成功")
            
            if not runtime_config_correct and reloaded_config_correct:
                logger.success("💡 配置缓存问题已解决，新配置已生效")
            
            logger.info("\n🚀 下一步行动:")
            logger.info("   1. 重启Celery Worker和Beat服务")
            logger.info("   2. 观察日志中的素材要求数量")
            logger.info("   3. 应该看到'需要 3 个素材来创建计划'")
            
            return True
        else:
            logger.error("❌ 配置重载验证失败")
            logger.error("🔧 需要手动检查配置文件和重启服务")
            return False


def main():
    """主函数"""
    verifier = ConfigReloadVerifier()
    
    logger.info("🚀 启动配置重载验证")
    logger.info("🎯 目标：确保test_workflow配置正确加载")
    
    success = verifier.run_comprehensive_verification()
    
    if success:
        logger.success("🎊 配置重载验证完成")
        logger.success("💡 建议：立即重启Celery服务使配置生效")
        return 0
    else:
        logger.error("❌ 配置重载验证失败")
        logger.error("🔧 建议：检查配置文件并手动重启服务")
        return 1


if __name__ == "__main__":
    exit(main())
