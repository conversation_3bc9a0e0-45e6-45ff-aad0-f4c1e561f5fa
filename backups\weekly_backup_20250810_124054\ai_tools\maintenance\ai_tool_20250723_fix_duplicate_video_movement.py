#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复重复视频移动BUG，确保审核通过的视频只移动一次
清理条件: 问题解决后可归档，但建议保留作为参考
"""

import os
import sys
import json
import shutil
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.database import load_settings
from qianchuan_aw.database.models import PlatformCreative, Principal
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.hash_utils import calculate_file_hash

class DuplicateVideoMovementFixer:
    """修复重复视频移动问题的工具类"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_workflow_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        self.library_path = os.path.join(self.base_workflow_dir, "approved_creatives.json")
        self.stats = {
            'duplicates_found': 0,
            'duplicates_removed': 0,
            'library_entries_cleaned': 0,
            'files_moved_back': 0
        }
    
    def analyze_duplicate_situation(self):
        """分析当前重复情况"""
        logger.info("🔍 分析重复视频移动情况...")
        
        # 检查弹药库重复条目
        duplicates_in_library = self._find_library_duplicates()
        
        # 检查文件系统重复
        duplicates_in_filesystem = self._find_filesystem_duplicates()
        
        logger.info(f"📊 分析结果:")
        logger.info(f"  弹药库重复条目: {len(duplicates_in_library)}")
        logger.info(f"  文件系统重复: {len(duplicates_in_filesystem)}")
        
        return duplicates_in_library, duplicates_in_filesystem
    
    def _find_library_duplicates(self) -> Dict[str, list]:
        """查找弹药库中的重复条目"""
        if not os.path.exists(self.library_path):
            return {}
        
        try:
            with open(self.library_path, 'r', encoding='utf-8') as f:
                library = json.load(f)
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"读取弹药库失败: {e}")
            return {}
        
        # 按material_id分组查找重复
        material_groups = {}
        for item in library:
            material_id = item.get('material_id') or item.get('material_id_qc')
            if material_id:
                if material_id not in material_groups:
                    material_groups[material_id] = []
                material_groups[material_id].append(item)
        
        # 返回重复的条目
        duplicates = {k: v for k, v in material_groups.items() if len(v) > 1}
        return duplicates
    
    def _find_filesystem_duplicates(self) -> Dict[str, list]:
        """查找文件系统中的重复文件"""
        approved_dir = os.path.join(self.base_workflow_dir, '03_materials_approved')
        if not os.path.exists(approved_dir):
            return {}
        
        file_groups = {}
        
        # 遍历所有主体目录
        for principal_dir in os.listdir(approved_dir):
            principal_path = os.path.join(approved_dir, principal_dir)
            if not os.path.isdir(principal_path):
                continue
            
            # 遍历日期目录
            for date_dir in os.listdir(principal_path):
                date_path = os.path.join(principal_path, date_dir)
                if not os.path.isdir(date_path):
                    continue
                
                # 检查文件
                for filename in os.listdir(date_path):
                    file_path = os.path.join(date_path, filename)
                    if os.path.isfile(file_path) and filename.endswith(('.mp4', '.mov', '.avi', '.mkv')):
                        # 计算文件哈希
                        try:
                            file_hash = calculate_file_hash(file_path)
                            if file_hash not in file_groups:
                                file_groups[file_hash] = []
                            file_groups[file_hash].append(file_path)
                        except Exception as e:
                            logger.warning(f"计算文件哈希失败 {file_path}: {e}")
        
        # 返回重复的文件
        duplicates = {k: v for k, v in file_groups.items() if len(v) > 1}
        return duplicates
    
    def fix_library_duplicates(self, duplicates_in_library: Dict[str, list]):
        """修复弹药库中的重复条目"""
        if not duplicates_in_library:
            logger.info("✅ 弹药库中没有重复条目")
            return
        
        logger.info(f"🔧 修复弹药库中的 {len(duplicates_in_library)} 个重复条目...")
        
        try:
            with open(self.library_path, 'r', encoding='utf-8') as f:
                library = json.load(f)
        except Exception as e:
            logger.error(f"读取弹药库失败: {e}")
            return
        
        # 创建去重后的弹药库
        cleaned_library = []
        seen_materials = set()
        
        for item in library:
            material_id = item.get('material_id') or item.get('material_id_qc')
            if material_id and material_id not in seen_materials:
                # 标准化字段名
                if 'material_id_qc' in item and 'material_id' not in item:
                    item['material_id'] = item['material_id_qc']
                
                cleaned_library.append(item)
                seen_materials.add(material_id)
                self.stats['library_entries_cleaned'] += 1
        
        # 备份原弹药库
        backup_path = f"{self.library_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(self.library_path, backup_path)
        logger.info(f"📦 已备份原弹药库到: {backup_path}")
        
        # 保存清理后的弹药库
        with open(self.library_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_library, f, indent=4, ensure_ascii=False)
        
        logger.success(f"✅ 弹药库去重完成，清理了 {len(library) - len(cleaned_library)} 个重复条目")
    
    def fix_filesystem_duplicates(self, duplicates_in_filesystem: Dict[str, list]):
        """修复文件系统中的重复文件"""
        if not duplicates_in_filesystem:
            logger.info("✅ 文件系统中没有重复文件")
            return
        
        logger.info(f"🔧 修复文件系统中的 {len(duplicates_in_filesystem)} 组重复文件...")
        
        for file_hash, file_paths in duplicates_in_filesystem.items():
            if len(file_paths) <= 1:
                continue
            
            # 按修改时间排序，保留最新的
            file_paths.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            keep_file = file_paths[0]
            duplicate_files = file_paths[1:]
            
            logger.info(f"📁 处理重复文件组 (hash: {file_hash[:8]}...):")
            logger.info(f"  保留: {keep_file}")
            
            for dup_file in duplicate_files:
                try:
                    # 移动到隔离目录而不是直接删除
                    quarantine_dir = os.path.join(self.base_workflow_dir, 'quarantine', 'duplicate_files')
                    os.makedirs(quarantine_dir, exist_ok=True)
                    
                    quarantine_name = f"{file_hash[:8]}_{os.path.basename(dup_file)}"
                    quarantine_path = os.path.join(quarantine_dir, quarantine_name)
                    
                    shutil.move(dup_file, quarantine_path)
                    logger.info(f"  隔离: {dup_file} -> {quarantine_path}")
                    self.stats['files_moved_back'] += 1
                    
                except Exception as e:
                    logger.error(f"隔离重复文件失败 {dup_file}: {e}")
    
    def create_enhanced_duplicate_prevention(self):
        """创建增强的重复预防机制"""
        logger.info("🛡️ 创建增强的重复预防机制...")
        
        # 创建改进的文件移动函数
        enhanced_code = '''
def _move_file_to_approved_dir_enhanced(
    platform_creative: PlatformCreative,
    principal: Principal,
    app_settings: Dict[str, Any]
) -> Tuple[bool, Optional[str]]:
    """
    [V59.2] 增强版文件移动 - 防止重复移动
    返回: (是否成功, 目标文件路径)
    """
    try:
        local_creative = platform_creative.local_creative
        if not local_creative or not local_creative.file_path:
            logger.warning(f"素材 {platform_creative.material_id_qc} 缺少文件路径")
            return False, None

        # 路径准备
        date_str = datetime.now().strftime('%Y%m%d')
        filename = os.path.basename(local_creative.file_path)
        
        base_workflow_dir = app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        workflow_dirs = app_settings.get('workflow_dirs', {})
        approved_dir = os.path.join(
            base_workflow_dir, 
            workflow_dirs.get('DIR_03_MATERIALS_APPROVED', '03_materials_approved'), 
            principal.name, 
            date_str
        )
        target_path = os.path.join(approved_dir, filename)
        
        # 检查目标文件是否已存在
        if os.path.exists(target_path):
            # 比较文件哈希，如果相同则跳过
            source_hash = calculate_file_hash(local_creative.file_path)
            target_hash = calculate_file_hash(target_path)
            
            if source_hash == target_hash:
                logger.info(f"文件已存在且内容相同，跳过移动: {filename}")
                return True, target_path
            else:
                # 文件名相同但内容不同，重命名
                base_name, ext = os.path.splitext(filename)
                counter = 1
                while os.path.exists(target_path):
                    new_filename = f"{base_name}_{counter}{ext}"
                    target_path = os.path.join(approved_dir, new_filename)
                    counter += 1
                logger.info(f"目标文件已存在但内容不同，重命名为: {os.path.basename(target_path)}")
        
        # 确保目标目录存在
        os.makedirs(approved_dir, exist_ok=True)
        
        # 移动文件
        if os.path.exists(local_creative.file_path):
            shutil.move(local_creative.file_path, target_path)
            logger.success(f"文件移动成功: {filename} -> {target_path}")
            return True, target_path
        else:
            logger.warning(f"源文件不存在: {local_creative.file_path}")
            return False, None
            
    except Exception as e:
        logger.error(f"移动文件失败: {e}", exc_info=True)
        return False, None
'''
        
        # 保存增强代码到文件
        enhanced_file = os.path.join(project_root, 'ai_tools', 'maintenance', 'enhanced_file_movement.py')
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_code)
        
        logger.success(f"✅ 增强代码已保存到: {enhanced_file}")
    
    def run_complete_fix(self):
        """运行完整的修复流程"""
        logger.info("🚀 开始完整的重复视频移动修复流程...")
        logger.info("=" * 60)
        
        try:
            # 1. 分析当前情况
            duplicates_in_library, duplicates_in_filesystem = self.analyze_duplicate_situation()
            
            # 2. 修复弹药库重复
            self.fix_library_duplicates(duplicates_in_library)
            
            # 3. 修复文件系统重复
            self.fix_filesystem_duplicates(duplicates_in_filesystem)
            
            # 4. 创建增强预防机制
            self.create_enhanced_duplicate_prevention()
            
            # 5. 输出统计信息
            logger.info("\n📊 修复统计:")
            logger.info(f"  弹药库条目清理: {self.stats['library_entries_cleaned']}")
            logger.info(f"  重复文件隔离: {self.stats['files_moved_back']}")
            
            logger.success("✅ 重复视频移动修复完成！")
            
        except Exception as e:
            logger.error(f"修复过程中发生错误: {e}", exc_info=True)

def main():
    """主函数"""
    fixer = DuplicateVideoMovementFixer()
    fixer.run_complete_fix()

if __name__ == "__main__":
    main()
