#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 启动系统保护机制
清理条件: 长期保留，用于生产环境

系统保护启动工具
==============

在qc_env环境中启动系统保护机制，确保不再出现浏览器过载问题。
"""

import os
import sys
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.services.system_protection import (
    initialize_system_protection,
    start_system_protection,
    get_protection_status
)
from src.qianchuan_aw.services.resource_monitor import ResourceThresholds


def main():
    """启动系统保护"""
    logger.info("🛡️ 启动千川自动化系统保护机制...")
    
    try:
        # 设置生产环境阈值
        production_thresholds = ResourceThresholds(
            cpu_warning=60.0,      # 降低CPU警告阈值
            cpu_critical=80.0,     # 降低CPU严重阈值
            memory_warning=70.0,   # 降低内存警告阈值
            memory_critical=85.0,  # 降低内存严重阈值
            browser_warning=3,     # 降低浏览器进程警告阈值
            browser_critical=5     # 降低浏览器进程严重阈值
        )
        
        logger.info("📋 保护阈值配置:")
        logger.info(f"   CPU警告: {production_thresholds.cpu_warning}%, 严重: {production_thresholds.cpu_critical}%")
        logger.info(f"   内存警告: {production_thresholds.memory_warning}%, 严重: {production_thresholds.memory_critical}%")
        logger.info(f"   浏览器进程警告: {production_thresholds.browser_warning}, 严重: {production_thresholds.browser_critical}")
        
        # 初始化系统保护
        logger.info("🔧 初始化系统保护...")
        if not initialize_system_protection(production_thresholds):
            logger.error("❌ 系统保护初始化失败")
            return 1
        
        # 启动系统保护（15秒间隔，更频繁监控）
        logger.info("🚀 启动系统保护监控...")
        if not start_system_protection(15):
            logger.error("❌ 系统保护启动失败")
            return 1
        
        # 等待一段时间让监控开始工作
        logger.info("⏳ 等待监控系统启动...")
        time.sleep(5)
        
        # 检查保护状态
        status = get_protection_status()
        logger.info("📊 系统保护状态:")
        logger.info(f"   保护启用: {status.get('protection_enabled', False)}")
        logger.info(f"   紧急模式: {status.get('emergency_mode', False)}")
        
        monitor_stats = status.get('monitor_stats', {})
        if monitor_stats:
            logger.info(f"   监控状态: 运行中")
            current_metrics = monitor_stats.get('current_metrics')
            if current_metrics:
                logger.info(f"   当前CPU: {current_metrics.get('cpu_percent', 0):.1f}%")
                logger.info(f"   当前内存: {current_metrics.get('memory_percent', 0):.1f}%")
                logger.info(f"   浏览器进程: {current_metrics.get('browser_processes', 0)}")
        
        logger.info("✅ 系统保护机制已成功启动！")
        logger.info("💡 提示:")
        logger.info("   - 系统将每15秒检查一次资源状态")
        logger.info("   - CPU使用率超过80%时会自动触发保护")
        logger.info("   - 浏览器进程超过5个时会自动清理")
        logger.info("   - 使用 'python tools/monitor_system_status.py' 查看状态")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 启动系统保护失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
