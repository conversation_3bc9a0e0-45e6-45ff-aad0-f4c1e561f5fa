#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版素材审核报表页面
"""

import sys
import os
from datetime import datetime, timedelta
import pytz

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, project_root)
sys.path.insert(0, src_path)

def test_database_queries():
    """测试数据库查询功能"""
    print("🔍 测试数据库查询功能...")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount, Principal
        from sqlalchemy import func
        
        with database_session() as db:
            # 测试基础查询
            total_materials = db.query(LocalCreative).count()
            total_campaigns = db.query(Campaign).count()
            total_accounts = db.query(AdAccount).count()
            total_principals = db.query(Principal).count()
            
            print(f"✅ 数据库连接成功")
            print(f"   - 总素材数: {total_materials}")
            print(f"   - 总计划数: {total_campaigns}")
            print(f"   - 总账户数: {total_accounts}")
            print(f"   - 总主体数: {total_principals}")
            
            # 测试时区处理
            china_tz = pytz.timezone('Asia/Shanghai')
            now = datetime.now(china_tz)
            yesterday = now - timedelta(days=1)
            
            recent_materials = db.query(LocalCreative).filter(
                LocalCreative.created_at >= yesterday
            ).count()
            
            print(f"✅ 时区处理正常")
            print(f"   - 最近24小时素材: {recent_materials}")
            
            # 测试状态统计
            status_stats = db.query(
                LocalCreative.status,
                func.count(LocalCreative.id).label('count')
            ).group_by(LocalCreative.status).all()
            
            print(f"✅ 状态统计查询正常")
            for stat in status_stats[:5]:  # 显示前5个状态
                print(f"   - {stat.status}: {stat.count}")
                
            return True
            
    except Exception as e:
        print(f"❌ 数据库查询测试失败: {e}")
        return False

def test_time_range_functions():
    """测试时间范围处理功能"""
    print("\n⏰ 测试时间范围处理功能...")
    
    try:
        from ai_tool_20250720_enhanced_material_analytics import get_daily_trend_data
        from qianchuan_aw.utils.db_utils import database_session
        import pytz
        
        china_tz = pytz.timezone('Asia/Shanghai')
        time_range = {
            'start': datetime.now(china_tz) - timedelta(days=7),
            'end': datetime.now(china_tz)
        }
        
        with database_session() as db:
            trend_data = get_daily_trend_data(db, time_range)
            
            if trend_data:
                print(f"✅ 趋势数据获取成功")
                print(f"   - 数据天数: {len(trend_data)}")
                for date, data in list(trend_data.items())[:3]:  # 显示前3天
                    print(f"   - {date}: 素材{data['materials']}个, 计划{data['campaigns']}个")
            else:
                print(f"⚠️ 趋势数据为空（可能是时间范围内无数据）")
                
            return True
            
    except Exception as e:
        print(f"❌ 时间范围处理测试失败: {e}")
        return False

def test_status_distribution():
    """测试状态分布功能"""
    print("\n📊 测试状态分布功能...")
    
    try:
        from ai_tool_20250720_enhanced_material_analytics import (
            get_material_status_distribution,
            get_campaign_status_distribution
        )
        from qianchuan_aw.utils.db_utils import database_session
        import pytz
        
        china_tz = pytz.timezone('Asia/Shanghai')
        time_range = {
            'start': datetime.now(china_tz) - timedelta(days=30),
            'end': datetime.now(china_tz)
        }
        
        with database_session() as db:
            # 测试素材状态分布
            material_dist = get_material_status_distribution(db, time_range)
            if material_dist:
                print(f"✅ 素材状态分布获取成功")
                for status, count in list(material_dist.items())[:5]:
                    print(f"   - {status}: {count}")
            else:
                print(f"⚠️ 素材状态分布为空")
            
            # 测试计划状态分布
            campaign_dist = get_campaign_status_distribution(db, time_range)
            if campaign_dist:
                print(f"✅ 计划状态分布获取成功")
                for status, count in list(campaign_dist.items())[:5]:
                    print(f"   - {status}: {count}")
            else:
                print(f"⚠️ 计划状态分布为空")
                
            return True
            
    except Exception as e:
        print(f"❌ 状态分布测试失败: {e}")
        return False

def test_account_analysis():
    """测试账户分析功能"""
    print("\n🏢 测试账户分析功能...")
    
    try:
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.database.models import AdAccount, LocalCreative, Campaign
        from sqlalchemy.orm import joinedload
        import pytz
        
        china_tz = pytz.timezone('Asia/Shanghai')
        time_range = {
            'start': datetime.now(china_tz) - timedelta(days=30),
            'end': datetime.now(china_tz)
        }
        
        with database_session() as db:
            # 获取账户列表
            accounts = db.query(AdAccount).limit(3).all()
            
            if accounts:
                print(f"✅ 账户数据获取成功")
                
                for account in accounts:
                    # 统计该账户的素材和计划数量
                    materials_count = db.query(LocalCreative).filter(
                        LocalCreative.uploaded_to_account_id == account.id,
                        LocalCreative.created_at >= time_range['start'],
                        LocalCreative.created_at <= time_range['end']
                    ).count()
                    
                    campaigns_count = db.query(Campaign).filter(
                        Campaign.account_id == account.id,
                        Campaign.created_at >= time_range['start'],
                        Campaign.created_at <= time_range['end']
                    ).count()
                    
                    print(f"   - {account.name}: 素材{materials_count}个, 计划{campaigns_count}个")
            else:
                print(f"⚠️ 无账户数据")
                
            return True
            
    except Exception as e:
        print(f"❌ 账户分析测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试增强版素材审核报表页面...")
    print("="*60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据库查询", test_database_queries()))
    test_results.append(("时间范围处理", test_time_range_functions()))
    test_results.append(("状态分布", test_status_distribution()))
    test_results.append(("账户分析", test_account_analysis()))
    
    # 汇总测试结果
    print("\n" + "="*60)
    print("🎯 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强版报表页面功能正常。")
    elif passed >= total * 0.7:
        print("⚠️ 大部分测试通过，但仍有部分功能需要检查。")
    else:
        print("❌ 多项测试失败，需要检查系统配置和数据库连接。")
    
    print("\n💡 建议:")
    print("1. 如果数据库查询失败，请检查数据库连接配置")
    print("2. 如果时间范围处理失败，请检查pytz时区库安装")
    print("3. 如果状态分布为空，可能是选定时间范围内无数据")
    print("4. 可以通过Web UI访问 '🎬 素材管理' -> '📊 素材审核报表 (增强版)' 进行实际测试")

if __name__ == "__main__":
    main()
