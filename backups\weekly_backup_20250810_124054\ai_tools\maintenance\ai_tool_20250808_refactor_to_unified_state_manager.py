#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 重构现有模块使用统一状态管理器
清理条件: 成为项目维护工具，长期保留
"""

import os
import sys
from typing import List, Dict, Any
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class UnifiedStateManagerRefactor:
    """统一状态管理器重构工具"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.refactor_targets = [
            {
                'file': 'ai_tools/enhancement/ai_tool_20250801_enhancement_workflow_integration.py',
                'method': '_update_campaign_appeal_status',
                'priority': 'HIGH',
                'description': '智能提审服务的状态更新方法'
            },
            {
                'file': 'src/qianchuan_aw/workflows/appeal_and_monitor.py',
                'method': '_perform_appeal_actions',
                'priority': 'HIGH',
                'description': '手动提审工作流'
            },
            {
                'file': 'src/qianchuan_aw/utils/appeal_state_manager.py',
                'method': '_set_appealing_state',
                'priority': 'MEDIUM',
                'description': '旧版状态管理器（需要废弃）'
            },
            {
                'file': 'src/qianchuan_aw/services/unified_appeal_service.py',
                'method': '_update_appeal_status',
                'priority': 'MEDIUM',
                'description': '统一提审服务的状态更新'
            }
        ]
    
    def analyze_refactor_scope(self) -> Dict[str, Any]:
        """分析重构范围"""
        logger.info("🔍 分析重构范围...")
        
        analysis = {
            'total_files': len(self.refactor_targets),
            'high_priority': 0,
            'medium_priority': 0,
            'low_priority': 0,
            'files_exist': 0,
            'files_missing': 0,
            'detailed_analysis': []
        }
        
        for target in self.refactor_targets:
            file_path = self.project_root / target['file']
            exists = file_path.exists()
            
            if exists:
                analysis['files_exist'] += 1
            else:
                analysis['files_missing'] += 1
            
            if target['priority'] == 'HIGH':
                analysis['high_priority'] += 1
            elif target['priority'] == 'MEDIUM':
                analysis['medium_priority'] += 1
            else:
                analysis['low_priority'] += 1
            
            analysis['detailed_analysis'].append({
                'file': target['file'],
                'method': target['method'],
                'priority': target['priority'],
                'exists': exists,
                'description': target['description']
            })
        
        logger.info(f"📊 重构分析结果:")
        logger.info(f"   总文件数: {analysis['total_files']}")
        logger.info(f"   存在文件: {analysis['files_exist']}")
        logger.info(f"   缺失文件: {analysis['files_missing']}")
        logger.info(f"   高优先级: {analysis['high_priority']}")
        logger.info(f"   中优先级: {analysis['medium_priority']}")
        
        return analysis
    
    def refactor_smart_appeal_service(self) -> bool:
        """重构智能提审服务"""
        logger.info("🔧 重构智能提审服务...")
        
        file_path = self.project_root / 'ai_tools/enhancement/ai_tool_20250801_enhancement_workflow_integration.py'
        
        if not file_path.exists():
            logger.error(f"❌ 文件不存在: {file_path}")
            return False
        
        try:
            # 读取原文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经使用统一状态管理器
            if 'from qianchuan_aw.utils.unified_appeal_state_manager import' in content:
                logger.info("✅ 智能提审服务已经使用统一状态管理器")
                return True
            
            # 添加导入
            import_line = "from qianchuan_aw.utils.unified_appeal_state_manager import create_appeal_state_manager"
            
            # 找到合适的导入位置
            lines = content.split('\n')
            import_index = -1
            
            for i, line in enumerate(lines):
                if line.startswith('from qianchuan_aw.utils.logger import logger'):
                    import_index = i + 1
                    break
            
            if import_index > 0:
                lines.insert(import_index, import_line)
                
                # 替换状态更新方法
                new_method = '''
    def _update_campaign_appeal_status(self, db: Session, campaign: Campaign, result: Dict[str, Any]):
        """更新计划的提审状态到数据库 - 使用统一状态管理器"""
        
        try:
            # 使用统一状态管理器
            manager = create_appeal_state_manager(db)
            
            if result['success']:
                # 提审成功，使用统一状态管理器更新
                update_result = manager.set_appeal_success(
                    campaign.campaign_id_qc, 
                    f"智能提审成功: {result.get('message', '')}"
                )
                
                if update_result['success']:
                    logger.success(f"💾 计划 {campaign.campaign_id_qc} 状态更新成功: {update_result['message']}")
                else:
                    logger.error(f"❌ 计划 {campaign.campaign_id_qc} 状态更新失败: {update_result['message']}")
                    
            else:
                # 提审失败，记录错误
                campaign.appeal_error_message = result.get('error', '提审失败')
                campaign.last_updated = datetime.now(timezone.utc)
                
                logger.error(f"❌ 计划 {campaign.campaign_id_qc} 提审失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            logger.error(f"❌ 更新计划 {campaign.campaign_id_qc} 状态时发生异常: {e}", exc_info=True)
            campaign.appeal_error_message = f"状态更新异常: {str(e)}"
'''
                
                # 替换原方法
                content = '\n'.join(lines)
                
                # 找到并替换原方法
                method_start = content.find('def _update_campaign_appeal_status(self, db: Session, campaign: Campaign, result: Dict[str, Any]):')
                if method_start > 0:
                    # 找到方法结束位置
                    method_lines = content[method_start:].split('\n')
                    method_end_line = 0
                    indent_level = None
                    
                    for i, line in enumerate(method_lines[1:], 1):  # 跳过方法定义行
                        if line.strip() == '':
                            continue
                        
                        current_indent = len(line) - len(line.lstrip())
                        
                        if indent_level is None and line.strip():
                            indent_level = current_indent
                        
                        # 如果遇到同级或更低级的缩进，说明方法结束
                        if line.strip() and current_indent <= 4:  # 类方法级别的缩进
                            method_end_line = i
                            break
                    
                    if method_end_line > 0:
                        method_end = method_start + len('\n'.join(method_lines[:method_end_line]))
                        new_content = content[:method_start] + new_method + content[method_end:]
                        
                        # 写入文件
                        with open(file_path, 'w', encoding='utf-8') as f:
                            f.write(new_content)
                        
                        logger.success("✅ 智能提审服务重构完成")
                        return True
            
            logger.error("❌ 未找到合适的重构位置")
            return False
            
        except Exception as e:
            logger.error(f"❌ 重构智能提审服务失败: {e}", exc_info=True)
            return False
    
    def generate_refactor_report(self) -> Dict[str, Any]:
        """生成重构报告"""
        analysis = self.analyze_refactor_scope()
        
        report = {
            'analysis': analysis,
            'refactor_plan': {
                'phase1': '重构智能提审服务（最高优先级）',
                'phase2': '重构手动提审工作流',
                'phase3': '废弃旧版状态管理器',
                'phase4': '更新其他服务模块'
            },
            'estimated_effort': {
                'high_priority_files': analysis['high_priority'],
                'total_methods_to_refactor': len(self.refactor_targets),
                'estimated_hours': analysis['high_priority'] * 2 + analysis['medium_priority'] * 1
            }
        }
        
        logger.info("📋 重构计划:")
        for phase, description in report['refactor_plan'].items():
            logger.info(f"   {phase}: {description}")
        
        logger.info(f"📊 预估工作量: {report['estimated_effort']['estimated_hours']} 小时")
        
        return report


def main():
    """主函数"""
    refactor = UnifiedStateManagerRefactor()
    
    logger.info("🚀 开始统一状态管理器重构")
    logger.info("="*60)
    
    # 分析重构范围
    report = refactor.generate_refactor_report()
    
    # 执行第一阶段重构：智能提审服务
    logger.info("\n🔧 执行第一阶段重构...")
    success = refactor.refactor_smart_appeal_service()
    
    if success:
        logger.success("🎉 第一阶段重构完成！")
    else:
        logger.error("❌ 第一阶段重构失败")
    
    return report


if __name__ == "__main__":
    main()
