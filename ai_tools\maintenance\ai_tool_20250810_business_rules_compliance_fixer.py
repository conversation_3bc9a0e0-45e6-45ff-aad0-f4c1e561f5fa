#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复千川自动化系统业务铁律违规问题
清理条件: 修复完成后可归档，但建议保留作为参考
"""

import sys
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


class BusinessRulesComplianceFixer:
    """业务铁律合规性修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.fixes_failed = []
        
    def fix_all_business_rules_violations(self) -> Dict[str, Any]:
        """修复所有业务铁律违规问题"""
        logger.info("⚖️ 开始修复业务铁律违规问题...")
        
        results = {
            'total_fixes': 3,
            'successful_fixes': 0,
            'failed_fixes': 0,
            'fixes_applied': [],
            'fixes_failed': []
        }
        
        # 修复1: 账户类型规范化（铁律1）
        try:
            self._fix_account_type_normalization()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('账户类型规范化修复')
        except Exception as e:
            logger.error(f"账户类型规范化修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'账户类型规范化: {str(e)}')
        
        # 修复2: 素材唯一性约束修复（铁律2）
        try:
            self._fix_material_uniqueness_violations()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('素材唯一性约束修复')
        except Exception as e:
            logger.error(f"素材唯一性约束修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'素材唯一性约束: {str(e)}')
        
        # 修复3: 重复提审问题修复（铁律4）
        try:
            self._fix_duplicate_appeal_issues()
            results['successful_fixes'] += 1
            results['fixes_applied'].append('重复提审问题修复')
        except Exception as e:
            logger.error(f"重复提审问题修复失败: {e}")
            results['failed_fixes'] += 1
            results['fixes_failed'].append(f'重复提审问题: {str(e)}')
        
        logger.info(f"✅ 业务铁律修复完成: {results['successful_fixes']}/{results['total_fixes']}")
        return results
    
    def _fix_account_type_normalization(self):
        """修复账户类型规范化问题"""
        logger.info("🔧 修复账户类型规范化...")
        
        # 创建账户类型规范化脚本
        normalization_script = '''
-- 账户类型规范化脚本
-- 根据账户名称自动设置正确的账户类型

-- 1. 设置测试账户类型
UPDATE ad_accounts 
SET account_type = 'TEST'
WHERE (name ILIKE '%测试%' OR name ILIKE '%test%')
  AND account_type != 'TEST';

-- 2. 设置投放账户类型  
UPDATE ad_accounts 
SET account_type = 'DELIVERY'
WHERE (name ILIKE '%投放%' OR name ILIKE '%素材%' OR name ILIKE '%delivery%')
  AND account_type != 'DELIVERY';

-- 3. 对于其他账户，根据使用模式设置类型
-- 如果账户主要用于测试（有测试计划），设置为TEST
UPDATE ad_accounts 
SET account_type = 'TEST'
WHERE account_type = 'UNSET'
  AND id IN (
    SELECT DISTINCT aa.id
    FROM ad_accounts aa
    JOIN campaigns c ON aa.id = c.account_id
    WHERE c.status IN ('AUDITING', 'AUDIT_ACCEPTED')
    GROUP BY aa.id
    HAVING COUNT(c.id) <= 10  -- 计划数量较少，可能是测试账户
  );

-- 4. 其余账户设置为DELIVERY
UPDATE ad_accounts 
SET account_type = 'DELIVERY'
WHERE account_type = 'UNSET';

-- 查看修复结果
SELECT 
    account_type,
    COUNT(*) as account_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM ad_accounts 
GROUP BY account_type 
ORDER BY account_count DESC;
'''
        
        # 保存规范化脚本
        script_file = project_root / 'ai_temp' / 'account_type_normalization_20250810.sql'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(normalization_script)
        
        logger.success(f"✅ 账户类型规范化脚本已创建: {script_file}")
        logger.info("📋 请使用MCP execute_sql工具执行此脚本")
    
    def _fix_material_uniqueness_violations(self):
        """修复素材唯一性约束违规"""
        logger.info("🔧 修复素材唯一性约束违规...")
        
        # 创建素材唯一性修复脚本
        uniqueness_fix_script = '''
-- 素材唯一性约束修复脚本
-- 处理违反测试视频唯一性的重复计划

-- 1. 查找违反唯一性约束的素材
WITH duplicate_materials AS (
    SELECT 
        lc.file_hash,
        COUNT(DISTINCT c.id) as plan_count,
        ARRAY_AGG(DISTINCT c.campaign_id_qc ORDER BY c.created_at) as campaign_ids,
        ARRAY_AGG(DISTINCT c.id ORDER BY c.created_at) as internal_ids
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN ad_accounts aa ON pc.account_id = aa.id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.file_hash
    HAVING COUNT(DISTINCT c.id) > 1
)
SELECT 
    file_hash,
    plan_count,
    campaign_ids,
    internal_ids
FROM duplicate_materials
ORDER BY plan_count DESC;

-- 2. 标记重复的计划（保留最早的，标记其他的）
-- 注意：这里只是标记，不直接删除，需要人工确认
UPDATE campaigns 
SET status = 'DUPLICATE_MARKED_FOR_REVIEW'
WHERE id IN (
    WITH duplicate_materials AS (
        SELECT 
            lc.file_hash,
            ARRAY_AGG(c.id ORDER BY c.created_at) as campaign_internal_ids
        FROM local_creatives lc
        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
        JOIN ad_accounts aa ON pc.account_id = aa.id
        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
        JOIN campaigns c ON cpca.campaign_id = c.id
        WHERE aa.account_type = 'TEST'
        GROUP BY lc.file_hash
        HAVING COUNT(DISTINCT c.id) > 1
    )
    SELECT UNNEST(campaign_internal_ids[2:])  -- 保留第一个，标记其余的
    FROM duplicate_materials
);

-- 3. 查看标记结果
SELECT 
    COUNT(*) as marked_duplicate_count
FROM campaigns 
WHERE status = 'DUPLICATE_MARKED_FOR_REVIEW';
'''
        
        # 保存修复脚本
        script_file = project_root / 'ai_temp' / 'material_uniqueness_fix_20250810.sql'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(uniqueness_fix_script)
        
        logger.success(f"✅ 素材唯一性修复脚本已创建: {script_file}")
        logger.info("📋 请使用MCP execute_sql工具执行此脚本")
    
    def _fix_duplicate_appeal_issues(self):
        """修复重复提审问题"""
        logger.info("🔧 修复重复提审问题...")
        
        # 创建重复提审修复脚本
        appeal_fix_script = '''
-- 重复提审问题修复脚本
-- 清理和规范化提审状态

-- 1. 查找可能的重复提审情况
SELECT 
    campaign_id_qc,
    appeal_status,
    first_appeal_at,
    last_appeal_at,
    EXTRACT(EPOCH FROM (last_appeal_at - first_appeal_at))/60 as minutes_between_appeals,
    CASE 
        WHEN first_appeal_at IS NOT NULL AND last_appeal_at IS NOT NULL 
             AND first_appeal_at != last_appeal_at THEN 'POTENTIAL_DUPLICATE'
        WHEN appeal_status = 'appeal_pending' AND first_appeal_at IS NOT NULL THEN 'ALREADY_APPEALED'
        ELSE 'OK'
    END as violation_status
FROM campaigns 
WHERE appeal_status IN ('appeal_pending', 'appeal_success', 'appeal_failed')
  AND (
    (first_appeal_at IS NOT NULL AND last_appeal_at IS NOT NULL AND first_appeal_at != last_appeal_at)
    OR (appeal_status = 'appeal_pending' AND first_appeal_at IS NOT NULL)
  )
ORDER BY violation_status, campaign_id_qc;

-- 2. 修复重复提审状态
-- 对于已经成功的提审，清理重复标记
UPDATE campaigns 
SET appeal_status = 'appeal_success'
WHERE appeal_status = 'appeal_pending' 
  AND first_appeal_at IS NOT NULL 
  AND last_appeal_at IS NOT NULL
  AND status IN ('AUDIT_ACCEPTED', 'DELIVERY_OK');

-- 3. 对于真正pending的提审，确保状态一致
UPDATE campaigns 
SET appeal_status = 'appeal_pending'
WHERE status = 'AUDITING' 
  AND appeal_status IS NULL 
  AND first_appeal_at IS NOT NULL;

-- 4. 清理异常的提审状态
UPDATE campaigns 
SET 
    appeal_status = NULL,
    first_appeal_at = NULL,
    last_appeal_at = NULL
WHERE status NOT IN ('AUDITING', 'AUDIT_ACCEPTED', 'AUDIT_REJECT', 'DELIVERY_OK')
  AND appeal_status IS NOT NULL;

-- 5. 查看修复结果
SELECT 
    appeal_status,
    COUNT(*) as count,
    COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as with_appeal_time
FROM campaigns 
WHERE appeal_status IS NOT NULL
GROUP BY appeal_status
ORDER BY count DESC;
'''
        
        # 保存修复脚本
        script_file = project_root / 'ai_temp' / 'duplicate_appeal_fix_20250810.sql'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(appeal_fix_script)
        
        logger.success(f"✅ 重复提审修复脚本已创建: {script_file}")
        logger.info("📋 请使用MCP execute_sql工具执行此脚本")


def main():
    """主函数"""
    fixer = BusinessRulesComplianceFixer()
    
    try:
        results = fixer.fix_all_business_rules_violations()
        
        print("\n" + "="*60)
        print("⚖️ 业务铁律合规性修复结果")
        print("="*60)
        print(f"总修复项目: {results['total_fixes']}")
        print(f"成功修复: {results['successful_fixes']}")
        print(f"修复失败: {results['failed_fixes']}")
        
        if results['fixes_applied']:
            print("\n✅ 成功修复的问题:")
            for fix in results['fixes_applied']:
                print(f"  - {fix}")
        
        if results['fixes_failed']:
            print("\n❌ 修复失败的问题:")
            for fix in results['fixes_failed']:
                print(f"  - {fix}")
        
        print("\n📋 后续步骤:")
        print("1. 使用MCP execute_sql工具执行生成的SQL脚本")
        print("2. 验证修复效果")
        print("3. 运行健康检查工具确认合规性")
        print("4. 监控系统运行状态")
        
        print("\n📄 生成的修复脚本:")
        print("  - ai_temp/account_type_normalization_20250810.sql")
        print("  - ai_temp/material_uniqueness_fix_20250810.sql") 
        print("  - ai_temp/duplicate_appeal_fix_20250810.sql")
        
    except Exception as e:
        logger.error(f"修复过程发生错误: {e}", exc_info=True)
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
