#!/usr/bin/env python3
"""
验证96个恢复文件是否已经创建过测试计划
确保严格遵循"测试阶段一视频一计划"规则
"""

import sys
import os
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class RecoveredFilesVerifier:
    """恢复文件验证器"""
    
    def __init__(self):
        logger.critical("🔍 验证96个恢复文件是否已创建过测试计划")
        logger.critical("=" * 60)
    
    def verify_recovered_files_campaigns(self):
        """验证恢复文件的计划状态"""
        logger.critical("📊 验证恢复文件的计划状态")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查询所有在归档目录中找到的文件对应的素材记录
                archived_files_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.file_path,
                        lc.file_hash,
                        lc.status,
                        lc.created_at,
                        COUNT(c.id) as campaign_count,
                        STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids,
                        STRING_AGG(aa.name, ', ') as account_names
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    LEFT JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE lc.status = MaterialStatus.NEW.value
                    AND lc.file_path LIKE '%00_materials_archived%'
                    GROUP BY lc.id, lc.filename, lc.file_path, lc.file_hash, lc.status, lc.created_at
                    ORDER BY campaign_count DESC, lc.filename
                """)
                
                archived_results = db.execute(archived_files_query).fetchall()
                
                logger.critical(f"📊 归档目录中的素材分析:")
                logger.critical(f"  总数: {len(archived_results)} 个")
                
                # 分类统计
                has_campaigns = []
                no_campaigns = []
                
                for result in archived_results:
                    if result.campaign_count > 0:
                        has_campaigns.append(result)
                    else:
                        no_campaigns.append(result)
                
                logger.critical(f"  已有计划: {len(has_campaigns)} 个")
                logger.critical(f"  无计划: {len(no_campaigns)} 个")
                
                # 显示已有计划的详情
                if has_campaigns:
                    logger.critical(f"\n🚨 已有计划的归档文件 (不应重新入库):")
                    for result in has_campaigns[:10]:  # 显示前10个
                        logger.critical(f"  📋 {result.filename}:")
                        logger.critical(f"    计划数: {result.campaign_count}")
                        logger.critical(f"    计划ID: {result.campaign_ids}")
                        logger.critical(f"    账户: {result.account_names}")
                        logger.critical(f"    状态: {result.status}")
                
                # 显示无计划的详情
                if no_campaigns:
                    logger.critical(f"\n✅ 无计划的归档文件 (可以重新入库):")
                    for result in no_campaigns[:10]:  # 显示前10个
                        logger.critical(f"  📋 {result.filename}:")
                        logger.critical(f"    状态: {result.status}")
                        logger.critical(f"    创建时间: {result.created_at}")
                
                return {
                    'total_archived': len(archived_results),
                    'has_campaigns': has_campaigns,
                    'no_campaigns': no_campaigns,
                    'safe_to_recover': len(no_campaigns)
                }
                
        except Exception as e:
            logger.error(f"❌ 验证恢复文件失败: {e}")
            return None
    
    def check_file_hash_duplicates(self):
        """通过file_hash检查重复"""
        logger.critical("\n🔍 通过file_hash检查重复计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查找通过file_hash可能重复的计划
                hash_duplicate_query = text("""
                    WITH new_archived_materials AS (
                        SELECT 
                            lc.id,
                            lc.filename,
                            lc.file_hash,
                            lc.status
                        FROM local_creatives lc
                        WHERE lc.status = MaterialStatus.NEW.value
                        AND lc.file_path LIKE '%00_materials_archived%'
                    ),
                    existing_campaigns_by_hash AS (
                        SELECT 
                            lc.file_hash,
                            COUNT(c.id) as campaign_count,
                            STRING_AGG(c.campaign_id_qc, ', ') as campaign_ids,
                            STRING_AGG(aa.name, ', ') as account_names,
                            STRING_AGG(lc2.filename, ', ') as existing_filenames
                        FROM local_creatives lc
                        JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                        JOIN campaigns c ON cpca.campaign_id = c.id
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        JOIN local_creatives lc2 ON lc.file_hash = lc2.file_hash
                        WHERE lc.status != MaterialStatus.NEW.value
                        GROUP BY lc.file_hash
                    )
                    SELECT 
                        nam.id,
                        nam.filename,
                        nam.file_hash,
                        nam.status,
                        COALESCE(ecbh.campaign_count, 0) as existing_campaign_count,
                        ecbh.campaign_ids,
                        ecbh.account_names,
                        ecbh.existing_filenames
                    FROM new_archived_materials nam
                    LEFT JOIN existing_campaigns_by_hash ecbh ON nam.file_hash = ecbh.file_hash
                    WHERE ecbh.campaign_count > 0
                    ORDER BY ecbh.campaign_count DESC, nam.filename
                """)
                
                hash_conflicts = db.execute(hash_duplicate_query).fetchall()
                
                logger.critical(f"📊 file_hash重复检查结果:")
                logger.critical(f"  发现重复: {len(hash_conflicts)} 个")
                
                if hash_conflicts:
                    logger.critical(f"\n🚨 通过file_hash发现的重复计划:")
                    for conflict in hash_conflicts[:10]:
                        logger.critical(f"  📋 {conflict.filename}:")
                        logger.critical(f"    file_hash: {conflict.file_hash[:16]}...")
                        logger.critical(f"    已有计划数: {conflict.existing_campaign_count}")
                        logger.critical(f"    计划ID: {conflict.campaign_ids}")
                        logger.critical(f"    现有文件名: {conflict.existing_filenames}")
                
                return hash_conflicts
                
        except Exception as e:
            logger.error(f"❌ file_hash重复检查失败: {e}")
            return []
    
    def generate_safe_recovery_plan(self, verification_result, hash_conflicts):
        """生成安全的恢复计划"""
        logger.critical("\n📋 生成安全的恢复计划")
        logger.critical("=" * 60)
        
        if not verification_result:
            logger.error("❌ 没有验证结果，无法生成恢复计划")
            return None
        
        # 获取安全可恢复的文件
        safe_files = verification_result['no_campaigns']
        
        # 排除file_hash重复的文件
        hash_conflict_ids = {conflict.id for conflict in hash_conflicts}
        truly_safe_files = [f for f in safe_files if f.id not in hash_conflict_ids]
        
        logger.critical(f"📊 安全恢复计划:")
        logger.critical(f"  归档文件总数: {verification_result['total_archived']}")
        logger.critical(f"  已有计划: {len(verification_result['has_campaigns'])}")
        logger.critical(f"  无计划: {len(safe_files)}")
        logger.critical(f"  file_hash冲突: {len(hash_conflicts)}")
        logger.critical(f"  真正安全可恢复: {len(truly_safe_files)}")
        
        if truly_safe_files:
            logger.critical(f"\n✅ 可以安全恢复的文件:")
            for safe_file in truly_safe_files[:10]:
                logger.critical(f"  - {safe_file.filename}")
            
            if len(truly_safe_files) > 10:
                logger.critical(f"  ... 还有 {len(truly_safe_files) - 10} 个")
        
        # 生成恢复脚本
        if truly_safe_files:
            recovery_script = '''@echo off
REM 安全恢复验证通过的文件
echo 开始恢复验证通过的文件...
echo 这些文件确认没有创建过测试计划

'''
            for safe_file in truly_safe_files:
                filename = safe_file.filename
                source_path = f"G:\\workflow_assets\\00_materials_archived\\缇萃百货\\{filename}"
                recovery_script += f'''
REM 恢复文件: {filename}
if exist "{source_path}" (
    copy "{source_path}" "G:\\workflow_assets\\01_materials_to_process\\缇萃百货\\{filename}"
    echo 成功恢复: {filename}
) else (
    echo 警告: 归档文件不存在 - {filename}
)
'''
            
            recovery_script += '''
echo 安全恢复完成
pause
'''
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            safe_recovery_file = f"ai_reports/material_recovery/safe_recovery_{timestamp}.bat"
            
            with open(safe_recovery_file, 'w', encoding='gbk') as f:
                f.write(recovery_script)
            
            logger.critical(f"✅ 安全恢复脚本: {safe_recovery_file}")
        
        # 生成状态更新SQL
        if verification_result['has_campaigns']:
            status_update_sql = '''-- 更新已有计划的归档文件状态
-- 这些文件已经创建过计划，不应重新入库

BEGIN;

'''
            for file_with_campaign in verification_result['has_campaigns']:
                material_id = file_with_campaign.id
                filename = file_with_campaign.filename
                status_update_sql += f'''-- 更新素材: {filename} (ID: {material_id})
UPDATE local_creatives 
SET status = MaterialStatus.ALREADY_TESTED.value,
    updated_at = NOW()
WHERE id = {material_id};

'''
            
            status_update_sql += '''
COMMIT;

-- 验证更新结果
SELECT 
    status,
    COUNT(*) as count
FROM local_creatives 
WHERE file_path LIKE '%00_materials_archived%'
GROUP BY status;
'''
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            status_update_file = f"ai_reports/material_recovery/update_archived_status_{timestamp}.sql"
            
            with open(status_update_file, 'w', encoding='utf-8') as f:
                f.write(status_update_sql)
            
            logger.critical(f"✅ 状态更新SQL: {status_update_file}")
        
        return {
            'safe_recovery_file': safe_recovery_file if truly_safe_files else None,
            'status_update_file': status_update_file if verification_result['has_campaigns'] else None,
            'truly_safe_count': len(truly_safe_files),
            'blocked_count': len(verification_result['has_campaigns']) + len(hash_conflicts)
        }
    
    def generate_final_recommendations(self, recovery_plan):
        """生成最终建议"""
        logger.critical("\n💡 最终恢复建议")
        logger.critical("=" * 60)
        
        logger.critical("🎯 基于验证结果的最终建议:")
        
        if recovery_plan and recovery_plan['truly_safe_count'] > 0:
            logger.critical(f"\n✅ 可以安全恢复:")
            logger.critical(f"  安全文件数: {recovery_plan['truly_safe_count']} 个")
            logger.critical(f"  恢复脚本: {recovery_plan['safe_recovery_file']}")
            logger.critical(f"  这些文件确认没有创建过测试计划")
        else:
            logger.critical(f"\n❌ 没有可以安全恢复的文件")
        
        if recovery_plan and recovery_plan['blocked_count'] > 0:
            logger.critical(f"\n⚠️ 被阻止恢复:")
            logger.critical(f"  阻止文件数: {recovery_plan['blocked_count']} 个")
            logger.critical(f"  状态更新SQL: {recovery_plan.get('status_update_file', '无')}")
            logger.critical(f"  原因: 已创建过测试计划或file_hash重复")
        
        logger.critical(f"\n🚀 推荐执行步骤:")
        logger.critical("  1. 先执行状态更新SQL，标记已有计划的文件")
        logger.critical("  2. 再执行安全恢复脚本，恢复确认安全的文件")
        logger.critical("  3. 处理4个立即可用的文件")
        logger.critical("  4. 清理453个无法恢复的数据库记录")
        logger.critical("  5. 重启工作流处理恢复的文件")
        
        logger.critical(f"\n⚠️ 严格遵循业务规则:")
        logger.critical("  - 测试阶段：每个视频只能创建一个测试计划")
        logger.critical("  - 正式投放：同一视频可以创建多个计划")
        logger.critical("  - 通过file_hash确保唯一性")

def main():
    """主验证函数"""
    try:
        verifier = RecoveredFilesVerifier()
        
        # 1. 验证恢复文件的计划状态
        verification_result = verifier.verify_recovered_files_campaigns()
        
        if not verification_result:
            return False
        
        # 2. 检查file_hash重复
        hash_conflicts = verifier.check_file_hash_duplicates()
        
        # 3. 生成安全恢复计划
        recovery_plan = verifier.generate_safe_recovery_plan(verification_result, hash_conflicts)
        
        # 4. 生成最终建议
        verifier.generate_final_recommendations(recovery_plan)
        
        logger.critical(f"\n🎉 恢复文件验证完成!")
        if recovery_plan:
            logger.critical(f"安全恢复: {recovery_plan['truly_safe_count']} 个")
            logger.critical(f"被阻止: {recovery_plan['blocked_count']} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    main()
