# -*- coding: utf-8 -*-
"""
[V3.0] 高优先级Celery Worker 启动脚本 - 专门处理上传和创建任务
- 专用于处理upload_single_video、batch_upload_videos、batch_create_plans等高优先级任务
- 更高的并发数，更快的响应速度
- 独立的资源管理
"""
import sys
import os
import signal
import atexit

# --- 核心修复：在导入任何模块之前，首先修正 sys.path ---
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)
# ---------------------------------------------------------

# 现在路径已正确，可以安全地导入我们的模块和第三方库了
from celery.bin import celery
from qianchuan_aw.utils.logger import logger

# [V3.0] 集成生命周期管理器
try:
    from qianchuan_aw.utils.celery_lifecycle_manager import get_lifecycle_manager
    lifecycle_manager = get_lifecycle_manager()
    logger.info("✅ 高优先级Worker生命周期管理器已启动")
except ImportError as e:
    logger.warning(f"⚠️ 生命周期管理器导入失败，使用基础模式: {e}")
    lifecycle_manager = None

# [V3.0] 集成浏览器进程池清理
def cleanup_resources():
    """清理所有资源"""
    logger.info("🧹 开始清理高优先级Worker资源...")

    try:
        # 清理浏览器进程池
        from qianchuan_aw.utils.browser_process_pool_sync_adapter import cleanup_sync_browser_adapter
        cleanup_sync_browser_adapter()
        logger.info("✅ 浏览器进程池已清理")
    except Exception as e:
        logger.error(f"❌ 清理浏览器进程池失败: {e}")

    try:
        # 清理生命周期管理器
        if lifecycle_manager:
            lifecycle_manager.initiate_graceful_shutdown()
        logger.info("✅ 生命周期管理器已清理")
    except Exception as e:
        logger.error(f"❌ 清理生命周期管理器失败: {e}")

# 注册清理函数
atexit.register(cleanup_resources)

# 信号处理器
def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"🛑 收到信号 {signum}，开始优雅关闭高优先级Worker...")
    cleanup_resources()
    sys.exit(0)

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# [V3.0] 高优先级Worker配置说明
# 
# 为什么选择线程池 (threads) 而不是进程池 (prefork)？
# 
# 优势:
# 1. 平台原生: 基于 Python 内置的 threading 模块，在 Windows 上具有最佳的稳定性和兼容性。
# 2. 完美契合: 我们的任务是 I/O 密集型（网络请求、文件读写），线程池能在此类场景下实现真正的并发。
# 3. 架构纯净: 不再需要 gevent.monkey.patch_all()，消除了所有与猴子补丁相关的潜在冲突。
# 4. 资源高效: 线程比进程更轻量，可以支持更高的并发数

if __name__ == '__main__':
    # 我们通过修改 sys.argv 来模拟命令行调用，这是最健壮的方式。
    original_argv = sys.argv

    sys.argv = [
        'celery',
        '-A', 'qianchuan_aw.celery_app.app',
        'worker',
        '-l', 'info',
        '-P', 'threads',  # 使用原生线程池
        '-c', '15',       # 专门为高优先级任务提供15个并发线程
        '--queues=high_priority',  # 只处理高优先级队列
        '--prefetch-multiplier=1',  # 减少预取，提高响应性
        '-n', 'high_priority_worker@%h'  # 设置Worker名称
    ]

    try:
        logger.info("🚀 启动高优先级Celery Worker...")
        logger.info("📋 配置信息:")
        logger.info("  - 并发数: 15个线程")
        logger.info("  - 队列: high_priority")
        logger.info("  - 任务类型: upload_single_video, batch_upload_videos, batch_create_plans")
        logger.info("  - 预取倍数: 1 (快速响应)")
        celery.main()
    finally:
        sys.argv = original_argv
