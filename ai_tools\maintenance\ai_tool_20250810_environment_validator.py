#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证和确保正确的虚拟环境配置
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


class EnvironmentValidator:
    """环境验证器 - 确保正确的虚拟环境配置"""
    
    def __init__(self):
        self.required_env = 'qc_env'
        self.current_env = self._get_current_environment()
        
    def _get_current_environment(self) -> Optional[str]:
        """获取当前激活的虚拟环境"""
        
        # 方法1: 检查CONDA_DEFAULT_ENV环境变量
        conda_env = os.environ.get('CONDA_DEFAULT_ENV')
        if conda_env:
            return conda_env
            
        # 方法2: 检查虚拟环境路径
        virtual_env = os.environ.get('VIRTUAL_ENV')
        if virtual_env:
            return Path(virtual_env).name
            
        # 方法3: 检查Python可执行文件路径
        python_path = sys.executable
        if 'envs' in python_path:
            # 从路径中提取环境名
            parts = Path(python_path).parts
            for i, part in enumerate(parts):
                if part == 'envs' and i + 1 < len(parts):
                    return parts[i + 1]
        
        return 'base'  # 默认为base环境
    
    def validate_environment(self) -> Dict[str, Any]:
        """验证当前环境是否正确"""
        
        result = {
            'is_valid': False,
            'current_env': self.current_env,
            'required_env': self.required_env,
            'python_path': sys.executable,
            'issues': [],
            'recommendations': []
        }
        
        # 检查环境名称
        if self.current_env != self.required_env:
            result['issues'].append(f"当前环境 '{self.current_env}' 不是要求的 '{self.required_env}'")
            result['recommendations'].append(f"执行: conda activate {self.required_env}")
        
        # 检查关键包是否可用
        try:
            import sqlalchemy
            result['sqlalchemy_version'] = sqlalchemy.__version__
        except ImportError:
            result['issues'].append("SQLAlchemy包不可用")
            result['recommendations'].append("安装SQLAlchemy: pip install sqlalchemy")
        
        try:
            import psycopg2
            result['psycopg2_available'] = True
        except ImportError:
            result['issues'].append("psycopg2包不可用")
            result['recommendations'].append("安装psycopg2: pip install psycopg2-binary")
        
        try:
            import redis
            result['redis_available'] = True
        except ImportError:
            result['issues'].append("redis包不可用")
            result['recommendations'].append("安装redis: pip install redis")
        
        # 检查项目特定模块
        try:
            from qianchuan_aw.utils.logger import logger
            result['project_modules_available'] = True
        except ImportError as e:
            result['issues'].append(f"项目模块导入失败: {e}")
            result['recommendations'].append("检查PYTHONPATH和项目结构")
        
        # 如果没有问题，标记为有效
        if not result['issues']:
            result['is_valid'] = True
        
        return result
    
    def get_environment_activation_command(self) -> str:
        """获取环境激活命令"""
        
        if os.name == 'nt':  # Windows
            return f"conda activate {self.required_env}"
        else:  # Linux/Mac
            return f"source activate {self.required_env}"
    
    def check_database_connectivity(self) -> Dict[str, Any]:
        """检查数据库连接（在正确环境下）"""
        
        if not self.validate_environment()['is_valid']:
            return {
                'success': False,
                'error': '环境验证失败，无法进行数据库连接测试'
            }
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from sqlalchemy import text

            with database_session() as db:
                result = db.execute(text("SELECT 1 as test")).fetchone()

            return {
                'success': True,
                'message': '数据库连接正常',
                'test_result': result.test if result else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'数据库连接失败: {str(e)}'
            }
    
    def generate_environment_report(self) -> Dict[str, Any]:
        """生成完整的环境报告"""
        
        validation_result = self.validate_environment()
        db_result = self.check_database_connectivity()
        
        report = {
            'timestamp': str(Path(__file__).stat().st_mtime),
            'environment_validation': validation_result,
            'database_connectivity': db_result,
            'system_info': {
                'platform': sys.platform,
                'python_version': sys.version,
                'working_directory': str(Path.cwd()),
                'script_location': str(__file__)
            }
        }
        
        return report


def main():
    """主函数"""
    validator = EnvironmentValidator()
    
    print("🔍 千川自动化系统环境验证")
    print("=" * 50)
    
    # 生成环境报告
    report = validator.generate_environment_report()
    
    # 显示当前环境状态
    env_validation = report['environment_validation']
    print(f"当前环境: {env_validation['current_env']}")
    print(f"要求环境: {env_validation['required_env']}")
    print(f"Python路径: {env_validation['python_path']}")
    
    # 显示验证结果
    if env_validation['is_valid']:
        print("✅ 环境验证通过")
    else:
        print("❌ 环境验证失败")
        print("\n发现的问题:")
        for issue in env_validation['issues']:
            print(f"  - {issue}")
        
        print("\n建议的修复措施:")
        for rec in env_validation['recommendations']:
            print(f"  - {rec}")
    
    # 显示数据库连接状态
    db_result = report['database_connectivity']
    if db_result['success']:
        print("✅ 数据库连接正常")
    else:
        print(f"❌ 数据库连接失败: {db_result['error']}")
    
    # 显示包可用性
    print("\n📦 关键包状态:")
    if 'sqlalchemy_version' in env_validation:
        print(f"  ✅ SQLAlchemy: {env_validation['sqlalchemy_version']}")
    
    if env_validation.get('psycopg2_available'):
        print("  ✅ psycopg2: 可用")
    
    if env_validation.get('redis_available'):
        print("  ✅ redis: 可用")
    
    if env_validation.get('project_modules_available'):
        print("  ✅ 项目模块: 可用")
    
    # 如果环境不正确，提供激活命令
    if not env_validation['is_valid']:
        print(f"\n🔧 环境激活命令:")
        print(f"  {validator.get_environment_activation_command()}")
        return 1
    
    print("\n🎉 环境配置正确，可以安全执行千川自动化系统操作")
    return 0


if __name__ == "__main__":
    exit(main())
