#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 提审安全检查工具 - 确保不对正式投放计划进行提审
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import yaml
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_appeal_safety_config():
    """检查提审安全配置"""
    logger.info("🔍 检查提审安全配置...")
    
    try:
        # 加载配置文件
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查关键安全配置
        appeal_strategy = config.get('appeal_strategy', {})
        appeal_for_prod_plans = appeal_strategy.get('appeal_for_prod_plans', None)
        
        logger.info(f"📋 配置检查结果:")
        logger.info(f"   🔧 appeal_strategy 配置存在: {'✅' if appeal_strategy else '❌'}")
        logger.info(f"   🔧 appeal_for_prod_plans 配置: {appeal_for_prod_plans}")
        
        if appeal_for_prod_plans is None:
            logger.warning("⚠️ 未找到 appeal_for_prod_plans 配置，默认为 false")
            return True  # 返回True表示安全（不允许提审）
        elif appeal_for_prod_plans is False:
            logger.success("✅ 安全配置正确：禁止对正式投放计划进行提审")
            return True  # 返回True表示安全（不允许提审）
        else:
            logger.error("🚨 危险配置：允许对正式投放计划进行提审！")
            return False  # 返回False表示不安全（允许提审）
        
    except Exception as e:
        logger.error(f"❌ 检查配置失败: {e}")
        return False

def test_enhanced_scheduler_safety():
    """测试增强版调度器的安全性"""
    logger.info("🧪 测试增强版调度器安全性...")
    
    try:
        # 加载配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 导入增强版调度器
        sys.path.insert(0, str(project_root / 'ai_tools' / 'optimization'))
        from ai_tool_20250728_optimization_enhanced_smart_scheduler import EnhancedSmartAppealScheduler
        
        # 创建调度器实例
        scheduler = EnhancedSmartAppealScheduler(app_settings)
        
        # 尝试获取待提审计划
        grouped_plans = scheduler.get_enhanced_plans_grouped_by_account()
        
        # 验证结果
        if not grouped_plans:
            logger.success("✅ 增强版调度器安全检查通过：没有返回任何计划")
            return True
        else:
            logger.error(f"❌ 增强版调度器安全检查失败：返回了 {len(grouped_plans)} 个广告户的计划")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试增强版调度器失败: {e}")
        return False

def test_simple_scheduler_safety():
    """测试简化版调度器的安全性"""
    logger.info("🧪 测试简化版调度器安全性...")
    
    try:
        # 加载配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 导入简化版调度器
        sys.path.insert(0, str(project_root / 'ai_tools' / 'optimization'))
        from ai_tool_20250728_optimization_simple_smart_scheduler import get_plans_grouped_by_account_simple
        
        # 尝试获取待提审计划
        grouped_plans = get_plans_grouped_by_account_simple(60, app_settings)
        
        # 验证结果
        if not grouped_plans:
            logger.success("✅ 简化版调度器安全检查通过：没有返回任何计划")
            return True
        else:
            logger.error(f"❌ 简化版调度器安全检查失败：返回了 {len(grouped_plans)} 个广告户的计划")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试简化版调度器失败: {e}")
        return False

def check_existing_appeal_processes():
    """检查现有的提审进程"""
    logger.info("🔍 检查现有的提审进程...")
    
    try:
        import psutil
        
        # 查找可能的提审相关进程
        appeal_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline']:
                    cmdline_str = ' '.join(proc.info['cmdline'])
                    # 排除安全检查脚本本身
                    if 'security_appeal_safety_check' in cmdline_str:
                        continue

                    if any(keyword in cmdline_str.lower() for keyword in [
                        'appeal', 'smart_scheduler', 'copilot_service', 'production_appeal'
                    ]):
                        appeal_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline_str[:100] + '...' if len(cmdline_str) > 100 else cmdline_str
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if appeal_processes:
            logger.warning(f"⚠️ 发现 {len(appeal_processes)} 个可能的提审相关进程:")
            for proc in appeal_processes:
                logger.warning(f"   📋 PID: {proc['pid']}, 名称: {proc['name']}")
                logger.warning(f"      命令: {proc['cmdline']}")
        else:
            logger.success("✅ 没有发现运行中的提审相关进程")
        
        return len(appeal_processes) == 0
        
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法检查进程")
        return True
    except Exception as e:
        logger.error(f"❌ 检查进程失败: {e}")
        return False

def generate_safety_report():
    """生成安全检查报告"""
    logger.info("📊 生成安全检查报告...")
    
    try:
        # 执行所有安全检查
        checks = [
            ("配置文件安全检查", check_appeal_safety_config),
            ("增强版调度器安全检查", test_enhanced_scheduler_safety),
            ("简化版调度器安全检查", test_simple_scheduler_safety),
            ("现有进程检查", check_existing_appeal_processes)
        ]
        
        results = []
        
        for check_name, check_func in checks:
            logger.info(f"\n🔍 执行: {check_name}")
            try:
                result = check_func()
                results.append((check_name, result, None))
                status = "✅ 通过" if result else "❌ 失败"
                logger.info(f"   结果: {status}")
            except Exception as e:
                results.append((check_name, False, str(e)))
                logger.error(f"   结果: ❌ 异常 - {e}")
        
        # 生成报告
        logger.info("\n" + "="*80)
        logger.info("🛡️ 提审安全检查报告")
        logger.info("="*80)
        
        passed_count = sum(1 for _, result, _ in results if result)
        total_count = len(results)
        
        logger.info(f"📊 检查结果: {passed_count}/{total_count} 项通过")
        
        for check_name, result, error in results:
            if result:
                logger.info(f"   ✅ {check_name}: 通过")
            elif error:
                logger.info(f"   ❌ {check_name}: 异常 - {error}")
            else:
                logger.info(f"   ❌ {check_name}: 失败")
        
        # 安全评估
        if passed_count == total_count:
            logger.success("\n🎉 安全检查完全通过！")
            logger.info("💡 系统配置安全，不会对正式投放计划进行提审")
            safety_level = "安全"
        elif passed_count >= total_count * 0.75:
            logger.warning("\n⚠️ 安全检查基本通过，但有部分问题")
            logger.warning("💡 建议检查并修复失败的项目")
            safety_level = "基本安全"
        else:
            logger.error("\n🚨 安全检查失败！")
            logger.error("💡 存在安全风险，请立即检查配置")
            safety_level = "不安全"
        
        # 生成建议
        logger.info(f"\n📋 安全级别: {safety_level}")
        logger.info("💡 安全建议:")
        logger.info("1. 确保 appeal_strategy.appeal_for_prod_plans = false")
        logger.info("2. 定期检查提审相关进程")
        logger.info("3. 监控提审操作日志")
        logger.info("4. 在生产环境中禁用自动提审功能")
        
        return safety_level == "安全"
        
    except Exception as e:
        logger.error(f"❌ 生成安全报告失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🛡️ 提审安全检查工具")
    logger.info("="*80)
    logger.info("🎯 目标: 确保不对正式投放计划进行提审操作")
    logger.info("🔍 检查内容:")
    logger.info("1. 配置文件安全设置")
    logger.info("2. 调度器安全机制")
    logger.info("3. 现有进程状态")
    logger.info("4. 生成安全报告")
    logger.info("="*80)
    
    try:
        # 生成安全检查报告
        is_safe = generate_safety_report()
        
        if is_safe:
            logger.success("\n🎉 系统安全检查通过！")
            logger.info("💡 配置正确，不会对正式投放计划进行提审")
        else:
            logger.error("\n🚨 系统存在安全风险！")
            logger.error("💡 请立即检查配置并修复问题")
        
        return is_safe
        
    except Exception as e:
        logger.error(f"❌ 安全检查过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 提审安全检查完成，系统安全！")
        logger.info("💡 可以放心使用智能提审调度器")
    else:
        logger.error("\n❌ 提审安全检查失败，存在风险！")
        logger.error("💡 请修复安全问题后再使用")
    
    sys.exit(0 if success else 1)
