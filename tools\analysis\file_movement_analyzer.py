#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件移动逻辑分析工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 分析文件移动逻辑，找出文件路径同步问题的根本原因
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class FileMovementAnalyzer:
    """文件移动逻辑分析器"""
    
    def __init__(self):
        self.workflow_assets_dir = Path("D:/workflow_assets")
        self.analysis_results = {}
    
    def analyze_directory_structure(self) -> Dict:
        """分析目录结构"""
        print("🔍 分析workflow_assets目录结构...")
        
        structure = {}
        
        if not self.workflow_assets_dir.exists():
            print(f"❌ 目录不存在: {self.workflow_assets_dir}")
            return structure
        
        for item in self.workflow_assets_dir.iterdir():
            if item.is_dir():
                file_count = len(list(item.rglob("*.mp4")))
                structure[item.name] = {
                    'path': str(item),
                    'file_count': file_count,
                    'subdirs': []
                }
                
                # 分析子目录
                for subdir in item.iterdir():
                    if subdir.is_dir():
                        subdir_file_count = len(list(subdir.rglob("*.mp4")))
                        structure[item.name]['subdirs'].append({
                            'name': subdir.name,
                            'file_count': subdir_file_count
                        })
        
        print(f"📊 目录结构分析:")
        for dir_name, info in structure.items():
            print(f"  - {dir_name}: {info['file_count']} 个文件")
            for subdir in info['subdirs'][:3]:  # 只显示前3个子目录
                print(f"    └─ {subdir['name']}: {subdir['file_count']} 个文件")
        
        return structure
    
    def analyze_file_movement_patterns(self) -> Dict:
        """分析文件移动模式"""
        print("\n🔍 分析文件移动模式...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            from sqlalchemy import func
            
            with database_session() as db:
                # 按状态分组分析文件路径模式
                status_path_patterns = {}
                
                for status in [
                    MaterialStatus.PENDING_UPLOAD.value,
                    MaterialStatus.UPLOADING.value,
                    MaterialStatus.PROCESSING.value,
                    MaterialStatus.APPROVED.value,
                    MaterialStatus.REJECTED.value,
                    MaterialStatus.UPLOAD_FAILED.value
                ]:
                    materials = db.query(LocalCreative).filter(
                        LocalCreative.status == status
                    ).limit(100).all()
                    
                    path_patterns = defaultdict(int)
                    file_exists_count = 0
                    
                    for material in materials:
                        file_path = Path(material.file_path)
                        
                        # 提取路径模式
                        if 'workflow_assets' in str(file_path):
                            parts = file_path.parts
                            workflow_index = next(i for i, part in enumerate(parts) if 'workflow_assets' in part)
                            if workflow_index + 1 < len(parts):
                                pattern = parts[workflow_index + 1]  # 第一级子目录
                                path_patterns[pattern] += 1
                        
                        if file_path.exists():
                            file_exists_count += 1
                    
                    status_path_patterns[status] = {
                        'total_count': len(materials),
                        'file_exists_count': file_exists_count,
                        'file_exists_rate': (file_exists_count / len(materials) * 100) if materials else 0,
                        'path_patterns': dict(path_patterns)
                    }
                
                return status_path_patterns
                
        except Exception as e:
            print(f"❌ 分析文件移动模式失败: {e}")
            return {}
    
    def identify_file_movement_logic(self) -> Dict:
        """识别文件移动逻辑"""
        print("\n🔍 识别文件移动逻辑...")
        
        # 基于目录名称推断移动逻辑
        movement_logic = {
            '01_materials_to_process': '待处理素材目录',
            '00_processed': '已处理素材目录',
            '02_uploaded': '已上传素材目录',
            '03_approved': '已审核通过素材目录',
            '04_rejected': '被拒绝素材目录',
            '05_failed': '失败素材目录'
        }
        
        # 检查实际存在的目录
        actual_dirs = {}
        for dir_name, description in movement_logic.items():
            dir_path = self.workflow_assets_dir / dir_name
            if dir_path.exists():
                file_count = len(list(dir_path.rglob("*.mp4")))
                actual_dirs[dir_name] = {
                    'description': description,
                    'exists': True,
                    'file_count': file_count
                }
            else:
                actual_dirs[dir_name] = {
                    'description': description,
                    'exists': False,
                    'file_count': 0
                }
        
        # 检查其他数字开头的目录
        other_dirs = {}
        for item in self.workflow_assets_dir.iterdir():
            if item.is_dir() and item.name not in movement_logic:
                if item.name[0].isdigit():
                    file_count = len(list(item.rglob("*.mp4")))
                    other_dirs[item.name] = {
                        'file_count': file_count,
                        'description': '未知用途目录'
                    }
        
        return {
            'expected_dirs': actual_dirs,
            'other_dirs': other_dirs
        }
    
    def analyze_status_file_location_correlation(self) -> Dict:
        """分析状态与文件位置的关联性"""
        print("\n🔍 分析状态与文件位置的关联性...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            correlation_analysis = {}
            
            with database_session() as db:
                # 分析每种状态下文件的实际位置
                for status in [
                    MaterialStatus.PENDING_UPLOAD.value,
                    MaterialStatus.UPLOADING.value,
                    MaterialStatus.PROCESSING.value,
                    MaterialStatus.APPROVED.value,
                    MaterialStatus.REJECTED.value,
                    MaterialStatus.UPLOAD_FAILED.value
                ]:
                    materials = db.query(LocalCreative).filter(
                        LocalCreative.status == status
                    ).all()
                    
                    location_stats = defaultdict(int)
                    missing_files = 0
                    
                    for material in materials:
                        file_path = Path(material.file_path)
                        
                        if file_path.exists():
                            # 提取文件所在的主目录
                            if 'workflow_assets' in str(file_path):
                                parts = file_path.parts
                                try:
                                    workflow_index = next(i for i, part in enumerate(parts) if 'workflow_assets' in part)
                                    if workflow_index + 1 < len(parts):
                                        main_dir = parts[workflow_index + 1]
                                        location_stats[main_dir] += 1
                                except:
                                    location_stats['unknown'] += 1
                        else:
                            missing_files += 1
                    
                    correlation_analysis[status] = {
                        'total_materials': len(materials),
                        'missing_files': missing_files,
                        'location_distribution': dict(location_stats)
                    }
            
            return correlation_analysis
            
        except Exception as e:
            print(f"❌ 分析状态文件位置关联失败: {e}")
            return {}
    
    def generate_movement_recommendations(self, analysis_results: Dict) -> List[str]:
        """生成文件移动优化建议"""
        recommendations = []
        
        # 基于分析结果生成建议
        if 'status_correlation' in analysis_results:
            correlation = analysis_results['status_correlation']
            
            for status, stats in correlation.items():
                missing_rate = (stats['missing_files'] / stats['total_materials'] * 100) if stats['total_materials'] > 0 else 0
                
                if missing_rate > 50:
                    recommendations.append({
                        'priority': 'HIGH',
                        'category': '文件同步',
                        'issue': f"状态 {status} 的素材有 {missing_rate:.1f}% 文件缺失",
                        'solution': f"检查状态 {status} 的文件移动逻辑，确保数据库路径同步更新"
                    })
                
                # 检查位置分布是否合理
                locations = stats['location_distribution']
                if len(locations) > 3:
                    recommendations.append({
                        'priority': 'MEDIUM',
                        'category': '目录整理',
                        'issue': f"状态 {status} 的文件分散在 {len(locations)} 个目录中",
                        'solution': "考虑整合文件存储位置，简化目录结构"
                    })
        
        # 通用建议
        recommendations.extend([
            {
                'priority': 'HIGH',
                'category': '同步机制',
                'issue': "文件移动后数据库路径未同步更新",
                'solution': "实施文件移动后的自动路径更新机制"
            },
            {
                'priority': 'MEDIUM',
                'category': '预防机制',
                'issue': "缺少文件存在性检查",
                'solution': "在任务执行前添加文件存在性验证"
            },
            {
                'priority': 'LOW',
                'category': '监控告警',
                'issue': "缺少文件路径不一致监控",
                'solution': "建立定期文件路径一致性检查机制"
            }
        ])
        
        return recommendations
    
    def run_comprehensive_analysis(self) -> Dict:
        """运行综合分析"""
        print("🚀 开始文件移动逻辑综合分析...")
        
        results = {}
        
        # 1. 目录结构分析
        results['directory_structure'] = self.analyze_directory_structure()
        
        # 2. 文件移动模式分析
        results['movement_patterns'] = self.analyze_file_movement_patterns()
        
        # 3. 文件移动逻辑识别
        results['movement_logic'] = self.identify_file_movement_logic()
        
        # 4. 状态文件位置关联分析
        results['status_correlation'] = self.analyze_status_file_location_correlation()
        
        # 5. 生成建议
        results['recommendations'] = self.generate_movement_recommendations(results)
        
        return results

def main():
    """主函数"""
    print("🔍 文件移动逻辑分析工具")
    print("=" * 60)
    
    analyzer = FileMovementAnalyzer()
    
    try:
        # 运行综合分析
        results = analyzer.run_comprehensive_analysis()
        
        # 输出分析结果
        print(f"\n{'='*60}")
        print("📊 文件移动逻辑分析结果")
        print(f"{'='*60}")
        
        # 目录结构
        if results['directory_structure']:
            print(f"\n📁 目录结构:")
            for dir_name, info in results['directory_structure'].items():
                print(f"  - {dir_name}: {info['file_count']} 个文件")
        
        # 状态关联分析
        if results['status_correlation']:
            print(f"\n📊 状态与文件位置关联:")
            for status, stats in results['status_correlation'].items():
                missing_rate = (stats['missing_files'] / stats['total_materials'] * 100) if stats['total_materials'] > 0 else 0
                print(f"  - {status}: {stats['total_materials']} 个素材, {missing_rate:.1f}% 文件缺失")
                
                # 显示主要位置分布
                locations = stats['location_distribution']
                for location, count in sorted(locations.items(), key=lambda x: x[1], reverse=True)[:3]:
                    print(f"    └─ {location}: {count} 个文件")
        
        # 建议
        if results['recommendations']:
            print(f"\n💡 优化建议:")
            for i, rec in enumerate(results['recommendations'][:5], 1):
                print(f"  {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
                print(f"     解决方案: {rec['solution']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
