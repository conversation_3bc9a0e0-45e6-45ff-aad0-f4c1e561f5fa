#!/usr/bin/env python3
"""
千川视频隔离机制快速诊断工具
专注于识别误隔离问题
"""

import sys
import os
import re
from pathlib import Path
from collections import Counter
import cv2

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

def quick_quarantine_diagnosis():
    """快速诊断隔离问题"""
    logger.info("🔍 快速诊断视频隔离问题")
    logger.info("=" * 50)
    
    quarantine_dir = Path("quarantine/invalid_videos")
    if not quarantine_dir.exists():
        logger.error("隔离目录不存在")
        return
    
    video_files = list(quarantine_dir.glob("*.mp4"))
    logger.info(f"发现 {len(video_files)} 个隔离视频文件")
    
    # 分析隔离原因
    reasons = []
    authors = []
    misquarantined_count = 0
    
    for video_file in video_files:
        # 提取作者
        author = extract_author_from_filename(video_file.name)
        authors.append(author)
        
        # 读取隔离原因
        reason_file = video_file.with_suffix(video_file.suffix + ".reason.txt")
        reason = "Unknown"
        if reason_file.exists():
            try:
                with open(reason_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if '隔离原因:' in content:
                        reason = content.split('隔离原因:')[1].split('\n')[0].strip()
            except Exception:
                pass
        
        reasons.append(reason)
        
        # 简单检测是否误隔离
        if is_likely_misquarantined(video_file, reason):
            misquarantined_count += 1
            logger.info(f"  疑似误隔离: {video_file.name} - {reason}")
    
    # 统计分析
    logger.info(f"\n📊 隔离统计分析:")
    
    # 隔离原因分布
    reason_stats = Counter(reasons)
    logger.info(f"  隔离原因分布:")
    for reason, count in reason_stats.most_common():
        logger.info(f"    {reason}: {count}次")
    
    # 作者分布
    author_stats = Counter(authors)
    logger.info(f"\n  按作者统计:")
    for author, count in author_stats.most_common():
        logger.info(f"    {author}: {count}个文件")
    
    # 误隔离率
    total_count = len(video_files)
    misquarantine_rate = (misquarantined_count / total_count * 100) if total_count > 0 else 0
    
    logger.info(f"\n  误隔离分析:")
    logger.info(f"    总隔离文件: {total_count}")
    logger.info(f"    疑似误隔离: {misquarantined_count}")
    logger.info(f"    误隔离率: {misquarantine_rate:.1f}%")
    
    # 生成诊断结论
    generate_diagnosis_conclusion(misquarantine_rate, reason_stats, author_stats)

def extract_author_from_filename(filename):
    """从文件名提取作者信息"""
    patterns = [
        r'^\d+\.\d+-([^-]+)-',  # 格式: 7.19-作者名-
        r'-([^-]+)-\d+\.mp4$',  # 格式: -作者名-数字.mp4
        r'(\w+)-\d+\.mp4$',     # 格式: 作者名-数字.mp4
    ]
    
    for pattern in patterns:
        match = re.search(pattern, filename)
        if match:
            return match.group(1)
    
    return 'Unknown'

def is_likely_misquarantined(video_file, reason):
    """简单判断是否可能误隔离"""
    # 文件大小检查 - 如果文件很大但被隔离，可能是误隔离
    file_size_mb = video_file.stat().st_size / (1024 * 1024)
    
    if file_size_mb > 5.0:  # 大于5MB的文件很可能不是问题视频
        return True
    
    # 如果隔离原因是"无法获取视频时长"，尝试用OpenCV检测
    if '无法获取视频时长' in reason:
        try:
            cap = cv2.VideoCapture(str(video_file))
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                if fps > 0 and frame_count > 0:
                    duration = frame_count / fps
                    cap.release()
                    if duration >= 4.0:  # 如果实际时长>=4秒，是误隔离
                        return True
            cap.release()
        except Exception:
            pass
    
    return False

def generate_diagnosis_conclusion(misquarantine_rate, reason_stats, author_stats):
    """生成诊断结论"""
    logger.info(f"\n🎯 诊断结论:")
    
    if misquarantine_rate > 50:
        logger.error(f"  🔴 严重问题: 误隔离率高达 {misquarantine_rate:.1f}%")
        logger.error(f"      视频验证算法存在重大缺陷，需要立即修复")
    elif misquarantine_rate > 10:
        logger.warning(f"  🟡 中等问题: 误隔离率为 {misquarantine_rate:.1f}%")
        logger.warning(f"      需要优化视频验证算法")
    else:
        logger.info(f"  ✅ 良好: 误隔离率仅为 {misquarantine_rate:.1f}%")
    
    # 分析主要问题
    main_reason = reason_stats.most_common(1)[0] if reason_stats else ("Unknown", 0)
    logger.info(f"\n  主要隔离原因: {main_reason[0]} ({main_reason[1]}次)")
    
    if '无法获取视频时长' in main_reason[0]:
        logger.info(f"  🔧 建议修复: 改进视频时长检测算法")
    elif '视频过短' in main_reason[0]:
        logger.info(f"  🔧 建议修复: 优化时长阈值设置")
    elif main_reason[0] == 'Unknown':
        logger.warning(f"  ⚠️ 问题: 大量文件缺少隔离原因记录")
    
    # 分析作者模式
    if author_stats:
        top_author = author_stats.most_common(1)[0]
        if top_author[1] > len(author_stats) * 0.3:  # 如果某个作者占比超过30%
            logger.info(f"  📊 发现: {top_author[0]} 的视频被隔离较多 ({top_author[1]}个)")
            logger.info(f"      可能该作者的视频格式存在特殊问题")

def generate_fix_recommendations():
    """生成修复建议"""
    logger.info(f"\n🛠️ 修复建议:")
    logger.info(f"  1. 立即实施多重验证机制")
    logger.info(f"  2. 改进视频时长检测算法")
    logger.info(f"  3. 添加隔离前二次确认")
    logger.info(f"  4. 建立视频格式白名单")
    logger.info(f"  5. 实现隔离决策日志记录")
    
    logger.info(f"\n📋 下一步行动:")
    logger.info(f"  - 运行任务2: 视频验证算法优化")
    logger.info(f"  - 实施改进的视频验证逻辑")
    logger.info(f"  - 建立误隔离监控机制")

def main():
    """主函数"""
    try:
        quick_quarantine_diagnosis()
        generate_fix_recommendations()
        
        logger.info(f"\n✅ 隔离机制诊断完成")
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中出现错误: {e}")
        raise

if __name__ == "__main__":
    main()
