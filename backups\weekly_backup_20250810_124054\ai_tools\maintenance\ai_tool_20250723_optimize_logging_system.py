#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 优化日志系统，解决日志洪水和编码问题
清理条件: 日志系统优化完成后可归档
"""

import os
import sys
import gzip
import shutil
from datetime import datetime, timedelta
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from qianchuan_aw.utils.logger import logger

class LoggingSystemOptimizer:
    """日志系统优化器"""
    
    def __init__(self):
        self.logs_dir = os.path.join(project_root, 'logs')
        self.stats = {
            'large_logs_found': 0,
            'logs_compressed': 0,
            'logs_cleaned': 0,
            'space_saved_mb': 0
        }
    
    def analyze_log_situation(self):
        """分析当前日志情况"""
        logger.info("📊 分析当前日志情况...")
        
        if not os.path.exists(self.logs_dir):
            logger.warning(f"日志目录不存在: {self.logs_dir}")
            return
        
        total_size = 0
        large_logs = []
        log_files = []
        
        for file in os.listdir(self.logs_dir):
            file_path = os.path.join(self.logs_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                total_size += size
                
                if file.endswith('.log'):
                    log_files.append((file, size))
                    
                    # 大于50MB的日志文件
                    if size > 50 * 1024 * 1024:
                        large_logs.append((file, size))
                        self.stats['large_logs_found'] += 1
        
        logger.info(f"日志目录统计:")
        logger.info(f"  总大小: {total_size / (1024*1024*1024):.2f}GB")
        logger.info(f"  日志文件数: {len(log_files)}")
        logger.info(f"  大文件数(>50MB): {len(large_logs)}")
        
        if large_logs:
            logger.warning("🚨 发现大日志文件:")
            for file, size in large_logs:
                logger.warning(f"  {file}: {size / (1024*1024):.1f}MB")
        
        return large_logs, log_files
    
    def analyze_log_patterns(self, log_file_path: str) -> Dict[str, int]:
        """分析日志模式，找出重复错误"""
        logger.info(f"🔍 分析日志模式: {os.path.basename(log_file_path)}")
        
        error_patterns = {}
        
        try:
            # 只读取最后1000行以避免内存问题
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            # 分析最后1000行
            recent_lines = lines[-1000:] if len(lines) > 1000 else lines
            
            for line in recent_lines:
                line = line.strip()
                if 'ERROR' in line or 'WARNING' in line:
                    # 提取错误模式（去除时间戳和具体文件名）
                    pattern = self._extract_error_pattern(line)
                    if pattern:
                        error_patterns[pattern] = error_patterns.get(pattern, 0) + 1
            
            # 按频率排序
            sorted_patterns = sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)
            
            logger.info(f"  发现错误模式 {len(sorted_patterns)} 种")
            if sorted_patterns:
                logger.info("  最频繁的错误:")
                for pattern, count in sorted_patterns[:5]:
                    logger.info(f"    {count}次: {pattern[:100]}...")
            
        except Exception as e:
            logger.error(f"分析日志模式失败: {e}")
        
        return error_patterns
    
    def _extract_error_pattern(self, log_line: str) -> str:
        """提取错误模式"""
        try:
            # 移除时间戳
            if '|' in log_line:
                parts = log_line.split('|', 2)
                if len(parts) >= 3:
                    content = parts[2].strip()
                else:
                    content = log_line
            else:
                content = log_line
            
            # 移除具体的文件名和数字
            import re
            # 移除文件路径
            content = re.sub(r'[A-Za-z]:[\\\/][^\\\/\s]+', '[FILE_PATH]', content)
            # 移除数字ID
            content = re.sub(r'\b\d{6,}\b', '[ID]', content)
            # 移除时间戳
            content = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '[TIMESTAMP]', content)
            
            return content
            
        except Exception:
            return log_line
    
    def compress_old_logs(self, log_files: List[tuple]):
        """压缩旧日志文件"""
        logger.info("🗜️ 压缩旧日志文件...")
        
        cutoff_date = datetime.now() - timedelta(days=3)
        
        for file_name, size in log_files:
            file_path = os.path.join(self.logs_dir, file_name)
            
            try:
                # 检查文件修改时间
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                if mod_time < cutoff_date and not file_name.endswith('.gz'):
                    # 压缩文件
                    compressed_path = file_path + '.gz'
                    
                    with open(file_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # 删除原文件
                    os.remove(file_path)
                    
                    compressed_size = os.path.getsize(compressed_path)
                    saved_space = size - compressed_size
                    
                    logger.info(f"  压缩: {file_name} -> {file_name}.gz (节省 {saved_space / (1024*1024):.1f}MB)")
                    
                    self.stats['logs_compressed'] += 1
                    self.stats['space_saved_mb'] += saved_space / (1024*1024)
                    
            except Exception as e:
                logger.error(f"压缩日志文件失败 {file_name}: {e}")
    
    def clean_old_logs(self):
        """清理过期日志"""
        logger.info("🧹 清理过期日志...")
        
        cutoff_date = datetime.now() - timedelta(days=30)
        
        for file in os.listdir(self.logs_dir):
            file_path = os.path.join(self.logs_dir, file)
            
            if os.path.isfile(file_path):
                try:
                    mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if mod_time < cutoff_date:
                        size = os.path.getsize(file_path)
                        os.remove(file_path)
                        
                        logger.info(f"  删除过期日志: {file} ({size / (1024*1024):.1f}MB)")
                        self.stats['logs_cleaned'] += 1
                        self.stats['space_saved_mb'] += size / (1024*1024)
                        
                except Exception as e:
                    logger.error(f"删除过期日志失败 {file}: {e}")
    
    def create_optimized_logging_config(self):
        """创建优化的日志配置"""
        logger.info("⚙️ 创建优化的日志配置...")
        
        optimized_config = '''
# 优化的日志配置
import os
from loguru import logger
from datetime import datetime

# 移除默认处理器
logger.remove()

# 添加控制台处理器（只显示重要信息）
logger.add(
    sink=lambda msg: print(msg, end=""),
    level="INFO",
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    filter=lambda record: record["level"].name in ["INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"]
)

# 添加文件处理器（带轮转和压缩）
logger.add(
    sink="logs/app_{time:YYYY-MM-DD}.log",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="100 MB",      # 文件大小轮转
    retention="7 days",     # 保留7天
    compression="gz",       # 压缩旧文件
    encoding="utf-8",
    enqueue=True,          # 异步写入
    backtrace=True,
    diagnose=True
)

# 添加错误文件处理器
logger.add(
    sink="logs/error_{time:YYYY-MM-DD}.log",
    level="ERROR",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
    rotation="50 MB",
    retention="14 days",
    compression="gz",
    encoding="utf-8",
    enqueue=True
)

# 错误聚合器
class ErrorAggregator:
    def __init__(self):
        self.error_counts = {}
        self.last_report = datetime.now()
    
    def log_error(self, error_pattern: str, full_message: str):
        """聚合相同错误，避免日志洪水"""
        if error_pattern not in self.error_counts:
            self.error_counts[error_pattern] = {
                'count': 0,
                'first_seen': datetime.now(),
                'last_seen': datetime.now(),
                'sample_message': full_message
            }
        
        self.error_counts[error_pattern]['count'] += 1
        self.error_counts[error_pattern]['last_seen'] = datetime.now()
        
        # 每10分钟报告一次聚合结果
        if (datetime.now() - self.last_report).seconds > 600:
            self.report_aggregated_errors()
            self.last_report = datetime.now()
    
    def report_aggregated_errors(self):
        """报告聚合的错误"""
        if not self.error_counts:
            return
        
        logger.warning("📊 错误聚合报告:")
        for pattern, info in sorted(self.error_counts.items(), key=lambda x: x[1]['count'], reverse=True):
            if info['count'] > 1:
                logger.warning(f"  {info['count']}次: {pattern[:100]}...")
        
        # 清空计数器
        self.error_counts.clear()

# 全局错误聚合器
error_aggregator = ErrorAggregator()
'''
        
        config_path = os.path.join(project_root, 'ai_tools', 'maintenance', 'optimized_logging_config.py')
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(optimized_config)
        
        logger.success(f"✅ 优化日志配置已创建: {config_path}")
    
    def create_log_monitoring_script(self):
        """创建日志监控脚本"""
        logger.info("📊 创建日志监控脚本...")
        
        monitoring_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""日志监控脚本"""

import os
import time
from datetime import datetime, timedelta
from collections import defaultdict

def monitor_log_health():
    """监控日志健康状态"""
    logs_dir = "logs"
    
    if not os.path.exists(logs_dir):
        print("❌ 日志目录不存在")
        return
    
    # 统计日志文件
    total_size = 0
    log_files = []
    
    for file in os.listdir(logs_dir):
        if file.endswith('.log'):
            file_path = os.path.join(logs_dir, file)
            size = os.path.getsize(file_path)
            total_size += size
            log_files.append((file, size))
    
    print(f"📊 日志健康检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"  日志文件数: {len(log_files)}")
    print(f"  总大小: {total_size / (1024*1024):.1f}MB")
    
    # 检查大文件
    large_files = [(f, s) for f, s in log_files if s > 100 * 1024 * 1024]
    if large_files:
        print(f"⚠️  发现大日志文件:")
        for file, size in large_files:
            print(f"    {file}: {size / (1024*1024):.1f}MB")
    
    # 检查今天的错误数量
    today_log = f"app_{datetime.now().strftime('%Y-%m-%d')}.log"
    today_log_path = os.path.join(logs_dir, today_log)
    
    if os.path.exists(today_log_path):
        error_count = 0
        warning_count = 0
        
        try:
            with open(today_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    if 'ERROR' in line:
                        error_count += 1
                    elif 'WARNING' in line:
                        warning_count += 1
            
            print(f"  今日错误数: {error_count}")
            print(f"  今日警告数: {warning_count}")
            
            if error_count > 100:
                print("🚨 今日错误数过多，建议检查系统状态")
            
        except Exception as e:
            print(f"❌ 读取今日日志失败: {e}")

if __name__ == "__main__":
    monitor_log_health()
'''
        
        script_path = os.path.join(project_root, 'ai_tools', 'maintenance', 'log_health_monitor.py')
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.success(f"✅ 日志监控脚本已创建: {script_path}")
    
    def run_complete_optimization(self):
        """运行完整的日志系统优化"""
        logger.info("🚀 开始日志系统优化...")
        logger.info("=" * 60)
        
        try:
            # 1. 分析当前日志情况
            large_logs, log_files = self.analyze_log_situation()
            
            # 2. 分析主要日志的错误模式
            if large_logs:
                main_log = os.path.join(self.logs_dir, large_logs[0][0])
                self.analyze_log_patterns(main_log)
            
            # 3. 压缩旧日志
            self.compress_old_logs(log_files)
            
            # 4. 清理过期日志
            self.clean_old_logs()
            
            # 5. 创建优化配置
            self.create_optimized_logging_config()
            
            # 6. 创建监控脚本
            self.create_log_monitoring_script()
            
            # 7. 输出统计信息
            logger.info("\n📊 优化统计:")
            logger.info(f"  大日志文件: {self.stats['large_logs_found']}")
            logger.info(f"  压缩日志: {self.stats['logs_compressed']}")
            logger.info(f"  清理日志: {self.stats['logs_cleaned']}")
            logger.info(f"  节省空间: {self.stats['space_saved_mb']:.1f}MB")
            
            logger.success("✅ 日志系统优化完成！")
            logger.info("\n💡 建议:")
            logger.info("  1. 应用新的日志配置以减少日志洪水")
            logger.info("  2. 定期运行日志监控脚本")
            logger.info("  3. 考虑实施错误聚合机制")
            
        except Exception as e:
            logger.error(f"优化过程中发生错误: {e}", exc_info=True)

def main():
    """主函数"""
    optimizer = LoggingSystemOptimizer()
    optimizer.run_complete_optimization()

if __name__ == "__main__":
    main()
