#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 清理千川自动化项目弹药库中的重复视频文件
清理条件: 弹药库清理完成后可归档，建议保留作为维护工具
"""

import os
import sys
import hashlib
import shutil
from datetime import datetime
from typing import Dict, List, Tuple, Set
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import load_settings

class AmmunitionLibraryDeduplicator:
    """弹药库重复文件清理器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_workflow_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        self.principal_name = "缇萃百货"
        
        # 定义源目录和目标目录
        self.source_date = "2025-07-22"
        self.target_date = "2025-07-23"
        
        self.source_dir = os.path.join(
            self.base_workflow_dir, 
            '03_materials_approved', 
            self.principal_name, 
            self.source_date
        )
        
        self.target_dir = os.path.join(
            self.base_workflow_dir, 
            '03_materials_approved', 
            self.principal_name, 
            self.target_date
        )
        
        # 隔离目录
        self.quarantine_dir = os.path.join(
            self.base_workflow_dir, 
            'quarantine', 
            'duplicate_cleanup', 
            f'{self.target_date}_duplicates'
        )
        
        # 统计信息
        self.stats = {
            'source_files_count': 0,
            'target_files_count': 0,
            'duplicates_found': 0,
            'duplicates_removed': 0,
            'unique_files_remaining': 0,
            'quarantined_files': []
        }
        
        # 支持的视频格式
        self.video_extensions = {'.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm'}
    
    def print_cleanup_banner(self):
        """打印清理横幅"""
        banner = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    千川自动化项目 - 弹药库重复文件清理工具                   ║
║                                                                              ║
║  🎯 清理目标:                                                                ║
║     源目录 (保留): {self.source_date}                                        ║
║     目标目录 (清理): {self.target_date}                                      ║
║                                                                              ║
║  🔧 清理规则:                                                                ║
║     • 基于文件哈希值进行精确重复检测                                         ║
║     • 删除目标目录中与源目录重复的文件                                       ║
║     • 重复文件移动到隔离目录，不直接删除                                     ║
║     • 生成详细的清理报告和统计信息                                           ║
║                                                                              ║
║  📁 目录路径:                                                                ║
║     源: {self.source_dir}
║     标: {self.target_dir}
║     离: {self.quarantine_dir}
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        print(banner)
        logger.info("🚀 开始弹药库重复文件清理...")
    
    def calculate_file_hash(self, file_path: str) -> str:
        """计算文件的MD5哈希值"""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                # 分块读取以处理大文件
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_path}: {e}")
            return None
    
    def scan_directory_files(self, directory: str) -> Dict[str, Dict]:
        """扫描目录中的视频文件并计算哈希值"""
        logger.info(f"📁 扫描目录: {directory}")
        
        if not os.path.exists(directory):
            logger.warning(f"目录不存在: {directory}")
            return {}
        
        files_info = {}
        video_files = []
        
        # 收集所有视频文件
        for file in os.listdir(directory):
            file_path = os.path.join(directory, file)
            if os.path.isfile(file_path):
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext in self.video_extensions:
                    video_files.append((file, file_path))
        
        logger.info(f"  发现 {len(video_files)} 个视频文件")
        
        # 计算每个文件的哈希值
        for i, (filename, file_path) in enumerate(video_files, 1):
            logger.info(f"  计算哈希 ({i}/{len(video_files)}): {filename}")
            
            file_hash = self.calculate_file_hash(file_path)
            if file_hash:
                file_size = os.path.getsize(file_path)
                files_info[filename] = {
                    'path': file_path,
                    'hash': file_hash,
                    'size': file_size,
                    'size_mb': round(file_size / (1024 * 1024), 2)
                }
            else:
                logger.warning(f"  跳过文件 (哈希计算失败): {filename}")
        
        logger.success(f"  完成扫描，成功处理 {len(files_info)} 个文件")
        return files_info
    
    def find_duplicates(self, source_files: Dict, target_files: Dict) -> List[Dict]:
        """查找重复文件"""
        logger.info("🔍 查找重复文件...")
        
        # 创建源文件的哈希值集合
        source_hashes = {info['hash'] for info in source_files.values()}
        
        duplicates = []
        
        for filename, file_info in target_files.items():
            file_hash = file_info['hash']
            
            if file_hash in source_hashes:
                # 找到重复文件，查找源文件名
                source_filename = None
                for src_name, src_info in source_files.items():
                    if src_info['hash'] == file_hash:
                        source_filename = src_name
                        break
                
                duplicate_info = {
                    'target_filename': filename,
                    'target_path': file_info['path'],
                    'source_filename': source_filename,
                    'hash': file_hash,
                    'size_mb': file_info['size_mb'],
                    'same_name': filename == source_filename
                }
                
                duplicates.append(duplicate_info)
        
        self.stats['duplicates_found'] = len(duplicates)
        
        logger.info(f"发现 {len(duplicates)} 个重复文件:")
        for dup in duplicates:
            same_name_indicator = "✓" if dup['same_name'] else "✗"
            logger.info(f"  [{same_name_indicator}] {dup['target_filename']} ({dup['size_mb']}MB)")
            if not dup['same_name']:
                logger.info(f"      源文件名: {dup['source_filename']}")
        
        return duplicates
    
    def quarantine_duplicates(self, duplicates: List[Dict]) -> bool:
        """将重复文件移动到隔离目录"""
        if not duplicates:
            logger.info("✅ 没有重复文件需要处理")
            return True
        
        logger.info(f"🚫 开始隔离 {len(duplicates)} 个重复文件...")
        
        # 创建隔离目录
        os.makedirs(self.quarantine_dir, exist_ok=True)
        
        # 创建清理记录文件
        cleanup_log_path = os.path.join(self.quarantine_dir, 'cleanup_log.txt')
        
        quarantined_count = 0
        
        with open(cleanup_log_path, 'w', encoding='utf-8') as log_file:
            log_file.write(f"弹药库重复文件清理记录\n")
            log_file.write(f"{'='*50}\n")
            log_file.write(f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            log_file.write(f"源目录: {self.source_dir}\n")
            log_file.write(f"目标目录: {self.target_dir}\n")
            log_file.write(f"隔离目录: {self.quarantine_dir}\n")
            log_file.write(f"重复文件数: {len(duplicates)}\n\n")
            
            for i, dup in enumerate(duplicates, 1):
                try:
                    source_path = dup['target_path']
                    target_filename = dup['target_filename']
                    
                    # 生成隔离文件名（避免冲突）
                    quarantine_filename = f"{i:03d}_{target_filename}"
                    quarantine_path = os.path.join(self.quarantine_dir, quarantine_filename)
                    
                    # 移动文件到隔离目录
                    shutil.move(source_path, quarantine_path)
                    
                    # 记录到日志
                    log_entry = f"[{i:03d}] {target_filename}\n"
                    log_entry += f"      原路径: {source_path}\n"
                    log_entry += f"      隔离路径: {quarantine_path}\n"
                    log_entry += f"      文件大小: {dup['size_mb']}MB\n"
                    log_entry += f"      文件哈希: {dup['hash']}\n"
                    log_entry += f"      源文件名: {dup['source_filename']}\n"
                    log_entry += f"      同名文件: {'是' if dup['same_name'] else '否'}\n\n"
                    
                    log_file.write(log_entry)
                    
                    logger.info(f"  [{i:03d}] 隔离: {target_filename}")
                    
                    self.stats['quarantined_files'].append({
                        'original_name': target_filename,
                        'quarantine_name': quarantine_filename,
                        'size_mb': dup['size_mb']
                    })
                    
                    quarantined_count += 1
                    
                except Exception as e:
                    logger.error(f"隔离文件失败 {dup['target_filename']}: {e}")
                    log_file.write(f"[ERROR] 隔离失败: {dup['target_filename']} - {e}\n")
        
        self.stats['duplicates_removed'] = quarantined_count
        
        logger.success(f"✅ 成功隔离 {quarantined_count} 个重复文件")
        logger.info(f"📄 清理记录已保存到: {cleanup_log_path}")
        
        return quarantined_count == len(duplicates)
    
    def generate_cleanup_report(self, source_files: Dict, target_files: Dict, duplicates: List[Dict]):
        """生成清理报告"""
        logger.info("📊 生成清理报告...")
        
        # 计算清理后的统计
        self.stats['source_files_count'] = len(source_files)
        self.stats['target_files_count'] = len(target_files)
        self.stats['unique_files_remaining'] = len(target_files) - self.stats['duplicates_removed']
        
        # 计算节省的空间
        total_space_saved = sum(dup['size_mb'] for dup in self.stats['quarantined_files'])
        
        report = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                        弹药库重复文件清理报告                                ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  📅 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}                                    ║
║  🎯 清理对象: {self.principal_name} ({self.source_date} vs {self.target_date})                    ║
║                                                                              ║
║  📊 清理前统计:                                                              ║
║     • {self.source_date}目录文件数: {self.stats['source_files_count']}                                        ║
║     • {self.target_date}目录文件数: {self.stats['target_files_count']}                                        ║
║                                                                              ║
║  🔍 重复检测结果:                                                            ║
║     • 发现重复文件: {self.stats['duplicates_found']}个                                          ║
║     • 成功隔离文件: {self.stats['duplicates_removed']}个                                          ║
║     • 节省存储空间: {total_space_saved:.1f}MB                                        ║
║                                                                              ║
║  📈 清理后统计:                                                              ║
║     • {self.target_date}目录剩余文件: {self.stats['unique_files_remaining']}个                                ║
║     • 重复率: {(self.stats['duplicates_found']/self.stats['target_files_count']*100):.1f}%                                                ║
║     • 清理成功率: {(self.stats['duplicates_removed']/self.stats['duplicates_found']*100) if self.stats['duplicates_found'] > 0 else 100:.1f}%                                          ║
║                                                                              ║
║  📁 隔离目录: {self.quarantine_dir}
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        print(report)
        
        # 保存详细报告到文件
        report_path = os.path.join(
            project_root, 
            'ai_reports', 
            'ammunition_cleanup', 
            f'cleanup_report_{self.target_date}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
        )
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        detailed_report = f"""千川自动化项目弹药库重复文件清理详细报告
{'='*60}

清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
清理对象: {self.principal_name}
源目录: {self.source_dir}
目标目录: {self.target_dir}
隔离目录: {self.quarantine_dir}

统计信息:
- 源目录文件数: {self.stats['source_files_count']}
- 目标目录原文件数: {self.stats['target_files_count']}
- 发现重复文件: {self.stats['duplicates_found']}
- 成功隔离文件: {self.stats['duplicates_removed']}
- 剩余唯一文件: {self.stats['unique_files_remaining']}
- 节省存储空间: {total_space_saved:.1f}MB

隔离文件列表:
{'-'*30}
"""
        
        for i, file_info in enumerate(self.stats['quarantined_files'], 1):
            detailed_report += f"{i:3d}. {file_info['original_name']} ({file_info['size_mb']}MB)\n"
            detailed_report += f"     -> {file_info['quarantine_name']}\n"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(detailed_report)
        
        logger.info(f"📄 详细报告已保存到: {report_path}")
    
    def run_cleanup(self):
        """运行完整的清理流程"""
        self.print_cleanup_banner()
        
        try:
            # 1. 检查目录存在性
            if not os.path.exists(self.source_dir):
                logger.error(f"源目录不存在: {self.source_dir}")
                return False
            
            if not os.path.exists(self.target_dir):
                logger.error(f"目标目录不存在: {self.target_dir}")
                return False
            
            # 2. 扫描源目录文件
            logger.info(f"\n🔍 第一步: 扫描源目录 ({self.source_date})")
            source_files = self.scan_directory_files(self.source_dir)
            
            if not source_files:
                logger.warning("源目录中没有视频文件")
                return False
            
            # 3. 扫描目标目录文件
            logger.info(f"\n🔍 第二步: 扫描目标目录 ({self.target_date})")
            target_files = self.scan_directory_files(self.target_dir)
            
            if not target_files:
                logger.warning("目标目录中没有视频文件")
                return False
            
            # 4. 查找重复文件
            logger.info(f"\n🔍 第三步: 查找重复文件")
            duplicates = self.find_duplicates(source_files, target_files)
            
            # 5. 隔离重复文件
            logger.info(f"\n🚫 第四步: 隔离重复文件")
            success = self.quarantine_duplicates(duplicates)
            
            # 6. 生成清理报告
            logger.info(f"\n📊 第五步: 生成清理报告")
            self.generate_cleanup_report(source_files, target_files, duplicates)
            
            if success:
                logger.success("🎉 弹药库重复文件清理完成！")
            else:
                logger.warning("⚠️ 清理过程中遇到部分问题，请查看详细日志")
            
            return success
            
        except Exception as e:
            logger.error(f"清理过程中发生错误: {e}", exc_info=True)
            return False

def main():
    """主函数"""
    deduplicator = AmmunitionLibraryDeduplicator()
    success = deduplicator.run_cleanup()
    
    if success:
        logger.success("✅ 弹药库重复文件清理成功完成！")
    else:
        logger.error("❌ 清理过程中遇到问题，请查看详细日志")
    
    return success

if __name__ == "__main__":
    main()
