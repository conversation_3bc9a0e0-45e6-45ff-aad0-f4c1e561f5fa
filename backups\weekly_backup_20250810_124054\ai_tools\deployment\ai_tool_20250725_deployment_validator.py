#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目部署验证工具，验证新环境部署的完整性和功能性
清理条件: 项目不再需要部署验证时可删除
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import platform
import socket

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class QianchuanDeploymentValidator:
    """千川自动化项目部署验证器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.validation_results = {}
        self.validation_errors = []
        
    def validate_python_environment(self) -> Dict[str, Any]:
        """验证Python环境"""
        print("🐍 验证Python环境...")
        
        result = {
            "python_version": sys.version,
            "python_executable": sys.executable,
            "platform": platform.platform(),
            "architecture": platform.architecture(),
            "packages": {},
            "missing_packages": [],
            "status": "unknown"
        }
        
        # 检查Python版本
        version_info = sys.version_info
        if version_info.major == 3 and version_info.minor >= 9:
            result["python_version_ok"] = True
        else:
            result["python_version_ok"] = False
            self.validation_errors.append(f"Python版本过低: {sys.version}, 需要3.9+")
        
        # 检查关键包
        required_packages = [
            'streamlit', 'celery', 'sqlalchemy', 'playwright', 
            'requests', 'yaml', 'redis', 'psycopg2'
        ]
        
        for package in required_packages:
            try:
                __import__(package)
                result["packages"][package] = "已安装"
            except ImportError:
                result["packages"][package] = "未安装"
                result["missing_packages"].append(package)
        
        # 检查项目模块
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            result["project_modules"] = "可导入"
        except ImportError as e:
            result["project_modules"] = f"导入失败: {e}"
            self.validation_errors.append(f"项目模块导入失败: {e}")
        
        result["status"] = "通过" if not result["missing_packages"] and result["python_version_ok"] else "失败"
        return result
    
    def validate_system_services(self) -> Dict[str, Any]:
        """验证系统服务"""
        print("🔧 验证系统服务...")
        
        result = {
            "redis": {"status": "unknown", "details": ""},
            "postgresql": {"status": "unknown", "details": ""},
            "browser": {"status": "unknown", "details": ""},
            "network": {"status": "unknown", "details": ""}
        }
        
        # 检查Redis
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
            r.ping()
            result["redis"]["status"] = "运行中"
            result["redis"]["details"] = "Redis连接成功"
        except Exception as e:
            result["redis"]["status"] = "失败"
            result["redis"]["details"] = f"Redis连接失败: {e}"
            self.validation_errors.append(f"Redis服务不可用: {e}")
        
        # 检查PostgreSQL (可选)
        try:
            import psycopg2
            # 尝试连接默认配置
            conn = psycopg2.connect(
                host='localhost', port=5432, 
                database='postgres', user='postgres', password='',
                connect_timeout=5
            )
            conn.close()
            result["postgresql"]["status"] = "可用"
            result["postgresql"]["details"] = "PostgreSQL连接成功"
        except Exception as e:
            result["postgresql"]["status"] = "不可用"
            result["postgresql"]["details"] = f"PostgreSQL连接失败: {e} (可选服务)"
        
        # 检查浏览器
        try:
            from playwright.sync_api import sync_playwright
            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                browser.close()
            result["browser"]["status"] = "可用"
            result["browser"]["details"] = "Playwright浏览器启动成功"
        except Exception as e:
            result["browser"]["status"] = "失败"
            result["browser"]["details"] = f"浏览器启动失败: {e}"
            self.validation_errors.append(f"浏览器自动化不可用: {e}")
        
        # 检查网络连接
        try:
            # 测试DNS解析和网络连接
            socket.create_connection(("8.8.8.8", 53), timeout=5)
            result["network"]["status"] = "正常"
            result["network"]["details"] = "网络连接正常"
        except Exception as e:
            result["network"]["status"] = "异常"
            result["network"]["details"] = f"网络连接异常: {e}"
            self.validation_errors.append(f"网络连接问题: {e}")
        
        return result
    
    def validate_project_structure(self) -> Dict[str, Any]:
        """验证项目结构"""
        print("📁 验证项目结构...")
        
        result = {
            "core_files": {},
            "config_files": {},
            "directories": {},
            "permissions": {},
            "missing_items": []
        }
        
        # 检查核心文件
        core_files = [
            "main.py", "web_ui.py", "requirements.txt",
            "run_celery_worker.py", "run_celery_beat.py"
        ]
        
        for file_name in core_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                result["core_files"][file_name] = {
                    "exists": True,
                    "size": file_path.stat().st_size,
                    "readable": os.access(file_path, os.R_OK)
                }
            else:
                result["core_files"][file_name] = {"exists": False}
                result["missing_items"].append(file_name)
        
        # 检查配置文件
        config_files = ["config/settings.yml", "config/banned_terms.yml"]
        for file_name in config_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                result["config_files"][file_name] = {
                    "exists": True,
                    "readable": os.access(file_path, os.R_OK),
                    "writable": os.access(file_path, os.W_OK)
                }
            else:
                result["config_files"][file_name] = {"exists": False}
                result["missing_items"].append(file_name)
        
        # 检查目录
        required_dirs = ["src", "config", "logs", "workflow_assets"]
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                result["directories"][dir_name] = {
                    "exists": True,
                    "writable": os.access(dir_path, os.W_OK)
                }
            else:
                result["directories"][dir_name] = {"exists": False}
                result["missing_items"].append(dir_name)
        
        return result
    
    def validate_configuration(self) -> Dict[str, Any]:
        """验证配置文件"""
        print("⚙️ 验证配置文件...")
        
        result = {
            "settings_yml": {"status": "unknown", "details": {}},
            "database_config": {"status": "unknown", "details": {}},
            "api_config": {"status": "unknown", "details": {}},
            "paths_config": {"status": "unknown", "details": {}}
        }
        
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            
            result["settings_yml"]["status"] = "加载成功"
            
            # 验证数据库配置
            db_config = settings.get('database', {})
            db_type = db_config.get('type', 'sqlite')
            
            if db_type == 'sqlite':
                sqlite_config = db_config.get('sqlite', {})
                db_name = sqlite_config.get('db_name', 'qianchuan_aw.db')
                result["database_config"]["details"] = {
                    "type": "sqlite",
                    "db_name": db_name,
                    "configured": True
                }
            elif db_type == 'postgresql':
                pg_config = db_config.get('postgresql', {})
                required_fields = ['host', 'port', 'dbname', 'user', 'password']
                missing_fields = [f for f in required_fields if not pg_config.get(f)]
                
                result["database_config"]["details"] = {
                    "type": "postgresql",
                    "configured": len(missing_fields) == 0,
                    "missing_fields": missing_fields
                }
            
            result["database_config"]["status"] = "配置正确" if result["database_config"]["details"].get("configured") else "配置不完整"
            
            # 验证API配置
            api_config = settings.get('api_credentials', {})
            has_app_id = bool(api_config.get('app_id'))
            has_secret = bool(api_config.get('secret'))
            
            result["api_config"]["details"] = {
                "has_app_id": has_app_id,
                "has_secret": has_secret,
                "configured": has_app_id and has_secret
            }
            result["api_config"]["status"] = "配置正确" if has_app_id and has_secret else "配置不完整"
            
            # 验证路径配置
            workflow_dir = settings.get('custom_workflow_assets_dir', '')
            if workflow_dir:
                workflow_path = Path(workflow_dir)
                path_exists = workflow_path.exists()
                path_writable = path_exists and os.access(workflow_path, os.W_OK)
                
                result["paths_config"]["details"] = {
                    "workflow_dir": workflow_dir,
                    "exists": path_exists,
                    "writable": path_writable
                }
                result["paths_config"]["status"] = "路径正确" if path_exists and path_writable else "路径问题"
            else:
                result["paths_config"]["status"] = "使用默认路径"
                result["paths_config"]["details"] = {"workflow_dir": "默认: workflow_assets"}
            
        except Exception as e:
            result["settings_yml"]["status"] = f"加载失败: {e}"
            self.validation_errors.append(f"配置文件验证失败: {e}")
        
        return result
    
    def validate_database_connection(self) -> Dict[str, Any]:
        """验证数据库连接"""
        print("🗄️ 验证数据库连接...")
        
        result = {
            "connection": {"status": "unknown", "details": ""},
            "tables": {"status": "unknown", "count": 0, "details": []},
            "data": {"status": "unknown", "details": {}}
        }
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Base
            from sqlalchemy import text
            
            with database_session() as db:
                # 测试连接
                db.execute(text("SELECT 1"))
                result["connection"]["status"] = "连接成功"
                result["connection"]["details"] = "数据库连接正常"
                
                # 检查表结构
                from sqlalchemy import inspect
                inspector = inspect(db.get_bind())
                table_names = inspector.get_table_names()
                result["tables"]["count"] = len(table_names)
                result["tables"]["details"] = table_names
                result["tables"]["status"] = "表结构正常" if table_names else "无表结构"
                
                # 检查基础数据
                table_counts = {}
                for table_name in table_names:
                    try:
                        count_result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = count_result.scalar()
                        table_counts[table_name] = count
                    except Exception as e:
                        table_counts[table_name] = f"查询失败: {e}"
                
                result["data"]["details"] = table_counts
                result["data"]["status"] = "数据正常"
                
        except Exception as e:
            result["connection"]["status"] = f"连接失败: {e}"
            result["connection"]["details"] = str(e)
            self.validation_errors.append(f"数据库连接失败: {e}")
        
        return result
    
    def test_core_functionality(self) -> Dict[str, Any]:
        """测试核心功能"""
        print("🧪 测试核心功能...")
        
        result = {
            "config_loading": {"status": "unknown", "details": ""},
            "database_operations": {"status": "unknown", "details": ""},
            "api_client": {"status": "unknown", "details": ""},
            "celery_tasks": {"status": "unknown", "details": ""}
        }
        
        # 测试配置加载
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            result["config_loading"]["status"] = "正常"
            result["config_loading"]["details"] = f"配置项数量: {len(settings)}"
        except Exception as e:
            result["config_loading"]["status"] = f"失败: {e}"
            self.validation_errors.append(f"配置加载测试失败: {e}")
        
        # 测试数据库操作
        try:
            from qianchuan_aw.utils.db_utils import database_session
            with database_session() as db:
                db.execute(text("SELECT 1"))
            result["database_operations"]["status"] = "正常"
            result["database_operations"]["details"] = "基础数据库操作正常"
        except Exception as e:
            result["database_operations"]["status"] = f"失败: {e}"
            self.validation_errors.append(f"数据库操作测试失败: {e}")
        
        # 测试API客户端
        try:
            from qianchuan_aw.sdk_qc.client import QianchuanClient
            client = QianchuanClient()
            result["api_client"]["status"] = "正常"
            result["api_client"]["details"] = "API客户端初始化成功"
        except Exception as e:
            result["api_client"]["status"] = f"失败: {e}"
            self.validation_errors.append(f"API客户端测试失败: {e}")
        
        # 测试Celery任务
        try:
            from qianchuan_aw.celery_app import app
            result["celery_tasks"]["status"] = "正常"
            result["celery_tasks"]["details"] = f"Celery应用配置正常"
        except Exception as e:
            result["celery_tasks"]["status"] = f"失败: {e}"
            self.validation_errors.append(f"Celery任务测试失败: {e}")
        
        return result
    
    def generate_validation_report(self) -> Dict[str, Any]:
        """生成完整的验证报告"""
        print("📋 生成验证报告...")
        
        report = {
            "validation_time": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "python_environment": self.validate_python_environment(),
            "system_services": self.validate_system_services(),
            "project_structure": self.validate_project_structure(),
            "configuration": self.validate_configuration(),
            "database_connection": self.validate_database_connection(),
            "core_functionality": self.test_core_functionality(),
            "validation_errors": self.validation_errors,
            "overall_status": "unknown"
        }
        
        # 计算总体状态
        critical_failures = len([e for e in self.validation_errors if "失败" in e or "错误" in e])
        if critical_failures == 0:
            report["overall_status"] = "通过"
        elif critical_failures <= 2:
            report["overall_status"] = "部分通过"
        else:
            report["overall_status"] = "失败"
        
        return report
    
    def run_full_validation(self) -> bool:
        """运行完整验证"""
        print("🚀 千川自动化项目部署验证")
        print("=" * 60)
        
        try:
            # 生成验证报告
            report = self.generate_validation_report()
            
            # 保存报告
            report_file = self.project_root / "ai_reports" / "deployment" / f"validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ 验证报告已保存到: {report_file}")
            
            # 显示验证结果
            print(f"\n📊 验证结果摘要:")
            print(f"- 总体状态: {report['overall_status']}")
            print(f"- Python环境: {report['python_environment']['status']}")
            print(f"- 系统服务: Redis({report['system_services']['redis']['status']}), 浏览器({report['system_services']['browser']['status']})")
            print(f"- 项目结构: 缺失项目 {len(report['project_structure']['missing_items'])} 个")
            print(f"- 配置文件: {report['configuration']['settings_yml']['status']}")
            print(f"- 数据库: {report['database_connection']['connection']['status']}")
            print(f"- 验证错误: {len(report['validation_errors'])} 个")
            
            if report['validation_errors']:
                print("\n⚠️ 发现的问题:")
                for error in report['validation_errors']:
                    print(f"  - {error}")
            
            return report['overall_status'] in ['通过', '部分通过']
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False

def main():
    """主函数"""
    validator = QianchuanDeploymentValidator()
    success = validator.run_full_validation()
    
    if success:
        print("\n🎉 部署验证完成!")
        print("\n📝 下一步操作:")
        print("1. 解决发现的问题")
        print("2. 启动系统服务")
        print("3. 运行功能测试")
    else:
        print("\n❌ 部署验证失败，请解决问题后重新验证")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
