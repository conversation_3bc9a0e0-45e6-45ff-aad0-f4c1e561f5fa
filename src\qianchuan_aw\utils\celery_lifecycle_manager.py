#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Celery生命周期管理器
解决Beat停止后Worker仍运行的问题
"""


import signal
import sys
import time
import threading
from typing import Optional
from qianchuan_aw.utils.logger import logger


class CeleryLifecycleManager:
    """Celery生命周期管理器 - 支持配置控制"""

    def __init__(self):
        self.beat_running = True
        self.worker_should_stop = False
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.last_heartbeat = time.time()

        # 加载配置
        self.enabled = self._load_config()
    
    def _load_config(self):
        """加载生命周期管理配置"""
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            lifecycle_config = settings.get('celery_lifecycle', {})

            enabled = lifecycle_config.get('enabled', False)  # 默认禁用
            self.heartbeat_timeout = lifecycle_config.get('heartbeat_timeout', 3600)  # 1小时
            self.check_interval = lifecycle_config.get('check_interval', 600)  # 10分钟

            if enabled:
                logger.info(f"🔄 生命周期管理器已启用 (超时: {self.heartbeat_timeout}s, 检查间隔: {self.check_interval}s)")
            else:
                logger.info("🔄 生命周期管理器已禁用 (适合长期运行)")

            return enabled

        except Exception as e:
            logger.warning(f"加载生命周期管理配置失败: {e}，使用默认配置（禁用）")
            self.heartbeat_timeout = 3600
            self.check_interval = 600
            return False

    def start_heartbeat_monitor(self):
        """启动心跳监控 - 支持配置控制"""
        if not self.enabled:
            logger.info("🔄 生命周期管理器已禁用，跳过心跳监控")
            return

        self.heartbeat_thread = threading.Thread(target=self._heartbeat_monitor, daemon=True)
        self.heartbeat_thread.start()
        logger.info("🔄 启动Celery心跳监控")
    
    def _heartbeat_monitor(self):
        """心跳监控线程 - 使用配置的超时时间"""
        while not self.worker_should_stop:
            try:
                # 检查Beat是否还在运行 - 使用配置的超时时间
                current_time = time.time()
                if current_time - self.last_heartbeat > self.heartbeat_timeout:
                    logger.warning(f"⚠️ 检测到Beat可能已停止 (超过{self.heartbeat_timeout}秒无心跳)，准备关闭Worker")
                    self.initiate_graceful_shutdown()
                    break

                time.sleep(self.check_interval)  # 使用配置的检查间隔
                
            except Exception as e:
                logger.error(f"心跳监控异常: {e}")
    
    def update_heartbeat(self):
        """更新心跳时间 - 仅在启用时生效"""
        if self.enabled:
            self.last_heartbeat = time.time()
    
    def initiate_graceful_shutdown(self):
        """启动优雅关闭"""
        logger.info("🛑 启动Celery Worker优雅关闭...")
        
        self.worker_should_stop = True
        
        # 发送SIGTERM信号给当前进程
        try:
            import os
            os.kill(os.getpid(), signal.SIGTERM)
        except Exception as e:
            logger.error(f"发送关闭信号失败: {e}")
            sys.exit(0)
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            self.initiate_graceful_shutdown()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)


# 全局生命周期管理器
_lifecycle_manager = None

def get_lifecycle_manager() -> CeleryLifecycleManager:
    """获取生命周期管理器"""
    global _lifecycle_manager
    
    if _lifecycle_manager is None:
        _lifecycle_manager = CeleryLifecycleManager()
        _lifecycle_manager.setup_signal_handlers()
        _lifecycle_manager.start_heartbeat_monitor()
    
    return _lifecycle_manager
