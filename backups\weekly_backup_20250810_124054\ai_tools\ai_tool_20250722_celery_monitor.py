#!/usr/bin/env python3
"""
Celery工作流监控工具
实时监控任务执行状态和队列情况
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from qianchuan_aw.celery_app import app
import redis

def monitor_celery_status():
    """监控Celery状态"""
    logger.info("🔍 Celery工作流状态监控")
    logger.info("=" * 60)
    
    try:
        # 检查Celery连接
        inspect = app.control.inspect()
        
        # 检查活跃任务
        active_tasks = inspect.active()
        if active_tasks:
            logger.info("📋 活跃任务:")
            for worker, tasks in active_tasks.items():
                logger.info(f"  Worker {worker}: {len(tasks)} 个任务")
                for task in tasks[:3]:  # 只显示前3个
                    logger.info(f"    - {task['name']}: {task.get('id', 'N/A')}")
        else:
            logger.info("✅ 当前无活跃任务")
        
        # 检查队列状态
        reserved_tasks = inspect.reserved()
        if reserved_tasks:
            total_reserved = sum(len(tasks) for tasks in reserved_tasks.values())
            logger.info(f"📦 队列中等待任务: {total_reserved} 个")
        else:
            logger.info("✅ 队列为空")
        
        # 检查Worker统计
        stats = inspect.stats()
        if stats:
            logger.info("📊 Worker统计:")
            for worker, stat in stats.items():
                logger.info(f"  {worker}:")
                logger.info(f"    总任务: {stat.get('total', 0)}")
                logger.info(f"    连接池: {stat.get('pool', {}).get('max-concurrency', 'N/A')}")
        
        # 检查Redis连接
        try:
            redis_client = redis.Redis.from_url("redis://localhost:6379/0")
            redis_info = redis_client.info()
            logger.info(f"📡 Redis状态: 连接正常，内存使用 {redis_info.get('used_memory_human', 'N/A')}")
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")
        return False

def check_task_frequency():
    """检查任务执行频率"""
    logger.info("\n📈 任务执行频率分析")
    logger.info("=" * 60)
    
    # 分析今日日志中的任务执行模式
    log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
    
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return
    
    task_counts = {
        'create_plans': 0,
        'appeal_plans': 0,
        'ingest_and_upload': 0,
        'monitor_materials': 0
    }
    
    with open(log_file, 'r', encoding='utf-8') as f:
        for line in f:
            for task_name in task_counts.keys():
                if f'[Task Start] {task_name.replace("_", " ").title()}' in line:
                    task_counts[task_name] += 1
    
    logger.info("今日任务执行统计:")
    for task_name, count in task_counts.items():
        logger.info(f"  {task_name}: {count} 次")
    
    # 检查是否有异常高频执行
    if task_counts['create_plans'] > 100:
        logger.warning(f"⚠️ create_plans任务执行过于频繁: {task_counts['create_plans']} 次")
        logger.warning("  建议检查调度配置和任务逻辑")

def main():
    """主监控函数"""
    logger.info(f"🚀 开始Celery工作流监控 - {datetime.now()}")
    
    # 监控Celery状态
    monitor_celery_status()
    
    # 检查任务频率
    check_task_frequency()
    
    logger.info("\n💡 监控建议:")
    logger.info("  1. 定期检查活跃任务数量")
    logger.info("  2. 监控队列积压情况")
    logger.info("  3. 观察任务执行频率是否合理")
    logger.info("  4. 检查Redis内存使用情况")

if __name__ == "__main__":
    main()
