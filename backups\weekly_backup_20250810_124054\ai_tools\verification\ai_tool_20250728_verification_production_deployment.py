#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证生产环境部署是否成功
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def verify_production_service():
    """验证生产环境服务"""
    logger.info("🔍 验证生产环境优化服务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
        
        # 加载配置
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 创建服务实例
        service = create_production_appeal_service(app_settings)
        
        # 测试服务方法
        test_plans = [
            {
                'campaign_id': 'test_123',
                'principal_name': '测试主体1',
                'account_id': 123456
            },
            {
                'campaign_id': 'test_456',
                'principal_name': '测试主体1',
                'account_id': 123456
            },
            {
                'campaign_id': 'test_789',
                'principal_name': '测试主体2',
                'account_id': 789012
            }
        ]
        
        # 测试分组功能
        grouped = service.group_plans_by_account(test_plans)
        logger.success(f"✅ 分组功能正常: {len(grouped)} 个广告户")
        
        # 测试性能统计
        stats = service.get_performance_statistics(test_plans)
        logger.success(f"✅ 性能统计正常: 资源节约 {stats['resource_savings_percent']:.1f}%")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证生产环境服务失败: {e}")
        return False

def verify_scheduler_integration():
    """验证scheduler集成"""
    logger.info("🔍 验证scheduler集成...")
    
    try:
        # 检查scheduler文件
        scheduler_file = project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'scheduler.py'
        
        with open(scheduler_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('production_appeal_service', '生产环境服务导入'),
            ('优化版本', '优化版本标识'),
            ('batch_appeal_all_plans', '批量提审方法'),
            ('get_performance_statistics', '性能统计方法'),
            ('update_database_with_results', '数据库更新方法')
        ]
        
        all_passed = True
        for check, description in checks:
            if check in content:
                logger.success(f"✅ {description}: 已集成")
            else:
                logger.error(f"❌ {description}: 未集成")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        logger.error(f"❌ 验证scheduler集成失败: {e}")
        return False

def verify_celery_tasks():
    """验证Celery任务"""
    logger.info("🔍 验证Celery任务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        # 检查任务注册
        registered_tasks = list(app.tasks.keys())
        
        critical_tasks = [
            'tasks.appeal_plans',
            'tasks.create_plans',
            'tasks.monitor_materials'
        ]
        
        all_registered = True
        for task in critical_tasks:
            if task in registered_tasks:
                logger.success(f"✅ 任务已注册: {task}")
            else:
                logger.error(f"❌ 任务未注册: {task}")
                all_registered = False
        
        # 检查Beat调度配置
        beat_schedule = app.conf.beat_schedule
        if 'plan-appeal-configurable' in beat_schedule:
            logger.success("✅ 提审任务调度已配置")
        else:
            logger.error("❌ 提审任务调度未配置")
            all_registered = False
        
        return all_registered
        
    except Exception as e:
        logger.error(f"❌ 验证Celery任务失败: {e}")
        return False

def test_optimized_logic():
    """测试优化逻辑"""
    logger.info("🧪 测试优化逻辑...")
    
    try:
        # 模拟不同的计划分布
        test_scenarios = [
            {
                'name': '单广告户多计划',
                'plans': [
                    {'campaign_id': f'plan_{i}', 'principal_name': '主体A', 'account_id': 123456}
                    for i in range(5)
                ]
            },
            {
                'name': '多广告户多计划',
                'plans': [
                    {'campaign_id': f'plan_a_{i}', 'principal_name': '主体A', 'account_id': 123456}
                    for i in range(3)
                ] + [
                    {'campaign_id': f'plan_b_{i}', 'principal_name': '主体B', 'account_id': 789012}
                    for i in range(2)
                ]
            }
        ]
        
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
        
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        service = create_production_appeal_service(app_settings)
        
        for scenario in test_scenarios:
            logger.info(f"📋 测试场景: {scenario['name']}")
            
            stats = service.get_performance_statistics(scenario['plans'])
            
            logger.info(f"   📊 计划数: {stats['total_plans']}")
            logger.info(f"   🏢 广告户数: {stats['total_accounts']}")
            logger.info(f"   💾 资源节约: {stats['resource_savings_percent']:.1f}%")
            logger.info(f"   ⏱️ 时间节约: {stats['time_savings_percent']:.1f}%")
            
            if stats['resource_savings_percent'] > 0:
                logger.success(f"✅ {scenario['name']}: 优化效果显著")
            else:
                logger.info(f"💡 {scenario['name']}: 单计划场景，无优化空间")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试优化逻辑失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🎯 验证千川自动化项目优化批量提审服务生产环境部署")
    logger.info("="*80)
    logger.info("🔍 检查优化服务是否正确部署到生产环境")
    logger.info("="*80)
    
    try:
        verification_results = []
        
        # 1. 验证生产环境服务
        result1 = verify_production_service()
        verification_results.append(('生产环境服务', result1))
        
        # 2. 验证scheduler集成
        result2 = verify_scheduler_integration()
        verification_results.append(('Scheduler集成', result2))
        
        # 3. 验证Celery任务
        result3 = verify_celery_tasks()
        verification_results.append(('Celery任务', result3))
        
        # 4. 测试优化逻辑
        result4 = test_optimized_logic()
        verification_results.append(('优化逻辑', result4))
        
        # 生成验证报告
        logger.info("\n" + "="*80)
        logger.info("🎯 生产环境部署验证结果")
        logger.info("="*80)
        
        passed_count = sum(1 for _, result in verification_results if result)
        total_count = len(verification_results)
        
        logger.info(f"📊 验证结果: {passed_count}/{total_count} 项通过")
        
        for test_name, result in verification_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"   {test_name}: {status}")
        
        if passed_count == total_count:
            logger.success("\n🎉 生产环境部署验证完全成功！")
            logger.info("\n📋 验证总结:")
            logger.info("✅ 优化的批量提审服务已正确部署")
            logger.info("✅ Scheduler已集成优化逻辑")
            logger.info("✅ Celery任务配置正确")
            logger.info("✅ 优化逻辑功能正常")
            
            logger.info("\n🚀 生产环境状态:")
            logger.info("- 按广告户分组的优化提审服务: ✅ 已激活")
            logger.info("- 智投星对话复用: ✅ 已启用")
            logger.info("- 资源优化: ✅ 大幅减少浏览器会话")
            logger.info("- 时间优化: ✅ 显著提高处理效率")
            
            logger.info("\n💡 下一步:")
            logger.info("- 监控提审任务执行情况")
            logger.info("- 观察系统资源使用变化")
            logger.info("- 检查提审成功率")
            logger.info("- 评估整体性能提升")
            
        elif passed_count >= total_count * 0.75:
            logger.warning("⚠️ 生产环境部署基本成功，但有部分问题需要解决")
        else:
            logger.error("❌ 生产环境部署验证失败")
        
        return passed_count == total_count
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 千川自动化项目优化批量提审服务生产环境部署验证成功！")
        logger.info("💡 优化服务已成功应用到生产环境")
        logger.info("💡 系统将按广告户分组处理提审，大幅提高效率")
    else:
        logger.error("\n❌ 生产环境部署验证失败，请检查具体问题")
    
    sys.exit(0 if success else 1)
