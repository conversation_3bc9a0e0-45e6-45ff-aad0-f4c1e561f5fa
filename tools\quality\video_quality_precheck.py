#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频质量预检查工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 视频质量预检查工具，在上传前检测和修复质量问题
维护团队: 技术团队
"""

import sys
import os
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class VideoQualityPreChecker:
    """视频质量预检查器"""
    
    def __init__(self):
        self.min_duration = 3  # 最小时长3秒
        self.max_duration = 60  # 最大时长60秒
        self.min_width = 480  # 最小宽度
        self.min_height = 480  # 最小高度
        self.max_file_size = 500 * 1024 * 1024  # 最大文件大小500MB
        
        self.supported_formats = ['.mp4', '.mov', '.avi', '.mkv']
        self.supported_codecs = ['h264', 'h265', 'hevc']
    
    def check_file_basic_info(self, file_path: str) -> Dict[str, any]:
        """检查文件基本信息"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {
                'valid': False,
                'reason': '文件不存在',
                'file_exists': False
            }
        
        # 检查文件扩展名
        if file_path.suffix.lower() not in self.supported_formats:
            return {
                'valid': False,
                'reason': f'不支持的文件格式: {file_path.suffix}',
                'file_exists': True,
                'format_supported': False
            }
        
        # 检查文件大小
        file_size = file_path.stat().st_size
        if file_size > self.max_file_size:
            return {
                'valid': False,
                'reason': f'文件过大: {file_size / (1024*1024):.1f}MB',
                'file_exists': True,
                'format_supported': True,
                'file_size': file_size
            }
        
        if file_size == 0:
            return {
                'valid': False,
                'reason': '文件为空',
                'file_exists': True,
                'format_supported': True,
                'file_size': file_size
            }
        
        return {
            'valid': True,
            'file_exists': True,
            'format_supported': True,
            'file_size': file_size
        }
    
    def get_video_info_with_ffprobe(self, file_path: str) -> Optional[Dict]:
        """使用ffprobe获取视频信息"""
        try:
            cmd = [
                'ffprobe',
                '-v', 'quiet',
                '-print_format', 'json',
                '-show_format',
                '-show_streams',
                str(file_path)
            ]
            
            # 修复Windows编码问题
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=30,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )
            
            if result.returncode != 0:
                return None
            
            if not result.stdout.strip():
                return None
            
            data = json.loads(result.stdout)
            
            # 查找视频流
            video_stream = None
            for stream in data.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                return None
            
            format_info = data.get('format', {})
            
            # 安全地获取帧率
            fps = 0
            try:
                fps_str = video_stream.get('r_frame_rate', '0/1')
                if '/' in fps_str:
                    num, den = fps_str.split('/')
                    if int(den) != 0:
                        fps = int(num) / int(den)
            except:
                fps = 0
            
            return {
                'duration': float(format_info.get('duration', 0)),
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'codec': video_stream.get('codec_name', ''),
                'bitrate': int(format_info.get('bit_rate', 0)),
                'fps': fps,
                'format': format_info.get('format_name', '')
            }
            
        except Exception as e:
            print(f"⚠️ ffprobe检测失败: {e}")
            return None
    
    def check_video_technical_specs(self, file_path: str) -> Dict[str, any]:
        """检查视频技术规格"""
        video_info = self.get_video_info_with_ffprobe(file_path)
        
        if not video_info:
            return {
                'valid': False,
                'reason': '无法获取视频信息，可能文件损坏',
                'technical_readable': False
            }
        
        issues = []
        
        # 检查时长
        duration = video_info['duration']
        if duration < self.min_duration:
            issues.append(f'视频过短: {duration:.1f}秒 (最小{self.min_duration}秒)')
        elif duration > self.max_duration:
            issues.append(f'视频过长: {duration:.1f}秒 (最大{self.max_duration}秒)')
        
        # 检查尺寸
        width = video_info['width']
        height = video_info['height']
        if width < self.min_width or height < self.min_height:
            issues.append(f'视频尺寸过小: {width}x{height} (最小{self.min_width}x{self.min_height})')
        
        # 检查编码格式
        codec = video_info['codec'].lower()
        if codec and codec not in self.supported_codecs:
            issues.append(f'不支持的编码格式: {codec}')
        
        # 检查帧率
        fps = video_info['fps']
        if fps > 0 and (fps < 15 or fps > 60):
            issues.append(f'帧率异常: {fps:.1f}fps')
        
        if issues:
            return {
                'valid': False,
                'reason': '; '.join(issues),
                'technical_readable': True,
                'video_info': video_info,
                'issues': issues
            }
        
        return {
            'valid': True,
            'technical_readable': True,
            'video_info': video_info
        }
    
    def comprehensive_check(self, file_path: str) -> Dict[str, any]:
        """综合检查视频质量"""
        print(f"🔍 检查视频: {Path(file_path).name}")
        
        # 1. 基本信息检查
        basic_check = self.check_file_basic_info(file_path)
        if not basic_check['valid']:
            return {
                'valid': False,
                'reason': basic_check['reason'],
                'check_stage': 'basic',
                'details': basic_check
            }
        
        # 2. 技术规格检查
        tech_check = self.check_video_technical_specs(file_path)
        if not tech_check['valid']:
            return {
                'valid': False,
                'reason': tech_check['reason'],
                'check_stage': 'technical',
                'details': {**basic_check, **tech_check}
            }
        
        # 所有检查通过
        video_info = tech_check.get('video_info', {})
        return {
            'valid': True,
            'reason': '所有质量检查通过',
            'check_stage': 'complete',
            'details': {
                'file_size_mb': basic_check['file_size'] / (1024*1024),
                'duration': video_info.get('duration', 0),
                'resolution': f"{video_info.get('width', 0)}x{video_info.get('height', 0)}",
                'codec': video_info.get('codec', ''),
                'fps': video_info.get('fps', 0),
                'bitrate_kbps': video_info.get('bitrate', 0) / 1000 if video_info.get('bitrate', 0) > 0 else 0
            }
        }
    
    def batch_check_pending_materials(self, limit: int = 10) -> Dict[str, List]:
        """批量检查待上传素材"""
        print("🚀 开始批量检查待上传素材...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                pending_materials = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
                ).limit(limit).all()
                
                print(f"📊 找到 {len(pending_materials)} 个待检查素材")
                
                valid_materials = []
                invalid_materials = []
                
                for material in pending_materials:
                    check_result = self.comprehensive_check(material.file_path)
                    
                    material_info = {
                        'id': material.id,
                        'filename': material.filename,
                        'file_path': material.file_path,
                        'check_result': check_result
                    }
                    
                    if check_result['valid']:
                        valid_materials.append(material_info)
                        print(f"  ✅ {material.filename}")
                    else:
                        invalid_materials.append(material_info)
                        print(f"  ❌ {material.filename}: {check_result['reason']}")
                
                return {
                    'valid_materials': valid_materials,
                    'invalid_materials': invalid_materials
                }
                
        except Exception as e:
            print(f"❌ 批量检查失败: {e}")
            return {'valid_materials': [], 'invalid_materials': []}

def main():
    """主函数"""
    print("🚀 开始视频质量预检查...")
    
    checker = VideoQualityPreChecker()
    
    # 批量检查待上传素材
    print(f"\n{'='*60}")
    print("📊 批量检查待上传素材")
    print(f"{'='*60}")
    
    batch_result = checker.batch_check_pending_materials(limit=10)
    
    valid_count = len(batch_result['valid_materials'])
    invalid_count = len(batch_result['invalid_materials'])
    
    print(f"\n📊 检查结果统计:")
    print(f"  - 质量合格: {valid_count} 个")
    print(f"  - 质量不合格: {invalid_count} 个")
    
    if invalid_count > 0:
        print(f"\n❌ 质量不合格素材详情:")
        for item in batch_result['invalid_materials'][:5]:  # 只显示前5个
            filename = item['filename']
            reason = item['check_result']['reason']
            print(f"  - {filename}: {reason}")
        
        if invalid_count > 5:
            print(f"  ... 还有 {invalid_count - 5} 个质量不合格素材")
        
        print(f"\n💡 建议:")
        print(f"  1. 将质量不合格的素材状态改为upload_failed")
        print(f"  2. 通知内容团队重新制作这些素材")
        print(f"  3. 只对质量合格的素材进行上传")
    
    if valid_count > 0:
        print(f"\n✅ 有 {valid_count} 个素材质量合格，可以正常上传")
    
    print(f"\n🎉 视频质量预检查完成！")
    
    return 0

if __name__ == "__main__":
    exit(main())
