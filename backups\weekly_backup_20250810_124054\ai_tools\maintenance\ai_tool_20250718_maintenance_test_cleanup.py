#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时测试工具
生命周期: 7天
创建目的: 测试清理后的全局账户选择功能
依赖关系: Streamlit, 全局账户选择器组件
清理条件: 测试完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

def test_web_ui_imports():
    """测试Web UI的导入清理"""
    print("🔍 测试Web UI导入清理...")
    
    try:
        # 检查web_ui.py中的导入
        web_ui_path = project_root / "web_ui.py"
        if not web_ui_path.exists():
            print("❌ web_ui.py文件不存在")
            return False
        
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否删除了create_single_account_selector导入
        if "create_single_account_selector" in content:
            # 检查是否只在注释中出现
            lines = content.split('\n')
            import_lines = [line for line in lines if "create_single_account_selector" in line and not line.strip().startswith('#')]
            if import_lines:
                print("⚠️ create_single_account_selector导入未完全清理")
                for line in import_lines:
                    print(f"   发现: {line.strip()}")
                return False
        
        print("✅ create_single_account_selector导入已清理")
        
        # 检查是否保留了create_multi_account_selector（用于特殊场景）
        if "create_multi_account_selector" not in content:
            print("⚠️ create_multi_account_selector导入被误删")
            return False
        
        print("✅ create_multi_account_selector导入已保留（用于特殊场景）")
        
        # 检查是否包含全局账户选择器的导入
        if "ai_tool_20250718_maintenance_global_account_selector" not in content:
            print("❌ 全局账户选择器导入缺失")
            return False
        
        print("✅ 全局账户选择器导入正常")
        
        return True
        
    except Exception as e:
        print(f"❌ Web UI导入测试失败: {e}")
        return False

def test_function_usage_cleanup():
    """测试函数使用的清理情况"""
    print("🔍 测试函数使用清理...")
    
    try:
        web_ui_path = project_root / "web_ui.py"
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计create_single_account_selector的使用次数
        single_selector_count = content.count("create_single_account_selector(")
        print(f"📊 create_single_account_selector使用次数: {single_selector_count}")
        
        # 统计create_multi_account_selector的使用次数
        multi_selector_count = content.count("create_multi_account_selector(")
        print(f"📊 create_multi_account_selector使用次数: {multi_selector_count}")
        
        # 统计require_account_selection的使用次数
        require_count = content.count("require_account_selection(")
        print(f"📊 require_account_selection使用次数: {require_count}")
        
        # 统计get_global_selected_account的使用次数
        global_get_count = content.count("get_global_selected_account(")
        print(f"📊 get_global_selected_account使用次数: {global_get_count}")
        
        # 检查是否有合理的使用分布
        if single_selector_count > 2:  # 应该大部分被清理
            print("⚠️ create_single_account_selector使用次数过多，可能清理不彻底")
            return False
        
        if require_count < 3:  # 应该有多个地方使用
            print("⚠️ require_account_selection使用次数过少，可能未正确替换")
            return False
        
        print("✅ 函数使用分布合理")
        return True
        
    except Exception as e:
        print(f"❌ 函数使用清理测试失败: {e}")
        return False

def test_account_id_usage():
    """测试账户ID获取逻辑"""
    print("🔍 测试账户ID获取逻辑...")
    
    try:
        web_ui_path = project_root / "web_ui.py"
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否正确使用account_id_qc
        account_id_qc_count = content.count("account_id_qc")
        print(f"📊 account_id_qc使用次数: {account_id_qc_count}")
        
        # 检查是否有错误的账户ID获取方式
        error_patterns = [
            "selected_account.id",  # 应该使用account_id_qc而不是id
            "account_options[",     # 旧的字典查找方式
        ]
        
        for pattern in error_patterns:
            if pattern in content:
                print(f"⚠️ 发现可能的错误模式: {pattern}")
                # 查找具体位置
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if pattern in line and not line.strip().startswith('#'):
                        print(f"   第{i}行: {line.strip()}")
        
        print("✅ 账户ID获取逻辑检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 账户ID获取逻辑测试失败: {e}")
        return False

def test_new_launch_center():
    """测试新版投放中心"""
    print("🔍 测试新版投放中心...")
    
    try:
        from ai_tool_20250718_maintenance_new_launch_center import render_new_launch_center_page
        print("✅ 新版投放中心导入成功")
        
        # 检查是否正确使用全局账户选择
        new_center_path = project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_new_launch_center.py"
        with open(new_center_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用了全局账户选择函数
        if "get_global_selected_account" not in content:
            print("⚠️ 新版投放中心未使用全局账户选择")
            return False
        
        if "require_account_selection" not in content:
            print("⚠️ 新版投放中心未使用账户选择检查")
            return False
        
        print("✅ 新版投放中心使用全局账户选择")
        return True
        
    except Exception as e:
        print(f"❌ 新版投放中心测试失败: {e}")
        return False

def test_special_cases():
    """测试特殊场景保留情况"""
    print("🔍 测试特殊场景保留情况...")
    
    try:
        web_ui_path = project_root / "web_ui.py"
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查抖音号授权管理是否保留多选功能
        if "aweme_auth_form" in content:
            # 查找抖音号授权相关的多选
            lines = content.split('\n')
            in_aweme_section = False
            has_multiselect = False
            
            for line in lines:
                if "aweme_auth_form" in line:
                    in_aweme_section = True
                elif in_aweme_section and "st.form_submit_button" in line:
                    in_aweme_section = False
                elif in_aweme_section and "st.multiselect" in line:
                    has_multiselect = True
            
            if has_multiselect:
                print("✅ 抖音号授权管理保留了多选功能")
            else:
                print("⚠️ 抖音号授权管理的多选功能可能被误删")
                return False
        
        # 检查高级复制工具的目标账户选择
        if "replication_target_accounts" in content:
            print("✅ 高级复制工具保留了目标账户多选")
        else:
            print("⚠️ 高级复制工具的目标账户多选可能被误删")
        
        return True
        
    except Exception as e:
        print(f"❌ 特殊场景测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 全局账户选择器清理验证测试")
    print("=" * 50)
    
    tests = [
        ("Web UI导入清理测试", test_web_ui_imports),
        ("函数使用清理测试", test_function_usage_cleanup),
        ("账户ID获取逻辑测试", test_account_id_usage),
        ("新版投放中心测试", test_new_launch_center),
        ("特殊场景保留测试", test_special_cases)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有清理验证测试通过！")
        print("\n📋 清理总结:")
        print("✅ 删除了重复的账户选择组件")
        print("✅ 统一使用全局账户选择")
        print("✅ 保留了特殊场景的多选功能")
        print("✅ 修正了账户ID获取逻辑")
        print("✅ 清理了未使用的导入")
        
        print("\n🚀 可以启动Web UI测试清理后的功能:")
        print("   streamlit run web_ui.py")
        print("   测试各个功能页面的账户选择是否正常工作")
        return True
    else:
        print("⚠️ 部分清理验证失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
