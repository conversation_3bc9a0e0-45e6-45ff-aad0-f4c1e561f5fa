#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件路径同步修复工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 修复文件移动后数据库路径不同步的问题
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class FilePathSyncFixer:
    """文件路径同步修复器"""
    
    def __init__(self):
        self.workflow_assets_dir = Path("D:/workflow_assets")
        self.materials_to_process = self.workflow_assets_dir / "01_materials_to_process"
        self.processed_materials = self.workflow_assets_dir / "00_processed"
        
        self.fixes_applied = []
        self.issues_found = []
    
    def scan_file_locations(self) -> Dict[str, List[Path]]:
        """扫描文件实际位置"""
        print("🔍 扫描文件实际位置...")
        
        file_locations = {
            'to_process': [],
            'processed': [],
            'other_locations': []
        }
        
        # 扫描待处理目录
        if self.materials_to_process.exists():
            for file_path in self.materials_to_process.rglob("*.mp4"):
                file_locations['to_process'].append(file_path)
        
        # 扫描已处理目录
        if self.processed_materials.exists():
            for file_path in self.processed_materials.rglob("*.mp4"):
                file_locations['processed'].append(file_path)
        
        # 扫描其他可能的位置
        for subdir in self.workflow_assets_dir.iterdir():
            if subdir.is_dir() and subdir.name.startswith('0') and subdir != self.materials_to_process and subdir != self.processed_materials:
                for file_path in subdir.rglob("*.mp4"):
                    file_locations['other_locations'].append(file_path)
        
        print(f"📊 文件位置统计:")
        print(f"  - 待处理目录: {len(file_locations['to_process'])} 个文件")
        print(f"  - 已处理目录: {len(file_locations['processed'])} 个文件")
        print(f"  - 其他位置: {len(file_locations['other_locations'])} 个文件")
        
        return file_locations
    
    def analyze_database_file_paths(self) -> List[Dict]:
        """分析数据库中的文件路径"""
        print("\n🔍 分析数据库中的文件路径...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 查找所有素材记录
                materials = db.query(LocalCreative).all()
                
                path_analysis = []
                
                for material in materials:
                    file_path = Path(material.file_path)
                    
                    analysis_item = {
                        'id': material.id,
                        'filename': material.filename,
                        'status': material.status,
                        'db_path': material.file_path,
                        'file_exists': file_path.exists(),
                        'actual_location': None,
                        'needs_update': False
                    }
                    
                    # 如果文件不存在，尝试在其他位置查找
                    if not file_path.exists():
                        actual_location = self.find_file_in_workflow_assets(material.filename)
                        if actual_location:
                            analysis_item['actual_location'] = str(actual_location)
                            analysis_item['needs_update'] = True
                    
                    path_analysis.append(analysis_item)
                
                return path_analysis
                
        except Exception as e:
            print(f"❌ 分析数据库文件路径失败: {e}")
            return []
    
    def find_file_in_workflow_assets(self, filename: str) -> Optional[Path]:
        """在workflow_assets目录中查找文件"""
        if not self.workflow_assets_dir.exists():
            return None
        
        # 在所有子目录中查找文件
        for file_path in self.workflow_assets_dir.rglob(filename):
            if file_path.is_file():
                return file_path
        
        return None
    
    def fix_file_path_mismatches(self, path_analysis: List[Dict]) -> int:
        """修复文件路径不匹配问题"""
        print("\n🔧 修复文件路径不匹配问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            fixed_count = 0
            
            with database_session() as db:
                for item in path_analysis:
                    if item['needs_update'] and item['actual_location']:
                        try:
                            material = db.query(LocalCreative).filter(
                                LocalCreative.id == item['id']
                            ).first()
                            
                            if material:
                                old_path = material.file_path
                                new_path = item['actual_location']
                                
                                material.file_path = new_path
                                material.updated_at = datetime.utcnow()
                                
                                db.commit()
                                fixed_count += 1
                                
                                self.fixes_applied.append(f"更新素材 {item['id']} 路径: {Path(old_path).name} -> {Path(new_path).name}")
                                
                                if fixed_count <= 10:  # 只显示前10个
                                    print(f"  ✅ 修复素材 {item['id']}: {Path(old_path).name}")
                        
                        except Exception as e:
                            print(f"  ❌ 修复素材 {item['id']} 失败: {e}")
                            db.rollback()
            
            print(f"📊 修复结果: {fixed_count} 个文件路径已更新")
            return fixed_count
            
        except Exception as e:
            print(f"❌ 修复文件路径失败: {e}")
            return 0
    
    def identify_duplicate_upload_attempts(self) -> List[Dict]:
        """识别重复上传尝试"""
        print("\n🔍 识别重复上传尝试...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            from sqlalchemy import func
            
            with database_session() as db:
                # 查找相同文件名的多个记录
                duplicate_files = db.query(
                    LocalCreative.filename,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.filename).having(
                    func.count(LocalCreative.id) > 1
                ).all()
                
                duplicate_analysis = []
                
                for dup in duplicate_files:
                    records = db.query(LocalCreative).filter(
                        LocalCreative.filename == dup.filename
                    ).all()
                    
                    duplicate_analysis.append({
                        'filename': dup.filename,
                        'count': dup.count,
                        'records': [
                            {
                                'id': r.id,
                                'status': r.status,
                                'file_path': r.file_path,
                                'created_at': r.created_at,
                                'updated_at': r.updated_at
                            } for r in records
                        ]
                    })
                
                print(f"📊 发现 {len(duplicate_analysis)} 个重复文件")
                return duplicate_analysis
                
        except Exception as e:
            print(f"❌ 识别重复上传失败: {e}")
            return []
    
    def fix_status_inconsistencies(self) -> int:
        """修复状态不一致问题"""
        print("\n🔧 修复状态不一致问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            fixed_count = 0
            
            with database_session() as db:
                # 查找文件不存在但状态不是upload_failed的素材
                materials_to_fix = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([
                        MaterialStatus.PENDING_UPLOAD.value,
                        MaterialStatus.UPLOADING.value
                    ])
                ).all()
                
                for material in materials_to_fix:
                    file_path = Path(material.file_path)
                    
                    # 如果文件不存在，检查是否已经被处理
                    if not file_path.exists():
                        # 尝试在已处理目录中查找
                        actual_location = self.find_file_in_workflow_assets(material.filename)
                        
                        if actual_location:
                            # 文件存在于其他位置，更新路径
                            material.file_path = str(actual_location)
                            material.updated_at = datetime.utcnow()
                            fixed_count += 1
                        else:
                            # 文件确实不存在，标记为失败
                            material.status = MaterialStatus.UPLOAD_FAILED.value
                            material.updated_at = datetime.utcnow()
                            fixed_count += 1
                            
                            self.fixes_applied.append(f"标记素材 {material.id} 为upload_failed: 文件不存在")
                
                db.commit()
                
            print(f"📊 修复结果: {fixed_count} 个状态不一致问题已修复")
            return fixed_count
            
        except Exception as e:
            print(f"❌ 修复状态不一致失败: {e}")
            return 0
    
    def generate_sync_report(self, path_analysis: List[Dict], duplicate_analysis: List[Dict]) -> str:
        """生成同步报告"""
        from datetime import datetime
        
        # 统计信息
        total_materials = len(path_analysis)
        missing_files = sum(1 for item in path_analysis if not item['file_exists'])
        needs_update = sum(1 for item in path_analysis if item['needs_update'])
        duplicate_files = len(duplicate_analysis)
        
        report = f"""
{'='*60}
🔧 文件路径同步修复报告
{'='*60}
修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 统计信息:
- 总素材数: {total_materials}
- 文件缺失: {missing_files}
- 需要更新路径: {needs_update}
- 重复文件: {duplicate_files}

"""
        
        if self.fixes_applied:
            report += "✅ 应用的修复:\n"
            for i, fix in enumerate(self.fixes_applied[:10], 1):  # 只显示前10个
                report += f"  {i}. {fix}\n"
            
            if len(self.fixes_applied) > 10:
                report += f"  ... 还有 {len(self.fixes_applied) - 10} 个修复\n"
            report += "\n"
        
        if self.issues_found:
            report += "❌ 发现的问题:\n"
            for i, issue in enumerate(self.issues_found[:10], 1):
                report += f"  {i}. {issue}\n"
            report += "\n"
        
        report += "💡 建议:\n"
        if missing_files > 0:
            report += f"  1. 有 {missing_files} 个文件缺失，建议检查文件移动逻辑\n"
        if duplicate_files > 0:
            report += f"  2. 有 {duplicate_files} 个重复文件，建议清理重复记录\n"
        if needs_update > 0:
            report += f"  3. 有 {needs_update} 个路径需要更新，已自动修复\n"
        
        report += "  4. 建议实施文件移动后的路径同步机制\n"
        report += "  5. 建议添加文件存在性检查到上传任务\n"
        
        report += f"\n{'='*60}\n"
        
        return report
    
    def run_comprehensive_fix(self) -> Dict:
        """运行综合修复"""
        print("🚀 开始文件路径同步综合修复...")
        
        results = {}
        
        # 1. 扫描文件位置
        file_locations = self.scan_file_locations()
        results['file_locations'] = file_locations
        
        # 2. 分析数据库路径
        path_analysis = self.analyze_database_file_paths()
        results['path_analysis'] = path_analysis
        
        # 3. 修复路径不匹配
        fixed_paths = self.fix_file_path_mismatches(path_analysis)
        results['fixed_paths'] = fixed_paths
        
        # 4. 识别重复上传
        duplicate_analysis = self.identify_duplicate_upload_attempts()
        results['duplicate_analysis'] = duplicate_analysis
        
        # 5. 修复状态不一致
        fixed_status = self.fix_status_inconsistencies()
        results['fixed_status'] = fixed_status
        
        # 6. 生成报告
        sync_report = self.generate_sync_report(path_analysis, duplicate_analysis)
        results['sync_report'] = sync_report
        
        print(sync_report)
        
        return results

def main():
    """主函数"""
    print("🔧 文件路径同步修复工具")
    print("=" * 60)
    
    fixer = FilePathSyncFixer()
    
    try:
        # 运行综合修复
        results = fixer.run_comprehensive_fix()
        
        # 保存报告
        from datetime import datetime
        report_file = Path("logs") / f"file_path_sync_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results['sync_report'])
        
        print(f"📄 同步报告已保存到: {report_file}")
        
        # 返回状态
        total_fixes = results.get('fixed_paths', 0) + results.get('fixed_status', 0)
        
        if total_fixes > 0:
            print(f"\n🎉 成功应用 {total_fixes} 个修复！")
            print("💡 建议重启上传任务以应用修复")
            return 0
        else:
            print(f"\n✅ 文件路径同步正常，无需修复")
            return 0
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        return 2

if __name__ == "__main__":
    exit(main())
