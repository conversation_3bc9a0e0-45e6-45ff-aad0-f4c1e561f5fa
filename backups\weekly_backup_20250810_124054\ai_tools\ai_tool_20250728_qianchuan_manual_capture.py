"""
千川申诉手动操作参数捕获器
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 通过手动操作指导，精准捕获callback参数
依赖关系: Playwright浏览器自动化
清理条件: 功能被官方API替代时可删除

使用方式：
1. 启动浏览器并导航到千川后台
2. 用户手动执行申诉操作
3. 程序在后台捕获callback请求参数
4. 保存参数供API使用
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanManualCapture:
    """千川申诉手动操作参数捕获器"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的callback请求
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_comprehensive_capture(self, page: Page):
        """设置全面的网络请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                method = request.method
                
                # 捕获所有copilot相关的POST请求
                if 'copilot' in url and method == 'POST':
                    logger.info(f"🎯 捕获到copilot POST请求: {url}")
                    
                    try:
                        post_data = request.post_data
                        if post_data:
                            try:
                                data = json.loads(post_data)
                                
                                # 记录请求信息
                                request_info = {
                                    'url': url,
                                    'method': method,
                                    'data': data,
                                    'headers': dict(request.headers),
                                    'timestamp': time.time(),
                                    'is_callback': 'callback' in url
                                }
                                
                                self.callback_requests.append(request_info)
                                
                                if 'callback' in url:
                                    logger.success(f"🎉 捕获到callback请求！")
                                    logger.info(f"📋 关键参数:")
                                    logger.info(f"  sessionId: {data.get('sessionId', 'N/A')}")
                                    logger.info(f"  windowId: {data.get('windowId', 'N/A')}")
                                    logger.info(f"  messageId: {data.get('messageId', 'N/A')}")
                                    logger.info(f"  callBackCode: {data.get('callBackCode', 'N/A')}")
                                else:
                                    logger.info(f"📋 其他copilot请求: {list(data.keys())}")
                                    
                            except json.JSONDecodeError:
                                logger.info(f"📋 非JSON POST数据: {post_data[:100]}...")
                                
                    except Exception as e:
                        logger.debug(f"处理POST数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        page.on("request", handle_request)
    
    def start_manual_capture_session(self, advertiser_id: str) -> Dict[str, Any]:
        """启动手动捕获会话"""
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 启动手动参数捕获会话...")
            logger.info(f"📋 广告户ID: {advertiser_id}")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)  # 可见模式
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            
            # 设置全面的网络捕获
            self._setup_comprehensive_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url, wait_until="networkidle", timeout=60000)
            
            # 等待页面完全加载
            page.wait_for_timeout(5000)
            logger.info("✅ 页面加载完成")
            
            # 显示操作指导
            self._show_manual_instructions()
            
            # 等待用户手动操作
            logger.info("⏳ 等待您手动执行申诉操作...")
            logger.info("💡 请按照指导完成申诉流程，程序将自动捕获参数")
            
            # 持续监听，直到捕获到callback请求或用户中断
            max_wait_time = 300  # 最多等待5分钟
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                # 检查是否捕获到callback请求
                callback_requests = [req for req in self.callback_requests if req['is_callback']]
                if callback_requests:
                    logger.success(f"🎉 成功捕获到 {len(callback_requests)} 个callback请求！")
                    break
                
                # 每5秒检查一次
                page.wait_for_timeout(5000)
                
                # 显示当前捕获状态
                total_requests = len(self.callback_requests)
                if total_requests > 0:
                    logger.info(f"📊 已捕获 {total_requests} 个copilot请求，等待callback...")
            
            # 分析捕获结果
            return self._analyze_captured_requests()
            
        except Exception as e:
            logger.error(f"❌ 手动捕获会话失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 询问是否关闭浏览器
            try:
                logger.info("🔍 捕获完成，浏览器将保持打开状态供您查看")
                logger.info("💡 如需关闭浏览器，请按 Ctrl+C")
                
                # 保持浏览器打开，直到用户中断
                while True:
                    time.sleep(10)
                    
            except KeyboardInterrupt:
                logger.info("👋 用户中断，正在关闭浏览器...")
                
            finally:
                try:
                    if context:
                        context.close()
                    if browser:
                        browser.close()
                    if playwright:
                        playwright.stop()
                except:
                    pass
    
    def _show_manual_instructions(self):
        """显示手动操作指导"""
        print("\n" + "="*60)
        print("🎯 千川申诉手动操作指导")
        print("="*60)
        print("请按照以下步骤手动执行申诉操作：")
        print()
        print("1. 🔍 寻找智投星图标或申诉相关按钮")
        print("   - 可能在页面右下角或工具栏中")
        print("   - 寻找'智投星'、'申诉'、'客服'等字样")
        print()
        print("2. 📝 点击'自助申诉表单'")
        print("   - 如果看到申诉选项，点击进入")
        print()
        print("3. 📋 选择'计划/商品申诉'")
        print("   - 在申诉类型中选择计划相关选项")
        print()
        print("4. 🎯 选择问题类型")
        print("   - 选择'计划审核不通过/结果申诉'")
        print()
        print("5. 📝 输入计划ID")
        print("   - 输入: 1838840072680523")
        print()
        print("6. ✅ 点击'提交'按钮")
        print("   - 提交后程序会自动捕获参数")
        print()
        print("💡 提示：")
        print("- 程序正在后台监听网络请求")
        print("- 当您点击提交时，会自动捕获callback参数")
        print("- 如果找不到申诉入口，请尝试不同的页面或功能")
        print("="*60)
        print()
    
    def _analyze_captured_requests(self) -> Dict[str, Any]:
        """分析捕获的请求"""
        try:
            if not self.callback_requests:
                return {
                    "success": False, 
                    "error": "未捕获到任何copilot请求",
                    "total_requests": 0,
                    "callback_requests": 0
                }
            
            # 分离callback请求和其他请求
            callback_requests = [req for req in self.callback_requests if req['is_callback']]
            other_requests = [req for req in self.callback_requests if not req['is_callback']]
            
            logger.info(f"📊 捕获分析:")
            logger.info(f"  总请求数: {len(self.callback_requests)}")
            logger.info(f"  callback请求: {len(callback_requests)}")
            logger.info(f"  其他copilot请求: {len(other_requests)}")
            
            if callback_requests:
                # 使用最新的callback请求
                latest_callback = callback_requests[-1]
                data = latest_callback['data']
                
                # 提取关键参数
                params = {
                    "success": True,
                    "sessionId": data.get('sessionId'),
                    "windowId": data.get('windowId'),
                    "messageId": data.get('messageId'),
                    "callValue": data.get('callValue'),
                    "applicationCode": data.get('applicationCode', 'QC'),
                    "callBackCode": data.get('callBackCode'),
                    "url": latest_callback['url'],
                    "headers": latest_callback['headers'],
                    "captured_at": latest_callback['timestamp'],
                    "total_requests": len(self.callback_requests),
                    "callback_requests": len(callback_requests),
                    "raw_data": data
                }
                
                # 保存参数到文件
                self._save_captured_params(params)
                
                logger.success(f"✅ 成功提取callback参数")
                return params
            else:
                return {
                    "success": False,
                    "error": "捕获到copilot请求但没有callback请求",
                    "total_requests": len(self.callback_requests),
                    "callback_requests": 0,
                    "other_requests": len(other_requests),
                    "captured_urls": [req['url'] for req in other_requests]
                }
                
        except Exception as e:
            logger.error(f"分析捕获请求失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_captured_params(self, params: Dict[str, Any]):
        """保存捕获的参数到文件"""
        try:
            # 保存到临时文件
            timestamp = int(time.time())
            filename = f"ai_temp/captured_callback_params_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(params, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"💾 参数已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存参数失败: {e}")


def start_manual_capture(advertiser_id: str, principal_name: str = "缇萃百货") -> Dict[str, Any]:
    """
    便捷函数：启动手动参数捕获会话
    
    Args:
        advertiser_id: 广告户ID
        principal_name: 主体名称
        
    Returns:
        捕获结果字典
    """
    capture = QianchuanManualCapture(principal_name)
    return capture.start_manual_capture_session(advertiser_id)


if __name__ == "__main__":
    # 启动手动捕获会话
    test_advertiser_id = "1836333804939273"
    
    print("🎯 千川申诉手动参数捕获器")
    print("=" * 50)
    print("这个工具将：")
    print("1. 打开千川后台页面")
    print("2. 显示详细的操作指导")
    print("3. 在后台监听网络请求")
    print("4. 自动捕获callback参数")
    print("5. 保存参数供API使用")
    print()
    
    confirm = input("确认开始手动捕获会话？(y/N): ").strip().lower()
    if confirm == 'y':
        try:
            result = start_manual_capture(test_advertiser_id)
            
            if result.get('success'):
                print("\n🎉 参数捕获成功！")
                print("现在您可以使用这些参数进行API调用")
            else:
                print(f"\n❌ 参数捕获失败: {result.get('error')}")
                if result.get('total_requests', 0) > 0:
                    print(f"💡 捕获到 {result['total_requests']} 个请求，但没有callback请求")
                    print("建议：尝试不同的申诉入口或操作流程")
                    
        except KeyboardInterrupt:
            print("\n👋 用户中断操作")
        except Exception as e:
            print(f"\n💥 程序异常: {e}")
    else:
        print("操作已取消")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_manual_capture import start_manual_capture")
    print("result = start_manual_capture('广告户ID')")
