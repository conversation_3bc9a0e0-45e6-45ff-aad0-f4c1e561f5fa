#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化系统 - 统一素材状态定义

这是整个项目中素材状态定义的单一真实来源(Single Source of Truth)。
所有其他文件必须从此文件导入状态定义，严禁使用硬编码状态字符串。

状态命名铁律：
1. 所有状态名称必须使用小写字母和下划线
2. 状态名称必须具有明确的业务含义
3. 状态转换必须遵循预定义的路径
4. 禁止在代码中直接使用字符串，必须使用枚举值

创建时间: 2025-08-10
维护责任: 千川自动化系统核心团队
变更审批: 所有状态定义变更必须经过技术负责人审批
"""

from enum import Enum
from typing import Dict, List, Set
from dataclasses import dataclass


class MaterialStatus(Enum):
    """
    视频素材状态枚举 - 项目级别的状态定义标准
    
    状态生命周期：
    new → pending_grouping → processing → uploaded_pending_plan → 
    testing_pending_review → approved/rejected → harvested
    
    异常状态：
    upload_failed, quality_failed, plan_creation_failed, already_tested
    """
    
    # === 基础流程状态 ===
    NEW = "new"                                    # 新创建的素材
    PENDING_GROUPING = "pending_grouping"          # 等待分组处理
    PROCESSING = "processing"                      # 正在处理（上传中）
    UPLOADED_PENDING_PLAN = "uploaded_pending_plan"  # 已上传，等待创建计划
    TESTING_PENDING_REVIEW = "testing_pending_review"  # 测试中，等待审核

    # === 终态状态 ===
    APPROVED = "approved"                          # 审核通过
    REJECTED = "rejected"                          # 审核拒绝
    HARVESTED = "harvested"                        # 已收割（最终状态）
    ALREADY_TESTED = "already_tested"              # 已测试过（跳过状态）

    # === 异常状态 ===
    UPLOAD_FAILED = "upload_failed"               # 上传失败
    QUALITY_FAILED = "quality_failed"             # 质量检查失败
    PLAN_CREATION_FAILED = "plan_creation_failed" # 计划创建失败

    # === 兼容性状态（逐步废弃） ===
    # 注意：以下状态仅为向后兼容，新代码不应使用
    PENDING_UPLOAD = "pending_upload"             # 废弃：使用 PENDING_GROUPING 替代
    UPLOADING = "uploading"                       # 废弃：使用 PROCESSING 替代
    UPLOADED = "uploaded"                         # 废弃：使用 UPLOADED_PENDING_PLAN 替代
    
    def __str__(self) -> str:
        """返回状态的字符串值"""
        return self.value
    
    @classmethod
    def get_all_values(cls) -> List[str]:
        """获取所有状态值的列表"""
        return [status.value for status in cls]
    
    @classmethod
    def get_active_statuses(cls) -> List[str]:
        """获取所有活跃状态（排除废弃状态）"""
        deprecated = {cls.PENDING_UPLOAD, cls.UPLOADING, cls.UPLOADED}
        return [status.value for status in cls if status not in deprecated]
    
    @classmethod
    def get_terminal_statuses(cls) -> List[str]:
        """获取所有终态状态"""
        terminal = {
            cls.HARVESTED, cls.ALREADY_TESTED, cls.QUALITY_FAILED
        }
        return [status.value for status in terminal]
    
    @classmethod
    def get_error_statuses(cls) -> List[str]:
        """获取所有错误状态"""
        error = {
            cls.UPLOAD_FAILED, cls.QUALITY_FAILED, cls.PLAN_CREATION_FAILED
        }
        return [status.value for status in error]
    
    @classmethod
    def is_valid_status(cls, status: str) -> bool:
        """检查状态是否有效"""
        return status in cls.get_all_values()
    
    @classmethod
    def is_terminal_status(cls, status: str) -> bool:
        """检查是否为终态状态"""
        return status in cls.get_terminal_statuses()
    
    @classmethod
    def is_error_status(cls, status: str) -> bool:
        """检查是否为错误状态"""
        return status in cls.get_error_statuses()


@dataclass
class StatusTransition:
    """状态转换定义"""
    from_status: str
    to_status: str
    description: str
    conditions: List[str] = None
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = []


class MaterialStateTransitions:
    """
    素材状态转换定义
    
    定义所有有效的状态转换路径，作为状态转换验证的依据。
    任何状态转换都必须在此处定义，否则将被拒绝。
    """
    
    # 定义所有有效的状态转换
    VALID_TRANSITIONS: Dict[str, List[str]] = {
        # 基础流程转换
        MaterialStatus.NEW.value: [
            MaterialStatus.PENDING_GROUPING.value
        ],
        
        MaterialStatus.PENDING_GROUPING.value: [
            MaterialStatus.PROCESSING.value,
            MaterialStatus.ALREADY_TESTED.value,
            # 兼容性转换（逐步废弃）
            MaterialStatus.PENDING_UPLOAD.value
        ],
        
        MaterialStatus.PROCESSING.value: [
            MaterialStatus.UPLOADED_PENDING_PLAN.value,
            MaterialStatus.UPLOAD_FAILED.value,
            MaterialStatus.QUALITY_FAILED.value
        ],
        
        MaterialStatus.UPLOADED_PENDING_PLAN.value: [
            MaterialStatus.TESTING_PENDING_REVIEW.value,
            MaterialStatus.PLAN_CREATION_FAILED.value
        ],
        
        MaterialStatus.TESTING_PENDING_REVIEW.value: [
            MaterialStatus.APPROVED.value,
            MaterialStatus.REJECTED.value
        ],
        
        MaterialStatus.APPROVED.value: [
            MaterialStatus.HARVESTED.value
        ],
        
        # 错误恢复转换
        MaterialStatus.REJECTED.value: [
            MaterialStatus.PENDING_GROUPING.value   # 可以重新处理
        ],
        
        MaterialStatus.UPLOAD_FAILED.value: [
            MaterialStatus.PENDING_GROUPING.value   # 可以重试
        ],
        
        MaterialStatus.PLAN_CREATION_FAILED.value: [
            MaterialStatus.UPLOADED_PENDING_PLAN.value  # 可以重试
        ],
        
        # 兼容性转换（逐步废弃）
        MaterialStatus.PENDING_UPLOAD.value: [
            MaterialStatus.UPLOADING.value,
            MaterialStatus.PROCESSING.value
        ],
        
        MaterialStatus.UPLOADING.value: [
            MaterialStatus.UPLOADED.value,
            MaterialStatus.PROCESSING.value,
            MaterialStatus.UPLOAD_FAILED.value
        ],
        
        MaterialStatus.UPLOADED.value: [
            MaterialStatus.UPLOADED_PENDING_PLAN.value
        ],
        
        # 终态状态（无后续转换）
        MaterialStatus.HARVESTED.value: [],
        MaterialStatus.ALREADY_TESTED.value: [],
        MaterialStatus.QUALITY_FAILED.value: []
    }
    
    @classmethod
    def get_valid_next_states(cls, current_state: str) -> List[str]:
        """获取当前状态的有效下一状态"""
        return MaterialStateTransitions.VALID_TRANSITIONS.get(current_state, [])
    
    @classmethod
    def can_transition(cls, from_state: str, to_state: str) -> bool:
        """检查状态转换是否有效"""
        valid_next_states = cls.get_valid_next_states(from_state)
        return to_state in valid_next_states
    
    @classmethod
    def get_all_transitions(cls) -> List[StatusTransition]:
        """获取所有状态转换的详细信息"""
        transitions = []
        
        # 基础流程转换
        transitions.extend([
            StatusTransition(
                MaterialStatus.NEW.value,
                MaterialStatus.PENDING_GROUPING.value,
                "新素材进入分组队列"
            ),
            StatusTransition(
                MaterialStatus.PENDING_GROUPING.value,
                MaterialStatus.PROCESSING.value,
                "开始处理素材（上传）"
            ),
            StatusTransition(
                MaterialStatus.PROCESSING.value,
                MaterialStatus.UPLOADED_PENDING_PLAN.value,
                "素材上传成功，等待创建计划"
            ),
            StatusTransition(
                MaterialStatus.UPLOADED_PENDING_PLAN.value,
                MaterialStatus.TESTING_PENDING_REVIEW.value,
                "计划创建成功，开始测试"
            ),
            StatusTransition(
                MaterialStatus.TESTING_PENDING_REVIEW.value,
                MaterialStatus.APPROVED.value,
                "测试通过，审核批准"
            ),
            StatusTransition(
                MaterialStatus.APPROVED.value,
                MaterialStatus.HARVESTED.value,
                "素材收割完成"
            )
        ])
        
        # 错误处理转换
        transitions.extend([
            StatusTransition(
                MaterialStatus.PROCESSING.value,
                MaterialStatus.UPLOAD_FAILED.value,
                "上传失败",
                ["网络错误", "API错误", "文件损坏"]
            ),
            StatusTransition(
                MaterialStatus.PROCESSING.value,
                MaterialStatus.QUALITY_FAILED.value,
                "质量检查失败",
                ["文件格式不支持", "分辨率不符合要求", "内容违规"]
            ),
            StatusTransition(
                MaterialStatus.UPLOADED_PENDING_PLAN.value,
                MaterialStatus.PLAN_CREATION_FAILED.value,
                "计划创建失败",
                ["账户余额不足", "素材被拒绝", "API限制"]
            )
        ])
        
        return transitions
    
    @classmethod
    def validate_transition_path(cls, status_path: List[str]) -> bool:
        """验证状态转换路径是否有效"""
        if len(status_path) < 2:
            return True
        
        for i in range(len(status_path) - 1):
            current_state = status_path[i]
            next_state = status_path[i + 1]
            
            if not cls.can_transition(current_state, next_state):
                return False
        
        return True


# === 状态分组定义 ===

class StatusGroups:
    """状态分组定义，用于批量操作和查询"""
    
    # 工作流活跃状态
    ACTIVE_WORKFLOW = {
        MaterialStatus.NEW.value,
        MaterialStatus.PENDING_GROUPING.value,
        MaterialStatus.PROCESSING.value,
        MaterialStatus.UPLOADED_PENDING_PLAN.value,
        MaterialStatus.TESTING_PENDING_REVIEW.value
    }
    
    # 需要人工干预的状态
    REQUIRES_INTERVENTION = {
        MaterialStatus.UPLOAD_FAILED.value,
        MaterialStatus.PLAN_CREATION_FAILED.value,
        MaterialStatus.REJECTED.value
    }
    
    # 成功完成的状态
    SUCCESS_STATES = {
        MaterialStatus.APPROVED.value,
        MaterialStatus.HARVESTED.value
    }
    
    # 可重试的错误状态
    RETRYABLE_ERRORS = {
        MaterialStatus.UPLOAD_FAILED.value,
        MaterialStatus.PLAN_CREATION_FAILED.value
    }
    
    # 不可恢复的错误状态
    PERMANENT_ERRORS = {
        MaterialStatus.QUALITY_FAILED.value
    }


# === 导出接口 ===

# 主要枚举类
__all__ = [
    'MaterialStatus',
    'MaterialStateTransitions', 
    'StatusTransition',
    'StatusGroups'
]

# 便捷访问接口
def get_status_enum() -> MaterialStatus:
    """获取状态枚举类"""
    return MaterialStatus

def get_transitions() -> MaterialStateTransitions:
    """获取状态转换类"""
    return MaterialStateTransitions

def validate_status(status: str) -> bool:
    """验证状态是否有效"""
    return MaterialStatus.is_valid_status(status)

def validate_transition(from_status: str, to_status: str) -> bool:
    """验证状态转换是否有效"""
    return MaterialStateTransitions.can_transition(from_status, to_status)

# 在类定义完成后添加便捷访问属性
MaterialStatus.VALID_TRANSITIONS = MaterialStateTransitions.VALID_TRANSITIONS
