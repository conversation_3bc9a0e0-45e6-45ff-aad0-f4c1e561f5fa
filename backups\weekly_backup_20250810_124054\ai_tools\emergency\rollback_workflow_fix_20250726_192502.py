#!/usr/bin/env python3
# 工作流修复回滚脚本
# 生成时间: 2025-07-26 19:25:02.220693

import os
import shutil

project_root = r"D:\Project\qianchuangzl"
backup_files = ['D:\\Project\\qianchuangzl\\config/settings.yml.backup_20250726_192501', 'D:\\Project\\qianchuangzl\\src/qianchuan_aw/workflows/scheduler.py.backup_20250726_192501']

def rollback():
    print("🔄 开始回滚...")
    
    for backup_file in backup_files:
        if os.path.exists(backup_file):
            original_file = backup_file.split('.backup_')[0]
            try:
                shutil.copy2(backup_file, original_file)
                print(f"✅ 已回滚: {original_file}")
            except Exception as e:
                print(f"❌ 回滚失败: {original_file} - {e}")
    
    print("🎯 回滚完成，请重启Celery服务")

if __name__ == '__main__':
    rollback()
