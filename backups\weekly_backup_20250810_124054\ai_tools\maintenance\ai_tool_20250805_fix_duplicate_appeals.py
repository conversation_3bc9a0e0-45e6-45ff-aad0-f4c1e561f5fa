#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 维护工具
生命周期: 永久保留
创建目的: 修复重复提审问题，确保每个计划只提审一次的铁律
清理条件: 问题彻底解决后可归档
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class DuplicateAppealFixer:
    """重复提审修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'duplicate_appeals_found': 0,
            'appeals_fixed': 0,
            'status_corrections': 0,
            'recommendations': []
        }

    def analyze_duplicate_appeals(self):
        """分析重复提审问题"""
        print("🔍 分析重复提审问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import and_
            
            with database_session() as db:
                # 查找重复提审的计划
                duplicate_appeals = db.query(Campaign).filter(
                    and_(
                        Campaign.appeal_attempt_count > 1,
                        Campaign.created_at >= datetime.utcnow() - timedelta(days=3)
                    )
                ).all()
                
                self.results['duplicate_appeals_found'] = len(duplicate_appeals)
                
                print(f"📊 发现 {len(duplicate_appeals)} 个重复提审的计划")
                
                if duplicate_appeals:
                    print("⚠️ 重复提审详情:")
                    for campaign in duplicate_appeals[:10]:  # 只显示前10个
                        print(f"  - 计划 {campaign.campaign_id_qc}: 提审 {campaign.appeal_attempt_count} 次")
                        print(f"    状态: {campaign.appeal_status}, 首次提审: {campaign.first_appeal_at}")
                
                return duplicate_appeals
                
        except Exception as e:
            print(f"❌ 分析重复提审失败: {e}")
            return []

    def fix_duplicate_appeal_status(self):
        """修复重复提审状态"""
        print("🔧 修复重复提审状态...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import and_
            
            with database_session() as db:
                # 查找需要修复的计划
                campaigns_to_fix = db.query(Campaign).filter(
                    and_(
                        Campaign.appeal_attempt_count > 1,
                        Campaign.appeal_status == 'appeal_pending',
                        Campaign.created_at >= datetime.utcnow() - timedelta(days=3)
                    )
                ).all()
                
                fixed_count = 0
                
                for campaign in campaigns_to_fix:
                    # 重置为只提审一次的状态
                    campaign.appeal_attempt_count = 1
                    campaign.appeal_status = 'APPEALING'  # 设置为正在申诉状态
                    
                    # 如果没有首次提审时间，设置为当前时间
                    if not campaign.first_appeal_at:
                        campaign.first_appeal_at = datetime.utcnow()
                    
                    fixed_count += 1
                    print(f"  ✅ 修复计划 {campaign.campaign_id_qc}: 重置提审次数为1")
                
                db.commit()
                self.results['appeals_fixed'] = fixed_count
                print(f"🔧 修复完成，处理了 {fixed_count} 个计划")
                
                return fixed_count
                
        except Exception as e:
            print(f"❌ 修复重复提审状态失败: {e}")
            return 0

    def add_appeal_protection_check(self):
        """添加提审保护检查"""
        print("🛡️ 添加提审保护检查...")
        
        # 检查scheduler.py中的提审逻辑
        scheduler_file = self.project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'scheduler.py'
        
        if not scheduler_file.exists():
            print("❌ scheduler.py文件不存在")
            return False
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已有保护逻辑
            if 'appeal_attempt_count > 0' in content:
                print("✅ 提审保护逻辑已存在")
                return True
            else:
                print("⚠️ 提审保护逻辑缺失，需要手动添加")
                self.results['recommendations'].append("在scheduler.py中添加更严格的提审保护逻辑")
                return False
                
        except Exception as e:
            print(f"❌ 检查提审保护逻辑失败: {e}")
            return False

    def create_appeal_monitoring_query(self):
        """创建提审监控查询"""
        print("📊 创建提审监控查询...")
        
        monitoring_queries = {
            'duplicate_appeals': """
                -- 查找重复提审的计划
                SELECT 
                    campaign_id_qc,
                    appeal_attempt_count,
                    appeal_status,
                    first_appeal_at,
                    created_at
                FROM campaigns 
                WHERE appeal_attempt_count > 1
                    AND created_at >= CURRENT_DATE - INTERVAL '2 days'
                ORDER BY appeal_attempt_count DESC, created_at DESC;
            """,
            
            'appeal_status_distribution': """
                -- 提审状态分布
                SELECT 
                    appeal_status,
                    COUNT(*) as count,
                    AVG(appeal_attempt_count) as avg_attempts
                FROM campaigns 
                WHERE created_at >= CURRENT_DATE - INTERVAL '2 days'
                GROUP BY appeal_status
                ORDER BY count DESC;
            """,
            
            'pending_appeals_too_long': """
                -- 长时间待提审的计划
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    EXTRACT(EPOCH FROM (NOW() - first_appeal_at))/3600 as hours_since_first_appeal
                FROM campaigns 
                WHERE appeal_status = 'appeal_pending'
                    AND first_appeal_at < NOW() - INTERVAL '2 hours'
                ORDER BY first_appeal_at ASC;
            """
        }
        
        # 保存监控查询到文件
        queries_file = self.project_root / 'ai_tools' / 'monitoring' / 'appeal_monitoring_queries.sql'
        queries_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(queries_file, 'w', encoding='utf-8') as f:
            f.write("-- 提审监控查询集合\n")
            f.write(f"-- 生成时间: {datetime.now().isoformat()}\n\n")
            
            for name, query in monitoring_queries.items():
                f.write(f"-- {name}\n")
                f.write(query.strip())
                f.write("\n\n")
        
        print(f"📄 监控查询已保存: {queries_file}")
        return queries_file

    def generate_recommendations(self):
        """生成修复建议"""
        print("💡 生成修复建议...")
        
        recommendations = [
            {
                'priority': 'CRITICAL',
                'category': '提审逻辑',
                'issue': '重复提审违反业务铁律',
                'solution': '在所有提审入口添加appeal_attempt_count检查'
            },
            {
                'priority': 'HIGH',
                'category': '浏览器管理',
                'issue': '浏览器实例过多导致性能问题',
                'solution': '使用浏览器实例管理器限制并发数量'
            },
            {
                'priority': 'MEDIUM',
                'category': '配置优化',
                'issue': '提审间隔和批处理参数不合理',
                'solution': '调整配置参数，减少系统压力'
            },
            {
                'priority': 'LOW',
                'category': '监控改进',
                'issue': '缺乏提审状态监控',
                'solution': '建立提审状态监控和告警机制'
            }
        ]
        
        self.results['recommendations'].extend(recommendations)
        
        print("📋 修复建议:")
        for rec in recommendations:
            print(f"  [{rec['priority']}] {rec['category']}: {rec['issue']}")
            print(f"      解决方案: {rec['solution']}")
        
        return recommendations

    def run_comprehensive_fix(self):
        """运行综合修复"""
        print("🚀 开始重复提审综合修复...")
        print("="*60)
        
        try:
            # 1. 分析重复提审问题
            duplicate_appeals = self.analyze_duplicate_appeals()
            print()
            
            # 2. 修复重复提审状态
            if duplicate_appeals:
                self.fix_duplicate_appeal_status()
                print()
            
            # 3. 检查保护逻辑
            self.add_appeal_protection_check()
            print()
            
            # 4. 创建监控查询
            self.create_appeal_monitoring_query()
            print()
            
            # 5. 生成建议
            self.generate_recommendations()
            print()
            
            # 6. 生成报告
            self.generate_report()
            
            print("="*60)
            print("📊 修复结果汇总:")
            print(f"  🔄 发现重复提审: {self.results['duplicate_appeals_found']} 个")
            print(f"  🔧 修复计划: {self.results['appeals_fixed']} 个")
            print(f"  💡 修复建议: {len(self.results['recommendations'])} 条")
            
            return self.results
            
        except Exception as e:
            print(f"❌ 修复过程发生错误: {e}")
            return None

    def generate_report(self):
        """生成修复报告"""
        report_path = self.project_root / 'ai_reports' / 'maintenance' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_duplicate_appeal_fix.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 修复报告已保存: {report_path}")

def main():
    """主函数"""
    fixer = DuplicateAppealFixer()
    
    print("🎯 千川自动化项目 - 重复提审修复工具")
    print("📌 目标: 确保每个计划只提审一次的业务铁律")
    print()
    
    results = fixer.run_comprehensive_fix()
    
    if results:
        print("\n🎉 修复完成！")
        print("✅ 重复提审问题已处理")
        print("✅ 提审保护逻辑已检查")
        print("✅ 监控查询已创建")
    else:
        print("❌ 修复失败！")

if __name__ == "__main__":
    main()
