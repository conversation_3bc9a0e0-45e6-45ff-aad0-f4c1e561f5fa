#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 逐步恢复工作流功能
清理条件: 工作流稳定运行后可删除

工作流逐步恢复工具
================

按照正确的顺序和频率逐步恢复千川自动化工作流功能。
"""

import os
import sys
import yaml
import time
import shutil

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class WorkflowRecoveryManager:
    """工作流恢复管理器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = os.path.join(project_root, 'config', 'settings.yml')
        self.backup_file = os.path.join(project_root, 'ai_temp', f'settings_backup_recovery_{int(time.time())}.yml')
        
    def backup_config(self):
        """备份当前配置"""
        try:
            os.makedirs(os.path.dirname(self.backup_file), exist_ok=True)
            shutil.copy2(self.config_file, self.backup_file)
            logger.info(f"✅ 配置文件已备份到: {self.backup_file}")
            return True
        except Exception as e:
            logger.error(f"❌ 备份配置文件失败: {e}")
            return False
    
    def check_material_directory(self):
        """检查素材目录状态"""
        logger.info("🔍 检查素材目录状态...")
        
        material_dir = os.path.join(project_root, 'workflow_assets', '01_materials_to_process', '缇萃百货')
        
        if not os.path.exists(material_dir):
            logger.warning(f"⚠️ 素材目录不存在: {material_dir}")
            return False, 0
        
        # 统计视频文件
        video_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv']
        video_files = []
        
        try:
            for file in os.listdir(material_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(file)
            
            logger.info(f"📊 发现 {len(video_files)} 个视频文件待处理")
            
            if video_files:
                logger.info("📋 前5个文件示例:")
                for i, file in enumerate(video_files[:5]):
                    logger.info(f"   {i+1}. {file}")
            
            return True, len(video_files)
            
        except Exception as e:
            logger.error(f"❌ 检查素材目录失败: {e}")
            return False, 0
    
    def enable_workflow_stage_1(self):
        """第一阶段：启用文件摄取（最安全）"""
        logger.info("🚀 第一阶段：启用文件摄取...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 启用文件摄取，使用较低频率
            config['workflow']['file_ingestion']['enabled'] = True
            config['workflow']['file_ingestion']['interval_seconds'] = 300  # 5分钟
            
            # 确保其他工作流仍然禁用
            config['workflow']['group_dispatch']['enabled'] = False
            config['workflow']['plan_creation']['enabled'] = False
            config['workflow']['plan_appeal']['enabled'] = False
            config['workflow']['material_monitoring']['enabled'] = False
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info("✅ 第一阶段配置已更新")
            logger.info("📋 启用的工作流:")
            logger.info("   - file_ingestion: 每5分钟执行一次")
            logger.info("📋 仍然禁用的工作流:")
            logger.info("   - group_dispatch, plan_creation, plan_appeal, material_monitoring")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 第一阶段配置失败: {e}")
            return False
    
    def enable_workflow_stage_2(self):
        """第二阶段：启用分组派发"""
        logger.info("🚀 第二阶段：启用分组派发...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 启用分组派发，使用较低频率
            config['workflow']['group_dispatch']['enabled'] = True
            config['workflow']['group_dispatch']['interval_seconds'] = 120  # 2分钟
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info("✅ 第二阶段配置已更新")
            logger.info("📋 新启用的工作流:")
            logger.info("   - group_dispatch: 每2分钟执行一次")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 第二阶段配置失败: {e}")
            return False
    
    def enable_workflow_stage_3(self):
        """第三阶段：启用计划创建"""
        logger.info("🚀 第三阶段：启用计划创建...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 启用计划创建，使用较低频率
            config['workflow']['plan_creation']['enabled'] = True
            config['workflow']['plan_creation']['interval_seconds'] = 300  # 5分钟（降低频率）
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info("✅ 第三阶段配置已更新")
            logger.info("📋 新启用的工作流:")
            logger.info("   - plan_creation: 每5分钟执行一次")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 第三阶段配置失败: {e}")
            return False
    
    def enable_workflow_stage_4(self):
        """第四阶段：启用监控和提审"""
        logger.info("🚀 第四阶段：启用监控和提审...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 启用素材监控和计划提审
            config['workflow']['material_monitoring']['enabled'] = True
            config['workflow']['material_monitoring']['interval_seconds'] = 600  # 10分钟
            
            config['workflow']['plan_appeal']['enabled'] = True
            config['workflow']['plan_appeal']['interval_seconds'] = 900  # 15分钟
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info("✅ 第四阶段配置已更新")
            logger.info("📋 新启用的工作流:")
            logger.info("   - material_monitoring: 每10分钟执行一次")
            logger.info("   - plan_appeal: 每15分钟执行一次")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 第四阶段配置失败: {e}")
            return False
    
    def show_current_status(self):
        """显示当前工作流状态"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            workflow_config = config.get('workflow', {})
            
            logger.info("📊 当前工作流状态:")
            logger.info("=" * 60)
            
            workflows = [
                ('file_ingestion', '文件摄取'),
                ('group_dispatch', '分组派发'),
                ('plan_creation', '计划创建'),
                ('plan_appeal', '计划提审'),
                ('material_monitoring', '素材监控'),
                ('zombie_cleanup', '僵尸清理'),
                ('violation_detection', '违规检测'),
                ('comment_management', '评论管理')
            ]
            
            for workflow_key, workflow_name in workflows:
                workflow_settings = workflow_config.get(workflow_key, {})
                enabled = workflow_settings.get('enabled', False)
                interval = workflow_settings.get('interval_seconds', 'N/A')
                
                status_icon = "🟢" if enabled else "🔴"
                logger.info(f"{status_icon} {workflow_name}: {'启用' if enabled else '禁用'} (间隔: {interval}s)")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 显示状态失败: {e}")
            return False
    
    def generate_recovery_report(self):
        """生成恢复报告"""
        report = f"""
工作流逐步恢复报告
================

恢复时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

恢复策略:
本次恢复采用分阶段策略，确保系统稳定性：

第一阶段 - 文件摄取（最安全）:
- 启用 file_ingestion，间隔5分钟
- 仅处理文件入库，不涉及网络操作
- 风险等级: 低

第二阶段 - 分组派发:
- 启用 group_dispatch，间隔2分钟
- 开始派发上传任务
- 风险等级: 中

第三阶段 - 计划创建:
- 启用 plan_creation，间隔5分钟（降低频率）
- 开始创建测试计划
- 风险等级: 中高

第四阶段 - 监控提审:
- 启用 material_monitoring，间隔10分钟
- 启用 plan_appeal，间隔15分钟
- 完整工作流恢复
- 风险等级: 高

使用说明:
1. 运行此脚本选择恢复阶段
2. 每个阶段运行后观察5-10分钟
3. 确认系统稳定后再进入下一阶段
4. 如有异常立即停止并回滚配置

监控要点:
- CPU使用率应保持在50%以下
- 浏览器进程数量应少于10个
- 日志中不应有大量错误信息
- 数据库操作应正常完成

回滚方法:
如需回滚，使用备份文件: {self.backup_file}
"""
        
        report_file = os.path.join(project_root, 'ai_temp', f'workflow_recovery_report_{int(time.time())}.txt')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 恢复报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚀 开始工作流逐步恢复...")
    
    manager = WorkflowRecoveryManager()
    
    try:
        # 1. 备份配置
        if not manager.backup_config():
            return 1
        
        # 2. 检查素材目录
        has_materials, material_count = manager.check_material_directory()
        if not has_materials:
            logger.warning("⚠️ 素材目录为空或不存在，工作流可能没有实际工作")
        
        # 3. 显示当前状态
        logger.info("📊 当前工作流状态:")
        manager.show_current_status()
        
        # 4. 交互式恢复
        print("\n" + "="*60)
        print("工作流逐步恢复选项:")
        print("1. 第一阶段 - 启用文件摄取（最安全）")
        print("2. 第二阶段 - 启用分组派发")
        print("3. 第三阶段 - 启用计划创建")
        print("4. 第四阶段 - 启用监控提审（完整恢复）")
        print("5. 显示当前状态")
        print("0. 退出")
        print("="*60)
        
        while True:
            choice = input("\n请选择恢复阶段 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                if manager.enable_workflow_stage_1():
                    logger.info("🎯 第一阶段完成！请观察5-10分钟确认系统稳定")
                    logger.info("💡 观察要点: 检查日志中的文件摄取活动")
            elif choice == '2':
                if manager.enable_workflow_stage_2():
                    logger.info("🎯 第二阶段完成！请观察5-10分钟确认上传任务正常")
                    logger.info("💡 观察要点: 检查upload_single_video任务执行情况")
            elif choice == '3':
                if manager.enable_workflow_stage_3():
                    logger.info("🎯 第三阶段完成！请观察10-15分钟确认计划创建正常")
                    logger.info("💡 观察要点: 检查create_plans任务和浏览器进程数量")
            elif choice == '4':
                if manager.enable_workflow_stage_4():
                    logger.info("🎯 第四阶段完成！工作流已完全恢复")
                    logger.info("💡 观察要点: 监控所有任务执行情况和系统资源使用")
            elif choice == '5':
                manager.show_current_status()
            else:
                print("无效选择，请重新输入")
        
        # 5. 生成报告
        report = manager.generate_recovery_report()
        
        logger.info("🎯 工作流恢复工具执行完成！")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 恢复过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
