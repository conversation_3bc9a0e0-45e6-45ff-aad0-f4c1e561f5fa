"""
千川申诉参数捕获器 - 基于录制代码
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 基于用户录制的精确代码实现参数捕获
依赖关系: Playwright浏览器自动化，项目cookies系统
清理条件: 功能被官方API替代时可删除

基于录制的精确选择器：
- 智投星图标: #copilot-sdk-ark div (第4个)
- 输入框: get_by_placeholder("请描述你的问题")
- 计划/商品申诉: get_by_text("计划/商品申诉")
- 问题类型: get_by_text("计划审核不通过/结果申诉")
- 计划ID输入: get_by_placeholder("请输入计划ID，支持托管计划")
- 提交按钮: get_by_role("button", name="提交")
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanRecordedCapture:
    """千川申诉参数捕获器 - 基于录制代码"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的callback请求
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_callback_capture(self, page: Page):
        """设置callback请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                if 'copilot/api/v1/agw/card/callback' in url and request.method == 'POST':
                    logger.success(f"🎯 捕获到目标callback请求: {url}")
                    
                    try:
                        post_data = request.post_data
                        if post_data:
                            data = json.loads(post_data)
                            
                            # 记录完整的请求信息
                            callback_info = {
                                'url': url,
                                'method': request.method,
                                'data': data,
                                'headers': dict(request.headers),
                                'timestamp': time.time()
                            }
                            
                            self.callback_requests.append(callback_info)
                            logger.success(f"✅ 成功捕获callback参数")
                            
                            # 显示关键参数
                            logger.info(f"📋 sessionId: {data.get('sessionId', 'N/A')}")
                            logger.info(f"📋 windowId: {data.get('windowId', 'N/A')[:20]}...")
                            logger.info(f"📋 messageId: {data.get('messageId', 'N/A')}")
                            logger.info(f"📋 callBackCode: {data.get('callBackCode', 'N/A')}")
                            
                    except Exception as e:
                        logger.warning(f"解析callback数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        page.on("request", handle_request)
    
    def capture_with_recorded_flow(self, advertiser_id: str, plan_id: str) -> Dict[str, Any]:
        """使用录制的精确流程捕获参数"""
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 使用录制的精确流程捕获参数...")
            logger.info(f"📋 广告户ID: {advertiser_id}")
            logger.info(f"📋 计划ID: {plan_id}")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # 设置callback捕获
            self._setup_callback_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url)
            
            # 等待页面加载
            page.wait_for_timeout(3000)
            logger.info("✅ 页面加载完成")
            
            # 执行录制的精确流程
            success = self._execute_recorded_flow(page, plan_id)
            
            if not success:
                return {"success": False, "error": "录制流程执行失败"}
            
            # 等待callback请求
            logger.info("⏳ 等待callback请求...")
            page.wait_for_timeout(10000)
            
            # 检查捕获结果
            if self.callback_requests:
                logger.success(f"🎉 成功捕获 {len(self.callback_requests)} 个callback请求")
                return self._extract_captured_params()
            else:
                logger.warning("⚠️ 未捕获到callback请求")
                return {"success": False, "error": "未捕获到callback请求"}
            
        except Exception as e:
            logger.error(f"❌ 参数捕获失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 保持浏览器打开供查看
            try:
                logger.info("🔍 捕获完成，浏览器将保持打开状态供您查看")
                logger.info("💡 按 Ctrl+C 关闭浏览器")
                
                while True:
                    time.sleep(10)
                    
            except KeyboardInterrupt:
                logger.info("👋 用户中断，正在关闭浏览器...")
                
            finally:
                try:
                    if context:
                        context.close()
                    if browser:
                        browser.close()
                    if playwright:
                        playwright.stop()
                except:
                    pass
    
    def _execute_recorded_flow(self, page: Page, plan_id: str) -> bool:
        """执行录制的精确流程"""
        try:
            logger.info("🎯 开始执行录制的精确流程...")
            
            # 步骤1: 点击智投星图标 (基于录制代码)
            logger.info("🔍 步骤1: 点击智投星图标...")
            try:
                # 使用录制的精确选择器
                copilot_element = page.locator("#copilot-sdk-ark div").nth(3)
                copilot_element.click(timeout=10000)
                logger.info("✅ 智投星图标点击成功")
                page.wait_for_timeout(3000)
            except Exception as e:
                logger.error(f"❌ 点击智投星图标失败: {e}")
                return False
            
            # 步骤2: 输入文字指令 (基于录制代码)
            logger.info("🔍 步骤2: 输入文字指令...")
            try:
                input_element = page.get_by_placeholder("请描述你的问题")
                input_element.click()
                input_element.fill("自助申诉表单")
                logger.info("✅ 文字指令输入成功")
                page.wait_for_timeout(2000)
                
                # 发送指令 (按回车或点击发送)
                input_element.press("Enter")
                logger.info("✅ 文字指令发送成功")
                page.wait_for_timeout(5000)  # 等待智投星回复
            except Exception as e:
                logger.error(f"❌ 输入文字指令失败: {e}")
                return False
            
            # 步骤3: 点击"计划/商品申诉" (基于录制代码，处理多个元素问题)
            logger.info("🔍 步骤3: 点击'计划/商品申诉'...")
            try:
                # 由于有多个相同文本的元素，使用first()选择第一个可见的
                plan_appeal_element = page.get_by_text("计划/商品申诉").first
                plan_appeal_element.click(timeout=10000)
                logger.info("✅ 计划/商品申诉点击成功")
                page.wait_for_timeout(3000)
            except Exception as e:
                logger.error(f"❌ 点击计划/商品申诉失败: {e}")
                # 尝试备用方案：点击最后一个（最新的回复）
                try:
                    logger.info("🔄 尝试备用方案：点击最后一个元素...")
                    plan_appeal_element = page.get_by_text("计划/商品申诉").last
                    plan_appeal_element.click(timeout=10000)
                    logger.info("✅ 计划/商品申诉点击成功（备用方案）")
                    page.wait_for_timeout(3000)
                except Exception as e2:
                    logger.error(f"❌ 备用方案也失败: {e2}")
                    return False
            
            # 步骤4: 选择问题类型 (基于录制代码，处理多个下拉框问题)
            logger.info("🔍 步骤4: 选择问题类型...")
            try:
                # 由于有多个相同的下拉框，尝试不同的策略
                logger.info("🔄 尝试点击问题类型下拉框...")

                # 策略1: 使用last()选择最后一个（最新的）下拉框 - 保持一致性
                try:
                    dropdown_element = page.get_by_placeholder("请选择", exact=True).last
                    dropdown_element.click(timeout=5000)
                    logger.info("✅ 问题类型下拉框点击成功（策略1 - last，保持一致性）")
                    page.wait_for_timeout(1000)
                except Exception as e1:
                    logger.warning(f"策略1失败: {e1}")

                    # 策略2: 使用first()选择第一个下拉框
                    try:
                        dropdown_element = page.get_by_placeholder("请选择", exact=True).first
                        dropdown_element.click(timeout=5000)
                        logger.info("✅ 问题类型下拉框点击成功（策略2 - first）")
                        page.wait_for_timeout(1000)
                    except Exception as e2:
                        logger.warning(f"策略2失败: {e2}")

                        # 策略3: 直接点击选项，不点击下拉框
                        logger.info("🔄 尝试直接点击选项...")

                # 选择具体选项："计划审核不通过/结果申诉" - 也使用last保持一致性
                try:
                    option_element = page.get_by_text("计划审核不通过/结果申诉").last
                    option_element.click(timeout=10000)
                    logger.info("✅ 问题类型选择成功（使用last保持一致性）")
                    page.wait_for_timeout(2000)
                except Exception as e3:
                    logger.error(f"❌ 选择问题类型选项失败: {e3}")
                    # 如果自动选择失败，提供手动指导
                    logger.warning("⚠️ 自动选择失败，需要手动操作")
                    print("\n" + "="*50)
                    print("🔍 请手动选择问题类型")
                    print("在下拉框中点击：'计划审核不通过/结果申诉'")
                    print("="*50)
                    input("请手动选择问题类型，然后按回车继续...")

            except Exception as e:
                logger.error(f"❌ 选择问题类型失败: {e}")
                # 提供手动指导
                print("\n" + "="*50)
                print("🔍 请手动选择问题类型")
                print("在问题类型下拉框中选择：'计划审核不通过/结果申诉'")
                print("="*50)
                input("请手动选择问题类型，然后按回车继续...")
            
            # 步骤5: 输入计划ID (基于录制代码，使用last保持一致性)
            logger.info("🔍 步骤5: 输入计划ID...")
            try:
                # 使用last()选择最后一个（最新的）计划ID输入框，保持一致性
                plan_id_element = page.get_by_placeholder("请输入计划ID，支持托管计划").last
                plan_id_element.click()
                plan_id_element.fill(plan_id)
                logger.info(f"✅ 计划ID输入成功: {plan_id}（使用last保持一致性）")
                page.wait_for_timeout(1000)
            except Exception as e:
                logger.error(f"❌ 输入计划ID失败: {e}")
                # 尝试备用方案
                try:
                    plan_id_element = page.get_by_placeholder("请输入计划ID，支持托管计划").first
                    plan_id_element.click()
                    plan_id_element.fill(plan_id)
                    logger.info(f"✅ 计划ID输入成功: {plan_id}（备用方案 - first）")
                    page.wait_for_timeout(1000)
                except Exception as e2:
                    logger.error(f"❌ 备用方案也失败: {e2}")
                    return False
            
            # 步骤6: 点击提交按钮 (基于录制代码，处理多个提交按钮问题)
            logger.info("🔍 步骤6: 点击提交按钮...")
            try:
                # 由于有多个提交按钮，尝试不同的策略
                logger.info("🔄 尝试点击提交按钮...")

                # 策略1: 使用last()选择最后一个（最新的）提交按钮
                try:
                    submit_element = page.get_by_role("button", name="提交").last
                    submit_element.click(timeout=10000)
                    logger.info("✅ 提交按钮点击成功（策略1 - last）")
                    page.wait_for_timeout(3000)
                except Exception as e1:
                    logger.warning(f"策略1失败: {e1}")

                    # 策略2: 使用first()选择第一个提交按钮
                    try:
                        submit_element = page.get_by_role("button", name="提交").first
                        submit_element.click(timeout=10000)
                        logger.info("✅ 提交按钮点击成功（策略2 - first）")
                        page.wait_for_timeout(3000)
                    except Exception as e2:
                        logger.warning(f"策略2失败: {e2}")

                        # 策略3: 手动指导
                        logger.warning("⚠️ 自动点击失败，需要手动操作")
                        print("\n" + "="*50)
                        print("🔍 请手动点击提交按钮")
                        print("找到申诉表单中的'提交'按钮并点击")
                        print("="*50)
                        input("请手动点击提交按钮，然后按回车继续...")
                        page.wait_for_timeout(3000)

            except Exception as e:
                logger.error(f"❌ 点击提交按钮失败: {e}")
                # 提供手动指导
                print("\n" + "="*50)
                print("🔍 请手动点击提交按钮")
                print("在申诉表单底部找到'提交'按钮并点击")
                print("="*50)
                input("请手动点击提交按钮，然后按回车继续...")
                page.wait_for_timeout(3000)
            
            logger.success("🎉 录制流程执行完成！")
            return True
            
        except Exception as e:
            logger.error(f"执行录制流程失败: {e}")
            return False
    
    def _extract_captured_params(self) -> Dict[str, Any]:
        """提取捕获的参数"""
        try:
            if not self.callback_requests:
                return {"success": False, "error": "没有捕获到callback请求"}
            
            # 使用最新的请求
            latest_request = self.callback_requests[-1]
            data = latest_request['data']
            
            # 提取关键参数
            params = {
                "success": True,
                "sessionId": data.get('sessionId'),
                "windowId": data.get('windowId'),
                "messageId": data.get('messageId'),
                "callValue": data.get('callValue'),
                "applicationCode": data.get('applicationCode', 'QC'),
                "callBackCode": data.get('callBackCode'),
                "url": latest_request['url'],
                "headers": latest_request['headers'],
                "captured_at": latest_request['timestamp'],
                "total_requests": len(self.callback_requests),
                "raw_data": data
            }
            
            # 保存参数到文件
            self._save_captured_params(params)
            
            logger.success(f"✅ 成功提取callback参数")
            return params
            
        except Exception as e:
            logger.error(f"提取参数失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _save_captured_params(self, params: Dict[str, Any]):
        """保存捕获的参数到文件"""
        try:
            timestamp = int(time.time())
            filename = f"ai_temp/recorded_callback_params_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(params, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"💾 参数已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存参数失败: {e}")


def capture_with_recorded_flow(advertiser_id: str, plan_id: str, principal_name: str = "缇萃百货") -> Dict[str, Any]:
    """
    便捷函数：使用录制的精确流程捕获参数
    
    Args:
        advertiser_id: 广告户ID
        plan_id: 计划ID
        principal_name: 主体名称
        
    Returns:
        捕获结果字典
    """
    capture = QianchuanRecordedCapture(principal_name)
    return capture.capture_with_recorded_flow(advertiser_id, plan_id)


if __name__ == "__main__":
    # 使用录制的精确流程进行参数捕获
    test_advertiser_id = "1836333804939273"
    test_plan_id = "1838840072680523"
    
    print("🎬 千川申诉参数捕获器 - 基于录制代码")
    print("=" * 60)
    print("基于用户录制的精确选择器：")
    print("✅ 智投星图标: #copilot-sdk-ark div (第4个)")
    print("✅ 输入框: get_by_placeholder('请描述你的问题')")
    print("✅ 计划/商品申诉: get_by_text('计划/商品申诉')")
    print("✅ 问题类型: get_by_text('计划审核不通过/结果申诉')")
    print("✅ 计划ID输入: get_by_placeholder('请输入计划ID，支持托管计划')")
    print("✅ 提交按钮: get_by_role('button', name='提交')")
    print()
    
    try:
        result = capture_with_recorded_flow(test_advertiser_id, test_plan_id)
        
        if result.get('success'):
            print("🎉 参数捕获成功！")
            print("现在您可以使用这些真实参数进行API调用")
        else:
            print(f"❌ 参数捕获失败: {result.get('error')}")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断操作")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_recorded_capture import capture_with_recorded_flow")
    print("result = capture_with_recorded_flow('广告户ID', '计划ID')")
