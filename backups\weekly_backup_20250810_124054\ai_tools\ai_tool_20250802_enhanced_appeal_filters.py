#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的提审过滤逻辑 - 防止重复提审
"""

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_
from src.qianchuan_aw.database.models import Campaign, AdAccount
from src.qianchuan_aw.utils.logger import logger
from datetime import datetime, timezone, timedelta
from typing import List, <PERSON><PERSON>


def get_plans_ready_for_first_appeal(db: Session) -> List[Campaign]:
    """获取准备进行首次提审的计划 - 增强版防重复逻辑"""
    
    plans = db.query(Campaign).options(
        joinedload(Campaign.account).joinedload(AdAccount.principal)
    ).filter(
        # 基础条件：计划状态为AUDITING
        Campaign.status == 'AUDITING',
        
        # 核心防重复条件：确保从未成功提审过
        and_(
            # 条件1：appeal_status必须为空（从未成功提审）
            Campaign.appeal_status.is_(None),
            
            # 条件2：first_appeal_at必须为空（从未开始提审）
            Campaign.first_appeal_at.is_(None),
            
            # 条件3：appeal_attempt_count必须为0或空（从未尝试提审）
            or_(
                Campaign.appeal_attempt_count.is_(None), 
                Campaign.appeal_attempt_count == 0
            )
        )
    ).all()
    
    logger.info(f"🔍 增强过滤后找到 {len(plans)} 个符合首次提审条件的计划")
    
    # 额外的安全检查
    safe_plans = []
    for plan in plans:
        # 双重检查：确保没有任何提审历史
        if (plan.appeal_status is None and 
            plan.first_appeal_at is None and 
            (plan.appeal_attempt_count is None or plan.appeal_attempt_count == 0)):
            safe_plans.append(plan)
        else:
            logger.warning(f"⚠️ 计划 {plan.campaign_id_qc} 未通过安全检查，跳过提审")
    
    logger.success(f"✅ 安全检查后确认 {len(safe_plans)} 个计划可以进行首次提审")
    return safe_plans

def get_plans_ready_for_retry_appeal(db: Session, retry_interval_minutes: int = 5) -> List[Campaign]:
    """获取准备进行重试提审的计划 - 仅限失败的计划"""
    
    now = datetime.now(timezone.utc)
    retry_cutoff = now - timedelta(minutes=retry_interval_minutes)
    
    plans = db.query(Campaign).options(
        joinedload(Campaign.account).joinedload(AdAccount.principal)
    ).filter(
        # 基础条件：计划状态为AUDITING
        Campaign.status == 'AUDITING',
        
        # 重试条件：只有失败的计划才能重试
        Campaign.appeal_status == 'submission_failed',
        
        # 时间条件：距离上次失败已超过重试间隔
        Campaign.last_appeal_at < retry_cutoff,
        
        # 限制条件：重试次数不超过3次
        Campaign.appeal_attempt_count < 3
    ).all()
    
    logger.info(f"🔄 找到 {len(plans)} 个符合重试条件的失败计划")
    return plans

def should_skip_plan_for_appeal(plan: Campaign) -> Tuple[bool, str]:
    """检查计划是否应该跳过提审 - 防重复核心逻辑"""
    
    # 检查1：已经成功提审的计划
    if plan.appeal_status == 'appeal_pending':
        return True, f"计划已成功提审，状态为appeal_pending，跳过重复提审"
    
    # 检查2：已经有提审历史的计划
    if plan.first_appeal_at is not None:
        return True, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，跳过重复提审"
    
    # 检查3：尝试次数过多的计划
    if plan.appeal_attempt_count and plan.appeal_attempt_count >= 3:
        return True, f"计划提审尝试次数过多 ({plan.appeal_attempt_count}次)，跳过继续尝试"
    
    # 检查4：状态不符合的计划
    if plan.status != 'AUDITING':
        return True, f"计划状态不是AUDITING ({plan.status})，不需要提审"
    
    return False, "计划符合提审条件"
