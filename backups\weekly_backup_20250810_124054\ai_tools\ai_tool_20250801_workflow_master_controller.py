"""
千川自动化工作流主控制器
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 主控制器，负责整个工作流的监控、恢复和调度，确保系统长期稳定运行
清理条件: 永不删除，核心系统组件
"""

import os
import sys
import json
import time
import signal
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.config_manager import get_settings


class WorkflowMasterController:
    """工作流主控制器"""
    
    def __init__(self):
        self.app_settings = get_settings()
        self.running = False
        self.status_file = "ai_temp/workflow_master_status.json"
        self.history_file = "ai_temp/workflow_recovery_history.json"
        self.check_interval = 300  # 5分钟检查一次
        self.recovery_history = []
        
        # 确保目录存在
        os.makedirs("ai_temp", exist_ok=True)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        logger.info(f"收到信号 {signum}，正在优雅关闭...")
        self.stop()
    
    def start(self):
        """启动主控制器"""
        logger.info("🚀 启动千川自动化工作流主控制器...")
        
        self.running = True
        self._save_status({
            'status': 'running',
            'started_at': datetime.now().isoformat(),
            'pid': os.getpid(),
            'check_interval': self.check_interval
        })
        
        # 立即执行一次检查
        self._perform_system_check()
        
        # 启动监控循环
        self._monitoring_loop()
    
    def stop(self):
        """停止主控制器"""
        logger.info("🛑 停止工作流主控制器...")
        
        self.running = False
        self._save_status({
            'status': 'stopped',
            'stopped_at': datetime.now().isoformat(),
            'pid': None
        })
        
        logger.info("✅ 主控制器已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        logger.info(f"⏰ 开始监控循环，每 {self.check_interval} 秒检查一次...")
        
        while self.running:
            try:
                time.sleep(self.check_interval)
                
                if self.running:  # 再次检查，防止在sleep期间被停止
                    self._perform_system_check()
                    
            except Exception as e:
                logger.error(f"监控循环发生错误: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def _perform_system_check(self):
        """执行系统检查"""
        logger.info("🔍 执行系统状态检查...")
        
        try:
            # 导入恢复系统组件
            from ai_tool_20250801_workflow_resilience_system import WorkflowResilienceSystem
            from ai_tool_20250801_workflow_recovery_executor import WorkflowRecoveryExecutor
            
            # 创建系统检查点
            recovery_system = WorkflowResilienceSystem(self.app_settings)
            checkpoints = recovery_system.create_system_checkpoint()
            
            # 计算整体完成率
            total_completion = sum(cp.completion_rate for cp in checkpoints.values()) / len(checkpoints)
            
            # 生成恢复计划
            recovery_plan = recovery_system.generate_recovery_plan(checkpoints)
            
            check_result = {
                'timestamp': datetime.now().isoformat(),
                'overall_completion': total_completion,
                'checkpoints': {
                    stage: {
                        'completion_rate': cp.completion_rate,
                        'total_items': cp.total_items,
                        'completed_items': cp.completed_items,
                        'pending_items': cp.pending_items,
                        'failed_items': cp.failed_items,
                        'issues': cp.issues
                    }
                    for stage, cp in checkpoints.items()
                },
                'recovery_actions_needed': len(recovery_plan)
            }
            
            # 保存检查结果
            self._save_status({
                'status': 'running',
                'last_check': check_result,
                'pid': os.getpid()
            })
            
            # 如果需要恢复动作，执行恢复
            if recovery_plan:
                logger.warning(f"发现 {len(recovery_plan)} 个恢复动作需要执行")
                self._execute_recovery(recovery_plan, checkpoints)
            else:
                logger.success(f"✅ 系统状态良好，整体完成率: {total_completion:.1f}%")
                
        except Exception as e:
            logger.error(f"系统检查失败: {e}")
    
    def _execute_recovery(self, recovery_plan: List[Dict[str, Any]], checkpoints: Dict):
        """执行恢复动作"""
        logger.info("🔧 开始执行恢复动作...")
        
        try:
            from ai_tool_20250801_workflow_recovery_executor import WorkflowRecoveryExecutor
            
            executor = WorkflowRecoveryExecutor(self.app_settings)
            results = executor.execute_recovery_plan(recovery_plan)
            
            # 记录恢复历史
            recovery_record = {
                'timestamp': datetime.now().isoformat(),
                'recovery_plan': recovery_plan,
                'results': results,
                'checkpoints_before': {
                    stage: {
                        'completion_rate': cp.completion_rate,
                        'pending_items': cp.pending_items,
                        'issues': cp.issues
                    }
                    for stage, cp in checkpoints.items()
                }
            }
            
            self.recovery_history.append(recovery_record)
            self._save_recovery_history()
            
            if results['successful_actions'] > 0:
                logger.success(f"✅ 恢复执行完成: 成功 {results['successful_actions']}, 失败 {results['failed_actions']}")
            else:
                logger.error(f"❌ 恢复执行失败: 所有 {results['failed_actions']} 个动作都失败了")
                
        except Exception as e:
            logger.error(f"执行恢复动作失败: {e}")
    
    def _save_status(self, status_data: Dict[str, Any]):
        """保存状态"""
        try:
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存状态失败: {e}")
    
    def _save_recovery_history(self):
        """保存恢复历史"""
        try:
            # 只保留最近100条记录
            if len(self.recovery_history) > 100:
                self.recovery_history = self.recovery_history[-100:]
            
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recovery_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存恢复历史失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取当前状态"""
        try:
            if os.path.exists(self.status_file):
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"读取状态失败: {e}")
        
        return {'status': 'unknown'}
    
    def get_recovery_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取恢复历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    return history[-limit:] if limit else history
        except Exception as e:
            logger.error(f"读取恢复历史失败: {e}")
        
        return []
    
    def print_status_report(self):
        """打印状态报告"""
        status = self.get_status()
        history = self.get_recovery_history(5)
        
        logger.info("\n" + "="*80)
        logger.info("📊 千川自动化工作流主控制器状态报告")
        logger.info("="*80)
        
        logger.info(f"🔧 控制器状态: {status.get('status', 'unknown')}")
        logger.info(f"🆔 进程ID: {status.get('pid', 'N/A')}")
        
        if 'started_at' in status:
            logger.info(f"⏰ 启动时间: {status['started_at']}")
        
        if 'last_check' in status:
            last_check = status['last_check']
            logger.info(f"🔍 最后检查: {last_check['timestamp']}")
            logger.info(f"📈 整体完成率: {last_check['overall_completion']:.1f}%")
            logger.info(f"🔧 待恢复动作: {last_check['recovery_actions_needed']}")
        
        if history:
            logger.info(f"\n📜 最近 {len(history)} 次恢复记录:")
            for i, record in enumerate(history, 1):
                results = record['results']
                logger.info(f"  {i}. {record['timestamp']}: 成功 {results['successful_actions']}, 失败 {results['failed_actions']}")
        
        logger.info("="*80)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川自动化工作流主控制器')
    parser.add_argument('--action', choices=['start', 'stop', 'status', 'history'], 
                       default='start', help='执行的动作')
    parser.add_argument('--daemon', action='store_true', help='以守护进程模式运行')
    
    args = parser.parse_args()
    
    controller = WorkflowMasterController()
    
    if args.action == 'start':
        if args.daemon:
            logger.info("以守护进程模式启动...")
            # 这里可以添加守护进程逻辑
        controller.start()
    elif args.action == 'stop':
        controller.stop()
    elif args.action == 'status':
        controller.print_status_report()
    elif args.action == 'history':
        history = controller.get_recovery_history(20)
        logger.info(f"恢复历史记录 (最近20条):")
        for record in history:
            logger.info(f"{record['timestamp']}: {len(record['recovery_plan'])} 个动作")


if __name__ == "__main__":
    main()
