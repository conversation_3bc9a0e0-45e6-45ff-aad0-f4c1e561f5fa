#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目多线程调度性能优化
清理条件: 优化完成并验证稳定后可归档
"""

import os
import sys
import yaml
import shutil
import time
import psutil
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class ConcurrencyPerformanceOptimizer:
    """千川自动化项目并发性能优化器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = self.project_root / 'config' / 'settings.yml'
        self.worker_file = self.project_root / 'run_celery_worker.py'
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"concurrency_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 优化目标配置
        self.optimization_targets = {
            'api_rate_limits': {
                'global_rate': 30,  # 从5提升到30（千川官方限制的60%）
                'global_capacity': 50,  # 从20提升到50
                'create_campaign_rate': 8,  # 从2提升到8
                'upload_video_rate': 12,  # 从3提升到12
                'get_materials_rate': 5,  # 从1提升到5
            },
            'celery_concurrency': {
                'worker_threads': 12,  # 从5提升到12
                'max_tasks_per_child': 50,  # 添加任务限制
                'prefetch_multiplier': 2,  # 优化预取
            },
            'task_intervals': {
                'plan_creation': 60,  # 从120缩短到60秒
                'independent_harvest': 300,  # 从600缩短到300秒
                'material_monitoring': 180,  # 从300缩短到180秒
                'group_dispatch': 30,  # 从60缩短到30秒
            },
            'database_pool': {
                'pool_size': 20,  # 增加连接池大小
                'max_overflow': 30,  # 增加溢出连接
                'pool_timeout': 20,  # 减少获取连接超时
                'pool_recycle': 1800,  # 30分钟回收连接
            }
        }
    
    def run_comprehensive_optimization(self):
        """执行综合性能优化"""
        logger.critical("🚀 开始千川自动化项目并发性能优化")
        logger.critical("=" * 80)
        
        try:
            # 创建备份
            self._create_backups()
            
            # 系统资源评估
            self._assess_system_resources()
            
            # 按优先级执行优化
            logger.info("📋 执行优化方案（按优先级）:")
            
            # 高优先级优化
            self._optimize_api_rate_limits()  # 优先级1
            self._optimize_celery_concurrency()  # 优先级2
            
            # 中优先级优化
            self._optimize_database_pool()  # 优先级3
            self._optimize_task_scheduling()  # 优先级4
            
            # 验证优化效果
            self._verify_optimizations()
            
            logger.critical("✅ 并发性能优化完成！")
            logger.critical("🎯 预期效果：任务执行间隔从7.3分钟降低到<2分钟")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 优化过程中发生错误: {e}")
            return False
    
    def _create_backups(self):
        """创建配置备份"""
        logger.info("💾 创建配置文件备份...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份关键文件
        files_to_backup = [
            self.config_file,
            self.worker_file,
            self.project_root / 'run_celery_beat.py',
            self.project_root / 'src' / 'qianchuan_aw' / 'celery_app.py'
        ]
        
        for file_path in files_to_backup:
            if file_path.exists():
                backup_path = self.backup_dir / file_path.name
                shutil.copy2(file_path, backup_path)
                logger.info(f"✅ 已备份: {file_path.name}")
    
    def _assess_system_resources(self):
        """评估系统资源"""
        logger.info("🔍 评估系统资源...")
        
        # CPU信息
        cpu_count = psutil.cpu_count()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        memory_percent = memory.percent
        
        logger.info(f"💻 系统资源状况:")
        logger.info(f"  - CPU核心数: {cpu_count}")
        logger.info(f"  - CPU使用率: {cpu_percent}%")
        logger.info(f"  - 内存总量: {memory_gb:.1f}GB")
        logger.info(f"  - 内存使用率: {memory_percent}%")
        
        # 根据系统资源调整优化目标
        if cpu_count >= 8 and memory_gb >= 16:
            logger.info("✅ 系统资源充足，可以使用激进优化策略")
            self.optimization_targets['celery_concurrency']['worker_threads'] = 15
        elif cpu_count >= 4 and memory_gb >= 8:
            logger.info("✅ 系统资源适中，使用标准优化策略")
            # 保持默认配置
        else:
            logger.warning("⚠️ 系统资源有限，使用保守优化策略")
            self.optimization_targets['celery_concurrency']['worker_threads'] = 8
            self.optimization_targets['api_rate_limits']['global_rate'] = 20
    
    def _optimize_api_rate_limits(self):
        """优化API频率限制配置（优先级1）"""
        logger.info("🔧 优化API频率限制配置...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取当前配置
            current_rate_limiting = config.get('rate_limiting', {})
            current_global_rate = current_rate_limiting.get('global', {}).get('rate', 5)
            
            # 应用优化配置
            targets = self.optimization_targets['api_rate_limits']
            
            config['rate_limiting'] = {
                'enabled': True,
                'global': {
                    'enabled': True,
                    'rate': targets['global_rate'],
                    'capacity': targets['global_capacity']
                },
                'endpoints': {
                    'create_campaign': {
                        'enabled': True,
                        'rate': targets['create_campaign_rate'],
                        'capacity': targets['create_campaign_rate'] * 5
                    },
                    'upload_video': {
                        'enabled': True,
                        'rate': targets['upload_video_rate'],
                        'capacity': targets['upload_video_rate'] * 5
                    },
                    'get_materials_in_ad': {
                        'enabled': True,
                        'rate': targets['get_materials_rate'],
                        'capacity': targets['get_materials_rate'] * 3
                    },
                    'security_score_disposal_info': {
                        'enabled': True,
                        'rate': 2,  # 保持保守，避免触发安全限制
                        'capacity': 6
                    }
                },
                'error_handling': {
                    'backoff_factor': 1.5,  # 减少退避因子，更快恢复
                    'min_rate': 2,  # 提高最小速率
                    'recovery_time': 300  # 减少恢复时间到5分钟
                }
            }
            
            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success(f"✅ API频率限制已优化:")
            logger.info(f"  - 全局速率: {current_global_rate}/秒 → {targets['global_rate']}/秒 (+{targets['global_rate']-current_global_rate})")
            logger.info(f"  - 计划创建: 2/秒 → {targets['create_campaign_rate']}/秒 (+{targets['create_campaign_rate']-2})")
            logger.info(f"  - 视频上传: 3/秒 → {targets['upload_video_rate']}/秒 (+{targets['upload_video_rate']-3})")
            logger.info(f"  - 素材查询: 1/秒 → {targets['get_materials_rate']}/秒 (+{targets['get_materials_rate']-1})")
            
        except Exception as e:
            logger.error(f"❌ 优化API频率限制失败: {e}")
    
    def _optimize_celery_concurrency(self):
        """优化Celery并发配置（优先级2）"""
        logger.info("🔧 优化Celery并发配置...")
        
        try:
            # 修改worker配置
            with open(self.worker_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 获取当前并发数
            current_threads = 5
            target_threads = self.optimization_targets['celery_concurrency']['worker_threads']
            
            # 替换并发数配置
            old_config = "'-c', '5'"
            new_config = f"'-c', '{target_threads}'"
            
            if old_config in content:
                content = content.replace(old_config, new_config)
                
                # 添加任务限制配置
                if "'--max-tasks-per-child'" not in content:
                    max_tasks = self.optimization_targets['celery_concurrency']['max_tasks_per_child']
                    insert_pos = content.find(new_config)
                    if insert_pos != -1:
                        next_line_pos = content.find('\n', insert_pos)
                        if next_line_pos != -1:
                            new_line = f"        '--max-tasks-per-child', '{max_tasks}',  # 限制每个进程任务数\n"
                            content = content[:next_line_pos] + '\n' + new_line + content[next_line_pos+1:]
                
                with open(self.worker_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.success(f"✅ Celery并发配置已优化:")
                logger.info(f"  - Worker线程数: {current_threads} → {target_threads} (+{target_threads-current_threads})")
                logger.info(f"  - 每进程任务限制: {self.optimization_targets['celery_concurrency']['max_tasks_per_child']}")
            else:
                logger.warning("⚠️ 未找到预期的并发配置，可能已被修改")
                
        except Exception as e:
            logger.error(f"❌ 优化Celery并发配置失败: {e}")
    
    def _optimize_database_pool(self):
        """优化数据库连接池配置（优先级3）"""
        logger.info("🔧 优化数据库连接池配置...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 添加数据库连接池优化配置
            if 'database' not in config:
                config['database'] = {}
            
            targets = self.optimization_targets['database_pool']
            
            config['database']['connection_pool'] = {
                'pool_size': targets['pool_size'],
                'max_overflow': targets['max_overflow'],
                'pool_timeout': targets['pool_timeout'],
                'pool_recycle': targets['pool_recycle'],
                'pool_pre_ping': True,
                'echo': False
            }
            
            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success("✅ 数据库连接池配置已优化:")
            logger.info(f"  - 连接池大小: {targets['pool_size']}")
            logger.info(f"  - 最大溢出: {targets['max_overflow']}")
            logger.info(f"  - 连接超时: {targets['pool_timeout']}秒")
            logger.info(f"  - 连接回收: {targets['pool_recycle']}秒")
            
        except Exception as e:
            logger.error(f"❌ 优化数据库连接池失败: {e}")
    
    def _optimize_task_scheduling(self):
        """优化任务调度策略（优先级4）"""
        logger.info("🔧 优化任务调度策略...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取当前工作流配置
            workflow = config.get('workflow', {})
            targets = self.optimization_targets['task_intervals']
            
            # 优化各任务间隔
            optimizations = []
            
            # 计划创建间隔
            if 'plan_creation' not in workflow:
                workflow['plan_creation'] = {}
            old_interval = workflow['plan_creation'].get('interval_seconds', 120)
            workflow['plan_creation']['interval_seconds'] = targets['plan_creation']
            optimizations.append(f"计划创建: {old_interval}秒 → {targets['plan_creation']}秒")
            
            # 独立收割间隔
            if 'independent_harvest' in workflow:
                old_interval = workflow['independent_harvest'].get('interval_seconds', 600)
                workflow['independent_harvest']['interval_seconds'] = targets['independent_harvest']
                optimizations.append(f"独立收割: {old_interval}秒 → {targets['independent_harvest']}秒")
            
            # 素材监控间隔
            if 'material_monitoring' not in workflow:
                workflow['material_monitoring'] = {}
            old_interval = workflow['material_monitoring'].get('interval_seconds', 300)
            workflow['material_monitoring']['interval_seconds'] = targets['material_monitoring']
            optimizations.append(f"素材监控: {old_interval}秒 → {targets['material_monitoring']}秒")
            
            # 组分发间隔
            if 'group_dispatch' in workflow:
                old_interval = workflow['group_dispatch'].get('interval_seconds', 60)
                workflow['group_dispatch']['interval_seconds'] = targets['group_dispatch']
                optimizations.append(f"组分发: {old_interval}秒 → {targets['group_dispatch']}秒")
            
            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success("✅ 任务调度策略已优化:")
            for opt in optimizations:
                logger.info(f"  - {opt}")
                
        except Exception as e:
            logger.error(f"❌ 优化任务调度策略失败: {e}")
    
    def _verify_optimizations(self):
        """验证优化配置"""
        logger.info("🔍 验证优化配置...")
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证API频率限制
            rate_limiting = config.get('rate_limiting', {})
            global_rate = rate_limiting.get('global', {}).get('rate', 0)
            if global_rate >= 20:
                logger.success(f"✅ API全局速率已优化: {global_rate}/秒")
            else:
                logger.warning(f"⚠️ API全局速率可能需要检查: {global_rate}/秒")
            
            # 验证任务间隔
            workflow = config.get('workflow', {})
            plan_interval = workflow.get('plan_creation', {}).get('interval_seconds', 0)
            harvest_interval = workflow.get('independent_harvest', {}).get('interval_seconds', 0)
            
            if plan_interval <= 60:
                logger.success(f"✅ 计划创建间隔已优化: {plan_interval}秒")
            if harvest_interval <= 300:
                logger.success(f"✅ 独立收割间隔已优化: {harvest_interval}秒")
            
            # 验证Celery配置
            with open(self.worker_file, 'r', encoding='utf-8') as f:
                worker_content = f.read()
                if "'-c', '12'" in worker_content or "'-c', '15'" in worker_content:
                    logger.success("✅ Celery并发数已优化")
                elif "'-c', '8'" in worker_content:
                    logger.success("✅ Celery并发数已优化（保守策略）")
                else:
                    logger.warning("⚠️ Celery并发数可能需要检查")
            
            logger.info(f"📁 备份文件位置: {self.backup_dir}")
            
        except Exception as e:
            logger.error(f"❌ 验证优化配置失败: {e}")

    def generate_optimization_report(self):
        """生成优化效果报告"""
        logger.critical("📊 生成优化效果预测报告")
        logger.critical("=" * 50)

        targets = self.optimization_targets

        # API性能提升预测
        api_improvement = {
            '全局API速率': f"5/秒 → {targets['api_rate_limits']['global_rate']}/秒 (+{targets['api_rate_limits']['global_rate']-5}x)",
            '计划创建速率': f"2/秒 → {targets['api_rate_limits']['create_campaign_rate']}/秒 (+{targets['api_rate_limits']['create_campaign_rate']/2:.1f}x)",
            '视频上传速率': f"3/秒 → {targets['api_rate_limits']['upload_video_rate']}/秒 (+{targets['api_rate_limits']['upload_video_rate']/3:.1f}x)"
        }

        # 并发性能提升预测
        concurrency_improvement = {
            'Worker线程数': f"5 → {targets['celery_concurrency']['worker_threads']} (+{targets['celery_concurrency']['worker_threads']-5})",
            '理论并发能力': f"{5}x → {targets['celery_concurrency']['worker_threads']}x (+{targets['celery_concurrency']['worker_threads']/5:.1f}x)"
        }

        # 任务调度改进预测
        scheduling_improvement = {
            '计划创建间隔': f"120秒 → {targets['task_intervals']['plan_creation']}秒 (-{120-targets['task_intervals']['plan_creation']}秒)",
            '独立收割间隔': f"600秒 → {targets['task_intervals']['independent_harvest']}秒 (-{600-targets['task_intervals']['independent_harvest']}秒)",
            '素材监控间隔': f"300秒 → {targets['task_intervals']['material_monitoring']}秒 (-{300-targets['task_intervals']['material_monitoring']}秒)"
        }

        logger.info("🚀 API性能提升预测:")
        for key, value in api_improvement.items():
            logger.info(f"  - {key}: {value}")

        logger.info("⚡ 并发性能提升预测:")
        for key, value in concurrency_improvement.items():
            logger.info(f"  - {key}: {value}")

        logger.info("⏱️ 任务调度改进预测:")
        for key, value in scheduling_improvement.items():
            logger.info(f"  - {key}: {value}")

        # 整体性能预测
        logger.critical("🎯 整体性能提升预测:")
        logger.critical(f"  - 任务执行间隔: 7.3分钟 → <2分钟 (提升70%+)")
        logger.critical(f"  - API吞吐量: 提升{targets['api_rate_limits']['global_rate']/5:.1f}x")
        logger.critical(f"  - 并发处理能力: 提升{targets['celery_concurrency']['worker_threads']/5:.1f}x")
        logger.critical(f"  - 系统响应速度: 提升50%+")

def main():
    """主函数"""
    optimizer = ConcurrencyPerformanceOptimizer()

    # 生成优化预测报告
    optimizer.generate_optimization_report()

    # 执行优化
    success = optimizer.run_comprehensive_optimization()

    if success:
        logger.critical("🎉 并发性能优化完成！")
        logger.critical("📋 下一步操作:")
        logger.critical("  1. 重启Celery服务以应用更改")
        logger.critical("  2. 监控任务执行间隔（目标<2分钟）")
        logger.critical("  3. 监控API调用成功率")
        logger.critical("  4. 监控系统资源使用率")
        logger.critical("")
        logger.critical("🔄 重启命令:")
        logger.critical("  python run_celery_worker.py")
        logger.critical("  python run_celery_beat.py")
        logger.critical("")
        logger.critical("📊 监控命令:")
        logger.critical("  python ai_tools/optimization/ai_tool_20250728_optimization_performance_monitor.py")
    else:
        logger.critical("❌ 优化失败，请检查错误日志")

if __name__ == "__main__":
    main()
