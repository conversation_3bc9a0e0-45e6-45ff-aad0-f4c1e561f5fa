#!/usr/bin/env python3
"""
修复千川工作流提审超时机制
解决342个超时计划未被正确标记的问题
"""

import sys
import os
from datetime import datetime, timedelta, timezone
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text

class AppealTimeoutFixer:
    """提审超时修复器"""
    
    def __init__(self):
        logger.critical("🔧 千川工作流提审超时机制修复")
        logger.critical("=" * 60)
        self.app_settings = load_settings()
        self.appeal_max_hours = self.app_settings.get('robustness', {}).get('appeal_max_duration_hours', 10)
        self.current_time = datetime.now(timezone.utc)
    
    def fix_timeout_campaigns_immediately(self):
        """立即修复超时的计划"""
        logger.critical("🔧 立即修复超时的计划")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 查找所有超过10小时且状态不正确的计划
                timeout_threshold = self.current_time - timedelta(hours=self.appeal_max_hours)
                
                timeout_fix_query = text("""
                    UPDATE campaigns 
                    SET status = 'APPEAL_TIMEOUT',
                        updated_at = NOW()
                    WHERE first_appeal_at IS NOT NULL
                    AND first_appeal_at < :timeout_threshold
                    AND status NOT IN ('APPEAL_TIMEOUT', 'MONITORING', 'APPROVED', 'REJECTED')
                    AND campaign_id_qc IN (
                        SELECT c.campaign_id_qc 
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE aa.account_type = 'TEST'
                    )
                    RETURNING campaign_id_qc, status, first_appeal_at
                """)
                
                result = db.execute(timeout_fix_query, {
                    'timeout_threshold': timeout_threshold
                })
                
                fixed_campaigns = result.fetchall()
                db.commit()
                
                logger.critical(f"✅ 超时计划修复完成:")
                logger.critical(f"  修复计划数: {len(fixed_campaigns)} 个")
                logger.critical(f"  超时阈值: {self.appeal_max_hours} 小时")
                logger.critical(f"  修复时间: {self.current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
                
                if fixed_campaigns:
                    logger.critical("  修复的计划:")
                    for campaign in fixed_campaigns[:10]:  # 显示前10个
                        hours_overdue = (self.current_time - campaign.first_appeal_at).total_seconds() / 3600
                        logger.critical(f"    📋 {campaign.campaign_id_qc}: 超时 {hours_overdue:.1f} 小时")
                
                return len(fixed_campaigns)
                
        except Exception as e:
            logger.error(f"❌ 修复超时计划失败: {e}")
            return 0
    
    def add_comprehensive_timeout_check(self):
        """添加全面的超时检查函数到scheduler.py"""
        logger.critical("\n🔧 添加全面的超时检查函数")
        logger.critical("=" * 60)
        
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经存在全面超时检查函数
            if 'handle_timeout_campaigns_comprehensive' in content:
                logger.critical("✅ 全面超时检查函数已存在")
                return True
            
            # 添加新的超时检查函数
            timeout_function = '''
def handle_timeout_campaigns_comprehensive(db: Session, app_settings: Dict[str, Any]):
    """全面检查和处理超时的提审计划"""
    logger.info("--- [工作流2.1] 开始：全面超时检查 ---")
    try:
        appeal_max_hours = app_settings.get('robustness', {}).get('appeal_max_duration_hours', 10)
        timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=appeal_max_hours)
        
        # 查找所有超时的计划（不限制状态）
        timeout_campaigns = db.query(Campaign).options(
            joinedload(Campaign.account)
        ).filter(
            Campaign.first_appeal_at.isnot(None),
            Campaign.first_appeal_at < timeout_threshold,
            Campaign.status.notin_(['APPEAL_TIMEOUT', 'MONITORING', 'APPROVED', 'REJECTED'])
        ).join(AdAccount).filter(
            AdAccount.account_type == 'TEST'
        ).all()
        
        if not timeout_campaigns:
            logger.info("没有发现超时的提审计划")
            return
        
        logger.warning(f"发现 {len(timeout_campaigns)} 个超时的提审计划")
        
        for campaign in timeout_campaigns:
            hours_overdue = (datetime.now(timezone.utc) - campaign.first_appeal_at).total_seconds() / 3600
            logger.warning(f"计划 {campaign.campaign_id_qc} 首次提审已超过 {hours_overdue:.1f} 小时，标记为超时")
            
            campaign.status = 'APPEAL_TIMEOUT'
            campaign.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        logger.info(f"已将 {len(timeout_campaigns)} 个超时计划标记为 APPEAL_TIMEOUT")
        
    except Exception as e:
        logger.error(f"全面超时检查失败: {e}", exc_info=True)
        db.rollback()

'''
            
            # 找到合适的位置插入函数（在handle_plans_awaiting_appeal之前）
            insert_position = content.find('def handle_plans_awaiting_appeal(')
            if insert_position == -1:
                logger.error("❌ 未找到handle_plans_awaiting_appeal函数")
                return False
            
            # 插入新函数
            new_content = content[:insert_position] + timeout_function + '\n' + content[insert_position:]
            
            # 写回文件
            with open(scheduler_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.critical("✅ 全面超时检查函数已添加到scheduler.py")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 添加超时检查函数失败: {e}")
            return False
    
    def update_main_scheduler_call(self):
        """更新主调度器调用，包含全面超时检查"""
        logger.critical("\n🔧 更新主调度器调用")
        logger.critical("=" * 60)
        
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找main函数中的调用位置
            main_function_start = content.find('def main():')
            if main_function_start == -1:
                logger.error("❌ 未找到main函数")
                return False
            
            # 查找handle_plans_awaiting_appeal的调用
            appeal_call = 'handle_plans_awaiting_appeal(db, app_settings)'
            appeal_call_pos = content.find(appeal_call, main_function_start)
            
            if appeal_call_pos == -1:
                logger.error("❌ 未找到handle_plans_awaiting_appeal调用")
                return False
            
            # 检查是否已经添加了全面超时检查调用
            if 'handle_timeout_campaigns_comprehensive' in content[main_function_start:]:
                logger.critical("✅ 全面超时检查调用已存在")
                return True
            
            # 在handle_plans_awaiting_appeal调用之前添加全面超时检查
            timeout_call = '        handle_timeout_campaigns_comprehensive(db, app_settings)\n        '
            
            new_content = content[:appeal_call_pos] + timeout_call + content[appeal_call_pos:]
            
            # 写回文件
            with open(scheduler_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.critical("✅ 主调度器调用已更新，包含全面超时检查")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 更新主调度器调用失败: {e}")
            return False
    
    def verify_fix_effectiveness(self):
        """验证修复效果"""
        logger.critical("\n🔍 验证修复效果")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 检查修复后的状态
                timeout_threshold = self.current_time - timedelta(hours=self.appeal_max_hours)
                
                remaining_timeout_query = text("""
                    SELECT 
                        COUNT(*) as remaining_count
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NOT NULL
                    AND c.first_appeal_at < :timeout_threshold
                    AND c.status NOT IN ('APPEAL_TIMEOUT', 'MONITORING', 'APPROVED', 'REJECTED')
                    AND aa.account_type = 'TEST'
                """)
                
                result = db.execute(remaining_timeout_query, {
                    'timeout_threshold': timeout_threshold
                }).fetchone()
                
                remaining_count = result.remaining_count
                
                # 检查APPEAL_TIMEOUT状态的计划数
                timeout_status_query = text("""
                    SELECT 
                        COUNT(*) as timeout_count
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.status = 'APPEAL_TIMEOUT'
                    AND aa.account_type = 'TEST'
                """)
                
                timeout_result = db.execute(timeout_status_query).fetchone()
                timeout_count = timeout_result.timeout_count
                
                logger.critical(f"📊 修复效果验证:")
                logger.critical(f"  剩余超时计划: {remaining_count} 个")
                logger.critical(f"  APPEAL_TIMEOUT状态: {timeout_count} 个")
                
                if remaining_count == 0:
                    logger.critical("✅ 修复完全成功！所有超时计划已正确标记")
                else:
                    logger.critical(f"⚠️ 仍有 {remaining_count} 个计划需要处理")
                
                return remaining_count == 0
                
        except Exception as e:
            logger.error(f"❌ 验证修复效果失败: {e}")
            return False
    
    def generate_prevention_recommendations(self):
        """生成预防建议"""
        logger.critical("\n💡 预防建议")
        logger.critical("=" * 60)
        
        logger.critical("🎯 为防止超时机制再次失效，建议:")
        
        logger.critical("\n1. 代码层面改进:")
        logger.critical("   - ✅ 已添加全面超时检查函数")
        logger.critical("   - ✅ 不依赖计划状态，直接检查first_appeal_at时间")
        logger.critical("   - ✅ 在每次工作流运行时都执行超时检查")
        
        logger.critical("\n2. 监控机制:")
        logger.critical("   - 建议每日检查超时计划数量")
        logger.critical("   - 设置告警：超时计划数 > 10个时通知")
        logger.critical("   - 定期验证超时机制是否正常工作")
        
        logger.critical("\n3. 配置优化:")
        logger.critical("   - 当前超时时间: 10小时")
        logger.critical("   - 建议根据实际审核时间调整")
        logger.critical("   - 考虑不同时段的审核效率差异")
        
        logger.critical("\n4. 日志改进:")
        logger.critical("   - 增加超时检查的详细日志")
        logger.critical("   - 记录每次超时处理的计划数量")
        logger.critical("   - 便于后续问题排查")

def main():
    """主修复函数"""
    try:
        fixer = AppealTimeoutFixer()
        
        # 1. 立即修复现有的超时计划
        fixed_count = fixer.fix_timeout_campaigns_immediately()
        
        if fixed_count > 0:
            logger.critical(f"\n✅ 立即修复完成，处理了 {fixed_count} 个超时计划")
        
        # 2. 添加全面的超时检查函数
        if fixer.add_comprehensive_timeout_check():
            logger.critical("✅ 超时检查函数添加成功")
        else:
            logger.error("❌ 超时检查函数添加失败")
            return False
        
        # 3. 更新主调度器调用
        if fixer.update_main_scheduler_call():
            logger.critical("✅ 主调度器更新成功")
        else:
            logger.error("❌ 主调度器更新失败")
            return False
        
        # 4. 验证修复效果
        if fixer.verify_fix_effectiveness():
            logger.critical("✅ 修复效果验证通过")
        else:
            logger.critical("⚠️ 修复效果需要进一步检查")
        
        # 5. 生成预防建议
        fixer.generate_prevention_recommendations()
        
        logger.critical(f"\n🎉 千川工作流提审超时机制修复完成!")
        logger.critical("⚠️ 建议重启工作流以使修改生效")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
