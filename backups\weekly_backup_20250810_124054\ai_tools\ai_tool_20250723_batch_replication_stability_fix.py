#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复批量账户复制页面的稳定性问题
依赖关系: web_ui.py批量复制功能
清理条件: 功能被替代时删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def analyze_batch_replication_issues():
    """分析批量复制页面的问题"""
    print("🔍 批量账户复制页面问题分析")
    print("=" * 60)
    
    issues = [
        {
            "问题": "状态管理不稳定",
            "描述": "session_state变量缺乏一致性检查，多处st.rerun()调用导致状态竞争",
            "影响": "页面状态混乱，点击操作不响应",
            "严重程度": "高"
        },
        {
            "问题": "表单提交冲突",
            "描述": "表单内使用复杂组件，提交后立即调用st.rerun()导致冲突",
            "影响": "表单提交失败，页面跳转异常",
            "严重程度": "高"
        },
        {
            "问题": "数据编辑器不稳定",
            "描述": "st.data_editor与其他状态管理冲突，选择状态不同步",
            "影响": "计划选择失效，数据显示异常",
            "严重程度": "中"
        },
        {
            "问题": "任务执行状态混乱",
            "描述": "任务运行时状态更新导致页面重新渲染问题",
            "影响": "任务中断，进度丢失",
            "严重程度": "高"
        },
        {
            "问题": "错误恢复机制缺失",
            "描述": "缺乏有效的错误恢复和状态重置机制",
            "影响": "错误状态无法恢复，需要刷新页面",
            "严重程度": "中"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['问题']} ({issue['严重程度']})")
        print(f"   描述: {issue['描述']}")
        print(f"   影响: {issue['影响']}")
    
    return issues

def create_stability_fixes():
    """创建稳定性修复代码"""
    
    fixes = {
        "状态管理优化": '''
# 改进的状态初始化函数
def init_batch_state_safe():
    """安全的批量复制状态初始化"""
    defaults = {
        'batch_replication_step': 1,
        'batch_found_campaigns': [],
        'batch_selected_campaigns': [],
        'batch_source_accounts': [],
        'batch_task_running': False,
        'batch_task_paused': False,
        'batch_progress': {'current': 0, 'total': 0, 'logs': [], 'success_count': 0, 'failed_count': 0},
        'batch_filter_criteria': {},
        'batch_last_update_time': 0,
        'batch_error_state': False,
        'batch_error_message': ""
    }

    # 安全初始化，避免覆盖现有有效状态
    for key, default_value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = default_value
        elif key == 'batch_progress' and not isinstance(st.session_state[key], dict):
            # 修复损坏的进度状态
            st.session_state[key] = default_value

    # 状态一致性检查
    if st.session_state.batch_replication_step < 1 or st.session_state.batch_replication_step > 4:
        st.session_state.batch_replication_step = 1
        st.session_state.batch_error_state = True
        st.session_state.batch_error_message = "检测到状态异常，已重置到第一步"

def reset_batch_state():
    """重置批量复制状态"""
    keys_to_reset = [
        'batch_replication_step', 'batch_found_campaigns', 'batch_selected_campaigns',
        'batch_source_accounts', 'batch_task_running', 'batch_task_paused',
        'batch_progress', 'batch_filter_criteria', 'batch_error_state', 'batch_error_message'
    ]
    
    for key in keys_to_reset:
        if key in st.session_state:
            del st.session_state[key]
    
    # 重新初始化
    init_batch_state_safe()
''',

        "表单处理优化": '''
# 改进的表单提交处理
def handle_form_submission_safe(action, **kwargs):
    """安全的表单提交处理"""
    try:
        if action == "next_step":
            step = kwargs.get('step', 1)
            st.session_state.batch_replication_step = step
            st.session_state.batch_last_update_time = time.time()
            
            # 延迟rerun，避免表单冲突
            st.success(f"✅ 正在跳转到第{step}步...")
            time.sleep(0.1)  # 短暂延迟
            st.rerun()
            
        elif action == "back_step":
            step = kwargs.get('step', 1)
            st.session_state.batch_replication_step = step
            st.success("⬅️ 正在返回上一步...")
            time.sleep(0.1)
            st.rerun()
            
        elif action == "reset":
            reset_batch_state()
            st.success("🔄 状态已重置")
            time.sleep(0.1)
            st.rerun()
            
    except Exception as e:
        st.session_state.batch_error_state = True
        st.session_state.batch_error_message = f"表单处理失败: {str(e)}"
        st.error(f"操作失败: {str(e)}")

# 改进的多账户选择器使用
def render_source_account_selection_safe():
    """安全的源账户选择"""
    st.subheader("🏢 第一步：选择源广告账户")
    st.info("选择要从中复制计划的源广告账户，支持选择多个账户进行批量操作")

    # 使用session_state缓存选择结果，避免表单冲突
    if 'batch_temp_source_accounts' not in st.session_state:
        st.session_state.batch_temp_source_accounts = []

    with st.form("batch_select_source_accounts_form"):
        try:
            # 多账户选择器
            selected_source_accounts = create_multi_account_selector(
                key="batch_source_accounts_temp",
                label="选择源广告账户（支持多选）",
                help_text="⭐ 标记表示收藏账户，选择要复制计划的源账户",
                show_filter=True,
                show_stats=True,
                in_form=True
            )

            # 缓存选择结果
            if selected_source_accounts:
                st.session_state.batch_temp_source_accounts = selected_source_accounts

            # 显示当前选择
            if st.session_state.batch_temp_source_accounts:
                st.subheader("📋 已选择的源账户")
                for i, account in enumerate(st.session_state.batch_temp_source_accounts, 1):
                    is_favorite = getattr(account, 'is_favorite', False)
                    star = "⭐ " if is_favorite else ""
                    st.write(f"{i}. {star}{account.name} ({account.account_id_qc})")

            next_step_submitted = st.form_submit_button("下一步：设置筛选条件 ➡️")

            if next_step_submitted:
                if not st.session_state.batch_temp_source_accounts:
                    st.error("必须选择至少一个源广告账户！")
                else:
                    # 确认选择
                    st.session_state.batch_source_accounts = st.session_state.batch_temp_source_accounts
                    handle_form_submission_safe("next_step", step=2)

        except Exception as e:
            st.error(f"源账户选择失败: {str(e)}")
            st.session_state.batch_error_state = True
            st.session_state.batch_error_message = str(e)
''',

        "数据编辑器优化": '''
# 改进的计划选择界面
def render_campaign_selection_safe():
    """安全的计划选择界面"""
    st.subheader("📋 第三步：选择要复制的计划")

    if not st.session_state.batch_found_campaigns:
        st.error("没有找到计划数据")
        if st.button("⬅️ 返回上一步"):
            handle_form_submission_safe("back_step", step=2)
        return

    st.info(f"从 {len(st.session_state.batch_source_accounts)} 个源账户找到 {len(st.session_state.batch_found_campaigns)} 个符合条件的计划")

    # 使用简化的选择机制，避免data_editor的复杂性
    if 'batch_campaign_selections' not in st.session_state:
        # 初始化选择状态（默认全选）
        st.session_state.batch_campaign_selections = {
            str(i): True for i in range(len(st.session_state.batch_found_campaigns))
        }

    # 批量操作按钮
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("✅ 全选"):
            st.session_state.batch_campaign_selections = {
                str(i): True for i in range(len(st.session_state.batch_found_campaigns))
            }
            st.rerun()
    with col2:
        if st.button("❌ 全不选"):
            st.session_state.batch_campaign_selections = {
                str(i): False for i in range(len(st.session_state.batch_found_campaigns))
            }
            st.rerun()
    with col3:
        if st.button("🔄 反选"):
            st.session_state.batch_campaign_selections = {
                k: not v for k, v in st.session_state.batch_campaign_selections.items()
            }
            st.rerun()

    # 分页显示计划列表
    campaigns_per_page = 20
    total_campaigns = len(st.session_state.batch_found_campaigns)
    total_pages = (total_campaigns + campaigns_per_page - 1) // campaigns_per_page

    if 'batch_current_page' not in st.session_state:
        st.session_state.batch_current_page = 0

    # 分页控制
    if total_pages > 1:
        col1, col2, col3 = st.columns([1, 2, 1])
        with col1:
            if st.button("⬅️ 上一页", disabled=st.session_state.batch_current_page == 0):
                st.session_state.batch_current_page -= 1
                st.rerun()
        with col2:
            st.write(f"第 {st.session_state.batch_current_page + 1} 页，共 {total_pages} 页")
        with col3:
            if st.button("下一页 ➡️", disabled=st.session_state.batch_current_page >= total_pages - 1):
                st.session_state.batch_current_page += 1
                st.rerun()

    # 显示当前页的计划
    start_idx = st.session_state.batch_current_page * campaigns_per_page
    end_idx = min(start_idx + campaigns_per_page, total_campaigns)
    current_page_campaigns = st.session_state.batch_found_campaigns[start_idx:end_idx]

    # 使用checkbox显示计划
    for i, campaign in enumerate(current_page_campaigns):
        actual_idx = start_idx + i
        key = str(actual_idx)
        
        col1, col2 = st.columns([1, 10])
        with col1:
            selected = st.checkbox(
                "",
                value=st.session_state.batch_campaign_selections.get(key, True),
                key=f"campaign_select_{actual_idx}"
            )
            st.session_state.batch_campaign_selections[key] = selected
        
        with col2:
            st.write(f"**{campaign['name']}**")
            st.write(f"状态: {campaign.get('status_display', 'N/A')} | 源账户: {campaign.get('source_account_name', 'N/A')}")

    # 统计选中的计划
    selected_count = sum(1 for v in st.session_state.batch_campaign_selections.values() if v)
    
    # 按源账户分组显示统计
    if selected_count > 0:
        st.subheader("📊 选择统计")
        account_stats = {}
        for i, campaign in enumerate(st.session_state.batch_found_campaigns):
            if st.session_state.batch_campaign_selections.get(str(i), False):
                account_name = campaign['source_account_name']
                if account_name not in account_stats:
                    account_stats[account_name] = 0
                account_stats[account_name] += 1

        cols = st.columns(min(len(account_stats), 4))
        for i, (account_name, count) in enumerate(account_stats.items()):
            with cols[i % 4]:
                st.metric(account_name, f"{count} 个计划")

    # 导航按钮
    col1, col2, col3 = st.columns([1, 1, 1])
    with col1:
        if st.button("⬅️ 返回上一步"):
            handle_form_submission_safe("back_step", step=2)
    with col2:
        st.info(f"已选择 {selected_count} 个计划")
    with col3:
        if st.button("下一步 ➡️", disabled=selected_count == 0):
            # 保存选中的计划
            selected_campaigns = []
            for i, campaign in enumerate(st.session_state.batch_found_campaigns):
                if st.session_state.batch_campaign_selections.get(str(i), False):
                    selected_campaigns.append(campaign)
            
            st.session_state.batch_selected_campaigns = selected_campaigns
            handle_form_submission_safe("next_step", step=4)
''',

        "任务执行优化": '''
# 改进的任务执行管理
def execute_batch_replication_task_safe(new_plan_prefix, clean_prefixes, preserve_standard_format):
    """安全的批量复制任务执行"""
    try:
        # 检查任务状态
        if not st.session_state.batch_task_running or st.session_state.batch_task_paused:
            return

        progress = st.session_state.batch_progress
        
        # 防止重复执行
        current_time = time.time()
        if current_time - st.session_state.get('batch_last_execution_time', 0) < 2:
            return  # 2秒内不重复执行
        
        st.session_state.batch_last_execution_time = current_time

        # 任务开始日志
        if progress['current'] == 0:
            progress['logs'].append({
                'type': 'info',
                'message': f"开始批量账户内复制任务：{len(st.session_state.batch_selected_campaigns)} 个计划",
                'timestamp': current_time
            })

        # 按源账户分组处理
        campaigns_by_account = {}
        for campaign in st.session_state.batch_selected_campaigns:
            account_id = campaign['source_account_id']
            if account_id not in campaigns_by_account:
                campaigns_by_account[account_id] = []
            campaigns_by_account[account_id].append(campaign)

        # 执行复制任务
        total_success = 0
        total_failed = 0

        for account_id, campaigns in campaigns_by_account.items():
            # 检查是否被暂停或停止
            if not st.session_state.batch_task_running or st.session_state.batch_task_paused:
                break

            progress['logs'].append({
                'type': 'info',
                'message': f"正在处理账户 {account_id} 的 {len(campaigns)} 个计划",
                'timestamp': time.time()
            })

            try:
                # 执行复制
                result = replicate_plan_tool.run_replication(
                    target_account_ids=[account_id],
                    new_plan_prefix=new_plan_prefix if new_plan_prefix else None,
                    selected_campaigns=campaigns,
                    clean_prefixes=clean_prefixes,
                    preserve_standard_format=preserve_standard_format
                )

                if result['success']:
                    total_success += len(campaigns)
                    progress['success_count'] = total_success
                    progress['logs'].append({
                        'type': 'success',
                        'message': f"✅ 账户 {account_id} 复制成功",
                        'timestamp': time.time()
                    })
                else:
                    total_failed += len(campaigns)
                    progress['failed_count'] = total_failed
                    progress['logs'].append({
                        'type': 'error',
                        'message': f"❌ 账户 {account_id} 复制失败: {result['message']}",
                        'timestamp': time.time()
                    })

            except Exception as e:
                total_failed += len(campaigns)
                progress['failed_count'] = total_failed
                progress['logs'].append({
                    'type': 'error',
                    'message': f"❌ 账户 {account_id} 处理异常: {str(e)}",
                    'timestamp': time.time()
                })

            # 更新进度
            progress['current'] = total_success + total_failed

        # 任务完成处理
        if progress['current'] >= len(st.session_state.batch_selected_campaigns):
            st.session_state.batch_task_running = False
            
            if total_success > 0:
                progress['logs'].append({
                    'type': 'success',
                    'message': f"✅ 批量复制任务完成: {total_success} 个成功, {total_failed} 个失败",
                    'timestamp': time.time()
                })
                st.success(f"🎉 批量账户内复制任务完成！成功: {total_success}, 失败: {total_failed}")
            else:
                progress['logs'].append({
                    'type': 'error',
                    'message': "❌ 所有复制操作都失败了",
                    'timestamp': time.time()
                })
                st.error("❌ 批量账户内复制失败: 所有操作都失败了")

    except Exception as e:
        st.session_state.batch_task_running = False
        st.session_state.batch_error_state = True
        st.session_state.batch_error_message = str(e)
        st.error(f"任务执行异常: {str(e)}")
'''
    }
    
    return fixes

def main():
    """主函数"""
    print("🔧 批量账户复制页面稳定性修复工具")
    print("=" * 60)
    
    # 分析问题
    issues = analyze_batch_replication_issues()
    
    print(f"\n📊 发现 {len(issues)} 个主要问题")
    
    # 生成修复方案
    fixes = create_stability_fixes()
    
    print(f"\n💡 生成 {len(fixes)} 个修复方案")
    
    print("\n🚀 修复方案概览:")
    for fix_name in fixes.keys():
        print(f"  ✅ {fix_name}")
    
    print("\n📋 实施建议:")
    print("1. 备份当前的web_ui.py文件")
    print("2. 逐步应用修复方案，每次修复一个问题")
    print("3. 在每次修复后进行测试验证")
    print("4. 重点测试表单提交和状态切换")
    print("5. 验证任务执行的稳定性")
    
    print("\n⚠️ 注意事项:")
    print("- 修复涉及核心状态管理，需要谨慎测试")
    print("- 建议在测试环境先验证修复效果")
    print("- 保持现有功能的兼容性")
    print("- 添加更多的错误处理和恢复机制")
    
    return True

if __name__ == "__main__":
    main()
