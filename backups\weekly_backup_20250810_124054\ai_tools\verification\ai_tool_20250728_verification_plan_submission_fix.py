#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证千川自动化项目计划提审功能修复是否生效
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
import redis
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def check_celery_task_registration():
    """检查Celery任务注册状态"""
    logger.info("🔍 检查Celery任务注册状态...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        # 获取所有注册的任务
        registered_tasks = list(app.tasks.keys())
        
        # 检查关键任务
        critical_tasks = [
            'tasks.submit_plans',
            'tasks.create_plans',
            'tasks.appeal_plans',
            'tasks.monitor_materials'
        ]
        
        logger.info("📋 Celery任务注册状态:")
        logger.info("-" * 50)
        
        missing_tasks = []
        for task in critical_tasks:
            if task in registered_tasks:
                logger.success(f"✅ {task} - 已注册")
            else:
                logger.error(f"❌ {task} - 未注册")
                missing_tasks.append(task)
        
        # 显示所有任务（用于调试）
        logger.info(f"\n📊 总注册任务数: {len(registered_tasks)}")
        logger.info("🔍 所有注册任务:")
        for task in sorted(registered_tasks):
            if task.startswith('tasks.'):
                logger.info(f"   📌 {task}")
        
        return len(missing_tasks) == 0, missing_tasks
        
    except Exception as e:
        logger.error(f"❌ 检查任务注册失败: {e}")
        return False, []

def check_celery_beat_schedule():
    """检查Celery Beat调度配置"""
    logger.info("\n🔍 检查Celery Beat调度配置...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        beat_schedule = app.conf.beat_schedule
        
        # 检查关键调度任务
        critical_schedules = [
            'plan-submission-configurable',
            'plan-creation-configurable',
            'plan-appeal-configurable',
            'material-monitoring-configurable'
        ]
        
        logger.info("📅 Beat调度配置状态:")
        logger.info("-" * 50)
        
        missing_schedules = []
        for schedule_name in critical_schedules:
            if schedule_name in beat_schedule:
                config = beat_schedule[schedule_name]
                task_name = config.get('task', 'Unknown')
                schedule_info = config.get('schedule', 'Unknown')
                logger.success(f"✅ {schedule_name}")
                logger.info(f"   📌 任务: {task_name}")
                logger.info(f"   ⏰ 调度: {schedule_info}")
            else:
                logger.error(f"❌ {schedule_name} - 未配置")
                missing_schedules.append(schedule_name)
        
        return len(missing_schedules) == 0, missing_schedules
        
    except Exception as e:
        logger.error(f"❌ 检查Beat调度配置失败: {e}")
        return False, []

def check_configuration_file():
    """检查配置文件中的plan_submission配置"""
    logger.info("\n🔍 检查配置文件...")
    
    try:
        config = load_config()
        workflow = config.get('workflow', {})
        
        # 检查plan_submission配置
        plan_submission = workflow.get('plan_submission', {})
        
        if plan_submission:
            logger.success("✅ plan_submission配置存在")
            logger.info(f"   📌 enabled: {plan_submission.get('enabled', 'Unknown')}")
            logger.info(f"   ⏰ interval_seconds: {plan_submission.get('interval_seconds', 'Unknown')}")
            logger.info(f"   📝 description: {plan_submission.get('description', 'Unknown')}")
            
            if plan_submission.get('enabled', False):
                logger.success("✅ plan_submission已启用")
                return True
            else:
                logger.error("❌ plan_submission已禁用")
                return False
        else:
            logger.error("❌ plan_submission配置不存在")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查配置文件失败: {e}")
        return False

def check_database_unsubmitted_plans():
    """检查数据库中未提审的计划"""
    logger.info("\n🔍 检查数据库中未提审的计划...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 查询未提审的计划
        cursor.execute("""
            SELECT 
                COUNT(*) as total_unsubmitted,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_unsubmitted
            FROM campaigns 
            WHERE status = 'AUDITING' 
            AND appeal_status IS NULL 
            AND first_appeal_at IS NULL
        """)
        
        total_unsubmitted, recent_unsubmitted = cursor.fetchone()
        
        logger.info("📊 未提审计划统计:")
        logger.info("-" * 30)
        logger.info(f"   📋 总计未提审: {total_unsubmitted}")
        logger.info(f"   🕐 最近1小时未提审: {recent_unsubmitted}")
        
        if total_unsubmitted > 0:
            logger.warning(f"⚠️ 发现 {total_unsubmitted} 个未提审计划")
            
            # 显示最近的几个未提审计划
            cursor.execute("""
                SELECT campaign_id_qc, created_at, 
                       EXTRACT(EPOCH FROM (NOW() - created_at))/3600 as hours_since_creation
                FROM campaigns 
                WHERE status = 'AUDITING' 
                AND appeal_status IS NULL 
                AND first_appeal_at IS NULL
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            logger.info("\n📋 最近未提审的计划:")
            for campaign_id, created_at, hours_since in cursor.fetchall():
                logger.warning(f"   ⚠️ {campaign_id} - 创建于 {hours_since:.1f}小时前")
        else:
            logger.success("✅ 没有未提审的计划")
        
        cursor.close()
        conn.close()
        
        return total_unsubmitted, recent_unsubmitted
        
    except Exception as e:
        logger.error(f"❌ 检查数据库失败: {e}")
        return -1, -1

def manual_trigger_submission_task():
    """手动触发计划提审任务"""
    logger.info("\n🚀 手动触发计划提审任务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        logger.info("📤 正在执行手动触发...")
        
        # 手动调用任务
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 任务已派发，ID: {result.id}")
        
        # 等待一段时间检查结果
        logger.info("⏳ 等待任务执行...")
        time.sleep(10)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 任务执行成功")
                return True, "任务执行成功"
            else:
                error = result.result
                logger.error(f"❌ 任务执行失败: {error}")
                return False, str(error)
        else:
            logger.info("⏳ 任务仍在执行中...")
            return True, "任务正在执行"
            
    except Exception as e:
        logger.error(f"❌ 手动触发任务失败: {e}")
        return False, str(e)

def check_celery_beat_schedule_file():
    """检查Celery Beat调度文件"""
    logger.info("\n🔍 检查Celery Beat调度文件...")
    
    schedule_file = project_root / 'logs' / 'celerybeat-schedule.db'
    
    if schedule_file.exists():
        stat = schedule_file.stat()
        modified_time = datetime.fromtimestamp(stat.st_mtime)
        age_hours = (datetime.now() - modified_time).total_seconds() / 3600
        
        logger.info(f"📅 调度文件存在: {schedule_file}")
        logger.info(f"   📅 修改时间: {modified_time}")
        logger.info(f"   ⏰ 文件年龄: {age_hours:.1f} 小时")
        
        if age_hours > 1:
            logger.warning("⚠️ 调度文件较旧，可能需要重新生成")
            logger.info("💡 建议操作:")
            logger.info("   1. 停止Celery Beat进程")
            logger.info("   2. 删除 logs/celerybeat-schedule.db")
            logger.info("   3. 重新启动Celery Beat进程")
            return False
        else:
            logger.success("✅ 调度文件较新")
            return True
    else:
        logger.warning("⚠️ 调度文件不存在")
        logger.info("💡 这可能是正常的，Beat进程启动时会自动创建")
        return True

def check_redis_task_queue():
    """检查Redis任务队列"""
    logger.info("\n🔍 检查Redis任务队列...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        # 检查队列长度
        queue_length = r.llen('celery')
        
        logger.info(f"📊 Celery队列长度: {queue_length}")
        
        # 检查最近的任务
        recent_tasks = r.lrange('celery', -10, -1)
        
        submission_tasks = 0
        for task_data in recent_tasks:
            try:
                task_str = task_data.decode('utf-8')
                if 'submit_plans' in task_str:
                    submission_tasks += 1
            except:
                pass
        
        logger.info(f"📋 最近10个任务中的提审任务: {submission_tasks}")
        
        if submission_tasks > 0:
            logger.success("✅ 发现提审任务在队列中")
        else:
            logger.warning("⚠️ 未发现提审任务在队列中")
        
        return queue_length, submission_tasks
        
    except Exception as e:
        logger.error(f"❌ 检查Redis队列失败: {e}")
        return -1, -1

def run_comprehensive_verification():
    """运行综合验证"""
    logger.info("🚀 开始计划提审功能修复验证...")
    logger.info("="*80)
    
    verification_results = {
        'task_registered': False,
        'beat_scheduled': False,
        'config_enabled': False,
        'database_checked': False,
        'manual_trigger_success': False,
        'schedule_file_ok': False,
        'redis_queue_ok': False
    }
    
    issues_found = []
    recommendations = []
    
    # 1. 检查任务注册
    task_ok, missing_tasks = check_celery_task_registration()
    verification_results['task_registered'] = task_ok
    if not task_ok:
        issues_found.append(f"缺少任务注册: {missing_tasks}")
        recommendations.append("检查tasks.py文件是否正确添加了submit_plans任务")
    
    # 2. 检查Beat调度
    beat_ok, missing_schedules = check_celery_beat_schedule()
    verification_results['beat_scheduled'] = beat_ok
    if not beat_ok:
        issues_found.append(f"缺少Beat调度: {missing_schedules}")
        recommendations.append("检查celery_app.py文件是否正确添加了plan-submission-configurable调度")
    
    # 3. 检查配置文件
    config_ok = check_configuration_file()
    verification_results['config_enabled'] = config_ok
    if not config_ok:
        issues_found.append("plan_submission配置缺失或禁用")
        recommendations.append("检查config/settings.yml文件中的plan_submission配置")
    
    # 4. 检查数据库状态
    total_unsubmitted, recent_unsubmitted = check_database_unsubmitted_plans()
    verification_results['database_checked'] = total_unsubmitted >= 0
    if total_unsubmitted > 0:
        issues_found.append(f"仍有 {total_unsubmitted} 个未提审计划")
        if recent_unsubmitted > 0:
            recommendations.append("最近仍有计划未被提审，说明修复可能未生效")
    
    # 5. 手动触发测试
    trigger_ok, trigger_msg = manual_trigger_submission_task()
    verification_results['manual_trigger_success'] = trigger_ok
    if not trigger_ok:
        issues_found.append(f"手动触发失败: {trigger_msg}")
        recommendations.append("检查任务代码是否有语法错误或导入问题")
    
    # 6. 检查调度文件
    schedule_file_ok = check_celery_beat_schedule_file()
    verification_results['schedule_file_ok'] = schedule_file_ok
    if not schedule_file_ok:
        recommendations.append("重新生成Celery Beat调度文件")
    
    # 7. 检查Redis队列
    queue_length, submission_tasks = check_redis_task_queue()
    verification_results['redis_queue_ok'] = queue_length >= 0
    
    # 生成验证报告
    logger.info("\n" + "="*80)
    logger.info("🎯 验证结果总结")
    logger.info("="*80)
    
    success_count = sum(verification_results.values())
    total_count = len(verification_results)
    
    for check_name, result in verification_results.items():
        status = "✅" if result else "❌"
        check_display = check_name.replace('_', ' ').title()
        logger.info(f"{status} {check_display}")
    
    success_rate = (success_count / total_count) * 100
    logger.info(f"\n📈 验证通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 输出问题和建议
    if issues_found:
        logger.error("\n🚨 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            logger.error(f"   {i}. {issue}")
    
    if recommendations:
        logger.info("\n💡 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
    
    # 最终判断
    if success_rate >= 80 and total_unsubmitted == 0:
        logger.success("\n🎉 计划提审功能修复验证通过！")
    elif success_rate >= 60:
        logger.warning("\n⚠️ 计划提审功能部分正常，需要进一步调试")
    else:
        logger.error("\n❌ 计划提审功能修复验证失败，需要重新修复")
    
    return verification_results, issues_found, recommendations

def main():
    """主函数"""
    try:
        results, issues, recommendations = run_comprehensive_verification()
        
        # 保存验证报告
        report_file = project_root / 'ai_temp' / f'plan_submission_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'verification_results': results,
            'issues_found': issues,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 验证报告已保存: {report_file}")
        
        # 返回成功率
        success_rate = sum(results.values()) / len(results) * 100
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
