#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急手动提审指定计划
清理条件: Celery服务恢复正常后可删除
"""

import os
import sys
import time
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def manual_appeal_specific_plan(campaign_id: str):
    """手动提审指定计划"""
    logger.info(f"🎯 开始手动提审计划: {campaign_id}")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
        from sqlalchemy.orm import joinedload
        import yaml
        
        # 加载配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 查找指定计划
        db = SessionLocal()
        try:
            campaign = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(Campaign.campaign_id_qc == campaign_id).first()
            
            if not campaign:
                logger.error(f"❌ 未找到计划: {campaign_id}")
                return False
            
            logger.info(f"📋 找到计划: {campaign_id}")
            logger.info(f"   📊 当前状态: {campaign.status}")
            logger.info(f"   📊 提审状态: {campaign.appeal_status}")
            logger.info(f"   📊 错误信息: {campaign.appeal_error_message}")
            logger.info(f"   🏢 广告户: {campaign.account.principal.name} ({campaign.account.account_id_qc})")
            
            # 检查是否需要提审
            if campaign.status != 'AUDITING':
                logger.warning(f"⚠️ 计划状态不是AUDITING，当前状态: {campaign.status}")
                return False
            
            if campaign.appeal_status == 'appeal_pending' and campaign.first_appeal_at:
                logger.warning(f"⚠️ 计划已经提审成功，无需重复提审")
                return False
            
            # 准备提审数据
            plans_data = [{
                'campaign_id': campaign.campaign_id_qc,
                'principal_name': campaign.account.principal.name,
                'account_id': campaign.account.account_id_qc
            }]
            
            # 创建优化的提审服务
            appeal_service = create_production_appeal_service(app_settings)
            
            logger.info("🚀 开始执行手动提审...")
            
            # 执行提审
            results = appeal_service.batch_appeal_all_plans(plans_data)
            
            # 更新数据库
            updated_count = appeal_service.update_database_with_results(db, results)
            
            # 分析结果
            if results and len(results) > 0:
                result = results[0]
                if result['success']:
                    logger.success(f"✅ 计划 {campaign_id} 手动提审成功！")
                    logger.info(f"📝 回复内容: {result['message'][:100]}...")
                    return True
                else:
                    logger.error(f"❌ 计划 {campaign_id} 手动提审失败")
                    logger.error(f"📝 失败原因: {result['message']}")
                    return False
            else:
                logger.error(f"❌ 未获取到提审结果")
                return False
                
        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"❌ 手动提审失败: {e}")
        return False

def check_pending_appeals():
    """检查所有待提审的计划"""
    logger.info("🔍 检查所有待提审的计划...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from sqlalchemy.orm import joinedload
        from sqlalchemy import or_
        
        db = SessionLocal()
        try:
            # 查找所有需要提审的计划
            pending_campaigns = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None),
                or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0)
            ).all()
            
            logger.info(f"📊 找到 {len(pending_campaigns)} 个待提审计划:")
            
            for campaign in pending_campaigns:
                time_diff = time.time() - campaign.created_at.timestamp()
                hours_ago = time_diff / 3600
                
                logger.info(f"   📋 {campaign.campaign_id_qc} - {campaign.account.principal.name} - {hours_ago:.1f}小时前创建")
            
            return pending_campaigns
            
        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"❌ 检查待提审计划失败: {e}")
        return []

def batch_manual_appeal_pending():
    """批量手动提审所有待提审计划"""
    logger.info("🚀 批量手动提审所有待提审计划...")
    
    try:
        pending_campaigns = check_pending_appeals()
        
        if not pending_campaigns:
            logger.info("✅ 没有待提审的计划")
            return True
        
        success_count = 0
        
        for campaign in pending_campaigns:
            logger.info(f"\n📋 处理计划: {campaign.campaign_id_qc}")
            
            if manual_appeal_specific_plan(campaign.campaign_id_qc):
                success_count += 1
                logger.success(f"✅ 计划 {campaign.campaign_id_qc} 提审成功")
            else:
                logger.error(f"❌ 计划 {campaign.campaign_id_qc} 提审失败")
            
            # 计划之间等待一下
            if campaign != pending_campaigns[-1]:
                logger.info("⏳ 等待3秒后处理下一个计划...")
                time.sleep(3)
        
        logger.info(f"\n📊 批量提审完成: {success_count}/{len(pending_campaigns)} 个成功")
        
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 批量手动提审失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚨 紧急手动提审工具")
    logger.info("="*60)
    logger.info("🎯 目标: 手动提审因Celery服务问题而未提审的计划")
    logger.info("="*60)
    
    try:
        # 1. 检查待提审计划
        pending_campaigns = check_pending_appeals()
        
        if not pending_campaigns:
            logger.info("✅ 没有待提审的计划，系统正常")
            return True
        
        logger.warning(f"⚠️ 发现 {len(pending_campaigns)} 个待提审计划，可能是Celery服务问题")
        
        # 2. 特别处理用户提到的计划
        target_campaign = "1838875703448667"
        logger.info(f"\n🎯 优先处理用户关注的计划: {target_campaign}")
        
        if manual_appeal_specific_plan(target_campaign):
            logger.success(f"✅ 用户关注的计划 {target_campaign} 已成功提审")
        else:
            logger.error(f"❌ 用户关注的计划 {target_campaign} 提审失败")
        
        # 3. 询问是否批量处理其他计划
        other_campaigns = [c for c in pending_campaigns if c.campaign_id_qc != target_campaign]
        
        if other_campaigns:
            logger.info(f"\n📋 还有 {len(other_campaigns)} 个其他待提审计划")
            logger.info("🚀 开始批量处理其他计划...")
            
            success_count = 0
            for campaign in other_campaigns:
                if manual_appeal_specific_plan(campaign.campaign_id_qc):
                    success_count += 1
                time.sleep(2)  # 避免过于频繁
            
            logger.info(f"📊 其他计划处理完成: {success_count}/{len(other_campaigns)} 个成功")
        
        logger.success("\n🎉 紧急手动提审完成！")
        logger.info("\n💡 建议:")
        logger.info("1. 检查并修复Celery服务配置")
        logger.info("2. 确保Redis服务正常运行")
        logger.info("3. 重启Celery worker和beat服务")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 紧急提审过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 紧急手动提审执行成功！")
        logger.info("💡 用户关注的计划应该已经被提审")
    else:
        logger.error("\n❌ 紧急手动提审执行失败")
    
    sys.exit(0 if success else 1)
