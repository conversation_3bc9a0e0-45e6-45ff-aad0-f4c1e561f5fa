#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 测试账户选择修复效果
依赖关系: 全局账户选择器
清理条件: 测试完成后可删除
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

def test_account_selection_stability():
    """测试账户选择稳定性"""
    st.title("🧪 账户选择稳定性测试")
    
    # 导入全局账户选择器
    try:
        from ai_tool_20250718_maintenance_global_account_selector import (
            render_global_account_selector,
            get_global_selected_account,
            get_accounts_with_favorites
        )
        st.success("✅ 全局账户选择器导入成功")
    except Exception as e:
        st.error(f"❌ 导入失败: {e}")
        return
    
    # 渲染全局账户选择器
    st.sidebar.title("🎯 账户选择测试")
    render_global_account_selector()
    
    # 主要测试区域
    st.header("📊 账户选择状态监控")
    
    # 获取当前选中的账户
    current_account = get_global_selected_account()
    
    if current_account:
        st.success(f"✅ 当前选中账户: {current_account.name}")
        
        # 显示详细信息
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📋 账户详情")
            st.write(f"**账户名称**: {current_account.name}")
            st.write(f"**千川ID**: {current_account.account_id_qc}")
            st.write(f"**账户类型**: {current_account.account_type}")
            
            if hasattr(current_account, 'principal') and current_account.principal:
                st.write(f"**主体**: {current_account.principal.name}")
            
            if hasattr(current_account, 'aweme_id') and current_account.aweme_id:
                st.write(f"**抖音号**: {current_account.aweme_id}")
            else:
                st.write("**抖音号**: 未授权")
        
        with col2:
            st.subheader("🔧 状态验证")
            
            # 检查session_state中的状态
            selector_value = st.session_state.get("global_account_selector", "未设置")
            last_id = st.session_state.get("_last_selected_account_id", "未知")
            
            st.write(f"**选择器值**: {selector_value[:50]}...")
            st.write(f"**记录的ID**: {last_id}")
            st.write(f"**当前账户ID**: {current_account.account_id_qc}")
            
            # 状态一致性检查
            if str(last_id) == str(current_account.account_id_qc):
                st.success("✅ 状态一致")
            else:
                st.error("❌ 状态不一致")
        
        # 测试功能
        st.subheader("🧪 功能测试")
        
        if st.button("🔄 刷新页面状态"):
            st.rerun()
        
        if st.button("🧹 清理状态"):
            if 'global_selected_account' in st.session_state:
                del st.session_state['global_selected_account']
            if '_last_selected_account_id' in st.session_state:
                del st.session_state['_last_selected_account_id']
            st.success("状态已清理，请重新选择账户")
            st.rerun()
        
        # 连续测试
        st.subheader("⚡ 连续选择测试")
        
        if 'test_count' not in st.session_state:
            st.session_state.test_count = 0
        
        if st.button("🎯 执行连续选择测试"):
            st.session_state.test_count += 1
            st.info(f"第 {st.session_state.test_count} 次测试")
            
            # 模拟账户选择操作
            accounts = get_accounts_with_favorites()
            if accounts and len(accounts) > 1:
                # 选择不同的账户进行测试
                test_account = accounts[st.session_state.test_count % len(accounts)]
                st.info(f"测试切换到: {test_account.name}")
                
                # 这里不能直接设置，需要用户手动在侧边栏选择
                st.warning("请在左侧栏手动选择不同的账户来测试选择稳定性")
    
    else:
        st.warning("⚠️ 未选中任何账户")
        st.info("请在左侧栏选择一个广告账户")
        
        # 显示调试信息
        st.subheader("🔧 调试信息")
        selector_value = st.session_state.get("global_account_selector", "未设置")
        st.write(f"**选择器值**: {selector_value}")
        
        # 显示可用账户
        try:
            accounts = get_accounts_with_favorites()
            st.write(f"**可用账户数量**: {len(accounts)}")
            
            if accounts:
                st.subheader("📋 可用账户列表")
                for i, account in enumerate(accounts[:5], 1):  # 只显示前5个
                    is_favorite = getattr(account, 'is_favorite', False)
                    star = "⭐ " if is_favorite else ""
                    st.write(f"{i}. {star}{account.name} ({account.account_id_qc})")
                
                if len(accounts) > 5:
                    st.write(f"... 还有 {len(accounts) - 5} 个账户")
        
        except Exception as e:
            st.error(f"获取账户列表失败: {e}")
    
    # 实时状态监控
    st.subheader("📡 实时状态监控")
    
    # 创建一个自动刷新的状态显示
    placeholder = st.empty()
    
    with placeholder.container():
        current_time = st.empty()
        import datetime
        current_time.text(f"最后更新: {datetime.datetime.now().strftime('%H:%M:%S')}")
        
        # 显示关键状态变量
        st.json({
            "global_selected_account": str(st.session_state.get('global_selected_account', 'None')),
            "global_account_selector": st.session_state.get('global_account_selector', 'None'),
            "_last_selected_account_id": st.session_state.get('_last_selected_account_id', 'None'),
            "_account_options_cache": len(st.session_state.get('_account_options_cache', {}))
        })
    
    # 自动刷新按钮
    if st.button("🔄 启用自动监控"):
        st.info("状态监控已启用，每5秒自动刷新")
        # 注意：实际的自动刷新需要使用st.rerun()和时间控制

def main():
    """主函数"""
    st.set_page_config(
        page_title="账户选择稳定性测试",
        page_icon="🧪",
        layout="wide"
    )
    
    test_account_selection_stability()
    
    # 使用说明
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        ### 测试步骤
        
        1. **基础测试**
           - 在左侧栏选择不同的广告账户
           - 观察右侧的状态显示是否正确更新
           - 检查状态一致性指示器
        
        2. **稳定性测试**
           - 快速切换多个不同账户
           - 观察是否有选择不生效的情况
           - 检查状态同步是否正常
        
        3. **调试模式**
           - 在左侧栏启用"调试模式"
           - 查看详细的状态信息
           - 分析状态同步问题
        
        4. **压力测试**
           - 使用"连续选择测试"功能
           - 多次刷新页面
           - 测试状态恢复能力
        
        ### 问题排查
        
        - 如果选择不生效，检查"状态一致性"指示器
        - 如果状态不一致，使用"清理状态"按钮重置
        - 启用调试模式查看详细信息
        - 观察实时状态监控中的变量变化
        """)

if __name__ == "__main__":
    main()
