#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目系统性恢复方案
清理条件: 系统完全恢复后可删除
"""

import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from loguru import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative, AdAccount, Principal
from qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import func, desc

class SystemRecoveryManager:
    """系统恢复管理器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.base_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        self.recovery_report = {
            'start_time': datetime.now(),
            'phases': {},
            'statistics': {},
            'recommendations': []
        }
        
    def generate_system_health_report(self):
        """生成系统健康报告"""
        logger.critical("🏥 生成系统健康报告")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 1. 基础统计
                total_materials = db.query(func.count(LocalCreative.id)).scalar()
                total_campaigns = db.query(func.count(Campaign.id)).scalar()
                total_accounts = db.query(func.count(AdAccount.id)).scalar()
                total_principals = db.query(func.count(Principal.id)).scalar()
                
                logger.info(f"📊 基础统计:")
                logger.info(f"   主体数量: {total_principals}")
                logger.info(f"   账户数量: {total_accounts}")
                logger.info(f"   素材总数: {total_materials}")
                logger.info(f"   计划总数: {total_campaigns}")
                
                # 2. 素材状态分布
                logger.info(f"📈 素材状态分布:")
                status_stats = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).order_by(desc('count')).all()
                
                for status, count in status_stats:
                    percentage = (count / total_materials * 100) if total_materials > 0 else 0
                    logger.info(f"   {status}: {count} ({percentage:.1f}%)")
                
                # 3. 计划状态分布
                logger.info(f"📋 计划状态分布:")
                campaign_stats = db.query(
                    Campaign.status,
                    func.count(Campaign.id).label('count')
                ).group_by(Campaign.status).order_by(desc('count')).all()
                
                for status, count in campaign_stats:
                    percentage = (count / total_campaigns * 100) if total_campaigns > 0 else 0
                    logger.info(f"   {status}: {count} ({percentage:.1f}%)")
                
                # 4. 文件系统状态
                self._check_filesystem_health()
                
                # 5. 识别关键问题
                self._identify_critical_issues(db)
                
        except Exception as e:
            logger.error(f"❌ 生成系统健康报告失败: {e}")
    
    def _check_filesystem_health(self):
        """检查文件系统健康状况"""
        logger.info(f"📁 文件系统健康检查:")
        
        directories = [
            '01_materials_to_process/缇萃百货',
            '03_materials_approved/缇萃百货',
            '00_materials_archived',
            'quarantine'
        ]
        
        for dir_name in directories:
            dir_path = os.path.join(self.base_dir, dir_name)
            if os.path.exists(dir_path):
                try:
                    file_count = len([f for f in os.listdir(dir_path) 
                                    if os.path.isfile(os.path.join(dir_path, f))])
                    logger.info(f"   {dir_name}: {file_count} 个文件")
                except Exception as e:
                    logger.warning(f"   {dir_name}: 无法访问 ({e})")
            else:
                logger.warning(f"   {dir_name}: 目录不存在")
    
    def _identify_critical_issues(self, db):
        """识别关键问题"""
        logger.critical(f"🚨 关键问题识别:")
        
        issues = []
        
        # 1. 孤儿记录检查
        existing_files = self._get_existing_files()
        orphaned_count = 0
        
        creatives = db.query(LocalCreative).filter(
            LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
        ).all()
        
        for creative in creatives:
            if creative.filename not in existing_files:
                orphaned_count += 1
        
        if orphaned_count > 0:
            issues.append(f"数据库孤儿记录: {orphaned_count} 条")
        
        # 2. 申诉超时问题
        appeal_timeout_count = db.query(Campaign).filter(
            Campaign.status == 'APPEAL_TIMEOUT'
        ).count()
        
        if appeal_timeout_count > 100:
            issues.append(f"申诉超时计划过多: {appeal_timeout_count} 个")
        
        # 3. 上传失败素材
        upload_failed_count = db.query(LocalCreative).filter(
            LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value
        ).count()
        
        if upload_failed_count > 50:
            issues.append(f"上传失败素材: {upload_failed_count} 个")
        
        # 4. 处理中卡住的素材
        processing_count = db.query(LocalCreative).filter(
            LocalCreative.status == MaterialStatus.PROCESSING.value
        ).count()
        
        if processing_count > 100:
            issues.append(f"处理中卡住素材: {processing_count} 个")
        
        for issue in issues:
            logger.critical(f"   ❌ {issue}")
        
        if not issues:
            logger.success(f"   ✅ 未发现关键问题")
        
        return issues
    
    def _get_existing_files(self) -> set:
        """获取实际存在的文件列表"""
        existing_files = set()
        process_dir = os.path.join(self.base_dir, '01_materials_to_process', '缇萃百货')
        
        if os.path.exists(process_dir):
            video_extensions = ['.mp4', '.mov', '.avi', '.mkv']
            for file in os.listdir(process_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    existing_files.add(file)
        
        return existing_files
    
    def execute_phase1_database_cleanup(self):
        """执行阶段1：数据库清理"""
        logger.critical("🧹 阶段1：数据库清理")
        logger.critical("=" * 60)
        
        phase_results = {
            'orphaned_cleaned': 0,
            'failed_uploads_reset': 0,
            'stuck_processing_reset': 0
        }
        
        try:
            # 1. 清理孤儿记录
            logger.info("🗑️ 清理孤儿记录...")
            orphaned_count = self._clean_orphaned_records()
            phase_results['orphaned_cleaned'] = orphaned_count
            
            # 2. 重置失败上传
            logger.info("🔄 重置失败上传...")
            reset_count = self._reset_failed_uploads()
            phase_results['failed_uploads_reset'] = reset_count
            
            # 3. 重置卡住的处理任务
            logger.info("⚡ 重置卡住的处理任务...")
            stuck_count = self._reset_stuck_processing()
            phase_results['stuck_processing_reset'] = stuck_count
            
            self.recovery_report['phases']['phase1'] = phase_results
            logger.success("✅ 阶段1完成")
            
        except Exception as e:
            logger.error(f"❌ 阶段1执行失败: {e}")
            return False
        
        return True
    
    def _clean_orphaned_records(self) -> int:
        """清理孤儿记录"""
        existing_files = self._get_existing_files()
        deleted_count = 0
        
        try:
            with database_session() as db:
                creatives = db.query(LocalCreative).filter(
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in creatives:
                    if creative.filename not in existing_files:
                        # 先删除相关的PlatformCreative记录
                        platform_creatives = db.query(PlatformCreative).filter_by(
                            local_creative_id=creative.id
                        ).all()
                        for pc in platform_creatives:
                            db.delete(pc)
                        
                        # 然后删除LocalCreative记录
                        db.delete(creative)
                        deleted_count += 1
                
                db.commit()
                logger.success(f"✅ 清理了 {deleted_count} 条孤儿记录")
                
        except Exception as e:
            logger.error(f"❌ 清理孤儿记录失败: {e}")
            return 0
        
        return deleted_count
    
    def _reset_failed_uploads(self) -> int:
        """重置失败的上传"""
        existing_files = self._get_existing_files()
        reset_count = 0
        
        try:
            with database_session() as db:
                failed_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value,
                    LocalCreative.file_path.like('%01_materials_to_process%缇萃百货%')
                ).all()
                
                for creative in failed_creatives:
                    if creative.filename in existing_files:
                        creative.status = MaterialStatus.PENDING_GROUPING.value
                        reset_count += 1
                
                db.commit()
                logger.success(f"✅ 重置了 {reset_count} 个失败上传")
                
        except Exception as e:
            logger.error(f"❌ 重置失败上传失败: {e}")
            return 0
        
        return reset_count
    
    def _reset_stuck_processing(self) -> int:
        """重置卡住的处理任务"""
        reset_count = 0
        
        try:
            with database_session() as db:
                # 重置超过1小时还在processing状态的素材
                cutoff_time = datetime.now() - timedelta(hours=1)
                
                stuck_creatives = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PROCESSING.value,
                    LocalCreative.updated_at < cutoff_time
                ).all()
                
                for creative in stuck_creatives:
                    creative.status = MaterialStatus.PENDING_GROUPING.value
                    reset_count += 1
                
                db.commit()
                logger.success(f"✅ 重置了 {reset_count} 个卡住的处理任务")
                
        except Exception as e:
            logger.error(f"❌ 重置卡住处理任务失败: {e}")
            return 0
        
        return reset_count
    
    def generate_recovery_recommendations(self):
        """生成恢复建议"""
        logger.critical("💡 系统恢复建议")
        logger.critical("=" * 60)
        
        recommendations = [
            "1. 立即执行数据库清理，移除孤儿记录",
            "2. 重启工作流服务以应用修复",
            "3. 监控素材处理流程，确保正常运转",
            "4. 检查申诉机制，优化申诉超时处理",
            "5. 定期执行系统健康检查",
            "6. 建立自动化监控告警机制",
            "7. 优化素材上传重试机制",
            "8. 清理过期的归档文件释放存储空间"
        ]
        
        for rec in recommendations:
            logger.info(f"   {rec}")
        
        self.recovery_report['recommendations'] = recommendations
    
    def execute_full_recovery(self):
        """执行完整恢复流程"""
        logger.critical("🚀 开始系统完整恢复")
        logger.critical("=" * 80)
        
        # 1. 生成健康报告
        self.generate_system_health_report()
        
        # 2. 执行数据库清理
        if self.execute_phase1_database_cleanup():
            logger.success("✅ 数据库清理完成")
        else:
            logger.error("❌ 数据库清理失败")
            return False
        
        # 3. 生成恢复建议
        self.generate_recovery_recommendations()
        
        # 4. 生成最终报告
        self._generate_final_report()
        
        logger.critical("🎉 系统恢复流程完成！")
        return True
    
    def _generate_final_report(self):
        """生成最终报告"""
        self.recovery_report['end_time'] = datetime.now()
        self.recovery_report['duration'] = (
            self.recovery_report['end_time'] - self.recovery_report['start_time']
        ).total_seconds()
        
        logger.critical("📋 恢复执行报告")
        logger.critical("=" * 60)
        logger.critical(f"开始时间: {self.recovery_report['start_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        logger.critical(f"结束时间: {self.recovery_report['end_time'].strftime('%Y-%m-%d %H:%M:%S')}")
        logger.critical(f"执行时长: {self.recovery_report['duration']:.1f} 秒")
        
        if 'phase1' in self.recovery_report['phases']:
            phase1 = self.recovery_report['phases']['phase1']
            logger.critical(f"清理统计:")
            logger.critical(f"  孤儿记录清理: {phase1['orphaned_cleaned']} 条")
            logger.critical(f"  失败上传重置: {phase1['failed_uploads_reset']} 个")
            logger.critical(f"  卡住任务重置: {phase1['stuck_processing_reset']} 个")

def main():
    """主函数"""
    try:
        recovery_manager = SystemRecoveryManager()
        success = recovery_manager.execute_full_recovery()
        
        if success:
            logger.success("🎉 系统恢复成功完成！")
            logger.info("💡 建议：重启工作流服务以应用所有修复")
        else:
            logger.error("❌ 系统恢复失败，需要人工干预")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 系统恢复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    main()
