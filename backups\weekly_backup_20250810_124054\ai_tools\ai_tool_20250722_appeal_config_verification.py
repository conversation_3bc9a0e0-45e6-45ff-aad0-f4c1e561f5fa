#!/usr/bin/env python3
"""
千川工作流计划提审功能配置执行情况验证
验证提审超时机制和目标筛选逻辑
"""

import sys
import os
from datetime import datetime, timedelta, timezone
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text

class AppealConfigVerifier:
    """提审配置验证器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流计划提审功能配置验证")
        logger.critical("=" * 60)
        self.app_settings = load_settings()
        self.appeal_max_hours = self.app_settings.get('robustness', {}).get('appeal_max_duration_hours', 10)
        self.current_time = datetime.now(timezone.utc)
    
    def verify_appeal_timeout_mechanism(self):
        """验证提审超时机制"""
        logger.critical("\n🔍 问题1：提审超时机制验证")
        logger.critical("=" * 60)
        
        logger.critical(f"📊 配置参数检查:")
        logger.critical(f"  appeal_max_duration_hours: {self.appeal_max_hours} 小时")
        logger.critical(f"  当前时间: {self.current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        
        try:
            with database_session() as db:
                # 1. 查找所有有提审记录的计划
                all_appealed_query = text("""
                    SELECT 
                        c.id,
                        c.campaign_id_qc,
                        c.status,
                        c.first_appeal_at,
                        c.created_at,
                        aa.name as account_name,
                        aa.account_type,
                        EXTRACT(EPOCH FROM (NOW() - c.first_appeal_at))/3600 as hours_since_appeal
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NOT NULL
                    AND aa.account_type = 'TEST'
                    ORDER BY c.first_appeal_at DESC
                """)
                
                all_appealed = db.execute(all_appealed_query).fetchall()
                
                logger.critical(f"📊 提审记录统计:")
                logger.critical(f"  有提审记录的计划: {len(all_appealed)} 个")
                
                # 2. 查找超过10小时仍在提审状态的计划
                timeout_threshold = self.current_time - timedelta(hours=self.appeal_max_hours)
                
                timeout_plans_query = text("""
                    SELECT 
                        c.id,
                        c.campaign_id_qc,
                        c.status,
                        c.first_appeal_at,
                        aa.name as account_name,
                        EXTRACT(EPOCH FROM (NOW() - c.first_appeal_at))/3600 as hours_since_appeal
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NOT NULL
                    AND c.first_appeal_at < :timeout_threshold
                    AND c.status NOT IN ('APPEAL_TIMEOUT', 'MONITORING', 'APPROVED', 'REJECTED')
                    AND aa.account_type = 'TEST'
                    ORDER BY c.first_appeal_at ASC
                """)
                
                timeout_plans = db.execute(timeout_plans_query, {
                    'timeout_threshold': timeout_threshold
                }).fetchall()
                
                logger.critical(f"\n🚨 超时机制验证结果:")
                logger.critical(f"  超过{self.appeal_max_hours}小时仍在提审的计划: {len(timeout_plans)} 个")
                
                if timeout_plans:
                    logger.critical("  超时计划详情:")
                    for plan in timeout_plans:
                        logger.critical(f"    📋 {plan.campaign_id_qc}:")
                        logger.critical(f"      状态: {plan.status}")
                        logger.critical(f"      账户: {plan.account_name}")
                        logger.critical(f"      首次提审: {plan.first_appeal_at}")
                        logger.critical(f"      超时时长: {plan.hours_since_appeal:.1f} 小时")
                    
                    logger.critical(f"\n❌ 超时机制未生效！")
                    logger.critical(f"  发现 {len(timeout_plans)} 个计划超过 {self.appeal_max_hours} 小时仍在提审")
                    logger.critical(f"  这些计划应该被自动标记为 APPEAL_TIMEOUT 状态")
                else:
                    logger.critical(f"✅ 超时机制正常工作")
                    logger.critical(f"  没有发现超过 {self.appeal_max_hours} 小时的提审计划")
                
                # 3. 检查已超时的计划状态分布
                timeout_status_query = text("""
                    SELECT 
                        c.status,
                        COUNT(*) as count,
                        AVG(EXTRACT(EPOCH FROM (NOW() - c.first_appeal_at))/3600) as avg_hours
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NOT NULL
                    AND c.first_appeal_at < :timeout_threshold
                    AND aa.account_type = 'TEST'
                    GROUP BY c.status
                    ORDER BY count DESC
                """)
                
                timeout_status = db.execute(timeout_status_query, {
                    'timeout_threshold': timeout_threshold
                }).fetchall()
                
                logger.critical(f"\n📊 超过{self.appeal_max_hours}小时的计划状态分布:")
                for status in timeout_status:
                    logger.critical(f"  {status.status}: {status.count} 个 (平均 {status.avg_hours:.1f} 小时)")
                
                return {
                    'total_appealed': len(all_appealed),
                    'timeout_plans': timeout_plans,
                    'timeout_count': len(timeout_plans),
                    'timeout_mechanism_working': len(timeout_plans) == 0
                }
                
        except Exception as e:
            logger.error(f"❌ 提审超时机制验证失败: {e}")
            return None
    
    def verify_appeal_target_selection(self):
        """验证提审目标筛选逻辑"""
        logger.critical("\n🔍 问题2：提审目标筛选逻辑验证")
        logger.critical("=" * 60)
        
        try:
            with database_session() as db:
                # 1. 检查账户类型分布
                account_type_query = text("""
                    SELECT 
                        aa.account_type,
                        COUNT(DISTINCT aa.id) as account_count,
                        COUNT(c.id) as total_campaigns,
                        COUNT(CASE WHEN c.first_appeal_at IS NOT NULL THEN 1 END) as appealed_campaigns
                    FROM ad_accounts aa
                    LEFT JOIN campaigns c ON aa.id = c.account_id
                    WHERE aa.status = 'active'
                    GROUP BY aa.account_type
                    ORDER BY aa.account_type
                """)
                
                account_types = db.execute(account_type_query).fetchall()
                
                logger.critical("📊 账户类型和提审情况:")
                for account in account_types:
                    appeal_rate = (account.appealed_campaigns / account.total_campaigns * 100) if account.total_campaigns > 0 else 0
                    logger.critical(f"  {account.account_type}:")
                    logger.critical(f"    账户数: {account.account_count}")
                    logger.critical(f"    总计划: {account.total_campaigns}")
                    logger.critical(f"    已提审: {account.appealed_campaigns} ({appeal_rate:.1f}%)")
                
                # 2. 检查是否对正式投放账户误提审
                formal_appeal_query = text("""
                    SELECT 
                        c.campaign_id_qc,
                        c.status,
                        c.first_appeal_at,
                        aa.name as account_name,
                        aa.account_type
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NOT NULL
                    AND aa.account_type != 'TEST'
                    ORDER BY c.first_appeal_at DESC
                """)
                
                formal_appeals = db.execute(formal_appeal_query).fetchall()
                
                logger.critical(f"\n🚨 正式投放账户误提审检查:")
                logger.critical(f"  对正式投放账户提审的计划: {len(formal_appeals)} 个")
                
                if formal_appeals:
                    logger.critical("  误提审详情:")
                    for appeal in formal_appeals:
                        logger.critical(f"    📋 {appeal.campaign_id_qc}:")
                        logger.critical(f"      账户类型: {appeal.account_type} (应该是TEST)")
                        logger.critical(f"      账户名: {appeal.account_name}")
                        logger.critical(f"      提审时间: {appeal.first_appeal_at}")
                    
                    logger.critical(f"\n❌ 目标筛选逻辑错误！")
                    logger.critical(f"  发现对 {len(formal_appeals)} 个正式投放账户计划进行了提审")
                else:
                    logger.critical(f"✅ 目标筛选逻辑正确")
                    logger.critical(f"  只对测试账户计划进行提审")
                
                # 3. 检查重复提审情况
                repeat_appeal_query = text("""
                    WITH appeal_counts AS (
                        SELECT 
                            c.campaign_id_qc,
                            c.first_appeal_at,
                            aa.name as account_name,
                            aa.account_type,
                            -- 模拟检查是否有多次提审记录（通过状态变化推断）
                            CASE 
                                WHEN c.status IN ('AUDITING', 'MONITORING') AND c.first_appeal_at IS NOT NULL THEN 1
                                ELSE 0
                            END as appeal_count
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE c.first_appeal_at IS NOT NULL
                        AND aa.account_type = 'TEST'
                    )
                    SELECT 
                        campaign_id_qc,
                        first_appeal_at,
                        account_name,
                        appeal_count
                    FROM appeal_counts
                    WHERE appeal_count > 1
                    ORDER BY first_appeal_at DESC
                """)
                
                repeat_appeals = db.execute(repeat_appeal_query).fetchall()
                
                logger.critical(f"\n📊 重复提审检查:")
                logger.critical(f"  可能重复提审的计划: {len(repeat_appeals)} 个")
                
                # 4. 检查未提审的测试计划
                never_appealed_query = text("""
                    SELECT 
                        c.campaign_id_qc,
                        c.status,
                        c.created_at,
                        aa.name as account_name,
                        EXTRACT(EPOCH FROM (NOW() - c.created_at))/3600 as hours_since_creation
                    FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE c.first_appeal_at IS NULL
                    AND aa.account_type = 'TEST'
                    AND c.status NOT IN ('DRAFT', 'DELETED')
                    AND c.created_at < NOW() - INTERVAL '1 hour'  -- 创建超过1小时
                    ORDER BY c.created_at ASC
                    LIMIT 20
                """)
                
                never_appealed = db.execute(never_appealed_query).fetchall()
                
                logger.critical(f"\n📊 未提审的测试计划:")
                logger.critical(f"  创建超过1小时但从未提审: {len(never_appealed)} 个")
                
                if never_appealed:
                    logger.critical("  未提审计划详情:")
                    for plan in never_appealed[:10]:  # 显示前10个
                        logger.critical(f"    📋 {plan.campaign_id_qc}:")
                        logger.critical(f"      状态: {plan.status}")
                        logger.critical(f"      账户: {plan.account_name}")
                        logger.critical(f"      创建时间: {plan.created_at}")
                        logger.critical(f"      创建时长: {plan.hours_since_creation:.1f} 小时")
                
                return {
                    'account_types': account_types,
                    'formal_appeals': formal_appeals,
                    'formal_appeal_count': len(formal_appeals),
                    'repeat_appeals': repeat_appeals,
                    'never_appealed': never_appealed,
                    'target_selection_correct': len(formal_appeals) == 0
                }
                
        except Exception as e:
            logger.error(f"❌ 提审目标筛选逻辑验证失败: {e}")
            return None
    
    def analyze_scheduler_code_logic(self):
        """分析调度器代码逻辑"""
        logger.critical("\n🔍 调度器代码逻辑分析")
        logger.critical("=" * 60)
        
        scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        
        try:
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找超时机制相关代码
            timeout_logic_found = False
            target_selection_logic_found = False
            
            lines = content.split('\n')
            
            logger.critical("📊 代码逻辑检查:")
            
            # 检查超时机制
            for i, line in enumerate(lines):
                if 'appeal_max_duration_hours' in line:
                    timeout_logic_found = True
                    logger.critical(f"  ✅ 找到超时机制代码 (第{i+1}行):")
                    logger.critical(f"    {line.strip()}")
                    
                    # 显示相关上下文
                    for j in range(max(0, i-2), min(len(lines), i+3)):
                        if j != i:
                            logger.critical(f"    {lines[j].strip()}")
                    break
            
            # 检查账户类型筛选
            for i, line in enumerate(lines):
                if 'account_type' in line and 'TEST' in line:
                    target_selection_logic_found = True
                    logger.critical(f"  ✅ 找到账户类型筛选代码 (第{i+1}行):")
                    logger.critical(f"    {line.strip()}")
                    break
            
            # 检查提审条件
            appeal_conditions = []
            for i, line in enumerate(lines):
                if 'first_appeal_at' in line and ('IS NULL' in line or 'is None' in line):
                    appeal_conditions.append(f"第{i+1}行: {line.strip()}")
            
            logger.critical(f"\n📊 提审条件检查:")
            if appeal_conditions:
                logger.critical(f"  找到提审条件: {len(appeal_conditions)} 处")
                for condition in appeal_conditions:
                    logger.critical(f"    {condition}")
            else:
                logger.critical(f"  ❌ 未找到first_appeal_at检查逻辑")
            
            return {
                'timeout_logic_found': timeout_logic_found,
                'target_selection_logic_found': target_selection_logic_found,
                'appeal_conditions_count': len(appeal_conditions)
            }
            
        except Exception as e:
            logger.error(f"❌ 代码逻辑分析失败: {e}")
            return None
    
    def generate_fix_recommendations(self, timeout_result, target_result, code_analysis):
        """生成修复建议"""
        logger.critical("\n💡 修复建议")
        logger.critical("=" * 60)
        
        issues_found = []
        recommendations = []
        
        # 检查超时机制问题
        if timeout_result and not timeout_result['timeout_mechanism_working']:
            issues_found.append(f"超时机制未生效：{timeout_result['timeout_count']} 个计划超时")
            recommendations.append({
                'priority': 'HIGH',
                'issue': '提审超时机制未生效',
                'action': '修复scheduler.py中的超时检查逻辑',
                'code_fix': '''
# 在scheduler.py中添加或修复超时检查
if plan.first_appeal_at and (datetime.now(timezone.utc) - plan.first_appeal_at) > timedelta(hours=app_settings['robustness']['appeal_max_duration_hours']):
    logger.warning(f"计划 {plan.campaign_id_qc} 首次提审已超过 {app_settings['robustness']['appeal_max_duration_hours']} 小时，判定为超时。")
    plan.status = 'APPEAL_TIMEOUT'
    db.commit()
    continue
'''
            })
        
        # 检查目标筛选问题
        if target_result and not target_result['target_selection_correct']:
            issues_found.append(f"目标筛选错误：对 {target_result['formal_appeal_count']} 个正式账户计划提审")
            recommendations.append({
                'priority': 'CRITICAL',
                'issue': '对正式投放账户误提审',
                'action': '修复账户类型筛选逻辑',
                'code_fix': '''
# 确保只对测试账户提审
WHERE aa.account_type = 'TEST'
AND c.first_appeal_at IS NULL
'''
            })
        
        # 检查代码逻辑问题
        if code_analysis:
            if not code_analysis['timeout_logic_found']:
                issues_found.append("代码中缺少超时机制实现")
                recommendations.append({
                    'priority': 'HIGH',
                    'issue': '代码中缺少超时机制',
                    'action': '在scheduler.py中实现超时检查逻辑'
                })
            
            if not code_analysis['target_selection_logic_found']:
                issues_found.append("代码中缺少账户类型筛选")
                recommendations.append({
                    'priority': 'CRITICAL',
                    'issue': '缺少账户类型筛选',
                    'action': '添加account_type = TEST筛选条件'
                })
        
        # 生成报告
        logger.critical("🎯 问题总结:")
        if issues_found:
            for i, issue in enumerate(issues_found, 1):
                logger.critical(f"  {i}. {issue}")
        else:
            logger.critical("  ✅ 未发现配置执行问题")
        
        logger.critical("\n💡 修复建议:")
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                logger.critical(f"  {i}. [{rec['priority']}] {rec['issue']}")
                logger.critical(f"     建议: {rec['action']}")
                if 'code_fix' in rec:
                    logger.critical(f"     代码修复:")
                    for line in rec['code_fix'].strip().split('\n'):
                        logger.critical(f"       {line}")
        else:
            logger.critical("  ✅ 当前配置执行正常，无需修复")
        
        return {
            'issues_count': len(issues_found),
            'recommendations': recommendations,
            'overall_status': 'NEEDS_FIX' if issues_found else 'HEALTHY'
        }
    
    def generate_comprehensive_report(self, results):
        """生成综合报告"""
        logger.critical("\n📋 千川工作流提审功能配置验证报告")
        logger.critical("=" * 60)
        
        timeout_result = results.get('timeout_result')
        target_result = results.get('target_result')
        code_analysis = results.get('code_analysis')
        fix_recommendations = results.get('fix_recommendations')
        
        logger.critical("🎯 验证概要:")
        logger.critical(f"  验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.critical(f"  配置参数: appeal_max_duration_hours = {self.appeal_max_hours}")
        
        if timeout_result:
            logger.critical(f"  提审记录: {timeout_result['total_appealed']} 个计划")
            logger.critical(f"  超时计划: {timeout_result['timeout_count']} 个")
            logger.critical(f"  超时机制: {'✅ 正常' if timeout_result['timeout_mechanism_working'] else '❌ 异常'}")
        
        if target_result:
            logger.critical(f"  误提审计划: {target_result['formal_appeal_count']} 个")
            logger.critical(f"  目标筛选: {'✅ 正确' if target_result['target_selection_correct'] else '❌ 错误'}")
        
        logger.critical(f"\n📊 详细验证结果:")
        
        # 问题1结果
        logger.critical("  问题1 - 提审超时机制:")
        if timeout_result:
            if timeout_result['timeout_mechanism_working']:
                logger.critical("    ✅ 超时机制正常工作")
                logger.critical(f"    ✅ 没有发现超过{self.appeal_max_hours}小时的提审计划")
            else:
                logger.critical("    ❌ 超时机制未生效")
                logger.critical(f"    ❌ 发现{timeout_result['timeout_count']}个超时计划")
        
        # 问题2结果
        logger.critical("  问题2 - 提审目标筛选:")
        if target_result:
            if target_result['target_selection_correct']:
                logger.critical("    ✅ 目标筛选逻辑正确")
                logger.critical("    ✅ 只对测试账户计划进行提审")
            else:
                logger.critical("    ❌ 目标筛选逻辑错误")
                logger.critical(f"    ❌ 对{target_result['formal_appeal_count']}个正式账户计划误提审")
        
        # 总体状态
        if fix_recommendations:
            overall_status = fix_recommendations['overall_status']
            logger.critical(f"\n🏥 总体状态: {overall_status}")
            
            if overall_status == 'NEEDS_FIX':
                logger.critical(f"  发现问题: {fix_recommendations['issues_count']} 个")
                logger.critical("  建议立即修复相关问题")
            else:
                logger.critical("  配置执行正常，系统运行健康")

def main():
    """主验证函数"""
    try:
        verifier = AppealConfigVerifier()
        
        results = {}
        
        # 问题1：验证提审超时机制
        results['timeout_result'] = verifier.verify_appeal_timeout_mechanism()
        
        # 问题2：验证提审目标筛选逻辑
        results['target_result'] = verifier.verify_appeal_target_selection()
        
        # 分析调度器代码逻辑
        results['code_analysis'] = verifier.analyze_scheduler_code_logic()
        
        # 生成修复建议
        results['fix_recommendations'] = verifier.generate_fix_recommendations(
            results['timeout_result'],
            results['target_result'],
            results['code_analysis']
        )
        
        # 生成综合报告
        verifier.generate_comprehensive_report(results)
        
        logger.critical(f"\n🎉 千川工作流提审功能配置验证完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return False

if __name__ == "__main__":
    main()
