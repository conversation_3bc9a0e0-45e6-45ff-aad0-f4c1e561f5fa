#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 验证工具
生命周期: 7天
创建目的: 验证Celery架构修复效果
清理条件: 验证完成后删除
"""

import os
import sys
import psutil
import time
from datetime import datetime, timezone
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AdAccount, Campaign


class CeleryFixVerifier:
    """Celery修复效果验证器"""
    
    def __init__(self):
        self.verification_results = {}
    
    def run_comprehensive_verification(self) -> Dict[str, Any]:
        """运行全面的修复效果验证"""
        logger.info("🔍 开始Celery修复效果验证")
        logger.info("="*80)
        
        verification_results = {
            'timestamp': datetime.now(timezone.utc),
            'celery_processes': self._check_celery_processes(),
            'browser_processes': self._check_browser_processes(),
            'test_account_compliance': self._verify_test_account_compliance(),
            'task_configuration': self._check_task_configuration(),
            'code_modifications': self._verify_code_modifications(),
            'overall_status': 'UNKNOWN'
        }
        
        # 计算总体状态
        verification_results['overall_status'] = self._calculate_overall_status(verification_results)
        
        # 输出验证报告
        self._output_verification_report(verification_results)
        
        return verification_results
    
    def _check_celery_processes(self) -> Dict[str, Any]:
        """检查Celery进程状态"""
        logger.info("🔍 检查Celery进程状态...")
        
        worker_processes = []
        beat_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info']):
                try:
                    proc_info = proc.info
                    cmdline = ' '.join(proc_info.get('cmdline', []))
                    
                    if 'run_celery_worker.py' in cmdline or 'celery worker' in cmdline:
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        worker_processes.append({
                            'pid': proc_info['pid'],
                            'memory_mb': round(memory_mb, 1)
                        })
                    elif 'run_celery_beat.py' in cmdline or 'celery beat' in cmdline:
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        beat_processes.append({
                            'pid': proc_info['pid'],
                            'memory_mb': round(memory_mb, 1)
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            status = "HEALTHY"
            issues = []
            
            if len(worker_processes) == 0:
                status = "ERROR"
                issues.append("没有Celery Worker进程运行")
            elif len(worker_processes) > 1:
                status = "WARNING"
                issues.append(f"发现多个Worker进程: {len(worker_processes)}")
            
            if len(beat_processes) == 0:
                status = "ERROR"
                issues.append("没有Celery Beat进程运行")
            elif len(beat_processes) > 1:
                status = "WARNING"
                issues.append(f"发现多个Beat进程: {len(beat_processes)}")
            
            logger.info(f"   Worker进程: {len(worker_processes)} 个")
            logger.info(f"   Beat进程: {len(beat_processes)} 个")
            logger.info(f"   状态: {status}")
            
            return {
                'worker_processes': worker_processes,
                'beat_processes': beat_processes,
                'status': status,
                'issues': issues
            }
            
        except Exception as e:
            logger.error(f"检查Celery进程失败: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _check_browser_processes(self) -> Dict[str, Any]:
        """检查浏览器进程状态"""
        logger.info("🔍 检查浏览器进程状态...")
        
        browser_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info['name'].lower()
                    
                    if any(browser in name for browser in ['chrome', 'chromium']):
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage']):
                            memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                            browser_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'memory_mb': round(memory_mb, 1)
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            total_memory = sum(p['memory_mb'] for p in browser_processes)
            browser_count = len(browser_processes)
            
            status = "HEALTHY"
            if browser_count > 5:
                status = "WARNING"
            elif browser_count > 8:
                status = "ERROR"
            
            logger.info(f"   浏览器进程数: {browser_count}")
            logger.info(f"   总内存使用: {total_memory:.1f}MB")
            logger.info(f"   状态: {status}")
            
            return {
                'browser_count': browser_count,
                'total_memory_mb': total_memory,
                'processes': browser_processes,
                'status': status
            }
            
        except Exception as e:
            logger.error(f"检查浏览器进程失败: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _verify_test_account_compliance(self) -> Dict[str, Any]:
        """验证测试账户合规性"""
        logger.info("🛡️ 验证测试账户合规性...")
        
        try:
            with SessionLocal() as db:
                # 检查所有在提审状态的计划
                campaigns_in_appeal = db.query(Campaign, AdAccount).join(AdAccount).filter(
                    Campaign.appeal_status.isnot(None)
                ).all()
                
                test_appeals = 0
                non_test_appeals = 0
                violations = []
                
                for campaign, account in campaigns_in_appeal:
                    if account.account_type == 'TEST':
                        test_appeals += 1
                    else:
                        non_test_appeals += 1
                        violations.append({
                            'campaign_id_qc': campaign.campaign_id_qc,
                            'account_type': account.account_type,
                            'account_name': account.name
                        })
                
                status = "COMPLIANT" if non_test_appeals == 0 else "VIOLATION"
                
                logger.info(f"   测试账户提审: {test_appeals}")
                logger.info(f"   非测试账户提审: {non_test_appeals}")
                logger.info(f"   合规状态: {status}")
                
                return {
                    'total_appeals': len(campaigns_in_appeal),
                    'test_appeals': test_appeals,
                    'non_test_appeals': non_test_appeals,
                    'violations': violations,
                    'status': status
                }
                
        except Exception as e:
            logger.error(f"测试账户合规性检查失败: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _check_task_configuration(self) -> Dict[str, Any]:
        """检查任务配置"""
        logger.info("🔍 检查任务配置...")
        
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            
            workflow_config = settings.get('workflow', {})
            
            # 检查关键任务间隔
            appeal_interval = workflow_config.get('plan_appeal', {}).get('interval_seconds', 0)
            monitoring_interval = workflow_config.get('material_monitoring', {}).get('interval_seconds', 0)
            
            config_status = "OPTIMAL"
            issues = []
            
            if appeal_interval < 900:  # 小于15分钟
                config_status = "SUBOPTIMAL"
                issues.append(f"提审任务间隔过短: {appeal_interval}s")
            
            if monitoring_interval < 120:  # 小于2分钟
                config_status = "SUBOPTIMAL"
                issues.append(f"监控任务间隔过短: {monitoring_interval}s")
            
            logger.info(f"   提审任务间隔: {appeal_interval}s")
            logger.info(f"   监控任务间隔: {monitoring_interval}s")
            logger.info(f"   配置状态: {config_status}")
            
            return {
                'appeal_interval': appeal_interval,
                'monitoring_interval': monitoring_interval,
                'status': config_status,
                'issues': issues
            }
            
        except Exception as e:
            logger.error(f"任务配置检查失败: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _verify_code_modifications(self) -> Dict[str, Any]:
        """验证代码修改"""
        logger.info("🔍 验证代码修改...")
        
        modifications_verified = {
            'tasks_py_browser_cleanup': False,
            'tasks_py_test_account_check': False,
            'scheduler_py_test_account_limit': False
        }
        
        try:
            # 检查tasks.py中的浏览器清理函数
            tasks_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/tasks.py')
            if os.path.exists(tasks_file):
                with open(tasks_file, 'r', encoding='utf-8') as f:
                    tasks_content = f.read()
                
                if '_cleanup_excess_browsers' in tasks_content:
                    modifications_verified['tasks_py_browser_cleanup'] = True
                
                if 'test_account_limiter.get_test_account_ids()' in tasks_content:
                    modifications_verified['tasks_py_test_account_check'] = True
            
            # 检查scheduler.py中的测试账户限制
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            if os.path.exists(scheduler_file):
                with open(scheduler_file, 'r', encoding='utf-8') as f:
                    scheduler_content = f.read()
                
                if 'Campaign.account_id.in_(test_account_ids)' in scheduler_content:
                    modifications_verified['scheduler_py_test_account_limit'] = True
            
            verified_count = sum(modifications_verified.values())
            total_count = len(modifications_verified)
            
            status = "COMPLETE" if verified_count == total_count else "PARTIAL"
            
            logger.info(f"   代码修改验证: {verified_count}/{total_count}")
            logger.info(f"   修改状态: {status}")
            
            return {
                'modifications': modifications_verified,
                'verified_count': verified_count,
                'total_count': total_count,
                'status': status
            }
            
        except Exception as e:
            logger.error(f"代码修改验证失败: {e}")
            return {'status': 'ERROR', 'error': str(e)}
    
    def _calculate_overall_status(self, results: Dict[str, Any]) -> str:
        """计算总体状态"""
        critical_issues = 0
        warnings = 0
        
        # 检查关键组件状态
        if results['celery_processes'].get('status') == 'ERROR':
            critical_issues += 1
        elif results['celery_processes'].get('status') == 'WARNING':
            warnings += 1
        
        if results['test_account_compliance'].get('status') == 'VIOLATION':
            critical_issues += 1
        
        if results['code_modifications'].get('status') == 'PARTIAL':
            warnings += 1
        
        if critical_issues > 0:
            return 'CRITICAL'
        elif warnings > 0:
            return 'WARNING'
        else:
            return 'HEALTHY'
    
    def _output_verification_report(self, results: Dict[str, Any]):
        """输出验证报告"""
        logger.info("\n📋 Celery修复效果验证报告")
        logger.info("="*80)
        
        overall_status = results['overall_status']
        status_icon = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '🚨'
        }.get(overall_status, '❓')
        
        logger.info(f"{status_icon} 总体状态: {overall_status}")
        
        # 详细状态
        logger.info(f"📊 详细检查结果:")
        logger.info(f"   Celery进程: {results['celery_processes'].get('status', 'UNKNOWN')}")
        logger.info(f"   浏览器进程: {results['browser_processes'].get('status', 'UNKNOWN')}")
        logger.info(f"   测试账户合规: {results['test_account_compliance'].get('status', 'UNKNOWN')}")
        logger.info(f"   任务配置: {results['task_configuration'].get('status', 'UNKNOWN')}")
        logger.info(f"   代码修改: {results['code_modifications'].get('status', 'UNKNOWN')}")
        
        # 建议
        if overall_status == 'HEALTHY':
            logger.success("🎉 修复效果良好，系统运行正常")
        elif overall_status == 'WARNING':
            logger.warning("⚠️ 修复基本成功，但存在一些需要关注的问题")
        else:
            logger.error("🚨 修复效果不理想，需要进一步处理")


def main():
    """主函数"""
    verifier = CeleryFixVerifier()
    results = verifier.run_comprehensive_verification()
    
    overall_status = results['overall_status']
    if overall_status == 'HEALTHY':
        logger.success("✅ Celery修复验证通过")
        return 0
    elif overall_status == 'WARNING':
        logger.warning("⚠️ Celery修复基本成功，但需要关注")
        return 1
    else:
        logger.error("❌ Celery修复验证失败")
        return 2


if __name__ == "__main__":
    exit(main())
