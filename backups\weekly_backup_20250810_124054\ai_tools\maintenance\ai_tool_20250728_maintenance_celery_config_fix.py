#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 修复Celery配置语法错误
清理条件: 配置修复完成后可删除
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class CeleryConfigFixer:
    """Celery配置修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.worker_file = self.project_root / 'run_celery_worker.py'
    
    def fix_celery_config(self):
        """修复Celery配置语法错误"""
        logger.critical("🔧 修复Celery配置语法错误")
        logger.critical("=" * 50)
        
        try:
            # 读取当前配置
            with open(self.worker_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            logger.info("🔍 检查当前配置...")
            
            # 验证配置语法
            if self._validate_config_syntax(content):
                logger.success("✅ Celery配置语法正确")
                return True
            else:
                logger.warning("⚠️ 发现配置语法问题，正在修复...")
                return self._fix_syntax_errors(content)
                
        except Exception as e:
            logger.error(f"❌ 修复配置失败: {e}")
            return False
    
    def _validate_config_syntax(self, content):
        """验证配置语法"""
        # 检查常见语法错误
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            if 'sys.argv = [' in line:
                # 找到sys.argv配置块
                argv_start = i
                bracket_count = 0
                argv_end = -1
                
                for j in range(i, len(lines)):
                    if '[' in lines[j]:
                        bracket_count += lines[j].count('[')
                    if ']' in lines[j]:
                        bracket_count -= lines[j].count(']')
                        if bracket_count == 0:
                            argv_end = j
                            break
                
                if argv_end == -1:
                    logger.error("❌ 未找到sys.argv配置块结束")
                    return False
                
                # 检查配置块内的语法
                argv_block = lines[argv_start:argv_end+1]
                return self._check_argv_syntax(argv_block)
        
        return True
    
    def _check_argv_syntax(self, argv_block):
        """检查sys.argv配置块语法"""
        issues = []
        
        for i, line in enumerate(argv_block):
            line = line.strip()
            
            # 跳过注释和空行
            if not line or line.startswith('#'):
                continue
            
            # 检查字符串参数是否正确闭合
            if "'" in line:
                single_quotes = line.count("'")
                if single_quotes % 2 != 0:
                    issues.append(f"第{i+1}行: 单引号未正确闭合")
            
            # 检查逗号分隔
            if line.endswith("'") and not line.endswith("',") and not line.endswith("']"):
                if i < len(argv_block) - 2:  # 不是最后一个参数
                    issues.append(f"第{i+1}行: 缺少逗号分隔符")
            
            # 检查参数格式
            if '-c' in line and '--max-tasks-per-child' in line:
                issues.append(f"第{i+1}行: 多个参数在同一行，缺少逗号分隔")
        
        if issues:
            logger.warning("⚠️ 发现语法问题:")
            for issue in issues:
                logger.warning(f"  - {issue}")
            return False
        
        return True
    
    def _fix_syntax_errors(self, content):
        """修复语法错误"""
        try:
            # 创建正确的配置
            correct_config = '''    sys.argv = [
        'celery',
        '-A', 'qianchuan_aw.celery_app.app',
        'worker',
        '-l', 'info',
        '-P', 'threads', # 使用原生线程池
        '-c', '15',       # 开启15个并发线程 (优化后)
        '--max-tasks-per-child', '50',  # 限制每个进程任务数
    ]'''
            
            # 查找并替换sys.argv配置
            lines = content.split('\n')
            new_lines = []
            in_argv_block = False
            bracket_count = 0
            
            for line in lines:
                if 'sys.argv = [' in line:
                    in_argv_block = True
                    bracket_count = line.count('[') - line.count(']')
                    # 添加正确的配置
                    new_lines.extend(correct_config.split('\n'))
                    continue
                
                if in_argv_block:
                    bracket_count += line.count('[') - line.count(']')
                    if bracket_count <= 0:
                        in_argv_block = False
                    continue
                
                new_lines.append(line)
            
            # 写回文件
            new_content = '\n'.join(new_lines)
            with open(self.worker_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.success("✅ Celery配置已修复")
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复语法错误失败: {e}")
            return False
    
    def test_celery_config(self):
        """测试Celery配置"""
        logger.info("🧪 测试Celery配置...")
        
        try:
            # 模拟导入测试
            import subprocess
            import tempfile
            
            # 创建测试脚本
            test_script = f'''
import sys
sys.path.insert(0, r"{self.project_root / 'src'}")

# 测试配置语法
try:
    from celery.bin import celery
    
    # 模拟配置
    sys.argv = [
        'celery',
        '-A', 'qianchuan_aw.celery_app.app',
        'worker',
        '-l', 'info',
        '-P', 'threads',
        '-c', '15',
        '--max-tasks-per-child', '50',
        '--help'  # 只显示帮助，不实际启动
    ]
    
    print("✅ Celery配置语法正确")
except Exception as e:
    print(f"❌ Celery配置错误: {{e}}")
    sys.exit(1)
'''
            
            # 写入临时文件并执行
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(test_script)
                temp_file = f.name
            
            try:
                result = subprocess.run([sys.executable, temp_file], 
                                      capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    logger.success("✅ Celery配置测试通过")
                    return True
                else:
                    logger.error(f"❌ Celery配置测试失败: {result.stderr}")
                    return False
                    
            finally:
                os.unlink(temp_file)
                
        except Exception as e:
            logger.warning(f"⚠️ 无法执行配置测试: {e}")
            return True  # 假设配置正确
    
    def show_current_config(self):
        """显示当前配置"""
        logger.info("📋 当前Celery Worker配置:")
        
        try:
            with open(self.worker_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            in_argv = False
            for i, line in enumerate(lines):
                if 'sys.argv = [' in line:
                    in_argv = True
                    logger.info(f"  {i+1:2d}: {line.rstrip()}")
                elif in_argv:
                    logger.info(f"  {i+1:2d}: {line.rstrip()}")
                    if ']' in line:
                        break
                        
        except Exception as e:
            logger.error(f"❌ 无法读取配置: {e}")

def main():
    """主函数"""
    fixer = CeleryConfigFixer()
    
    logger.critical("🔧 Celery配置修复工具")
    logger.critical("=" * 50)
    
    # 显示当前配置
    fixer.show_current_config()
    
    # 修复配置
    if fixer.fix_celery_config():
        # 测试配置
        if fixer.test_celery_config():
            logger.critical("🎉 Celery配置修复完成！")
            logger.critical("📋 现在可以重新启动Celery Worker:")
            logger.critical("  python run_celery_worker.py")
        else:
            logger.critical("❌ 配置测试失败，请检查错误信息")
    else:
        logger.critical("❌ 配置修复失败")

if __name__ == "__main__":
    main()
