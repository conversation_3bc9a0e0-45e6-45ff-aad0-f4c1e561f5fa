
-- 建议添加的数据库约束，防止重复计划创建

-- 1. 在local_creatives表添加唯一约束
-- ALTER TABLE local_creatives ADD CONSTRAINT unique_file_hash UNIQUE (file_hash);

-- 2. 创建函数检查重复计划
CREATE OR REPLACE FUNCTION check_duplicate_campaign()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查是否已存在相同file_hash的计划
    IF EXISTS (
        SELECT 1 
        FROM campaigns c
        JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
        JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
        JOIN local_creatives lc ON pc.local_creative_id = lc.id
        WHERE lc.file_hash = (
            SELECT lc2.file_hash 
            FROM local_creatives lc2
            JOIN platform_creatives pc2 ON lc2.id = pc2.local_creative_id
            WHERE pc2.id = NEW.platform_creative_id
        )
    ) THEN
        RAISE EXCEPTION '重复计划创建：该视频文件已存在测试计划';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. 创建触发器
-- CREATE TRIGGER prevent_duplicate_campaigns
--     BEFORE INSERT ON campaign_platform_creative_association
--     FOR EACH ROW
--     EXECUTE FUNCTION check_duplicate_campaign();
