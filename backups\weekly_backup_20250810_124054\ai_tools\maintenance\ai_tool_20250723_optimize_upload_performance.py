#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 优化视频上传性能，解决上传效率低下问题
清理条件: 性能优化完成后可归档
"""

import os
import sys
import yaml
import time
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import load_settings

class UploadPerformanceOptimizer:
    """视频上传性能优化器"""
    
    def __init__(self):
        self.app_settings = load_settings()
        self.config_path = os.path.join(project_root, 'config', 'settings.yml')
        self.stats = {
            'config_updates': 0,
            'invalid_videos_found': 0,
            'optimization_applied': 0
        }
    
    def analyze_current_performance(self):
        """分析当前性能配置"""
        logger.info("📊 分析当前上传性能配置...")
        
        current_config = {
            'api_rate_limits': self.app_settings.get('rate_limiting', {}),
            'robustness': self.app_settings.get('robustness', {}),
            'workflow': self.app_settings.get('workflow', {}),
            'video_validation': self.app_settings.get('workflow', {}).get('video_validation', {})
        }
        
        logger.info("当前配置:")
        logger.info(f"  上传API限制: {current_config['api_rate_limits'].get('endpoints', {}).get('upload_video', {})}")
        logger.info(f"  最大重试次数: {current_config['robustness'].get('max_retries_for_upload', 3)}")
        logger.info(f"  重试延迟: {current_config['robustness'].get('upload_retry_delay', 30)}秒")
        logger.info(f"  最大上传工作者: {current_config['workflow'].get('max_upload_workers', 15)}")
        
        return current_config
    
    def create_optimized_config(self) -> Dict[str, Any]:
        """创建优化的配置"""
        logger.info("🔧 创建优化的性能配置...")
        
        optimized_config = {
            # 优化API频率限制
            'rate_limiting': {
                'enabled': True,
                'global': {
                    'enabled': True,
                    'rate': 8,      # 从5提升到8
                    'capacity': 30  # 从20提升到30
                },
                'endpoints': {
                    'upload_video': {
                        'enabled': True,
                        'rate': 5,      # 从3提升到5
                        'capacity': 25  # 从15提升到25
                    },
                    'create_campaign': {
                        'enabled': True,
                        'rate': 3,      # 从2提升到3
                        'capacity': 15  # 从10提升到15
                    },
                    'get_materials_in_ad': {
                        'enabled': True,
                        'rate': 2,      # 从1提升到2
                        'capacity': 8   # 从5提升到8
                    }
                },
                'error_handling': {
                    'backoff_factor': 1.5,  # 从2.0降低到1.5，更快恢复
                    'min_rate': 1,
                    'recovery_time': 300    # 从600降低到300秒
                }
            },
            
            # 优化重试机制
            'robustness': {
                'max_retries_for_upload': 5,    # 从3提升到5
                'upload_retry_delay': 15,       # 从30降低到15秒
                'initial_retry_delay': 5,       # 从10降低到5秒
                'api_sync_delay': 3,            # 从5降低到3秒
                'plan_creation_interval': 20,   # 从25降低到20秒
                'monitoring_interval': 300      # 从600降低到300秒
            },
            
            # 优化工作流配置
            'workflow': {
                'max_upload_workers': 20,       # 从15提升到20
                'file_ingestion': {
                    'enabled': True,
                    'interval_seconds': 90,     # 从120降低到90秒
                    'batch_size': 50            # 新增批处理大小
                },
                'group_dispatch': {
                    'enabled': True,
                    'interval_seconds': 45,     # 从60降低到45秒
                    'batch_size': 30            # 新增批处理大小
                },
                'video_validation': {
                    'enabled': True,
                    'auto_quarantine': True,
                    'pre_upload_validation': True,  # 新增：上传前验证
                    'min_duration_seconds': 4,
                    'max_duration_seconds': 300,
                    'max_file_size_mb': 500,
                    'quarantine_dir': 'quarantine/invalid_videos',
                    'validation_cache': True    # 新增：验证结果缓存
                }
            }
        }
        
        return optimized_config
    
    def scan_invalid_videos(self) -> List[str]:
        """扫描无效视频文件"""
        logger.info("🔍 扫描无效视频文件...")
        
        invalid_videos = []
        workflow_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        process_dir = os.path.join(workflow_dir, '01_materials_to_process')
        
        if not os.path.exists(process_dir):
            logger.warning(f"处理目录不存在: {process_dir}")
            return invalid_videos
        
        try:
            import cv2
            video_extensions = ['.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv']
            
            for root, dirs, files in os.walk(process_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        file_path = os.path.join(root, file)
                        
                        try:
                            # 使用OpenCV检查视频
                            cap = cv2.VideoCapture(file_path)
                            if not cap.isOpened():
                                invalid_videos.append(f"{file_path} - 无法打开")
                                continue
                            
                            # 获取视频信息
                            fps = cap.get(cv2.CAP_PROP_FPS)
                            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                            duration = frame_count / fps if fps > 0 else 0
                            
                            cap.release()
                            
                            # 检查时长
                            if duration < 4 or duration > 300:
                                invalid_videos.append(f"{file_path} - 时长{duration:.1f}秒不符合要求")
                            
                            # 检查文件大小
                            file_size_mb = os.path.getsize(file_path) / (1024 * 1024)
                            if file_size_mb > 500:
                                invalid_videos.append(f"{file_path} - 文件大小{file_size_mb:.1f}MB超限")
                                
                        except Exception as e:
                            invalid_videos.append(f"{file_path} - 检查失败: {e}")
            
            self.stats['invalid_videos_found'] = len(invalid_videos)
            
        except ImportError:
            logger.warning("OpenCV未安装，跳过视频验证。建议安装: pip install opencv-python")
        
        return invalid_videos
    
    def quarantine_invalid_videos(self, invalid_videos: List[str]):
        """隔离无效视频"""
        if not invalid_videos:
            logger.info("✅ 没有发现无效视频")
            return
        
        logger.info(f"🚫 发现 {len(invalid_videos)} 个无效视频，开始隔离...")
        
        workflow_dir = self.app_settings.get('custom_workflow_assets_dir', 'G:/workflow_assets')
        quarantine_dir = os.path.join(workflow_dir, 'quarantine', 'invalid_videos')
        os.makedirs(quarantine_dir, exist_ok=True)
        
        quarantined_count = 0
        
        for invalid_info in invalid_videos:
            try:
                # 解析文件路径和原因
                if ' - ' in invalid_info:
                    file_path, reason = invalid_info.split(' - ', 1)
                else:
                    file_path, reason = invalid_info, "未知原因"
                
                if not os.path.exists(file_path):
                    continue
                
                # 移动到隔离目录
                filename = os.path.basename(file_path)
                quarantine_path = os.path.join(quarantine_dir, filename)
                
                # 如果目标已存在，添加时间戳
                if os.path.exists(quarantine_path):
                    name, ext = os.path.splitext(filename)
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    quarantine_path = os.path.join(quarantine_dir, f"{name}_{timestamp}{ext}")
                
                import shutil
                shutil.move(file_path, quarantine_path)
                
                # 创建原因文件
                reason_file = quarantine_path + ".reason.txt"
                with open(reason_file, 'w', encoding='utf-8') as f:
                    f.write(f"隔离原因: {reason}\n")
                    f.write(f"原路径: {file_path}\n")
                    f.write(f"隔离时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                
                logger.info(f"  隔离: {filename} - {reason}")
                quarantined_count += 1
                
            except Exception as e:
                logger.error(f"隔离文件失败 {file_path}: {e}")
        
        logger.success(f"✅ 成功隔离 {quarantined_count} 个无效视频")
    
    def apply_optimized_config(self, optimized_config: Dict[str, Any]):
        """应用优化配置"""
        logger.info("⚙️ 应用优化配置...")
        
        try:
            # 读取当前配置
            with open(self.config_path, 'r', encoding='utf-8') as f:
                current_config = yaml.safe_load(f)
            
            # 备份当前配置
            backup_path = f"{self.config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_path, 'w', encoding='utf-8') as f:
                yaml.dump(current_config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"📦 已备份当前配置到: {backup_path}")
            
            # 更新配置
            for section, values in optimized_config.items():
                if section in current_config:
                    if isinstance(values, dict):
                        current_config[section].update(values)
                    else:
                        current_config[section] = values
                else:
                    current_config[section] = values
                self.stats['config_updates'] += 1
            
            # 保存优化后的配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(current_config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success("✅ 优化配置已应用")
            self.stats['optimization_applied'] = 1
            
        except Exception as e:
            logger.error(f"应用配置失败: {e}", exc_info=True)
    
    def create_performance_monitoring_script(self):
        """创建性能监控脚本"""
        logger.info("📊 创建性能监控脚本...")
        
        monitoring_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""上传性能监控脚本"""

import time
from datetime import datetime, timedelta
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.utils.logger import logger
from sqlalchemy import text

def monitor_upload_performance():
    """监控上传性能"""
    try:
        with database_session() as db:
            # 统计最近1小时的上传情况
            stats_query = text("""
                SELECT 
                    COUNT(*) as total_uploads,
                    COUNT(CASE WHEN status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 1 END) as successful_uploads,
                    COUNT(CASE WHEN status = MaterialStatus.UPLOAD_FAILED.value THEN 1 END) as failed_uploads,
                    COUNT(CASE WHEN status = MaterialStatus.PROCESSING.value THEN 1 END) as processing_uploads
                FROM local_creatives 
                WHERE updated_at > NOW() - INTERVAL '1 hour'
            """)
            
            result = db.execute(stats_query).fetchone()
            
            success_rate = (result.successful_uploads / result.total_uploads * 100) if result.total_uploads > 0 else 0
            
            logger.info(f"📊 最近1小时上传统计:")
            logger.info(f"  总上传数: {result.total_uploads}")
            logger.info(f"  成功上传: {result.successful_uploads}")
            logger.info(f"  失败上传: {result.failed_uploads}")
            logger.info(f"  处理中: {result.processing_uploads}")
            logger.info(f"  成功率: {success_rate:.1f}%")
            
            return success_rate
            
    except Exception as e:
        logger.error(f"监控上传性能失败: {e}")
        return 0

if __name__ == "__main__":
    monitor_upload_performance()
'''
        
        script_path = os.path.join(project_root, 'ai_tools', 'maintenance', 'upload_performance_monitor.py')
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.success(f"✅ 性能监控脚本已创建: {script_path}")
    
    def run_complete_optimization(self):
        """运行完整的性能优化"""
        logger.info("🚀 开始视频上传性能优化...")
        logger.info("=" * 60)
        
        try:
            # 1. 分析当前性能
            current_config = self.analyze_current_performance()
            
            # 2. 扫描并隔离无效视频
            invalid_videos = self.scan_invalid_videos()
            self.quarantine_invalid_videos(invalid_videos)
            
            # 3. 创建并应用优化配置
            optimized_config = self.create_optimized_config()
            self.apply_optimized_config(optimized_config)
            
            # 4. 创建性能监控脚本
            self.create_performance_monitoring_script()
            
            # 5. 输出统计信息
            logger.info("\n📊 优化统计:")
            logger.info(f"  配置更新数: {self.stats['config_updates']}")
            logger.info(f"  无效视频隔离: {self.stats['invalid_videos_found']}")
            logger.info(f"  优化应用状态: {'成功' if self.stats['optimization_applied'] else '失败'}")
            
            logger.success("✅ 视频上传性能优化完成！")
            logger.info("\n💡 建议:")
            logger.info("  1. 重启Celery服务以应用新配置")
            logger.info("  2. 运行性能监控脚本观察效果")
            logger.info("  3. 根据实际情况进一步调整参数")
            
        except Exception as e:
            logger.error(f"优化过程中发生错误: {e}", exc_info=True)

def main():
    """主函数"""
    optimizer = UploadPerformanceOptimizer()
    optimizer.run_complete_optimization()

if __name__ == "__main__":
    main()
