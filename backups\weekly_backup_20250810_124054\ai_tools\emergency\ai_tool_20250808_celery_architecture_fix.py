#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 7天
创建目的: 修复Celery架构中的提审模块问题
清理条件: 修复完成后删除
"""

import os
import sys
import psutil
import time
from datetime import datetime, timezone
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AdAccount, Campaign


class CeleryArchitectureFixer:
    """Celery架构修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
    
    def run_comprehensive_fix(self) -> Dict[str, Any]:
        """运行全面的Celery架构修复"""
        logger.error("🚨 开始Celery架构问题修复")
        logger.error("="*80)
        
        try:
            # 1. 检查Celery进程状态
            celery_status = self._check_celery_processes()
            
            # 2. 清理浏览器进程
            browser_cleanup = self._emergency_browser_cleanup()
            
            # 3. 验证测试账户限制
            test_account_check = self._verify_test_account_compliance()
            
            # 4. 检查任务配置
            task_config_check = self._check_task_configuration()
            
            # 5. 生成修复建议
            recommendations = self._generate_recommendations()
            
            return {
                'timestamp': datetime.now(timezone.utc),
                'celery_status': celery_status,
                'browser_cleanup': browser_cleanup,
                'test_account_check': test_account_check,
                'task_config_check': task_config_check,
                'recommendations': recommendations,
                'fixes_applied': self.fixes_applied,
                'issues_found': self.issues_found,
                'success': len(self.issues_found) == 0
            }
            
        except Exception as e:
            logger.error(f"❌ Celery架构修复失败: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    def _check_celery_processes(self) -> Dict[str, Any]:
        """检查Celery进程状态"""
        logger.info("🔍 检查Celery进程状态...")
        
        celery_processes = {
            'worker_processes': [],
            'beat_processes': [],
            'python_processes': []
        }
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_info', 'create_time']):
                try:
                    proc_info = proc.info
                    cmdline = ' '.join(proc_info.get('cmdline', []))
                    
                    # 检查Celery Worker进程
                    if 'run_celery_worker.py' in cmdline or 'celery worker' in cmdline:
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        age_minutes = (time.time() - proc_info['create_time']) / 60
                        celery_processes['worker_processes'].append({
                            'pid': proc_info['pid'],
                            'memory_mb': round(memory_mb, 1),
                            'age_minutes': round(age_minutes, 1),
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                        })
                    
                    # 检查Celery Beat进程
                    elif 'run_celery_beat.py' in cmdline or 'celery beat' in cmdline:
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        age_minutes = (time.time() - proc_info['create_time']) / 60
                        celery_processes['beat_processes'].append({
                            'pid': proc_info['pid'],
                            'memory_mb': round(memory_mb, 1),
                            'age_minutes': round(age_minutes, 1),
                            'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                        })
                    
                    # 检查其他Python进程
                    elif proc_info['name'].lower() in ['python.exe', 'python'] and 'qianchuan' in cmdline.lower():
                        memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                        celery_processes['python_processes'].append({
                            'pid': proc_info['pid'],
                            'memory_mb': round(memory_mb, 1),
                            'cmdline': cmdline[:80] + '...' if len(cmdline) > 80 else cmdline
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 分析结果
            worker_count = len(celery_processes['worker_processes'])
            beat_count = len(celery_processes['beat_processes'])
            
            logger.info(f"   Celery Worker进程: {worker_count} 个")
            logger.info(f"   Celery Beat进程: {beat_count} 个")
            logger.info(f"   其他Python进程: {len(celery_processes['python_processes'])} 个")
            
            # 检查异常情况
            if worker_count == 0:
                self.issues_found.append("没有发现Celery Worker进程")
                logger.error("🚨 没有发现Celery Worker进程")
            elif worker_count > 1:
                self.issues_found.append(f"发现多个Celery Worker进程: {worker_count}")
                logger.warning(f"⚠️ 发现多个Celery Worker进程: {worker_count}")
            
            if beat_count == 0:
                self.issues_found.append("没有发现Celery Beat进程")
                logger.error("🚨 没有发现Celery Beat进程")
            elif beat_count > 1:
                self.issues_found.append(f"发现多个Celery Beat进程: {beat_count}")
                logger.warning(f"⚠️ 发现多个Celery Beat进程: {beat_count}")
            
            return celery_processes
            
        except Exception as e:
            logger.error(f"检查Celery进程失败: {e}")
            self.issues_found.append(f"检查Celery进程失败: {e}")
            return celery_processes
    
    def _emergency_browser_cleanup(self) -> Dict[str, Any]:
        """紧急浏览器进程清理"""
        logger.info("🧹 紧急清理浏览器进程...")
        
        browser_processes = []
        killed_count = 0
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info['name'].lower()
                    
                    # 检查是否为我们启动的浏览器进程
                    if any(browser in name for browser in ['chrome', 'chromium']):
                        cmdline = ' '.join(proc_info.get('cmdline', []))
                        if any(flag in cmdline for flag in ['--no-sandbox', '--disable-dev-shm-usage']):
                            memory_mb = proc_info['memory_info'].rss / 1024 / 1024
                            browser_processes.append({
                                'pid': proc_info['pid'],
                                'name': proc_info['name'],
                                'memory_mb': round(memory_mb, 1)
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if len(browser_processes) > 3:  # 如果浏览器进程过多
                logger.warning(f"⚠️ 发现 {len(browser_processes)} 个浏览器进程，执行清理...")
                
                # 按内存使用排序，优先清理高内存进程
                browser_processes.sort(key=lambda x: x['memory_mb'], reverse=True)
                
                # 保留前3个，清理其余的
                for proc_info in browser_processes[3:]:
                    try:
                        proc = psutil.Process(proc_info['pid'])
                        proc.terminate()
                        
                        try:
                            proc.wait(timeout=3)
                            killed_count += 1
                            logger.info(f"  ✅ 清理进程 PID:{proc_info['pid']} ({proc_info['memory_mb']}MB)")
                        except psutil.TimeoutExpired:
                            proc.kill()
                            proc.wait(timeout=2)
                            killed_count += 1
                            logger.info(f"  ⚡ 强制清理进程 PID:{proc_info['pid']}")
                            
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                self.fixes_applied.append(f"清理了 {killed_count} 个多余的浏览器进程")
            else:
                logger.success(f"✅ 浏览器进程数量正常: {len(browser_processes)} 个")
            
            return {
                'total_browsers': len(browser_processes),
                'cleaned_browsers': killed_count,
                'remaining_browsers': len(browser_processes) - killed_count
            }
            
        except Exception as e:
            logger.error(f"浏览器进程清理失败: {e}")
            self.issues_found.append(f"浏览器进程清理失败: {e}")
            return {'error': str(e)}
    
    def _verify_test_account_compliance(self) -> Dict[str, Any]:
        """验证测试账户合规性"""
        logger.info("🛡️ 验证测试账户合规性...")
        
        try:
            with SessionLocal() as db:
                # 检查所有在提审状态的计划
                campaigns_in_appeal = db.query(Campaign, AdAccount).join(AdAccount).filter(
                    Campaign.appeal_status.isnot(None)
                ).all()
                
                test_appeals = 0
                non_test_appeals = 0
                violations = []
                
                for campaign, account in campaigns_in_appeal:
                    if account.account_type == 'TEST':
                        test_appeals += 1
                    else:
                        non_test_appeals += 1
                        violations.append({
                            'campaign_id_qc': campaign.campaign_id_qc,
                            'account_type': account.account_type,
                            'account_name': account.name,
                            'appeal_status': campaign.appeal_status
                        })
                
                if violations:
                    self.issues_found.append(f"发现 {len(violations)} 个非测试账户提审违规")
                    logger.error(f"🚨 发现 {len(violations)} 个非测试账户提审违规")
                    
                    # 立即修复违规
                    campaign_ids = [v['campaign_id_qc'] for v in violations]
                    updated = db.query(Campaign).filter(
                        Campaign.campaign_id_qc.in_(campaign_ids)
                    ).update({
                        Campaign.appeal_status: None,
                        Campaign.appeal_attempt_count: 0,
                        Campaign.first_appeal_at: None,
                        Campaign.last_appeal_at: None,
                        Campaign.appeal_error_message: 'Celery修复：非测试账户不允许提审'
                    }, synchronize_session=False)
                    
                    db.commit()
                    self.fixes_applied.append(f"修复了 {updated} 个非测试账户的提审违规")
                    logger.success(f"✅ 修复了 {updated} 个非测试账户的提审违规")
                else:
                    logger.success("✅ 测试账户合规性检查通过")
                
                return {
                    'total_appeals': len(campaigns_in_appeal),
                    'test_appeals': test_appeals,
                    'non_test_appeals': non_test_appeals,
                    'violations_fixed': len(violations)
                }
                
        except Exception as e:
            logger.error(f"测试账户合规性检查失败: {e}")
            self.issues_found.append(f"测试账户合规性检查失败: {e}")
            return {'error': str(e)}
    
    def _check_task_configuration(self) -> Dict[str, Any]:
        """检查任务配置"""
        logger.info("🔍 检查Celery任务配置...")
        
        try:
            from qianchuan_aw.utils.config_loader import load_settings
            settings = load_settings()
            
            workflow_config = settings.get('workflow', {})
            
            # 检查关键任务的配置
            task_configs = {
                'plan_appeal': workflow_config.get('plan_appeal', {}),
                'material_monitoring': workflow_config.get('material_monitoring', {}),
                'independent_harvest': workflow_config.get('independent_harvest', {})
            }
            
            config_issues = []
            
            # 检查提审任务间隔
            appeal_interval = task_configs['plan_appeal'].get('interval_seconds', 180)
            if appeal_interval < 300:  # 小于5分钟
                config_issues.append(f"提审任务间隔过短: {appeal_interval}s，建议至少300s")
            
            # 检查监控任务间隔
            monitoring_interval = task_configs['material_monitoring'].get('interval_seconds', 300)
            if monitoring_interval < 120:  # 小于2分钟
                config_issues.append(f"监控任务间隔过短: {monitoring_interval}s，建议至少120s")
            
            # 检查测试账户限制
            harvest_scope = task_configs['independent_harvest'].get('scope', {})
            if not harvest_scope.get('test_accounts_only', False):
                config_issues.append("独立收割未限制为仅测试账户")
            
            if config_issues:
                self.issues_found.extend(config_issues)
                logger.warning(f"⚠️ 发现 {len(config_issues)} 个配置问题")
            else:
                logger.success("✅ 任务配置检查通过")
            
            return {
                'task_configs': task_configs,
                'config_issues': config_issues,
                'appeal_interval': appeal_interval,
                'monitoring_interval': monitoring_interval
            }
            
        except Exception as e:
            logger.error(f"任务配置检查失败: {e}")
            self.issues_found.append(f"任务配置检查失败: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        if self.issues_found:
            recommendations.extend([
                "立即重启Celery Worker和Beat进程",
                "调整提审任务间隔至少为900秒（15分钟）",
                "监控浏览器进程数量，保持在3个以下",
                "确保所有提审操作仅限测试账户",
                "定期检查任务执行时间，避免长时间阻塞"
            ])
        else:
            recommendations.extend([
                "继续监控系统运行状况",
                "定期清理浏览器进程",
                "保持当前的任务配置"
            ])
        
        return recommendations


def main():
    """主函数"""
    fixer = CeleryArchitectureFixer()
    results = fixer.run_comprehensive_fix()
    
    logger.error("\n📋 Celery架构修复报告")
    logger.error("="*80)
    
    if results.get('success', False):
        logger.success("✅ Celery架构修复成功")
        logger.success(f"应用了 {len(results.get('fixes_applied', []))} 个修复")
    else:
        logger.error("❌ Celery架构修复发现问题")
        logger.error(f"发现了 {len(results.get('issues_found', []))} 个问题")
    
    logger.error("\n💡 建议措施:")
    for rec in results.get('recommendations', []):
        logger.error(f"   - {rec}")
    
    return 0 if results.get('success', False) else 1


if __name__ == "__main__":
    exit(main())
