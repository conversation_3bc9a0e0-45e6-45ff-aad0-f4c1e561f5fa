"""
千川自动化提审模块 - 广告户级别批量处理架构
===========================================
版本: v2.0
创建时间: 2025-07-29
功能: 实现广告户级别的批量提审处理，最大化资源复用和性能优化
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from loguru import logger

@dataclass
class PlanAppealTask:
    """单个计划提审任务"""
    plan_id: str
    campaign_name: str
    created_at: datetime
    priority: int = 0  # 优先级，数字越大优先级越高

@dataclass
class AdvertiserAppealBatch:
    """广告户提审批次"""
    advertiser_id: str
    account_name: str
    principal_name: str
    plans: List[PlanAppealTask] = field(default_factory=list)
    
    @property
    def plan_count(self) -> int:
        return len(self.plans)
    
    @property
    def plan_ids(self) -> List[str]:
        return [plan.plan_id for plan in self.plans]

@dataclass
class AppealResult:
    """提审结果"""
    plan_id: str
    success: bool
    method: str  # text_command, browser_automation_init, browser_automation_reuse
    message: str
    duration: float
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class AdvertiserAppealSummary:
    """广告户提审汇总"""
    advertiser_id: str
    account_name: str
    total_plans: int
    success_count: int
    failed_count: int
    total_duration: float
    method_stats: Dict[str, Dict[str, int]]
    results: List[AppealResult]
    
    @property
    def success_rate(self) -> float:
        return (self.success_count / self.total_plans * 100) if self.total_plans > 0 else 0
    
    @property
    def avg_duration_per_plan(self) -> float:
        return self.total_duration / self.total_plans if self.total_plans > 0 else 0

class AppealDataManager:
    """提审数据管理器"""
    
    def __init__(self):
        self.db = None
    
    def get_pending_appeal_batches(self, max_plans_per_advertiser: int = 10) -> List[AdvertiserAppealBatch]:
        """获取待提审的广告户批次"""
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            
            self.db = SessionLocal()
            
            # 查询待提审计划，按广告户分组
            pending_campaigns = self.db.query(Campaign, AdAccount, Principal).select_from(Campaign)\
                .join(AdAccount).join(Principal)\
                .filter(
                    Campaign.status == 'AUDITING',
                    Campaign.appeal_status.is_(None)
                ).order_by(Campaign.created_at.desc()).all()
            
            # 按广告户分组
            advertiser_groups = {}
            for campaign, account, principal in pending_campaigns:
                advertiser_id = account.account_id_qc
                
                if advertiser_id not in advertiser_groups:
                    advertiser_groups[advertiser_id] = AdvertiserAppealBatch(
                        advertiser_id=advertiser_id,
                        account_name=account.name,
                        principal_name=principal.name
                    )
                
                # 添加计划任务
                task = PlanAppealTask(
                    plan_id=campaign.campaign_id_qc,
                    campaign_name=f"计划_{campaign.campaign_id_qc}",  # 简化命名
                    created_at=campaign.created_at,
                    priority=1 if account.account_type == 'PRODUCTION' else 0
                )
                advertiser_groups[advertiser_id].plans.append(task)
            
            # 限制每个广告户的计划数量并排序
            batches = []
            for batch in advertiser_groups.values():
                # 按优先级和创建时间排序
                batch.plans.sort(key=lambda x: (-x.priority, x.created_at), reverse=True)
                
                # 限制计划数量
                if len(batch.plans) > max_plans_per_advertiser:
                    batch.plans = batch.plans[:max_plans_per_advertiser]
                
                # 只处理有计划的广告户
                if batch.plan_count > 0:
                    batches.append(batch)
            
            # 按计划数量排序，优先处理计划多的广告户
            batches.sort(key=lambda x: x.plan_count, reverse=True)
            
            logger.info(f"📊 获取到 {len(batches)} 个广告户的提审批次，总计划数: {sum(b.plan_count for b in batches)}")
            
            return batches
            
        except Exception as e:
            logger.error(f"❌ 获取提审批次失败: {e}")
            return []
        finally:
            if self.db:
                self.db.close()
    
    def update_appeal_status(self, results: List[AppealResult]):
        """更新提审状态到数据库"""
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign
            
            db = SessionLocal()
            
            for result in results:
                campaign = db.query(Campaign).filter(
                    Campaign.campaign_id_qc == result.plan_id
                ).first()
                
                if campaign:
                    if result.success:
                        campaign.appeal_status = 'APPEALING'
                        campaign.appeal_method = result.method
                        campaign.appeal_time = result.timestamp
                        campaign.appeal_message = result.message
                    else:
                        campaign.appeal_status = 'FAILED'
                        campaign.appeal_error = result.message
            
            db.commit()
            logger.info(f"✅ 更新了 {len(results)} 个计划的提审状态")
            
        except Exception as e:
            logger.error(f"❌ 更新提审状态失败: {e}")
        finally:
            if db:
                db.close()

class AdvertiserAppealProcessor:
    """广告户提审处理器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
    
    def process_advertiser_batch(self, batch: AdvertiserAppealBatch) -> AdvertiserAppealSummary:
        """处理单个广告户的提审批次"""
        logger.info(f"🚀 开始处理广告户: {batch.account_name} ({batch.plan_count} 个计划)")
        
        start_time = time.time()
        results = []
        
        try:
            from qianchuan_aw.services.copilot_service import CopilotSession
            
            # 为该广告户创建智投星会话
            with CopilotSession(batch.principal_name, batch.advertiser_id, self.app_settings) as session:
                logger.info(f"✅ 广告户 {batch.account_name} 的智投星会话创建成功")
                
                # 使用优化版批量提审
                batch_results = session.batch_appeal_via_optimized_automation(batch.plan_ids)
                
                # 转换结果格式
                for result_data in batch_results:
                    result = AppealResult(
                        plan_id=result_data['plan_id'],
                        success=result_data['success'],
                        method=result_data['method'],
                        message=result_data['message'],
                        duration=0,  # 单个计划的时间在批量处理中难以精确计算
                        timestamp=datetime.now()
                    )
                    results.append(result)
        
        except Exception as e:
            logger.error(f"❌ 广告户 {batch.account_name} 处理失败: {e}")
            # 为所有计划创建失败结果
            for plan in batch.plans:
                result = AppealResult(
                    plan_id=plan.plan_id,
                    success=False,
                    method='error',
                    message=f"处理异常: {e}",
                    duration=0,
                    timestamp=datetime.now()
                )
                results.append(result)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r.success)
        failed_count = len(results) - success_count
        
        # 统计方法使用情况
        method_stats = {}
        for result in results:
            method = result.method
            if method not in method_stats:
                method_stats[method] = {'success': 0, 'failed': 0, 'total': 0}
            
            method_stats[method]['total'] += 1
            if result.success:
                method_stats[method]['success'] += 1
            else:
                method_stats[method]['failed'] += 1
        
        summary = AdvertiserAppealSummary(
            advertiser_id=batch.advertiser_id,
            account_name=batch.account_name,
            total_plans=len(results),
            success_count=success_count,
            failed_count=failed_count,
            total_duration=total_duration,
            method_stats=method_stats,
            results=results
        )
        
        logger.info(f"📊 广告户 {batch.account_name} 处理完成:")
        logger.info(f"   成功: {success_count}/{len(results)} ({summary.success_rate:.1f}%)")
        logger.info(f"   耗时: {total_duration:.1f} 秒")
        
        return summary

class BatchAppealOrchestrator:
    """批量提审编排器"""
    
    def __init__(self, app_settings: Dict[str, Any], max_concurrent_advertisers: int = 3):
        self.app_settings = app_settings
        self.max_concurrent_advertisers = max_concurrent_advertisers
        self.data_manager = AppealDataManager()
    
    def execute_batch_appeals(self, max_plans_per_advertiser: int = 10) -> Dict[str, Any]:
        """执行批量提审"""
        logger.info("🚀 开始执行广告户级别批量提审")
        
        start_time = time.time()
        
        # 1. 获取待处理的广告户批次
        batches = self.data_manager.get_pending_appeal_batches(max_plans_per_advertiser)
        
        if not batches:
            logger.info("📋 没有待提审的计划")
            return {
                'total_advertisers': 0,
                'total_plans': 0,
                'success_count': 0,
                'failed_count': 0,
                'success_rate': 0.0,
                'duration': 0,
                'avg_duration_per_advertiser': 0,
                'summaries': []
            }
        
        logger.info(f"📋 将处理 {len(batches)} 个广告户，总计划数: {sum(b.plan_count for b in batches)}")
        
        # 2. 并发处理广告户批次
        summaries = []
        all_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_concurrent_advertisers) as executor:
            # 提交所有广告户处理任务
            future_to_batch = {}
            for batch in batches:
                processor = AdvertiserAppealProcessor(self.app_settings)
                future = executor.submit(processor.process_advertiser_batch, batch)
                future_to_batch[future] = batch
            
            # 收集结果
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    summary = future.result()
                    summaries.append(summary)
                    all_results.extend(summary.results)
                    
                    logger.success(f"✅ 广告户 {batch.account_name} 处理完成")
                    
                except Exception as e:
                    logger.error(f"❌ 广告户 {batch.account_name} 处理异常: {e}")
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 3. 更新数据库状态
        if all_results:
            self.data_manager.update_appeal_status(all_results)
        
        # 4. 汇总统计
        total_plans = sum(s.total_plans for s in summaries)
        total_success = sum(s.success_count for s in summaries)
        total_failed = sum(s.failed_count for s in summaries)
        
        overall_stats = {
            'total_advertisers': len(summaries),
            'total_plans': total_plans,
            'success_count': total_success,
            'failed_count': total_failed,
            'success_rate': (total_success / total_plans * 100) if total_plans > 0 else 0,
            'duration': total_duration,
            'avg_duration_per_advertiser': total_duration / len(summaries) if summaries else 0,
            'summaries': summaries
        }
        
        logger.info(f"🎉 批量提审完成:")
        logger.info(f"   处理广告户: {overall_stats['total_advertisers']} 个")
        logger.info(f"   处理计划: {total_success}/{total_plans} ({overall_stats['success_rate']:.1f}%)")
        logger.info(f"   总耗时: {total_duration:.1f} 秒")
        
        return overall_stats

# 工作流集成接口
def execute_advertiser_level_batch_appeal(max_plans_per_advertiser: int = 10, 
                                        max_concurrent_advertisers: int = 3) -> Dict[str, Any]:
    """
    工作流集成接口：执行广告户级别批量提审
    
    Args:
        max_plans_per_advertiser: 每个广告户最大处理计划数
        max_concurrent_advertisers: 最大并发广告户数
    
    Returns:
        执行结果统计
    """
    try:
        from qianchuan_aw.database.database import load_settings
        
        app_settings = load_settings()
        orchestrator = BatchAppealOrchestrator(app_settings, max_concurrent_advertisers)
        
        return orchestrator.execute_batch_appeals(max_plans_per_advertiser)
        
    except Exception as e:
        logger.error(f"❌ 批量提审执行失败: {e}")
        return {
            'error': str(e),
            'total_advertisers': 0,
            'total_plans': 0,
            'success_count': 0,
            'failed_count': 0,
            'duration': 0
        }

class AppealMonitor:
    """提审监控器"""

    @staticmethod
    def generate_execution_report(stats: Dict[str, Any]) -> str:
        """生成执行报告"""
        if 'error' in stats:
            return f"❌ 批量提审执行失败: {stats['error']}"

        report = f"""
📊 千川自动化提审执行报告
========================
⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📈 整体统计:
- 处理广告户: {stats['total_advertisers']} 个
- 处理计划: {stats['total_plans']} 个
- 成功计划: {stats['success_count']} 个
- 失败计划: {stats['failed_count']} 个
- 成功率: {stats['success_rate']:.1f}%
- 总耗时: {stats['duration']:.1f} 秒
- 平均每广告户: {stats['avg_duration_per_advertiser']:.1f} 秒

📋 广告户详情:"""

        for summary in stats.get('summaries', []):
            report += f"""
- {summary.account_name}: {summary.success_count}/{summary.total_plans} ({summary.success_rate:.1f}%) - {summary.total_duration:.1f}s"""

        return report

    @staticmethod
    def check_system_health() -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign

            db = SessionLocal()

            # 统计待提审计划
            pending_count = db.query(Campaign).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None)
            ).count()

            # 统计今日提审情况
            today = datetime.now().date()
            today_appeals = db.query(Campaign).filter(
                Campaign.appeal_time >= today,
                Campaign.appeal_status.isnot(None)
            ).count()

            db.close()

            return {
                'healthy': True,
                'pending_appeals': pending_count,
                'today_appeals': today_appeals,
                'timestamp': datetime.now()
            }

        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now()
            }

if __name__ == "__main__":
    # 测试执行
    result = execute_advertiser_level_batch_appeal(
        max_plans_per_advertiser=5,
        max_concurrent_advertisers=2
    )

    # 生成报告
    report = AppealMonitor.generate_execution_report(result)
    print(report)
