#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误分析和修复工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 分析和修复系统中的重复错误，提升系统稳定性
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class ErrorAnalyzer:
    """错误分析器"""
    
    def __init__(self):
        self.error_patterns = {
            'file_not_found': r'❌ 文件不存在: (.+)',
            'file_format_error': r'文件格式错误',
            'video_size_error': r'视频尺寸错误',
            'ffprobe_error': r'ffprobe检测失败.*: (.+)',
            'moov_atom_error': r'moov atom not found',
            'status_transition_error': r'❌ 状态转换失败: 素材(\d+) (.+)->(.+): (.+)',
            'upload_failed': r'❌ 上传失败，状态已自动回滚: (.+)',
            'copilot_error': r'发送消息失败: (.+)',
        }
    
    def analyze_missing_files(self) -> Dict[str, List[str]]:
        """分析文件缺失问题"""
        print("🔍 分析文件缺失问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 查找pending_upload状态的素材
                pending_materials = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
                ).all()
                
                missing_files = []
                existing_files = []
                
                for material in pending_materials:
                    file_path = Path(material.file_path)
                    if not file_path.exists():
                        missing_files.append({
                            'id': material.id,
                            'filename': material.filename,
                            'path': material.file_path,
                            'status': material.status,
                            'updated_at': material.updated_at
                        })
                    else:
                        existing_files.append(material)
                
                print(f"📊 文件状态统计:")
                print(f"  - 待上传素材总数: {len(pending_materials)}")
                print(f"  - 文件存在: {len(existing_files)}")
                print(f"  - 文件缺失: {len(missing_files)}")
                
                return {
                    'missing_files': missing_files,
                    'existing_files': existing_files
                }
                
        except Exception as e:
            print(f"❌ 分析文件缺失失败: {e}")
            return {'missing_files': [], 'existing_files': []}
    
    def fix_missing_files(self, missing_files: List[Dict]) -> int:
        """修复文件缺失问题"""
        if not missing_files:
            print("✅ 没有文件缺失需要修复")
            return 0
        
        print(f"🔧 修复 {len(missing_files)} 个文件缺失问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                fixed_count = 0
                
                for file_info in missing_files:
                    try:
                        # 将状态改为upload_failed
                        material = db.query(LocalCreative).filter(
                            LocalCreative.id == file_info['id']
                        ).first()
                        
                        if material:
                            material.status = MaterialStatus.UPLOAD_FAILED.value
                            material.updated_at = datetime.utcnow()
                            db.commit()
                            fixed_count += 1
                            
                            if fixed_count <= 5:  # 只显示前5个
                                print(f"  ✅ 修复素材 {file_info['id']}: {file_info['filename']}")
                    
                    except Exception as e:
                        print(f"  ❌ 修复素材 {file_info['id']} 失败: {e}")
                        db.rollback()
                
                print(f"📊 修复结果: {fixed_count}/{len(missing_files)} 个文件缺失问题已修复")
                return fixed_count
                
        except Exception as e:
            print(f"❌ 修复文件缺失失败: {e}")
            return 0
    
    def fix_stuck_uploading_materials(self) -> int:
        """修复卡在uploading状态的素材"""
        print("\n🔧 修复卡在uploading状态的素材...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 查找uploading状态超过1小时的素材
                one_hour_ago = datetime.utcnow() - timedelta(hours=1)
                
                stuck_materials = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOADING.value,
                    LocalCreative.updated_at < one_hour_ago
                ).all()
                
                print(f"📊 找到 {len(stuck_materials)} 个卡住的uploading素材")
                
                if not stuck_materials:
                    return 0
                
                fixed_count = 0
                for material in stuck_materials:
                    try:
                        # 重置为pending_upload状态
                        material.status = MaterialStatus.PENDING_UPLOAD.value
                        material.updated_at = datetime.utcnow()
                        db.commit()
                        fixed_count += 1
                        
                        if fixed_count <= 5:  # 只显示前5个
                            print(f"  ✅ 重置素材 {material.id}: {material.filename}")
                    
                    except Exception as e:
                        print(f"  ❌ 重置素材 {material.id} 失败: {e}")
                        db.rollback()
                
                print(f"📊 修复结果: {fixed_count}/{len(stuck_materials)} 个卡住素材已重置")
                return fixed_count
                
        except Exception as e:
            print(f"❌ 修复卡住素材失败: {e}")
            return 0
    
    def analyze_video_quality_issues(self) -> Dict[str, int]:
        """分析视频质量问题"""
        print("\n🔍 分析视频质量问题...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 查找upload_failed状态的素材
                failed_materials = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value
                ).all()
                
                quality_issues = {
                    'format_error': 0,
                    'size_error': 0,
                    'duration_error': 0,
                    'corruption_error': 0,
                    'other_error': 0
                }
                
                print(f"📊 视频质量问题统计:")
                print(f"  - 上传失败素材总数: {len(failed_materials)}")
                print(f"  - 格式错误: {quality_issues['format_error']}")
                print(f"  - 尺寸错误: {quality_issues['size_error']}")
                print(f"  - 时长错误: {quality_issues['duration_error']}")
                print(f"  - 文件损坏: {quality_issues['corruption_error']}")
                
                return quality_issues
                
        except Exception as e:
            print(f"❌ 分析视频质量问题失败: {e}")
            return {}
    
    def create_error_prevention_rules(self) -> List[str]:
        """创建错误预防规则"""
        print("\n🛡️ 创建错误预防规则...")
        
        prevention_rules = [
            "文件上传前进行格式和尺寸预检查",
            "实现文件完整性验证（moov atom检查）",
            "添加文件存在性检查到批量上传任务",
            "实现错误分类和智能处理机制",
            "添加视频质量预检查工具",
            "实现文件路径标准化和验证",
            "添加重复错误检测和自动修复",
            "实现错误统计和趋势分析"
        ]
        
        print(f"📋 错误预防规则:")
        for i, rule in enumerate(prevention_rules, 1):
            print(f"  {i}. {rule}")
        
        return prevention_rules

def main():
    """主函数"""
    print("🚀 开始错误分析和修复...")
    
    analyzer = ErrorAnalyzer()
    
    # 1. 分析和修复文件缺失问题
    print(f"\n{'='*60}")
    print("🔍 文件缺失问题分析和修复")
    print(f"{'='*60}")
    
    file_analysis = analyzer.analyze_missing_files()
    missing_count = len(file_analysis.get('missing_files', []))
    
    if missing_count > 0:
        fixed_files = analyzer.fix_missing_files(file_analysis['missing_files'])
        print(f"✅ 修复了 {fixed_files} 个文件缺失问题")
    else:
        fixed_files = 0
    
    # 2. 修复卡住的uploading素材
    print(f"\n{'='*60}")
    print("🔧 修复卡住的uploading素材")
    print(f"{'='*60}")
    
    fixed_stuck = analyzer.fix_stuck_uploading_materials()
    
    # 3. 分析视频质量问题
    print(f"\n{'='*60}")
    print("🔍 视频质量问题分析")
    print(f"{'='*60}")
    
    quality_issues = analyzer.analyze_video_quality_issues()
    
    # 4. 创建预防规则
    print(f"\n{'='*60}")
    print("🛡️ 错误预防规则")
    print(f"{'='*60}")
    
    prevention_rules = analyzer.create_error_prevention_rules()
    
    # 生成总结报告
    print(f"\n{'='*60}")
    print("📊 错误分析和修复总结")
    print(f"{'='*60}")
    
    print(f"修复文件缺失: {fixed_files} 个")
    print(f"修复卡住素材: {fixed_stuck} 个")
    print(f"质量问题分析: {'✅ 完成' if quality_issues else '❌ 失败'}")
    print(f"预防规则创建: {len(prevention_rules)} 条")
    
    total_fixes = fixed_files + fixed_stuck
    
    if total_fixes > 0:
        print(f"\n🎉 成功修复 {total_fixes} 个问题！")
        print(f"💡 建议重启Celery Worker以应用修复")
        return 0
    else:
        print(f"\n✅ 系统运行正常，无需修复")
        return 0

if __name__ == "__main__":
    exit(main())
