#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超时创建计划功能修复工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 诊断和修复超时创建计划功能，解决素材长时间等待问题
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class TimeoutPlanCreationFixer:
    """超时创建计划功能修复器"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        self.config_issues = []
    
    def diagnose_timeout_logic(self) -> Dict:
        """诊断超时逻辑问题"""
        print("🔍 诊断超时创建计划逻辑...")
        
        diagnosis = {
            'config_issues': [],
            'code_issues': [],
            'data_issues': [],
            'timeout_materials': []
        }
        
        try:
            # 1. 检查配置
            import yaml
            config_file = Path("config/settings.yml")
            with open(config_file, 'r', encoding='utf-8') as f:
                app_settings = yaml.safe_load(f)
            
            flexible_config = app_settings.get('flexible_grouping', {})
            
            print(f"📋 当前flexible_grouping配置:")
            print(f"  - enabled: {flexible_config.get('enabled', 'NOT_SET')}")
            print(f"  - timeout_hours: {flexible_config.get('timeout_hours', 'NOT_SET')}")
            print(f"  - force_create_threshold: {flexible_config.get('force_create_threshold', 'NOT_SET')}")
            print(f"  - min_creative_count: {flexible_config.get('min_creative_count', 'NOT_SET')}")
            print(f"  - max_creative_count: {flexible_config.get('max_creative_count', 'NOT_SET')}")
            
            # 检查配置问题
            if not flexible_config.get('enabled', False):
                diagnosis['config_issues'].append("flexible_grouping未启用")
            
            timeout_hours = flexible_config.get('timeout_hours', 0)
            if timeout_hours <= 0:
                diagnosis['config_issues'].append(f"timeout_hours配置异常: {timeout_hours}")
            
            force_threshold = flexible_config.get('force_create_threshold', 0)
            if force_threshold <= 0:
                diagnosis['config_issues'].append(f"force_create_threshold配置异常: {force_threshold}")
            
            # 2. 检查数据库中的超时素材
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            with database_session() as db:
                # 查找超时素材
                timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=timeout_hours)
                
                timeout_materials = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([
                        MaterialStatus.PENDING_GROUPING.value,
                        MaterialStatus.UPLOADED_PENDING_PLAN.value
                    ]),
                    LocalCreative.updated_at < timeout_threshold
                ).all()
                
                print(f"\n📊 超时素材统计:")
                print(f"  - 超时阈值: {timeout_threshold}")
                print(f"  - 超时素材数量: {len(timeout_materials)}")
                
                # 按账户分组统计
                account_stats = defaultdict(list)
                for material in timeout_materials:
                    account_id = material.uploaded_to_account_id or 'unknown'
                    hours_waiting = (datetime.now(timezone.utc) - material.updated_at.replace(tzinfo=timezone.utc)).total_seconds() / 3600
                    
                    account_stats[account_id].append({
                        'id': material.id,
                        'filename': material.filename,
                        'status': material.status,
                        'hours_waiting': hours_waiting,
                        'updated_at': material.updated_at
                    })
                
                diagnosis['timeout_materials'] = dict(account_stats)
                
                print(f"  - 涉及账户数: {len(account_stats)}")
                for account_id, materials in account_stats.items():
                    print(f"    账户 {account_id}: {len(materials)} 个超时素材")
            
            # 3. 检查调度器是否正常运行
            diagnosis['scheduler_status'] = self.check_scheduler_status()
            
            return diagnosis
            
        except Exception as e:
            print(f"❌ 诊断过程失败: {e}")
            diagnosis['code_issues'].append(f"诊断异常: {e}")
            return diagnosis
    
    def check_scheduler_status(self) -> Dict:
        """检查调度器状态"""
        print("\n🔍 检查调度器状态...")
        
        try:
            # 检查最近的调度日志
            log_files = list(Path("logs").glob("*.log")) if Path("logs").exists() else []
            
            recent_scheduler_activity = False
            recent_timeout_checks = False
            
            for log_file in log_files[-3:]:  # 检查最近3个日志文件
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # 检查调度器活动
                        if '计划创建调度器' in content or 'plan_creation_scheduler' in content:
                            recent_scheduler_activity = True
                        
                        # 检查超时检查
                        if '超时视频' in content or 'should_force_create_plan' in content:
                            recent_timeout_checks = True
                            
                except Exception:
                    continue
            
            return {
                'recent_scheduler_activity': recent_scheduler_activity,
                'recent_timeout_checks': recent_timeout_checks,
                'log_files_checked': len(log_files)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def test_timeout_logic_directly(self) -> Dict:
        """直接测试超时逻辑"""
        print("\n🧪 直接测试超时逻辑...")
        
        try:
            from qianchuan_aw.workflows.flexible_grouping import should_force_create_plan, get_flexible_grouping_config
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            import yaml

            config_file = Path("config/settings.yml")
            with open(config_file, 'r', encoding='utf-8') as f:
                app_settings = yaml.safe_load(f)
            grouping_config = get_flexible_grouping_config(app_settings)
            
            with database_session() as db:
                # 获取一些等待中的素材
                pending_materials = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([
                        MaterialStatus.PENDING_GROUPING.value,
                        MaterialStatus.UPLOADED_PENDING_PLAN.value
                    ])
                ).limit(10).all()
                
                if not pending_materials:
                    return {'result': 'no_pending_materials'}
                
                # 测试超时逻辑
                should_force = should_force_create_plan(
                    pending_materials,
                    grouping_config['force_create_threshold'],
                    grouping_config['timeout_hours']
                )
                
                print(f"  - 测试素材数量: {len(pending_materials)}")
                print(f"  - 配置超时时间: {grouping_config['timeout_hours']} 小时")
                print(f"  - 强制创建阈值: {grouping_config['force_create_threshold']}")
                print(f"  - 超时逻辑结果: {should_force}")
                
                # 检查每个素材的超时情况
                timeout_threshold = datetime.now(timezone.utc) - timedelta(hours=grouping_config['timeout_hours'])
                timeout_count = 0
                
                for material in pending_materials:
                    material_updated_at = material.updated_at.replace(tzinfo=timezone.utc)
                    is_timeout = material_updated_at < timeout_threshold
                    hours_waiting = (datetime.now(timezone.utc) - material_updated_at).total_seconds() / 3600
                    
                    if is_timeout:
                        timeout_count += 1
                    
                    print(f"    素材 {material.id}: 等待 {hours_waiting:.1f}h, 超时: {is_timeout}")
                
                return {
                    'result': 'tested',
                    'should_force': should_force,
                    'total_materials': len(pending_materials),
                    'timeout_materials': timeout_count,
                    'config': grouping_config
                }
                
        except Exception as e:
            print(f"❌ 测试超时逻辑失败: {e}")
            return {'error': str(e)}
    
    def fix_configuration_issues(self, diagnosis: Dict) -> bool:
        """修复配置问题"""
        print("\n🔧 修复配置问题...")
        
        if not diagnosis['config_issues']:
            print("✅ 配置无问题，跳过修复")
            return True
        
        try:
            config_file = Path("config/settings.yml")
            
            # 读取当前配置
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否需要修复flexible_grouping配置
            if 'flexible_grouping未启用' in diagnosis['config_issues'] or \
               any('timeout_hours配置异常' in issue for issue in diagnosis['config_issues']) or \
               any('force_create_threshold配置异常' in issue for issue in diagnosis['config_issues']):
                
                # 修复flexible_grouping配置
                import re
                
                # 查找flexible_grouping配置块
                flexible_pattern = r'flexible_grouping:\s*\n((?:  .*\n)*)'
                match = re.search(flexible_pattern, content)
                
                if match:
                    # 替换现有配置
                    new_config = """flexible_grouping:
  enabled: true
  force_create_threshold: 3
  max_creative_count: 9
  min_creative_count: 9
  timeout_hours: 0.5
"""
                    content = re.sub(flexible_pattern, new_config, content)
                    
                    # 备份原文件
                    backup_file = config_file.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.yml')
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    # 写入修复后的配置
                    with open(config_file, 'w', encoding='utf-8') as f:
                        content_lines = content.split('\n')
                        # 替换flexible_grouping配置
                        new_content = re.sub(flexible_pattern, new_config, content)
                        f.write(new_content)
                    
                    self.fixes_applied.append("修复flexible_grouping配置")
                    print("✅ 已修复flexible_grouping配置")
                    return True
                else:
                    print("❌ 未找到flexible_grouping配置块")
                    return False
            
            return True
            
        except Exception as e:
            print(f"❌ 修复配置失败: {e}")
            return False
    
    def force_create_timeout_plans(self, diagnosis: Dict) -> int:
        """强制创建超时计划"""
        print("\n🚀 强制创建超时计划...")
        
        if not diagnosis['timeout_materials']:
            print("✅ 无超时素材，跳过强制创建")
            return 0
        
        created_plans = 0
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative, AdAccount, Principal, PlatformCreative
            from qianchuan_aw.workflows.common.plan_creation import create_ad_plan
            import yaml

            config_file = Path("config/settings.yml")
            with open(config_file, 'r', encoding='utf-8') as f:
                app_settings = yaml.safe_load(f)
            
            with database_session() as db:
                # 按账户处理超时素材
                for account_id, materials in diagnosis['timeout_materials'].items():
                    if account_id == 'unknown' or len(materials) < 3:
                        continue
                    
                    print(f"\n📋 处理账户 {account_id} 的 {len(materials)} 个超时素材...")
                    
                    # 获取账户信息
                    account = db.query(AdAccount).filter(AdAccount.id == account_id).first()
                    if not account:
                        print(f"❌ 未找到账户 {account_id}")
                        continue
                    
                    # 获取主体信息
                    principal = db.query(Principal).filter(Principal.id == account.principal_id).first()
                    if not principal:
                        print(f"❌ 未找到主体信息")
                        continue
                    
                    # 获取平台素材
                    material_ids = [m['id'] for m in materials[:9]]  # 最多取9个
                    platform_creatives = db.query(PlatformCreative).filter(
                        PlatformCreative.local_creative_id.in_(material_ids)
                    ).all()
                    
                    if len(platform_creatives) < 3:
                        print(f"⚠️ 账户 {account_id} 平台素材不足 ({len(platform_creatives)} < 3)")
                        continue
                    
                    # 创建计划
                    try:
                        result = create_ad_plan(
                            db=db,
                            principal=principal,
                            account=account,
                            platform_creatives=platform_creatives[:9],  # 最多9个
                            campaign_scene='DAILY_SALE',
                            bid_type='DEAL',
                            is_lab_ad=False,
                            budget=300.0,
                            app_settings=app_settings,
                            cpa_bid=30.0
                        )
                        
                        if result and result.get('success'):
                            created_plans += 1
                            print(f"✅ 成功为账户 {account_id} 创建超时计划")
                            self.fixes_applied.append(f"为账户 {account_id} 创建超时计划")
                        else:
                            print(f"❌ 账户 {account_id} 计划创建失败: {result.get('error', 'Unknown error')}")
                    
                    except Exception as e:
                        print(f"❌ 账户 {account_id} 计划创建异常: {e}")
                        continue
            
            return created_plans
            
        except Exception as e:
            print(f"❌ 强制创建计划失败: {e}")
            return 0
    
    def generate_fix_report(self, diagnosis: Dict, created_plans: int) -> str:
        """生成修复报告"""
        from datetime import datetime
        
        report = f"""
{'='*60}
🔧 超时创建计划功能修复报告
{'='*60}
修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 诊断结果:
- 配置问题: {len(diagnosis['config_issues'])} 个
- 代码问题: {len(diagnosis['code_issues'])} 个
- 超时素材: {sum(len(materials) for materials in diagnosis['timeout_materials'].values()) if isinstance(diagnosis['timeout_materials'], dict) else len(diagnosis['timeout_materials'])} 个
- 涉及账户: {len(diagnosis['timeout_materials'])} 个

"""
        
        if diagnosis['config_issues']:
            report += "❌ 发现的配置问题:\n"
            for i, issue in enumerate(diagnosis['config_issues'], 1):
                report += f"  {i}. {issue}\n"
            report += "\n"
        
        if self.fixes_applied:
            report += "✅ 应用的修复:\n"
            for i, fix in enumerate(self.fixes_applied, 1):
                report += f"  {i}. {fix}\n"
            report += "\n"
        
        report += f"🚀 强制创建计划: {created_plans} 个\n\n"
        
        report += "💡 建议:\n"
        report += "  1. 重启计划创建调度器以应用配置修复\n"
        report += "  2. 监控后续的超时检查是否正常工作\n"
        report += "  3. 定期运行此工具检查超时素材\n"
        report += "  4. 考虑调整timeout_hours配置以适应业务需求\n"
        
        report += f"\n{'='*60}\n"
        
        return report
    
    def run_comprehensive_fix(self) -> Dict:
        """运行综合修复"""
        print("🚀 开始超时创建计划功能综合修复...")
        
        # 1. 诊断问题
        diagnosis = self.diagnose_timeout_logic()
        
        # 2. 测试超时逻辑
        test_result = self.test_timeout_logic_directly()
        
        # 3. 修复配置问题
        config_fixed = self.fix_configuration_issues(diagnosis)
        
        # 4. 强制创建超时计划
        created_plans = self.force_create_timeout_plans(diagnosis)
        
        # 5. 生成报告
        fix_report = self.generate_fix_report(diagnosis, created_plans)
        
        return {
            'diagnosis': diagnosis,
            'test_result': test_result,
            'config_fixed': config_fixed,
            'created_plans': created_plans,
            'fix_report': fix_report
        }

def main():
    """主函数"""
    print("🔧 超时创建计划功能修复工具")
    print("=" * 60)
    
    fixer = TimeoutPlanCreationFixer()
    
    try:
        # 运行综合修复
        results = fixer.run_comprehensive_fix()
        
        # 输出修复报告
        print(results['fix_report'])
        
        # 保存报告
        from datetime import datetime
        report_file = Path("logs") / f"timeout_plan_creation_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results['fix_report'])
        
        print(f"📄 修复报告已保存到: {report_file}")
        
        # 返回状态
        if results['created_plans'] > 0 or results['config_fixed']:
            print(f"\n🎉 修复成功！创建了 {results['created_plans']} 个计划")
            return 0
        else:
            print(f"\n⚠️ 未发现需要修复的问题")
            return 1
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 2

if __name__ == "__main__":
    exit(main())
