#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 最终分析工作流系统问题并提供解决方案
清理条件: 成为项目核心诊断工具，长期保留
"""

from qianchuan_aw.utils.unified_material_status import MaterialStatus
import os
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logging():
    """设置日志"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}",
        level="INFO"
    )

def analyze_directory_vs_database():
    """分析目录文件与数据库记录的差异"""
    logger.info("🔍 分析目录文件与数据库记录的差异...")
    
    # 获取目录中的文件
    directory = "D:/workflow_assets/01_materials_to_process/缇萃百货"
    directory_files = set()
    
    if os.path.exists(directory):
        for filename in os.listdir(directory):
            if filename.endswith('.mp4'):
                directory_files.add(filename)
    
    logger.info(f"📁 目录中发现 {len(directory_files)} 个视频文件")
    
    # 查询数据库记录
    try:
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查询所有8.1和8.3的记录
            db_records = db.query(LocalCreative).filter(
                LocalCreative.filename.like('8.1-%') | 
                LocalCreative.filename.like('8.3-%')
            ).all()
            
            db_files = set(record.filename for record in db_records)
            logger.info(f"💾 数据库中发现 {len(db_files)} 条相关记录")
            
            # 分析差异
            only_in_directory = directory_files - db_files
            only_in_database = db_files - directory_files
            in_both = directory_files & db_files
            
            logger.info("📊 差异分析:")
            logger.info(f"   只在目录中: {len(only_in_directory)} 个")
            logger.info(f"   只在数据库中: {len(only_in_database)} 个")
            logger.info(f"   两者都有: {len(in_both)} 个")
            
            if only_in_directory:
                logger.warning("⚠️ 只在目录中的文件（未入库）:")
                for filename in sorted(only_in_directory):
                    logger.warning(f"   📄 {filename}")
            
            if only_in_database:
                logger.info("💾 只在数据库中的文件（已处理）:")
                for filename in sorted(only_in_database):
                    logger.info(f"   📄 {filename}")
            
            # 分析数据库记录状态
            status_analysis = {}
            path_issues = []
            
            for record in db_records:
                status = record.status
                status_analysis[status] = status_analysis.get(status, 0) + 1
                
                # 检查路径问题
                if record.file_path and '01_to_process' in record.file_path:
                    path_issues.append({
                        'filename': record.filename,
                        'status': record.status,
                        'old_path': record.file_path,
                        'created_at': record.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    })
            
            logger.info("📈 数据库记录状态分布:")
            for status, count in status_analysis.items():
                logger.info(f"   {status}: {count} 条")
            
            if path_issues:
                logger.error(f"❌ 发现 {len(path_issues)} 条路径问题:")
                for issue in path_issues:
                    logger.error(f"   {issue['filename']} ({issue['status']}) - {issue['old_path']}")
            
            return {
                'directory_files': directory_files,
                'db_files': db_files,
                'only_in_directory': only_in_directory,
                'only_in_database': only_in_database,
                'in_both': in_both,
                'status_analysis': status_analysis,
                'path_issues': path_issues
            }
            
    except Exception as e:
        logger.error(f"❌ 数据库分析失败: {e}")
        return None

def check_workflow_system_health():
    """检查工作流系统健康状态"""
    logger.info("🔍 检查工作流系统健康状态...")
    
    health_status = {
        'celery_beat_running': False,
        'recent_task_execution': False,
        'file_ingestion_working': False,
        'group_dispatch_working': False,
        'last_task_time': None
    }
    
    # 检查Celery Beat调度文件
    beat_file = "logs/celerybeat-schedule.db"
    if os.path.exists(beat_file):
        stat = os.stat(beat_file)
        last_modified = datetime.fromtimestamp(stat.st_mtime)
        health_status['celery_beat_running'] = (datetime.now() - last_modified).seconds < 300  # 5分钟内
        health_status['last_beat_update'] = last_modified.strftime('%Y-%m-%d %H:%M:%S')
        logger.info(f"📅 Celery Beat最后更新: {health_status['last_beat_update']}")
    else:
        logger.warning("⚠️ Celery Beat调度文件不存在")
    
    # 检查最近的任务执行
    log_file = "logs/app_2025-08-03.log"
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 查找最近的任务记录
            recent_tasks = []
            for line in reversed(lines[-2000:]):  # 检查最后2000行
                if 'Task Start' in line or 'Task End' in line:
                    recent_tasks.append(line.strip())
                    if len(recent_tasks) >= 20:
                        break
            
            if recent_tasks:
                health_status['recent_task_execution'] = True
                # 提取最后一个任务的时间
                last_task_line = recent_tasks[0]
                if '2025-08-03' in last_task_line:
                    health_status['last_task_time'] = last_task_line.split('|')[0].strip()
                
                # 检查特定任务
                for task in recent_tasks:
                    if 'Ingest Files' in task:
                        health_status['file_ingestion_working'] = True
                    if 'Group & Dispatch' in task:
                        health_status['group_dispatch_working'] = True
            
            logger.info("📋 最近任务执行状态:")
            logger.info(f"   文件摄取任务: {'✅ 正常' if health_status['file_ingestion_working'] else '❌ 异常'}")
            logger.info(f"   分组派发任务: {'✅ 正常' if health_status['group_dispatch_working'] else '❌ 异常'}")
            logger.info(f"   最后任务时间: {health_status['last_task_time'] or '未知'}")
            
        except Exception as e:
            logger.error(f"❌ 日志分析失败: {e}")
    
    return health_status

def generate_solution_recommendations(analysis_result, health_status):
    """生成解决方案建议"""
    logger.info("💡 生成解决方案建议...")
    
    recommendations = []
    
    if analysis_result:
        # 针对未入库文件的建议
        if analysis_result['only_in_directory']:
            recommendations.append({
                'priority': 'HIGH',
                'issue': f"{len(analysis_result['only_in_directory'])} 个文件未入库",
                'solution': "手动触发文件摄取任务或检查文件摄取逻辑",
                'command': "python -c \"from src.qianchuan_aw.workflows.tasks import task_ingest_and_upload; task_ingest_and_upload.delay()\""
            })
        
        # 针对路径问题的建议
        if analysis_result['path_issues']:
            recommendations.append({
                'priority': 'HIGH',
                'issue': f"{len(analysis_result['path_issues'])} 条记录存在路径问题",
                'solution': "更新数据库中的文件路径，从01_to_process改为01_materials_to_process",
                'command': "需要执行SQL更新语句修复路径"
            })
        
        # 针对pending状态的建议
        if MaterialStatus.PENDING_GROUPING.value in analysis_result['status_analysis']:
            count = analysis_result['status_analysis'][MaterialStatus.PENDING_GROUPING.value]
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': f"{count} 个素材卡在pending_grouping状态",
                'solution': "触发分组派发任务处理待分组素材",
                'command': "python -c \"from src.qianchuan_aw.workflows.tasks import task_group_and_dispatch; task_group_and_dispatch.delay()\""
            })
    
    # 针对系统健康状态的建议
    if not health_status['celery_beat_running']:
        recommendations.append({
            'priority': 'CRITICAL',
            'issue': "Celery Beat调度器未运行",
            'solution': "启动Celery Beat服务",
            'command': "celery -A src.qianchuan_aw.celery_app beat --loglevel=info"
        })
    
    if not health_status['file_ingestion_working']:
        recommendations.append({
            'priority': 'HIGH',
            'issue': "文件摄取任务未正常执行",
            'solution': "检查文件摄取任务配置和Celery Worker状态",
            'command': "检查celery worker是否运行正常"
        })
    
    # 输出建议
    logger.info("🎯 解决方案建议:")
    for i, rec in enumerate(recommendations, 1):
        priority_color = {
            'CRITICAL': '🔴',
            'HIGH': '🟠', 
            'MEDIUM': '🟡',
            'LOW': '🟢'
        }
        logger.info(f"   {priority_color.get(rec['priority'], '⚪')} [{rec['priority']}] {rec['issue']}")
        logger.info(f"      解决方案: {rec['solution']}")
        if rec['command']:
            logger.info(f"      执行命令: {rec['command']}")
        logger.info("")
    
    return recommendations

def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 80)
    logger.info("🔧 工作流系统最终分析工具")
    logger.info("=" * 80)
    
    # 1. 分析目录与数据库差异
    analysis_result = analyze_directory_vs_database()
    
    # 2. 检查系统健康状态
    health_status = check_workflow_system_health()
    
    # 3. 生成解决方案建议
    recommendations = generate_solution_recommendations(analysis_result, health_status)
    
    # 4. 生成最终报告
    final_report = {
        'timestamp': datetime.now().isoformat(),
        'analysis_result': analysis_result,
        'health_status': health_status,
        'recommendations': recommendations,
        'summary': {
            'total_directory_files': len(analysis_result['directory_files']) if analysis_result else 0,
            'total_db_records': len(analysis_result['db_files']) if analysis_result else 0,
            'unprocessed_files': len(analysis_result['only_in_directory']) if analysis_result else 0,
            'path_issues': len(analysis_result['path_issues']) if analysis_result else 0,
            'system_healthy': health_status['celery_beat_running'] and health_status['file_ingestion_working']
        }
    }
    
    # 保存报告
    report_file = f"ai_reports/diagnosis/ai_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_final_workflow_analysis.json"
    os.makedirs(os.path.dirname(report_file), exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(final_report, f, ensure_ascii=False, indent=2, default=str)
    
    logger.success(f"📋 最终分析报告已保存: {report_file}")
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("🎯 分析总结:")
    logger.info(f"   📁 目录文件: {final_report['summary']['total_directory_files']} 个")
    logger.info(f"   💾 数据库记录: {final_report['summary']['total_db_records']} 条")
    logger.info(f"   ⏳ 未处理文件: {final_report['summary']['unprocessed_files']} 个")
    logger.info(f"   🔧 路径问题: {final_report['summary']['path_issues']} 个")
    logger.info(f"   🏥 系统健康: {'✅ 正常' if final_report['summary']['system_healthy'] else '❌ 异常'}")
    logger.info(f"   💡 建议数量: {len(recommendations)} 条")
    logger.info("=" * 80)

if __name__ == "__main__":
    main()
