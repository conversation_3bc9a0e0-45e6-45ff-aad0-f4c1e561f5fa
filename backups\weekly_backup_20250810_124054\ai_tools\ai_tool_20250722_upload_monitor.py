#!/usr/bin/env python3
'''
千川上传接口实时监控脚本
定期收集系统指标并报告异常
'''

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

class UploadMonitor:
    def __init__(self):
        self.log_file = Path("logs/upload_monitor.log")
        self.log_file.parent.mkdir(exist_ok=True)
    
    def collect_metrics(self):
        process = psutil.Process()
        
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'memory_mb': process.memory_info().rss / (1024*1024),
            'memory_percent': process.memory_percent(),
            'open_files': len(process.open_files()) if hasattr(process, 'open_files') else 0,
            'connections': len([c for c in process.connections() if c.status == 'ESTABLISHED']),
            'threads': process.num_threads(),
            'cpu_percent': process.cpu_percent()
        }
        
        return metrics
    
    def check_alerts(self, metrics):
        alerts = []
        
        if metrics['memory_mb'] > 500:
            alerts.append(f"高内存使用: {metrics['memory_mb']:.1f}MB")
        
        if metrics['open_files'] > 100:
            alerts.append(f"文件句柄过多: {metrics['open_files']}")
        
        if metrics['connections'] > 50:
            alerts.append(f"连接数过多: {metrics['connections']}")
        
        return alerts
    
    def run_monitoring(self, interval=300):  # 5分钟间隔
        while True:
            try:
                metrics = self.collect_metrics()
                alerts = self.check_alerts(metrics)
                
                # 记录日志
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(json.dumps(metrics) + '\n')
                
                # 输出警报
                if alerts:
                    print(f"[{metrics['timestamp']}] 警报:")
                    for alert in alerts:
                        print(f"  - {alert}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(60)

if __name__ == "__main__":
    monitor = UploadMonitor()
    monitor.run_monitoring()
