#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 诊断异步Playwright问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import asyncio
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

async def test_basic_playwright():
    """测试基础Playwright异步API"""
    logger.info("🧪 测试基础Playwright异步API...")
    
    try:
        from playwright.async_api import async_playwright
        
        logger.info("1. 启动async_playwright...")
        playwright = await async_playwright().start()
        logger.success("✅ async_playwright启动成功")
        
        logger.info("2. 启动浏览器...")
        browser = await playwright.chromium.launch(headless=False)
        logger.success("✅ 浏览器启动成功")
        
        logger.info("3. 创建上下文...")
        context = await browser.new_context()
        logger.success("✅ 上下文创建成功")
        
        logger.info("4. 创建页面...")
        page = await context.new_page()
        logger.success("✅ 页面创建成功")
        
        logger.info("5. 导航到测试页面...")
        await page.goto("https://www.baidu.com")
        logger.success("✅ 页面导航成功")
        
        logger.info("6. 清理资源...")
        await page.close()
        await context.close()
        await browser.close()
        await playwright.stop()
        logger.success("✅ 资源清理成功")
        
        return True, "基础Playwright测试成功"
        
    except Exception as e:
        logger.error(f"❌ 基础Playwright测试失败: {e}")
        return False, str(e)

async def test_cookies_loading():
    """测试cookies加载"""
    logger.info("🧪 测试cookies加载...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright
        
        cookies_path = Path("config/cookies.json")
        
        if not cookies_path.exists():
            logger.error(f"Cookies文件不存在: {cookies_path}")
            return False, "Cookies文件不存在"
        
        logger.info("1. 读取cookies文件...")
        with open(cookies_path, 'r', encoding='utf-8') as f:
            import json
            all_cookies_data = json.load(f)
        
        logger.info("2. 查找缇萃百货的cookies...")
        principal_name = "缇萃百货"
        principal_data = all_cookies_data.get(principal_name, {})
        
        if isinstance(principal_data, dict) and "cookies" in principal_data:
            cookies_array = principal_data["cookies"]
            logger.info(f"✅ 找到新格式cookies: {len(cookies_array)} 个")
        elif isinstance(principal_data, list):
            cookies_array = principal_data
            logger.info(f"✅ 找到旧格式cookies: {len(cookies_array)} 个")
        else:
            logger.error(f"❌ 未找到主体 '{principal_name}' 的cookies")
            return False, f"未找到主体cookies"
        
        logger.info("3. 清理cookies...")
        cleaned_cookies = clean_cookies_for_playwright(cookies_array)
        logger.success(f"✅ 清理后cookies: {len(cleaned_cookies)} 个")
        
        return True, f"Cookies加载成功: {len(cleaned_cookies)} 个"
        
    except Exception as e:
        logger.error(f"❌ Cookies加载测试失败: {e}")
        return False, str(e)

async def test_playwright_with_cookies():
    """测试Playwright与cookies结合"""
    logger.info("🧪 测试Playwright与cookies结合...")
    
    try:
        # 先测试cookies加载
        cookies_ok, cookies_msg = await test_cookies_loading()
        if not cookies_ok:
            return False, f"Cookies加载失败: {cookies_msg}"
        
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright
        from playwright.async_api import async_playwright
        
        # 加载cookies
        cookies_path = Path("config/cookies.json")
        with open(cookies_path, 'r', encoding='utf-8') as f:
            import json
            all_cookies_data = json.load(f)
        
        principal_name = "缇萃百货"
        principal_data = all_cookies_data.get(principal_name, {})
        
        if isinstance(principal_data, dict) and "cookies" in principal_data:
            cookies_array = principal_data["cookies"]
        elif isinstance(principal_data, list):
            cookies_array = principal_data
        else:
            return False, "无法获取cookies"
        
        cleaned_cookies = clean_cookies_for_playwright(cookies_array)
        
        logger.info("1. 启动Playwright...")
        playwright = await async_playwright().start()
        
        logger.info("2. 启动浏览器...")
        app_settings = load_config()
        browser = await playwright.chromium.launch(
            headless=app_settings.get('browser', {}).get('headless', True),
            args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-web-security']
        )
        
        logger.info("3. 创建上下文...")
        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        )
        
        logger.info("4. 添加cookies...")
        await context.add_cookies(cleaned_cookies)
        logger.success(f"✅ 添加了 {len(cleaned_cookies)} 个cookies")
        
        logger.info("5. 创建页面...")
        page = await context.new_page()
        
        logger.info("6. 设置超时...")
        await page.set_default_timeout(20000)
        
        logger.info("7. 导航到千川平台...")
        base_url = "https://qianchuan.jinritemai.com/promotion-v2/standard?aavid=****************"
        await page.goto(base_url, wait_until="domcontentloaded")
        logger.success("✅ 千川平台导航成功")
        
        logger.info("8. 等待页面加载...")
        await asyncio.sleep(3)
        
        logger.info("9. 清理资源...")
        await page.close()
        await context.close()
        await browser.close()
        await playwright.stop()
        logger.success("✅ 资源清理成功")
        
        return True, "Playwright与cookies结合测试成功"
        
    except Exception as e:
        logger.error(f"❌ Playwright与cookies结合测试失败: {e}")
        return False, str(e)

async def test_async_copilot_session_direct():
    """直接测试AsyncCopilotSession"""
    logger.info("🧪 直接测试AsyncCopilotSession...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_copilot_service import AsyncCopilotSession
        
        app_settings = load_config()
        session_id = "test_session_001"
        principal_name = "缇萃百货"
        account_id = ****************
        
        logger.info("1. 创建AsyncCopilotSession实例...")
        session = AsyncCopilotSession(session_id, principal_name, account_id, app_settings)
        logger.success("✅ AsyncCopilotSession实例创建成功")
        
        logger.info("2. 初始化会话...")
        await session.initialize()
        logger.success("✅ AsyncCopilotSession初始化成功")
        
        logger.info("3. 检查会话状态...")
        if session.is_initialized and session.page:
            logger.success("✅ 会话状态正常")
        else:
            logger.error("❌ 会话状态异常")
            return False, "会话状态异常"
        
        logger.info("4. 清理会话...")
        await session.cleanup()
        logger.success("✅ 会话清理成功")
        
        return True, "AsyncCopilotSession直接测试成功"
        
    except Exception as e:
        logger.error(f"❌ AsyncCopilotSession直接测试失败: {e}")
        return False, str(e)

async def run_async_playwright_diagnosis():
    """运行异步Playwright诊断"""
    logger.info("🎯 开始异步Playwright问题诊断")
    logger.info("="*80)
    
    tests = [
        ("基础Playwright测试", test_basic_playwright),
        ("Cookies加载测试", test_cookies_loading),
        ("Playwright与Cookies结合测试", test_playwright_with_cookies),
        ("AsyncCopilotSession直接测试", test_async_copilot_session_direct),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        logger.info("-" * 50)
        
        try:
            success, message = await test_func()
            results.append({
                'test_name': test_name,
                'success': success,
                'message': message
            })
            
            if success:
                logger.success(f"✅ {test_name}: {message}")
            else:
                logger.error(f"❌ {test_name}: {message}")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 执行异常: {e}")
            results.append({
                'test_name': test_name,
                'success': False,
                'message': str(e)
            })
    
    # 生成诊断报告
    logger.info("\n" + "="*80)
    logger.info("🎯 异步Playwright诊断结果")
    logger.info("="*80)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    success_rate = (success_count / total_count) * 100
    
    logger.info(f"📊 诊断统计:")
    logger.info(f"   ✅ 成功测试: {success_count}/{total_count}")
    logger.info(f"   📈 成功率: {success_rate:.1f}%")
    
    logger.info(f"\n📋 详细结果:")
    for result in results:
        status = "✅" if result['success'] else "❌"
        logger.info(f"   {status} {result['test_name']}: {result['message']}")
    
    if success_rate >= 75:
        logger.success("\n🎉 异步Playwright基础功能正常！")
        logger.info("💡 问题可能在于会话池管理或特定的异步上下文")
    elif success_rate >= 50:
        logger.warning("\n⚠️ 异步Playwright部分功能正常")
    else:
        logger.error("\n❌ 异步Playwright存在基础问题")
    
    return results, success_rate >= 50

async def main():
    """主函数"""
    logger.info("🎯 开始异步Playwright问题诊断")
    
    try:
        results, success = await run_async_playwright_diagnosis()
        
        # 保存诊断报告
        report_file = project_root / 'ai_temp' / f'async_playwright_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'diagnosis_results': results,
            'diagnosis_success': success,
            'diagnosis_type': 'async_playwright_issue_diagnosis'
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 诊断报告已保存: {report_file}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
