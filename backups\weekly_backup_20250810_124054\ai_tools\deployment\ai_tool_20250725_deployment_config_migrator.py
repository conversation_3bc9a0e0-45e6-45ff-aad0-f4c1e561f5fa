#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目配置迁移工具，自动化处理配置文件中的路径和环境设置
清理条件: 项目不再需要配置迁移时可删除
"""

import os
import sys
import json
import yaml
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import platform

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class QianchuanConfigMigrator:
    """千川自动化项目配置迁移器"""
    
    def __init__(self, project_root: Path = None, target_env: Dict[str, Any] = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.target_env = target_env or {}
        self.backup_dir = self.project_root / "ai_temp" / "config_backup" / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.migration_log = []
        
    def backup_configs(self) -> bool:
        """备份现有配置文件"""
        print("💾 备份现有配置文件...")
        
        try:
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            config_dir = self.project_root / "config"
            if config_dir.exists():
                for config_file in config_dir.glob("*"):
                    if config_file.is_file():
                        backup_file = self.backup_dir / config_file.name
                        shutil.copy2(config_file, backup_file)
                        self.migration_log.append(f"备份配置文件: {config_file.name}")
            
            print(f"✅ 配置文件已备份到: {self.backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ 配置备份失败: {e}")
            return False
    
    def migrate_settings_yml(self, new_config: Dict[str, Any]) -> bool:
        """迁移settings.yml配置文件"""
        print("⚙️ 迁移settings.yml配置...")
        
        settings_file = self.project_root / "config" / "settings.yml"
        
        try:
            # 读取现有配置
            current_config = {}
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    current_config = yaml.safe_load(f) or {}
            
            # 应用新配置
            updated_config = self.merge_configs(current_config, new_config)
            
            # 写入更新后的配置
            with open(settings_file, 'w', encoding='utf-8') as f:
                yaml.dump(updated_config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.migration_log.append("更新settings.yml配置文件")
            print("✅ settings.yml配置已更新")
            return True
            
        except Exception as e:
            print(f"❌ settings.yml迁移失败: {e}")
            return False
    
    def merge_configs(self, current: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置，保留现有配置的同时应用新配置"""
        result = current.copy()
        
        for key, value in new.items():
            if isinstance(value, dict) and key in result and isinstance(result[key], dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def update_workflow_paths(self, new_workflow_dir: str, new_source_dirs: List[str] = None) -> bool:
        """更新工作流相关路径"""
        print("📁 更新工作流路径配置...")
        
        try:
            settings_file = self.project_root / "config" / "settings.yml"
            
            with open(settings_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新工作流资产目录
            config['custom_workflow_assets_dir'] = new_workflow_dir
            
            # 更新素材收集目录
            if 'workflow' not in config:
                config['workflow'] = {}
            if 'material_collection' not in config['workflow']:
                config['workflow']['material_collection'] = {}
            
            # 更新目标目录
            principal_name = config['workflow'].get('comment_and_violation_patrol_principals', ['缇萃百货'])[0]
            config['workflow']['material_collection']['dest_dir'] = f"{new_workflow_dir}/01_materials_to_process/{principal_name}"
            
            # 更新源目录
            if new_source_dirs:
                config['workflow']['material_collection']['source_dirs'] = new_source_dirs
            
            # 保存配置
            with open(settings_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.migration_log.append(f"更新工作流目录: {new_workflow_dir}")
            print("✅ 工作流路径配置已更新")
            return True
            
        except Exception as e:
            print(f"❌ 工作流路径更新失败: {e}")
            return False
    
    def update_database_config(self, db_type: str, db_config: Dict[str, Any]) -> bool:
        """更新数据库配置"""
        print("🗄️ 更新数据库配置...")
        
        try:
            settings_file = self.project_root / "config" / "settings.yml"
            
            with open(settings_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            if 'database' not in config:
                config['database'] = {}
            
            config['database']['type'] = db_type
            
            if db_type == 'postgresql':
                config['database']['postgresql'] = db_config
            elif db_type == 'sqlite':
                config['database']['sqlite'] = db_config
            
            # 保存配置
            with open(settings_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.migration_log.append(f"更新数据库配置: {db_type}")
            print("✅ 数据库配置已更新")
            return True
            
        except Exception as e:
            print(f"❌ 数据库配置更新失败: {e}")
            return False
    
    def create_directory_structure(self, workflow_dir: str) -> bool:
        """创建必要的目录结构"""
        print("📂 创建目录结构...")
        
        try:
            base_dir = Path(workflow_dir)
            
            # 创建主要目录
            directories = [
                "01_materials_to_process",
                "02_materials_approved", 
                "03_materials_rejected",
                "auth_states",
                "database",
                "logs"
            ]
            
            for dir_name in directories:
                dir_path = base_dir / dir_name
                dir_path.mkdir(parents=True, exist_ok=True)
                self.migration_log.append(f"创建目录: {dir_path}")
            
            # 创建主体特定目录
            principal_dirs = ["缇萃百货"]  # 可以从配置中读取
            for principal in principal_dirs:
                for stage in ["01_materials_to_process", "02_materials_approved", "03_materials_rejected"]:
                    principal_dir = base_dir / stage / principal
                    principal_dir.mkdir(parents=True, exist_ok=True)
                    self.migration_log.append(f"创建主体目录: {principal_dir}")
            
            # 创建项目日志目录
            project_logs = self.project_root / "logs"
            project_logs.mkdir(exist_ok=True)
            
            print("✅ 目录结构创建完成")
            return True
            
        except Exception as e:
            print(f"❌ 目录创建失败: {e}")
            return False
    
    def generate_migration_script(self, target_config: Dict[str, Any]) -> str:
        """生成迁移脚本"""
        script_content = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化项目配置迁移脚本
自动生成时间: {datetime.now().isoformat()}
"""

import os
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from ai_tools.deployment.ai_tool_20250725_deployment_config_migrator import QianchuanConfigMigrator

def main():
    """执行配置迁移"""
    print("🚀 开始配置迁移...")
    
    # 目标配置
    target_config = {json.dumps(target_config, ensure_ascii=False, indent=8)}
    
    migrator = QianchuanConfigMigrator()
    
    # 1. 备份现有配置
    if not migrator.backup_configs():
        print("❌ 配置备份失败，停止迁移")
        return False
    
    # 2. 创建目录结构
    workflow_dir = target_config.get('workflow_dir', 'workflow_assets')
    if not migrator.create_directory_structure(workflow_dir):
        print("❌ 目录创建失败")
        return False
    
    # 3. 更新工作流路径
    source_dirs = target_config.get('source_dirs', [])
    if not migrator.update_workflow_paths(workflow_dir, source_dirs):
        print("❌ 工作流路径更新失败")
        return False
    
    # 4. 更新数据库配置
    db_config = target_config.get('database', {{}})
    db_type = db_config.get('type', 'sqlite')
    if not migrator.update_database_config(db_type, db_config.get(db_type, {{}})):
        print("❌ 数据库配置更新失败")
        return False
    
    print("✅ 配置迁移完成")
    print("\\n📋 迁移日志:")
    for log_entry in migrator.migration_log:
        print(f"  - {{log_entry}}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        return script_content
    
    def interactive_migration(self) -> bool:
        """交互式配置迁移"""
        print("🔧 交互式配置迁移")
        print("=" * 50)
        
        try:
            # 获取用户输入
            print("\n请提供新环境的配置信息:")
            
            # 工作流目录
            default_workflow = str(self.project_root / "workflow_assets")
            workflow_dir = input(f"工作流资产目录 (默认: {default_workflow}): ").strip()
            if not workflow_dir:
                workflow_dir = default_workflow
            
            # 数据库类型
            print("\n数据库类型选择:")
            print("1. SQLite (推荐，简单)")
            print("2. PostgreSQL (生产环境)")
            db_choice = input("请选择 (1/2): ").strip()
            
            db_config = {}
            if db_choice == "2":
                db_config = {
                    "type": "postgresql",
                    "postgresql": {
                        "host": input("PostgreSQL主机 (默认: localhost): ").strip() or "localhost",
                        "port": int(input("PostgreSQL端口 (默认: 5432): ").strip() or "5432"),
                        "dbname": input("数据库名称 (默认: qianchuan_analytics): ").strip() or "qianchuan_analytics",
                        "user": input("用户名: ").strip(),
                        "password": input("密码: ").strip(),
                        "postgres_bin_path": input("PostgreSQL bin路径 (可选): ").strip()
                    }
                }
            else:
                db_config = {
                    "type": "sqlite",
                    "sqlite": {
                        "db_name": "qianchuan_aw.db"
                    }
                }
            
            # 执行迁移
            print("\n🚀 开始执行迁移...")
            
            # 1. 备份配置
            if not self.backup_configs():
                return False
            
            # 2. 创建目录结构
            if not self.create_directory_structure(workflow_dir):
                return False
            
            # 3. 更新工作流路径
            if not self.update_workflow_paths(workflow_dir):
                return False
            
            # 4. 更新数据库配置
            if not self.update_database_config(db_config["type"], db_config.get(db_config["type"], {})):
                return False
            
            print("\n✅ 配置迁移完成!")
            print("\n📋 迁移日志:")
            for log_entry in self.migration_log:
                print(f"  - {log_entry}")
            
            print(f"\n💾 配置备份位置: {self.backup_dir}")
            
            return True
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消迁移")
            return False
        except Exception as e:
            print(f"\n❌ 迁移失败: {e}")
            return False

def main():
    """主函数"""
    print("🚀 千川自动化项目配置迁移工具")
    print("=" * 60)
    
    migrator = QianchuanConfigMigrator()
    
    # 交互式迁移
    success = migrator.interactive_migration()
    
    if success:
        print("\n🎉 配置迁移成功完成!")
        print("\n📝 下一步操作:")
        print("1. 检查配置文件是否正确")
        print("2. 安装Python依赖: pip install -r requirements.txt")
        print("3. 安装并启动Redis服务")
        print("4. 初始化数据库")
        print("5. 测试系统启动")
    else:
        print("\n❌ 配置迁移失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
