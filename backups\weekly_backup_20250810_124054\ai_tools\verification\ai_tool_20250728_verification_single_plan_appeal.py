#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 对单个指定计划进行提审验证
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def get_plan_details(campaign_id):
    """获取计划详细信息"""
    logger.info(f"🔍 查询计划详细信息: {campaign_id}")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 查询计划详细信息，包括关联的账户和主体
        cursor.execute("""
            SELECT 
                c.campaign_id_qc,
                c.status,
                c.appeal_status,
                c.appeal_error_message,
                c.first_appeal_at,
                c.created_at,
                a.account_id_qc,
                p.name as principal_name
            FROM campaigns c
            JOIN ad_accounts a ON c.account_id = a.id
            JOIN principals p ON a.principal_id = p.id
            WHERE c.campaign_id_qc = %s
        """, (campaign_id,))
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            plan_info = {
                'campaign_id_qc': result[0],
                'status': result[1],
                'appeal_status': result[2],
                'appeal_error_message': result[3],
                'first_appeal_at': result[4],
                'created_at': result[5],
                'account_id_qc': result[6],
                'principal_name': result[7]
            }
            
            logger.success("✅ 计划信息查询成功")
            logger.info(f"   📋 计划ID: {plan_info['campaign_id_qc']}")
            logger.info(f"   📊 状态: {plan_info['status']}")
            logger.info(f"   📤 提审状态: {plan_info['appeal_status']}")
            logger.info(f"   🏢 账户: {plan_info['account_id_qc']}")
            logger.info(f"   👤 主体: {plan_info['principal_name']}")
            logger.info(f"   📅 创建时间: {plan_info['created_at']}")
            
            if plan_info['appeal_error_message']:
                logger.warning(f"   ⚠️ 上次错误: {plan_info['appeal_error_message']}")
            
            return plan_info
        else:
            logger.error(f"❌ 未找到计划: {campaign_id}")
            return None
            
    except Exception as e:
        logger.error(f"❌ 查询计划信息失败: {e}")
        return None

def reset_plan_appeal_status(campaign_id):
    """重置计划的提审状态"""
    logger.info(f"🔄 重置计划提审状态: {campaign_id}")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 重置提审状态
        cursor.execute("""
            UPDATE campaigns 
            SET appeal_status = NULL,
                appeal_error_message = NULL,
                first_appeal_at = NULL
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        affected_rows = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        if affected_rows > 0:
            logger.success(f"✅ 计划状态已重置: {campaign_id}")
            return True
        else:
            logger.warning(f"⚠️ 没有找到需要重置的计划: {campaign_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 重置计划状态失败: {e}")
        return False

def appeal_single_plan(campaign_id, principal_name, account_id):
    """对单个计划进行提审"""
    logger.info(f"🎯 开始提审计划: {campaign_id}")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_appeal_adapter import AsyncAppealAdapter
        
        # 创建提审适配器
        adapter = AsyncAppealAdapter()
        
        logger.info(f"📤 正在提审...")
        logger.info(f"   👤 主体: {principal_name}")
        logger.info(f"   🏢 账户: {account_id}")
        logger.info(f"   📋 计划: {campaign_id}")
        
        # 执行提审
        result = adapter.appeal_plan(
            principal_name=principal_name,
            account_id=account_id,
            plan_id=campaign_id
        )
        
        if result.get('success'):
            logger.success(f"✅ 计划提审成功: {campaign_id}")
            logger.info(f"   📝 结果: {result.get('message', '提审成功')}")
            return True, "提审成功"
        else:
            error_msg = result.get('error', '未知错误')
            logger.error(f"❌ 计划提审失败: {campaign_id}")
            logger.error(f"   📝 错误: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        logger.error(f"❌ 提审过程中发生异常: {e}")
        return False, str(e)

def check_plan_status_after_appeal(campaign_id, wait_seconds=10):
    """检查提审后的计划状态"""
    logger.info(f"🔍 检查提审后状态: {campaign_id}")
    
    # 等待一段时间让数据库更新
    logger.info(f"⏳ 等待 {wait_seconds} 秒...")
    time.sleep(wait_seconds)
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                status,
                appeal_status,
                appeal_error_message,
                first_appeal_at
            FROM campaigns 
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            status, appeal_status, appeal_error_message, first_appeal_at = result
            
            logger.info("📊 提审后状态:")
            logger.info(f"   📋 计划状态: {status}")
            logger.info(f"   📤 提审状态: {appeal_status}")
            logger.info(f"   📅 提审时间: {first_appeal_at}")
            
            if appeal_error_message:
                logger.error(f"   ❌ 错误信息: {appeal_error_message}")
                return False, appeal_error_message
            elif appeal_status == 'appeal_pending':
                logger.success("✅ 提审状态正常：appeal_pending")
                return True, "提审成功，状态已更新"
            elif appeal_status is None:
                logger.warning("⚠️ 提审状态未更新，可能仍在处理中")
                return False, "状态未更新"
            else:
                logger.info(f"📋 当前提审状态: {appeal_status}")
                return True, f"状态: {appeal_status}"
        else:
            logger.error(f"❌ 未找到计划: {campaign_id}")
            return False, "计划不存在"
            
    except Exception as e:
        logger.error(f"❌ 检查状态失败: {e}")
        return False, str(e)

def run_single_plan_appeal_verification(campaign_id):
    """运行单个计划提审验证"""
    logger.info("🚀 开始单个计划提审验证...")
    logger.info("="*80)
    logger.info(f"🎯 目标计划: {campaign_id}")
    logger.info("="*80)
    
    verification_results = {
        'plan_found': False,
        'status_reset': False,
        'appeal_executed': False,
        'status_updated': False
    }
    
    try:
        # 1. 获取计划详细信息
        plan_info = get_plan_details(campaign_id)
        verification_results['plan_found'] = plan_info is not None
        
        if not plan_info:
            logger.error("❌ 无法获取计划信息，终止验证")
            return verification_results, "计划不存在"
        
        # 2. 重置提审状态（如果需要）
        if plan_info['appeal_status'] in ['submission_failed', 'submission_error']:
            logger.info("🔄 检测到失败状态，正在重置...")
            verification_results['status_reset'] = reset_plan_appeal_status(campaign_id)
        else:
            logger.info("📋 计划状态正常，无需重置")
            verification_results['status_reset'] = True
        
        # 3. 执行提审
        appeal_success, appeal_message = appeal_single_plan(
            campaign_id,
            plan_info['principal_name'],
            plan_info['account_id_qc']
        )
        verification_results['appeal_executed'] = appeal_success
        
        # 4. 检查提审后状态
        if appeal_success:
            status_ok, status_message = check_plan_status_after_appeal(campaign_id)
            verification_results['status_updated'] = status_ok
        
        # 生成验证报告
        logger.info("\n" + "="*80)
        logger.info("🎯 单个计划提审验证结果")
        logger.info("="*80)
        
        for check_name, result in verification_results.items():
            status = "✅" if result else "❌"
            check_display = check_name.replace('_', ' ').title()
            logger.info(f"{status} {check_display}")
        
        success_count = sum(verification_results.values())
        total_count = len(verification_results)
        success_rate = (success_count / total_count) * 100
        
        logger.info(f"\n📈 验证通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 100:
            logger.success("🎉 单个计划提审验证完全成功！")
            logger.info("\n📋 验证结果:")
            logger.info(f"✅ 计划 {campaign_id} 提审成功")
            logger.info("✅ 提审模块工作正常")
            logger.info("✅ 数据库状态正确更新")
            logger.info("✅ 完整的提审流程验证通过")
            
            final_message = "提审模块验证成功"
            
        elif success_rate >= 75:
            logger.warning("⚠️ 单个计划提审基本成功，但有小问题")
            final_message = f"提审基本成功，成功率 {success_rate:.1f}%"
            
        else:
            logger.error("❌ 单个计划提审验证失败")
            final_message = f"提审验证失败，成功率 {success_rate:.1f}%"
        
        return verification_results, final_message
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return verification_results, str(e)

def main():
    """主函数"""
    # 今天最后一个新建的测试计划ID
    target_campaign_id = "1838861965756505"
    
    logger.info(f"🎯 开始对计划 {target_campaign_id} 进行单独提审验证")
    
    try:
        results, message = run_single_plan_appeal_verification(target_campaign_id)
        
        # 保存验证报告
        report_file = project_root / 'ai_temp' / f'single_plan_appeal_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'target_campaign_id': target_campaign_id,
            'verification_results': results,
            'final_message': message
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 验证报告已保存: {report_file}")
        
        # 返回成功率
        success_rate = sum(results.values()) / len(results) * 100
        return success_rate >= 75
        
    except Exception as e:
        logger.error(f"❌ 主函数执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
