# Celery进程重启操作指南

**目的**: 应用工作流配置修复后，重启Celery进程以确保新配置生效  
**适用场景**: 工作流配置修复后的必要操作  
**预计时间**: 5-10分钟  

---

## 🔄 重启步骤

### 1. 停止现有Celery进程

#### 方法一：通过任务管理器（推荐）
1. 按 `Ctrl + Shift + Esc` 打开任务管理器
2. 在"进程"标签页中查找以下进程：
   - `python.exe` (运行 run_celery_worker.py)
   - `python.exe` (运行 run_celery_beat.py)
3. 选中这些进程，点击"结束任务"

#### 方法二：通过命令行
```bash
# 在项目根目录下执行
taskkill /f /im python.exe
# 注意：这会关闭所有Python进程，请确保没有其他重要Python程序在运行
```

### 2. 清理Redis队列（可选但推荐）

```bash
# 连接到Redis并清理队列
redis-cli
> FLUSHDB
> EXIT
```

### 3. 重新启动Celery进程

#### 启动Celery Worker
```bash
# 在项目根目录下，打开第一个终端窗口
python run_celery_worker.py
```

#### 启动Celery Beat
```bash
# 在项目根目录下，打开第二个终端窗口
python run_celery_beat.py
```

---

## ✅ 验证重启成功

### 1. 检查进程状态
- Worker进程应该显示：`[INFO/MainProcess] Connected to redis://localhost:6379/0`
- Beat进程应该显示：`[INFO/MainProcess] Scheduler: Sending due task`

### 2. 检查任务调度
运行监控脚本验证：
```bash
python ai_tools/monitoring/ai_tool_20250728_monitoring_workflow_status_simple.py
```

### 3. 观察日志输出
- Worker日志应该显示任务接收和执行
- Beat日志应该显示定时任务派发

---

## 🎯 预期效果

### 重启后应该看到的改进
1. **素材收集恢复**: 新素材能够自动从源目录收集到处理目录
2. **孤岛素材处理**: 206个重新激活的素材开始被处理
3. **僵尸清理启动**: 超时的processing状态会被自动重置
4. **评论管理恢复**: 每5分钟检查一次评论管理
5. **违规检测恢复**: 每30分钟检查一次账户违规

### 关键指标监控
- `new` 状态素材数量应该逐渐减少
- `processing` 状态素材应该正常流转
- 不应该再出现 `pending_upload` 孤岛素材

---

## 🚨 故障排除

### 常见问题及解决方案

#### 1. Celery Worker启动失败
**症状**: 显示连接Redis失败
**解决**: 
```bash
# 检查Redis是否运行
redis-cli ping
# 如果没有响应，启动Redis服务
```

#### 2. 任务不执行
**症状**: Beat发送任务但Worker不处理
**解决**: 
- 检查Worker进程是否正常运行
- 检查Redis连接是否正常
- 重启Worker进程

#### 3. 配置未生效
**症状**: 任务调度频率没有改变
**解决**: 
- 确认配置文件已保存
- 删除 `logs/celerybeat-schedule.db` 文件
- 重启Beat进程

#### 4. 素材处理停滞
**症状**: `new` 状态素材数量不减少
**解决**: 
- 检查API配置是否正确
- 检查账户权限是否正常
- 查看Worker日志中的错误信息

---

## 📊 监控命令

### 实时监控工作流状态
```bash
# 每分钟运行一次，观察状态变化
watch -n 60 "python ai_tools/monitoring/ai_tool_20250728_monitoring_workflow_status_simple.py"
```

### 检查Celery进程状态
```bash
# 检查Worker进程
ps aux | grep "run_celery_worker"

# 检查Beat进程  
ps aux | grep "run_celery_beat"
```

### 查看Redis队列状态
```bash
redis-cli
> INFO keyspace
> KEYS celery*
```

---

## 📋 重启检查清单

### 重启前检查
- [ ] 确认配置文件已备份
- [ ] 确认当前没有重要任务正在执行
- [ ] 确认Redis服务正常运行

### 重启过程检查
- [ ] 旧进程已完全停止
- [ ] Redis队列已清理（可选）
- [ ] Worker进程启动成功
- [ ] Beat进程启动成功

### 重启后验证
- [ ] 监控脚本显示正常状态
- [ ] 新素材开始被处理
- [ ] 任务调度频率符合预期
- [ ] 无错误日志输出

---

**重要提醒**: 重启Celery进程是应用配置修复的关键步骤。请按照此指南逐步操作，并在重启后持续监控工作流状态，确保所有功能正常运行。
