#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 验证账户内复制功能修改
依赖关系: web_ui.py
清理条件: 验证完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_target_account_removal():
    """检查是否已移除目标账户选择功能"""
    print("🔍 检查目标账户选择功能移除...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有目标账户选择器
        issues = []
        
        # 检查是否还有"选择目标账户"相关代码
        if 'label="选择目标账户"' in content:
            issues.append("仍然存在'选择目标账户'标签")
        
        if 'key="replication_target_accounts"' in content:
            issues.append("仍然存在单账户复制的目标账户选择器")
            
        if 'key="batch_replication_target_accounts"' in content:
            issues.append("仍然存在批量复制的目标账户选择器")
        
        # 检查是否有正确的说明文字
        if "根据千川平台规则" not in content:
            issues.append("缺少千川平台规则说明")
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ 目标账户选择功能已正确移除")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_function_signatures():
    """检查函数签名是否已更新"""
    print("\n🔧 检查函数签名...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 检查execute_single_replication_task函数签名
        if 'def execute_single_replication_task(selected_target_accounts,' in content:
            issues.append("execute_single_replication_task仍然包含selected_target_accounts参数")
        
        # 检查execute_batch_replication_task函数签名
        if 'def execute_batch_replication_task(selected_target_accounts,' in content:
            issues.append("execute_batch_replication_task仍然包含selected_target_accounts参数")
        
        # 检查是否有正确的新函数签名
        if 'def execute_single_replication_task(new_plan_prefix,' not in content:
            issues.append("execute_single_replication_task函数签名未正确更新")
            
        if 'def execute_batch_replication_task(new_plan_prefix,' not in content:
            issues.append("execute_batch_replication_task函数签名未正确更新")
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ 函数签名已正确更新")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_ui_text_updates():
    """检查UI文字是否已更新"""
    print("\n📝 检查UI文字更新...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_texts = [
            "批量账户内计划复制",
            "在各自账户内进行复制",
            "每个账户的计划在该账户内复制",
            "根据千川平台规则限制",
            "计划复制只能在同一个广告账户内进行"
        ]
        
        missing_texts = []
        for text in required_texts:
            if text not in content:
                missing_texts.append(text)
        
        if missing_texts:
            print("❌ 缺少必要的UI文字:")
            for text in missing_texts:
                print(f"   - {text}")
            return False
        else:
            print("✅ UI文字已正确更新")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_logic_updates():
    """检查业务逻辑是否已更新"""
    print("\n🔄 检查业务逻辑更新...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # 检查是否有按源账户分组的逻辑
        if 'campaigns_by_account' not in content:
            issues.append("缺少按源账户分组的逻辑")
        
        # 检查是否使用源账户作为目标账户
        if 'target_account_ids=[account_id]' not in content:
            issues.append("未使用源账户作为目标账户")
        
        # 检查是否有正确的日志信息
        if '账户内复制任务' not in content:
            issues.append("缺少账户内复制的日志信息")
        
        if issues:
            print("❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ 业务逻辑已正确更新")
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_syntax():
    """检查语法"""
    print("\n🔧 检查语法...")
    
    try:
        import py_compile
        web_ui_path = project_root / 'web_ui.py'
        py_compile.compile(str(web_ui_path), doraise=True)
        print("✅ 语法检查通过")
        return True
    except Exception as e:
        print(f"❌ 语法错误: {e}")
        return False

def main():
    """主验证函数"""
    print("🔧 账户内复制功能修改验证")
    print("=" * 50)
    
    checks = [
        ("目标账户选择移除检查", check_target_account_removal),
        ("函数签名检查", check_function_signatures),
        ("UI文字更新检查", check_ui_text_updates),
        ("业务逻辑更新检查", check_logic_updates),
        ("语法检查", check_syntax)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}")
        print("-" * 30)
        
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 账户内复制功能修改验证通过！")
        print("\n💡 功能说明:")
        print("   ✅ 单账户复制：在当前选中账户内复制计划")
        print("   ✅ 批量账户复制：对多个账户执行账户内复制")
        print("   ✅ 符合千川平台规则：不跨账户复制")
        print("\n🚀 测试建议:")
        print("   1. 重新启动Streamlit: streamlit run web_ui.py")
        print("   2. 访问投放中心 → 批量账户内计划复制")
        print("   3. 测试两个选项卡的完整流程")
    else:
        print("⚠️ 部分验证失败，需要进一步修复")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
