#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
原子状态管理器 - 兼容性包装器

为了保持向后兼容性，这个文件现在作为增强型状态管理器的包装器。
新代码应该直接使用 EnhancedAtomicStateManager。
"""

import os
from datetime import datetime, timezone
from contextlib import contextmanager
from typing import List, Dict, Any
from qianchuan_aw.database.models import LocalCreative
from qianchuan_aw.utils.enhanced_atomic_state_manager import (
    EnhancedAtomicStateManager,
    StateTransitionError,
    ConcurrencyError
)
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.unified_material_status import MaterialStatus


class AtomicStateManager:
    """
    原子状态管理器 - 兼容性包装器

    这个类现在是 EnhancedAtomicStateManager 的包装器，
    保持与现有代码的兼容性。
    """

    def __init__(self, db_session, redis_client=None):
        self.db = db_session
        self._enhanced_manager = EnhancedAtomicStateManager(db_session, redis_client)
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str, metadata: Dict = None):
        """
        原子状态转换上下文管理器 - 兼容性包装器

        委托给增强型状态管理器处理
        """
        try:
            with self._enhanced_manager.atomic_state_transition(
                creative_id, from_status, to_status, metadata, use_distributed_lock=False
            ) as creative:
                yield creative

        except (StateTransitionError, ConcurrencyError) as e:
            # 转换为原来的异常类型以保持兼容性
            raise ValueError(str(e))
    
    def batch_state_transition(self, transitions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        批量状态转换 - 兼容性包装器

        Args:
            transitions: 转换列表，格式: [{'creative_id': int, 'from_status': str, 'to_status': str}, ...]

        Returns:
            List[Dict]: 转换结果列表
        """
        # 转换为增强管理器的格式
        enhanced_transitions = [
            (t['creative_id'], t['from_status'], t['to_status'])
            for t in transitions
        ]

        results = self._enhanced_manager.batch_state_transition(enhanced_transitions)

        # 转换回兼容格式
        return [
            {
                'creative_id': r.creative_id,
                'from_status': r.from_status,
                'to_status': r.to_status,
                'success': r.success,
                'error': r.error_message
            }
            for r in results
        ]

    def safe_dispatch_upload_task(self, creative_id: int, account_id: int, file_path: str, principal_name: str):
        """
        安全派发上传任务 - 兼容性方法

        使用增强型状态管理器进行状态转换
        """
        try:
            # 获取当前状态
            current_state = self._enhanced_manager.get_current_state(creative_id)
            if not current_state:
                logger.error(f"❌ 素材 {creative_id} 不存在")
                return False

            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"❌ 文件不存在: {file_path}")
                return False

            # 使用增强型管理器进行状态转换
            result = self._enhanced_manager.safe_state_transition(
                creative_id,
                current_state,
                MaterialStatus.UPLOADING.value,
                {'account_id': account_id, 'file_path': file_path, 'principal_name': principal_name}
            )

            if not result.success:
                logger.error(f"❌ 状态转换失败: {result.error_message}")
                return False

            logger.info(f"📤 派发上传任务: 素材 {creative_id} -> 账户 {account_id}")

            # 🚀 异步调用实际的上传任务
            try:
                from qianchuan_aw.workflows.tasks import upload_single_video

                # 异步派发上传任务
                task_result = upload_single_video.delay(
                    local_creative_id=creative_id,
                    account_id=account_id,
                    file_path=file_path,
                    principal_name=principal_name
                )

                logger.info(f"🚀 异步上传任务已派发: {task_result.id}")
                return True

            except Exception as task_error:
                logger.error(f"❌ 派发异步上传任务失败: {task_error}")
                # 回滚状态
                self._enhanced_manager.safe_state_transition(
                    creative_id,
                    MaterialStatus.UPLOADING.value,
                    MaterialStatus.PENDING_UPLOAD.value
                )
                return False

        except Exception as e:
            logger.error(f"❌ 派发上传任务失败 (Creative {creative_id}): {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息 - 兼容性方法"""
        return self._enhanced_manager.get_statistics()
