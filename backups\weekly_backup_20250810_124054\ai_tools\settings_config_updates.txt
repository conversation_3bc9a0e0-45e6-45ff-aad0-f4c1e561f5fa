
# 建议在config/settings.yml中添加或更新以下配置：

# 1. 调整提审调度频率（降低系统负载）
workflow:
  plan_appeal:
    enabled: true
    interval_seconds: 300  # 从180调整为300

# 2. 增加浏览器超时时间（提高稳定性）
browser:
  default_timeout: 30000  # 从20000调整为30000
  headless: true

# 3. 添加提审系统专用配置
appeal_system:
  enabled: true
  schedule:
    interval_seconds: 300
    max_concurrent_appeals: 3
  browser:
    timeout_ms: 30000
    retry_on_timeout: true
  retry:
    max_attempts: 3
    initial_delay: 1.0
    exponential_backoff: true
  state_management:
    atomic_updates: true
    duplicate_prevention: true
  monitoring:
    success_rate_threshold: 90.0
    alert_on_failure_rate: true
