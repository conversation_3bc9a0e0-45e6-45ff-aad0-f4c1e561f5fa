#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 修复系统可靠性问题，确保100%自主运行
清理条件: 系统完全稳定后可归档
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from loguru import logger
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timedelta
import glob
import re

class SystemReliabilityFixer:
    """系统可靠性修复器"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'qianchuan_analytics',
            'user': 'lanfeng',
            'password': 'zmx5062686'
        }
        
    def fix_all_status_inconsistencies(self):
        """修复所有状态不一致问题"""
        logger.info("🔧 开始修复系统状态不一致问题...")
        
        results = {
            'code_fixes': self.fix_code_status_logic(),
            'database_fixes': self.fix_database_status_inconsistencies(),
            'monitoring_setup': self.setup_monitoring_system()
        }
        
        return results
    
    def fix_code_status_logic(self):
        """修复代码中的状态逻辑"""
        logger.info("📝 修复代码中的状态设置逻辑...")
        
        # 查找所有设置appeal_status为NULL的文件
        problematic_files = []
        
        # 搜索模式
        patterns = [
            r"appeal_status\s*=\s*NULL",
            r"appeal_status\s*=\s*None",
            r"SET\s+appeal_status\s*=\s*NULL"
        ]
        
        # 搜索目录
        search_dirs = [
            "ai_tools/",
            "src/qianchuan_aw/",
            "tools/"
        ]
        
        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                for root, dirs, files in os.walk(search_dir):
                    for file in files:
                        if file.endswith('.py'):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                    
                                for pattern in patterns:
                                    if re.search(pattern, content, re.IGNORECASE):
                                        problematic_files.append(file_path)
                                        break
                                        
                            except Exception as e:
                                logger.warning(f"无法读取文件 {file_path}: {e}")
        
        logger.info(f"📋 发现 {len(problematic_files)} 个需要修复的文件:")
        for file_path in problematic_files:
            logger.info(f"  - {file_path}")
        
        return {
            'problematic_files': problematic_files,
            'fix_needed': len(problematic_files) > 0
        }
    
    def fix_database_status_inconsistencies(self):
        """修复数据库中的状态不一致"""
        logger.info("🗄️ 修复数据库状态不一致...")
        
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # 1. 检查静默失败的计划
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                  AND c.status = 'AUDITING'
                  AND c.first_appeal_at IS NULL
                  AND c.created_at < NOW() - INTERVAL '30 minutes'
                  AND c.appeal_status IS NULL
            """)
            
            silent_failures = cursor.fetchone()['count']
            
            # 2. 修复静默失败
            if silent_failures > 0:
                cursor.execute("""
                    UPDATE campaigns 
                    SET appeal_status = 'appeal_pending',
                        last_updated = NOW()
                    WHERE id IN (
                        SELECT c.id
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                          AND c.status = 'AUDITING'
                          AND c.first_appeal_at IS NULL
                          AND c.created_at < NOW() - INTERVAL '30 minutes'
                          AND c.appeal_status IS NULL
                    )
                """)
                
                fixed_count = cursor.rowcount
                conn.commit()
                logger.success(f"✅ 修复了 {fixed_count} 个静默失败的计划")
            
            # 3. 检查卡住的计划
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                  AND c.appeal_status = 'appeal_executing'
                  AND c.last_updated < NOW() - INTERVAL '10 minutes'
            """)
            
            stuck_plans = cursor.fetchone()['count']
            
            # 4. 修复卡住的计划
            if stuck_plans > 0:
                cursor.execute("""
                    UPDATE campaigns 
                    SET appeal_status = 'appeal_pending',
                        appeal_error_message = 'Reset due to stuck status',
                        last_updated = NOW()
                    WHERE id IN (
                        SELECT c.id
                        FROM campaigns c
                        JOIN ad_accounts aa ON c.account_id = aa.id
                        WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                          AND c.appeal_status = 'appeal_executing'
                          AND c.last_updated < NOW() - INTERVAL '10 minutes'
                    )
                """)
                
                unstuck_count = cursor.rowcount
                conn.commit()
                logger.success(f"✅ 修复了 {unstuck_count} 个卡住的计划")
            
            conn.close()
            
            return {
                'silent_failures_fixed': fixed_count if silent_failures > 0 else 0,
                'stuck_plans_fixed': unstuck_count if stuck_plans > 0 else 0,
                'total_fixed': (fixed_count if silent_failures > 0 else 0) + (unstuck_count if stuck_plans > 0 else 0)
            }
            
        except Exception as e:
            logger.error(f"❌ 修复数据库状态失败: {e}")
            return {'error': str(e)}
    
    def setup_monitoring_system(self):
        """设置监控系统"""
        logger.info("📊 设置系统监控...")
        
        monitoring_script = '''#!/usr/bin/env python3
"""
系统健康监控脚本
每10分钟运行一次，检测和修复异常状态
"""

import psycopg2
from datetime import datetime, timedelta
from loguru import logger

def check_system_health():
    """检查系统健康状态"""
    try:
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            database='qianchuan_analytics',
            user='lanfeng',
            password='zmx5062686'
        )
        
        cursor = conn.cursor()
        
        # 检查静默失败
        cursor.execute("""
            SELECT COUNT(*) FROM campaigns c
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
              AND c.status = 'AUDITING'
              AND c.first_appeal_at IS NULL
              AND c.created_at < NOW() - INTERVAL '30 minutes'
              AND c.appeal_status IS NULL
        """)
        
        silent_failures = cursor.fetchone()[0]
        
        if silent_failures > 0:
            logger.error(f"🚨 发现 {silent_failures} 个静默失败的计划！")
            
            # 自动修复
            cursor.execute("""
                UPDATE campaigns 
                SET appeal_status = 'appeal_pending',
                    last_updated = NOW()
                WHERE id IN (
                    SELECT c.id FROM campaigns c
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
                      AND c.status = 'AUDITING'
                      AND c.first_appeal_at IS NULL
                      AND c.created_at < NOW() - INTERVAL '30 minutes'
                      AND c.appeal_status IS NULL
                )
            """)
            
            fixed = cursor.rowcount
            conn.commit()
            logger.success(f"✅ 自动修复了 {fixed} 个静默失败")
        
        # 检查提审成功率
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN first_appeal_at IS NOT NULL THEN 1 END) as submitted
            FROM campaigns c
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE (aa.account_type = 'TEST' OR aa.name LIKE '%测试%')
              AND c.created_at >= NOW() - INTERVAL '24 hours'
        """)
        
        result = cursor.fetchone()
        total, submitted = result
        
        if total > 0:
            success_rate = submitted / total * 100
            logger.info(f"📊 24小时提审成功率: {success_rate:.1f}% ({submitted}/{total})")
            
            if success_rate < 95:
                logger.error(f"🚨 提审成功率过低: {success_rate:.1f}%")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ 健康检查失败: {e}")

if __name__ == "__main__":
    check_system_health()
'''
        
        # 保存监控脚本
        monitor_file = "ai_tools/maintenance/ai_tool_20250802_maintenance_health_monitor.py"
        try:
            with open(monitor_file, 'w', encoding='utf-8') as f:
                f.write(monitoring_script)
            logger.success(f"✅ 监控脚本已创建: {monitor_file}")
            
            return {
                'monitor_script_created': True,
                'monitor_file': monitor_file,
                'recommendation': '建议设置cron任务每10分钟运行一次'
            }
            
        except Exception as e:
            logger.error(f"❌ 创建监控脚本失败: {e}")
            return {'error': str(e)}
    
    def generate_fix_report(self, results):
        """生成修复报告"""
        logger.info("📋 生成系统修复报告...")
        
        report = f"""
# 系统可靠性修复报告

**修复时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 修复结果

### 代码修复
- 发现问题文件: {len(results['code_fixes']['problematic_files'])} 个
- 需要手动修复: {'是' if results['code_fixes']['fix_needed'] else '否'}

### 数据库修复
- 静默失败修复: {results['database_fixes'].get('silent_failures_fixed', 0)} 个
- 卡住计划修复: {results['database_fixes'].get('stuck_plans_fixed', 0)} 个
- 总计修复: {results['database_fixes'].get('total_fixed', 0)} 个

### 监控系统
- 监控脚本: {'已创建' if results['monitoring_setup'].get('monitor_script_created') else '创建失败'}

## 建议

1. **立即行动**: 手动修复发现的问题文件
2. **设置监控**: 配置cron任务定期运行健康检查
3. **持续观察**: 监控系统运行状态，确保问题不再发生

## 问题文件列表
"""
        
        for file_path in results['code_fixes']['problematic_files']:
            report += f"- {file_path}\n"
        
        report += f"""
## 修复建议

将所有文件中的以下模式：
```sql
appeal_status = NULL
```

替换为：
```sql
appeal_status = 'appeal_pending'
```

这样确保重置后的计划能被提审函数正确找到。
"""
        
        return report

def main():
    """主函数"""
    logger.info("🔧 千川自动化系统可靠性修复工具")
    logger.info("目的: 修复状态管理不一致，确保100%自主运行")
    logger.info("="*60)
    
    fixer = SystemReliabilityFixer()
    
    # 执行修复
    results = fixer.fix_all_status_inconsistencies()
    
    # 生成报告
    report = fixer.generate_fix_report(results)
    
    # 保存报告
    report_file = f"ai_reports/audit/ai_report_20250802_audit_system_fix_report_{datetime.now().strftime('%H%M%S')}.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.success(f"✅ 修复报告已保存: {report_file}")
    except Exception as e:
        logger.error(f"❌ 保存报告失败: {e}")
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("🎯 修复完成总结")
    logger.info("="*60)
    
    total_fixed = results['database_fixes'].get('total_fixed', 0)
    files_need_fix = len(results['code_fixes']['problematic_files'])
    
    if total_fixed > 0:
        logger.success(f"✅ 数据库修复: {total_fixed} 个计划")
    
    if files_need_fix > 0:
        logger.warning(f"⚠️ 需要手动修复: {files_need_fix} 个文件")
        logger.info("💡 请查看报告文件获取详细修复指导")
    else:
        logger.success("✅ 代码检查: 未发现问题")
    
    if results['monitoring_setup'].get('monitor_script_created'):
        logger.success("✅ 监控系统: 已设置")
        logger.info("💡 建议设置cron任务: */10 * * * * python ai_tools/maintenance/ai_tool_20250802_maintenance_health_monitor.py")
    
    logger.info(f"📋 详细报告: {report_file}")
    
    return total_fixed > 0 or files_need_fix == 0

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n✅ 系统可靠性修复完成")
        logger.info("💡 建议: 继续观察系统运行状态，确保问题不再发生")
    else:
        logger.error("\n❌ 修复过程中发现问题，请查看报告")
