#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具 - WebUI监控集成
生命周期：永久
用途：将监控功能集成到现有web_ui.py中

使用方法：
在web_ui.py中导入此模块，并在适当位置调用render_monitoring_page()
"""

import sys
import os
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


def create_monitoring_page_with_mcp(execute_sql_func):
    """
    创建监控页面（使用MCP execute_sql函数）
    
    Args:
        execute_sql_func: MCP的execute_sql函数
    """
    
    def get_material_state_distribution():
        """获取素材状态分布"""
        try:
            result = execute_sql_func("""
                SELECT status, COUNT(*) as count 
                FROM local_creatives 
                GROUP BY status
                ORDER BY status
            """)
            
            distribution = {}
            if result:
                lines = result.strip().split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            distribution[data['status']] = data['count']
                        except:
                            continue
            
            return distribution
        except Exception as e:
            logger.error(f"获取素材状态分布失败: {e}")
            return {}
    
    def get_harvest_metrics():
        """获取收割指标"""
        try:
            result = execute_sql_func("""
                SELECT 
                    COUNT(*) as total_approved,
                    COUNT(CASE WHEN harvest_status = MaterialStatus.HARVESTED.value THEN 1 END) as harvested_count,
                    COUNT(CASE WHEN harvest_status = 'not_harvested' AND status = MaterialStatus.APPROVED.value THEN 1 END) as pending_harvest
                FROM local_creatives 
                WHERE status = MaterialStatus.APPROVED.value
            """)
            
            if result and result.strip():
                data = json.loads(result.strip())
                return {
                    'total_approved': data.get('total_approved', 0),
                    'harvested_count': data.get('harvested_count', 0),
                    'pending_harvest': data.get('pending_harvest', 0),
                    'harvest_success_rate': data.get('harvested_count', 0) / max(data.get('total_approved', 1), 1)
                }
            return {}
        except Exception as e:
            logger.error(f"获取收割指标失败: {e}")
            return {}
    
    def get_stuck_materials():
        """获取卡住的素材"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=1)
            result = execute_sql_func(f"""
                SELECT 
                    status,
                    COUNT(*) as stuck_count
                FROM local_creatives 
                WHERE status IN (MaterialStatus.UPLOADING.value, 'plan_pending', 'creating_plan')
                  AND updated_at < '{cutoff_time.isoformat()}'
                GROUP BY status
                ORDER BY stuck_count DESC
            """)
            
            stuck_data = []
            if result:
                lines = result.strip().split('\n')
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            stuck_data.append({
                                'status': data['status'],
                                'count': data['stuck_count']
                            })
                        except:
                            continue
            
            return stuck_data
        except Exception as e:
            logger.error(f"获取卡住素材失败: {e}")
            return []
    
    # 渲染监控页面
    st.title("🎯 系统监控")
    st.markdown("---")
    
    # 刷新按钮
    col1, col2, col3 = st.columns([1, 1, 8])
    with col1:
        if st.button("🔄 刷新数据"):
            st.rerun()
    
    with col2:
        auto_refresh = st.checkbox("自动刷新", key="monitoring_auto_refresh")
    
    # 获取数据
    with st.spinner("正在获取监控数据..."):
        material_dist = get_material_state_distribution()
        harvest_metrics = get_harvest_metrics()
        stuck_materials = get_stuck_materials()
    
    # 关键指标概览
    st.subheader("📊 关键指标概览")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_materials = sum(material_dist.values()) if material_dist else 0
        st.metric("总素材数", f"{total_materials:,}")
    
    with col2:
        approved_count = material_dist.get(MaterialStatus.APPROVED.value, 0)
        st.metric("已审核通过", f"{approved_count:,}")
    
    with col3:
        harvested_count = harvest_metrics.get('harvested_count', 0)
        pending_harvest = harvest_metrics.get('pending_harvest', 0)
        st.metric("已收割/待收割", f"{harvested_count:,}/{pending_harvest:,}")
    
    with col4:
        harvest_rate = harvest_metrics.get('harvest_success_rate', 0)
        st.metric("收割成功率", f"{harvest_rate:.1%}")
    
    # 状态分布图表
    st.subheader("📈 素材状态分布")
    if material_dist:
        col1, col2 = st.columns(2)
        
        with col1:
            # 饼图
            df_pie = pd.DataFrame(list(material_dist.items()), columns=['状态', '数量'])
            fig_pie = px.pie(df_pie, values='数量', names='状态', title="状态分布（饼图）")
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # 柱状图
            fig_bar = px.bar(df_pie, x='状态', y='数量', title="状态分布（柱状图）")
            fig_bar.update_layout(xaxis_tickangle=45)
            st.plotly_chart(fig_bar, use_container_width=True)
        
        # 详细数据表
        st.markdown("**详细数据**")
        df_pie['占比'] = df_pie['数量'] / df_pie['数量'].sum() * 100
        df_pie['占比'] = df_pie['占比'].round(1).astype(str) + '%'
        st.dataframe(df_pie, use_container_width=True)
    else:
        st.info("暂无状态分布数据")
    
    # 卡住素材告警
    st.subheader("🚨 卡住素材告警")
    if stuck_materials:
        st.warning(f"发现 {len(stuck_materials)} 种状态的素材卡住")
        
        # 创建告警表格
        df_stuck = pd.DataFrame(stuck_materials)
        df_stuck.columns = ['状态', '卡住数量']
        
        # 添加严重程度
        df_stuck['严重程度'] = df_stuck['卡住数量'].apply(
            lambda x: '🔴 严重' if x > 20 else '🟡 警告' if x > 5 else '🟢 轻微'
        )
        
        st.dataframe(df_stuck, use_container_width=True)
        
        # 卡住素材图表
        fig_stuck = px.bar(df_stuck, x='状态', y='卡住数量', 
                          title="卡住素材分布", color='卡住数量',
                          color_continuous_scale='Reds')
        st.plotly_chart(fig_stuck, use_container_width=True)
    else:
        st.success("✅ 当前无卡住素材")
    
    # 收割效率分析
    st.subheader("⚡ 收割效率分析")
    if harvest_metrics:
        col1, col2 = st.columns(2)
        
        with col1:
            # 收割进度条
            total_approved = harvest_metrics.get('total_approved', 0)
            harvested_count = harvest_metrics.get('harvested_count', 0)
            
            if total_approved > 0:
                progress = harvested_count / total_approved
                st.progress(progress)
                st.write(f"收割进度: {harvested_count}/{total_approved} ({progress:.1%})")
            else:
                st.info("暂无收割数据")
        
        with col2:
            # 收割状态饼图
            harvest_data = {
                '已收割': harvested_count,
                '待收割': harvest_metrics.get('pending_harvest', 0)
            }
            
            if sum(harvest_data.values()) > 0:
                df_harvest = pd.DataFrame(list(harvest_data.items()), columns=['状态', '数量'])
                fig_harvest = px.pie(df_harvest, values='数量', names='状态', 
                                   title="收割状态分布")
                st.plotly_chart(fig_harvest, use_container_width=True)
    else:
        st.info("暂无收割效率数据")
    
    # 操作建议
    st.subheader("💡 操作建议")
    suggestions = []
    
    # 基于数据生成建议
    if stuck_materials:
        total_stuck = sum(item['count'] for item in stuck_materials)
        suggestions.append(f"🔧 发现 {total_stuck} 个卡住素材，建议检查相关工作流")
    
    if harvest_metrics.get('pending_harvest', 0) > 100:
        suggestions.append("📈 待收割素材较多，建议增加收割频率")
    
    if harvest_metrics.get('harvest_success_rate', 0) < 0.95:
        suggestions.append("⚠️ 收割成功率偏低，建议检查收割逻辑")
    
    if not suggestions:
        suggestions.append("✅ 系统运行正常，无需特殊操作")
    
    for suggestion in suggestions:
        st.info(suggestion)
    
    # 自动刷新逻辑
    if auto_refresh:
        import time
        time.sleep(30)  # 30秒自动刷新
        st.rerun()


def render_monitoring_page():
    """
    渲染监控页面（独立版本，用于测试）
    """
    # 模拟MCP execute_sql函数
    def mock_execute_sql(sql: str) -> str:
        if "GROUP BY status" in sql and "local_creatives" in sql:
            return '''{"status":MaterialStatus.APPROVED.value,"count":1481}
{"status":MaterialStatus.PENDING_UPLOAD.value,"count":25}
{"status":MaterialStatus.UPLOADING.value,"count":8}
{"status":"plan_pending","count":12}
{"status":"under_review","count":45}
{"status":MaterialStatus.REJECTED.value,"count":23}'''
        elif "total_approved" in sql:
            return '{"total_approved":1481,"harvested_count":281,"pending_harvest":1200}'
        elif "stuck_count" in sql:
            return '''{"status":MaterialStatus.UPLOADING.value,"stuck_count":8}
{"status":"plan_pending","stuck_count":12}'''
        return ""
    
    create_monitoring_page_with_mcp(mock_execute_sql)


if __name__ == "__main__":
    st.set_page_config(
        page_title="千川监控",
        page_icon="🎯",
        layout="wide"
    )
    render_monitoring_page()
