#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 诊断Celery任务调度问题
清理条件: 问题解决后删除

Celery任务调度诊断工具
====================
"""

import os
import sys
import subprocess
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


def check_celery_processes():
    """检查Celery进程"""
    logger.info("🔍 检查Celery进程状态...")
    
    try:
        # 检查Python进程
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'CSV'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            python_processes = []
            
            for line in lines[1:]:  # 跳过标题行
                if 'python.exe' in line:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        pid = parts[1].strip('"')
                        python_processes.append(pid)
            
            logger.info(f"📊 发现 {len(python_processes)} 个Python进程")
            
            # 检查每个进程的命令行
            for pid in python_processes:
                try:
                    cmd_result = subprocess.run(['wmic', 'process', 'where', f'ProcessId={pid}', 'get', 'CommandLine', '/format:csv'], 
                                              capture_output=True, text=True, timeout=5)
                    if 'celery' in cmd_result.stdout.lower():
                        logger.success(f"✅ 发现Celery进程 PID: {pid}")
                    elif 'beat' in cmd_result.stdout.lower():
                        logger.success(f"✅ 发现Beat进程 PID: {pid}")
                except:
                    pass
        
    except Exception as e:
        logger.error(f"❌ 检查进程失败: {e}")


def test_manual_task_execution():
    """测试手动执行任务"""
    logger.info("🧪 测试手动执行计划创建任务...")
    
    try:
        # 导入必要的模块
        from src.qianchuan_aw.workflows import scheduler
        from src.qianchuan_aw.database.connection import database_session
        from src.qianchuan_aw.utils.config import load_settings
        
        app_settings = load_settings()
        
        logger.info("📋 手动执行计划创建任务...")
        
        with database_session() as db:
            # 手动调用计划创建函数
            scheduler.handle_plan_creation(db, app_settings)
        
        logger.success("✅ 手动任务执行完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 手动任务执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def check_celery_configuration():
    """检查Celery配置"""
    logger.info("🔧 检查Celery配置...")
    
    try:
        from src.qianchuan_aw.celery_app import app
        
        # 检查Beat调度配置
        beat_schedule = app.conf.beat_schedule
        logger.info(f"📋 Beat调度配置: {len(beat_schedule)} 个任务")
        
        for task_name, config in beat_schedule.items():
            logger.info(f"   - {task_name}: {config.get('task')} (间隔: {config.get('schedule')})")
        
        # 检查任务注册
        registered_tasks = list(app.tasks.keys())
        logger.info(f"📋 已注册任务: {len(registered_tasks)} 个")
        
        critical_tasks = ['tasks.create_plans', 'tasks.appeal_plans']
        for task in critical_tasks:
            if task in registered_tasks:
                logger.success(f"✅ 关键任务已注册: {task}")
            else:
                logger.error(f"❌ 关键任务未注册: {task}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查Celery配置失败: {e}")
        return False


def provide_manual_fix_commands():
    """提供手动修复命令"""
    logger.info("🛠️ 手动修复建议:")
    
    commands = [
        "# 1. 确认Celery Beat正在运行",
        "python run_celery_beat.py",
        "",
        "# 2. 确认Celery Worker正在运行", 
        "python run_celery_worker.py",
        "",
        "# 3. 手动触发计划创建任务",
        "python -c \"from src.qianchuan_aw.workflows.tasks import task_create_plans; task_create_plans()\"",
        "",
        "# 4. 手动触发申诉任务",
        "python -c \"from src.qianchuan_aw.workflows.tasks import task_appeal_plans; task_appeal_plans()\"",
    ]
    
    for cmd in commands:
        logger.info(cmd)


def main():
    """主函数"""
    logger.info("🚨 Celery任务调度诊断开始...")
    
    # 1. 检查进程状态
    check_celery_processes()
    
    # 2. 检查配置
    config_ok = check_celery_configuration()
    
    # 3. 测试手动执行
    if config_ok:
        manual_ok = test_manual_task_execution()
        
        if manual_ok:
            logger.success("🎉 手动任务执行成功！问题可能在Celery调度")
        else:
            logger.error("❌ 手动任务执行失败！问题在任务代码")
    
    # 4. 提供修复建议
    provide_manual_fix_commands()
    
    logger.info("🎯 诊断完成")


if __name__ == '__main__':
    main()
