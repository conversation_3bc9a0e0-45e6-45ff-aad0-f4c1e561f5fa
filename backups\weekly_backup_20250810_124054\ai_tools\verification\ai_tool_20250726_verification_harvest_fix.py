#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 验证收割工具修复是否正确
清理条件: 验证完成后可删除

收割工具修复验证器
================

验证以下修复是否正确：
1. 收割工具使用move而不是copy
2. 添加了重复检查机制
3. Celery配置包含收割任务
4. 工作流整体流程正确
"""

import os
import sys
import re
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class HarvestFixVerifier:
    """收割修复验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.issues = []
        self.fixes_verified = []
        
    def verify_harvest_tool_logic(self):
        """验证收割工具逻辑修复"""
        logger.info("🔍 验证收割工具逻辑修复...")
        
        harvest_file = os.path.join(self.project_root, 'src', 'qianchuan_aw', 'workflows', 'independent_material_harvest.py')
        
        try:
            with open(harvest_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查1: 是否使用shutil.move而不是shutil.copy
            if 'shutil.move(' in content:
                self.fixes_verified.append("✅ 收割工具已修复为使用shutil.move()")
            else:
                self.issues.append("❌ 收割工具仍未使用shutil.move()")
            
            if 'shutil.copy(' in content:
                self.issues.append("❌ 收割工具仍在使用shutil.copy()")
            else:
                self.fixes_verified.append("✅ 收割工具已移除shutil.copy()")
            
            # 检查2: 是否添加了重复检查机制
            if '遵循收割铁律' in content:
                self.fixes_verified.append("✅ 收割工具已添加收割铁律注释")
            else:
                self.issues.append("❌ 收割工具缺少收割铁律注释")
            
            if 'for date_folder in os.listdir' in content:
                self.fixes_verified.append("✅ 收割工具已添加重复检查机制")
            else:
                self.issues.append("❌ 收割工具缺少重复检查机制")
            
            # 检查3: 是否更新文件路径
            if 'local_creative.file_path = target_path' in content:
                self.fixes_verified.append("✅ 收割工具已添加文件路径更新")
            else:
                self.issues.append("❌ 收割工具缺少文件路径更新")
                
        except Exception as e:
            self.issues.append(f"❌ 无法读取收割工具文件: {e}")
    
    def verify_celery_configuration(self):
        """验证Celery配置修复"""
        logger.info("🔍 验证Celery配置修复...")
        
        celery_file = os.path.join(self.project_root, 'src', 'qianchuan_aw', 'celery_app.py')
        
        try:
            with open(celery_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查1: 是否添加了independent_harvest_config
            if 'independent_harvest_config' in content:
                self.fixes_verified.append("✅ Celery配置已添加independent_harvest_config")
            else:
                self.issues.append("❌ Celery配置缺少independent_harvest_config")
            
            # 检查2: 是否添加了independent_harvest_interval
            if 'independent_harvest_interval' in content:
                self.fixes_verified.append("✅ Celery配置已添加independent_harvest_interval")
            else:
                self.issues.append("❌ Celery配置缺少independent_harvest_interval")
            
            # 检查3: 是否添加了收割任务调度
            if 'independent-harvest-configurable' in content:
                self.fixes_verified.append("✅ Celery配置已添加收割任务调度")
            else:
                self.issues.append("❌ Celery配置缺少收割任务调度")
            
            # 检查4: 是否正确引用了harvest_materials任务
            if 'tasks.harvest_materials' in content:
                self.fixes_verified.append("✅ Celery配置正确引用harvest_materials任务")
            else:
                self.issues.append("❌ Celery配置未正确引用harvest_materials任务")
                
        except Exception as e:
            self.issues.append(f"❌ 无法读取Celery配置文件: {e}")
    
    def verify_task_definition(self):
        """验证任务定义"""
        logger.info("🔍 验证任务定义...")
        
        tasks_file = os.path.join(self.project_root, 'src', 'qianchuan_aw', 'workflows', 'tasks.py')
        
        try:
            with open(tasks_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查harvest_materials任务是否存在
            if 'task_harvest_materials' in content:
                self.fixes_verified.append("✅ harvest_materials任务定义存在")
            else:
                self.issues.append("❌ harvest_materials任务定义不存在")
            
            # 检查任务是否调用正确的函数
            if 'handle_independent_material_harvest' in content:
                self.fixes_verified.append("✅ harvest_materials任务调用正确函数")
            else:
                self.issues.append("❌ harvest_materials任务未调用正确函数")
                
        except Exception as e:
            self.issues.append(f"❌ 无法读取任务定义文件: {e}")
    
    def verify_workflow_configuration(self):
        """验证工作流配置"""
        logger.info("🔍 验证工作流配置...")
        
        config_file = os.path.join(self.project_root, 'config', 'settings.yml')
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查independent_harvest配置
            if 'independent_harvest:' in content:
                self.fixes_verified.append("✅ 配置文件包含independent_harvest配置")
            else:
                self.issues.append("❌ 配置文件缺少independent_harvest配置")
            
            # 检查是否启用
            if re.search(r'independent_harvest:.*?enabled:\s*true', content, re.DOTALL):
                self.fixes_verified.append("✅ independent_harvest已启用")
            else:
                self.issues.append("❌ independent_harvest未启用")
                
        except Exception as e:
            self.issues.append(f"❌ 无法读取配置文件: {e}")
    
    def verify_proper_harvest_tool_exists(self):
        """验证正确的收割工具是否存在"""
        logger.info("🔍 验证正确的收割工具是否存在...")
        
        proper_tool_path = os.path.join(self.project_root, 'ai_tools', 'harvest', 'ai_tool_20250726_harvest_proper_harvest.py')
        
        if os.path.exists(proper_tool_path):
            self.fixes_verified.append("✅ 正确的收割工具已创建")
            
            try:
                with open(proper_tool_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if 'shutil.move(' in content:
                    self.fixes_verified.append("✅ 正确的收割工具使用shutil.move()")
                else:
                    self.issues.append("❌ 正确的收割工具未使用shutil.move()")
                    
                if '~LocalCreative.filename.in_(already_harvested)' in content:
                    self.fixes_verified.append("✅ 正确的收割工具包含重复检查")
                else:
                    self.issues.append("❌ 正确的收割工具缺少重复检查")
                    
            except Exception as e:
                self.issues.append(f"❌ 无法读取正确的收割工具: {e}")
        else:
            self.issues.append("❌ 正确的收割工具不存在")
    
    def generate_verification_report(self):
        """生成验证报告"""
        report = f"""
收割工具修复验证报告
==================

验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ 修复验证通过项目:
"""
        
        for fix in self.fixes_verified:
            report += f"{fix}\n"
        
        report += f"""
❌ 发现的问题:
"""
        
        for issue in self.issues:
            report += f"{issue}\n"
        
        report += f"""
📊 验证统计:
- 通过项目: {len(self.fixes_verified)}
- 问题项目: {len(self.issues)}
- 总体状态: {'✅ 修复成功' if len(self.issues) == 0 else '❌ 仍有问题需要解决'}

🔧 下一步操作建议:
"""
        
        if len(self.issues) == 0:
            report += """
1. ✅ 所有修复都已正确应用
2. ✅ 可以安全重启Celery服务
3. ✅ 收割工作流将遵循收割铁律
4. ✅ 不会再出现重复收割问题

重启命令:
1. 停止当前的Celery Beat和Worker进程
2. 运行: python run_celery_beat.py
3. 运行: python run_celery_worker.py
4. 观察日志确认收割任务正常调度
"""
        else:
            report += """
1. ❌ 需要解决上述问题后再重启服务
2. ❌ 当前状态下重启可能仍会出现重复收割
3. ❌ 建议先修复所有问题

修复建议:
- 检查文件编辑是否正确保存
- 验证代码语法是否正确
- 确认所有必要的导入和配置都已添加
"""
        
        return report
    
    def run_verification(self):
        """运行完整验证"""
        logger.info("🚀 开始收割工具修复验证...")
        
        try:
            # 执行所有验证
            self.verify_harvest_tool_logic()
            self.verify_celery_configuration()
            self.verify_task_definition()
            self.verify_workflow_configuration()
            self.verify_proper_harvest_tool_exists()
            
            # 生成报告
            report = self.generate_verification_report()
            
            # 保存报告
            report_file = os.path.join(self.project_root, 'ai_temp', f'harvest_fix_verification_{int(__import__("time").time())}.txt')
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"✅ 验证报告已保存: {report_file}")
            
            # 输出报告
            print(report)
            
            # 返回验证结果
            return len(self.issues) == 0
            
        except Exception as e:
            logger.error(f"❌ 验证过程中发生错误: {e}")
            return False


def main():
    """主函数"""
    logger.info("🚀 开始收割工具修复验证...")
    
    verifier = HarvestFixVerifier()
    
    try:
        success = verifier.run_verification()
        
        if success:
            logger.info("🎉 所有修复验证通过！可以安全重启服务")
            return 0
        else:
            logger.warning("⚠️ 发现问题，需要进一步修复")
            return 1
            
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
