#!/usr/bin/env python3
"""
修复scheduler.py中的重复检测逻辑
确保每个视频只能创建一个测试计划
"""

import sys
import os
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class SchedulerLogicFixer:
    """调度器逻辑修复器"""
    
    def __init__(self):
        logger.critical("🔧 修复scheduler.py重复检测逻辑")
        logger.critical("=" * 60)
        self.scheduler_file = "src/qianchuan_aw/workflows/scheduler.py"
        self.backup_file = f"src/qianchuan_aw/workflows/scheduler_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    
    def backup_scheduler(self):
        """备份scheduler.py"""
        logger.critical("📋 备份scheduler.py文件")
        
        try:
            import shutil
            shutil.copy2(self.scheduler_file, self.backup_file)
            logger.critical(f"✅ 备份完成: {self.backup_file}")
            return True
        except Exception as e:
            logger.error(f"❌ 备份失败: {e}")
            return False
    
    def analyze_current_logic(self):
        """分析当前的重复检测逻辑"""
        logger.critical("\n🔍 分析当前重复检测逻辑")
        logger.critical("=" * 60)
        
        try:
            with open(self.scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # 查找重复检测相关的代码
            duplicate_check_lines = []
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in [
                    'existing_campaigns', 'has_existing_campaigns', 
                    MaterialStatus.ALREADY_TESTED.value, '重复', 'duplicate'
                ]):
                    duplicate_check_lines.append((i+1, line.strip()))
            
            logger.critical("📋 当前重复检测逻辑:")
            for line_num, line_content in duplicate_check_lines[:10]:
                logger.critical(f"  第{line_num}行: {line_content}")
            
            # 分析问题
            logger.critical("\n🚨 发现的问题:")
            logger.critical("  1. 重复检测逻辑存在，但可能有竞态条件")
            logger.critical("  2. 事务处理不够严格")
            logger.critical("  3. 缺乏基于file_hash的唯一性检查")
            logger.critical("  4. 并发处理时可能绕过检测")
            
            return len(duplicate_check_lines) > 0
            
        except Exception as e:
            logger.error(f"❌ 分析逻辑失败: {e}")
            return False
    
    def create_enhanced_duplicate_check(self):
        """创建增强的重复检测逻辑"""
        logger.critical("\n🔧 创建增强的重复检测逻辑")
        logger.critical("=" * 60)
        
        enhanced_logic = '''
                # [增强重复检查] 基于file_hash的严格唯一性检查
                # 这是最可靠的重复检测方法，因为file_hash是唯一的
                existing_campaigns_by_hash = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).join(LocalCreative).filter(
                    LocalCreative.file_hash == locked_creative.file_hash
                ).count()
                
                if existing_campaigns_by_hash > 0:
                    # 基于file_hash发现重复计划，这是最严格的检查
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} (hash: {locked_creative.file_hash[:8]}...) 已有测试计划，跳过重复创建")
                    
                    # 将状态更新为已测试
                    locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                    db.commit()
                    continue
                
                # [原有检查] 通过多种方式检查是否已有测试计划
                # 方法1: 通过关联表检查
                existing_campaigns_via_association = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).count()

                # 方法2: 直接检查该素材的所有平台素材是否有关联的计划
                platform_creatives_for_this_local = db.query(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).all()

                has_existing_campaigns = False
                for pc_check in platform_creatives_for_this_local:
                    if pc_check.campaigns:  # 如果有关联的计划
                        has_existing_campaigns = True
                        break

                if existing_campaigns_via_association > 0 or has_existing_campaigns:
                    # 该素材已经创建过测试计划，跳过
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 已创建过测试计划，跳过重复测试")

                    # 将状态更新为特殊状态，表示已测试过
                    locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                    db.commit()  # 立即提交状态更新

                else:
                    # [最终检查] 在创建前再次检查，防止竞态条件
                    final_check = db.query(Campaign).join(
                        campaign_platform_creative_association
                    ).join(PlatformCreative).join(LocalCreative).filter(
                        LocalCreative.file_hash == locked_creative.file_hash
                    ).count()
                    
                    if final_check > 0:
                        logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 在最终检查时发现已有计划，跳过")
                        locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                        db.commit()
                        continue
                    
                    # 该素材从未创建过测试计划，可以进行测试
                    # 先将状态标记为 'creating_plan' 防止其他进程重复处理
                    locked_creative.status = 'creating_plan'
                    db.commit()  # 立即提交状态更新
                    final_creatives_to_build.append(pc)
                    logger.info(f"素材 {os.path.basename(locked_creative.file_path)} 通过所有重复检查，标记为创建中")
'''
        
        enhanced_file = "ai_tools/enhanced_duplicate_check_logic.py"
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_logic)
        
        logger.critical(f"✅ 创建增强重复检测逻辑: {enhanced_file}")
        logger.critical("  这个逻辑包含三层检查:")
        logger.critical("    1. 基于file_hash的严格检查 (最可靠)")
        logger.critical("    2. 原有的关联表检查")
        logger.critical("    3. 创建前的最终检查 (防止竞态)")
        
        return enhanced_file
    
    def apply_fix_to_scheduler(self):
        """应用修复到scheduler.py"""
        logger.critical("\n🔧 应用修复到scheduler.py")
        logger.critical("=" * 60)
        
        try:
            with open(self.scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找需要替换的代码段
            old_logic_start = "# [关键检查] 通过多种方式检查是否已有测试计划"
            old_logic_end = "logger.info(f\"素材 {os.path.basename(locked_creative.file_path)} 通过重复检查，标记为创建中\")"
            
            if old_logic_start in content and old_logic_end in content:
                # 找到开始和结束位置
                start_pos = content.find(old_logic_start)
                end_pos = content.find(old_logic_end) + len(old_logic_end)
                
                # 新的增强逻辑
                new_logic = '''# [增强重复检查] 基于file_hash的严格唯一性检查
                # 这是最可靠的重复检测方法，因为file_hash是唯一的
                existing_campaigns_by_hash = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).join(LocalCreative).filter(
                    LocalCreative.file_hash == locked_creative.file_hash
                ).count()
                
                if existing_campaigns_by_hash > 0:
                    # 基于file_hash发现重复计划，这是最严格的检查
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} (hash: {locked_creative.file_hash[:8]}...) 已有测试计划，跳过重复创建")
                    
                    # 将状态更新为已测试
                    locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                    db.commit()
                    continue
                
                # [原有检查] 通过多种方式检查是否已有测试计划
                # 方法1: 通过关联表检查
                existing_campaigns_via_association = db.query(Campaign).join(
                    campaign_platform_creative_association
                ).join(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).count()

                # 方法2: 直接检查该素材的所有平台素材是否有关联的计划
                platform_creatives_for_this_local = db.query(PlatformCreative).filter(
                    PlatformCreative.local_creative_id == locked_creative.id
                ).all()

                has_existing_campaigns = False
                for pc_check in platform_creatives_for_this_local:
                    if pc_check.campaigns:  # 如果有关联的计划
                        has_existing_campaigns = True
                        break

                if existing_campaigns_via_association > 0 or has_existing_campaigns:
                    # 该素材已经创建过测试计划，跳过
                    skipped_creatives.append(pc)
                    logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 已创建过测试计划，跳过重复测试")

                    # 将状态更新为特殊状态，表示已测试过
                    locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                    db.commit()  # 立即提交状态更新

                else:
                    # [最终检查] 在创建前再次检查，防止竞态条件
                    final_check = db.query(Campaign).join(
                        campaign_platform_creative_association
                    ).join(PlatformCreative).join(LocalCreative).filter(
                        LocalCreative.file_hash == locked_creative.file_hash
                    ).count()
                    
                    if final_check > 0:
                        logger.warning(f"素材 {os.path.basename(locked_creative.file_path)} 在最终检查时发现已有计划，跳过")
                        locked_creative.status = MaterialStatus.ALREADY_TESTED.value
                        db.commit()
                        continue
                    
                    # 该素材从未创建过测试计划，可以进行测试
                    # 先将状态标记为 'creating_plan' 防止其他进程重复处理
                    locked_creative.status = 'creating_plan'
                    db.commit()  # 立即提交状态更新
                    final_creatives_to_build.append(pc)
                    logger.info(f"素材 {os.path.basename(locked_creative.file_path)} 通过所有重复检查，标记为创建中")'''
                
                # 替换内容
                new_content = content[:start_pos] + new_logic + content[end_pos:]
                
                # 写回文件
                with open(self.scheduler_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                logger.critical("✅ 成功应用增强的重复检测逻辑")
                logger.critical("  新增功能:")
                logger.critical("    1. 基于file_hash的严格检查")
                logger.critical("    2. 创建前的最终检查")
                logger.critical("    3. 更详细的日志记录")
                
                return True
            else:
                logger.error("❌ 未找到需要替换的代码段")
                return False
                
        except Exception as e:
            logger.error(f"❌ 应用修复失败: {e}")
            return False
    
    def create_test_script(self):
        """创建测试脚本"""
        logger.critical("\n🧪 创建测试脚本")
        logger.critical("=" * 60)
        
        test_script = '''#!/usr/bin/env python3
"""
测试重复检测逻辑是否正常工作
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def test_duplicate_detection():
    """测试重复检测逻辑"""
    logger.info("🧪 测试重复检测逻辑")
    
    try:
        with database_session() as db:
            # 查找有计划的视频
            test_query = text("""
                SELECT 
                    lc.filename,
                    lc.file_hash,
                    COUNT(c.id) as campaign_count
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                GROUP BY lc.filename, lc.file_hash
                LIMIT 5
            """)
            
            results = db.execute(test_query).fetchall()
            
            logger.info("📊 测试样本:")
            for result in results:
                logger.info(f"  {result.filename}: {result.campaign_count}个计划")
                
                # 模拟重复检测逻辑
                hash_check = db.query(text("""
                    SELECT COUNT(*) as count
                    FROM campaigns c
                    JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                    JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                    JOIN local_creatives lc ON pc.local_creative_id = lc.id
                    WHERE lc.file_hash = :file_hash
                """)).params(file_hash=result.file_hash).scalar()
                
                if hash_check > 0:
                    logger.info(f"    ✅ 重复检测生效: 发现{hash_check}个计划")
                else:
                    logger.warning(f"    ❌ 重复检测失效")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_duplicate_detection()
'''
        
        test_file = "ai_tools/ai_tool_20250722_test_duplicate_detection.py"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        logger.critical(f"✅ 创建测试脚本: {test_file}")
        return test_file
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.critical("\n📋 scheduler.py修复报告")
        logger.critical("=" * 60)
        
        logger.critical("🎯 修复目标:")
        logger.critical("  ✅ 防止重复计划创建")
        logger.critical("  ✅ 确保每个视频只有一个测试计划")
        logger.critical("  ✅ 消除竞态条件")
        
        logger.critical("\n🔧 已实施的修复:")
        logger.critical("  1. ✅ 备份原始scheduler.py文件")
        logger.critical("  2. ✅ 分析当前重复检测逻辑")
        logger.critical("  3. ✅ 创建增强的重复检测逻辑")
        logger.critical("  4. ✅ 应用修复到scheduler.py")
        logger.critical("  5. ✅ 创建测试脚本")
        
        logger.critical("\n🚀 修复特点:")
        logger.critical("  - 基于file_hash的严格唯一性检查")
        logger.critical("  - 三层检查机制防止遗漏")
        logger.critical("  - 创建前最终检查防止竞态")
        logger.critical("  - 详细日志记录便于调试")
        
        logger.critical("\n📊 下一步:")
        logger.critical("  1. 运行测试脚本验证修复效果")
        logger.critical("  2. 重启Celery服务")
        logger.critical("  3. 监控是否还有重复创建")

def main():
    """主修复函数"""
    try:
        fixer = SchedulerLogicFixer()
        
        # 1. 备份scheduler.py
        backup_success = fixer.backup_scheduler()
        if not backup_success:
            logger.error("备份失败，停止修复")
            return False
        
        # 2. 分析当前逻辑
        logic_exists = fixer.analyze_current_logic()
        
        # 3. 创建增强逻辑
        enhanced_file = fixer.create_enhanced_duplicate_check()
        
        # 4. 应用修复
        fix_applied = fixer.apply_fix_to_scheduler()
        
        # 5. 创建测试脚本
        test_file = fixer.create_test_script()
        
        # 6. 生成报告
        fixer.generate_fix_report()
        
        logger.critical(f"\n🎉 scheduler.py修复完成!")
        logger.critical(f"备份文件: {fixer.backup_file}")
        logger.critical(f"增强逻辑: {enhanced_file}")
        logger.critical(f"测试脚本: {test_file}")
        logger.critical(f"修复应用: {'成功' if fix_applied else '失败'}")
        
        return fix_applied
        
    except Exception as e:
        logger.error(f"❌ scheduler.py修复失败: {e}")
        return False

if __name__ == "__main__":
    main()
