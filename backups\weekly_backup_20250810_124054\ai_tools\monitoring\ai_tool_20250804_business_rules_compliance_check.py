"""
AI生成文件信息
================
文件类型: 监控工具
生命周期: 长期工具
创建目的: 检查千川自动化项目是否严格遵守业务铁律
清理条件: 作为长期监控工具保留
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any, Tuple
from loguru import logger

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Principal, AdAccount, LocalCreative, Campaign, PlatformCreative
from sqlalchemy.orm import joinedload
from sqlalchemy import func, and_, or_

class BusinessRulesComplianceChecker:
    """业务铁律合规性检查器"""
    
    def __init__(self):
        self.check_date = datetime.now().strftime('%Y-%m-%d')
        self.violations = []
        self.compliance_report = {
            'check_date': self.check_date,
            'rules_checked': 0,
            'violations_found': 0,
            'compliance_rate': 0.0,
            'details': {}
        }
    
    def check_rule_1_api_failure_tolerance(self) -> Dict[str, Any]:
        """铁律1：API失败时工作流应继续运行"""
        logger.info("🔍 检查铁律1：API失败容错机制")
        
        rule_result = {
            'rule_name': 'API失败容错铁律',
            'status': 'UNKNOWN',
            'violations': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            # 检查今天的日志中是否有API失败但工作流继续的记录
            log_file = f"logs/app_{self.check_date}.log"
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 查找健康检查失败的记录
                health_check_failures = log_content.count("检查账户") and log_content.count("健康度时发生错误")
                workflow_continues = log_content.count("工作流1B-Dispatcher") > 0
                
                rule_result['details'] = {
                    'health_check_failures': health_check_failures,
                    'workflow_continues': workflow_continues,
                    'log_file_exists': True
                }
                
                if health_check_failures > 0 and workflow_continues:
                    rule_result['status'] = 'PASS'
                    logger.success("✅ 铁律1检查通过：API失败时工作流继续运行")
                elif health_check_failures > 0 and not workflow_continues:
                    rule_result['status'] = 'FAIL'
                    rule_result['violations'].append("API失败导致工作流停止")
                    logger.error("❌ 铁律1违反：API失败导致工作流停止")
                else:
                    rule_result['status'] = 'PASS'
                    logger.info("✅ 铁律1检查通过：今日无API失败记录")
            else:
                rule_result['details']['log_file_exists'] = False
                rule_result['status'] = 'UNKNOWN'
                logger.warning("⚠️ 无法检查铁律1：日志文件不存在")
                
        except Exception as e:
            logger.error(f"❌ 铁律1检查失败: {e}")
            rule_result['status'] = 'ERROR'
            
        return rule_result
    
    def check_rule_2_test_account_restriction(self) -> Dict[str, Any]:
        """铁律2：测试账户限制铁律"""
        logger.info("🔍 检查铁律2：测试账户限制")
        
        rule_result = {
            'rule_name': '测试账户限制铁律',
            'status': 'UNKNOWN',
            'violations': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            with database_session() as db:
                # 检查今天创建的计划是否都在测试账户上
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_campaigns = db.query(Campaign).options(
                    joinedload(Campaign.account)
                ).filter(
                    Campaign.created_at >= today_start
                ).all()
                
                test_account_campaigns = 0
                non_test_account_campaigns = 0
                
                for campaign in today_campaigns:
                    if campaign.account.account_type == 'TEST' or campaign.account.is_test_account:
                        test_account_campaigns += 1
                    else:
                        non_test_account_campaigns += 1
                        rule_result['violations'].append(f"计划 {campaign.campaign_id_qc} 在非测试账户 {campaign.account.name}")
                
                rule_result['details'] = {
                    'total_campaigns_today': len(today_campaigns),
                    'test_account_campaigns': test_account_campaigns,
                    'non_test_account_campaigns': non_test_account_campaigns
                }
                
                if non_test_account_campaigns == 0:
                    rule_result['status'] = 'PASS'
                    logger.success(f"✅ 铁律2检查通过：今日 {test_account_campaigns} 个计划都在测试账户")
                else:
                    rule_result['status'] = 'FAIL'
                    logger.error(f"❌ 铁律2违反：{non_test_account_campaigns} 个计划在非测试账户")
                    
        except Exception as e:
            logger.error(f"❌ 铁律2检查失败: {e}")
            rule_result['status'] = 'ERROR'
            
        return rule_result
    
    def check_rule_3_single_appeal_principle(self) -> Dict[str, Any]:
        """铁律3：申诉提审一次性原则"""
        logger.info("🔍 检查铁律3：申诉提审一次性原则")
        
        rule_result = {
            'rule_name': '申诉提审一次性原则',
            'status': 'UNKNOWN',
            'violations': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            with database_session() as db:
                # 检查是否有计划被多次申诉
                multiple_appeal_campaigns = db.query(Campaign).filter(
                    Campaign.appeal_attempt_count > 1
                ).all()
                
                # 检查今天是否有重复申诉的记录
                today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                today_appeals = db.query(Campaign).filter(
                    and_(
                        Campaign.last_appeal_at >= today_start,
                        Campaign.appeal_attempt_count > 0
                    )
                ).all()
                
                repeated_appeals_today = [c for c in today_appeals if c.appeal_attempt_count > 1]
                
                rule_result['details'] = {
                    'total_multiple_appeal_campaigns': len(multiple_appeal_campaigns),
                    'today_appeals': len(today_appeals),
                    'repeated_appeals_today': len(repeated_appeals_today)
                }
                
                if len(repeated_appeals_today) == 0:
                    rule_result['status'] = 'PASS'
                    logger.success("✅ 铁律3检查通过：今日无重复申诉")
                else:
                    rule_result['status'] = 'FAIL'
                    for campaign in repeated_appeals_today:
                        rule_result['violations'].append(f"计划 {campaign.campaign_id_qc} 重复申诉 {campaign.appeal_attempt_count} 次")
                    logger.error(f"❌ 铁律3违反：{len(repeated_appeals_today)} 个计划重复申诉")
                    
        except Exception as e:
            logger.error(f"❌ 铁律3检查失败: {e}")
            rule_result['status'] = 'ERROR'
            
        return rule_result
    
    def check_rule_4_material_uniqueness(self) -> Dict[str, Any]:
        """铁律4：素材唯一性测试铁律"""
        logger.info("🔍 检查铁律4：素材唯一性测试")
        
        rule_result = {
            'rule_name': '素材唯一性测试铁律',
            'status': 'UNKNOWN',
            'violations': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            with database_session() as db:
                # 检查是否有素材被用于多个计划
                # 通过platform_creatives表和campaign_platform_creative_association表关联查询
                duplicate_materials = db.query(
                    PlatformCreative.material_id_qc,
                    func.count(PlatformCreative.id).label('campaign_count')
                ).join(
                    # 这里需要根据实际的关联表结构调整
                    Campaign, PlatformCreative.local_creative_id == Campaign.id  # 简化查询，实际可能需要调整
                ).group_by(
                    PlatformCreative.material_id_qc
                ).having(
                    func.count(PlatformCreative.id) > 1
                ).all()
                
                rule_result['details'] = {
                    'duplicate_materials_count': len(duplicate_materials)
                }
                
                if len(duplicate_materials) == 0:
                    rule_result['status'] = 'PASS'
                    logger.success("✅ 铁律4检查通过：无重复素材计划")
                else:
                    rule_result['status'] = 'FAIL'
                    for material_id, count in duplicate_materials:
                        rule_result['violations'].append(f"素材 {material_id} 被用于 {count} 个计划")
                    logger.error(f"❌ 铁律4违反：{len(duplicate_materials)} 个素材被重复使用")
                    
        except Exception as e:
            logger.error(f"❌ 铁律4检查失败: {e}")
            rule_result['status'] = 'ERROR'
            
        return rule_result
    
    def check_rule_5_continuous_harvest(self) -> Dict[str, Any]:
        """铁律5：持续收割原则"""
        logger.info("🔍 检查铁律5：持续收割原则")
        
        rule_result = {
            'rule_name': '持续收割原则',
            'status': 'UNKNOWN',
            'violations': [],
            'details': {},
            'recommendations': []
        }
        
        try:
            # 检查今天的日志中收割相关的记录
            log_file = f"logs/app_{self.check_date}.log"
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 统计收割相关的日志
                harvest_attempts = log_content.count("执行收割检查")
                harvest_success = log_content.count("素材收割成功")
                harvest_skipped = log_content.count("跳过收割")
                
                rule_result['details'] = {
                    'harvest_attempts': harvest_attempts,
                    'harvest_success': harvest_success,
                    'harvest_skipped': harvest_skipped,
                    'harvest_rate': harvest_success / max(harvest_attempts, 1) * 100
                }
                
                if harvest_attempts > 0:
                    rule_result['status'] = 'PASS'
                    logger.success(f"✅ 铁律5检查通过：执行了 {harvest_attempts} 次收割检查")
                else:
                    rule_result['status'] = 'UNKNOWN'
                    logger.warning("⚠️ 铁律5：今日无收割活动记录")
            else:
                rule_result['status'] = 'UNKNOWN'
                logger.warning("⚠️ 无法检查铁律5：日志文件不存在")
                
        except Exception as e:
            logger.error(f"❌ 铁律5检查失败: {e}")
            rule_result['status'] = 'ERROR'
            
        return rule_result
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """生成完整的合规性报告"""
        logger.info("📊 生成业务铁律合规性报告")
        
        # 执行所有规则检查
        rules_results = [
            self.check_rule_1_api_failure_tolerance(),
            self.check_rule_2_test_account_restriction(),
            self.check_rule_3_single_appeal_principle(),
            self.check_rule_4_material_uniqueness(),
            self.check_rule_5_continuous_harvest()
        ]
        
        # 统计结果
        total_rules = len(rules_results)
        passed_rules = len([r for r in rules_results if r['status'] == 'PASS'])
        failed_rules = len([r for r in rules_results if r['status'] == 'FAIL'])
        error_rules = len([r for r in rules_results if r['status'] == 'ERROR'])
        
        compliance_rate = (passed_rules / total_rules) * 100 if total_rules > 0 else 0
        
        # 收集所有违规
        all_violations = []
        for result in rules_results:
            all_violations.extend(result['violations'])
        
        self.compliance_report.update({
            'rules_checked': total_rules,
            'rules_passed': passed_rules,
            'rules_failed': failed_rules,
            'rules_error': error_rules,
            'violations_found': len(all_violations),
            'compliance_rate': compliance_rate,
            'details': {rule['rule_name']: rule for rule in rules_results},
            'all_violations': all_violations
        })
        
        return self.compliance_report

def main():
    """主函数"""
    logger.info("🛡️ 开始千川自动化项目业务铁律合规性检查")
    logger.info("=" * 60)
    
    checker = BusinessRulesComplianceChecker()
    report = checker.generate_compliance_report()
    
    # 输出报告摘要
    logger.info(f"📊 合规性检查报告摘要 ({report['check_date']})")
    logger.info(f"   检查规则数: {report['rules_checked']}")
    logger.info(f"   通过规则数: {report['rules_passed']}")
    logger.info(f"   失败规则数: {report['rules_failed']}")
    logger.info(f"   错误规则数: {report['rules_error']}")
    logger.info(f"   违规总数: {report['violations_found']}")
    logger.info(f"   合规率: {report['compliance_rate']:.1f}%")
    
    # 输出违规详情
    if report['all_violations']:
        logger.warning("⚠️ 发现的违规行为:")
        for i, violation in enumerate(report['all_violations'], 1):
            logger.warning(f"   {i}. {violation}")
    
    # 判断整体合规性
    if report['compliance_rate'] >= 95:
        logger.success("🎉 业务铁律合规性检查通过！")
    elif report['compliance_rate'] >= 80:
        logger.warning("⚠️ 业务铁律合规性需要关注")
    else:
        logger.error("❌ 业务铁律合规性严重不足，需要立即修复")
    
    return report

if __name__ == "__main__":
    main()
