#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 定时健康检查任务调度器，建立持续监控机制
清理条件: 系统运行期间需要持续使用，建议永久保留
"""

import os
import sys
import json
import schedule
import time
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, Any, List
from dataclasses import dataclass, asdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


@dataclass
class HealthTrend:
    """健康趋势数据"""
    timestamp: datetime
    health_score: int
    critical_issues: int
    warning_issues: int
    status: str


class ScheduledHealthMonitor:
    """定时健康检查监控器"""
    
    def __init__(self):
        self.health_history = []
        self.alert_thresholds = {
            'critical_score': 50,  # 健康分数低于50分触发Critical告警
            'warning_score': 70,   # 健康分数低于70分触发Warning告警
            'critical_issues': 3,  # Critical问题超过3个触发告警
            'trend_decline': 10    # 健康分数下降超过10分触发趋势告警
        }
        
    def run_scheduled_health_check(self) -> Dict[str, Any]:
        """运行定时健康检查"""
        logger.info("🕐 执行定时健康检查...")
        
        try:
            # 运行综合健康检查
            import subprocess
            import json

            # 直接调用健康检查脚本
            result = subprocess.run([
                sys.executable,
                str(project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250810_comprehensive_health_monitor.py')
            ], capture_output=True, text=True, cwd=str(project_root), encoding='utf-8', errors='ignore')

            if result.returncode != 0:
                raise Exception(f"健康检查脚本执行失败: {result.stderr}")

            # 解析健康检查结果
            health_report = self._parse_health_output(result.stdout)
            
            # 记录健康趋势
            trend = HealthTrend(
                timestamp=datetime.now(timezone.utc),
                health_score=health_report.get('health_score', 0),
                critical_issues=health_report.get('critical_issues', 0),
                warning_issues=health_report.get('warning_issues', 0),
                status=health_report.get('status', 'UNKNOWN')
            )
            
            self.health_history.append(trend)
            
            # 保持最近30天的历史记录
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            self.health_history = [h for h in self.health_history if h.timestamp > cutoff_date]
            
            # 分析健康趋势和触发告警
            alerts = self._analyze_health_trends(trend)
            
            # 保存健康历史
            self._save_health_history()
            
            result = {
                'timestamp': trend.timestamp.isoformat(),
                'health_score': trend.health_score,
                'status': trend.status,
                'critical_issues': trend.critical_issues,
                'warning_issues': trend.warning_issues,
                'alerts_triggered': alerts,
                'trend_analysis': self._get_trend_analysis()
            }
            
            logger.info(f"✅ 定时健康检查完成: 分数={trend.health_score}, 状态={trend.status}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 定时健康检查失败: {e}")
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'error': str(e),
                'status': 'ERROR'
            }

    def _parse_health_output(self, output: str) -> Dict[str, Any]:
        """解析健康检查输出"""
        try:
            # 从输出中提取健康分数和状态
            if not output:
                output = ""
            lines = output.strip().split('\n')

            health_score = 0
            status = 'UNKNOWN'
            critical_issues = 0
            warning_issues = 0

            for line in lines:
                if '健康分数:' in line:
                    # 提取健康分数
                    import re
                    match = re.search(r'健康分数:\s*(\d+)/100', line)
                    if match:
                        health_score = int(match.group(1))

                elif '总体状态:' in line:
                    # 提取状态
                    if 'WARNING' in line:
                        status = 'WARNING'
                    elif 'CRITICAL' in line:
                        status = 'CRITICAL'
                    elif 'HEALTHY' in line:
                        status = 'HEALTHY'

                elif 'Critical问题' in line:
                    # 提取Critical问题数量
                    import re
                    match = re.search(r'Critical问题\s*\((\d+)个\)', line)
                    if match:
                        critical_issues = int(match.group(1))

            return {
                'health_score': health_score,
                'status': status,
                'critical_issues': critical_issues,
                'warning_issues': warning_issues
            }

        except Exception as e:
            logger.error(f"解析健康检查输出失败: {e}")
            return {
                'health_score': 0,
                'status': 'ERROR',
                'critical_issues': 0,
                'warning_issues': 0
            }
    
    def _analyze_health_trends(self, current_trend: HealthTrend) -> List[Dict[str, Any]]:
        """分析健康趋势并触发告警"""
        alerts = []
        
        # 检查当前健康分数
        if current_trend.health_score < self.alert_thresholds['critical_score']:
            alerts.append({
                'type': 'CRITICAL_HEALTH_SCORE',
                'severity': 'CRITICAL',
                'message': f'系统健康分数过低: {current_trend.health_score}/100',
                'threshold': self.alert_thresholds['critical_score']
            })
        elif current_trend.health_score < self.alert_thresholds['warning_score']:
            alerts.append({
                'type': 'WARNING_HEALTH_SCORE',
                'severity': 'WARNING',
                'message': f'系统健康分数偏低: {current_trend.health_score}/100',
                'threshold': self.alert_thresholds['warning_score']
            })
        
        # 检查Critical问题数量
        if current_trend.critical_issues >= self.alert_thresholds['critical_issues']:
            alerts.append({
                'type': 'CRITICAL_ISSUES_COUNT',
                'severity': 'CRITICAL',
                'message': f'Critical问题过多: {current_trend.critical_issues}个',
                'threshold': self.alert_thresholds['critical_issues']
            })
        
        # 检查健康分数下降趋势
        if len(self.health_history) >= 2:
            previous_trend = self.health_history[-2]
            score_decline = previous_trend.health_score - current_trend.health_score
            
            if score_decline >= self.alert_thresholds['trend_decline']:
                alerts.append({
                    'type': 'HEALTH_SCORE_DECLINE',
                    'severity': 'WARNING',
                    'message': f'健康分数下降: {score_decline}分 ({previous_trend.health_score}→{current_trend.health_score})',
                    'threshold': self.alert_thresholds['trend_decline']
                })
        
        return alerts
    
    def _get_trend_analysis(self) -> Dict[str, Any]:
        """获取趋势分析"""
        if len(self.health_history) < 2:
            return {'status': 'insufficient_data'}
        
        recent_scores = [h.health_score for h in self.health_history[-7:]]  # 最近7次检查
        
        analysis = {
            'recent_average': sum(recent_scores) / len(recent_scores),
            'min_score': min(recent_scores),
            'max_score': max(recent_scores),
            'trend_direction': 'stable'
        }
        
        if len(recent_scores) >= 3:
            # 简单的趋势分析
            first_half = recent_scores[:len(recent_scores)//2]
            second_half = recent_scores[len(recent_scores)//2:]
            
            first_avg = sum(first_half) / len(first_half)
            second_avg = sum(second_half) / len(second_half)
            
            if second_avg > first_avg + 5:
                analysis['trend_direction'] = 'improving'
            elif second_avg < first_avg - 5:
                analysis['trend_direction'] = 'declining'
        
        return analysis
    
    def _save_health_history(self):
        """保存健康历史记录"""
        try:
            history_file = project_root / 'ai_reports' / 'monitoring' / 'health_history.json'
            history_file.parent.mkdir(parents=True, exist_ok=True)
            
            history_data = [asdict(h) for h in self.health_history]
            
            # 转换datetime为字符串
            for item in history_data:
                if isinstance(item['timestamp'], datetime):
                    item['timestamp'] = item['timestamp'].isoformat()
            
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(history_data, f, indent=2, ensure_ascii=False, default=str)
                
        except Exception as e:
            logger.error(f"保存健康历史失败: {e}")
    
    def load_health_history(self):
        """加载健康历史记录"""
        try:
            history_file = project_root / 'ai_reports' / 'monitoring' / 'health_history.json'
            
            if history_file.exists():
                with open(history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)
                
                self.health_history = []
                for item in history_data:
                    trend = HealthTrend(
                        timestamp=datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00')),
                        health_score=item['health_score'],
                        critical_issues=item['critical_issues'],
                        warning_issues=item['warning_issues'],
                        status=item['status']
                    )
                    self.health_history.append(trend)
                    
                logger.info(f"加载了{len(self.health_history)}条健康历史记录")
                
        except Exception as e:
            logger.error(f"加载健康历史失败: {e}")
    
    def setup_scheduled_tasks(self):
        """设置定时任务"""
        
        # 每小时执行一次健康检查
        schedule.every().hour.do(self.run_scheduled_health_check)
        
        # 每天凌晨2点执行深度健康检查
        schedule.every().day.at("02:00").do(self._run_deep_health_check)
        
        # 每周一凌晨3点执行健康趋势报告
        schedule.every().monday.at("03:00").do(self._generate_weekly_health_report)
        
        logger.info("✅ 定时健康检查任务已设置")
        logger.info("  - 每小时: 基础健康检查")
        logger.info("  - 每天02:00: 深度健康检查")
        logger.info("  - 每周一03:00: 健康趋势报告")
    
    def _run_deep_health_check(self):
        """运行深度健康检查"""
        logger.info("🔍 执行深度健康检查...")
        
        # 运行基础健康检查
        result = self.run_scheduled_health_check()
        
        # 额外的深度检查项目
        try:
            # 检查工作流堆积
            from ai_tools.maintenance.ai_tool_20250810_workflow_backlog_processor import WorkflowBacklogProcessor
            processor = WorkflowBacklogProcessor()
            workflow_stats = processor.get_workflow_statistics()
            
            # 检查AI文件状态
            ai_file_stats = self._check_ai_file_status()
            
            # 生成深度检查报告
            deep_report = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'basic_health': result,
                'workflow_stats': workflow_stats,
                'ai_file_stats': ai_file_stats
            }
            
            # 保存深度检查报告
            report_file = project_root / 'ai_reports' / 'monitoring' / f'deep_health_check_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(deep_report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ 深度健康检查完成，报告保存到: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 深度健康检查失败: {e}")
    
    def _check_ai_file_status(self) -> Dict[str, Any]:
        """检查AI文件状态"""
        try:
            ai_dirs = ['ai_temp', 'ai_tools', 'ai_reports']
            stats = {}
            
            for dir_name in ai_dirs:
                dir_path = project_root / dir_name
                if dir_path.exists():
                    file_count = len(list(dir_path.rglob('*')))
                    total_size = sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file())
                    
                    stats[dir_name] = {
                        'file_count': file_count,
                        'total_size_mb': round(total_size / (1024*1024), 2)
                    }
                else:
                    stats[dir_name] = {'exists': False}
            
            return stats
            
        except Exception as e:
            logger.error(f"检查AI文件状态失败: {e}")
            return {'error': str(e)}
    
    def _generate_weekly_health_report(self):
        """生成周健康趋势报告"""
        logger.info("📊 生成周健康趋势报告...")
        
        try:
            if len(self.health_history) < 7:
                logger.warning("健康历史数据不足，无法生成周报告")
                return
            
            # 获取最近7天的数据
            week_data = self.health_history[-7*24:]  # 假设每小时检查一次
            
            report = {
                'report_period': f"{week_data[0].timestamp.date()} to {week_data[-1].timestamp.date()}",
                'average_health_score': sum(h.health_score for h in week_data) / len(week_data),
                'min_health_score': min(h.health_score for h in week_data),
                'max_health_score': max(h.health_score for h in week_data),
                'critical_incidents': len([h for h in week_data if h.critical_issues > 0]),
                'status_distribution': {}
            }
            
            # 状态分布统计
            for status in ['HEALTHY', 'WARNING', 'CRITICAL']:
                count = len([h for h in week_data if h.status == status])
                report['status_distribution'][status] = count
            
            # 保存周报告
            report_file = project_root / 'ai_reports' / 'monitoring' / f'weekly_health_report_{datetime.now().strftime("%Y%m%d")}.json'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"✅ 周健康趋势报告生成完成: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ 生成周健康趋势报告失败: {e}")
    
    def run_monitoring_daemon(self):
        """运行监控守护进程"""
        logger.info("🚀 启动健康监控守护进程...")
        
        # 加载历史数据
        self.load_health_history()
        
        # 设置定时任务
        self.setup_scheduled_tasks()
        
        # 立即执行一次健康检查
        self.run_scheduled_health_check()
        
        # 开始监控循环
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次是否有待执行的任务
                
        except KeyboardInterrupt:
            logger.info("🛑 健康监控守护进程已停止")
        except Exception as e:
            logger.error(f"❌ 监控守护进程异常: {e}")


def main():
    """主函数"""
    monitor = ScheduledHealthMonitor()
    
    import argparse
    parser = argparse.ArgumentParser(description='定时健康检查监控器')
    parser.add_argument('--daemon', action='store_true', help='运行守护进程模式')
    parser.add_argument('--once', action='store_true', help='执行一次健康检查')
    
    args = parser.parse_args()
    
    if args.daemon:
        monitor.run_monitoring_daemon()
    elif args.once:
        result = monitor.run_scheduled_health_check()
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    else:
        print("使用 --daemon 启动守护进程或 --once 执行一次检查")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
