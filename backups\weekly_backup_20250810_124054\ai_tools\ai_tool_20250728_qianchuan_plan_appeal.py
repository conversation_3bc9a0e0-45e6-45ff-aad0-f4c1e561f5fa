"""
千川广告计划自动提审系统
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 基于抓包接口实现千川广告计划的自动化提审功能
依赖关系: 需要有效的千川后台登录cookies
清理条件: 功能被官方API替代时可删除
"""

import requests
import json
import time
import random
from typing import List, Dict, Optional, Any
from loguru import logger
import uuid


class QianchuanPlanAppealAPI:
    """千川广告计划提审API类"""
    
    def __init__(self):
        self.base_url = "https://qianchuan.jinritemai.com/copilot/api/v1/agw/card/callback"
        self.app_code = "QC"
        self.callback_code = "continue_process"
        self.application_code = "QC"
        
    def _generate_timestamp_id(self) -> str:
        """生成13位时间戳ID"""
        return str(int(time.time() * 1000))
    
    def _generate_planning_id(self) -> str:
        """生成planning_id（基于时间戳的批次ID）"""
        # 基于观察到的规律，planning_id似乎是13位时间戳
        return self._generate_timestamp_id()
    
    def _create_plan_appeal_item(self, plan_id: str) -> Dict[str, Any]:
        """创建单个计划申诉项的JSON结构"""
        return {
            "Description": "",
            "QuestionCategory": {
                "Description": "计划审核不通过/结果申诉"
            },
            "ID": plan_id,
            "AppealIDType": 1,
            "ExtraField": {
                "SelectedItem": []
            }
        }
    
    def _build_call_value(self, plan_ids: List[str], planning_id: str) -> str:
        """构建callValue JSON字符串"""
        # 创建计划申诉项列表
        appeal_items = [self._create_plan_appeal_item(plan_id) for plan_id in plan_ids]
        
        # 构建customAttribute
        custom_attribute = {
            "code": "1",
            "nodeId": "224022",
            "nodeName": "审核离线工单",
            "nodeTaskId": self._generate_timestamp_id(),
            "planning_id": planning_id,
            "taskId": self._generate_timestamp_id(),
            "tool_type": "workflow"
        }
        
        # 构建paramMapping
        param_mapping = {
            "审核离线工单_离线工单详情": json.dumps(appeal_items, ensure_ascii=False, separators=(',', ':'))
        }
        
        # 构建完整的callValue
        call_value = {
            "customAttribute": json.dumps(custom_attribute, ensure_ascii=False, separators=(',', ':')),
            "userTriggerTimestamp": int(time.time() * 1000),
            "copilot:triggerType": "6",
            "paramMapping": json.dumps(param_mapping, ensure_ascii=False, separators=(',', ':'))
        }
        
        return json.dumps(call_value, ensure_ascii=False, separators=(',', ':'))

    def _build_call_value_fixed(self, plan_ids: List[str]) -> str:
        """构建固定格式的callValue（基于成功案例）"""
        # 创建计划申诉项列表
        appeal_items = [self._create_plan_appeal_item(plan_id) for plan_id in plan_ids]

        # 使用固定的customAttribute参数
        custom_attribute = {
            "code": "1",
            "nodeId": "224022",
            "nodeName": "审核离线工单",
            "nodeTaskId": "6145051904258",
            "planning_id": "13852345585154",
            "taskId": "5857567303170",
            "tool_type": "workflow"
        }

        # 构建paramMapping
        param_mapping = {
            "审核离线工单_离线工单详情": json.dumps(appeal_items, ensure_ascii=False, separators=(',', ':'))
        }

        # 构建完整的callValue
        call_value = {
            "customAttribute": json.dumps(custom_attribute, ensure_ascii=False, separators=(',', ':')),
            "userTriggerTimestamp": 1753719540362,  # 使用固定时间戳
            "copilot:triggerType": "6",
            "paramMapping": json.dumps(param_mapping, ensure_ascii=False, separators=(',', ':'))
        }

        return json.dumps(call_value, ensure_ascii=False, separators=(',', ':'))

    def _build_call_value_optimized(self, plan_ids: List[str]) -> str:
        """构建优化的callValue（基于测试结果，部分参数动态化）"""
        # 创建计划申诉项列表
        appeal_items = [self._create_plan_appeal_item(plan_id) for plan_id in plan_ids]

        # 使用固定的customAttribute参数（但动态生成部分ID）
        custom_attribute = {
            "code": "1",
            "nodeId": "224022",
            "nodeName": "审核离线工单",
            "nodeTaskId": self._generate_timestamp_id(),  # 动态生成
            "planning_id": "13852345585154",  # 保持固定
            "taskId": self._generate_timestamp_id(),  # 动态生成
            "tool_type": "workflow"
        }

        # 构建paramMapping
        param_mapping = {
            "审核离线工单_离线工单详情": json.dumps(appeal_items, ensure_ascii=False, separators=(',', ':'))
        }

        # 构建完整的callValue
        call_value = {
            "customAttribute": json.dumps(custom_attribute, ensure_ascii=False, separators=(',', ':')),
            "userTriggerTimestamp": int(time.time() * 1000),  # 动态时间戳
            "copilot:triggerType": "6",
            "paramMapping": json.dumps(param_mapping, ensure_ascii=False, separators=(',', ':'))
        }

        return json.dumps(call_value, ensure_ascii=False, separators=(',', ':'))

    def _get_default_headers(self) -> Dict[str, str]:
        """获取默认请求头"""
        return {
            "accept": "application/json",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/json",
            "origin": "https://qianchuan.jinritemai.com",
            "priority": "u=1, i",
            "referer": f"https://qianchuan.jinritemai.com/promotion-v2/standard",
            "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
        }
    
    def submit_plan_appeal(
        self,
        plan_ids: List[str],
        advertiser_id: str,
        cookies: Dict[str, str],
        headers: Optional[Dict[str, str]] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        mode: str = "fixed"
    ) -> Dict[str, Any]:
        """
        提交广告计划申诉

        Args:
            plan_ids: 计划ID列表，最多5个
            advertiser_id: 广告户ID
            cookies: 千川后台登录cookies
            headers: 自定义请求头，可选
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            mode: 参数模式 ("fixed"=完全固定[推荐], "optimized"=优化模式[实验], "dynamic"=完全动态[不推荐])

        Returns:
            Dict包含:
            - success: bool, 是否成功
            - data: 响应数据
            - error: 错误信息
            - plan_ids: 提审的计划ID列表
            - appeal_id: 申诉批次ID
        """
        # 参数验证
        if not plan_ids:
            return {
                "success": False,
                "error": "计划ID列表不能为空",
                "data": None,
                "plan_ids": [],
                "appeal_id": None
            }

        if len(plan_ids) > 5:
            return {
                "success": False,
                "error": "单次最多只能提审5个计划",
                "data": None,
                "plan_ids": plan_ids,
                "appeal_id": None
            }

        logger.info(f"开始提审计划，计划数量: {len(plan_ids)}")
        logger.info(f"计划ID列表: {plan_ids}")
        logger.info(f"参数模式: {mode}")

        # 构建请求参数
        params = {
            "appCode": self.app_code,
            "aavid": advertiser_id
        }

        if mode == "fixed":
            # 完全固定参数模式（最稳定）
            data = {
                "sessionId": "13854144030210",  # 固定
                "windowId": "57a034b46ca9311ed77615740b64a9b58d37abc394657918f5d136204cb857ca",  # 固定
                "messageId": "13853834819074",  # 固定
                "callBackCode": "continue_process",
                "callValue": self._build_call_value_fixed(plan_ids),
                "applicationCode": "QC"
            }
            planning_id = "13852345585154"  # 固定的planning_id

        elif mode == "optimized":
            # 优化模式（基于测试结果的最佳平衡）
            data = {
                "sessionId": "13854144030210",  # 必须固定（测试证明）
                "windowId": str(uuid.uuid4()).replace('-', '') + str(uuid.uuid4()).replace('-', ''),  # 可动态
                "messageId": self._generate_timestamp_id(),  # 可动态
                "callBackCode": "continue_process",
                "callValue": self._build_call_value_optimized(plan_ids),
                "applicationCode": "QC"
            }
            planning_id = "13852345585154"  # 保持固定

        else:  # mode == "dynamic"
            # 完全动态参数模式（实验性）
            planning_id = self._generate_planning_id()
            session_id = self._generate_timestamp_id()
            message_id = self._generate_timestamp_id()
            window_id = str(uuid.uuid4()).replace('-', '') + str(uuid.uuid4()).replace('-', '')

            data = {
                "sessionId": session_id,
                "windowId": window_id,
                "messageId": message_id,
                "callBackCode": self.callback_code,
                "callValue": self._build_call_value(plan_ids, planning_id),
                "applicationCode": self.application_code
            }
        
        # 使用默认或自定义请求头
        request_headers = headers or self._get_default_headers()
        
        # 执行请求（带重试机制）
        last_error = None
        for attempt in range(max_retries):
            try:
                logger.info(f"发送提审请求，尝试次数: {attempt + 1}/{max_retries}")
                
                response = requests.post(
                    self.base_url,
                    headers=request_headers,
                    cookies=cookies,
                    params=params,
                    data=json.dumps(data, separators=(',', ':')),
                    timeout=30
                )
                
                logger.info(f"响应状态码: {response.status_code}")
                logger.debug(f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    try:
                        response_data = response.json()
                        return {
                            "success": True,
                            "data": response_data,
                            "error": None,
                            "plan_ids": plan_ids,
                            "appeal_id": planning_id,
                            "status_code": response.status_code
                        }
                    except json.JSONDecodeError as e:
                        logger.warning(f"响应JSON解析失败: {e}")
                        return {
                            "success": True,  # HTTP 200认为成功
                            "data": {"raw_response": response.text},
                            "error": f"JSON解析失败: {e}",
                            "plan_ids": plan_ids,
                            "appeal_id": planning_id,
                            "status_code": response.status_code
                        }
                else:
                    last_error = f"HTTP {response.status_code}: {response.text}"
                    logger.warning(f"请求失败: {last_error}")
                    
            except requests.exceptions.RequestException as e:
                last_error = f"请求异常: {e}"
                logger.error(f"请求异常: {e}")
            
            # 重试延迟
            if attempt < max_retries - 1:
                delay = retry_delay * (2 ** attempt) + random.uniform(0, 1)
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                time.sleep(delay)
        
        # 所有重试都失败
        return {
            "success": False,
            "error": last_error,
            "data": None,
            "plan_ids": plan_ids,
            "appeal_id": planning_id
        }


def submit_plan_appeal(
    plan_ids: List[str],
    advertiser_id: str,
    cookies: Dict[str, str],
    mode: str = "fixed",
    **kwargs
) -> Dict[str, Any]:
    """
    便捷函数：提交广告计划申诉

    Args:
        plan_ids: 计划ID列表
        advertiser_id: 广告户ID
        cookies: 登录cookies
        mode: 参数模式 ("fixed"=完全固定[推荐], "optimized"=优化模式[实验], "dynamic"=完全动态[不推荐])
        **kwargs: 其他参数传递给API类

    Returns:
        提审结果字典
    """
    api = QianchuanPlanAppealAPI()
    return api.submit_plan_appeal(plan_ids, advertiser_id, cookies, mode=mode, **kwargs)


if __name__ == "__main__":
    # 使用示例
    example_cookies =  {
    "passport_csrf_token": "250ad4a9923ea8ddb2ffdb9d9ae453b2",
    "passport_csrf_token_default": "250ad4a9923ea8ddb2ffdb9d9ae453b2",
    "uid_tt": "c40eea79cf9cc94f479f2ea998811a16",
    "uid_tt_ss": "c40eea79cf9cc94f479f2ea998811a16",
    "sid_tt": "21c610802a1fed4033545bae0c183762",
    "sessionid": "21c610802a1fed4033545bae0c183762",
    "sessionid_ss": "21c610802a1fed4033545bae0c183762",
    "is_staff_user": "false",
    "qc_tt_tag": "0",
    "s_v_web_id": "verify_mbtbxanl_zjTjbZRK_eoDm_46Gn_BIQB_6IuhlfMDzJy1",
    "ttcid": "d890a5d26c4f4a5b8975ab9fbb374e4174",
    "session_tlb_tag": "sttt%7C15%7CIcYQgCof7UAzVFuuDBg3Yv________-3Vc8--iIxPx4Hch_w6LFhG84E5Y5TRk6iGIOQr-2F_ME%3D",
    "tt_scid": "FcyzCHSnVddmKdHAYuP.QEJS9NtzszL9vwekjj2cLr49gPNGeZ9r4U2pV6yYS5VZ438c",
    "_tea_utm_cache_2906": "undefined",
    "csrftoken": "hBMGNpMl-IOjH8e9zn3K4UagjPSpQ0urFJtY",
    "csrf_session_id": "0bfd60e5c00f02350f7ec73a8db32254",
    "gfkadpd": "4333,31764|4333,31769|4333,31784|4333,34747",
    "passport_auth_status": "5098e0ae5b49b5b235790d06fe94a732%2C6ccc60fefd2b72daa356afeb659b067c",
    "passport_auth_status_ss": "5098e0ae5b49b5b235790d06fe94a732%2C6ccc60fefd2b72daa356afeb659b067c",
    "business-account-center-csrf-secret": "21c610802a1fed4033545bae0c183762",
    "business-account-center-csrf-token": "pWPX8jjf-1mxzu9aaTrz5IJ3BR_urg7tD0B0",
    "sid_guard": "21c610802a1fed4033545bae0c183762%7C1753711398%7C5184000%7CFri%2C+26-Sep-2025+14%3A03%3A18+GMT",
    "sid_ucp_v1": "1.0.0-KDAyNWUwOWFjODk1ZmFkOGI4YWNhNjYzMWJmYjRkNTU4MDU3ZmQ1NzEKFwjQ3PCPtKz0AxCmhp7EBhimDDgCQPEHGgJscSIgMjFjNjEwODAyYTFmZWQ0MDMzNTQ1YmFlMGMxODM3NjI",
    "ssid_ucp_v1": "1.0.0-KDAyNWUwOWFjODk1ZmFkOGI4YWNhNjYzMWJmYjRkNTU4MDU3ZmQ1NzEKFwjQ3PCPtKz0AxCmhp7EBhimDDgCQPEHGgJscSIgMjFjNjEwODAyYTFmZWQ0MDMzNTQ1YmFlMGMxODM3NjI",
    "gd_random": "************************************************************.tuVPsqoKqxjpM25K9EEZAkP1sx0x9QDiFDkGdu3OGac="
}
    
    # 测试您提供的计划ID
    test_plan_ids = ["****************"]

    print("🧪 测试三种参数模式")
    print("=" * 50)

    # 测试固定模式
    print("\n1. 固定参数模式（最稳定）")
    result1 = submit_plan_appeal(
        plan_ids=test_plan_ids,
        advertiser_id="****************",
        cookies=example_cookies,
        mode="fixed"
    )
    print(f"固定模式结果: {result1['success']}")
    if result1['success']:
        response_data = result1['data']
        if isinstance(response_data, dict):
            status_code = response_data.get('status_code', 'unknown')
            print(f"状态码: {status_code}")

    # # 测试优化模式
    # print("\n2. 优化参数模式（推荐）")
    # result2 = submit_plan_appeal(
    #     plan_ids=test_plan_ids,
    #     advertiser_id="****************",
    #     cookies=example_cookies,
    #     mode="optimized"
    # )
    # print(f"优化模式结果: {result2['success']}")
    # if result2['success']:
    #     response_data = result2['data']
    #     if isinstance(response_data, dict):
    #         status_code = response_data.get('status_code', 'unknown')
    #         print(f"状态码: {status_code}")

    # # 测试动态模式
    # print("\n3. 动态参数模式（实验性）")
    # result3 = submit_plan_appeal(
    #     plan_ids=test_plan_ids,
    #     advertiser_id="****************",
    #     cookies=example_cookies,
    #     mode="dynamic"
    # )
    # print(f"动态模式结果: {result3['success']}")
    # if result3['success']:
    #     response_data = result3['data']
    #     if isinstance(response_data, dict):
    #         status_code = response_data.get('status_code', 'unknown')
    #         print(f"状态码: {status_code}")

    print("\n🎯 推荐使用 mode='optimized' 获得最佳性能和稳定性平衡")
