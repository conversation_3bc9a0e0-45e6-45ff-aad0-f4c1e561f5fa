# 千川自动化项目铁律汇总

**汇总时间**: 2025-08-09  
**汇总范围**: 项目所有业务规则、技术约束、操作铁律  
**数据来源**: 代码、配置文件、数据库约束、文档

---

## 🚨 **核心业务铁律 (绝对不可违反)**

### **铁律1：账户类型严格分离**
**定义**: TEST账户和DELIVERY账户严格分离使用
**实施位置**: 
- `src/qianchuan_aw/services/account_sync_service.py:94-107`
- `ai_tools/workflow/ai_tool_20250803_workflow_test_account_limiter.py`

**具体规则**:
- ✅ **TEST账户**：专用于测试，名称包含"测试"或"test"
- ✅ **DELIVERY账户**：专用于正式投放，名称包含"素材"或"投放"
- 🚫 **严禁混用**：TEST账户不能用于正式投放，DELIVERY账户不能用于测试

**违规后果**: 业务逻辑错误，可能导致测试数据污染正式投放

---

### **铁律2：素材唯一性测试约束**
**定义**: 同一素材（基于file_hash）在TEST账户中只能创建一个计划
**实施位置**: 
- `src/qianchuan_aw/workflows/scheduler.py:1215-1230`
- `ai_tools/maintenance/ai_tool_20250727_database_constraints.sql:21-42`

**具体规则**:
- 🔍 **唯一性检查**：基于file_hash跨账户检查
- 🚫 **禁止重复**：已有测试计划的素材不能再创建计划
- ⚠️ **检查失败阻止**：唯一性检查失败时必须跳过，不能继续创建
- 📊 **数据库约束**：触发器级别的强制保护

**违规后果**: 
```sql
RAISE EXCEPTION '🚨 素材唯一性测试铁律违规: 素材已在测试账户中存在计划，禁止重复创建！'
```

---

### **铁律3：账户状态操作权限**
**定义**: 不同账户状态有严格的操作权限限制
**实施位置**: 
- `ai_tools/enhancement/ai_tool_20250801_enhancement_workflow_integration.py:164-172`
- `src/qianchuan_aw/workflows/scheduler.py:1307-1320`

**具体规则**:
- 🚫 **deleted账户**：严禁任何操作（上传、创建、提审、收割）
- ⚠️ **temporarily_blocked账户**：
  - 🚫 **禁止新建操作**：上传、创建计划
  - ✅ **允许已有操作**：提审、收割已有计划
- ✅ **active账户**：允许所有操作

**违规后果**: API调用失败，业务流程中断

---

### **铁律4：防重复提审约束**
**定义**: 每个计划只能提审一次，严禁重复提审
**实施位置**: 
- `ai_tools/maintenance/ai_tool_20250802_fix_duplicate_appeal_prevention.py:180-197`
- `ai_tools/ai_tool_20250803_maintenance_fix_business_rules_violations.py:32-42`

**具体规则**:
- 🔍 **提审状态检查**：appeal_status == 'appeal_pending' 的计划跳过
- 🔍 **提审历史检查**：first_appeal_at 不为空的计划跳过
- 🔍 **尝试次数限制**：appeal_attempt_count >= 3 的计划跳过
- 🔍 **状态要求**：只有status == 'AUDITING' 的计划才能提审

**违规后果**: 重复提审可能导致账户风险，违反千川平台规则

---

## 🔧 **技术约束铁律 (系统稳定性)**

### **铁律5：API频率限制 (已禁用)**
**定义**: API频率限制已完全禁用，系统以最大性能运行
**实施位置**:
- `config/settings.yml:238-268` (enabled: false)
- `src/qianchuan_aw/utils/rate_limiter.py:176-177` (直接返回True)

**当前状态**:
- 🚫 **全局限制**：已禁用 (enabled: false)
- 🚫 **端点限制**：所有端点频率限制已禁用
- ✅ **性能优化**：API调用无延迟，系统响应更快
- 📊 **监控机制**：依靠千川平台自身的频率控制

**业务考量**: 千川平台自身有频率保护，项目级别的限制已移除以提升性能

---

### **铁律6：并发操作限制**
**定义**: 严格的并发操作限制，防止系统过载
**实施位置**: 
- `config/violation_detection.yml:8`
- `config/browser_management.yml:48`

**具体限制**:
- 🔄 **违规检测并发**：最大3个并发
- 🌐 **浏览器进程**：最大999个（实际限制50个）
- 📊 **提审并发**：最大3个并发
- ⏱️ **操作超时**：30秒超时限制

**违规后果**: 系统资源耗尽，服务不稳定

---

### **铁律7：文件操作安全约束**
**定义**: 关键文件的自动保护和备份机制
**实施位置**: 
- `.augment/rules/Qianchuan_Auto_Execution_Rules.md:36-42`

**保护文件列表**:
- `config/settings.yml`
- `src/qianchuan_aw/sdk_qc/client.py`
- `src/qianchuan_aw/database/models.py`
- `requirements.txt`
- `main.py`
- `web_ui.py`

**具体规则**:
- 🔒 **修改前备份**：自动创建备份
- 🚫 **禁止直接修改**：必须通过工具修改
- 📊 **变更追踪**：所有变更自动记录

**违规后果**: 系统配置损坏，服务无法启动

---

## ⏱️ **时间和频率约束**

### **铁律8：工作流执行频率限制**
**定义**: 各工作流任务的执行频率严格限制
**实施位置**: `config/settings.yml:347-380`

**具体限制**:
- 📊 **计划创建频率**：每小时最多5个计划 (`hourly_plan_creation_rate_limit: 5`)
- 🔄 **独立收割间隔**：180秒间隔 (`interval_seconds: 180`)
- 📋 **分组调度间隔**：45秒间隔 (`interval_seconds: 45`)
- 🚀 **快速监控间隔**：300秒间隔 (`fast_monitor_interval_seconds: 300`)

**违规后果**: 系统过载，影响稳定性

---

### **铁律9：数据保留和清理约束**
**定义**: 严格的数据生命周期管理
**实施位置**: `config/settings.yml:9-20`

**具体规则**:
- 🗑️ **AI临时文件**：7天自动清理
- 📊 **AI报告文件**：30天自动清理
- 💾 **备份保留**：14天保留期
- 🔄 **每日备份**：自动执行

**违规后果**: 存储空间耗尽，系统性能下降

---

## 🛡️ **安全和合规铁律**

### **铁律10：状态转换合法性约束**
**定义**: 工作流状态只能按预定义路径转换
**实施位置**: `src/qianchuan_aw/utils/workflow_status.py:59-80`

**合法转换路径**:
```
NEW → PENDING_GROUPING → PROCESSING → UPLOADED_PENDING_PLAN → TESTING_PENDING_REVIEW → APPROVED → HARVESTED
```

**特殊路径**:
- `PENDING_GROUPING → ALREADY_TESTED` (发现已测试)
- `PROCESSING → UPLOAD_FAILED` (上传失败)
- `UPLOADED_PENDING_PLAN → PLAN_CREATION_FAILED` (计划创建失败)

**违规后果**: 状态不一致，工作流混乱

---

### **铁律11：资源使用限制**
**定义**: 系统资源使用的硬性限制
**实施位置**: `config/browser_management.yml:30-40`

**具体限制**:
- 💻 **CPU使用率**：不超过85%
- 🧠 **内存使用率**：不超过90%
- 🔍 **监控间隔**：60秒检查一次
- ⚠️ **告警阈值**：错误数超过100才告警

**违规后果**: 系统崩溃，服务中断

---

## 📊 **业务逻辑约束**

### **铁律12：计划创建数量限制**
**定义**: 不同工作流的计划创建数量严格按配置执行
**实施位置**:
- `config/settings.yml:136-172` (plan_creation_defaults)
- `config/settings.yml:94-98` (flexible_grouping)
- `src/qianchuan_aw/workflows/scheduler.py:1338-1341`

**具体规则**:
- 📊 **TEST账户**：1个计划放9个视频 (`test_workflow.creative_count: 9`)
- 📊 **DELIVERY账户**：1个计划放3个视频 (`manual_workflow.creative_count: 3`)
- 🔄 **灵活分组**：最少2个，最多9个，优先使用9个
- 💰 **预算范围**：30,000-40,000
- 🎯 **CPA出价范围**：35.1-35.7

**代码实现**:
```python
required_creative_count = workflow_config.get('creative_count', 9 if account.account_type == 'TEST' else 3)
```

**违规后果**: 测试效率低下，资源配置不当

---

### **铁律13：API重试和熔断约束**
**定义**: API调用的重试机制和熔断保护
**实施位置**: 
- `src/qianchuan_aw/utils/constants.py:27-34`
- `config/violation_detection.yml:23-27`

**具体规则**:
- 🔄 **最大重试次数**：3次
- ⏱️ **重试间隔**：1秒起始，指数退避
- ⏰ **请求超时**：30秒
- 🛡️ **熔断保护**：连续失败时暂停调用

**违规后果**: API调用雪崩，系统不可用

---

## 🔒 **数据安全铁律**

### **铁律14：敏感数据保护**
**定义**: 敏感配置和数据的保护机制
**实施位置**: 
- `config/auth_tokens.json` (加密存储)
- `.gitignore` (排除敏感文件)

**具体规则**:
- 🔐 **Token加密存储**：不明文存储API密钥
- 🚫 **禁止提交敏感文件**：.gitignore保护
- 📊 **访问日志记录**：所有敏感操作记录
- 🔄 **定期轮换**：Token定期更新

**违规后果**: 数据泄露，安全风险

---

### **铁律15：事务完整性约束**
**定义**: 数据库操作的事务完整性保证
**实施位置**: 多个工作流函数

**具体规则**:
- 🔄 **原子操作**：相关操作必须在同一事务中
- 🔙 **异常回滚**：操作失败时自动回滚
- 🔒 **锁机制**：并发操作使用锁保护
- ✅ **状态一致性**：确保数据状态一致

**违规后果**: 数据不一致，业务逻辑错误

---

## 📋 **AI助手操作铁律**

### **铁律16：AI文件管理规范**
**定义**: AI生成文件的严格管理规范
**实施位置**: `.augment/rules/imported/ai_template_20250718_config_augment_rules.md`

**命名规范**:
```
ai_[类型]_[日期]_[用途]_[描述].[扩展名]
```

**类型前缀**:
- `ai_temp_`: 临时文件（7天清理）
- `ai_tool_`: 长期工具（永久保留）
- `ai_report_`: 分析报告（30天清理）
- `ai_template_`: 配置模板（永久保留）

**存放规则**:
- 🚫 **禁止根目录**：不得在项目根目录创建AI文件
- ✅ **指定目录**：必须放置在ai_tools/、ai_temp/、ai_reports/、ai_templates/
- 📝 **生命周期标注**：文件头部必须包含生命周期信息

**违规后果**: 项目结构污染，文件管理混乱

---

### **铁律17：自动化执行规范**
**定义**: AI助手的强制自动执行规则
**实施位置**: `.augment/rules/Qianchuan_Auto_Execution_Rules.md`

**对话开始时自动执行**:
1. 🔍 **项目健康检查**：`python ai_tools/maintenance/ai_tool_20250718_maintenance_project_protection.py check`
2. 📊 **Git状态分析**：`python ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py status`
3. 💾 **备份状态检查**：超过24小时自动备份

**文件操作时自动执行**:
4. 🔒 **关键文件保护**：修改前自动备份
5. 📝 **AI文件规范化**：命名和位置检查
6. 🔍 **敏感内容检测**：防止敏感信息泄露

**Git操作时自动执行**:
7. 🛡️ **智能自动提交**：满足条件时自动提交
8. 🔍 **提交前安全检查**：语法和格式验证

**违规后果**: 项目安全性下降，规范性缺失

---

## 📊 **性能和资源约束**

### **铁律18：系统性能阈值**
**定义**: 系统性能的硬性限制
**实施位置**: `src/qianchuan_aw/utils/constants.py:248-252`

**具体阈值**:
- 💻 **CPU使用率**：不超过85%
- 🧠 **内存使用率**：不超过90%
- 💾 **磁盘使用率**：监控阈值
- ⚠️ **成本增长**：50%阈值告警
- 📈 **转化增长**：100%阈值告警

**违规后果**: 系统性能下降，服务质量受影响

---

### **铁律19：文件格式和大小约束**
**定义**: 支持的文件格式和大小限制
**实施位置**: `src/qianchuan_aw/utils/constants.py:38-65`

**支持格式**:
- 📹 **视频格式**：.mp4, .avi, .mov, .wmv, .flv, .mkv
- 🖼️ **图片格式**：.jpg, .jpeg, .png, .gif, .bmp
- 📏 **文件大小**：最小1MB，最大500MB

**违规后果**: 文件上传失败，工作流中断

---

## 🔄 **工作流执行约束**

### **铁律20：状态转换时序约束**
**定义**: 工作流状态转换的时序要求
**实施位置**: `src/qianchuan_aw/utils/workflow_status.py:155-162`

**超时配置**:
- ⏱️ **上传超时**：30分钟
- ⏱️ **计划创建超时**：15分钟
- ⏱️ **审核超时**：24小时
- ⏱️ **收割超时**：60分钟

**重试配置**:
- 🔄 **上传重试**：最多3次
- 🔄 **计划创建重试**：最多2次
- 🔄 **API调用重试**：最多3次

**违规后果**: 任务超时，工作流阻塞

---

## 🎯 **铁律执行优先级**

### **P0 - 绝对不可违反**
1. **账户类型分离** (铁律1)
2. **素材唯一性约束** (铁律2)
3. **账户状态权限** (铁律3)
4. **防重复提审** (铁律4)

### **P1 - 系统稳定性关键**
5. **API频率限制** (铁律5)
6. **并发操作限制** (铁律6)
7. **事务完整性** (铁律15)

### **P2 - 运维和安全**
8. **文件操作安全** (铁律7)
9. **敏感数据保护** (铁律14)
10. **AI文件管理** (铁律16)

### **P3 - 性能和优化**
11. **性能阈值** (铁律18)
12. **文件格式约束** (铁律19)
13. **状态转换时序** (铁律20)

---

## 🚨 **违规检测和处理机制**

### **自动检测**
- 🔍 **实时监控**：数据库触发器级别检测
- 📊 **定期审计**：30分钟间隔违规检测
- 🚨 **异常告警**：违规时立即告警

### **处理机制**
- 🛑 **立即阻止**：违规操作立即中断
- 📝 **详细记录**：所有违规事件记录
- 🔧 **自动修复**：可修复的问题自动处理

### **升级机制**
- ⚠️ **警告级别**：轻微违规警告
- 🚨 **错误级别**：严重违规阻止操作
- 🚫 **致命级别**：系统级违规停止服务

---

**总计**: 20条核心铁律，涵盖业务逻辑、技术约束、安全规范、性能限制等各个方面，确保千川自动化项目的稳定、安全、高效运行。
