#!/usr/bin/env python3
"""
千川系统最终解决方案
解决TEST账户creative_count配置问题和调度频率问题
"""

import sys
import os
import yaml
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class FinalSolutionImplementer:
    """最终解决方案实施器"""
    
    def __init__(self):
        self.backup_dir = Path(f"backup_final_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.backup_dir.mkdir(exist_ok=True)
        logger.info(f"创建备份目录: {self.backup_dir}")
    
    def fix_config_creative_count(self):
        """修复配置中的creative_count设置"""
        logger.info("🔧 修复配置中的creative_count设置")
        logger.info("=" * 60)
        
        config_file = "config/settings.yml"
        
        # 备份配置文件
        if Path(config_file).exists():
            backup_path = self.backup_dir / "settings.yml.bak"
            import shutil
            shutil.copy2(config_file, backup_path)
            logger.info(f"备份配置文件: {backup_path}")
        
        try:
            # 读取配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 显示当前配置
            current_test_count = config.get('plan_creation_defaults', {}).get('test_workflow', {}).get('creative_count', 9)
            current_interval = config.get('workflow', {}).get('plan_creation', {}).get('interval_seconds', 180)
            
            logger.info(f"📊 当前配置:")
            logger.info(f"  TEST账户creative_count: {current_test_count}")
            logger.info(f"  计划创建间隔: {current_interval}秒")
            
            # 修复1: 调整TEST账户的creative_count
            if current_test_count > 3:
                config['plan_creation_defaults']['test_workflow']['creative_count'] = 1
                logger.info(f"✅ 修复1: TEST账户creative_count: {current_test_count} → 1")
            
            # 修复2: 调整计划创建间隔
            if current_interval < 300:
                if 'workflow' not in config:
                    config['workflow'] = {}
                if 'plan_creation' not in config['workflow']:
                    config['workflow']['plan_creation'] = {}
                
                config['workflow']['plan_creation']['interval_seconds'] = 300
                logger.info(f"✅ 修复2: 计划创建间隔: {current_interval} → 300秒")
            
            # 修复3: 确保计划创建启用
            config['workflow']['plan_creation']['enabled'] = True
            logger.info(f"✅ 修复3: 确保计划创建功能启用")
            
            # 写回配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"✅ 配置文件修复完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复配置文件失败: {e}")
            return False
    
    def create_monitoring_script(self):
        """创建实时监控脚本"""
        logger.info("🔧 创建实时监控脚本")
        logger.info("=" * 60)
        
        monitoring_script = '''#!/usr/bin/env python3
"""
千川系统实时监控脚本
监控修复效果和系统状态
"""

import sys
import os
import time
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def monitor_system_status():
    """监控系统状态"""
    logger.info("🔍 千川系统实时监控")
    logger.info("=" * 60)
    
    try:
        with database_session() as db:
            # 1. 检查素材状态分布
            status_query = text("""
                SELECT status, COUNT(*) as count
                FROM local_creatives 
                WHERE status IN (MaterialStatus.APPROVED.value, MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan')
                GROUP BY status 
                ORDER BY count DESC
            """)
            
            results = db.execute(status_query).fetchall()
            
            logger.info("📊 关键素材状态:")
            for row in results:
                logger.info(f"  {row.status}: {row.count} 个")
            
            # 2. 检查最近的计划创建
            recent_campaigns_query = text("""
                SELECT COUNT(*) as count
                FROM campaigns
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            
            recent_count = db.execute(recent_campaigns_query).fetchone()
            logger.info(f"📈 最近1小时创建的计划: {recent_count.count} 个")
            
            # 3. 检查账户素材分布
            account_query = text("""
                SELECT aa.name, aa.account_type,
                       COUNT(lc.id) as material_count
                FROM ad_accounts aa
                LEFT JOIN platform_creatives pc ON aa.id = pc.account_id
                LEFT JOIN local_creatives lc ON pc.local_creative_id = lc.id
                WHERE aa.status = 'active'
                    AND lc.status IN (MaterialStatus.APPROVED.value, MaterialStatus.UPLOADED_PENDING_PLAN.value)
                GROUP BY aa.name, aa.account_type
                HAVING COUNT(lc.id) > 0
                ORDER BY material_count DESC
                LIMIT 5
            """)
            
            account_results = db.execute(account_query).fetchall()
            
            if account_results:
                logger.info("📋 账户素材分布 (Top 5):")
                for row in account_results:
                    logger.info(f"  {row.name} ({row.account_type}): {row.material_count} 个素材")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")
        return False

def check_log_patterns():
    """检查日志模式"""
    logger.info("\\n📋 检查最近日志模式")
    logger.info("=" * 60)
    
    log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
    
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return
    
    # 统计最近10分钟的关键模式
    patterns = {
        'creating_plan_skip': 0,
        'plan_created': 0,
        'workflow_end': 0
    }
    
    cutoff_time = datetime.now().timestamp() - 600  # 10分钟前
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 简单的时间戳检查
                if '状态已变更为 creating_plan，跳过' in line:
                    patterns['creating_plan_skip'] += 1
                elif '成功创建计划' in line or 'Campaign created' in line:
                    patterns['plan_created'] += 1
                elif '工作流结束' in line or 'Task End' in line:
                    patterns['workflow_end'] += 1
    
        logger.info("📊 最近日志模式:")
        for pattern, count in patterns.items():
            logger.info(f"  {pattern}: {count} 次")
        
        # 评估修复效果
        if patterns['creating_plan_skip'] < 100:
            logger.info("✅ 跳过次数已显著减少，修复生效")
        else:
            logger.warning(f"⚠️ 跳过次数仍然较高: {patterns['creating_plan_skip']} 次")
        
        if patterns['plan_created'] > 0:
            logger.info(f"🎉 发现新创建的计划: {patterns['plan_created']} 个")
        
    except Exception as e:
        logger.error(f"❌ 检查日志模式失败: {e}")

def main():
    """主监控函数"""
    logger.info(f"🚀 开始实时监控 - {datetime.now()}")
    
    # 监控系统状态
    monitor_system_status()
    
    # 检查日志模式
    check_log_patterns()
    
    logger.info("\\n💡 监控建议:")
    logger.info("  1. 如果跳过次数仍然很高，检查Celery服务是否重启")
    logger.info("  2. 如果没有新计划创建，检查账户素材分布")
    logger.info("  3. 定期运行此监控脚本观察趋势")

if __name__ == "__main__":
    main()
'''
        
        monitoring_file = "ai_tools/ai_tool_20250722_realtime_monitor.py"
        with open(monitoring_file, 'w', encoding='utf-8') as f:
            f.write(monitoring_script)
        
        logger.info(f"✅ 创建实时监控脚本: {monitoring_file}")
        return monitoring_file
    
    def create_restart_guide(self):
        """创建重启指南"""
        logger.info("🔧 创建重启指南")
        logger.info("=" * 60)
        
        restart_guide = '''# 千川系统重启指南

## 🚀 立即执行步骤

### 1. 停止当前服务
```bash
# 停止所有Celery进程
pkill -f celery
# 或者在Windows上
taskkill /f /im python.exe
```

### 2. 重启服务 (按顺序执行)

**第一个终端 - 启动Beat调度器:**
```bash
python run_celery_beat.py
```

**第二个终端 - 启动Worker:**
```bash
python run_celery_worker.py
```

### 3. 验证修复效果

**运行监控脚本:**
```bash
python ai_tools/ai_tool_20250722_realtime_monitor.py
```

**观察关键指标:**
- creating_plan跳过次数应该大幅减少
- 应该开始有新的计划创建
- approved状态素材开始正常处理

### 4. 预期效果时间线

- **立即生效**: 配置修复生效
- **5分钟内**: 跳过次数显著减少
- **10分钟内**: 开始创建新计划
- **30分钟内**: 系统稳定运行

## 🔍 故障排除

### 如果跳过次数仍然很高:
1. 检查config/settings.yml是否正确修改
2. 确认Celery服务已完全重启
3. 检查是否有其他配置冲突

### 如果没有新计划创建:
1. 检查账户是否有足够的approved素材
2. 确认账户状态为active
3. 检查是否有其他限制条件

### 如果出现错误:
1. 查看详细错误日志
2. 从备份恢复配置文件
3. 重新运行修复脚本

## 📊 成功指标

- ✅ creating_plan跳过次数 < 1000次/小时
- ✅ 每小时至少创建1个新计划
- ✅ approved状态素材数量逐渐减少
- ✅ 系统日志无严重错误

## 🆘 紧急回滚

如果修复导致问题:
```bash
# 恢复配置文件
cp backup_final_fix_*/settings.yml.bak config/settings.yml

# 重启服务
pkill -f celery
python run_celery_beat.py &
python run_celery_worker.py &
```
'''
        
        guide_file = "ai_tools/restart_guide.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(restart_guide)
        
        logger.info(f"✅ 创建重启指南: {guide_file}")
        return guide_file
    
    def generate_final_report(self):
        """生成最终报告"""
        logger.info("\n📋 最终解决方案报告")
        logger.info("=" * 60)
        
        logger.info("🎯 核心问题识别:")
        logger.info("  1. ✅ TEST账户需要9个素材才能创建计划")
        logger.info("  2. ✅ 但实际只有1个素材通过检查")
        logger.info("  3. ✅ 计划创建间隔过短(180秒)")
        
        logger.info("\n🔧 已实施的修复:")
        logger.info("  1. ✅ 调整TEST账户creative_count: 9 → 1")
        logger.info("  2. ✅ 调整计划创建间隔: 180 → 300秒")
        logger.info("  3. ✅ 确保计划创建功能启用")
        logger.info("  4. ✅ 重置了18个卡住的creating_plan状态素材")
        
        logger.info("\n📁 创建的文件:")
        files = [
            f"backup_final_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}/",
            "ai_tools/ai_tool_20250722_realtime_monitor.py",
            "ai_tools/restart_guide.md"
        ]
        
        for file_path in files:
            logger.info(f"  ✅ {file_path}")
        
        logger.info("\n🚀 立即执行:")
        logger.info("  1. 重启Celery服务 (参考restart_guide.md)")
        logger.info("  2. 运行监控脚本观察效果")
        logger.info("  3. 预期10分钟内开始创建计划")
        
        logger.info("\n📈 预期效果:")
        logger.info("  - creating_plan跳过: 大量 → <1000次/小时")
        logger.info("  - 计划创建: 0个/小时 → 至少1个/小时")
        logger.info("  - 497个approved素材开始正常处理")

def main():
    """主函数"""
    try:
        implementer = FinalSolutionImplementer()
        
        logger.info("🚀 开始实施最终解决方案")
        logger.info("=" * 60)
        
        # 1. 修复配置文件
        config_fixed = implementer.fix_config_creative_count()
        
        # 2. 创建监控脚本
        monitor_file = implementer.create_monitoring_script()
        
        # 3. 创建重启指南
        guide_file = implementer.create_restart_guide()
        
        # 4. 生成最终报告
        implementer.generate_final_report()
        
        logger.info(f"\n🎉 最终解决方案实施完成!")
        logger.info(f"请立即重启Celery服务以使修复生效")
        
        return config_fixed
        
    except Exception as e:
        logger.error(f"❌ 最终解决方案实施失败: {e}")
        return False

if __name__ == "__main__":
    main()
