#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库状态分析工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 分析数据库中的业务数据状态，识别工作流问题和数据一致性问题
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
from collections import defaultdict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class DatabaseStatusAnalyzer:
    """数据库状态分析器"""
    
    def __init__(self):
        self.analysis_time = datetime.now()
        self.time_window = timedelta(hours=5)
        self.start_time = self.analysis_time - self.time_window
        
    def analyze_material_status_distribution(self) -> Dict:
        """分析素材状态分布"""
        print("📊 分析素材状态分布...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            from sqlalchemy import func
            
            with database_session() as db:
                # 总体状态分布
                status_distribution = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).all()
                
                # 近5小时的状态分布
                recent_distribution = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).filter(
                    LocalCreative.updated_at >= self.start_time
                ).group_by(LocalCreative.status).all()
                
                # 卡住的素材（状态长时间未变化）
                stuck_materials = db.query(LocalCreative).filter(
                    LocalCreative.updated_at < self.start_time,
                    LocalCreative.status.in_([
                        MaterialStatus.UPLOADING.value,
                        MaterialStatus.PROCESSING.value,
                        MaterialStatus.PENDING_UPLOAD.value
                    ])
                ).count()
                
                return {
                    'total_distribution': {row.status: row.count for row in status_distribution},
                    'recent_distribution': {row.status: row.count for row in recent_distribution},
                    'stuck_materials': stuck_materials,
                    'total_materials': db.query(LocalCreative).count()
                }
                
        except Exception as e:
            print(f"❌ 分析素材状态失败: {e}")
            return {}
    
    def analyze_campaign_status_distribution(self) -> Dict:
        """分析计划状态分布"""
        print("📊 分析计划状态分布...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            from sqlalchemy import func
            
            with database_session() as db:
                # 计划状态分布
                campaign_distribution = db.query(
                    Campaign.status,
                    func.count(Campaign.id).label('count')
                ).group_by(Campaign.status).all()
                
                # 近5小时创建的计划
                recent_campaigns = db.query(Campaign).filter(
                    Campaign.created_at >= self.start_time
                ).count()
                
                # 提审状态分布
                appeal_distribution = db.query(
                    Campaign.appeal_status,
                    func.count(Campaign.id).label('count')
                ).group_by(Campaign.appeal_status).all()
                
                # 可提审的计划
                appealable_campaigns = db.query(Campaign).filter(
                    Campaign.status.in_(['REJECT', 'AUDITING'])
                ).count()
                
                return {
                    'campaign_distribution': {row.status: row.count for row in campaign_distribution},
                    'recent_campaigns': recent_campaigns,
                    'appeal_distribution': {row.appeal_status: row.count for row in appeal_distribution if row.appeal_status},
                    'appealable_campaigns': appealable_campaigns,
                    'total_campaigns': db.query(Campaign).count()
                }
                
        except Exception as e:
            print(f"❌ 分析计划状态失败: {e}")
            return {}
    
    def analyze_account_performance(self) -> Dict:
        """分析账户性能"""
        print("📊 分析账户性能...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import AdAccount, LocalCreative, Campaign
            from sqlalchemy import func
            
            with database_session() as db:
                # 账户素材分布
                account_materials = db.query(
                    AdAccount.name,
                    func.count(LocalCreative.id).label('material_count')
                ).join(LocalCreative).group_by(AdAccount.name).all()
                
                # 账户计划分布
                account_campaigns = db.query(
                    AdAccount.name,
                    func.count(Campaign.id).label('campaign_count')
                ).join(Campaign).group_by(AdAccount.name).all()
                
                # 测试账户识别
                test_accounts = db.query(AdAccount).filter(
                    AdAccount.name.like('%测试%')
                ).count()
                
                return {
                    'account_materials': {row.name: row.material_count for row in account_materials},
                    'account_campaigns': {row.name: row.campaign_count for row in account_campaigns},
                    'test_accounts': test_accounts,
                    'total_accounts': db.query(AdAccount).count()
                }
                
        except Exception as e:
            print(f"❌ 分析账户性能失败: {e}")
            return {}
    
    def analyze_workflow_bottlenecks(self) -> Dict:
        """分析工作流瓶颈"""
        print("🔍 分析工作流瓶颈...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative, Campaign
            from qianchuan_aw.utils.unified_material_status import MaterialStatus
            from sqlalchemy import func
            
            with database_session() as db:
                # 上传瓶颈分析
                pending_upload = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
                ).count()
                
                upload_failed = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value
                ).count()
                
                # 计划创建瓶颈
                uploaded_pending_plan = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value
                ).count()
                
                # 提审瓶颈
                rejected_campaigns = db.query(Campaign).filter(
                    Campaign.status == 'REJECT'
                ).count()
                
                auditing_campaigns = db.query(Campaign).filter(
                    Campaign.status == 'AUDITING'
                ).count()
                
                # 文件不存在问题
                missing_files = 0
                try:
                    materials_to_check = db.query(LocalCreative).filter(
                        LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
                    ).limit(100).all()
                    
                    for material in materials_to_check:
                        if not Path(material.file_path).exists():
                            missing_files += 1
                except:
                    pass
                
                return {
                    'upload_bottlenecks': {
                        'pending_upload': pending_upload,
                        'upload_failed': upload_failed,
                        'missing_files': missing_files
                    },
                    'plan_creation_bottlenecks': {
                        'uploaded_pending_plan': uploaded_pending_plan
                    },
                    'appeal_bottlenecks': {
                        'rejected_campaigns': rejected_campaigns,
                        'auditing_campaigns': auditing_campaigns
                    }
                }
                
        except Exception as e:
            print(f"❌ 分析工作流瓶颈失败: {e}")
            return {}
    
    def analyze_data_consistency(self) -> Dict:
        """分析数据一致性"""
        print("🔍 分析数据一致性...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative, Campaign, PlatformCreative
            
            with database_session() as db:
                # 素材-计划一致性
                materials_with_campaigns = db.query(LocalCreative).join(Campaign).count()
                total_materials = db.query(LocalCreative).count()
                
                # 计划-平台素材一致性
                campaigns_with_platform = db.query(Campaign).join(PlatformCreative).count()
                total_campaigns = db.query(Campaign).count()
                
                # 孤立记录
                orphaned_campaigns = db.query(Campaign).filter(
                    ~Campaign.local_creative_id.in_(
                        db.query(LocalCreative.id)
                    )
                ).count()
                
                return {
                    'material_campaign_consistency': {
                        'materials_with_campaigns': materials_with_campaigns,
                        'total_materials': total_materials,
                        'consistency_rate': (materials_with_campaigns / total_materials * 100) if total_materials > 0 else 0
                    },
                    'campaign_platform_consistency': {
                        'campaigns_with_platform': campaigns_with_platform,
                        'total_campaigns': total_campaigns,
                        'consistency_rate': (campaigns_with_platform / total_campaigns * 100) if total_campaigns > 0 else 0
                    },
                    'orphaned_records': {
                        'orphaned_campaigns': orphaned_campaigns
                    }
                }
                
        except Exception as e:
            print(f"❌ 分析数据一致性失败: {e}")
            return {}
    
    def generate_recommendations(self, analysis_results: Dict) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于素材状态的建议
        if 'material_status' in analysis_results:
            material_status = analysis_results['material_status']
            
            if material_status.get('stuck_materials', 0) > 0:
                recommendations.append({
                    'priority': 'HIGH',
                    'category': '状态修复',
                    'issue': f"发现{material_status['stuck_materials']}个卡住的素材",
                    'solution': "运行 python tools/maintenance/error_analysis_and_fix.py 修复卡住的素材"
                })
            
            pending_upload = material_status.get('total_distribution', {}).get('pending_upload', 0)
            if pending_upload > 100:
                recommendations.append({
                    'priority': 'MEDIUM',
                    'category': '上传优化',
                    'issue': f"有{pending_upload}个素材待上传",
                    'solution': "检查Worker状态，确保上传任务正常执行"
                })
        
        # 基于工作流瓶颈的建议
        if 'workflow_bottlenecks' in analysis_results:
            bottlenecks = analysis_results['workflow_bottlenecks']
            
            missing_files = bottlenecks.get('upload_bottlenecks', {}).get('missing_files', 0)
            if missing_files > 0:
                recommendations.append({
                    'priority': 'HIGH',
                    'category': '文件修复',
                    'issue': f"发现{missing_files}个文件不存在",
                    'solution': "运行文件修复工具，将缺失文件的素材标记为upload_failed"
                })
        
        return recommendations

def main():
    """主函数"""
    print("🚀 数据库状态分析")
    print("=" * 60)
    
    analyzer = DatabaseStatusAnalyzer()
    
    try:
        # 运行各项分析
        analysis_results = {}
        
        analysis_results['material_status'] = analyzer.analyze_material_status_distribution()
        analysis_results['campaign_status'] = analyzer.analyze_campaign_status_distribution()
        analysis_results['account_performance'] = analyzer.analyze_account_performance()
        analysis_results['workflow_bottlenecks'] = analyzer.analyze_workflow_bottlenecks()
        analysis_results['data_consistency'] = analyzer.analyze_data_consistency()
        
        # 生成建议
        recommendations = analyzer.generate_recommendations(analysis_results)
        
        # 输出分析结果
        print(f"\n{'='*60}")
        print("📊 数据库状态分析结果")
        print(f"{'='*60}")
        
        # 素材状态分析
        if analysis_results['material_status']:
            material_status = analysis_results['material_status']
            print(f"\n📋 素材状态分布:")
            for status, count in material_status.get('total_distribution', {}).items():
                print(f"  - {status}: {count}")
            
            if material_status.get('stuck_materials', 0) > 0:
                print(f"⚠️ 卡住的素材: {material_status['stuck_materials']} 个")
        
        # 计划状态分析
        if analysis_results['campaign_status']:
            campaign_status = analysis_results['campaign_status']
            print(f"\n📋 计划状态分布:")
            for status, count in campaign_status.get('campaign_distribution', {}).items():
                print(f"  - {status}: {count}")
            
            print(f"📊 近5小时新建计划: {campaign_status.get('recent_campaigns', 0)}")
            print(f"📊 可提审计划: {campaign_status.get('appealable_campaigns', 0)}")
        
        # 工作流瓶颈分析
        if analysis_results['workflow_bottlenecks']:
            bottlenecks = analysis_results['workflow_bottlenecks']
            print(f"\n🔍 工作流瓶颈:")
            
            upload_bottlenecks = bottlenecks.get('upload_bottlenecks', {})
            print(f"  上传瓶颈:")
            print(f"    - 待上传: {upload_bottlenecks.get('pending_upload', 0)}")
            print(f"    - 上传失败: {upload_bottlenecks.get('upload_failed', 0)}")
            print(f"    - 文件缺失: {upload_bottlenecks.get('missing_files', 0)}")
            
            plan_bottlenecks = bottlenecks.get('plan_creation_bottlenecks', {})
            print(f"  计划创建瓶颈:")
            print(f"    - 待创建计划: {plan_bottlenecks.get('uploaded_pending_plan', 0)}")
            
            appeal_bottlenecks = bottlenecks.get('appeal_bottlenecks', {})
            print(f"  提审瓶颈:")
            print(f"    - 被拒绝: {appeal_bottlenecks.get('rejected_campaigns', 0)}")
            print(f"    - 审核中: {appeal_bottlenecks.get('auditing_campaigns', 0)}")
        
        # 修复建议
        if recommendations:
            print(f"\n💡 修复建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
                print(f"     解决方案: {rec['solution']}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 数据库状态分析失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
