#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 自动化批量修复所有素材重复使用违规问题
清理条件: 系统完全合规后可删除

千川自动化自动合规性修复工具
========================

自动修复所有素材重复使用违规：
- 自动识别所有违规素材
- 保留最早计划，删除重复计划
- 更新素材状态为已测试
- 确保系统完全合规
"""

import sys
import os
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative, Campaign
from sqlalchemy import text


class AutoComplianceFixer:
    """自动合规性修复器"""
    
    def __init__(self):
        self.fixed_materials = 0
        self.deleted_plans = 0
        self.errors = []
    
    def get_all_violations(self) -> List[Dict[str, Any]]:
        """获取所有违规案例"""
        with database_session() as db:
            query = text("""
                SELECT 
                    lc.id as material_id,
                    lc.filename,
                    COUNT(DISTINCT c.id) as plan_count,
                    MIN(c.id) as keep_plan_id,
                    ARRAY_AGG(DISTINCT c.id ORDER BY c.created_at ASC) as all_plan_ids
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                JOIN campaigns c ON cpca.campaign_id = c.id
                JOIN ad_accounts aa ON c.account_id = aa.id
                WHERE aa.account_type = 'TEST'
                GROUP BY lc.id, lc.filename
                HAVING COUNT(DISTINCT c.id) > 1
                ORDER BY plan_count DESC
            """)
            
            result = db.execute(query)
            violations = []
            
            for row in result:
                violations.append({
                    'material_id': row.material_id,
                    'filename': row.filename,
                    'plan_count': row.plan_count,
                    'keep_plan_id': row.keep_plan_id,
                    'delete_plan_ids': row.all_plan_ids[1:]  # 除第一个外的所有计划
                })
            
            return violations
    
    def fix_single_violation(self, violation: Dict[str, Any]) -> bool:
        """修复单个违规"""
        try:
            material_id = violation['material_id']
            filename = violation['filename']
            delete_plan_ids = violation['delete_plan_ids']
            
            logger.info(f"修复素材: {filename} (删除 {len(delete_plan_ids)} 个重复计划)")
            
            with database_session() as db:
                # 删除关联表记录
                if delete_plan_ids:
                    db.execute(text("""
                        DELETE FROM campaign_platform_creative_association 
                        WHERE campaign_id = ANY(:plan_ids)
                    """), {"plan_ids": delete_plan_ids})
                    
                    # 删除计划记录
                    db.execute(text("""
                        DELETE FROM campaigns 
                        WHERE id = ANY(:plan_ids)
                    """), {"plan_ids": delete_plan_ids})
                    
                    # 更新素材状态
                    db.execute(text("""
                        UPDATE local_creatives 
                        SET status = MaterialStatus.ALREADY_TESTED.value 
                        WHERE id = :material_id
                    """), {"material_id": material_id})
                    
                    db.commit()
                    
                    self.fixed_materials += 1
                    self.deleted_plans += len(delete_plan_ids)
                    
                    return True
            
        except Exception as e:
            error_msg = f"修复素材 {violation['filename']} 失败: {e}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return False
    
    def fix_all_violations(self) -> Dict[str, Any]:
        """修复所有违规"""
        logger.info("🚨 开始自动修复所有素材违规...")
        
        violations = self.get_all_violations()
        total_violations = len(violations)
        
        logger.info(f"发现 {total_violations} 个违规素材")
        
        if total_violations == 0:
            logger.success("✅ 系统已完全合规！")
            return {
                'status': 'compliant',
                'fixed_materials': 0,
                'deleted_plans': 0,
                'errors': []
            }
        
        # 批量修复
        success_count = 0
        for i, violation in enumerate(violations, 1):
            logger.info(f"处理进度: {i}/{total_violations}")
            
            if self.fix_single_violation(violation):
                success_count += 1
            
            # 每修复10个素材检查一次进度
            if i % 10 == 0:
                logger.info(f"已修复 {success_count}/{i} 个素材")
        
        # 验证修复结果
        remaining_violations = self.get_all_violations()
        
        result = {
            'status': 'completed' if len(remaining_violations) == 0 else 'partial',
            'original_violations': total_violations,
            'fixed_materials': self.fixed_materials,
            'deleted_plans': self.deleted_plans,
            'remaining_violations': len(remaining_violations),
            'errors': self.errors
        }
        
        if len(remaining_violations) == 0:
            logger.success("🎉 所有违规已修复！系统完全合规！")
        else:
            logger.warning(f"⚠️ 仍有 {len(remaining_violations)} 个违规未修复")
        
        return result
    
    def generate_report(self, result: Dict[str, Any]) -> str:
        """生成修复报告"""
        report_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        report = f"""
千川自动化系统合规性修复报告
==========================
修复时间: {report_time}
修复状态: {'完全合规' if result['status'] == 'completed' else '部分修复'}

📊 修复统计:
- 原始违规素材: {result['original_violations']} 个
- 成功修复素材: {result['fixed_materials']} 个
- 删除重复计划: {result['deleted_plans']} 个
- 剩余违规素材: {result['remaining_violations']} 个

{'✅ 系统现已完全合规！' if result['status'] == 'completed' else '⚠️ 仍需进一步修复'}

🛠️ 修复措施:
1. 自动识别所有违规素材
2. 保留每个素材最早创建的计划
3. 删除所有重复创建的计划
4. 更新素材状态为"已测试"

⚠️ 重要提醒:
- 素材唯一性测试铁律已{'恢复' if result['status'] == 'completed' else '部分恢复'}
- 建议加强计划创建前的重复检查
- 定期进行合规性审计
- 考虑添加数据库约束防止未来违规

{'🎉 系统合规性修复完成！' if result['status'] == 'completed' else '⚠️ 需要进一步检查和修复'}
"""
        
        if result['errors']:
            report += f"\n❌ 修复过程中的错误:\n"
            for error in result['errors']:
                report += f"- {error}\n"
        
        return report


def main():
    """主函数"""
    print("🚨 千川自动化自动合规性修复")
    print("=" * 50)
    
    fixer = AutoComplianceFixer()
    
    try:
        # 执行自动修复
        result = fixer.fix_all_violations()
        
        # 生成报告
        report = fixer.generate_report(result)
        
        # 保存报告
        os.makedirs('ai_reports', exist_ok=True)
        report_file = f"ai_reports/auto_compliance_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 修复报告已保存: {report_file}")
        print(f"📊 修复结果: {result['status']}")
        print(f"📊 修复素材: {result['fixed_materials']} 个")
        print(f"📊 删除计划: {result['deleted_plans']} 个")
        
        if result['status'] == 'completed':
            print("🎉 系统合规性修复完成！")
            return 0
        else:
            print("⚠️ 部分修复完成，需要进一步检查")
            return 1
            
    except Exception as e:
        print(f"❌ 自动修复失败: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
