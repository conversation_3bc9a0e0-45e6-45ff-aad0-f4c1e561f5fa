#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 深度诊断计划提审功能为什么没有生效
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
import redis
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_celery_beat_actual_schedule():
    """检查Celery Beat实际运行的调度"""
    logger.info("🔍 检查Celery Beat实际运行的调度...")
    
    try:
        # 检查调度文件是否存在
        schedule_file = project_root / 'logs' / 'celerybeat-schedule.db'
        
        if schedule_file.exists():
            stat = schedule_file.stat()
            modified_time = datetime.fromtimestamp(stat.st_mtime)
            logger.info(f"📅 调度文件存在: {modified_time}")
            
            # 检查文件是否在最近被更新
            age_minutes = (datetime.now() - modified_time).total_seconds() / 60
            if age_minutes > 30:
                logger.warning(f"⚠️ 调度文件较旧: {age_minutes:.1f} 分钟前")
                return False
            else:
                logger.success(f"✅ 调度文件较新: {age_minutes:.1f} 分钟前")
        else:
            logger.error("❌ 调度文件不存在")
            return False
        
        # 尝试连接到Celery应用检查实际调度
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        # 检查Beat调度配置
        beat_schedule = app.conf.beat_schedule
        
        logger.info("📋 当前Beat调度配置:")
        for task_name, config in beat_schedule.items():
            task_func = config.get('task', 'Unknown')
            schedule = config.get('schedule', 'Unknown')
            logger.info(f"   🔹 {task_name}: {task_func} (间隔: {schedule})")
        
        # 特别检查plan-submission-configurable
        if 'plan-submission-configurable' in beat_schedule:
            submission_config = beat_schedule['plan-submission-configurable']
            logger.success(f"✅ 发现计划提审调度: {submission_config}")
            return True
        else:
            logger.error("❌ 未发现计划提审调度配置")
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查Beat调度失败: {e}")
        return False

def check_task_import_and_registration():
    """检查任务导入和注册"""
    logger.info("🔍 检查任务导入和注册...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        
        # 1. 检查任务模块导入
        try:
            from qianchuan_aw.workflows import tasks
            logger.success("✅ tasks模块导入成功")
        except Exception as e:
            logger.error(f"❌ tasks模块导入失败: {e}")
            return False
        
        # 2. 检查具体任务函数
        if hasattr(tasks, 'task_submit_plans'):
            logger.success("✅ task_submit_plans函数存在")
        else:
            logger.error("❌ task_submit_plans函数不存在")
            return False
        
        # 3. 检查Celery应用中的任务注册
        from qianchuan_aw.celery_app import app
        registered_tasks = list(app.tasks.keys())
        
        if 'tasks.submit_plans' in registered_tasks:
            logger.success("✅ tasks.submit_plans已在Celery中注册")
        else:
            logger.error("❌ tasks.submit_plans未在Celery中注册")
            logger.info("📋 已注册的任务:")
            for task in sorted(registered_tasks):
                if task.startswith('tasks.'):
                    logger.info(f"   📌 {task}")
            return False
        
        # 4. 尝试手动调用任务函数
        try:
            # 直接调用函数（不通过Celery）
            tasks.task_submit_plans()
            logger.success("✅ task_submit_plans函数可以直接调用")
        except Exception as e:
            logger.error(f"❌ task_submit_plans函数调用失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查任务导入失败: {e}")
        return False

def check_scheduler_function():
    """检查scheduler中的处理函数"""
    logger.info("🔍 检查scheduler中的处理函数...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows import scheduler
        
        # 检查handle_plan_submission函数
        if hasattr(scheduler, 'handle_plan_submission'):
            logger.success("✅ handle_plan_submission函数存在")
            
            # 尝试获取函数签名
            import inspect
            sig = inspect.signature(scheduler.handle_plan_submission)
            logger.info(f"📋 函数签名: {sig}")
            
            return True
        else:
            logger.error("❌ handle_plan_submission函数不存在")
            
            # 列出scheduler中的所有函数
            logger.info("📋 scheduler中的函数:")
            for attr_name in dir(scheduler):
                if callable(getattr(scheduler, attr_name)) and not attr_name.startswith('_'):
                    logger.info(f"   📌 {attr_name}")
            
            return False
            
    except Exception as e:
        logger.error(f"❌ 检查scheduler函数失败: {e}")
        return False

def check_database_for_unsubmitted_plans():
    """检查数据库中是否有未提审的计划"""
    logger.info("🔍 检查数据库中的未提审计划...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 检查未提审计划
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_1h,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 minutes' THEN 1 END) as recent_30m
            FROM campaigns 
            WHERE status = 'AUDITING' 
            AND appeal_status IS NULL 
            AND first_appeal_at IS NULL
        """)
        
        total, recent_1h, recent_30m = cursor.fetchone()
        
        logger.info(f"📊 未提审计划统计:")
        logger.info(f"   📋 总计: {total}")
        logger.info(f"   🕐 最近1小时: {recent_1h}")
        logger.info(f"   🕐 最近30分钟: {recent_30m}")
        
        if total > 0:
            # 显示最近的几个未提审计划
            cursor.execute("""
                SELECT campaign_id_qc, created_at, 
                       EXTRACT(EPOCH FROM (NOW() - created_at))/60 as minutes_since_creation
                FROM campaigns 
                WHERE status = 'AUDITING' 
                AND appeal_status IS NULL 
                AND first_appeal_at IS NULL
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            logger.info("📋 最近未提审的计划:")
            for campaign_id, created_at, minutes_since in cursor.fetchall():
                logger.warning(f"   ⚠️ {campaign_id} - 创建于 {minutes_since:.1f}分钟前")
        
        cursor.close()
        conn.close()
        
        return total, recent_1h, recent_30m
        
    except Exception as e:
        logger.error(f"❌ 检查数据库失败: {e}")
        return -1, -1, -1

def check_redis_task_activity():
    """检查Redis中的任务活动"""
    logger.info("🔍 检查Redis中的任务活动...")
    
    try:
        config = load_config()
        redis_config = config.get('database', {}).get('redis', {})
        
        r = redis.Redis(
            host=redis_config.get('host', 'localhost'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0)
        )
        
        # 检查队列长度
        queue_length = r.llen('celery')
        logger.info(f"📊 Celery队列长度: {queue_length}")
        
        # 检查最近的任务
        recent_tasks = r.lrange('celery', -20, -1)
        
        submission_tasks = 0
        other_tasks = 0
        
        logger.info("📋 最近20个任务:")
        for i, task_data in enumerate(recent_tasks):
            try:
                task_str = task_data.decode('utf-8')
                if 'submit_plans' in task_str:
                    submission_tasks += 1
                    logger.success(f"   ✅ 任务 {i+1}: 包含submit_plans")
                else:
                    other_tasks += 1
                    # 提取任务名称
                    if '"task"' in task_str:
                        import json
                        try:
                            task_obj = json.loads(task_str)
                            task_name = task_obj.get('task', 'unknown')
                            logger.info(f"   📋 任务 {i+1}: {task_name}")
                        except:
                            logger.info(f"   📋 任务 {i+1}: 其他任务")
            except:
                logger.info(f"   📋 任务 {i+1}: 无法解析")
        
        logger.info(f"📊 任务统计: 提审任务 {submission_tasks}, 其他任务 {other_tasks}")
        
        return queue_length, submission_tasks, other_tasks
        
    except Exception as e:
        logger.error(f"❌ 检查Redis失败: {e}")
        return -1, -1, -1

def manual_trigger_and_monitor():
    """手动触发任务并监控执行"""
    logger.info("🎯 手动触发计划提审任务并监控...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        # 记录触发前的状态
        total_before, _, _ = check_database_for_unsubmitted_plans()
        
        logger.info(f"📊 触发前未提审计划: {total_before}")
        
        # 手动触发任务
        logger.info("🚀 手动触发任务...")
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 任务已派发，ID: {result.id}")
        
        # 监控任务执行
        logger.info("⏳ 监控任务执行...")
        
        for i in range(30):  # 监控30秒
            time.sleep(1)
            
            if result.ready():
                if result.successful():
                    logger.success("✅ 任务执行成功")
                    break
                else:
                    logger.error(f"❌ 任务执行失败: {result.result}")
                    break
            
            if i % 5 == 0:
                logger.info(f"⏳ 等待中... ({i}秒)")
        
        if not result.ready():
            logger.warning("⚠️ 任务仍在执行中或超时")
        
        # 等待一段时间后检查数据库变化
        time.sleep(5)
        total_after, _, _ = check_database_for_unsubmitted_plans()
        
        logger.info(f"📊 触发后未提审计划: {total_after}")
        
        if total_after < total_before:
            logger.success(f"✅ 成功处理了 {total_before - total_after} 个计划")
            return True
        else:
            logger.warning("⚠️ 未检测到计划状态变化")
            return False
            
    except Exception as e:
        logger.error(f"❌ 手动触发失败: {e}")
        return False

def check_celery_worker_logs():
    """检查Celery Worker日志"""
    logger.info("📋 检查Celery Worker日志...")
    
    try:
        # 查找日志文件
        log_dir = project_root / 'logs'
        
        if not log_dir.exists():
            logger.warning("⚠️ 日志目录不存在")
            return False
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob('*.log'))
        if not log_files:
            logger.warning("⚠️ 未找到日志文件")
            return False
        
        # 按修改时间排序，获取最新的日志文件
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        logger.info(f"📄 检查日志文件: {latest_log}")
        
        # 读取最近的日志内容
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近20分钟的相关日志
        recent_logs = []
        submission_logs = []
        
        for line in lines[-2000:]:  # 只检查最后2000行
            if any(keyword in line for keyword in ['submit_plans', 'Submit Plans', 'plan_submission', 'handle_plan_submission']):
                submission_logs.append(line.strip())
            elif any(keyword in line for keyword in ['Task Start', 'Task End', 'tasks.']):
                recent_logs.append(line.strip())
        
        logger.info(f"📊 最近任务相关日志: {len(recent_logs)} 条")
        logger.info(f"📊 提审相关日志: {len(submission_logs)} 条")
        
        if submission_logs:
            logger.info("📋 提审相关日志:")
            for log_line in submission_logs[-10:]:
                logger.info(f"   📌 {log_line}")
        else:
            logger.warning("⚠️ 未找到任何提审相关日志")
        
        if recent_logs:
            logger.info("📋 最近任务日志（最后10条）:")
            for log_line in recent_logs[-10:]:
                logger.info(f"   📌 {log_line}")
        
        return len(submission_logs) > 0
        
    except Exception as e:
        logger.error(f"❌ 检查日志失败: {e}")
        return False

def run_deep_diagnosis():
    """运行深度诊断"""
    logger.info("🚀 开始计划提审功能深度诊断...")
    logger.info("="*80)
    
    diagnosis_results = {
        'beat_schedule_ok': False,
        'task_registration_ok': False,
        'scheduler_function_ok': False,
        'unsubmitted_plans_exist': False,
        'redis_activity_ok': False,
        'manual_trigger_ok': False,
        'logs_show_activity': False
    }
    
    issues_found = []
    recommendations = []
    
    # 1. 检查Beat调度
    diagnosis_results['beat_schedule_ok'] = check_celery_beat_actual_schedule()
    if not diagnosis_results['beat_schedule_ok']:
        issues_found.append("Celery Beat调度配置问题")
        recommendations.append("检查celery_app.py中的beat_schedule配置")
    
    # 2. 检查任务注册
    diagnosis_results['task_registration_ok'] = check_task_import_and_registration()
    if not diagnosis_results['task_registration_ok']:
        issues_found.append("任务注册问题")
        recommendations.append("检查tasks.py中的任务定义和导入")
    
    # 3. 检查scheduler函数
    diagnosis_results['scheduler_function_ok'] = check_scheduler_function()
    if not diagnosis_results['scheduler_function_ok']:
        issues_found.append("scheduler处理函数问题")
        recommendations.append("检查scheduler.py中的handle_plan_submission函数")
    
    # 4. 检查数据库
    total, recent_1h, recent_30m = check_database_for_unsubmitted_plans()
    diagnosis_results['unsubmitted_plans_exist'] = total > 0
    if total == 0:
        issues_found.append("没有未提审的计划")
        recommendations.append("检查是否有新创建的计划需要提审")
    
    # 5. 检查Redis活动
    queue_length, submission_tasks, other_tasks = check_redis_task_activity()
    diagnosis_results['redis_activity_ok'] = submission_tasks > 0
    if submission_tasks == 0:
        issues_found.append("Redis中没有提审任务")
        recommendations.append("检查Beat是否正确派发提审任务")
    
    # 6. 手动触发测试
    diagnosis_results['manual_trigger_ok'] = manual_trigger_and_monitor()
    if not diagnosis_results['manual_trigger_ok']:
        issues_found.append("手动触发任务失败")
        recommendations.append("检查任务执行逻辑和错误处理")
    
    # 7. 检查日志
    diagnosis_results['logs_show_activity'] = check_celery_worker_logs()
    if not diagnosis_results['logs_show_activity']:
        issues_found.append("日志中没有提审活动")
        recommendations.append("检查Worker是否正确执行提审任务")
    
    # 生成诊断报告
    logger.info("\n" + "="*80)
    logger.info("🎯 深度诊断结果")
    logger.info("="*80)
    
    success_count = sum(diagnosis_results.values())
    total_count = len(diagnosis_results)
    
    for check_name, result in diagnosis_results.items():
        status = "✅" if result else "❌"
        check_display = check_name.replace('_', ' ').title()
        logger.info(f"{status} {check_display}")
    
    success_rate = (success_count / total_count) * 100
    logger.info(f"\n📈 诊断通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 输出问题和建议
    if issues_found:
        logger.error("\n🚨 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            logger.error(f"   {i}. {issue}")
    
    if recommendations:
        logger.info("\n💡 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
    
    # 最终判断
    if success_rate >= 80:
        logger.success("\n🎉 计划提审功能基本正常")
    elif success_rate >= 50:
        logger.warning("\n⚠️ 计划提审功能部分正常，需要进一步修复")
    else:
        logger.error("\n❌ 计划提审功能存在严重问题，需要全面修复")
    
    return diagnosis_results, issues_found, recommendations

def main():
    """主函数"""
    try:
        results, issues, recommendations = run_deep_diagnosis()
        
        # 保存诊断报告
        report_file = project_root / 'ai_temp' / f'plan_submission_deep_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'diagnosis_results': results,
            'issues_found': issues,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 深度诊断报告已保存: {report_file}")
        
        # 返回成功率
        success_rate = sum(results.values()) / len(results) * 100
        return success_rate >= 50
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
