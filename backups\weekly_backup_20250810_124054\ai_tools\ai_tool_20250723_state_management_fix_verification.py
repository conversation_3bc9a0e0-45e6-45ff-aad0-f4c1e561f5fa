#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 验证Streamlit状态管理修复效果
依赖关系: web_ui.py, 全局账户选择器
清理条件: 验证完成后可删除
"""

import sys
import os
import re
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_form_rerun_fixes():
    """检查表单提交后rerun的修复情况"""
    print("🔍 检查表单提交后rerun修复...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找表单提交后的rerun模式
        form_rerun_patterns = []
        
        for i, line in enumerate(lines, 1):
            if 'submitted' in line and 'if ' in line:
                # 检查后续几行是否有rerun
                for j in range(i, min(i+5, len(lines))):
                    if 'st.rerun()' in lines[j]:
                        # 检查是否有缓冲信息
                        has_buffer = False
                        for k in range(i, j):
                            if 'st.success(' in lines[k] or 'st.info(' in lines[k]:
                                has_buffer = True
                                break
                        
                        form_rerun_patterns.append({
                            'line': j+1,
                            'form_line': i,
                            'has_buffer': has_buffer,
                            'context': lines[i-1:j+2]
                        })
                        break
        
        print(f"📊 发现 {len(form_rerun_patterns)} 个表单提交后rerun模式:")
        
        fixed_count = 0
        for pattern in form_rerun_patterns:
            status = "✅ 已修复" if pattern['has_buffer'] else "❌ 未修复"
            if pattern['has_buffer']:
                fixed_count += 1
            print(f"  - 第{pattern['line']}行: {status}")
        
        print(f"📈 修复率: {fixed_count}/{len(form_rerun_patterns)} ({fixed_count/len(form_rerun_patterns)*100:.1f}%)")
        
        return fixed_count == len(form_rerun_patterns)
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_state_initialization():
    """检查状态初始化的改进情况"""
    print("\n🔧 检查状态初始化改进...")
    
    web_ui_path = project_root / 'web_ui.py'
    
    try:
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有防护机制函数
        has_init_functions = []
        
        if 'def init_batch_state():' in content:
            has_init_functions.append('批量复制状态初始化函数')
        
        if 'def init_single_state():' in content:
            has_init_functions.append('单账户复制状态初始化函数')
        
        # 检查状态一致性检查
        consistency_checks = []
        
        if 'batch_replication_step < 1 or' in content:
            consistency_checks.append('批量复制步骤一致性检查')
        
        if 'single_replication_step < 1 or' in content:
            consistency_checks.append('单账户复制步骤一致性检查')
        
        print(f"✅ 状态初始化函数: {len(has_init_functions)} 个")
        for func in has_init_functions:
            print(f"  - {func}")
        
        print(f"✅ 一致性检查: {len(consistency_checks)} 个")
        for check in consistency_checks:
            print(f"  - {check}")
        
        return len(has_init_functions) >= 2 and len(consistency_checks) >= 2
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_account_selector_improvements():
    """检查账户选择器的改进情况"""
    print("\n👥 检查账户选择器改进...")
    
    selector_path = project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_global_account_selector.py'
    
    try:
        with open(selector_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        improvements = []
        
        # 检查重复更新防护
        if 'current_account.account_id_qc == selected_account.account_id_qc' in content:
            improvements.append('重复更新防护机制')
        
        # 检查详细错误信息
        if 'debug_account_selection' in content and '详细错误' in content:
            improvements.append('详细错误调试信息')
        
        # 检查异常处理
        if 'except Exception as e:' in content:
            improvements.append('异常处理机制')
        
        print(f"✅ 账户选择器改进: {len(improvements)} 项")
        for improvement in improvements:
            print(f"  - {improvement}")
        
        return len(improvements) >= 2
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_syntax_correctness():
    """检查语法正确性"""
    print("\n🔧 检查语法正确性...")
    
    try:
        import py_compile
        
        files_to_check = [
            project_root / 'web_ui.py',
            project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_global_account_selector.py'
        ]
        
        syntax_ok = True
        for file_path in files_to_check:
            try:
                py_compile.compile(str(file_path), doraise=True)
                print(f"✅ {file_path.name} 语法正确")
            except Exception as e:
                print(f"❌ {file_path.name} 语法错误: {e}")
                syntax_ok = False
        
        return syntax_ok
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def generate_test_recommendations():
    """生成测试建议"""
    print("\n🧪 生成测试建议...")
    
    recommendations = [
        "1. 测试侧边栏账户选择的稳定性",
        "   - 多次切换不同账户",
        "   - 检查账户状态是否正确更新",
        "   - 验证收藏账户的显示和筛选",
        "",
        "2. 测试批量账户复制的完整流程",
        "   - 选择多个源账户",
        "   - 设置筛选条件并点击查找",
        "   - 验证页面不会意外跳转",
        "   - 完成整个四步流程",
        "",
        "3. 测试单账户复制的完整流程",
        "   - 在左侧栏选择账户",
        "   - 设置筛选条件",
        "   - 选择计划并执行复制",
        "",
        "4. 测试任务控制功能",
        "   - 暂停/继续/结束按钮",
        "   - 进度显示的准确性",
        "   - 任务状态的实时更新",
        "",
        "5. 测试状态管理的稳定性",
        "   - 页面刷新后状态保持",
        "   - 多个功能间切换",
        "   - 异常情况下的状态恢复"
    ]
    
    for rec in recommendations:
        print(rec)

def main():
    """主验证函数"""
    print("🔧 Streamlit状态管理修复验证")
    print("=" * 60)
    
    checks = [
        ("表单提交rerun修复", check_form_rerun_fixes),
        ("状态初始化改进", check_state_initialization),
        ("账户选择器改进", check_account_selector_improvements),
        ("语法正确性", check_syntax_correctness)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        print(f"\n📋 {check_name}")
        print("-" * 40)
        
        try:
            if check_func():
                passed += 1
                print(f"✅ {check_name} 通过")
            else:
                print(f"❌ {check_name} 失败")
        except Exception as e:
            print(f"❌ {check_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 状态管理修复验证通过！")
        print("\n💡 主要改进:")
        print("  ✅ 表单提交后添加缓冲信息，避免页面跳转")
        print("  ✅ 统一状态初始化机制，防止状态不一致")
        print("  ✅ 账户选择器防重复更新，提高稳定性")
        print("  ✅ 添加状态一致性检查，增强容错性")
    else:
        print("⚠️ 部分验证失败，需要进一步修复")
    
    # 生成测试建议
    generate_test_recommendations()
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
