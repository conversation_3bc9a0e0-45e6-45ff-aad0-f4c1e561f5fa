accounts:
  ****************:
    access_token: 9b3f13643653d0620bd7c2d686c51ceedc8cd157
    account_name: 测试账户
    is_test_account: true
ai_file_management:
  auto_cleanup:
    daily_cleanup_time: 02:00
    enabled: true
    report_file_retention_days: 30
    temp_file_retention_days: 7
  auto_create_directories: true
  cleanup_notifications: true
  cleanup_rules:
  - exclude_patterns:
    - '*_important_*'
    - '*_keep_*'
    max_age_days: 7
    pattern: ai_temp/**/*
  - exclude_patterns:
    - '*_important_*'
    - '*_keep_*'
    - '*_final_*'
    max_age_days: 30
    pattern: ai_reports/**/*
  enabled: true
  lifecycle_management: true
  naming_validation: true
  notifications:
    cleanup_summary: true
    file_count_threshold: 100
    storage_threshold_mb: 500
    storage_warnings: true
api_credentials:
  app_id: ****************
  secret: ec45e041c8f682b2d68684dbfe8f92f1bd95fb47
appeal_scheduler:
  account_interval_seconds: 15
  audit_check_interval_minutes: 5
  batch_processing_enabled: true
  batch_size_per_account: 3
  early_appeal_minutes: 10
  enable_audit_check: true
  force_appeal_hours: 2
  max_retry_attempts: 1
  min_plan_age_minutes: 20
  smart_retry_enabled: false
appeal_strategy:
  appeal_for_prod_plans: false
async_playwright:
  enabled: true
  headless: true
  max_browser_sessions: 1
  session_timeout: 180
browser:
  bulletproof_popup_cleanup: true
  default_timeout: 20000
  headless: true
  max_copilot_attempts: 5
browser_management:
  sync_idle_timeout_seconds: 180
  worker_idle_timeout_seconds: 180
celery_lifecycle:
  check_interval: 600
  enabled: false
  heartbeat_timeout: 3600
copilot:
  poll_interval: 10
  total_wait_time: 45
custom_workflow_assets_dir: D:/workflow_assets
database:
  connection_pool:
    echo: false
    max_overflow: 35
    pool_pre_ping: true
    pool_recycle: 1800
    pool_size: 25
    pool_timeout: 20
  postgresql:
    dbname: qianchuan_analytics
    host: localhost
    password: zmx5062686
    port: 5432
    postgres_bin_path: ''
    user: lanfeng
  redis:
    db: 0
    host: localhost
    port: 6379
  sqlite:
    db_name: qianchuan_aw.db
  type: postgresql
flexible_grouping:
  enabled: true
  force_create_threshold: 2
  max_creative_count: 9
  min_creative_count: 2
  timeout_hours: 0.5
git_auto_commit:
  commit_scope:
    ai_reports_selective: true
    exclude_patterns:
    - ai_temp/**/*
    - logs/**/*
    - '**/__pycache__/**'
    - '*.pyc'
    - config/auth_tokens.json
    - config/browser_cookies.json
    include_patterns:
    - src/**/*.py
    - config/*.yml
    - tools/*.py
    - ai_tools/**/*
    - ai_templates/**/*
    - requirements.txt
    - main.py
    - web_ui.py
  enabled: true
  triggers:
    ai_tools_changes: true
    api_changes: true
git_auto_commit_triggers:
  config_changes: true
  critical_files_changed: true
  file_count_threshold: 5
  time_interval_hours: 24
lighthouse_plan:
  api_concurrency_limit: 5
  fast_monitor_enabled: false
  fast_monitor_interval_seconds: 300
  min_cost_for_rejection_alert: 100.0
  new_star_conversion_increase_threshold_percent: 100.0
  new_star_cost_increase_threshold_percent: 50.0
  new_star_detection_window_minutes: 60
  rejected_creative_detection_window_minutes: 120
plan_creation_defaults:
  # 测试工作流配置 - 恢复标准配置
  test_workflow:
    creative_count: 9  # 🔧 [配置修正] TEST账户标准配置：1个计划放9个视频
    default_budget_range:
    - 30000
    - 40000
    default_cpa_bid_range:
    - 35.1
    - 35.7
    default_roi_goal_range:
    - 1.95
    - 2.01
    smart_mix_ratios:
      custom_deal: 0.25
      custom_roi: 0.15
      managed_deal: 0.35
      managed_roi: 0.25
    strategies:
      custom_deal:
        bid: DEAL
        lab: false
        scene: DAILY_SALE
      custom_roi:
        bid: ROI
        lab: false
        scene: DAILY_SALE
      managed_deal:
        bid: DEAL
        lab: true
        scene: DAILY_SALE
      managed_roi:
        bid: ROI
        lab: true
        scene: DAILY_SALE
  manual_workflow:
    creative_count: 3
    default_budget_range:
    - 30000
    - 40000
    default_cpa_bid_range:
    - 35.1
    - 35.7
    default_roi_goal_range:
    - 1.95
    - 2.01
    smart_mix_ratios:
      custom_deal: 0.25
      custom_roi: 0.15
      managed_deal: 0.35
      managed_roi: 0.25
    strategies:
      custom_deal:
        bid: DEAL
        lab: false
        scene: DAILY_SALE
      custom_roi:
        bid: ROI
        lab: false
        scene: DAILY_SALE
      managed_deal:
        bid: DEAL
        lab: true
        scene: DAILY_SALE
      managed_roi:
        bid: ROI
        lab: true
        scene: DAILY_SALE
      new_customer_deal:
        bid: DEAL
        lab: true
        scene: NEW_CUSTOMER_TRANSFORMATION
      new_customer_roi:
        bid: ROI
        lab: true
        scene: NEW_CUSTOMER_TRANSFORMATION
  test_workflow:
    bid_type: DEAL
    budget: 300.0
    campaign_scene: DAILY_SALE
    cpa_bid: 30.0
    creative_count: 9
    is_lab_ad: false
project_protection:
  backup_strategy:
    daily_backup: true
    max_backups: 50
    retention_days: 30
  critical_files:
    config/settings.yml:
      backup_before_change: true
      max_versions: 10
      require_confirmation: true
    src/qianchuan_aw/sdk_qc/client.py:
      backup_before_change: true
      max_versions: 5
  database_backup:
    compress: true
    daily_backup: true
    enabled: true
    retention_days: 14
  enabled: true
rate_limiting:
  enabled: false  # 🔧 [API频率修正] 完全禁用API频率限制，提升系统性能
  endpoints:
    create_campaign:
      capacity: 18
      enabled: false  # 禁用计划创建频率限制
      rate: 3
    get_materials_in_ad:
      capacity: 12
      enabled: false  # 禁用素材查询频率限制
      rate: 2
    security_score_disposal_info:
      capacity: 3
      enabled: false  # 禁用违规检查频率限制
      rate: 1
    security_score_violation_event:
      capacity: 3
      enabled: false  # 禁用违规事件频率限制
      rate: 1
    upload_video:
      capacity: 30
      enabled: false  # 禁用视频上传频率限制
      rate: 6
  error_handling:
    backoff_factor: 3.0
    min_rate: 1
    recovery_time: 900
  global:
    capacity: 60
    enabled: false  # 禁用全局频率限制
    rate: 12
robustness:
  api_sync_delay: 5
  appeal_max_duration_hours: 10
  appeal_text_command_wait: 10
  initial_retry_delay: 10
  max_retries_for_cover: 5
  max_retries_for_upload: 3
  monitoring_interval: 600
  plan_creation_interval: 25
  plan_creation_reliability:
    alert_enabled: true
    base_retry_delay: 30
    enabled: true
    max_retries: 3
    max_retry_delay: 300
    smart_retry_enabled: true
    verification_enabled: true
  upload_reliability:
    alert_enabled: true
    enabled: true
    exponential_backoff: true
    max_retry_delay: 600
    max_verification_retries: 3
    smart_retry_enabled: true
    verification_delay_seconds: 5
    verification_enabled: true
    verification_retry_delay: 2
  upload_retry_delay: 30
  upload_timeout: 120
system_protection:
  enabled: true
  monitor_interval: 30
  thresholds:
    browser_critical: 8
    browser_warning: 5
    cpu_critical: 85
    cpu_warning: 70
    memory_critical: 90
    memory_warning: 75
upload_optimization:
  async_upload_enabled: true
  batch_db_operations: true
  batch_delay_seconds: 2
  batch_size: 10
  connection_pool_size: 20
  graceful_shutdown: true
  library_query_delay: 5
  library_query_retries: 3
  library_query_timeout: 30
  network_retry_count: 3
  performance_mode: balanced
  pre_compute_md5: true
  upload_timeout: 120
  validation_cache_enabled: true
video_similarity:
  enabled: true
  hamming_distance_threshold: 20
  phash_dct_crop_size: 8
  phash_size: 32
workflow:
  account_selection_strategy: random
  comment_and_violation_patrol_principals:
  - 缇萃百货
  comment_check_days: 30
  comment_management:
    check_interval_minutes: 5
    enabled: true
  file_ingestion:
    enabled: true
    interval_seconds: 150
    supported_formats:
    - .mp4
    - .mov
    - .avi
    - .mkv
  group_dispatch:
    enabled: true
    interval_seconds: 45
  hourly_plan_creation_rate_limit: 5
  independent_harvest:
    description: 独立素材收割机制，不依赖申诉结果定期扫描素材状态
    enabled: true
    interval_seconds: 180
    scope:
      account_type_filter:
      - TEST
      include_delivery_accounts: false
      test_accounts_only: true
  material_collection:
    archive_folder_name: 已处理
    dest_dir: D:/workflow_assets/01_materials_to_process/缇萃百货
    enabled: true
    end_hour: 20
    end_minute: 30
    interval_minutes: 30
    max_workers: 8
    source_dirs:
    - \\Os-20250508skou\王梦珂成品\8月\8.9白发
    - \\Os-20231126uosq\婷婷成品\杨婷婷-8.8
    - \\Os-20231107owpw\代朋飞成片\2025\8.9-代朋飞-
    - \\Os-20240103dcly\成片-张明鑫\8.9-张明鑫
    - \\Desktop-mr72sjo\蒿宏静-成片\蒿宏静成片8.9
    - \\Os-20231029jjmt\曹晓敏-成片\8.9
    - \\Os-20240103tfdd\郭世攀成片\白转黑\8.9-郭世攀
    - \\Os-20231014vkfp\谢莉\八月视频\8.9
    - \\Pc-************\付珂佳-成品\8.9付珂佳
    - \\Os-20250408iaqc\成品-张丽可\8.9
    start_hour: 8
    start_minute: 30
    video_extensions:
    - .mp4
    - .mkv
    - .avi
    - .mov
    - .wmv
    - .flv
  material_monitoring:
    enabled: true
    interval_seconds: 120
    monitor_duration_hours: 10
  max_upload_workers: 6
  plan_appeal:
    enabled: true
    interval_seconds: 900
  plan_creation:
    enabled: true
    interval_seconds: 60
  plan_submission:
    description: 提交新创建的计划进行审核
    enabled: true
    interval_seconds: 90
  titles_per_plan: 3
  video_validation:
    auto_quarantine: true
    enabled: true
    max_duration_seconds: 300
    max_file_size_mb: 500
    min_duration_seconds: 4
    quarantine_dir: quarantine/invalid_videos
  violation_detection:
    check_interval_minutes: 30
    enabled: true
  workflow_dirs:
    DIR_00_ARCHIVED: 00_uploaded_archive
    DIR_01_TO_PROCESS: 01_materials_to_process
    DIR_03_MATERIALS_APPROVED: 03_materials_approved
  zombie_cleanup:
    enabled: true
    patrol_interval_minutes: 10
    timeout_minutes: 10
