
# 千川自动化系统启动指南

## 前置条件检查
1. 确保PostgreSQL服务运行
2. 确保Redis服务运行  
3. 确保所有依赖包已安装

## 启动步骤

### 1. 启动Celery Worker (终端1)
```bash
cd /path/to/project
python run_celery_worker.py
```

### 2. 启动Celery Beat调度器 (终端2)  
```bash
cd /path/to/project
python run_celery_beat.py
```

### 3. 启动Web UI (终端3, 可选)
```bash
cd /path/to/project
streamlit run web_ui.py
```

## 监控和验证
- 检查Celery Worker日志确认任务执行
- 检查数据库中的任务记录
- 监控工作流目录中的文件处理

## 故障排除
- 如果遇到模块导入错误，检查sys.path设置
- 如果数据库连接失败，验证配置文件中的连接参数
- 如果Redis连接失败，确认Redis服务状态

## 配置调整
- 根据实际需求调整config/settings.yml中的间隔时间
- 根据系统性能调整Celery worker并发数
- 根据业务需求启用/禁用特定工作流
