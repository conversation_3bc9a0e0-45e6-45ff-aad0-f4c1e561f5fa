# 千川自动化 - 上传工作流全面诊断报告（初版）

时间: 2025-08-15
负责人: Augment Agent
范围: 文件扫描、上传工作流、提审模块、日志与调度、配置与数据库

---

## 一、结论摘要（TL;DR）
- 当前配置将入库目录指向 D:/workflow_assets/01_materials_to_process/缇萃百货，与您提供的路径一致。
- 代码中“文件入库”仅扫描扩展名 [.mp4, .mov, .avi, .mkv]，未包含 [.wmv, .flv]（配置里列了但未生效）。若目录内主要是 .wmv/.flv，会导致“0 文件入库 → 0 上传”。
- 今日运行日志文件 app_2025-08-15.log 为空，疑似 Celery worker/beat 未运行或未接收到任务，导致工作流未执行。
- 入库流程依赖“数据库中的主体列表”，只会扫描存在于 DB 的主体名子目录；若数据库中没有“缇萃百货”，也会导致目录被跳过。
- 提审模块采用异步适配器（AsyncAppealAdapter），只有当“计划创建”走通后才会进入提审；当前尚未到达该阶段。

建议优先排查顺序：
1) 确认 Celery worker 与 Celery beat 在 qc_env 中正常运行，并监听 default,high_priority 两个队列。
2) 检查待处理目录内的文件扩展名；如包含 .wmv/.flv，需修复入库扩展名过滤逻辑或转码/改名。
3) 确认数据库里存在主体“缇萃百货”，且有活跃 TEST 账户（用于测试上传）。

---

## 二、关键发现（按模块）

### 1) 文件扫描/入库（handle_file_ingestion）
- 基础路径解析：
  - base = custom_workflow_assets_dir（配置: D:/workflow_assets）
  - to_process = base/01_materials_to_process
  - principal_dir = to_process/<DB中的主体名>
- 仅当 principal_dir 存在时才扫描。
- 实际扫描扩展名是硬编码的：.mp4, .mov, .avi, .mkv（未包含 .wmv/.flv）。
- 每个命中的文件会计算 MD5，调用 find_or_create_local_creative 入库，状态置为 pending_grouping。

风险与影响：
- 目录中如以 .wmv/.flv 为主，将导致“无文件被入库”，后续派发/上传全程不会触发。
- 若 DB 不存在当前主体名（如“缇萃百货”），其目录不会被扫描。

改进建议：
- 将扩展名来源统一改为配置（workflow.file_ingestion.supported_formats 与 workflow.material_collection.video_extensions 的并集），避免代码与配置不一致。
- 扫描不到文件时输出更强提示日志（目录路径、扩展名统计、权限提示）。

### 2) 上传工作流（文件发现→入库→分组派发→上传）
- 周期调度位于 celery_app.py（已将“分组派发”升级为批处理任务）：
  - file-ingestion-configurable → tasks.ingest_and_upload（入库）
  - batch-upload-configurable → tasks.batch_upload_videos（批量派发上传）
- 入库成功后：pending_grouping →（在派发/批量上传开始时）转换为 pending_upload
- 账户筛选：仅选择主体下“TEST 且 active”的健康账户（is_account_healthy）。该函数对 API 错误是“容错为 True”，避免因临时错误阻断流程。
- 实际上传：tasks.upload_single_video → scheduler._process_upload_core_logic → sdk_qc.client.upload_video（含重试与 Token 自动刷新）。

风险与影响：
- 若无 TEST 账户或均被标记为不健康，将无法派发上传任务。
- 若 Celery worker/beat 未运行或监听的队列不全（需 default 与 high_priority），整个流程不会执行。

改进建议：
- 在 batch_upload_videos 开始处增加“关键对象与候选数量”的可观测日志，以便快速判定卡点（例如 pending_upload 数量、健康账户数量等）。

### 3) 提审模块
- 通过 AsyncAppealAdapter 适配异步提审；同步入口 appeal_plan_sync。
- 触发条件：计划创建后、状态满足提审策略时才会进来。
- 状态管理使用数据库事务与锁，已实现防重与失败回滚。

当前没有到达提审阶段的迹象，建议先打通入库→上传链路。

---

## 三、日志与调度现状
- 日志配置（loguru）写入项目根目录 logs/app_{date}.log。
- 今日日志文件 app_2025-08-15.log 为空，历史日志存在，推测当前没有活跃的定时任务输出。
- 建议核查 Celery 进程：
  - Worker（需监听 default,high_priority）
  - Beat（产生周期任务）

---

## 四、配置与路径核查
- config/settings.yml:
  - custom_workflow_assets_dir: D:/workflow_assets
  - workflow_dirs.DIR_01_TO_PROCESS: 01_materials_to_process
  - workflow.file_ingestion.supported_formats: [.mp4, .mov, .avi, .mkv]
  - workflow.material_collection.video_extensions: [.mp4, .mkv, .avi, .mov, .wmv, .flv]

风险与影响：
- 两处扩展名配置不一致且代码未引用配置，导致 .wmv/.flv 实际不被处理。

---

## 五、数据库侧要点（模型与状态）
- LocalCreative：file_path, filename, file_hash（唯一）, status（统一枚举，含 pending_grouping/pending_upload/uploading/processing/uploaded_pending_plan 等）。
- 入库后应看到 pending_grouping 分布增加；派发/批量上传开始时会批量转为 pending_upload。
- 若 10 分钟内完全无变化，可能原因：未扫描到文件、Celery 未执行、主体缺失、或权限错误导致 MD5/读文件失败（会有日志）。

---

## 六、根因假设与优先级
1) 高优先级：Celery worker/beat 未运行或未监听正确队列 → 工作流未启动（从今日日志为空推断）。
2) 高优先级：入库扩展名过滤未包含 .wmv/.flv，而目录内为这些格式 → 无文件入库。
3) 中优先级：数据库缺少主体“缇萃百货”或名称不一致 → 目录未被扫描。
4) 中优先级：目标目录/网络盘权限问题（读/锁定） → 入库阶段失败，但通常会有警告日志。

---

## 七、修复方案（小步快跑，向后兼容）
A. 运行确认（qc_env 虚拟环境）
- 确认并启动 Celery：
  1) conda activate qc_env
  2) 启动 worker：celery -A qianchuan_aw.celery_app:app worker -l info -Q default,high_priority
  3) 启动 beat：celery -A qianchuan_aw.celery_app:app beat -l info
- 观察 logs/app_{today}.log 是否开始有入库与派发日志。

B. 修复文件扫描扩展名
- 修改 scheduler.handle_file_ingestion，读取配置的扩展名并取并集（.mp4/.mov/.avi/.mkv/.wmv/.flv），统一到小写比较。
- 限制风险：仅更改过滤列表，不影响后续流程；保持向后兼容。

C. 验证主体与账户
- 确认 DB 中存在 Principal.name == “缇萃百货”。
- 确认该主体下存在 TEST 且 active 的 AdAccount。

D. 可观测性增强（可选）
- 在入库与批量上传任务开头增加统计日志（扫描到的文件数、扩展名分布、pending_* 数量、健康账户数等）。

---

## 八、建议的代码改进（示例补丁思路）
- 在 src/qianchuan_aw/workflows/scheduler.py 的 handle_file_ingestion 中：
  - 从 app_settings 取 formats1 = workflow.file_ingestion.supported_formats；formats2 = workflow.material_collection.video_extensions；
  - exts = set([e.lower() for e in (formats1 + formats2)])
  - 过滤条件改为 f.lower().endswith(tuple(exts))。

> 注意：我将等待确认后再提交补丁，并在提交前触发关键文件自动备份。

---

## 九、验证与测试
- 单元/集成测试建议：
  - 模拟不同扩展名文件分布，验证入库数量正确。
  - 数据库写入 LocalCreative 后状态梯度转换（pending_grouping→pending_upload）正确。
  - Mock QianchuanClient.upload_video，验证任务派发与重试机制。

---

## 十、辅助调试脚本
- 我已创建临时探针脚本（ai_temp/analysis/ai_temp_20250815_analysis_upload_workflow_probe.py），用于：
  - 基于配置定位扫描目录，统计各扩展名文件数与样本；
  - 列出 DB 主体名，抽样查看“缇萃百货”的素材状态。
- 运行方式（qc_env）：
  - conda activate qc_env
  - python ai_temp/analysis/ai_temp_20250815_analysis_upload_workflow_probe.py

---

## 十一、后续步骤（需要您的确认）
1) 我可以现在为 handle_file_ingestion 提交“按配置扩展名扫描”的修复补丁吗？
2) 需要我帮您在 qc_env 下启动/验证 Celery worker 与 beat，并回传关键日志吗？
3) 如需要，我可提供一键“上传系统快速体检”命令，自动输出可读报告。

---

## 附录 A：自动化健康检查结果
- 项目健康检查：失败（UnicodeDecodeError 读取某 .py 文件时，编码非 utf-8）。建议维护工具在遍历源码时跳过二进制/压缩文件或按二进制读取。
- Git 状态：clean，无未提交改动。
- 备份与清理：建议在问题修复后执行一次日常备份与临时文件清理。


