#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证浏览器进程限制移除效果
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import psutil
import time
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def count_browser_processes():
    """统计当前浏览器进程数量"""
    try:
        browser_count = 0
        browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
        
        browser_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'create_time']):
            try:
                proc_name = proc.info['name'].lower()
                if any(browser in proc_name for browser in browser_names):
                    browser_count += 1
                    browser_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'create_time': proc.info['create_time']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        return browser_count, browser_processes
        
    except Exception as e:
        logger.error(f"统计浏览器进程失败: {e}")
        return 0, []

def test_appeal_service_resource_check():
    """测试提审服务的资源检查"""
    logger.info("🧪 测试提审服务资源检查...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_appeal_service import AsyncAppealService
        
        # 创建服务实例
        service = AsyncAppealService({})
        
        logger.info(f"📊 浏览器进程阈值: {service.max_browser_processes}")
        
        # 测试资源检查
        import asyncio
        
        async def test_resource_check():
            result = await service._check_system_resources()
            return result
        
        # 运行异步测试
        result = asyncio.run(test_resource_check())
        
        if result:
            logger.success("✅ 系统资源检查通过")
        else:
            logger.warning("⚠️ 系统资源检查未通过")
        
        return result, service.max_browser_processes
        
    except Exception as e:
        logger.error(f"❌ 测试提审服务失败: {e}")
        return False, 0

def manual_trigger_plan_submission():
    """手动触发计划提审任务"""
    logger.info("🎯 手动触发计划提审任务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        # 手动调用任务
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 提审任务已派发，ID: {result.id}")
        
        # 等待任务执行
        logger.info("⏳ 等待任务执行...")
        time.sleep(10)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 提审任务执行成功")
                return True, "任务执行成功"
            else:
                error = result.result
                logger.error(f"❌ 提审任务执行失败: {error}")
                return False, str(error)
        else:
            logger.info("⏳ 提审任务仍在执行中...")
            return True, "任务正在执行"
            
    except Exception as e:
        logger.error(f"❌ 手动触发提审任务失败: {e}")
        return False, str(e)

def check_recent_appeal_logs():
    """检查最近的提审日志"""
    logger.info("📋 检查最近的提审日志...")
    
    try:
        # 检查日志文件
        log_dir = project_root / 'logs'
        
        if not log_dir.exists():
            logger.warning("⚠️ 日志目录不存在")
            return False
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob('*.log'))
        if not log_files:
            logger.warning("⚠️ 未找到日志文件")
            return False
        
        # 按修改时间排序，获取最新的日志文件
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        logger.info(f"📄 检查日志文件: {latest_log}")
        
        # 读取最近的日志内容
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近10分钟的提审相关日志
        current_time = datetime.now()
        recent_logs = []
        
        for line in lines[-1000:]:  # 只检查最后1000行
            if any(keyword in line for keyword in ['提审', 'appeal', 'submit_plans', '浏览器进程过多']):
                recent_logs.append(line.strip())
        
        if recent_logs:
            logger.info("📋 最近的提审相关日志:")
            for log_line in recent_logs[-10:]:  # 只显示最后10条
                if '浏览器进程过多' in log_line:
                    logger.error(f"   🚨 {log_line}")
                elif '提审成功' in log_line or 'appeal.*成功' in log_line:
                    logger.success(f"   ✅ {log_line}")
                elif '提审失败' in log_line or 'appeal.*失败' in log_line:
                    logger.warning(f"   ⚠️ {log_line}")
                else:
                    logger.info(f"   📋 {log_line}")
        else:
            logger.info("📋 未找到最近的提审相关日志")
        
        # 检查是否还有浏览器进程限制的错误
        browser_limit_errors = [line for line in recent_logs if '浏览器进程过多' in line]
        
        if browser_limit_errors:
            logger.error(f"❌ 仍然发现 {len(browser_limit_errors)} 个浏览器进程限制错误")
            return False
        else:
            logger.success("✅ 未发现浏览器进程限制错误")
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查日志失败: {e}")
        return False

def run_comprehensive_verification():
    """运行综合验证"""
    logger.info("🚀 开始浏览器限制移除效果验证...")
    logger.info("="*60)
    
    # 1. 统计当前浏览器进程
    browser_count, browser_processes = count_browser_processes()
    logger.info(f"📊 当前浏览器进程数量: {browser_count}")
    
    if browser_count > 10:
        logger.info("📋 浏览器进程较多，这是测试移除限制效果的好机会")
    
    # 2. 测试提审服务资源检查
    resource_check_passed, threshold = test_appeal_service_resource_check()
    
    # 3. 手动触发提审任务
    task_success, task_message = manual_trigger_plan_submission()
    
    # 4. 检查日志
    log_check_passed = check_recent_appeal_logs()
    
    # 5. 生成验证报告
    logger.info("\n" + "="*60)
    logger.info("🎯 验证结果总结")
    logger.info("="*60)
    
    logger.info(f"📊 当前浏览器进程数: {browser_count}")
    logger.info(f"⚙️ 提审服务浏览器阈值: {threshold}")
    
    if threshold >= 999:
        logger.success("✅ 浏览器进程阈值已成功提高到999")
    else:
        logger.error(f"❌ 浏览器进程阈值仍然较低: {threshold}")
    
    if resource_check_passed:
        logger.success("✅ 系统资源检查通过（不再被浏览器进程限制）")
    else:
        logger.warning("⚠️ 系统资源检查未通过（可能是其他原因）")
    
    if task_success:
        logger.success(f"✅ 提审任务执行成功: {task_message}")
    else:
        logger.error(f"❌ 提审任务执行失败: {task_message}")
    
    if log_check_passed:
        logger.success("✅ 日志检查通过，未发现浏览器进程限制错误")
    else:
        logger.warning("⚠️ 日志检查发现问题")
    
    # 综合评估
    success_indicators = [
        threshold >= 999,
        resource_check_passed,
        task_success,
        log_check_passed
    ]
    
    success_count = sum(success_indicators)
    success_rate = (success_count / len(success_indicators)) * 100
    
    logger.info(f"\n📈 验证成功率: {success_count}/{len(success_indicators)} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        logger.success("🎉 浏览器进程限制移除验证成功！")
        logger.info("\n📋 效果确认:")
        logger.info("✅ 提审服务不再因浏览器进程数量而失败")
        logger.info("✅ 用户可以正常使用大量浏览器进程")
        logger.info("✅ 计划提审功能恢复正常")
        
        logger.info("\n🔄 建议后续操作:")
        logger.info("1. 继续观察提审任务的执行情况")
        logger.info("2. 监控数据库中未提审计划数量的变化")
        logger.info("3. 确认工作流完整性恢复")
        
    else:
        logger.error("❌ 浏览器进程限制移除验证存在问题")
        logger.info("\n🔧 建议操作:")
        logger.info("1. 检查是否还有其他地方存在浏览器进程限制")
        logger.info("2. 重启Celery进程以确保更改生效")
        logger.info("3. 查看详细的错误日志")
    
    return success_rate >= 75

def main():
    """主函数"""
    try:
        success = run_comprehensive_verification()
        return 0 if success else 1
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
