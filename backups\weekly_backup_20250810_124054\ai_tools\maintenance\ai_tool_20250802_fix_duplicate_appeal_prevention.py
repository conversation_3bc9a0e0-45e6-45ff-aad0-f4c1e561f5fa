#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复工作流重复提审问题，确保每个计划只提审一次
清理条件: 成为项目核心组件后不可删除
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_
from src.qianchuan_aw.database.database import SessionLocal
from src.qianchuan_aw.database.models import Campaign, AdAccount
from src.qianchuan_aw.utils.logger import logger
from datetime import datetime, timezone
import yaml

def load_app_settings():
    """加载应用配置"""
    try:
        with open('config/settings.yml', 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return {}

class DuplicateAppealPrevention:
    """重复提审防护系统"""
    
    def __init__(self, app_settings):
        self.app_settings = app_settings
        logger.info("🛡️ 重复提审防护系统初始化")
    
    def analyze_current_appeal_status(self, db: Session):
        """分析当前提审状态"""
        logger.info("🔍 分析当前提审状态...")
        logger.info("="*80)
        
        # 查询所有计划的提审状态
        all_campaigns = db.query(Campaign).filter(
            Campaign.created_at >= '2025-08-01'
        ).all()
        
        # 分类统计
        status_stats = {
            'never_appealed': [],      # 从未提审过
            'appeal_pending': [],      # 提审成功，等待结果
            'submission_failed': [],   # 提审失败
            'multiple_attempts': [],   # 多次尝试提审
            'potential_duplicates': [] # 可能重复提审的
        }
        
        for campaign in all_campaigns:
            # 从未提审过
            if (campaign.appeal_status is None and 
                campaign.appeal_attempt_count == 0 and 
                campaign.first_appeal_at is None):
                status_stats['never_appealed'].append(campaign)
            
            # 提审成功，等待结果
            elif campaign.appeal_status == 'appeal_pending':
                status_stats['appeal_pending'].append(campaign)
                
                # 检查是否有多次尝试
                if campaign.appeal_attempt_count > 1:
                    status_stats['multiple_attempts'].append(campaign)
            
            # 提审失败
            elif campaign.appeal_status == 'submission_failed':
                status_stats['submission_failed'].append(campaign)
            
            # 潜在的重复提审问题
            elif (campaign.appeal_attempt_count > 2 or 
                  (campaign.appeal_status == 'appeal_pending' and campaign.appeal_attempt_count > 1)):
                status_stats['potential_duplicates'].append(campaign)
        
        # 输出分析结果
        logger.info("📊 提审状态分析结果:")
        logger.info(f"  📋 从未提审过: {len(status_stats['never_appealed'])} 个")
        logger.info(f"  ✅ 提审成功等待结果: {len(status_stats['appeal_pending'])} 个")
        logger.info(f"  ❌ 提审失败: {len(status_stats['submission_failed'])} 个")
        logger.info(f"  🔄 多次尝试提审: {len(status_stats['multiple_attempts'])} 个")
        logger.info(f"  ⚠️ 可能重复提审: {len(status_stats['potential_duplicates'])} 个")
        
        # 详细显示问题计划
        if status_stats['multiple_attempts']:
            logger.warning("🔄 多次尝试提审的计划:")
            for campaign in status_stats['multiple_attempts']:
                logger.warning(f"  - 计划 {campaign.campaign_id_qc}: "
                             f"状态={campaign.appeal_status}, "
                             f"尝试次数={campaign.appeal_attempt_count}, "
                             f"首次提审={campaign.first_appeal_at}")
        
        if status_stats['potential_duplicates']:
            logger.error("⚠️ 可能重复提审的计划:")
            for campaign in status_stats['potential_duplicates']:
                logger.error(f"  - 计划 {campaign.campaign_id_qc}: "
                           f"状态={campaign.appeal_status}, "
                           f"尝试次数={campaign.appeal_attempt_count}")
        
        return status_stats
    
    def create_enhanced_appeal_filter(self):
        """创建增强的提审过滤逻辑"""
        logger.info("🔧 创建增强的提审过滤逻辑...")
        
        enhanced_filter_code = '''
def get_plans_ready_for_first_appeal(db: Session) -> List[Campaign]:
    """获取准备进行首次提审的计划 - 增强版防重复逻辑"""
    
    plans = db.query(Campaign).options(
        joinedload(Campaign.account).joinedload(AdAccount.principal)
    ).filter(
        # 基础条件：计划状态为AUDITING
        Campaign.status == 'AUDITING',
        
        # 核心防重复条件：确保从未成功提审过
        and_(
            # 条件1：appeal_status必须为空（从未成功提审）
            Campaign.appeal_status.is_(None),
            
            # 条件2：first_appeal_at必须为空（从未开始提审）
            Campaign.first_appeal_at.is_(None),
            
            # 条件3：appeal_attempt_count必须为0或空（从未尝试提审）
            or_(
                Campaign.appeal_attempt_count.is_(None), 
                Campaign.appeal_attempt_count == 0
            )
        )
    ).all()
    
    logger.info(f"🔍 增强过滤后找到 {len(plans)} 个符合首次提审条件的计划")
    
    # 额外的安全检查
    safe_plans = []
    for plan in plans:
        # 双重检查：确保没有任何提审历史
        if (plan.appeal_status is None and 
            plan.first_appeal_at is None and 
            (plan.appeal_attempt_count is None or plan.appeal_attempt_count == 0)):
            safe_plans.append(plan)
        else:
            logger.warning(f"⚠️ 计划 {plan.campaign_id_qc} 未通过安全检查，跳过提审")
    
    logger.success(f"✅ 安全检查后确认 {len(safe_plans)} 个计划可以进行首次提审")
    return safe_plans

def get_plans_ready_for_retry_appeal(db: Session, retry_interval_minutes: int = 5) -> List[Campaign]:
    """获取准备进行重试提审的计划 - 仅限失败的计划"""
    
    now = datetime.now(timezone.utc)
    retry_cutoff = now - timedelta(minutes=retry_interval_minutes)
    
    plans = db.query(Campaign).options(
        joinedload(Campaign.account).joinedload(AdAccount.principal)
    ).filter(
        # 基础条件：计划状态为AUDITING
        Campaign.status == 'AUDITING',
        
        # 重试条件：只有失败的计划才能重试
        Campaign.appeal_status == 'submission_failed',
        
        # 时间条件：距离上次失败已超过重试间隔
        Campaign.last_appeal_at < retry_cutoff,
        
        # 限制条件：重试次数不超过3次
        Campaign.appeal_attempt_count < 3
    ).all()
    
    logger.info(f"🔄 找到 {len(plans)} 个符合重试条件的失败计划")
    return plans

def should_skip_plan_for_appeal(plan: Campaign) -> Tuple[bool, str]:
    """检查计划是否应该跳过提审 - 防重复核心逻辑"""
    
    # 检查1：已经成功提审的计划
    if plan.appeal_status == 'appeal_pending':
        return True, f"计划已成功提审，状态为appeal_pending，跳过重复提审"
    
    # 检查2：已经有提审历史的计划
    if plan.first_appeal_at is not None:
        return True, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，跳过重复提审"
    
    # 检查3：尝试次数过多的计划
    if plan.appeal_attempt_count and plan.appeal_attempt_count >= 3:
        return True, f"计划提审尝试次数过多 ({plan.appeal_attempt_count}次)，跳过继续尝试"
    
    # 检查4：状态不符合的计划
    if plan.status != 'AUDITING':
        return True, f"计划状态不是AUDITING ({plan.status})，不需要提审"
    
    return False, "计划符合提审条件"
'''
        
        # 保存增强过滤逻辑到文件
        with open('ai_tool_20250802_enhanced_appeal_filters.py', 'w', encoding='utf-8') as f:
            f.write('#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n')
            f.write('"""\n增强的提审过滤逻辑 - 防止重复提审\n"""\n\n')
            f.write('from sqlalchemy.orm import Session, joinedload\n')
            f.write('from sqlalchemy import or_, and_\n')
            f.write('from src.qianchuan_aw.database.models import Campaign, AdAccount\n')
            f.write('from src.qianchuan_aw.utils.logger import logger\n')
            f.write('from datetime import datetime, timezone, timedelta\n')
            f.write('from typing import List, Tuple\n\n')
            f.write(enhanced_filter_code)
        
        logger.success("✅ 增强的提审过滤逻辑已保存到 ai_tool_20250802_enhanced_appeal_filters.py")
        
        return enhanced_filter_code
    
    def fix_workflow_integration(self):
        """修复工作流集成中的重复提审问题"""
        logger.info("🔧 修复工作流集成中的重复提审问题...")
        
        # 读取当前的工作流集成文件
        integration_file = 'ai_tool_20250801_enhancement_workflow_integration.py'
        
        try:
            with open(integration_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经有防重复逻辑
            if 'should_skip_plan_for_appeal' in content:
                logger.info("✅ 工作流集成文件已包含防重复逻辑")
                return True
            
            # 添加防重复检查逻辑
            logger.info("📝 添加防重复检查逻辑到工作流集成...")
            
            # 在_find_candidate_plans函数后添加额外的安全检查
            additional_check = '''
    
    def _additional_safety_check(self, plans: List[Campaign]) -> List[Campaign]:
        """额外的安全检查 - 防止重复提审"""
        
        safe_plans = []
        
        for plan in plans:
            # 使用增强的防重复逻辑
            should_skip, reason = self._should_skip_plan_for_appeal(plan)
            
            if should_skip:
                logger.warning(f"⚠️ 跳过计划 {plan.campaign_id_qc}: {reason}")
                continue
            
            safe_plans.append(plan)
        
        logger.info(f"🛡️ 安全检查后确认 {len(safe_plans)}/{len(plans)} 个计划可以提审")
        return safe_plans
    
    def _should_skip_plan_for_appeal(self, plan: Campaign) -> Tuple[bool, str]:
        """检查计划是否应该跳过提审 - 防重复核心逻辑"""
        
        # 检查1：已经成功提审的计划
        if plan.appeal_status == 'appeal_pending':
            return True, f"计划已成功提审，状态为appeal_pending，跳过重复提审"
        
        # 检查2：已经有提审历史的计划
        if plan.first_appeal_at is not None:
            return True, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，跳过重复提审"
        
        # 检查3：尝试次数过多的计划
        if plan.appeal_attempt_count and plan.appeal_attempt_count >= 3:
            return True, f"计划提审尝试次数过多 ({plan.appeal_attempt_count}次)，跳过继续尝试"
        
        # 检查4：状态不符合的计划
        if plan.status != 'AUDITING':
            return True, f"计划状态不是AUDITING ({plan.status})，不需要提审"
        
        return False, "计划符合提审条件"
'''
            
            logger.success("✅ 防重复逻辑已准备就绪")
            return additional_check
            
        except Exception as e:
            logger.error(f"❌ 修复工作流集成失败: {e}")
            return None

def main():
    """主函数"""
    logger.info("🚀 启动重复提审问题修复工具")
    logger.info("="*80)
    
    app_settings = load_app_settings()
    prevention = DuplicateAppealPrevention(app_settings)
    
    with SessionLocal() as db:
        # 1. 分析当前提审状态
        status_stats = prevention.analyze_current_appeal_status(db)
        
        # 2. 创建增强的过滤逻辑
        enhanced_filter = prevention.create_enhanced_appeal_filter()
        
        # 3. 修复工作流集成
        additional_check = prevention.fix_workflow_integration()
        
        # 4. 提供修复建议
        logger.info("\n🎯 修复建议:")
        logger.info("="*80)
        logger.info("1. ✅ 已创建增强的提审过滤逻辑")
        logger.info("2. 🔧 需要将防重复逻辑集成到工作流中")
        logger.info("3. 🛡️ 建议在每次提审前都进行安全检查")
        logger.info("4. 📊 定期监控提审状态，防止重复提审")
        
        # 5. 检查是否有需要立即处理的问题
        if status_stats['potential_duplicates']:
            logger.warning("\n⚠️ 发现可能的重复提审问题，建议立即处理！")
            logger.warning("建议运行: python ai_tool_20250802_fix_duplicate_appeal_prevention.py")

if __name__ == "__main__":
    main()
