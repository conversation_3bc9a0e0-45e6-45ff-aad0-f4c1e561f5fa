#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化项目综合日志分析工具
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 分析项目运行日志，识别错误模式、性能问题和业务流程问题
维护团队: 技术团队
"""

import sys
import os
import re
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from collections import defaultdict, Counter
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class ComprehensiveLogAnalyzer:
    """综合日志分析器"""
    
    def __init__(self):
        self.log_dir = project_root / "logs"
        self.analysis_time_window = timedelta(hours=5)
        self.current_time = datetime.now()
        self.start_time = self.current_time - self.analysis_time_window
        
        # 错误模式定义
        self.error_patterns = {
            'api_failures': [
                r'API请求失败.*Code: (\d+).*Message: (.+)',
                r'❌.*API.*失败',
                r'QianchuanAPIError.*: (.+)',
            ],
            'file_errors': [
                r'❌ 文件不存在: (.+)',
                r'文件格式错误',
                r'视频尺寸错误',
                r'moov atom not found',
            ],
            'database_errors': [
                r'数据库.*失败',
                r'❌ 状态转换失败',
                r'数据库事务回滚',
                r'DetachedInstanceError',
            ],
            'task_errors': [
                r'❌.*Task Failed.*Error',
                r'Task.*retry.*attempt',
                r'Celery.*ERROR',
                r'Worker.*crashed',
            ],
            'upload_errors': [
                r'❌ 上传失败',
                r'上传过程中发生.*错误',
                r'upload.*failed',
                r'uploading.*error',
            ],
            'plan_creation_errors': [
                r'计划创建.*失败',
                r'create.*plan.*error',
                r'Campaign.*creation.*failed',
            ],
            'appeal_errors': [
                r'提审.*失败',
                r'appeal.*failed',
                r'申诉.*错误',
            ]
        }
        
        # 性能指标模式
        self.performance_patterns = {
            'task_duration': r'Task.*succeeded in ([\d.]+)s',
            'upload_time': r'上传.*耗时.*?([\d.]+).*秒',
            'api_response_time': r'API.*响应时间.*?([\d.]+).*ms',
            'queue_size': r'队列.*大小.*?(\d+)',
        }
        
        # 业务流程模式
        self.workflow_patterns = {
            'material_upload': r'🚀.*Uploading video.*(.+)',
            'plan_creation': r'📋.*创建计划.*(.+)',
            'appeal_submission': r'📤.*提审.*(.+)',
            'status_transition': r'状态转换.*素材(\d+).*(.+)->(.+)',
        }
        
        self.analysis_results = {
            'errors': defaultdict(list),
            'performance': defaultdict(list),
            'workflow': defaultdict(list),
            'statistics': defaultdict(int),
            'timeline': [],
            'recommendations': []
        }
    
    def find_log_files(self) -> List[Path]:
        """查找日志文件"""
        log_files = []
        
        if not self.log_dir.exists():
            print(f"⚠️ 日志目录不存在: {self.log_dir}")
            return log_files
        
        # 查找各种类型的日志文件
        patterns = [
            "*.log",
            "celery*.log",
            "worker*.log",
            "qianchuan*.log",
            "app*.log"
        ]
        
        for pattern in patterns:
            for log_file in self.log_dir.glob(pattern):
                if log_file.is_file():
                    # 检查文件修改时间是否在分析窗口内
                    mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if mtime >= self.start_time:
                        log_files.append(log_file)
        
        # 如果没有找到日志文件，尝试查找系统日志
        if not log_files:
            print("🔍 未找到标准日志文件，尝试查找其他日志...")
            for log_file in self.log_dir.rglob("*.log"):
                if log_file.is_file():
                    mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if mtime >= self.start_time:
                        log_files.append(log_file)
        
        return log_files
    
    def parse_log_line(self, line: str, timestamp: Optional[datetime] = None) -> Dict:
        """解析日志行"""
        log_entry = {
            'timestamp': timestamp or self.current_time,
            'level': 'INFO',
            'message': line.strip(),
            'errors': [],
            'performance': {},
            'workflow': {}
        }
        
        # 提取日志级别
        level_match = re.search(r'\b(DEBUG|INFO|WARNING|ERROR|CRITICAL)\b', line)
        if level_match:
            log_entry['level'] = level_match.group(1)
        
        # 检查错误模式
        for error_type, patterns in self.error_patterns.items():
            for pattern in patterns:
                matches = re.findall(pattern, line, re.IGNORECASE)
                if matches:
                    log_entry['errors'].append({
                        'type': error_type,
                        'pattern': pattern,
                        'matches': matches
                    })
        
        # 检查性能模式
        for perf_type, pattern in self.performance_patterns.items():
            matches = re.findall(pattern, line, re.IGNORECASE)
            if matches:
                log_entry['performance'][perf_type] = matches
        
        # 检查业务流程模式
        for workflow_type, pattern in self.workflow_patterns.items():
            matches = re.findall(pattern, line, re.IGNORECASE)
            if matches:
                log_entry['workflow'][workflow_type] = matches
        
        return log_entry
    
    def analyze_log_file(self, log_file: Path) -> Dict:
        """分析单个日志文件"""
        print(f"📄 分析日志文件: {log_file.name}")
        
        file_stats = {
            'total_lines': 0,
            'error_lines': 0,
            'warning_lines': 0,
            'time_range': None
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                file_stats['total_lines'] = len(lines)
                
                timestamps = []
                
                for line in lines:
                    # 尝试提取时间戳
                    timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    timestamp = None
                    
                    if timestamp_match:
                        try:
                            timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                            timestamps.append(timestamp)
                            
                            # 只分析时间窗口内的日志
                            if timestamp < self.start_time:
                                continue
                        except:
                            pass
                    
                    # 解析日志行
                    log_entry = self.parse_log_line(line, timestamp)
                    
                    # 统计错误和警告
                    if log_entry['level'] == 'ERROR':
                        file_stats['error_lines'] += 1
                    elif log_entry['level'] == 'WARNING':
                        file_stats['warning_lines'] += 1
                    
                    # 收集错误信息
                    for error in log_entry['errors']:
                        self.analysis_results['errors'][error['type']].append({
                            'timestamp': log_entry['timestamp'],
                            'message': log_entry['message'],
                            'matches': error['matches'],
                            'file': log_file.name
                        })
                    
                    # 收集性能信息
                    for perf_type, values in log_entry['performance'].items():
                        self.analysis_results['performance'][perf_type].extend([
                            {
                                'timestamp': log_entry['timestamp'],
                                'value': v,
                                'file': log_file.name
                            } for v in values
                        ])
                    
                    # 收集工作流信息
                    for workflow_type, values in log_entry['workflow'].items():
                        self.analysis_results['workflow'][workflow_type].extend([
                            {
                                'timestamp': log_entry['timestamp'],
                                'value': v,
                                'file': log_file.name
                            } for v in values
                        ])
                    
                    # 添加到时间线
                    if log_entry['errors'] or log_entry['performance'] or log_entry['workflow']:
                        self.analysis_results['timeline'].append(log_entry)
                
                # 设置时间范围
                if timestamps:
                    file_stats['time_range'] = (min(timestamps), max(timestamps))
        
        except Exception as e:
            print(f"❌ 分析日志文件失败 {log_file}: {e}")
        
        return file_stats
    
    def generate_error_analysis(self) -> Dict:
        """生成错误分析报告"""
        print("\n🔍 生成错误分析报告...")
        
        error_analysis = {
            'summary': {},
            'patterns': {},
            'frequency': {},
            'impact': {},
            'priority': {}
        }
        
        # 统计错误频率
        for error_type, errors in self.analysis_results['errors'].items():
            error_count = len(errors)
            error_analysis['frequency'][error_type] = error_count
            
            if error_count > 0:
                # 分析错误模式
                messages = [e['message'] for e in errors]
                error_analysis['patterns'][error_type] = Counter(messages).most_common(5)
                
                # 评估影响
                if error_count >= 50:
                    impact = 'HIGH'
                elif error_count >= 10:
                    impact = 'MEDIUM'
                else:
                    impact = 'LOW'
                
                error_analysis['impact'][error_type] = impact
                
                # 确定优先级
                if error_type in ['api_failures', 'database_errors', 'task_errors']:
                    priority = 'HIGH'
                elif error_type in ['upload_errors', 'plan_creation_errors']:
                    priority = 'MEDIUM'
                else:
                    priority = 'LOW'
                
                error_analysis['priority'][error_type] = priority
        
        # 生成总结
        total_errors = sum(error_analysis['frequency'].values())
        high_priority_errors = sum(
            count for error_type, count in error_analysis['frequency'].items()
            if error_analysis['priority'].get(error_type) == 'HIGH'
        )
        
        error_analysis['summary'] = {
            'total_errors': total_errors,
            'high_priority_errors': high_priority_errors,
            'error_types': len(error_analysis['frequency']),
            'most_frequent': max(error_analysis['frequency'].items(), key=lambda x: x[1]) if error_analysis['frequency'] else None
        }
        
        return error_analysis
    
    def generate_performance_analysis(self) -> Dict:
        """生成性能分析报告"""
        print("\n📊 生成性能分析报告...")
        
        performance_analysis = {
            'task_performance': {},
            'api_performance': {},
            'bottlenecks': [],
            'recommendations': []
        }
        
        # 分析任务性能
        if 'task_duration' in self.analysis_results['performance']:
            durations = [
                float(p['value']) for p in self.analysis_results['performance']['task_duration']
                if p['value'].replace('.', '').isdigit()
            ]
            
            if durations:
                performance_analysis['task_performance'] = {
                    'count': len(durations),
                    'avg_duration': sum(durations) / len(durations),
                    'max_duration': max(durations),
                    'min_duration': min(durations)
                }
                
                # 识别性能瓶颈
                if performance_analysis['task_performance']['avg_duration'] > 60:
                    performance_analysis['bottlenecks'].append('任务平均执行时间过长')
                
                if performance_analysis['task_performance']['max_duration'] > 300:
                    performance_analysis['bottlenecks'].append('存在超长执行任务')
        
        # 分析API性能
        if 'api_response_time' in self.analysis_results['performance']:
            response_times = [
                float(p['value']) for p in self.analysis_results['performance']['api_response_time']
                if p['value'].replace('.', '').isdigit()
            ]
            
            if response_times:
                performance_analysis['api_performance'] = {
                    'count': len(response_times),
                    'avg_response_time': sum(response_times) / len(response_times),
                    'max_response_time': max(response_times),
                    'min_response_time': min(response_times)
                }
                
                # 识别API性能问题
                if performance_analysis['api_performance']['avg_response_time'] > 5000:
                    performance_analysis['bottlenecks'].append('API响应时间过长')
        
        return performance_analysis
    
    def generate_workflow_analysis(self) -> Dict:
        """生成业务流程分析报告"""
        print("\n🔄 生成业务流程分析报告...")
        
        workflow_analysis = {
            'material_flow': {},
            'completion_rates': {},
            'bottlenecks': [],
            'recommendations': []
        }
        
        # 分析素材流转
        upload_count = len(self.analysis_results['workflow'].get('material_upload', []))
        plan_count = len(self.analysis_results['workflow'].get('plan_creation', []))
        appeal_count = len(self.analysis_results['workflow'].get('appeal_submission', []))
        
        workflow_analysis['material_flow'] = {
            'uploads': upload_count,
            'plans_created': plan_count,
            'appeals_submitted': appeal_count
        }
        
        # 计算完成率
        if upload_count > 0:
            workflow_analysis['completion_rates'] = {
                'upload_to_plan': (plan_count / upload_count) * 100 if upload_count > 0 else 0,
                'plan_to_appeal': (appeal_count / plan_count) * 100 if plan_count > 0 else 0,
                'overall_completion': (appeal_count / upload_count) * 100 if upload_count > 0 else 0
            }
        
        # 识别工作流瓶颈
        if workflow_analysis['completion_rates'].get('upload_to_plan', 0) < 80:
            workflow_analysis['bottlenecks'].append('上传到计划创建转换率低')
        
        if workflow_analysis['completion_rates'].get('plan_to_appeal', 0) < 80:
            workflow_analysis['bottlenecks'].append('计划创建到提审转换率低')
        
        return workflow_analysis

def main():
    """主函数"""
    from datetime import datetime, timedelta

    print("🚀 千川自动化项目综合日志分析")
    print("=" * 60)
    print(f"📅 分析时间窗口: 近5小时")
    print(f"🕐 开始时间: {datetime.now() - timedelta(hours=5)}")
    print(f"🕐 结束时间: {datetime.now()}")
    
    analyzer = ComprehensiveLogAnalyzer()
    
    try:
        # 1. 查找日志文件
        log_files = analyzer.find_log_files()
        print(f"\n📄 找到 {len(log_files)} 个日志文件")
        
        if not log_files:
            print("⚠️ 未找到日志文件，尝试分析数据库状态...")
            # 这里可以添加数据库状态分析
            return 1
        
        # 2. 分析日志文件
        for log_file in log_files:
            analyzer.analyze_log_file(log_file)
        
        # 3. 生成分析报告
        error_analysis = analyzer.generate_error_analysis()
        performance_analysis = analyzer.generate_performance_analysis()
        workflow_analysis = analyzer.generate_workflow_analysis()
        
        # 4. 输出分析结果
        print(f"\n{'='*60}")
        print("📊 日志分析结果")
        print(f"{'='*60}")
        
        # 错误分析
        print(f"\n🚨 错误分析:")
        print(f"  - 总错误数: {error_analysis['summary'].get('total_errors', 0)}")
        print(f"  - 高优先级错误: {error_analysis['summary'].get('high_priority_errors', 0)}")
        print(f"  - 错误类型数: {error_analysis['summary'].get('error_types', 0)}")
        
        if error_analysis['frequency']:
            print(f"\n📋 错误频率排序:")
            for error_type, count in sorted(error_analysis['frequency'].items(), key=lambda x: x[1], reverse=True):
                priority = error_analysis['priority'].get(error_type, 'LOW')
                print(f"  - {error_type}: {count} 次 [{priority}]")
        
        # 性能分析
        if performance_analysis['task_performance']:
            print(f"\n⚡ 性能分析:")
            perf = performance_analysis['task_performance']
            print(f"  - 任务数量: {perf['count']}")
            print(f"  - 平均耗时: {perf['avg_duration']:.2f}秒")
            print(f"  - 最长耗时: {perf['max_duration']:.2f}秒")
        
        # 工作流分析
        if workflow_analysis['material_flow']:
            print(f"\n🔄 工作流分析:")
            flow = workflow_analysis['material_flow']
            print(f"  - 上传数量: {flow['uploads']}")
            print(f"  - 计划创建: {flow['plans_created']}")
            print(f"  - 提审数量: {flow['appeals_submitted']}")
            
            if workflow_analysis['completion_rates']:
                rates = workflow_analysis['completion_rates']
                print(f"  - 上传→计划转换率: {rates['upload_to_plan']:.1f}%")
                print(f"  - 计划→提审转换率: {rates['plan_to_appeal']:.1f}%")
                print(f"  - 整体完成率: {rates['overall_completion']:.1f}%")
        
        # 保存详细报告
        report_file = Path("logs") / f"comprehensive_log_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_file.parent.mkdir(exist_ok=True)
        
        detailed_report = {
            'analysis_time': datetime.now().isoformat(),
            'time_window': '5_hours',
            'error_analysis': error_analysis,
            'performance_analysis': performance_analysis,
            'workflow_analysis': workflow_analysis
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(detailed_report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 详细报告已保存到: {report_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 日志分析过程中发生错误: {e}")
        return 2

if __name__ == "__main__":
    exit(main())
