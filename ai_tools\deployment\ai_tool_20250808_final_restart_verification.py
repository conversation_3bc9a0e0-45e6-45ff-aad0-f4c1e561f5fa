#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 最终安全重启验证
清理条件: 成为项目核心验证工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative


class FinalRestartVerification:
    """最终安全重启验证"""
    
    def run_final_verification(self):
        """运行最终验证"""
        logger.info("🎯 最终安全重启验证")
        logger.info("="*100)
        
        # 1. 验证Windows兼容性修复
        windows_fix = self._verify_windows_compatibility()
        
        # 2. 验证Redis队列清理
        redis_status = self._verify_redis_cleanup()
        
        # 3. 验证数据库状态
        database_status = self._verify_database_final_state()
        
        # 4. 验证代码修复
        code_fix = self._verify_code_fixes()
        
        # 5. 生成最终报告
        self._generate_final_report({
            'windows_fix': windows_fix,
            'redis_status': redis_status,
            'database_status': database_status,
            'code_fix': code_fix
        })
    
    def _verify_windows_compatibility(self):
        """验证Windows兼容性修复"""
        logger.info("🪟 验证Windows兼容性修复...")
        
        try:
            # 检查scheduler.py是否移除了fcntl
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查修复
            windows_fixes = [
                'import fcntl' not in content,                    # 移除fcntl导入
                'Windows兼容' in content,                         # 添加Windows兼容注释
                'os.path.getmtime' in content,                   # 使用文件时间检查
                'os.remove(lock_file_path)' in content          # 使用os.remove删除锁文件
            ]
            
            fix_score = sum(windows_fixes) / len(windows_fixes) * 100
            
            if fix_score >= 75:
                logger.success(f"✅ Windows兼容性修复验证通过 ({fix_score:.0f}%)")
                return {'windows_compatible': True, 'fix_score': fix_score}
            else:
                logger.error(f"❌ Windows兼容性修复不完整 ({fix_score:.0f}%)")
                return {'windows_compatible': False, 'fix_score': fix_score}
                
        except Exception as e:
            logger.error(f"❌ Windows兼容性验证失败: {e}")
            return {'windows_compatible': False, 'error': str(e)}
    
    def _verify_redis_cleanup(self):
        """验证Redis清理状态"""
        logger.info("🗄️ 验证Redis清理状态...")
        
        try:
            import redis
            r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            
            # 检查Celery相关键
            celery_keys = r.keys('*celery*')
            kombu_keys = r.keys('*kombu*')
            beat_keys = r.keys('*beat*')
            
            total_remaining = len(celery_keys) + len(kombu_keys) + len(beat_keys)
            
            if total_remaining == 0:
                logger.success("✅ Redis队列完全清理")
                return {'redis_clean': True, 'remaining_keys': 0}
            else:
                logger.warning(f"⚠️ Redis仍有 {total_remaining} 个相关键")
                return {'redis_clean': False, 'remaining_keys': total_remaining}
                
        except Exception as e:
            logger.error(f"❌ Redis验证失败: {e}")
            return {'redis_clean': None, 'error': str(e)}
    
    def _verify_database_final_state(self):
        """验证数据库最终状态"""
        logger.info("🗄️ 验证数据库最终状态...")
        
        with SessionLocal() as db:
            # 检查关键状态
            processing_count = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value
            ).count()
            
            pending_upload_count = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
            ).count()
            
            uploaded_pending_plan_count = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value
            ).count()
            
            # 状态健康评估
            if processing_count == 0:
                status_health = 'EXCELLENT'
                logger.success("✅ 无processing状态积压")
            elif processing_count < 5:
                status_health = 'GOOD'
                logger.warning(f"⚠️ 少量processing状态: {processing_count}个")
            else:
                status_health = 'CRITICAL'
                logger.error(f"❌ processing状态积压: {processing_count}个")
            
            logger.info(f"📊 数据库状态:")
            logger.info(f"   ⏳ Processing: {processing_count} 个")
            logger.info(f"   📤 Pending Upload: {pending_upload_count} 个")
            logger.info(f"   📋 Uploaded Pending Plan: {uploaded_pending_plan_count} 个")
            
            return {
                'processing_count': processing_count,
                'pending_upload_count': pending_upload_count,
                'uploaded_pending_plan_count': uploaded_pending_plan_count,
                'status_health': status_health,
                'data_ready': pending_upload_count > 0 or uploaded_pending_plan_count > 0
            }
    
    def _verify_code_fixes(self):
        """验证代码修复"""
        logger.info("🔧 验证代码修复...")
        
        fixes_verified = []
        
        try:
            # 验证原子状态管理器
            from qianchuan_aw.utils.atomic_state_manager import AtomicStateManager
from qianchuan_aw.utils.unified_material_status import MaterialStatus
            
            import inspect
            source = inspect.getsource(AtomicStateManager.safe_dispatch_upload_task)
            
            if 'return False' in source and 'return True' in source:
                fixes_verified.append("原子状态管理器返回值修复")
                logger.success("✅ 原子状态管理器返回值修复验证通过")
            
            # 验证scheduler修复
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                scheduler_content = f.read()
            
            if 'dispatch_success' in scheduler_content:
                fixes_verified.append("Scheduler返回值处理修复")
                logger.success("✅ Scheduler返回值处理修复验证通过")
            
            if 'Windows兼容' in scheduler_content:
                fixes_verified.append("Windows兼容性修复")
                logger.success("✅ Windows兼容性修复验证通过")
            
            return {
                'fixes_verified': fixes_verified,
                'fix_count': len(fixes_verified),
                'all_fixes_applied': len(fixes_verified) >= 3
            }
            
        except Exception as e:
            logger.error(f"❌ 代码修复验证失败: {e}")
            return {'fixes_verified': [], 'fix_count': 0, 'all_fixes_applied': False}
    
    def _generate_final_report(self, results):
        """生成最终报告"""
        logger.info("\n📋 最终安全重启验证报告")
        logger.info("="*100)
        
        # 修复状态
        windows_fix = results['windows_fix']
        redis_status = results['redis_status']
        database_status = results['database_status']
        code_fix = results['code_fix']
        
        # 计算总体评分
        scores = []
        
        if windows_fix['windows_compatible']:
            scores.append(100)
            logger.success("✅ Windows兼容性: 完全修复")
        else:
            scores.append(0)
            logger.error("❌ Windows兼容性: 仍有问题")
        
        if redis_status['redis_clean']:
            scores.append(100)
            logger.success("✅ Redis队列: 完全清理")
        else:
            scores.append(50)
            logger.warning("⚠️ Redis队列: 部分清理")
        
        if database_status['status_health'] == 'EXCELLENT':
            scores.append(100)
            logger.success("✅ 数据库状态: 优秀")
        elif database_status['status_health'] == 'GOOD':
            scores.append(80)
            logger.warning("⚠️ 数据库状态: 良好")
        else:
            scores.append(0)
            logger.error("❌ 数据库状态: 严重问题")
        
        if code_fix['all_fixes_applied']:
            scores.append(100)
            logger.success("✅ 代码修复: 全部完成")
        else:
            scores.append(50)
            logger.warning("⚠️ 代码修复: 部分完成")
        
        overall_score = sum(scores) / len(scores)
        
        logger.info(f"\n🎯 最终验证总分: {overall_score:.1f}/100")
        
        # 重启建议
        if overall_score >= 90:
            logger.success("🚀 系统完全就绪，可以立即重启")
            restart_recommendation = "IMMEDIATE"
        elif overall_score >= 75:
            logger.warning("⚠️ 系统基本就绪，建议小批量测试后重启")
            restart_recommendation = "CAUTIOUS"
        else:
            logger.error("❌ 系统仍有问题，需要进一步修复")
            restart_recommendation = "NOT_READY"
        
        # 具体重启指令
        logger.info("\n🚀 重启指令")
        logger.info("="*80)
        
        if restart_recommendation == "IMMEDIATE":
            logger.info("🎯 立即重启模式:")
            logger.info("   # 终端1:")
            logger.info("   python run_celery_beat.py")
            logger.info("")
            logger.info("   # 终端2:")
            logger.info("   python run_celery_worker.py")
            
        elif restart_recommendation == "CAUTIOUS":
            logger.info("🛡️ 谨慎重启模式:")
            logger.info("   # 终端1 - 先启动Worker:")
            logger.info("   python run_celery_worker.py")
            logger.info("")
            logger.info("   # 终端2 - 小批量测试:")
            logger.info("   python -c \"")
            logger.info("   from qianchuan_aw.workflows.tasks import batch_upload_videos")
            logger.info("   result = batch_upload_videos.delay(batch_size=2)")
            logger.info("   print('测试任务ID:', result.id)")
            logger.info("   \"")
            logger.info("")
            logger.info("   # 观察5分钟无问题后启动Beat:")
            logger.info("   python run_celery_beat.py")
        
        else:
            logger.info("🚨 系统未就绪，需要进一步修复")
        
        # 监控指标
        logger.info("\n📊 关键监控指标")
        logger.info("="*80)
        logger.info("🎯 成功运行的标志:")
        logger.info("   • Processing状态保持在0-3个")
        logger.info("   • 无'No module named fcntl'错误")
        logger.info("   • 无'状态不匹配'错误")
        logger.info("   • 上传任务正常执行")
        logger.info("   • 浏览器进程稳定")
        
        logger.info("\n🚨 需要立即停止的情况:")
        logger.info("   • Processing状态积压 > 10个")
        logger.info("   • 大量模块导入错误")
        logger.info("   • 重复的状态转换错误")
        logger.info("   • 系统资源耗尽")
        
        return {
            'overall_score': overall_score,
            'restart_recommendation': restart_recommendation,
            'system_ready': overall_score >= 75
        }


def main():
    """主函数"""
    verifier = FinalRestartVerification()
    result = verifier.run_final_verification()
    
    overall_score = result['overall_score']
    system_ready = result['system_ready']
    
    if system_ready:
        logger.success(f"🎊 最终验证通过: {overall_score:.1f}/100")
        logger.success("🚀 系统已完全修复，可以安全重启！")
        return 0
    else:
        logger.error(f"❌ 最终验证失败: {overall_score:.1f}/100")
        logger.error("🔧 需要进一步修复问题")
        return 1


if __name__ == "__main__":
    exit(main())
