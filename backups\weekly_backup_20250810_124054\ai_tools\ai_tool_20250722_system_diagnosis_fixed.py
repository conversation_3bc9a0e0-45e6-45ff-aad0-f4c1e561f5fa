#!/usr/bin/env python3
"""
千川系统全面诊断工具 (修正版)
基于实际数据库表结构检查系统中卡住的任务、异常数据和流程问题
"""

import sys
import os
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import json

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class SystemDiagnosisAnalyzer:
    """系统诊断分析器 (修正版)"""
    
    def __init__(self):
        self.diagnosis_results = {
            'local_creatives_issues': {},
            'platform_creatives_issues': {},
            'campaign_status_issues': {},
            'workflow_bottlenecks': {},
            'data_inconsistencies': {},
            'recommendations': []
        }
    
    def analyze_local_creatives_flow(self):
        """分析本地素材状态流转问题"""
        logger.info("🔍 1. 分析本地素材状态流转问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询本地素材状态分布
                status_query = text("""
                    SELECT status, COUNT(*) as count, 
                           MIN(created_at) as oldest_create,
                           MAX(updated_at) as latest_update
                    FROM local_creatives 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                
                status_results = db.execute(status_query).fetchall()
                
                logger.info("📊 本地素材状态分布:")
                total_creatives = sum(row.count for row in status_results)
                
                for row in status_results:
                    percentage = (row.count / total_creatives * 100) if total_creatives > 0 else 0
                    logger.info(f"  {row.status}: {row.count}个 ({percentage:.1f}%)")
                    logger.info(f"    最早创建: {row.oldest_create}")
                    logger.info(f"    最新更新: {row.latest_update}")
                
                # 查找长时间卡在某状态的素材
                stuck_query = text("""
                    SELECT status, COUNT(*) as count,
                           AVG(EXTRACT(EPOCH FROM (NOW() - updated_at))/3600) as avg_hours_stuck
                    FROM local_creatives 
                    WHERE updated_at < NOW() - INTERVAL '2 hours'
                    GROUP BY status
                    HAVING COUNT(*) > 0
                    ORDER BY avg_hours_stuck DESC
                """)
                
                stuck_results = db.execute(stuck_query).fetchall()
                
                if stuck_results:
                    logger.warning("🚨 发现长时间卡住的本地素材:")
                    for row in stuck_results:
                        logger.warning(f"  {row.status}: {row.count}个素材，平均卡住 {row.avg_hours_stuck:.1f} 小时")
                
                self.diagnosis_results['local_creatives_issues'] = {
                    'total_creatives': total_creatives,
                    'status_distribution': [(r.status, r.count) for r in status_results],
                    'stuck_creatives': [(r.status, r.count, r.avg_hours_stuck) for r in stuck_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析本地素材状态失败: {e}")
    
    def analyze_platform_creatives_flow(self):
        """分析平台素材状态流转问题"""
        logger.info("\n🔍 2. 分析平台素材状态流转问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询平台素材状态分布
                platform_status_query = text("""
                    SELECT review_status, COUNT(*) as count,
                           MIN(created_at) as oldest_create,
                           MAX(updated_at) as latest_update
                    FROM platform_creatives 
                    GROUP BY review_status 
                    ORDER BY count DESC
                """)
                
                platform_results = db.execute(platform_status_query).fetchall()
                
                logger.info("📊 平台素材审核状态分布:")
                total_platform = sum(row.count for row in platform_results)
                
                for row in platform_results:
                    percentage = (row.count / total_platform * 100) if total_platform > 0 else 0
                    logger.info(f"  {row.review_status}: {row.count}个 ({percentage:.1f}%)")
                
                # 查找需要创建计划的素材
                pending_plan_query = text("""
                    SELECT pc.review_status, COUNT(*) as count,
                           AVG(EXTRACT(EPOCH FROM (NOW() - pc.updated_at))/3600) as avg_hours_waiting
                    FROM platform_creatives pc
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    WHERE pc.review_status = 'APPROVED' 
                        AND cpca.campaign_id IS NULL
                        AND pc.updated_at < NOW() - INTERVAL '1 hour'
                    GROUP BY pc.review_status
                """)
                
                pending_results = db.execute(pending_plan_query).fetchall()
                
                if pending_results:
                    logger.warning("🚨 发现需要创建计划的已审核素材:")
                    for row in pending_results:
                        logger.warning(f"  {row.review_status}: {row.count}个素材，等待 {row.avg_hours_waiting:.1f} 小时")
                
                self.diagnosis_results['platform_creatives_issues'] = {
                    'total_platform': total_platform,
                    'status_distribution': [(r.review_status, r.count) for r in platform_results],
                    'pending_plan_creation': [(r.review_status, r.count, r.avg_hours_waiting) for r in pending_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析平台素材状态失败: {e}")
    
    def analyze_campaign_status_flow(self):
        """分析计划状态流转问题"""
        logger.info("\n🔍 3. 分析计划状态流转问题")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 查询计划状态分布
                campaign_status_query = text("""
                    SELECT status, COUNT(*) as count,
                           MIN(created_at) as oldest_create,
                           MAX(created_at) as latest_create
                    FROM campaigns 
                    GROUP BY status 
                    ORDER BY count DESC
                """)
                
                campaign_results = db.execute(campaign_status_query).fetchall()
                
                logger.info("📊 计划状态分布:")
                total_campaigns = sum(row.count for row in campaign_results)
                
                for row in campaign_results:
                    percentage = (row.count / total_campaigns * 100) if total_campaigns > 0 else 0
                    logger.info(f"  {row.status}: {row.count}个 ({percentage:.1f}%)")
                
                # 查找需要提审但长时间未处理的计划
                appeal_needed_query = text("""
                    SELECT c.status, COUNT(*) as count,
                           AVG(EXTRACT(EPOCH FROM (NOW() - c.created_at))/3600) as avg_hours_waiting
                    FROM campaigns c
                    WHERE c.status IN ('AUDITING', 'REJECTED') 
                        AND (c.first_appeal_at IS NULL OR c.first_appeal_at < NOW() - INTERVAL '24 hours')
                    GROUP BY c.status
                """)
                
                appeal_results = db.execute(appeal_needed_query).fetchall()
                
                if appeal_results:
                    logger.warning("🚨 发现需要提审的计划:")
                    for row in appeal_results:
                        logger.warning(f"  {row.status}: {row.count}个计划，等待 {row.avg_hours_waiting:.1f} 小时")
                
                self.diagnosis_results['campaign_status_issues'] = {
                    'total_campaigns': total_campaigns,
                    'status_distribution': [(r.status, r.count) for r in campaign_results],
                    'appeal_needed': [(r.status, r.count, r.avg_hours_waiting) for r in appeal_results]
                }
                
        except Exception as e:
            logger.error(f"❌ 分析计划状态失败: {e}")
    
    def analyze_workflow_bottlenecks(self):
        """分析工作流瓶颈"""
        logger.info("\n🔍 4. 分析工作流瓶颈")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查账户素材分布
                account_materials_query = text("""
                    SELECT a.account_id_qc, a.account_type, 
                           COUNT(lc.id) as local_creative_count,
                           COUNT(pc.id) as platform_creative_count,
                           COUNT(c.id) as campaign_count,
                           COUNT(CASE WHEN pc.review_status = 'APPROVED' THEN 1 END) as approved_count,
                           COUNT(CASE WHEN c.status = 'AUDITING' THEN 1 END) as auditing_count
                    FROM ad_accounts a
                    LEFT JOIN local_creatives lc ON a.principal_id = lc.principal_id
                    LEFT JOIN platform_creatives pc ON a.id = pc.account_id
                    LEFT JOIN campaigns c ON a.id = c.account_id
                    WHERE a.status = 'active'
                    GROUP BY a.account_id_qc, a.account_type
                    ORDER BY platform_creative_count DESC
                """)
                
                account_results = db.execute(account_materials_query).fetchall()
                
                logger.info("📊 账户素材和计划分布:")
                for row in account_results:
                    if row.platform_creative_count > 0 or row.campaign_count > 0:
                        logger.info(f"  账户 {row.account_id_qc} ({row.account_type}):")
                        logger.info(f"    本地素材: {row.local_creative_count}")
                        logger.info(f"    平台素材: {row.platform_creative_count}")
                        logger.info(f"    已审核素材: {row.approved_count}")
                        logger.info(f"    计划总数: {row.campaign_count}")
                        logger.info(f"    审核中计划: {row.auditing_count}")
                
                # 检查是否有账户达到创建限制
                bottleneck_accounts = []
                for row in account_results:
                    if row.approved_count > 0 and row.campaign_count == 0:
                        bottleneck_accounts.append({
                            'account_id': row.account_id_qc,
                            'account_type': row.account_type,
                            'approved_count': row.approved_count,
                            'issue': '有已审核素材但无计划'
                        })
                    elif row.auditing_count > 10:
                        bottleneck_accounts.append({
                            'account_id': row.account_id_qc,
                            'account_type': row.account_type,
                            'auditing_count': row.auditing_count,
                            'issue': '审核中计划过多'
                        })
                
                if bottleneck_accounts:
                    logger.warning("🚨 发现可能的瓶颈账户:")
                    for account in bottleneck_accounts:
                        logger.warning(f"  账户 {account['account_id']}: {account['issue']}")
                
                self.diagnosis_results['workflow_bottlenecks'] = {
                    'account_distribution': [(r.account_id_qc, r.platform_creative_count, r.campaign_count) for r in account_results],
                    'bottleneck_accounts': bottleneck_accounts
                }
                
        except Exception as e:
            logger.error(f"❌ 分析工作流瓶颈失败: {e}")
    
    def analyze_recent_logs(self):
        """分析最近的日志模式"""
        logger.info("\n🔍 5. 分析最近的日志模式")
        logger.info("=" * 60)
        
        try:
            # 分析今日日志文件
            log_file = f"logs/app_{datetime.now().strftime('%Y-%m-%d')}.log"
            
            if not os.path.exists(log_file):
                logger.warning(f"日志文件不存在: {log_file}")
                return
            
            # 统计关键日志模式
            patterns = {
                'creating_plan_skip': 0,
                'plan_creation_errors': 0,
                'material_status_changes': 0,
                'workflow_interruptions': 0,
                'appeal_operations': 0
            }
            
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if '状态已变更为 creating_plan，跳过' in line:
                        patterns['creating_plan_skip'] += 1
                    elif 'ERROR' in line and ('plan' in line.lower() or 'campaign' in line.lower()):
                        patterns['plan_creation_errors'] += 1
                    elif '状态已变更' in line:
                        patterns['material_status_changes'] += 1
                    elif '逻辑中断' in line or '工作流结束' in line:
                        patterns['workflow_interruptions'] += 1
                    elif '提审' in line or 'appeal' in line.lower():
                        patterns['appeal_operations'] += 1
            
            logger.info("📊 今日日志模式统计:")
            for pattern, count in patterns.items():
                logger.info(f"  {pattern}: {count} 次")
            
            # 特别关注creating_plan跳过的情况
            if patterns['creating_plan_skip'] > 1000:
                logger.warning(f"🚨 creating_plan跳过次数异常高: {patterns['creating_plan_skip']} 次")
                logger.warning("  可能原因: 素材状态更新机制有问题或计划创建逻辑异常")
            
        except Exception as e:
            logger.error(f"❌ 分析日志模式失败: {e}")
    
    def generate_recommendations(self):
        """生成修复建议"""
        logger.info("\n📋 生成系统修复建议")
        logger.info("=" * 60)
        
        recommendations = []
        
        # 基于分析结果生成建议
        platform_issues = self.diagnosis_results.get('platform_creatives_issues', {})
        
        if platform_issues.get('pending_plan_creation'):
            for status, count, hours in platform_issues['pending_plan_creation']:
                if count > 5:
                    recommendations.append({
                        'priority': 'HIGH',
                        'issue': f'{count}个已审核素材等待创建计划',
                        'solution': '检查计划创建工作流，可能需要手动触发',
                        'command': f'检查account_type配置和creative_count设置'
                    })
        
        campaign_issues = self.diagnosis_results.get('campaign_status_issues', {})
        if campaign_issues.get('appeal_needed'):
            for status, count, hours in campaign_issues['appeal_needed']:
                if count > 3:
                    recommendations.append({
                        'priority': 'MEDIUM',
                        'issue': f'{count}个{status}状态计划需要提审',
                        'solution': '检查提审工作流，可能需要手动提审',
                        'command': '运行提审工作流或检查提审配置'
                    })
        
        bottlenecks = self.diagnosis_results.get('workflow_bottlenecks', {})
        if bottlenecks.get('bottleneck_accounts'):
            recommendations.append({
                'priority': 'MEDIUM',
                'issue': f'{len(bottlenecks["bottleneck_accounts"])}个账户存在工作流瓶颈',
                'solution': '检查账户配置和素材分配策略',
                'command': '检查account_type和creative_count配置'
            })
        
        logger.info("🎯 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            priority_icon = "🔴" if rec['priority'] == 'HIGH' else "🟡" if rec['priority'] == 'MEDIUM' else "🟢"
            logger.info(f"  {priority_icon} {i}. {rec['issue']}")
            logger.info(f"     解决方案: {rec['solution']}")
            logger.info(f"     建议操作: {rec['command']}")
        
        self.diagnosis_results['recommendations'] = recommendations
        
        return recommendations
    
    def generate_summary_report(self):
        """生成诊断总结报告"""
        logger.info("\n📋 系统诊断总结报告")
        logger.info("=" * 60)
        
        logger.info("🎯 核心发现:")
        
        # 本地素材问题
        local_issues = self.diagnosis_results.get('local_creatives_issues', {})
        if local_issues:
            logger.info(f"  📊 本地素材总数: {local_issues.get('total_creatives', 0)}")
        
        # 平台素材问题
        platform_issues = self.diagnosis_results.get('platform_creatives_issues', {})
        if platform_issues:
            logger.info(f"  📊 平台素材总数: {platform_issues.get('total_platform', 0)}")
            pending_plan = platform_issues.get('pending_plan_creation', [])
            if pending_plan:
                total_pending = sum(count for _, count, _ in pending_plan)
                logger.warning(f"  🚨 等待创建计划的素材: {total_pending} 个")
        
        # 计划状态问题
        campaign_issues = self.diagnosis_results.get('campaign_status_issues', {})
        if campaign_issues:
            logger.info(f"  📊 计划总数: {campaign_issues.get('total_campaigns', 0)}")
            appeal_needed = campaign_issues.get('appeal_needed', [])
            if appeal_needed:
                total_appeal = sum(count for _, count, _ in appeal_needed)
                logger.warning(f"  🚨 需要提审的计划: {total_appeal} 个")
        
        # 工作流瓶颈
        bottlenecks = self.diagnosis_results.get('workflow_bottlenecks', {})
        if bottlenecks.get('bottleneck_accounts'):
            logger.warning(f"  🚨 瓶颈账户数: {len(bottlenecks['bottleneck_accounts'])}")
        
        logger.info("\n💡 立即行动建议:")
        recommendations = self.diagnosis_results.get('recommendations', [])
        high_priority = [r for r in recommendations if r['priority'] == 'HIGH']
        
        if high_priority:
            logger.info("  🔴 高优先级问题需要立即处理:")
            for rec in high_priority:
                logger.info(f"    - {rec['issue']}")
        else:
            logger.info("  ✅ 未发现高优先级问题")
        
        return self.diagnosis_results

def main():
    """主诊断函数"""
    try:
        analyzer = SystemDiagnosisAnalyzer()
        
        logger.info("🚀 开始千川系统全面诊断 (修正版)")
        logger.info("=" * 60)
        
        # 执行各项诊断
        analyzer.analyze_local_creatives_flow()
        analyzer.analyze_platform_creatives_flow()
        analyzer.analyze_campaign_status_flow()
        analyzer.analyze_workflow_bottlenecks()
        analyzer.analyze_recent_logs()
        
        # 生成建议和报告
        recommendations = analyzer.generate_recommendations()
        summary = analyzer.generate_summary_report()
        
        logger.info(f"\n✅ 系统诊断完成")
        logger.info(f"发现 {len(recommendations)} 个需要处理的问题")
        
        return summary
        
    except Exception as e:
        logger.error(f"❌ 系统诊断失败: {e}")
        return None

if __name__ == "__main__":
    main()
