-- 千川自动化素材唯一性数据库约束
-- =====================================
-- 目的: 从数据库层面彻底防止素材重复使用违规
-- 创建时间: 2025-07-27
-- 基于发现: 155个违规素材，445个重复计划的严重问题

-- 1. 创建素材测试唯一性约束函数
CREATE OR REPLACE FUNCTION check_material_uniqueness_constraint()
RETURNS TRIGGER AS $$
DECLARE
    existing_count INTEGER;
    material_hash TEXT;
    material_filename TEXT;
BEGIN
    -- 获取素材的file_hash和filename
    SELECT lc.file_hash, lc.filename
    INTO material_hash, material_filename
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    WHERE pc.id = NEW.platform_creative_id;
    
    -- 检查该file_hash是否已经在测试账户中有计划
    SELECT COUNT(*)
    INTO existing_count
    FROM campaigns c
    JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
    JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
    JOIN local_creatives lc ON pc.local_creative_id = lc.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE lc.file_hash = material_hash
      AND aa.account_type = 'TEST'
      AND c.id != NEW.campaign_id; -- 排除当前正在插入的记录
    
    -- 如果已存在，抛出异常
    IF existing_count > 0 THEN
        RAISE EXCEPTION '🚨 素材唯一性测试铁律违规: 素材 "%" (hash: %) 已在测试账户中存在计划，禁止重复创建！', 
            material_filename, LEFT(material_hash, 8);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. 创建触发器：在关联表插入时检查唯一性
DROP TRIGGER IF EXISTS trigger_material_uniqueness_check ON campaign_platform_creative_association;
CREATE TRIGGER trigger_material_uniqueness_check
    BEFORE INSERT ON campaign_platform_creative_association
    FOR EACH ROW
    EXECUTE FUNCTION check_material_uniqueness_constraint();

-- 3. 创建素材状态约束函数
CREATE OR REPLACE FUNCTION check_material_status_constraint()
RETURNS TRIGGER AS $$
BEGIN
    -- 检查素材状态转换的合法性
    IF NEW.status = 'testing_pending_review' THEN
        -- 确保素材没有被其他测试计划使用
        IF EXISTS (
            SELECT 1
            FROM campaigns c
            JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
            JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
            JOIN local_creatives lc ON pc.local_creative_id = lc.id
            JOIN ad_accounts aa ON c.account_id = aa.id
            WHERE lc.file_hash = NEW.file_hash
              AND aa.account_type = 'TEST'
              AND lc.id != NEW.id
        ) THEN
            RAISE EXCEPTION '🚨 素材状态约束违规: 相同内容的素材已在测试中，禁止重复测试！';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. 创建素材状态触发器
DROP TRIGGER IF EXISTS trigger_material_status_check ON local_creatives;
CREATE TRIGGER trigger_material_status_check
    BEFORE UPDATE ON local_creatives
    FOR EACH ROW
    EXECUTE FUNCTION check_material_status_constraint();

-- 5. 创建唯一索引（额外保护）
CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_material_test_constraint
ON campaign_platform_creative_association (
    (SELECT lc.file_hash 
     FROM local_creatives lc 
     JOIN platform_creatives pc ON lc.id = pc.local_creative_id 
     WHERE pc.id = platform_creative_id),
    (SELECT aa.account_type 
     FROM ad_accounts aa 
     JOIN campaigns c ON aa.id = c.account_id 
     WHERE c.id = campaign_id)
)
WHERE (SELECT aa.account_type 
       FROM ad_accounts aa 
       JOIN campaigns c ON aa.id = c.account_id 
       WHERE c.id = campaign_id) = 'TEST';

-- 6. 创建违规检测函数
CREATE OR REPLACE FUNCTION detect_material_violations()
RETURNS TABLE(
    violation_type TEXT,
    material_id INTEGER,
    filename TEXT,
    file_hash TEXT,
    plan_count BIGINT,
    plan_ids TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        '素材重复使用违规'::TEXT as violation_type,
        lc.id as material_id,
        lc.filename,
        lc.file_hash,
        COUNT(DISTINCT c.id) as plan_count,
        STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.id, lc.filename, lc.file_hash
    HAVING COUNT(DISTINCT c.id) > 1
    ORDER BY plan_count DESC;
END;
$$ LANGUAGE plpgsql;

-- 7. 创建合规性检查函数
CREATE OR REPLACE FUNCTION check_system_compliance()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    violation_count BIGINT,
    details TEXT
) AS $$
DECLARE
    violation_count BIGINT;
BEGIN
    -- 检查素材重复使用违规
    SELECT COUNT(*) INTO violation_count
    FROM detect_material_violations();
    
    RETURN QUERY
    SELECT 
        '素材唯一性测试铁律'::TEXT as check_name,
        CASE 
            WHEN violation_count = 0 THEN '✅ 合规'
            ELSE '❌ 违规'
        END as status,
        violation_count,
        CASE 
            WHEN violation_count = 0 THEN '所有素材遵守唯一性测试铁律'
            ELSE violation_count || '个素材违反唯一性测试铁律'
        END as details;
END;
$$ LANGUAGE plpgsql;

-- 8. 创建自动修复函数
CREATE OR REPLACE FUNCTION auto_fix_material_violations()
RETURNS TABLE(
    action TEXT,
    affected_count INTEGER,
    details TEXT
) AS $$
DECLARE
    violation_record RECORD;
    deleted_plans INTEGER := 0;
    updated_materials INTEGER := 0;
BEGIN
    -- 遍历所有违规并修复
    FOR violation_record IN 
        SELECT * FROM detect_material_violations()
    LOOP
        -- 删除除最早计划外的所有重复计划
        WITH plans_to_delete AS (
            SELECT c.id
            FROM campaigns c
            JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
            JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
            JOIN local_creatives lc ON pc.local_creative_id = lc.id
            WHERE lc.file_hash = violation_record.file_hash
            ORDER BY c.created_at DESC
            OFFSET 1  -- 保留最早的计划
        )
        DELETE FROM campaign_platform_creative_association
        WHERE campaign_id IN (SELECT id FROM plans_to_delete);
        
        DELETE FROM campaigns
        WHERE id IN (
            SELECT c.id
            FROM campaigns c
            JOIN platform_creatives pc ON TRUE
            JOIN local_creatives lc ON pc.local_creative_id = lc.id
            WHERE lc.file_hash = violation_record.file_hash
              AND c.created_at > (
                  SELECT MIN(c2.created_at)
                  FROM campaigns c2
                  JOIN campaign_platform_creative_association cpca2 ON c2.id = cpca2.campaign_id
                  JOIN platform_creatives pc2 ON cpca2.platform_creative_id = pc2.id
                  JOIN local_creatives lc2 ON pc2.local_creative_id = lc2.id
                  WHERE lc2.file_hash = violation_record.file_hash
              )
        );
        
        GET DIAGNOSTICS deleted_plans = ROW_COUNT;
        
        -- 更新素材状态
        UPDATE local_creatives
        SET status = 'already_tested'
        WHERE id = violation_record.material_id;
        
        updated_materials := updated_materials + 1;
    END LOOP;
    
    RETURN QUERY
    SELECT 
        '删除重复计划'::TEXT as action,
        deleted_plans as affected_count,
        '删除了违规的重复测试计划'::TEXT as details
    UNION ALL
    SELECT 
        '更新素材状态'::TEXT as action,
        updated_materials as affected_count,
        '更新素材状态为已测试'::TEXT as details;
END;
$$ LANGUAGE plpgsql;

-- 9. 创建日志表记录约束触发
CREATE TABLE IF NOT EXISTS material_constraint_logs (
    id SERIAL PRIMARY KEY,
    event_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event_type TEXT NOT NULL,
    material_hash TEXT,
    material_filename TEXT,
    attempted_action TEXT,
    error_message TEXT,
    user_context TEXT
);

-- 10. 创建约束日志记录函数
CREATE OR REPLACE FUNCTION log_constraint_violation(
    p_event_type TEXT,
    p_material_hash TEXT,
    p_material_filename TEXT,
    p_attempted_action TEXT,
    p_error_message TEXT
) RETURNS VOID AS $$
BEGIN
    INSERT INTO material_constraint_logs (
        event_type,
        material_hash,
        material_filename,
        attempted_action,
        error_message,
        user_context
    ) VALUES (
        p_event_type,
        p_material_hash,
        p_material_filename,
        p_attempted_action,
        p_error_message,
        current_user
    );
END;
$$ LANGUAGE plpgsql;

-- 使用说明
-- ========
-- 1. 执行此脚本创建所有约束和函数
-- 2. 使用 SELECT * FROM check_system_compliance(); 检查合规性
-- 3. 使用 SELECT * FROM detect_material_violations(); 检测违规
-- 4. 使用 SELECT * FROM auto_fix_material_violations(); 自动修复违规
-- 5. 查看 material_constraint_logs 表了解约束触发历史

-- 测试约束是否生效
-- SELECT '约束测试完成，系统已受到数据库层面保护' as status;
