#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提审状态定义统一规范
==================

本文件定义了千川自动化项目中提审模块的统一状态规范，
确保所有模块对状态的理解完全一致，避免状态语义混乱。

版本: v1.0
创建时间: 2025-08-08
维护者: AI Assistant
"""

from enum import Enum
from typing import Dict, List, Optional, Set
from datetime import datetime, timezone


class AppealStatus(Enum):
    """
    提审状态枚举 - 统一定义
    
    重要说明：
    1. 所有模块必须使用此枚举，不得自定义状态值
    2. 状态转换必须遵循定义的转换规则
    3. 状态语义严格按照注释执行，不得随意解释
    """
    
    # === 提审前状态 ===
    NOT_APPEALED = None                         # 未提审（数据库NULL值）
    APPEAL_NEEDED = "appeal_needed"             # 需要提审（明确标识需要人工干预）
    
    # === 提审过程状态 ===
    APPEAL_EXECUTING = "appeal_executing"       # 提审执行中（浏览器操作进行中）
    APPEAL_SUBMITTED = "appeal_submitted"       # 提审已提交（表单提交成功）
    
    # === 提审后状态 ===
    APPEAL_PENDING = "appeal_pending"           # 提审成功，等待千川审核结果
    APPEAL_MONITORING = "appeal_monitoring"     # 申诉进度查询中
    
    # === 终态 ===
    APPEAL_SUCCESS = "appeal_success"           # 申诉成功（千川审核通过）
    APPEAL_FAILED = "appeal_failed"             # 申诉失败（千川审核拒绝）
    APPEAL_TIMEOUT = "appeal_timeout"           # 申诉超时（超过监控时间限制）


class AppealStage(Enum):
    """提审执行阶段枚举"""
    BROWSER_LAUNCHING = "browser_launching"     # 浏览器启动中
    FORM_FILLING = "form_filling"               # 表单填写中
    SUBMISSION_WAITING = "submission_waiting"   # 等待提交确认
    RESULT_VERIFICATION = "result_verification" # 结果验证中
    PROGRESS_MONITORING = "progress_monitoring" # 进度监控中


# === 状态转换规则定义 ===
VALID_STATE_TRANSITIONS: Dict[Optional[AppealStatus], List[AppealStatus]] = {
    # 从未提审状态开始
    None: [AppealStatus.APPEAL_NEEDED, AppealStatus.APPEAL_EXECUTING],

    # 需要提审 -> 开始执行
    AppealStatus.APPEAL_NEEDED: [AppealStatus.APPEAL_EXECUTING],
    
    # 执行中 -> 提交成功/失败
    AppealStatus.APPEAL_EXECUTING: [
        AppealStatus.APPEAL_SUBMITTED, 
        AppealStatus.APPEAL_FAILED
    ],
    
    # 已提交 -> 等待审核/失败
    AppealStatus.APPEAL_SUBMITTED: [
        AppealStatus.APPEAL_PENDING, 
        AppealStatus.APPEAL_FAILED
    ],
    
    # 等待审核 -> 开始监控/超时
    AppealStatus.APPEAL_PENDING: [
        AppealStatus.APPEAL_MONITORING, 
        AppealStatus.APPEAL_TIMEOUT
    ],
    
    # 监控中 -> 成功/失败/超时
    AppealStatus.APPEAL_MONITORING: [
        AppealStatus.APPEAL_SUCCESS, 
        AppealStatus.APPEAL_FAILED, 
        AppealStatus.APPEAL_TIMEOUT
    ],
    
    # 终态不能转换
    AppealStatus.APPEAL_SUCCESS: [],
    AppealStatus.APPEAL_FAILED: [],
    AppealStatus.APPEAL_TIMEOUT: [],
}


# === 状态分类定义 ===
PRE_APPEAL_STATES: Set[Optional[AppealStatus]] = {
    None, 
    AppealStatus.APPEAL_NEEDED
}

PROCESSING_STATES: Set[AppealStatus] = {
    AppealStatus.APPEAL_EXECUTING, 
    AppealStatus.APPEAL_SUBMITTED
}

POST_APPEAL_STATES: Set[AppealStatus] = {
    AppealStatus.APPEAL_PENDING, 
    AppealStatus.APPEAL_MONITORING
}

TERMINAL_STATES: Set[AppealStatus] = {
    AppealStatus.APPEAL_SUCCESS, 
    AppealStatus.APPEAL_FAILED, 
    AppealStatus.APPEAL_TIMEOUT
}


# === 状态中文翻译 ===
STATUS_TRANSLATIONS: Dict[Optional[str], str] = {
    None: "未提审",
    "appeal_needed": "需要提审",
    "appeal_executing": "提审执行中",
    "appeal_submitted": "提审已提交",
    "appeal_pending": "等待审核结果",
    "appeal_monitoring": "申诉进度查询中",
    "appeal_success": "申诉成功",
    "appeal_failed": "申诉失败",
    "appeal_timeout": "申诉超时",
}


# === 字段一致性规则 ===
class StateConsistencyRules:
    """状态一致性规则定义"""
    
    @staticmethod
    def validate_appeal_pending_state(appeal_status: str, first_appeal_at: Optional[datetime], 
                                    appeal_attempt_count: Optional[int]) -> List[str]:
        """验证appeal_pending状态的一致性"""
        issues = []
        
        if appeal_status == AppealStatus.APPEAL_PENDING.value:
            if not first_appeal_at:
                issues.append("appeal_pending状态必须有first_appeal_at时间")
            
            if not appeal_attempt_count or appeal_attempt_count == 0:
                issues.append("appeal_pending状态必须有appeal_attempt_count > 0")
        
        return issues
    
    @staticmethod
    def validate_first_appeal_consistency(first_appeal_at: Optional[datetime], 
                                        appeal_status: Optional[str]) -> List[str]:
        """验证首次提审时间的一致性"""
        issues = []
        
        if first_appeal_at and not appeal_status:
            issues.append("有first_appeal_at时间但appeal_status为空")
        
        if first_appeal_at and appeal_status in [None, AppealStatus.APPEAL_NEEDED.value]:
            issues.append("有first_appeal_at时间但状态显示未提审")
        
        return issues


# === 工具函数 ===
def is_valid_transition(from_status: Optional[str], to_status: str) -> bool:
    """检查状态转换是否合法"""
    try:
        from_enum = AppealStatus(from_status) if from_status else None
        to_enum = AppealStatus(to_status)
        
        allowed_transitions = VALID_STATE_TRANSITIONS.get(from_enum, [])
        return to_enum in allowed_transitions
    except ValueError:
        return False


def get_status_display_name(status: Optional[str]) -> str:
    """获取状态的中文显示名称"""
    return STATUS_TRANSLATIONS.get(status, f"未知状态({status})")


def is_terminal_state(status: Optional[str]) -> bool:
    """判断是否为终态"""
    try:
        if not status:
            return False
        status_enum = AppealStatus(status)
        return status_enum in TERMINAL_STATES
    except ValueError:
        return False


def needs_appeal(status: Optional[str]) -> bool:
    """判断是否需要提审"""
    try:
        status_enum = AppealStatus(status) if status else None
        return status_enum in PRE_APPEAL_STATES
    except ValueError:
        return False


def has_been_appealed(status: Optional[str]) -> bool:
    """判断是否已经提审过"""
    try:
        if not status:
            return False
        status_enum = AppealStatus(status)
        return status_enum not in PRE_APPEAL_STATES
    except ValueError:
        return False


# === 状态修复工具 ===
def get_correct_status_for_inconsistent_data(appeal_status: Optional[str], 
                                           first_appeal_at: Optional[datetime]) -> Optional[str]:
    """
    为不一致的数据获取正确的状态值
    
    Args:
        appeal_status: 当前状态
        first_appeal_at: 首次提审时间
    
    Returns:
        修正后的正确状态值
    """
    # 情况1：状态为appeal_pending但没有提审时间 -> 重置为需要提审
    if appeal_status == AppealStatus.APPEAL_PENDING.value and not first_appeal_at:
        return None  # 重置为未提审状态
    
    # 情况2：有提审时间但状态为空 -> 设置为已提审状态
    if first_appeal_at and not appeal_status:
        return AppealStatus.APPEAL_PENDING.value
    
    # 其他情况保持原状态
    return appeal_status
