#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 第一阶段紧急修复实施方案
清理条件: 成为项目核心修复工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import LocalCreative
from sqlalchemy import text


class Phase1EmergencyFixes:
    """第一阶段紧急修复实施器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
    
    def run_emergency_fixes(self) -> Dict[str, Any]:
        """运行紧急修复"""
        logger.info("🚨 开始第一阶段紧急修复")
        logger.info("="*80)
        
        fix_results = {
            'timestamp': datetime.now(timezone.utc),
            'processing_cleanup': self._fix_processing_backlog(),
            'state_manager_upgrade': self._implement_atomic_state_manager(),
            'browser_pool_setup': self._setup_browser_process_pool(),
            'celery_lifecycle_fix': self._fix_celery_lifecycle(),
            'fixes_applied': self.fixes_applied,
            'issues_found': self.issues_found
        }
        
        self._generate_fix_report(fix_results)
        return fix_results
    
    def _fix_processing_backlog(self) -> Dict[str, Any]:
        """修复processing状态积压"""
        logger.info("🔧 修复processing状态积压...")
        
        with SessionLocal() as db:
            # 查找长期卡住的processing状态
            stuck_threshold = datetime.now(timezone.utc) - timedelta(hours=1)
            
            stuck_creatives = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PROCESSING.value,
                LocalCreative.created_at < stuck_threshold
            ).all()
            
            logger.info(f"   发现 {len(stuck_creatives)} 个卡住的processing状态")
            
            # 重置为pending_upload状态，允许重新处理
            reset_count = 0
            for creative in stuck_creatives:
                try:
                    creative.status = MaterialStatus.PENDING_UPLOAD.value
                    creative.updated_at = datetime.now(timezone.utc)
                    reset_count += 1
                except Exception as e:
                    logger.error(f"重置素材 {creative.id} 失败: {e}")
            
            db.commit()
            
            self.fixes_applied.append(f"重置了 {reset_count} 个卡住的processing状态")
            logger.success(f"✅ 重置了 {reset_count} 个卡住的processing状态")
            
            return {
                'stuck_count': len(stuck_creatives),
                'reset_count': reset_count,
                'success': reset_count > 0
            }
    
    def _implement_atomic_state_manager(self) -> Dict[str, Any]:
        """实现原子状态管理器"""
        logger.info("🔧 实现原子状态管理器...")
        
        # 创建原子状态管理器代码
        atomic_manager_code = '''
class AtomicStateManager:
    """原子状态管理器 - 确保状态转换的原子性"""
    
    def __init__(self, db_session):
        self.db = db_session
    
    @contextmanager
    def atomic_state_transition(self, creative_id: int, from_status: str, to_status: str):
        """原子状态转换上下文管理器"""
        creative = None
        try:
            # 开始事务
            creative = self.db.query(LocalCreative).filter(
                LocalCreative.id == creative_id,
                LocalCreative.status == from_status
            ).with_for_update().first()
            
            if not creative:
                raise ValueError(f"素材 {creative_id} 不存在或状态不匹配")
            
            # 更新状态
            creative.status = to_status
            creative.updated_at = datetime.now(timezone.utc)
            
            yield creative
            
            # 提交事务
            self.db.commit()
            logger.debug(f"状态转换成功: {creative_id} {from_status} → {to_status}")
            
        except Exception as e:
            # 回滚事务
            self.db.rollback()
            if creative:
                creative.status = from_status  # 恢复原状态
            logger.error(f"状态转换失败: {creative_id} {from_status} → {to_status}: {e}")
            raise
    
    def safe_dispatch_upload_task(self, creative_id: int, account_id: int, file_path: str, principal_name: str):
        """安全派发上传任务"""
        with self.atomic_state_transition(creative_id, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value) as creative:
            # 在状态锁定后派发任务
            from qianchuan_aw.workflows.tasks import upload_single_video
            upload_single_video.delay(creative_id, account_id, file_path, principal_name)
            logger.info(f"安全派发上传任务: {os.path.basename(file_path)}")
'''
        
        # 保存原子状态管理器代码
        manager_file = os.path.join(project_root, 'src/qianchuan_aw/utils/atomic_state_manager.py')
        try:
            with open(manager_file, 'w', encoding='utf-8') as f:
                f.write('#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n')
                f.write('"""\n原子状态管理器\n确保状态转换的原子性，防止状态不一致\n"""\n\n')
                f.write('import os\nfrom datetime import datetime, timezone\n')
                f.write('from contextlib import contextmanager\n')
                f.write('from qianchuan_aw.database.models import LocalCreative\n')
                f.write('from qianchuan_aw.utils.logger import logger\n\n')
                f.write(atomic_manager_code)
            
            self.fixes_applied.append("创建了原子状态管理器")
            logger.success("✅ 创建了原子状态管理器")
            
            return {'success': True, 'file_created': manager_file}
            
        except Exception as e:
            logger.error(f"创建原子状态管理器失败: {e}")
            self.issues_found.append(f"创建原子状态管理器失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _setup_browser_process_pool(self) -> Dict[str, Any]:
        """设置浏览器进程池"""
        logger.info("🔧 设置浏览器进程池...")
        
        # 创建浏览器进程池代码
        browser_pool_code = '''
import asyncio
import threading
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from playwright.async_api import async_playwright, Browser, BrowserContext
from qianchuan_aw.utils.logger import logger


class BrowserProcessPool:
    """浏览器进程池 - 统一管理浏览器进程，提升效率"""
    
    def __init__(self, min_instances: int = 2, max_instances: int = 5, idle_timeout: int = 300):
        self.min_instances = min_instances
        self.max_instances = max_instances
        self.idle_timeout = idle_timeout
        
        self.browsers: List[Dict[str, Any]] = []
        self.available_browsers: List[int] = []
        self.busy_browsers: List[int] = []
        self.lock = threading.Lock()
        self.playwright = None
        self.initialized = False
    
    async def initialize(self):
        """初始化浏览器进程池"""
        if self.initialized:
            return
        
        logger.info(f"🚀 初始化浏览器进程池 (min={self.min_instances}, max={self.max_instances})")
        
        self.playwright = await async_playwright().start()
        
        # 创建最小数量的浏览器实例
        for i in range(self.min_instances):
            await self._create_browser_instance()
        
        self.initialized = True
        logger.success(f"✅ 浏览器进程池初始化完成，创建了 {len(self.browsers)} 个实例")
    
    async def _create_browser_instance(self) -> int:
        """创建浏览器实例"""
        browser = await self.playwright.chromium.launch(
            headless=True,
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-extensions',
                '--memory-pressure-off'
            ]
        )
        
        browser_info = {
            'id': len(self.browsers),
            'browser': browser,
            'created_at': datetime.now(),
            'last_used': datetime.now(),
            'usage_count': 0
        }
        
        self.browsers.append(browser_info)
        self.available_browsers.append(browser_info['id'])
        
        logger.debug(f"创建浏览器实例 #{browser_info['id']}")
        return browser_info['id']
    
    @asynccontextmanager
    async def get_browser_context(self, principal_name: str):
        """获取浏览器上下文"""
        browser_id = await self._acquire_browser()
        
        try:
            browser_info = self.browsers[browser_id]
            browser = browser_info['browser']
            
            # 创建新的上下文
            context = await browser.new_context()
            
            # 加载cookies等初始化操作
            await self._setup_browser_context(context, principal_name)
            
            # 更新使用统计
            browser_info['last_used'] = datetime.now()
            browser_info['usage_count'] += 1
            
            yield context
            
        finally:
            await context.close()
            await self._release_browser(browser_id)
    
    async def _acquire_browser(self) -> int:
        """获取可用的浏览器实例"""
        with self.lock:
            if self.available_browsers:
                browser_id = self.available_browsers.pop(0)
                self.busy_browsers.append(browser_id)
                return browser_id
            
            # 如果没有可用实例且未达到最大数量，创建新实例
            if len(self.browsers) < self.max_instances:
                browser_id = await self._create_browser_instance()
                self.available_browsers.remove(browser_id)
                self.busy_browsers.append(browser_id)
                return browser_id
            
            # 等待可用实例
            # 这里可以实现更复杂的等待逻辑
            raise RuntimeError("浏览器进程池已满，请稍后重试")
    
    async def _release_browser(self, browser_id: int):
        """释放浏览器实例"""
        with self.lock:
            if browser_id in self.busy_browsers:
                self.busy_browsers.remove(browser_id)
                self.available_browsers.append(browser_id)
    
    async def _setup_browser_context(self, context: BrowserContext, principal_name: str):
        """设置浏览器上下文"""
        # 这里实现cookies加载等初始化逻辑
        pass
    
    async def cleanup(self):
        """清理浏览器进程池"""
        logger.info("🧹 清理浏览器进程池...")
        
        for browser_info in self.browsers:
            try:
                await browser_info['browser'].close()
            except Exception as e:
                logger.error(f"关闭浏览器实例失败: {e}")
        
        if self.playwright:
            await self.playwright.stop()
        
        self.browsers.clear()
        self.available_browsers.clear()
        self.busy_browsers.clear()
        self.initialized = False
        
        logger.success("✅ 浏览器进程池清理完成")


# 全局浏览器进程池实例
_browser_pool = None

async def get_browser_pool() -> BrowserProcessPool:
    """获取全局浏览器进程池实例"""
    global _browser_pool
    
    if _browser_pool is None:
        _browser_pool = BrowserProcessPool()
        await _browser_pool.initialize()
    
    return _browser_pool
'''
        
        # 保存浏览器进程池代码
        pool_file = os.path.join(project_root, 'src/qianchuan_aw/utils/browser_process_pool.py')
        try:
            with open(pool_file, 'w', encoding='utf-8') as f:
                f.write('#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n')
                f.write('"""\n浏览器进程池\n统一管理浏览器进程，提升上传效率\n"""\n\n')
                f.write(browser_pool_code)
            
            self.fixes_applied.append("创建了浏览器进程池")
            logger.success("✅ 创建了浏览器进程池")
            
            return {'success': True, 'file_created': pool_file}
            
        except Exception as e:
            logger.error(f"创建浏览器进程池失败: {e}")
            self.issues_found.append(f"创建浏览器进程池失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _fix_celery_lifecycle(self) -> Dict[str, Any]:
        """修复Celery生命周期问题"""
        logger.info("🔧 修复Celery生命周期问题...")
        
        # 创建Celery生命周期管理器
        lifecycle_code = '''
import signal
import sys
import time
import threading
from typing import Optional
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.unified_material_status import MaterialStatus


class CeleryLifecycleManager:
    """Celery生命周期管理器"""
    
    def __init__(self):
        self.beat_running = True
        self.worker_should_stop = False
        self.heartbeat_thread: Optional[threading.Thread] = None
        self.last_heartbeat = time.time()
    
    def start_heartbeat_monitor(self):
        """启动心跳监控"""
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_monitor, daemon=True)
        self.heartbeat_thread.start()
        logger.info("🔄 启动Celery心跳监控")
    
    def _heartbeat_monitor(self):
        """心跳监控线程"""
        while not self.worker_should_stop:
            try:
                # 检查Beat是否还在运行
                current_time = time.time()
                if current_time - self.last_heartbeat > 300:  # 5分钟无心跳
                    logger.warning("⚠️ 检测到Beat可能已停止，准备关闭Worker")
                    self.initiate_graceful_shutdown()
                    break
                
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"心跳监控异常: {e}")
    
    def update_heartbeat(self):
        """更新心跳时间"""
        self.last_heartbeat = time.time()
    
    def initiate_graceful_shutdown(self):
        """启动优雅关闭"""
        logger.info("🛑 启动Celery Worker优雅关闭...")
        
        self.worker_should_stop = True
        
        # 发送SIGTERM信号给当前进程
        try:
            import os
            os.kill(os.getpid(), signal.SIGTERM)
        except Exception as e:
            logger.error(f"发送关闭信号失败: {e}")
            sys.exit(0)
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始优雅关闭...")
            self.initiate_graceful_shutdown()
        
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)


# 全局生命周期管理器
_lifecycle_manager = None

def get_lifecycle_manager() -> CeleryLifecycleManager:
    """获取生命周期管理器"""
    global _lifecycle_manager
    
    if _lifecycle_manager is None:
        _lifecycle_manager = CeleryLifecycleManager()
        _lifecycle_manager.setup_signal_handlers()
        _lifecycle_manager.start_heartbeat_monitor()
    
    return _lifecycle_manager
'''
        
        # 保存生命周期管理器代码
        lifecycle_file = os.path.join(project_root, 'src/qianchuan_aw/utils/celery_lifecycle_manager.py')
        try:
            with open(lifecycle_file, 'w', encoding='utf-8') as f:
                f.write('#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n')
                f.write('"""\nCelery生命周期管理器\n解决Beat停止后Worker仍运行的问题\n"""\n\n')
                f.write(lifecycle_code)
            
            self.fixes_applied.append("创建了Celery生命周期管理器")
            logger.success("✅ 创建了Celery生命周期管理器")
            
            return {'success': True, 'file_created': lifecycle_file}
            
        except Exception as e:
            logger.error(f"创建Celery生命周期管理器失败: {e}")
            self.issues_found.append(f"创建Celery生命周期管理器失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _generate_fix_report(self, results: Dict[str, Any]):
        """生成修复报告"""
        logger.info("\n📋 第一阶段紧急修复报告")
        logger.info("="*80)
        
        # 修复统计
        total_fixes = len(self.fixes_applied)
        total_issues = len(self.issues_found)
        
        logger.info(f"🔧 应用的修复: {total_fixes} 个")
        for fix in self.fixes_applied:
            logger.info(f"   ✅ {fix}")
        
        if total_issues > 0:
            logger.info(f"❌ 发现的问题: {total_issues} 个")
            for issue in self.issues_found:
                logger.info(f"   ❌ {issue}")
        
        # 处理状态修复
        processing_fix = results['processing_cleanup']
        if processing_fix['success']:
            logger.success(f"✅ 处理状态修复: 重置了 {processing_fix['reset_count']} 个卡住的素材")
        
        # 下一步建议
        logger.info("\n💡 下一步建议:")
        logger.info("   1. 重启Celery Worker和Beat进程")
        logger.info("   2. 监控processing状态是否还会积压")
        logger.info("   3. 测试浏览器进程池是否正常工作")
        logger.info("   4. 开始实施第二阶段优化")


def main():
    """主函数"""
    fixer = Phase1EmergencyFixes()
    results = fixer.run_emergency_fixes()
    
    success_count = len(fixer.fixes_applied)
    issue_count = len(fixer.issues_found)
    
    if success_count > issue_count:
        logger.success(f"✅ 第一阶段紧急修复基本成功: {success_count} 个修复, {issue_count} 个问题")
        return 0
    else:
        logger.error(f"❌ 第一阶段紧急修复需要关注: {success_count} 个修复, {issue_count} 个问题")
        return 1


if __name__ == "__main__":
    exit(main())
