
-- 分步执行版本（更安全）

-- 1. 查看违规情况
SELECT 
    lc.filename,
    COUNT(DISTINCT c.id) as plan_count,
    STRING_AGG(DISTINCT c.campaign_id_qc, ', ') as plan_ids
FROM local_creatives lc
JOIN platform_creatives pc ON lc.id = pc.local_creative_id
JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
JOIN campaigns c ON cpca.campaign_id = c.id
JOIN ad_accounts aa ON c.account_id = aa.id
WHERE aa.account_type = 'TEST'
GROUP BY lc.filename
HAVING COUNT(DISTINCT c.id) > 1
ORDER BY plan_count DESC
LIMIT 10;

-- 2. 对于每个违规素材，手动执行以下步骤：
-- 2a. 查找该素材的所有计划（按创建时间排序）
-- 2b. 删除除最早计划外的所有计划
-- 2c. 更新素材状态为已测试

-- 示例（针对特定素材）：
-- DELETE FROM campaign_platform_creative_association WHERE campaign_id IN (计划ID列表);
-- DELETE FROM campaigns WHERE id IN (计划ID列表);
-- UPDATE local_creatives SET status = 'already_tested' WHERE filename = '素材文件名';
