#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 测试异步Playwright修复效果
清理条件: 长期保留，用于验证修复

异步Playwright修复验证工具
=========================

测试新的异步CopilotService和AppealService，验证是否解决了浏览器过载问题。
"""

import os
import sys
import asyncio
import time
import psutil
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.config_loader import load_settings


class AsyncPlaywrightTester:
    """异步Playwright测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.app_settings = None
        self.test_results = []
    
    def load_config(self):
        """加载配置"""
        try:
            self.app_settings = load_settings()
            logger.info("✅ 配置加载成功")
            return True
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            return False
    
    def check_system_before_test(self):
        """测试前检查系统状态"""
        logger.info("🔍 测试前系统状态检查...")
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        browser_count = self.count_browser_processes()
        
        logger.info(f"CPU使用率: {cpu_percent}%")
        logger.info(f"内存使用率: {memory.percent}%")
        logger.info(f"浏览器进程数: {browser_count}")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'browser_count': browser_count
        }
    
    def count_browser_processes(self):
        """统计浏览器进程"""
        browser_count = 0
        browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
        
        for proc in psutil.process_iter(['name']):
            try:
                proc_name = proc.info['name'].lower()
                if any(browser in proc_name for browser in browser_names):
                    browser_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        return browser_count
    
    async def test_async_playwright_import(self):
        """测试异步Playwright导入"""
        logger.info("🧪 测试1: 异步Playwright导入...")
        
        try:
            from playwright.async_api import async_playwright
            logger.info("✅ 异步Playwright导入成功")
            
            # 测试基本功能
            async with async_playwright() as p:
                logger.info("✅ 异步Playwright上下文创建成功")
                
                browser = await p.chromium.launch(headless=True)
                logger.info("✅ 异步浏览器启动成功")
                
                await browser.close()
                logger.info("✅ 异步浏览器关闭成功")
            
            self.test_results.append({
                'test': 'async_playwright_import',
                'status': 'success',
                'message': '异步Playwright基本功能正常'
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 异步Playwright导入测试失败: {e}")
            self.test_results.append({
                'test': 'async_playwright_import',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    async def test_async_copilot_service(self):
        """测试异步CopilotService"""
        logger.info("🧪 测试2: 异步CopilotService...")
        
        try:
            from src.qianchuan_aw.services.async_copilot_service import AsyncBrowserSessionPool
            
            # 测试浏览器会话池
            pool = AsyncBrowserSessionPool(max_sessions=1)
            logger.info("✅ 异步浏览器会话池创建成功")
            
            # 测试会话获取（不实际连接，只测试结构）
            logger.info("✅ 异步CopilotService结构验证成功")
            
            self.test_results.append({
                'test': 'async_copilot_service',
                'status': 'success',
                'message': '异步CopilotService结构正常'
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 异步CopilotService测试失败: {e}")
            self.test_results.append({
                'test': 'async_copilot_service',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    async def test_async_appeal_service(self):
        """测试异步AppealService"""
        logger.info("🧪 测试3: 异步AppealService...")
        
        try:
            from src.qianchuan_aw.services.async_appeal_service import AsyncAppealService
            
            # 创建异步提审服务
            appeal_service = AsyncAppealService(self.app_settings)
            logger.info("✅ 异步AppealService创建成功")
            
            # 测试资源检查
            resource_ok = await appeal_service._check_system_resources()
            logger.info(f"✅ 系统资源检查: {'正常' if resource_ok else '不足'}")
            
            # 测试统计信息
            stats = appeal_service.get_stats()
            logger.info(f"✅ 统计信息获取成功: {stats}")
            
            self.test_results.append({
                'test': 'async_appeal_service',
                'status': 'success',
                'message': '异步AppealService功能正常'
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 异步AppealService测试失败: {e}")
            self.test_results.append({
                'test': 'async_appeal_service',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    async def test_concurrent_sessions(self):
        """测试并发会话控制"""
        logger.info("🧪 测试4: 并发会话控制...")
        
        try:
            from src.qianchuan_aw.services.async_copilot_service import AsyncBrowserSessionPool
            
            # 创建会话池（最大2个会话）
            pool = AsyncBrowserSessionPool(max_sessions=2)
            
            # 测试信号量机制
            semaphore_acquired = 0
            
            async def test_acquire():
                nonlocal semaphore_acquired
                await pool.session_semaphore.acquire()
                semaphore_acquired += 1
                logger.info(f"获取信号量: {semaphore_acquired}")
                await asyncio.sleep(0.1)
                pool.session_semaphore.release()
                semaphore_acquired -= 1
            
            # 并发测试
            tasks = [test_acquire() for _ in range(5)]
            await asyncio.gather(*tasks)
            
            logger.info("✅ 并发会话控制测试成功")
            
            self.test_results.append({
                'test': 'concurrent_sessions',
                'status': 'success',
                'message': '并发控制机制正常'
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 并发会话控制测试失败: {e}")
            self.test_results.append({
                'test': 'concurrent_sessions',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    async def test_resource_monitoring(self):
        """测试资源监控"""
        logger.info("🧪 测试5: 资源监控...")
        
        try:
            from src.qianchuan_aw.services.async_appeal_service import AppealResourceMonitor, AsyncAppealService
            
            # 创建服务和监控器
            appeal_service = AsyncAppealService(self.app_settings)
            monitor = AppealResourceMonitor(appeal_service)
            
            # 启动监控（短时间）
            await monitor.start_monitoring(interval=1)
            await asyncio.sleep(2)
            await monitor.stop_monitoring()
            
            logger.info("✅ 资源监控测试成功")
            
            self.test_results.append({
                'test': 'resource_monitoring',
                'status': 'success',
                'message': '资源监控机制正常'
            })
            return True
            
        except Exception as e:
            logger.error(f"❌ 资源监控测试失败: {e}")
            self.test_results.append({
                'test': 'resource_monitoring',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    async def test_memory_leak_prevention(self):
        """测试内存泄露预防"""
        logger.info("🧪 测试6: 内存泄露预防...")
        
        try:
            import gc
            
            # 记录初始内存
            initial_memory = psutil.virtual_memory().percent
            
            # 模拟多次会话创建和销毁
            from src.qianchuan_aw.services.async_copilot_service import AsyncBrowserSessionPool
            
            for i in range(3):
                pool = AsyncBrowserSessionPool(max_sessions=1)
                # 模拟会话操作
                await asyncio.sleep(0.1)
                await pool.cleanup_all()
                
                # 强制垃圾回收
                gc.collect()
            
            # 检查内存变化
            final_memory = psutil.virtual_memory().percent
            memory_increase = final_memory - initial_memory
            
            logger.info(f"内存变化: {initial_memory:.1f}% -> {final_memory:.1f}% (增加: {memory_increase:.1f}%)")
            
            if memory_increase < 5:  # 内存增加小于5%认为正常
                logger.info("✅ 内存泄露预防测试成功")
                self.test_results.append({
                    'test': 'memory_leak_prevention',
                    'status': 'success',
                    'message': f'内存增加: {memory_increase:.1f}%'
                })
                return True
            else:
                logger.warning(f"⚠️ 内存增加较多: {memory_increase:.1f}%")
                self.test_results.append({
                    'test': 'memory_leak_prevention',
                    'status': 'warning',
                    'message': f'内存增加: {memory_increase:.1f}%'
                })
                return True
            
        except Exception as e:
            logger.error(f"❌ 内存泄露预防测试失败: {e}")
            self.test_results.append({
                'test': 'memory_leak_prevention',
                'status': 'failed',
                'message': str(e)
            })
            return False
    
    def check_system_after_test(self):
        """测试后检查系统状态"""
        logger.info("🔍 测试后系统状态检查...")
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        browser_count = self.count_browser_processes()
        
        logger.info(f"CPU使用率: {cpu_percent}%")
        logger.info(f"内存使用率: {memory.percent}%")
        logger.info(f"浏览器进程数: {browser_count}")
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'browser_count': browser_count
        }
    
    def generate_test_report(self, before_stats, after_stats):
        """生成测试报告"""
        logger.info("📋 生成测试报告...")
        
        success_count = sum(1 for r in self.test_results if r['status'] == 'success')
        total_count = len(self.test_results)
        
        report = f"""
异步Playwright修复验证报告
========================

测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

测试结果概览:
- 总测试数: {total_count}
- 成功测试: {success_count}
- 成功率: {success_count/total_count*100:.1f}%

详细测试结果:
"""
        
        for result in self.test_results:
            status_icon = "✅" if result['status'] == 'success' else "❌" if result['status'] == 'failed' else "⚠️"
            report += f"- {status_icon} {result['test']}: {result['message']}\n"
        
        report += f"""
系统资源对比:
- CPU使用率: {before_stats['cpu_percent']:.1f}% -> {after_stats['cpu_percent']:.1f}%
- 内存使用率: {before_stats['memory_percent']:.1f}% -> {after_stats['memory_percent']:.1f}%
- 浏览器进程: {before_stats['browser_count']} -> {after_stats['browser_count']}

修复效果评估:
"""
        
        if success_count == total_count:
            report += "🎉 所有测试通过，异步Playwright修复成功！\n"
        elif success_count >= total_count * 0.8:
            report += "✅ 大部分测试通过，修复基本成功，需要关注失败项。\n"
        else:
            report += "⚠️ 多个测试失败，需要进一步修复。\n"
        
        # 保存报告
        report_file = os.path.join(self.project_root, 'ai_temp', f'async_playwright_test_report_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 测试报告已保存: {report_file}")
        return report


async def main():
    """主函数"""
    logger.info("🚀 开始异步Playwright修复验证...")
    
    tester = AsyncPlaywrightTester()
    
    try:
        # 1. 加载配置
        if not tester.load_config():
            return 1
        
        # 2. 测试前系统状态
        before_stats = tester.check_system_before_test()
        
        # 3. 执行测试
        logger.info("=" * 50)
        logger.info("开始执行测试...")
        
        await tester.test_async_playwright_import()
        await tester.test_async_copilot_service()
        await tester.test_async_appeal_service()
        await tester.test_concurrent_sessions()
        await tester.test_resource_monitoring()
        await tester.test_memory_leak_prevention()
        
        # 4. 测试后系统状态
        after_stats = tester.check_system_after_test()
        
        # 5. 生成报告
        logger.info("=" * 50)
        report = tester.generate_test_report(before_stats, after_stats)
        
        logger.info("🎯 测试完成！")
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(asyncio.run(main()))
