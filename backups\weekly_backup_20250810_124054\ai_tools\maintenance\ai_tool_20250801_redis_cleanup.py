"""
AI生成文件信息
================
文件类型: Redis清理工具
生命周期: 永久保留
创建目的: 清理Redis中的残留Celery任务和过期数据
清理条件: 作为维护工具长期保留
"""

import redis
import json
import sys
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger


class RedisCeleryCleanup:
    """Redis Celery任务清理工具"""
    
    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, decode_responses=True)
        self.cleaned_tasks = 0
        self.cleaned_results = 0
        self.cleaned_schedules = 0
        
    def test_connection(self) -> bool:
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            logger.info("✅ Redis连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ Redis连接失败: {e}")
            return False
    
    def analyze_redis_data(self) -> Dict[str, Any]:
        """分析Redis中的数据"""
        logger.info("🔍 分析Redis中的Celery数据...")
        
        analysis = {
            'total_keys': 0,
            'celery_keys': 0,
            'task_queues': [],
            'result_keys': 0,
            'schedule_keys': 0,
            'g_path_tasks': 0
        }
        
        try:
            # 获取所有键
            all_keys = self.redis_client.keys('*')
            analysis['total_keys'] = len(all_keys)
            
            # 分析Celery相关键
            celery_patterns = ['celery-*', '*celery*', 'unacked*', '_kombu*']
            for pattern in celery_patterns:
                keys = self.redis_client.keys(pattern)
                analysis['celery_keys'] += len(keys)
            
            # 分析任务队列
            queue_keys = self.redis_client.keys('celery')
            if queue_keys:
                queue_length = self.redis_client.llen('celery')
                analysis['task_queues'].append(('celery', queue_length))
            
            # 分析结果键
            result_keys = self.redis_client.keys('celery-task-meta-*')
            analysis['result_keys'] = len(result_keys)
            
            # 分析调度键
            schedule_keys = self.redis_client.keys('*beat*')
            analysis['schedule_keys'] = len(schedule_keys)
            
            # 检查G盘路径任务
            analysis['g_path_tasks'] = self._count_g_path_tasks()
            
            logger.info(f"📊 Redis数据分析结果:")
            logger.info(f"   总键数: {analysis['total_keys']}")
            logger.info(f"   Celery键数: {analysis['celery_keys']}")
            logger.info(f"   任务队列: {analysis['task_queues']}")
            logger.info(f"   结果键数: {analysis['result_keys']}")
            logger.info(f"   调度键数: {analysis['schedule_keys']}")
            logger.info(f"   G盘路径任务: {analysis['g_path_tasks']}")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Redis数据分析失败: {e}")
            return analysis
    
    def _count_g_path_tasks(self) -> int:
        """统计包含G盘路径的任务"""
        g_path_count = 0
        
        try:
            # 检查任务队列
            queue_length = self.redis_client.llen('celery')
            if queue_length > 0:
                tasks = self.redis_client.lrange('celery', 0, -1)
                for task_json in tasks:
                    try:
                        task_data = json.loads(task_json)
                        task_str = json.dumps(task_data)
                        if 'G:/' in task_str or 'G:\\' in task_str:
                            g_path_count += 1
                    except:
                        continue
            
            # 检查结果键
            result_keys = self.redis_client.keys('celery-task-meta-*')
            for key in result_keys[:100]:  # 限制检查数量
                try:
                    result_data = self.redis_client.get(key)
                    if result_data and ('G:/' in result_data or 'G:\\' in result_data):
                        g_path_count += 1
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"统计G盘路径任务失败: {e}")
        
        return g_path_count
    
    def clean_task_queue(self, queue_name='celery') -> int:
        """清理任务队列中的G盘路径任务"""
        logger.info(f"🧹 清理任务队列: {queue_name}")
        
        cleaned = 0
        try:
            queue_length = self.redis_client.llen(queue_name)
            logger.info(f"队列长度: {queue_length}")
            
            if queue_length == 0:
                return 0
            
            # 获取所有任务
            tasks = self.redis_client.lrange(queue_name, 0, -1)
            
            # 清空队列
            self.redis_client.delete(queue_name)
            
            # 重新添加不包含G盘路径的任务
            valid_tasks = []
            for task_json in tasks:
                try:
                    task_data = json.loads(task_json)
                    task_str = json.dumps(task_data)
                    
                    if 'G:/' in task_str or 'G:\\' in task_str:
                        cleaned += 1
                        logger.debug(f"清理G盘路径任务: {task_data.get('task', 'unknown')}")
                    else:
                        valid_tasks.append(task_json)
                except Exception as e:
                    logger.warning(f"解析任务失败: {e}")
                    cleaned += 1
            
            # 重新添加有效任务
            if valid_tasks:
                for task in valid_tasks:
                    self.redis_client.lpush(queue_name, task)
            
            logger.info(f"✅ 清理了 {cleaned} 个G盘路径任务，保留了 {len(valid_tasks)} 个有效任务")
            
        except Exception as e:
            logger.error(f"清理任务队列失败: {e}")
        
        return cleaned
    
    def clean_result_keys(self) -> int:
        """清理结果键"""
        logger.info("🧹 清理任务结果键...")
        
        cleaned = 0
        try:
            result_keys = self.redis_client.keys('celery-task-meta-*')
            logger.info(f"找到 {len(result_keys)} 个结果键")
            
            for key in result_keys:
                try:
                    result_data = self.redis_client.get(key)
                    if result_data and ('G:/' in result_data or 'G:\\' in result_data):
                        self.redis_client.delete(key)
                        cleaned += 1
                except:
                    # 删除无法解析的键
                    self.redis_client.delete(key)
                    cleaned += 1
            
            logger.info(f"✅ 清理了 {cleaned} 个结果键")
            
        except Exception as e:
            logger.error(f"清理结果键失败: {e}")
        
        return cleaned
    
    def clean_unacked_tasks(self) -> int:
        """清理未确认的任务"""
        logger.info("🧹 清理未确认任务...")
        
        cleaned = 0
        try:
            unacked_keys = self.redis_client.keys('unacked*')
            for key in unacked_keys:
                self.redis_client.delete(key)
                cleaned += 1
            
            logger.info(f"✅ 清理了 {cleaned} 个未确认任务键")
            
        except Exception as e:
            logger.error(f"清理未确认任务失败: {e}")
        
        return cleaned
    
    def clean_all_celery_data(self, confirm=False) -> Dict[str, int]:
        """清理所有Celery数据"""
        if not confirm:
            logger.warning("⚠️ 这将清理所有Celery数据，请使用 confirm=True 确认")
            return {}
        
        logger.warning("🚨 开始清理所有Celery数据...")
        
        results = {
            'task_queues': 0,
            'result_keys': 0,
            'unacked_tasks': 0,
            'other_keys': 0
        }
        
        try:
            # 清理任务队列
            results['task_queues'] = self.clean_task_queue()
            
            # 清理结果键
            results['result_keys'] = self.clean_result_keys()
            
            # 清理未确认任务
            results['unacked_tasks'] = self.clean_unacked_tasks()
            
            # 清理其他Celery键
            other_patterns = ['_kombu*', '*celery*']
            for pattern in other_patterns:
                keys = self.redis_client.keys(pattern)
                for key in keys:
                    if key != 'celery':  # 保留主队列
                        self.redis_client.delete(key)
                        results['other_keys'] += 1
            
            logger.success("🎉 Celery数据清理完成!")
            logger.info(f"清理统计: {results}")
            
        except Exception as e:
            logger.error(f"清理Celery数据失败: {e}")
        
        return results
    
    def restart_celery_services(self):
        """提示重启Celery服务"""
        logger.info("💡 建议执行以下操作:")
        logger.info("1. 停止所有Celery worker和beat进程")
        logger.info("2. 清理celerybeat-schedule.db文件")
        logger.info("3. 重启Celery服务")
        logger.info("")
        logger.info("命令示例:")
        logger.info("   taskkill /f /im celery.exe")
        logger.info("   del celerybeat-schedule.db*")
        logger.info("   python run_celery_worker.py")
        logger.info("   python run_celery_beat.py")


def main():
    """主函数"""
    logger.info("🚨 启动Redis Celery清理工具...")
    logger.info("=" * 60)
    
    cleaner = RedisCeleryCleanup()
    
    # 1. 测试连接
    if not cleaner.test_connection():
        return
    
    # 2. 分析数据
    analysis = cleaner.analyze_redis_data()
    
    if analysis['g_path_tasks'] == 0 and analysis['celery_keys'] == 0:
        logger.success("✅ Redis中没有发现需要清理的数据")
        return
    
    # 3. 询问是否清理
    print("\n" + "=" * 60)
    print("⚠️  警告: 即将清理Redis中的Celery数据")
    print(f"📊 将清理约 {analysis['celery_keys']} 个Celery相关键")
    print(f"🔄 包含 {analysis['g_path_tasks']} 个G盘路径任务")
    
    confirm = input("\n是否继续执行清理? (输入 'YES' 确认): ")
    
    if confirm != 'YES':
        logger.info("❌ 用户取消操作")
        return
    
    # 4. 执行清理
    logger.info("\n" + "=" * 60)
    results = cleaner.clean_all_celery_data(confirm=True)
    
    # 5. 重新分析
    logger.info("\n" + "=" * 60)
    cleaner.analyze_redis_data()
    
    # 6. 提示重启服务
    logger.info("\n" + "=" * 60)
    cleaner.restart_celery_services()


if __name__ == "__main__":
    main()
