#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川项目简化版语法错误检查工具
清理条件: 项目代码质量管理完全重构时可删除

千川项目简化版语法错误检查工具
============================

专门检查核心文件的语法错误，避免复杂的枚举序列化问题。

使用方法:
python ai_tools/maintenance/ai_tool_20250724_maintenance_simple_syntax_checker.py
"""

import os
import sys
import ast
import json
import traceback
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

try:
    from src.qianchuan_aw.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)


class SimpleSyntaxChecker:
    """简化版语法检查器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        
        # 定义核心文件列表
        self.core_files = [
            'main.py',
            'web_ui.py',
            'run_celery_beat.py',
            'run_celery_worker.py',
            'src/qianchuan_aw/workflows/scheduler.py',
            'src/qianchuan_aw/database/models.py',
            'src/qianchuan_aw/database/database.py',
            'src/qianchuan_aw/sdk_qc/client.py',
            'src/qianchuan_aw/utils/logger.py',
            'src/qianchuan_aw/utils/state_managers.py',
            'src/qianchuan_aw/utils/constants.py',
        ]
        
        # 定义重要文件模式
        self.important_patterns = [
            'src/qianchuan_aw/workflows/',
            'src/qianchuan_aw/services/',
            'src/qianchuan_aw/utils/',
        ]
    
    def check_file_syntax(self, file_path: str) -> dict:
        """检查单个文件的语法"""
        result = {
            'file_path': file_path,
            'exists': False,
            'is_parseable': False,
            'syntax_errors': [],
            'import_errors': [],
            'warnings': [],
            'line_count': 0,
            'function_count': 0,
            'class_count': 0
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                result['syntax_errors'].append({
                    'type': 'FileNotFound',
                    'message': f'文件不存在: {file_path}',
                    'line': 0,
                    'severity': 'critical'
                })
                return result
            
            result['exists'] = True
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result['line_count'] = len(content.split('\n'))
            
            # 检查语法
            try:
                tree = ast.parse(content, filename=file_path)
                result['is_parseable'] = True
                
                # 统计函数和类
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        result['function_count'] += 1
                    elif isinstance(node, ast.ClassDef):
                        result['class_count'] += 1
                
                # 检查导入
                self._check_imports(tree, file_path, result)
                
            except SyntaxError as e:
                result['syntax_errors'].append({
                    'type': 'SyntaxError',
                    'message': str(e.msg),
                    'line': e.lineno or 0,
                    'column': e.offset or 0,
                    'severity': 'critical'
                })
            except Exception as e:
                result['syntax_errors'].append({
                    'type': 'ParseError',
                    'message': f'解析错误: {str(e)}',
                    'line': 0,
                    'severity': 'high'
                })
                
        except Exception as e:
            result['syntax_errors'].append({
                'type': 'FileError',
                'message': f'文件读取错误: {str(e)}',
                'line': 0,
                'severity': 'high'
            })
        
        return result
    
    def _check_imports(self, tree: ast.AST, file_path: str, result: dict):
        """检查导入语句"""
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    self._validate_import(alias.name, node.lineno, file_path, result)
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    self._validate_import(node.module, node.lineno, file_path, result)
    
    def _validate_import(self, module_name: str, line_number: int, file_path: str, result: dict):
        """验证单个导入"""
        # 跳过标准库和相对导入
        standard_libs = {
            'os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'typing',
            'collections', 'functools', 'itertools', 'logging', 'traceback',
            'ast', 'importlib', 'enum', 'dataclasses', 're', 'hashlib',
            'uuid', 'threading', 'multiprocessing', 'subprocess', 'shutil',
            'tempfile', 'glob', 'pickle', 'csv', 'sqlite3', 'urllib',
            'http', 'email', 'html', 'xml', 'base64', 'binascii', 'zlib',
            'gzip', 'tarfile', 'zipfile', 'configparser', 'argparse'
        }
        
        if (module_name.startswith('.') or 
            module_name.split('.')[0] in standard_libs):
            return
        
        # 检查第三方库
        third_party_libs = {
            'streamlit', 'pandas', 'numpy', 'requests', 'sqlalchemy',
            'celery', 'redis', 'psycopg2', 'playwright', 'selenium',
            'loguru', 'pydantic', 'fastapi', 'flask', 'django',
            'pytest', 'click', 'tqdm', 'pillow', 'opencv', 'matplotlib',
            'seaborn', 'plotly', 'bokeh', 'dash', 'jupyter'
        }
        
        if module_name.split('.')[0] in third_party_libs:
            return
        
        # 检查项目内部模块
        if module_name.startswith('src.qianchuan_aw'):
            # 这是项目内部模块，暂时不检查
            return
        
        # 其他未知模块记录为警告
        result['import_errors'].append({
            'type': 'UnknownModule',
            'module': module_name,
            'line': line_number,
            'message': f'未知模块: {module_name}',
            'severity': 'medium'
        })
    
    def check_all_core_files(self) -> dict:
        """检查所有核心文件"""
        logger.info("🔍 开始检查核心文件语法...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_files': len(self.core_files),
            'files': [],
            'summary': {
                'parseable_files': 0,
                'files_with_errors': 0,
                'total_syntax_errors': 0,
                'total_import_errors': 0,
                'missing_files': 0
            }
        }
        
        for rel_path in self.core_files:
            file_path = os.path.join(self.project_root, rel_path)
            logger.info(f"检查文件: {rel_path}")
            
            try:
                file_result = self.check_file_syntax(file_path)
                file_result['relative_path'] = rel_path
                file_result['importance'] = 'core'
                
                results['files'].append(file_result)
                
                # 更新统计
                if not file_result['exists']:
                    results['summary']['missing_files'] += 1
                elif file_result['is_parseable']:
                    results['summary']['parseable_files'] += 1
                
                if file_result['syntax_errors'] or file_result['import_errors']:
                    results['summary']['files_with_errors'] += 1
                
                results['summary']['total_syntax_errors'] += len(file_result['syntax_errors'])
                results['summary']['total_import_errors'] += len(file_result['import_errors'])
                
            except Exception as e:
                logger.error(f"检查文件失败 {rel_path}: {e}")
                results['files'].append({
                    'relative_path': rel_path,
                    'file_path': file_path,
                    'exists': False,
                    'error': str(e),
                    'importance': 'core'
                })
        
        logger.info(f"✅ 核心文件检查完成")
        return results
    
    def find_important_files(self) -> list:
        """查找重要文件"""
        important_files = []
        
        for pattern in self.important_patterns:
            pattern_path = os.path.join(self.project_root, pattern)
            if os.path.exists(pattern_path):
                for root, dirs, files in os.walk(pattern_path):
                    # 跳过一些目录
                    dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
                    
                    for file in files:
                        if file.endswith('.py'):
                            file_path = os.path.join(root, file)
                            rel_path = os.path.relpath(file_path, self.project_root)
                            if rel_path not in [f.replace('\\', '/') for f in self.core_files]:
                                important_files.append(rel_path)
        
        return important_files[:20]  # 限制数量
    
    def check_important_files(self) -> dict:
        """检查重要文件"""
        logger.info("🔍 开始检查重要文件语法...")
        
        important_files = self.find_important_files()
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'total_files': len(important_files),
            'files': [],
            'summary': {
                'parseable_files': 0,
                'files_with_errors': 0,
                'total_syntax_errors': 0,
                'total_import_errors': 0,
                'missing_files': 0
            }
        }
        
        for rel_path in important_files:
            file_path = os.path.join(self.project_root, rel_path)
            logger.info(f"检查文件: {rel_path}")
            
            try:
                file_result = self.check_file_syntax(file_path)
                file_result['relative_path'] = rel_path
                file_result['importance'] = 'important'
                
                results['files'].append(file_result)
                
                # 更新统计
                if not file_result['exists']:
                    results['summary']['missing_files'] += 1
                elif file_result['is_parseable']:
                    results['summary']['parseable_files'] += 1
                
                if file_result['syntax_errors'] or file_result['import_errors']:
                    results['summary']['files_with_errors'] += 1
                
                results['summary']['total_syntax_errors'] += len(file_result['syntax_errors'])
                results['summary']['total_import_errors'] += len(file_result['import_errors'])
                
            except Exception as e:
                logger.error(f"检查文件失败 {rel_path}: {e}")
        
        logger.info(f"✅ 重要文件检查完成")
        return results


def main():
    """主函数"""
    checker = SimpleSyntaxChecker()
    
    # 检查核心文件
    core_results = checker.check_all_core_files()
    
    # 检查重要文件
    important_results = checker.check_important_files()
    
    # 合并结果
    combined_results = {
        'timestamp': datetime.now().isoformat(),
        'core_files': core_results,
        'important_files': important_results,
        'overall_summary': {
            'total_files_checked': core_results['total_files'] + important_results['total_files'],
            'total_syntax_errors': core_results['summary']['total_syntax_errors'] + important_results['summary']['total_syntax_errors'],
            'total_import_errors': core_results['summary']['total_import_errors'] + important_results['summary']['total_import_errors'],
            'core_files_with_errors': core_results['summary']['files_with_errors'],
            'important_files_with_errors': important_results['summary']['files_with_errors']
        }
    }
    
    # 保存结果
    output_file = 'ai_reports/audit/simple_syntax_check_results.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(combined_results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"📄 检查结果已保存到: {output_file}")
    
    # 显示摘要
    print(f"\n📊 语法检查摘要:")
    print(f"   核心文件: {core_results['total_files']} 个")
    print(f"   重要文件: {important_results['total_files']} 个")
    print(f"   语法错误: {combined_results['overall_summary']['total_syntax_errors']} 个")
    print(f"   导入错误: {combined_results['overall_summary']['total_import_errors']} 个")
    print(f"   核心文件错误: {combined_results['overall_summary']['core_files_with_errors']} 个")
    print(f"   重要文件错误: {combined_results['overall_summary']['important_files_with_errors']} 个")
    
    return combined_results


if __name__ == '__main__':
    main()
