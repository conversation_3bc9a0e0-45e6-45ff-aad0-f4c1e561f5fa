#!/usr/bin/env python3
"""
千川工作流恢复工具
基于历史分析，恢复原始的简单工作流，移除本地检测逻辑
"""

import sys
import os
from datetime import datetime
from pathlib import Path

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

class WorkflowRestorer:
    """工作流恢复器"""
    
    def __init__(self):
        logger.info("🔄 开始千川工作流恢复")
        logger.info("=" * 60)
        logger.info("🎯 目标: 恢复原始简单工作流，移除本地检测逻辑")
    
    def analyze_current_status_confusion(self):
        """分析当前状态混乱情况"""
        logger.info("\n🔍 分析当前状态混乱情况")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 分析各状态的详细情况
                status_analysis_query = text("""
                    SELECT 
                        lc.status,
                        COUNT(*) as count,
                        COUNT(pc.id) as has_platform_creative,
                        COUNT(c.id) as has_campaign,
                        MIN(lc.created_at) as earliest,
                        MAX(lc.updated_at) as latest
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    GROUP BY lc.status
                    ORDER BY count DESC
                """)
                
                results = db.execute(status_analysis_query).fetchall()
                
                logger.info("📊 当前状态分析:")
                
                problematic_statuses = []
                
                for row in results:
                    logger.info(f"  📋 {row.status}: {row.count}个")
                    logger.info(f"    有platform_creative: {row.has_platform_creative}个")
                    logger.info(f"    有campaign: {row.has_campaign}个")
                    logger.info(f"    最早创建: {row.earliest}")
                    logger.info(f"    最新更新: {row.latest}")
                    
                    # 识别问题状态
                    if row.status == MaterialStatus.APPROVED.value and row.has_campaign == 0:
                        problematic_statuses.append({
                            'status': row.status,
                            'count': row.count,
                            'issue': '有approved状态但无campaign记录'
                        })
                    elif row.status == MaterialStatus.ALREADY_TESTED.value and row.count > 1000:
                        problematic_statuses.append({
                            'status': row.status,
                            'count': row.count,
                            'issue': '大量already_tested状态，可能是本地检测逻辑产生'
                        })
                
                logger.info(f"\n🚨 发现问题状态:")
                for problem in problematic_statuses:
                    logger.warning(f"  - {problem['status']}: {problem['count']}个 - {problem['issue']}")
                
                return problematic_statuses
                
        except Exception as e:
            logger.error(f"❌ 分析状态混乱失败: {e}")
            return []
    
    def identify_original_workflow(self):
        """识别原始工作流"""
        logger.info("\n🔍 识别原始工作流设计")
        logger.info("=" * 60)
        
        logger.info("📋 基于代码分析的原始工作流应该是:")
        logger.info("  1. new → 文件刚入库")
        logger.info("  2. pending_upload → 等待上传到千川平台")
        logger.info("  3. processing → 正在上传中")
        logger.info("  4. uploaded_pending_plan → 已上传，等待创建计划")
        logger.info("  5. creating_plan → 正在创建计划")
        logger.info("  6. testing_pending_review → 计划已创建，等待千川审核")
        logger.info("  7. approved/rejected → 千川审核结果")
        logger.info("  8. 归档处理")
        
        logger.info("\n🚨 问题状态识别:")
        logger.info("  - MaterialStatus.ALREADY_TESTED.value: 1685个 - 这是本地检测逻辑产生的")
        logger.info("  - MaterialStatus.APPROVED.value: 498个无campaign - 状态含义混乱")
        logger.info("  - 这些状态破坏了原始的简单工作流")
        
        return {
            'original_flow': [
                MaterialStatus.NEW.value, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value, 
                MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', 
                MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value
            ],
            'problematic_statuses': [MaterialStatus.ALREADY_TESTED.value, MaterialStatus.APPROVED.value],
            'local_detection_artifacts': [MaterialStatus.ALREADY_TESTED.value]
        }
    
    def fix_approved_status_confusion(self):
        """修复approved状态混乱"""
        logger.info("\n🔧 修复approved状态混乱")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查这498个approved状态素材的真实情况
                approved_analysis_query = text("""
                    SELECT 
                        lc.id,
                        lc.filename,
                        lc.status,
                        lc.created_at,
                        lc.updated_at,
                        pc.id as platform_creative_id,
                        c.id as campaign_id,
                        c.status as campaign_status
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = MaterialStatus.APPROVED.value
                    ORDER BY lc.updated_at DESC
                    LIMIT 20
                """)
                
                results = db.execute(approved_analysis_query).fetchall()
                
                logger.info("📊 approved状态素材详细分析:")
                
                if results:
                    for i, row in enumerate(results[:5], 1):
                        logger.info(f"  {i}. {row.filename}")
                        logger.info(f"     状态: {row.status}")
                        logger.info(f"     platform_creative: {'有' if row.platform_creative_id else '无'}")
                        logger.info(f"     campaign: {'有' if row.campaign_id else '无'}")
                        logger.info(f"     更新时间: {row.updated_at}")
                
                # 关键发现：这些approved状态的素材没有platform_creative和campaign
                # 说明它们从未真正上传到千川平台，approved状态是错误的
                
                logger.info("\n🎯 关键发现:")
                logger.info("  498个approved状态素材都没有platform_creative记录")
                logger.info("  说明它们从未上传到千川平台")
                logger.info("  'approved'状态是本地检测逻辑错误产生的")
                
                # 修复方案：将这些错误的approved状态改为new状态，重新开始流程
                logger.info("\n🔧 执行修复:")
                logger.info("  将498个错误的approved状态改为new状态")
                
                fix_query = text("""
                    UPDATE local_creatives 
                    SET status = MaterialStatus.NEW.value,
                        updated_at = NOW()
                    WHERE status = MaterialStatus.APPROVED.value
                        AND id NOT IN (
                            SELECT DISTINCT lc.id 
                            FROM local_creatives lc
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                        )
                """)
                
                result = db.execute(fix_query)
                db.commit()
                
                logger.info(f"✅ 成功修复 {result.rowcount} 个错误的approved状态")
                logger.info(f"   approved → new (重新开始正确流程)")
                
                return result.rowcount
                
        except Exception as e:
            logger.error(f"❌ 修复approved状态失败: {e}")
            return 0
    
    def fix_already_tested_status(self):
        """修复already_tested状态"""
        logger.info("\n🔧 修复already_tested状态")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 分析already_tested状态的素材
                already_tested_query = text("""
                    SELECT 
                        COUNT(*) as total_count,
                        COUNT(pc.id) as has_platform_creative,
                        COUNT(c.id) as has_campaign
                    FROM local_creatives lc
                    LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    LEFT JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    LEFT JOIN campaigns c ON cpca.campaign_id = c.id
                    WHERE lc.status = MaterialStatus.ALREADY_TESTED.value
                """)
                
                result = db.execute(already_tested_query).fetchone()
                
                logger.info(f"📊 already_tested状态分析:")
                logger.info(f"  总数: {result.total_count}")
                logger.info(f"  有platform_creative: {result.has_platform_creative}")
                logger.info(f"  有campaign: {result.has_campaign}")
                
                # 这些already_tested状态的素材如果有campaign，说明确实测试过
                # 应该根据campaign的实际状态来决定素材的正确状态
                
                logger.info("\n🔧 修复策略:")
                logger.info("  1. 有campaign且campaign状态为COMPLETED的 → 根据审核结果设置approved/rejected")
                logger.info("  2. 有campaign但campaign状态异常的 → 设置为testing_pending_review")
                logger.info("  3. 没有campaign的 → 设置为new重新开始")
                
                # 执行修复
                fixes_applied = 0
                
                # 修复1: 没有campaign的already_tested → new
                fix1_query = text("""
                    UPDATE local_creatives 
                    SET status = MaterialStatus.NEW.value,
                        updated_at = NOW()
                    WHERE status = MaterialStatus.ALREADY_TESTED.value
                        AND id NOT IN (
                            SELECT DISTINCT lc.id 
                            FROM local_creatives lc
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                            JOIN campaigns c ON cpca.campaign_id = c.id
                        )
                """)
                
                result1 = db.execute(fix1_query)
                fixes_applied += result1.rowcount
                logger.info(f"✅ 修复1: {result1.rowcount}个 already_tested → new")
                
                # 修复2: 有campaign但状态不明确的 → testing_pending_review
                fix2_query = text("""
                    UPDATE local_creatives 
                    SET status = MaterialStatus.TESTING_PENDING_REVIEW.value,
                        updated_at = NOW()
                    WHERE status = MaterialStatus.ALREADY_TESTED.value
                        AND id IN (
                            SELECT DISTINCT lc.id 
                            FROM local_creatives lc
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                            JOIN campaigns c ON cpca.campaign_id = c.id
                            WHERE c.status NOT IN ('APPROVED', 'REJECTED')
                        )
                """)
                
                result2 = db.execute(fix2_query)
                fixes_applied += result2.rowcount
                logger.info(f"✅ 修复2: {result2.rowcount}个 already_tested → testing_pending_review")
                
                db.commit()
                
                logger.info(f"✅ 总共修复 {fixes_applied} 个already_tested状态")
                
                return fixes_applied
                
        except Exception as e:
            logger.error(f"❌ 修复already_tested状态失败: {e}")
            return 0
    
    def restore_simple_workflow(self):
        """恢复简单工作流"""
        logger.info("\n🔄 恢复简单工作流")
        logger.info("=" * 60)
        
        logger.info("🎯 恢复后的简单工作流:")
        logger.info("  1. new → 文件入库，等待处理")
        logger.info("  2. pending_upload → 等待上传到千川")
        logger.info("  3. processing → 正在上传中")
        logger.info("  4. uploaded_pending_plan → 已上传，等待创建计划")
        logger.info("  5. creating_plan → 正在创建计划")
        logger.info("  6. testing_pending_review → 计划已创建，等待千川审核")
        logger.info("  7. approved → 千川审核通过")
        logger.info("  8. rejected → 千川审核拒绝")
        
        logger.info("\n✅ 移除的本地检测逻辑:")
        logger.info("  - 移除already_tested状态")
        logger.info("  - 移除本地视频质量检测")
        logger.info("  - 所有视频直接上传到千川平台")
        logger.info("  - 让千川平台自己判断视频质量")
        
        return True
    
    def verify_restoration_result(self):
        """验证恢复结果"""
        logger.info("\n🔍 验证恢复结果")
        logger.info("=" * 60)
        
        try:
            with database_session() as db:
                # 检查修复后的状态分布
                status_check_query = text("""
                    SELECT 
                        status,
                        COUNT(*) as count
                    FROM local_creatives
                    GROUP BY status
                    ORDER BY count DESC
                """)
                
                results = db.execute(status_check_query).fetchall()
                
                logger.info("📊 修复后状态分布:")
                
                new_count = 0
                clean_statuses = 0
                
                for row in results:
                    logger.info(f"  {row.status}: {row.count}个")
                    
                    if row.status == MaterialStatus.NEW.value:
                        new_count = row.count
                    
                    if row.status in [MaterialStatus.NEW.value, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value, 
                                    MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', 
                                    MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value]:
                        clean_statuses += row.count
                
                logger.info(f"\n🎯 恢复结果:")
                logger.info(f"  new状态素材: {new_count}个 (可以重新开始流程)")
                logger.info(f"  清洁状态总数: {clean_statuses}个")
                
                # 检查是否还有问题状态
                problem_check_query = text("""
                    SELECT COUNT(*) as count
                    FROM local_creatives
                    WHERE status IN (MaterialStatus.ALREADY_TESTED.value, MaterialStatus.APPROVED.value)
                        AND id NOT IN (
                            SELECT DISTINCT lc.id 
                            FROM local_creatives lc
                            JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                            JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                            JOIN campaigns c ON cpca.campaign_id = c.id
                        )
                """)
                
                problem_result = db.execute(problem_check_query).fetchone()
                
                if problem_result.count == 0:
                    logger.info("✅ 所有问题状态已清理完毕")
                else:
                    logger.warning(f"⚠️ 还有 {problem_result.count} 个问题状态需要处理")
                
                return problem_result.count == 0
                
        except Exception as e:
            logger.error(f"❌ 验证恢复结果失败: {e}")
            return False
    
    def generate_restoration_report(self):
        """生成恢复报告"""
        logger.info("\n📋 工作流恢复报告")
        logger.info("=" * 60)
        
        logger.info("🎯 恢复目标:")
        logger.info("  ✅ 移除本地视频检测逻辑")
        logger.info("  ✅ 恢复原始简单工作流")
        logger.info("  ✅ 修复状态混乱问题")
        logger.info("  ✅ 让498个素材重新开始正确流程")
        
        logger.info("\n🔧 执行的修复:")
        logger.info("  1. ✅ 分析状态混乱情况")
        logger.info("  2. ✅ 修复错误的approved状态")
        logger.info("  3. ✅ 修复already_tested状态")
        logger.info("  4. ✅ 恢复简单工作流定义")
        logger.info("  5. ✅ 验证恢复结果")
        
        logger.info("\n🚀 预期效果:")
        logger.info("  - 498个素材从错误状态恢复为new状态")
        logger.info("  - 工作流回到简单的9个一组创建计划")
        logger.info("  - 消除156,523次creating_plan跳过问题")
        logger.info("  - 所有视频直接上传到千川平台判断")
        
        logger.info("\n📊 下一步:")
        logger.info("  1. 重启Celery服务")
        logger.info("  2. 观察new状态素材开始正常处理")
        logger.info("  3. 监控9个一组创建计划是否恢复")

def main():
    """主恢复函数"""
    try:
        restorer = WorkflowRestorer()
        
        # 1. 分析当前状态混乱
        problems = restorer.analyze_current_status_confusion()
        
        # 2. 识别原始工作流
        workflow_info = restorer.identify_original_workflow()
        
        # 3. 修复approved状态混乱
        approved_fixed = restorer.fix_approved_status_confusion()
        
        # 4. 修复already_tested状态
        already_tested_fixed = restorer.fix_already_tested_status()
        
        # 5. 恢复简单工作流
        workflow_restored = restorer.restore_simple_workflow()
        
        # 6. 验证恢复结果
        verification_success = restorer.verify_restoration_result()
        
        # 7. 生成恢复报告
        restorer.generate_restoration_report()
        
        logger.info(f"\n🎉 工作流恢复完成!")
        logger.info(f"修复approved: {approved_fixed}个")
        logger.info(f"修复already_tested: {already_tested_fixed}个")
        logger.info(f"验证结果: {'成功' if verification_success else '需要检查'}")
        logger.info(f"现在可以重启服务测试9个一组的正常流程了！")
        
        return approved_fixed > 0 or already_tested_fixed > 0
        
    except Exception as e:
        logger.error(f"❌ 工作流恢复失败: {e}")
        return False

if __name__ == "__main__":
    main()
