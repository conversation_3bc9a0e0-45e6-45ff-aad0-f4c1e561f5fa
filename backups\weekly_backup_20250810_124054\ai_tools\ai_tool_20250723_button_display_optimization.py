#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 优化按钮账户选择器的显示效果
依赖关系: 全局账户选择器
清理条件: 优化完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_optimized_button_display():
    """创建优化的按钮显示代码"""
    
    optimized_code = '''
    # 使用按钮网格显示账户
    accounts_changed = False
    
    for i in range(0, len(current_page_accounts), 2):
        col1, col2 = st.sidebar.columns(2)
        
        # 第一个账户
        account1 = current_page_accounts[i]
        is_favorite1 = getattr(account1, 'is_favorite', False)
        has_douyin1 = getattr(account1, 'douyin_id', None) is not None
        is_selected1 = account1.account_id_qc == current_selected_id
        
        # 构建按钮文本 - 智能显示名称
        star1 = "⭐" if is_favorite1 else ""
        phone1 = "📱" if has_douyin1 else ""
        
        # 智能截断账户名称
        name1 = account1.name
        if len(name1) > 15:
            # 保留重要部分，去掉中间部分
            if '-' in name1:
                parts = name1.split('-')
                if len(parts) >= 2:
                    name1 = f"{parts[0][:6]}-{parts[-1][:6]}"
                else:
                    name1 = name1[:15] + "..."
            else:
                name1 = name1[:15] + "..."
        
        button_text1 = f"{star1}{phone1}\\n{name1}"
        
        with col1:
            # 动态获取当前选中状态，确保按钮颜色实时更新
            current_account = get_global_selected_account()
            is_current_selected1 = (current_account and 
                                  current_account.account_id_qc == account1.account_id_qc)
            
            if st.button(
                button_text1,
                key=f"account_btn_{account1.account_id_qc}",
                type="primary" if is_current_selected1 else "secondary",
                use_container_width=True,
                help=f"完整名称: {account1.name}\\n千川ID: {account1.account_id_qc}\\n点击切换到此账户"
            ):
                if not is_current_selected1:
                    set_global_selected_account(account1)
                    accounts_changed = True
                    logger.info(f"账户选择变化: {current_account.name if current_account else 'None'} → {account1.name}")
                    # 立即更新按钮状态
                    st.rerun()
        
        # 第二个账户（如果存在）
        if i + 1 < len(current_page_accounts):
            account2 = current_page_accounts[i + 1]
            is_favorite2 = getattr(account2, 'is_favorite', False)
            has_douyin2 = getattr(account2, 'douyin_id', None) is not None
            is_selected2 = account2.account_id_qc == current_selected_id
            
            star2 = "⭐" if is_favorite2 else ""
            phone2 = "📱" if has_douyin2 else ""
            
            # 智能截断账户名称
            name2 = account2.name
            if len(name2) > 15:
                if '-' in name2:
                    parts = name2.split('-')
                    if len(parts) >= 2:
                        name2 = f"{parts[0][:6]}-{parts[-1][:6]}"
                    else:
                        name2 = name2[:15] + "..."
                else:
                    name2 = name2[:15] + "..."
            
            button_text2 = f"{star2}{phone2}\\n{name2}"
            
            with col2:
                # 动态获取当前选中状态
                current_account = get_global_selected_account()
                is_current_selected2 = (current_account and 
                                      current_account.account_id_qc == account2.account_id_qc)
                
                if st.button(
                    button_text2,
                    key=f"account_btn_{account2.account_id_qc}",
                    type="primary" if is_current_selected2 else "secondary",
                    use_container_width=True,
                    help=f"完整名称: {account2.name}\\n千川ID: {account2.account_id_qc}\\n点击切换到此账户"
                ):
                    if not is_current_selected2:
                        set_global_selected_account(account2)
                        accounts_changed = True
                        logger.info(f"账户选择变化: {current_account.name if current_account else 'None'} → {account2.name}")
                        # 立即更新按钮状态
                        st.rerun()
'''
    
    return optimized_code

def apply_button_optimization():
    """应用按钮显示优化"""
    
    selector_path = project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_global_account_selector.py'
    
    print("🎨 优化按钮账户选择器显示效果...")
    print("💡 修复按钮颜色更新和名称显示问题")
    
    try:
        # 读取当前文件
        with open(selector_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成优化的代码
        optimized_code = create_optimized_button_display()
        
        # 查找并替换按钮显示部分
        import re
        
        # 匹配从"使用按钮网格显示账户"到下一个注释或函数
        pattern = r'    # 使用按钮网格显示账户.*?(?=    # 显示当前选中的账户状态|\ndef \w+|\nclass \w+|\Z)'
        
        if re.search(pattern, content, re.DOTALL):
            # 替换按钮显示代码
            new_content = re.sub(pattern, optimized_code.strip(), content, flags=re.DOTALL)
            
            # 写入文件
            with open(selector_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 按钮显示优化应用成功")
            return True
        else:
            print("❌ 未找到按钮显示代码段")
            return False
            
    except Exception as e:
        print(f"❌ 优化应用失败: {e}")
        return False

def main():
    """主函数"""
    print("🎨 按钮账户选择器显示优化")
    print("=" * 60)
    print("🎯 目标：修复按钮颜色更新和名称显示问题")
    print()
    
    print("🔧 优化内容:")
    print("  ✅ 修复按钮颜色不跟随更新的问题")
    print("  ✅ 优化账户名称显示，避免截断过短")
    print("  ✅ 智能名称截断，保留重要信息")
    print("  ✅ 增强按钮提示信息")
    print("  ✅ 动态状态检查，确保颜色实时更新")
    print()
    
    print("💡 技术改进:")
    print("  - 在按钮渲染时动态获取当前选中状态")
    print("  - 点击后立即调用st.rerun()更新按钮颜色")
    print("  - 智能名称截断算法，保留关键信息")
    print("  - 增强的hover提示，显示完整信息")
    print()
    
    # 应用优化
    if apply_button_optimization():
        print("🎉 按钮显示优化应用成功！")
        print()
        print("🔧 修复效果:")
        print("  ✅ 点击按钮后颜色立即更新")
        print("  ✅ 账户名称显示更完整")
        print("  ✅ 智能截断保留重要信息")
        print("  ✅ 提示信息更详细")
        print()
        print("🧪 测试建议:")
        print("  1. 重启 Streamlit 应用")
        print("  2. 点击不同账户按钮")
        print("  3. 观察按钮颜色是否立即变化")
        print("  4. 检查账户名称是否显示更完整")
        print("  5. 鼠标悬停查看完整信息")
        print()
        print("⚡ 预期效果:")
        print("  - 按钮颜色: 点击后立即更新")
        print("  - 名称显示: 更完整，智能截断")
        print("  - 用户体验: 视觉反馈更直观")
        
        return True
    else:
        print("❌ 优化应用失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
