#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复提审状态数据不一致问题
清理条件: 成为项目维护工具，长期保留
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import Campaign
from qianchuan_aw.utils.appeal_status_definitions import (
    AppealStatus, StateConsistencyRules, get_correct_status_for_inconsistent_data,
    get_status_display_name
)
from sqlalchemy import text


@contextmanager
def database_session():
    """数据库会话上下文管理器"""
    session = SessionLocal()
    try:
        yield session
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()


class AppealStatusDataFixer:
    """提审状态数据修复器"""
    
    def __init__(self):
        self.fixed_count = 0
        self.issues_found = []
        self.backup_created = False
    
    def run_full_fix(self, dry_run: bool = True) -> Dict[str, Any]:
        """运行完整的数据修复流程"""
        logger.info("🚀 开始提审状态数据修复流程")
        logger.info(f"📋 运行模式: {'DRY RUN（预览模式）' if dry_run else 'LIVE RUN（实际修复）'}")
        logger.info("="*80)
        
        try:
            # 步骤1：数据备份
            if not dry_run:
                self._create_backup()
            
            # 步骤2：分析当前问题
            analysis = self._analyze_current_issues()
            
            # 步骤3：修复数据不一致
            fix_results = self._fix_inconsistent_data(dry_run)
            
            # 步骤4：验证修复结果
            if not dry_run:
                validation = self._validate_fix_results()
            else:
                validation = {"status": "skipped", "message": "DRY RUN模式跳过验证"}
            
            # 步骤5：生成报告
            report = self._generate_fix_report(analysis, fix_results, validation)
            
            logger.success("✅ 提审状态数据修复流程完成")
            return report
            
        except Exception as e:
            logger.error(f"❌ 数据修复流程失败: {e}", exc_info=True)
            raise
    
    def _create_backup(self):
        """创建数据备份"""
        logger.info("💾 创建数据备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_campaigns_appeal_status_{timestamp}.sql"
        
        with database_session() as db:
            # 导出相关字段的备份
            backup_query = text("""
                SELECT
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    last_appeal_at,
                    appeal_attempt_count,
                    appeal_result,
                    appeal_error_message,
                    created_at,
                    last_updated
                FROM campaigns
                WHERE appeal_status IS NOT NULL 
                   OR first_appeal_at IS NOT NULL
                ORDER BY created_at
            """)
            
            results = db.execute(backup_query).fetchall()
            
            # 保存备份信息到日志
            logger.info(f"📊 备份记录数: {len(results)}")
            logger.info(f"💾 备份文件: {backup_file}")
            
            self.backup_created = True
    
    def _analyze_current_issues(self) -> Dict[str, Any]:
        """分析当前的数据问题"""
        logger.info("🔍 分析当前数据问题...")
        
        with database_session() as db:
            # 查询所有可能有问题的记录
            analysis_query = text("""
                SELECT 
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    last_appeal_at,
                    appeal_attempt_count,
                    created_at,
                    CASE 
                        WHEN appeal_status = 'appeal_pending' AND first_appeal_at IS NULL THEN 'pending_without_time'
                        WHEN appeal_status IS NULL AND first_appeal_at IS NOT NULL THEN 'time_without_status'
                        WHEN appeal_status = 'appeal_pending' AND (appeal_attempt_count IS NULL OR appeal_attempt_count = 0) THEN 'pending_without_count'
                        ELSE 'consistent'
                    END as issue_type
                FROM campaigns 
                WHERE created_at >= NOW() - INTERVAL '7 days'
                ORDER BY created_at DESC
            """)
            
            results = db.execute(analysis_query).fetchall()
            
            # 统计问题类型
            issue_stats = {
                'pending_without_time': 0,      # appeal_pending但没有first_appeal_at
                'time_without_status': 0,       # 有first_appeal_at但状态为空
                'pending_without_count': 0,     # appeal_pending但尝试次数为0
                'consistent': 0,                # 数据一致
                'total': len(results)
            }
            
            problem_campaigns = []
            
            for row in results:
                issue_type = row.issue_type
                issue_stats[issue_type] += 1
                
                if issue_type != 'consistent':
                    problem_campaigns.append({
                        'campaign_id_qc': row.campaign_id_qc,
                        'issue_type': issue_type,
                        'current_status': row.appeal_status,
                        'first_appeal_at': row.first_appeal_at,
                        'attempt_count': row.appeal_attempt_count,
                        'created_at': row.created_at
                    })
            
            logger.info(f"📊 数据分析结果:")
            logger.info(f"   总计划数: {issue_stats['total']}")
            logger.info(f"   数据一致: {issue_stats['consistent']}")
            logger.info(f"   pending但无时间: {issue_stats['pending_without_time']}")
            logger.info(f"   有时间但无状态: {issue_stats['time_without_status']}")
            logger.info(f"   pending但无计数: {issue_stats['pending_without_count']}")
            
            return {
                'statistics': issue_stats,
                'problem_campaigns': problem_campaigns,
                'analysis_time': datetime.now(timezone.utc)
            }
    
    def _fix_inconsistent_data(self, dry_run: bool) -> Dict[str, Any]:
        """修复数据不一致问题"""
        logger.info(f"🔧 {'预览' if dry_run else '执行'}数据修复...")
        
        fix_operations = []
        
        with database_session() as db:
            # 查找需要修复的记录
            problem_query = text("""
                SELECT 
                    id,
                    campaign_id_qc,
                    appeal_status,
                    first_appeal_at,
                    appeal_attempt_count
                FROM campaigns 
                WHERE (appeal_status = 'appeal_pending' AND first_appeal_at IS NULL)
                   OR (appeal_status IS NULL AND first_appeal_at IS NOT NULL)
                   OR (appeal_status = 'appeal_pending' AND (appeal_attempt_count IS NULL OR appeal_attempt_count = 0))
            """)
            
            problem_records = db.execute(problem_query).fetchall()
            
            for record in problem_records:
                # 确定正确的状态
                correct_status = get_correct_status_for_inconsistent_data(
                    record.appeal_status, 
                    record.first_appeal_at
                )
                
                operation = {
                    'campaign_id_qc': record.campaign_id_qc,
                    'old_status': record.appeal_status,
                    'new_status': correct_status,
                    'old_first_appeal_at': record.first_appeal_at,
                    'old_attempt_count': record.appeal_attempt_count,
                    'action': 'reset_to_not_appealed' if correct_status is None else 'set_appealed_status'
                }
                
                if not dry_run:
                    # 执行实际修复
                    if correct_status is None:
                        # 重置为未提审状态
                        update_query = text("""
                            UPDATE campaigns
                            SET appeal_status = NULL,
                                appeal_attempt_count = 0,
                                last_updated = NOW()
                            WHERE id = :id
                        """)
                        db.execute(update_query, {'id': record.id})
                        operation['executed'] = True
                    else:
                        # 设置为已提审状态
                        update_query = text("""
                            UPDATE campaigns 
                            SET appeal_status = :status,
                                appeal_attempt_count = COALESCE(appeal_attempt_count, 1),
                                updated_at = NOW()
                            WHERE id = :id
                        """)
                        db.execute(update_query, {'id': record.id, 'status': correct_status})
                        operation['executed'] = True
                else:
                    operation['executed'] = False
                
                fix_operations.append(operation)
                
                logger.info(f"   {'✅ 修复' if not dry_run else '📋 计划修复'}: {record.campaign_id_qc} "
                          f"{get_status_display_name(record.appeal_status)} → {get_status_display_name(correct_status)}")
            
            if not dry_run and fix_operations:
                db.commit()
                self.fixed_count = len([op for op in fix_operations if op['executed']])
                logger.success(f"✅ 成功修复 {self.fixed_count} 个计划的状态")
            
            return {
                'total_operations': len(fix_operations),
                'executed_operations': len([op for op in fix_operations if op.get('executed', False)]),
                'operations': fix_operations,
                'fix_time': datetime.now(timezone.utc)
            }
    
    def _validate_fix_results(self) -> Dict[str, Any]:
        """验证修复结果"""
        logger.info("✅ 验证修复结果...")
        
        with database_session() as db:
            # 重新检查数据一致性
            validation_query = text("""
                SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN appeal_status = 'appeal_pending' AND first_appeal_at IS NULL THEN 1 ELSE 0 END) as pending_without_time,
                    SUM(CASE WHEN appeal_status IS NULL AND first_appeal_at IS NOT NULL THEN 1 ELSE 0 END) as time_without_status,
                    SUM(CASE WHEN appeal_status = 'appeal_pending' AND (appeal_attempt_count IS NULL OR appeal_attempt_count = 0) THEN 1 ELSE 0 END) as pending_without_count
                FROM campaigns 
                WHERE created_at >= NOW() - INTERVAL '7 days'
            """)
            
            result = db.execute(validation_query).fetchone()
            
            validation_result = {
                'total_campaigns': result.total_count,
                'remaining_issues': {
                    'pending_without_time': result.pending_without_time,
                    'time_without_status': result.time_without_status,
                    'pending_without_count': result.pending_without_count
                },
                'validation_time': datetime.now(timezone.utc),
                'success': (result.pending_without_time + result.time_without_status + result.pending_without_count) == 0
            }
            
            if validation_result['success']:
                logger.success("✅ 验证通过：所有数据不一致问题已修复")
            else:
                logger.warning(f"⚠️ 验证发现剩余问题: {validation_result['remaining_issues']}")
            
            return validation_result
    
    def _generate_fix_report(self, analysis: Dict, fix_results: Dict, validation: Dict) -> Dict[str, Any]:
        """生成修复报告"""
        report = {
            'fix_summary': {
                'total_problems_found': len(analysis['problem_campaigns']),
                'total_operations_planned': fix_results['total_operations'],
                'total_operations_executed': fix_results['executed_operations'],
                'backup_created': self.backup_created,
                'validation_success': validation.get('success', False)
            },
            'detailed_analysis': analysis,
            'fix_operations': fix_results,
            'validation_results': validation,
            'report_time': datetime.now(timezone.utc)
        }
        
        logger.info("📋 修复报告生成完成")
        return report


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='提审状态数据修复工具')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不执行实际修复')
    parser.add_argument('--live', action='store_true', help='实际执行修复')
    
    args = parser.parse_args()
    
    if not args.dry_run and not args.live:
        logger.error("❌ 请指定运行模式: --dry-run (预览) 或 --live (实际执行)")
        return
    
    if args.live:
        logger.warning("⚠️ 即将执行实际数据修复，请确认已做好备份！")
        confirm = input("确认继续？(yes/no): ")
        if confirm.lower() != 'yes':
            logger.info("❌ 用户取消操作")
            return
    
    fixer = AppealStatusDataFixer()
    report = fixer.run_full_fix(dry_run=args.dry_run)
    
    logger.info("🎉 修复流程完成")
    logger.info(f"📊 修复统计: {report['fix_summary']}")


if __name__ == "__main__":
    main()
