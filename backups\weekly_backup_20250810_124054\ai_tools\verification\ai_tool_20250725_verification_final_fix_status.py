#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 最终验证修复状态
清理条件: 长期保留，用于验证

最终修复状态验证工具
==================

在qc_env环境中验证异步Playwright修复的最终效果。
"""

import os
import sys
import time
import psutil

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.services.async_appeal_adapter import health_check_sync
from src.qianchuan_aw.services.system_protection import get_protection_status


class FinalFixVerification:
    """最终修复验证器"""
    
    def __init__(self):
        self.verification_results = []
    
    def log_result(self, test: str, status: str, message: str, details: dict = None):
        """记录验证结果"""
        result = {
            'test': test,
            'status': status,
            'message': message,
            'details': details or {},
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.verification_results.append(result)
        
        if status == 'pass':
            logger.info(f"✅ {test}: {message}")
        elif status == 'warning':
            logger.warning(f"⚠️ {test}: {message}")
        else:
            logger.error(f"❌ {test}: {message}")
    
    def verify_system_resources(self):
        """验证系统资源状态"""
        logger.info("🔍 验证系统资源状态...")
        
        try:
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=2)
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            
            # 检查浏览器进程数
            browser_count = self._count_browser_processes()
            
            # 检查Python进程数
            python_count = self._count_python_processes()
            
            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'browser_processes': browser_count,
                'python_processes': python_count,
                'memory_available_gb': memory.available / (1024**3)
            }
            
            # 评估系统状态
            if cpu_percent < 30 and memory.percent < 50 and browser_count < 10:
                self.log_result("系统资源状态", "pass", 
                              f"系统资源正常 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, 浏览器: {browser_count}",
                              details)
                return True
            elif cpu_percent < 50 and memory.percent < 70 and browser_count < 20:
                self.log_result("系统资源状态", "warning",
                              f"系统资源可接受 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, 浏览器: {browser_count}",
                              details)
                return True
            else:
                self.log_result("系统资源状态", "fail",
                              f"系统资源过高 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, 浏览器: {browser_count}",
                              details)
                return False
                
        except Exception as e:
            self.log_result("系统资源状态", "fail", f"检查失败: {e}")
            return False
    
    def _count_browser_processes(self):
        """统计浏览器进程"""
        try:
            browser_count = 0
            browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
            
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        browser_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return browser_count
        except:
            return 0
    
    def _count_python_processes(self):
        """统计Python进程"""
        try:
            python_count = 0
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if 'python' in proc_name:
                        python_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return python_count
        except:
            return 0
    
    def verify_async_services(self):
        """验证异步服务状态"""
        logger.info("🔍 验证异步服务状态...")
        
        try:
            # 检查异步提审服务健康状态
            health = health_check_sync()
            
            if health.get('healthy', False):
                details = {
                    'cpu_percent': health.get('cpu_percent', 0),
                    'memory_percent': health.get('memory_percent', 0),
                    'browser_processes': health.get('browser_processes', 0)
                }
                self.log_result("异步提审服务", "pass", "异步提审服务健康", details)
                return True
            else:
                error_msg = health.get('error', '未知错误')
                if '服务未初始化' in error_msg:
                    self.log_result("异步提审服务", "warning", "服务未初始化（正常，需要实际使用时才初始化）")
                    return True
                else:
                    self.log_result("异步提审服务", "fail", f"服务异常: {error_msg}")
                    return False
                    
        except Exception as e:
            self.log_result("异步提审服务", "fail", f"检查失败: {e}")
            return False
    
    def verify_system_protection(self):
        """验证系统保护机制"""
        logger.info("🔍 验证系统保护机制...")
        
        try:
            status = get_protection_status()
            
            protection_enabled = status.get('protection_enabled', False)
            emergency_mode = status.get('emergency_mode', False)
            
            monitor_stats = status.get('monitor_stats', {})
            monitoring_active = monitor_stats.get('monitoring', False)
            
            details = {
                'protection_enabled': protection_enabled,
                'emergency_mode': emergency_mode,
                'monitoring_active': monitoring_active,
                'total_checks': monitor_stats.get('total_checks', 0)
            }
            
            if protection_enabled and not emergency_mode:
                self.log_result("系统保护机制", "pass", "系统保护机制正常运行", details)
                return True
            elif protection_enabled and emergency_mode:
                self.log_result("系统保护机制", "warning", "系统保护机制运行中，当前处于紧急模式", details)
                return True
            else:
                self.log_result("系统保护机制", "fail", "系统保护机制未启用", details)
                return False
                
        except Exception as e:
            self.log_result("系统保护机制", "fail", f"检查失败: {e}")
            return False
    
    def verify_configuration(self):
        """验证配置文件"""
        logger.info("🔍 验证配置文件...")
        
        try:
            config_file = os.path.join(project_root, 'config', 'settings.yml')
            
            if not os.path.exists(config_file):
                self.log_result("配置文件", "fail", "配置文件不存在")
                return False
            
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置
            checks = {
                'plan_appeal_enabled': 'enabled: true' in content and 'plan_appeal:' in content,
                'async_playwright_config': 'async_playwright:' in content,
                'system_protection_config': 'system_protection:' in content
            }
            
            all_passed = all(checks.values())
            
            if all_passed:
                self.log_result("配置文件", "pass", "所有关键配置已正确设置", checks)
                return True
            else:
                missing = [k for k, v in checks.items() if not v]
                self.log_result("配置文件", "fail", f"缺少配置: {missing}", checks)
                return False
                
        except Exception as e:
            self.log_result("配置文件", "fail", f"检查失败: {e}")
            return False
    
    def verify_new_services_available(self):
        """验证新服务可用性"""
        logger.info("🔍 验证新服务可用性...")
        
        try:
            # 尝试导入新服务
            services_to_check = [
                ('异步CopilotService', 'src.qianchuan_aw.services.async_copilot_service'),
                ('异步AppealService', 'src.qianchuan_aw.services.async_appeal_service'),
                ('异步适配器', 'src.qianchuan_aw.services.async_appeal_adapter'),
                ('资源监控器', 'src.qianchuan_aw.services.resource_monitor'),
                ('系统保护', 'src.qianchuan_aw.services.system_protection')
            ]
            
            import_results = {}
            all_imported = True
            
            for service_name, module_name in services_to_check:
                try:
                    __import__(module_name)
                    import_results[service_name] = True
                except ImportError as e:
                    import_results[service_name] = False
                    all_imported = False
                    logger.error(f"导入 {service_name} 失败: {e}")
            
            if all_imported:
                self.log_result("新服务可用性", "pass", "所有新服务可正常导入", import_results)
                return True
            else:
                failed_services = [k for k, v in import_results.items() if not v]
                self.log_result("新服务可用性", "fail", f"服务导入失败: {failed_services}", import_results)
                return False
                
        except Exception as e:
            self.log_result("新服务可用性", "fail", f"检查失败: {e}")
            return False
    
    def verify_monitoring_tools(self):
        """验证监控工具"""
        logger.info("🔍 验证监控工具...")
        
        try:
            # 检查监控脚本是否存在
            monitor_script = os.path.join(project_root, 'tools', 'monitor_system_status.py')
            
            if not os.path.exists(monitor_script):
                self.log_result("监控工具", "fail", "监控脚本不存在")
                return False
            
            # 检查脚本是否可执行（简单语法检查）
            with open(monitor_script, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            # 基本检查
            has_main = 'def main():' in script_content
            has_health_check = 'health_check_sync' in script_content
            has_protection_status = 'get_protection_status' in script_content
            
            details = {
                'script_exists': True,
                'has_main_function': has_main,
                'has_health_check': has_health_check,
                'has_protection_status': has_protection_status
            }
            
            if has_main and has_health_check and has_protection_status:
                self.log_result("监控工具", "pass", "监控工具完整可用", details)
                return True
            else:
                self.log_result("监控工具", "warning", "监控工具存在但可能不完整", details)
                return True
                
        except Exception as e:
            self.log_result("监控工具", "fail", f"检查失败: {e}")
            return False
    
    def generate_final_report(self):
        """生成最终验证报告"""
        logger.info("📋 生成最终验证报告...")
        
        pass_count = sum(1 for r in self.verification_results if r['status'] == 'pass')
        warning_count = sum(1 for r in self.verification_results if r['status'] == 'warning')
        fail_count = sum(1 for r in self.verification_results if r['status'] == 'fail')
        total_count = len(self.verification_results)
        
        report = f"""
千川自动化异步Playwright修复最终验证报告
=====================================

验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
验证环境: qc_env虚拟环境

验证结果概览:
- 总验证项: {total_count}
- 通过项: {pass_count}
- 警告项: {warning_count}
- 失败项: {fail_count}
- 通过率: {pass_count/max(total_count, 1)*100:.1f}%

详细验证结果:
"""
        
        for result in self.verification_results:
            if result['status'] == 'pass':
                status_icon = "✅"
            elif result['status'] == 'warning':
                status_icon = "⚠️"
            else:
                status_icon = "❌"
            
            report += f"{status_icon} {result['test']}: {result['message']}\n"
            
            # 添加详细信息
            if result['details']:
                for key, value in result['details'].items():
                    report += f"   - {key}: {value}\n"
        
        report += f"""
修复效果总结:
"""
        
        if fail_count == 0:
            if warning_count == 0:
                report += "🎉 修复完全成功！所有验证项通过，系统已完全恢复正常。\n"
                report += "✅ 浏览器过载问题已彻底解决，提审功能可以安全使用。\n"
            else:
                report += "✅ 修复基本成功！核心功能正常，有少量警告项需要关注。\n"
                report += "✅ 浏览器过载问题已解决，提审功能可以使用。\n"
        elif fail_count <= total_count * 0.2:
            report += "⚠️ 修复大部分成功，少量项目需要进一步处理。\n"
            report += "⚠️ 建议解决失败项后再大规模使用提审功能。\n"
        else:
            report += "❌ 修复存在较多问题，需要进一步排查和修复。\n"
            report += "❌ 不建议使用提审功能，直到所有问题解决。\n"
        
        report += f"""
使用建议:
1. 定期运行监控脚本: python tools/monitor_system_status.py
2. 关注系统资源使用情况，特别是浏览器进程数量
3. 如果发现异常，立即运行紧急清理脚本
4. 系统保护机制已启动，会自动防止过载重现

技术改进总结:
✅ 异步Playwright API - 解决了同步API在异步环境中的问题
✅ 浏览器会话池 - 限制并发浏览器数量，防止资源耗尽
✅ 系统保护机制 - 实时监控资源使用，自动触发保护措施
✅ 向后兼容适配器 - 无需修改现有代码即可使用新功能
✅ 完善的监控工具 - 提供实时状态监控和问题诊断
"""
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_reports', 'verification', f'final_fix_verification_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 最终验证报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚀 开始最终修复状态验证...")
    
    verifier = FinalFixVerification()
    
    try:
        logger.info("=" * 60)
        logger.info("千川自动化异步Playwright修复最终验证")
        logger.info("=" * 60)
        
        # 执行所有验证
        verifier.verify_system_resources()
        verifier.verify_async_services()
        verifier.verify_system_protection()
        verifier.verify_configuration()
        verifier.verify_new_services_available()
        verifier.verify_monitoring_tools()
        
        # 生成最终报告
        logger.info("=" * 60)
        report = verifier.generate_final_report()
        
        logger.info("🎯 最终验证完成！")
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
