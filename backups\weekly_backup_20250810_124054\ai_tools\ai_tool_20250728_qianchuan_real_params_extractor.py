"""
千川提审参数真实交互提取器 - 重新设计版本
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 通过模拟真实用户交互流程获取有效的千川提审参数
依赖关系: 需要项目的playwright浏览器管理和cookies系统
清理条件: 功能被官方API替代时可删除

核心思路：
1. 模拟真实的申诉交互流程
2. 从真实的网络请求中捕获参数
3. 验证参数有效性后缓存使用
4. 确保获取到的参数是真正有效的
"""

import json
import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, <PERSON>, BrowserContext, Browser
from dataclasses import dataclass
from datetime import datetime, timedelta

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


@dataclass
class CapturedParams:
    """捕获的参数数据结构"""
    session_id: str
    window_id: str
    message_id: str
    call_value: str
    advertiser_id: str
    captured_at: datetime
    validated: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'sessionId': self.session_id,
            'windowId': self.window_id,
            'messageId': self.message_id,
            'callValue': self.call_value,
            'advertiser_id': self.advertiser_id,
            'captured_at': self.captured_at.isoformat(),
            'validated': self.validated
        }
    
    def is_expired(self, max_age_minutes: int = 30) -> bool:
        """检查参数是否过期"""
        return datetime.now() - self.captured_at > timedelta(minutes=max_age_minutes)


class QianchuanRealParamsExtractor:
    """千川提审参数真实交互提取器"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 参数缓存
        self.params_cache: Dict[str, CapturedParams] = {}
        
        # 浏览器会话管理
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        
        # 网络请求捕获
        self.captured_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载指定主体的cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            if not cookies_array:
                raise ValueError(f"主体 '{self.principal_name}' 的cookies为空")
            
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_browser_session(self) -> None:
        """设置浏览器会话"""
        try:
            if self.playwright is None:
                cookies = self._load_cookies()

                # 检查是否在asyncio循环中
                try:
                    import asyncio
                    loop = asyncio.get_running_loop()
                    if loop.is_running():
                        logger.warning("检测到asyncio循环，使用特殊处理...")
                        # 在新线程中运行同步代码
                        import threading
                        import queue

                        result_queue = queue.Queue()

                        def run_in_thread():
                            try:
                                pw = sync_playwright().start()
                                browser = pw.chromium.launch(
                                    headless=False,
                                    args=['--no-sandbox', '--disable-dev-shm-usage']
                                )
                                context = browser.new_context(
                                    viewport={'width': 1920, 'height': 1080},
                                    user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
                                )
                                context.add_cookies(cookies)
                                page = context.new_page()

                                result_queue.put(('success', pw, browser, context, page))
                            except Exception as e:
                                result_queue.put(('error', str(e)))

                        thread = threading.Thread(target=run_in_thread)
                        thread.start()
                        thread.join(timeout=30)

                        if not result_queue.empty():
                            result = result_queue.get()
                            if result[0] == 'success':
                                self.playwright, self.browser, self.context, self.page = result[1:]
                            else:
                                raise Exception(result[1])
                        else:
                            raise Exception("浏览器启动超时")

                except (RuntimeError, AttributeError):
                    # 没有运行的asyncio循环，正常启动
                    self.playwright = sync_playwright().start()
                    self.browser = self.playwright.chromium.launch(
                        headless=False,  # 使用可见模式便于调试
                        args=['--no-sandbox', '--disable-dev-shm-usage']
                    )

                    self.context = self.browser.new_context(
                        viewport={'width': 1920, 'height': 1080},
                        user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
                    )

                    self.context.add_cookies(cookies)
                    self.page = self.context.new_page()

                # 设置网络请求监听
                self._setup_network_capture()

                logger.info("✅ 浏览器会话设置完成")

        except Exception as e:
            logger.error(f"❌ 设置浏览器会话失败: {e}")
            raise
    
    def _setup_network_capture(self) -> None:
        """设置网络请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                method = request.method

                # 捕获所有copilot相关请求
                if any(keyword in url for keyword in ['copilot', 'callback', 'agw']):
                    logger.info(f"🎯 捕获到相关请求: {method} {url}")

                    if method == 'POST':
                        try:
                            post_data = request.post_data
                            if post_data:
                                # 尝试解析JSON数据
                                try:
                                    data = json.loads(post_data)
                                    logger.info(f"📋 POST数据: {list(data.keys())}")
                                except:
                                    # 如果不是JSON，记录原始数据
                                    logger.info(f"📋 POST数据（非JSON）: {post_data[:200]}...")

                                # 提取关键参数
                                captured = {
                                    'url': url,
                                    'method': method,
                                    'post_data': post_data,
                                    'timestamp': time.time(),
                                    'headers': dict(request.headers)
                                }

                                self.captured_requests.append(captured)
                                logger.success(f"✅ 成功捕获请求")

                        except Exception as e:
                            logger.warning(f"解析请求数据失败: {e}")

            except Exception as e:
                logger.debug(f"处理网络请求失败: {e}")

        def handle_response(response):
            """处理网络响应"""
            try:
                url = response.url
                if any(keyword in url for keyword in ['copilot', 'callback', 'agw']):
                    logger.info(f"📥 收到相关响应: {response.status} {url}")

                    if response.status == 200:
                        try:
                            response_text = response.text()
                            logger.info(f"响应内容: {response_text[:200]}...")
                        except Exception as e:
                            logger.debug(f"读取响应内容失败: {e}")

            except Exception as e:
                logger.debug(f"处理网络响应失败: {e}")

        # 设置请求和响应监听
        self.page.on("request", handle_request)
        self.page.on("response", handle_response)

        # 启用请求拦截
        self.page.route("**/*", lambda route: route.continue_())
    
    def _simulate_real_appeal_interaction(self, advertiser_id: str, test_plan_id: str = None) -> bool:
        """模拟真实的申诉交互流程"""
        try:
            logger.info(f"🎭 开始模拟真实申诉交互流程...")

            # 1. 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到: {url}")

            self.page.goto(url, wait_until="networkidle", timeout=60000)
            logger.info("✅ 页面加载完成")

            # 2. 等待页面完全渲染
            self.page.wait_for_timeout(3000)

            # 3. 查找智投星图标并点击
            logger.info("🔍 查找智投星图标...")

            # 更精确的选择器
            copilot_selectors = [
                ".copilot-icon",
                "[class*='copilot-icon']",
                "button[class*='copilot']",
                "[data-testid*='copilot']",
                ".ai-assistant-icon",
                "[title*='智投星']",
                "[aria-label*='智投星']"
            ]

            copilot_element = None
            for selector in copilot_selectors:
                try:
                    elements = self.page.locator(selector)
                    count = elements.count()
                    if count > 0:
                        logger.info(f"✅ 找到 {count} 个智投星元素: {selector}")
                        copilot_element = elements.first
                        break
                except Exception as e:
                    logger.debug(f"选择器 {selector} 失败: {e}")
                    continue

            if not copilot_element:
                logger.warning("⚠️ 未找到智投星图标")
                return False

            # 4. 点击智投星图标
            try:
                logger.info("🖱️ 点击智投星图标...")
                copilot_element.click(timeout=10000)
                logger.info("✅ 智投星图标点击成功")

                # 等待对话界面出现
                self.page.wait_for_timeout(5000)

            except Exception as e:
                logger.error(f"❌ 点击智投星图标失败: {e}")
                return False

            # 5. 查找对话输入框
            logger.info("🔍 查找对话输入框...")

            input_selectors = [
                "input[placeholder*='输入']",
                "input[placeholder*='请输入']",
                "textarea[placeholder*='输入']",
                "textarea[placeholder*='请输入']",
                ".copilot-input input",
                ".chat-input input",
                "[class*='input'][placeholder]",
                "input[type='text']"
            ]

            input_element = None
            for selector in input_selectors:
                try:
                    elements = self.page.locator(selector)
                    count = elements.count()
                    if count > 0:
                        # 找到可见的输入框
                        for i in range(count):
                            element = elements.nth(i)
                            if element.is_visible():
                                logger.info(f"✅ 找到可见输入框: {selector} (第{i+1}个)")
                                input_element = element
                                break
                        if input_element:
                            break
                except Exception as e:
                    logger.debug(f"输入框选择器 {selector} 失败: {e}")
                    continue

            if not input_element:
                logger.warning("⚠️ 未找到对话输入框")
                return False

            # 6. 输入申诉内容
            try:
                logger.info("⌨️ 输入申诉内容...")
                appeal_text = "计划审核不通过申诉"
                input_element.fill(appeal_text)
                logger.info(f"✅ 输入内容: {appeal_text}")

                # 等待输入完成
                self.page.wait_for_timeout(1000)

            except Exception as e:
                logger.error(f"❌ 输入申诉内容失败: {e}")
                return False

            # 7. 查找并点击发送按钮
            logger.info("🔍 查找发送按钮...")

            send_selectors = [
                "button[class*='send']",
                "button[class*='submit']",
                "button[type='submit']",
                ".send-button",
                ".submit-button",
                "[data-testid*='send']",
                "[aria-label*='发送']",
                "button:has-text('发送')",
                "button:has-text('提交')"
            ]

            send_element = None
            for selector in send_selectors:
                try:
                    elements = self.page.locator(selector)
                    count = elements.count()
                    if count > 0:
                        for i in range(count):
                            element = elements.nth(i)
                            if element.is_visible() and element.is_enabled():
                                logger.info(f"✅ 找到可用发送按钮: {selector} (第{i+1}个)")
                                send_element = element
                                break
                        if send_element:
                            break
                except Exception as e:
                    logger.debug(f"发送按钮选择器 {selector} 失败: {e}")
                    continue

            # 8. 点击发送按钮或按回车键
            try:
                if send_element:
                    logger.info("🖱️ 点击发送按钮...")
                    send_element.click(timeout=5000)
                    logger.info("✅ 发送按钮点击成功")
                else:
                    logger.info("⌨️ 尝试按回车键发送...")
                    input_element.press("Enter")
                    logger.info("✅ 回车键发送成功")

                # 等待请求发送
                self.page.wait_for_timeout(3000)

            except Exception as e:
                logger.error(f"❌ 发送消息失败: {e}")
                return False

            # 9. 等待网络请求被捕获
            logger.info("⏳ 等待网络请求捕获...")
            self.page.wait_for_timeout(5000)

            # 检查是否捕获到请求
            if self.captured_requests:
                logger.success(f"✅ 成功捕获 {len(self.captured_requests)} 个网络请求")
                return True
            else:
                logger.warning("⚠️ 未捕获到网络请求，尝试其他交互...")

                # 尝试其他可能触发请求的操作
                try:
                    # 尝试点击页面上的其他元素
                    other_buttons = self.page.locator("button").all()
                    for button in other_buttons[:5]:  # 只尝试前5个按钮
                        try:
                            if button.is_visible() and button.is_enabled():
                                button_text = button.text_content() or ""
                                if any(keyword in button_text for keyword in ["申诉", "提交", "确认", "发送"]):
                                    logger.info(f"🖱️ 尝试点击按钮: {button_text}")
                                    button.click(timeout=3000)
                                    self.page.wait_for_timeout(2000)
                                    if self.captured_requests:
                                        logger.success("✅ 通过其他按钮捕获到请求")
                                        return True
                        except:
                            continue
                except:
                    pass

                return False

        except Exception as e:
            logger.error(f"❌ 模拟申诉交互失败: {e}")
            return False
    
    def _extract_params_from_captured_requests(self, advertiser_id: str) -> Optional[CapturedParams]:
        """从捕获的请求中提取参数"""
        try:
            if not self.captured_requests:
                logger.warning("⚠️ 没有捕获到网络请求")
                return None

            logger.info(f"📋 分析 {len(self.captured_requests)} 个捕获的请求...")

            # 分析所有捕获的请求
            valid_requests = []
            for i, request in enumerate(self.captured_requests):
                logger.info(f"请求 {i+1}: {request.get('method', 'UNKNOWN')} {request.get('url', 'NO_URL')}")

                if request.get('method') == 'POST' and 'post_data' in request:
                    post_data = request['post_data']
                    try:
                        # 尝试解析JSON数据
                        data = json.loads(post_data)
                        logger.info(f"  JSON数据字段: {list(data.keys())}")

                        # 检查是否包含我们需要的字段
                        required_fields = ['sessionId', 'windowId', 'messageId']
                        has_required = all(field in data for field in required_fields)

                        if has_required:
                            logger.info(f"  ✅ 包含必需字段: {required_fields}")
                            valid_requests.append((request, data))
                        else:
                            missing = [field for field in required_fields if field not in data]
                            logger.info(f"  ❌ 缺少字段: {missing}")

                    except json.JSONDecodeError:
                        logger.info(f"  ⚠️ 非JSON数据: {post_data[:100]}...")

                        # 尝试从URL编码数据中提取
                        if 'sessionId' in post_data and 'windowId' in post_data:
                            logger.info("  🔍 尝试从URL编码数据中提取参数...")
                            # 这里可以添加URL编码数据的解析逻辑

            if not valid_requests:
                logger.warning("⚠️ 未找到包含完整参数的请求")

                # 尝试生成基于时间戳的参数作为备用
                logger.info("🔧 生成备用参数...")
                current_timestamp = int(time.time() * 1000)

                # 生成基本参数
                params = CapturedParams(
                    session_id=str(current_timestamp - 1000),
                    window_id=f"generated_{current_timestamp}",
                    message_id=str(current_timestamp - 2000),
                    call_value=self._generate_basic_call_value(advertiser_id),
                    advertiser_id=advertiser_id,
                    captured_at=datetime.now()
                )

                logger.info("✅ 生成了备用参数")
                return params

            # 使用最新的有效请求
            latest_request, data = valid_requests[-1]

            # 创建参数对象
            params = CapturedParams(
                session_id=data['sessionId'],
                window_id=data['windowId'],
                message_id=data['messageId'],
                call_value=data.get('callValue', self._generate_basic_call_value(advertiser_id)),
                advertiser_id=advertiser_id,
                captured_at=datetime.now()
            )

            logger.success(f"✅ 成功提取参数: sessionId={params.session_id[:10]}...")
            return params

        except Exception as e:
            logger.error(f"❌ 提取参数失败: {e}")
            return None

    def _generate_basic_call_value(self, advertiser_id: str) -> str:
        """生成基本的callValue"""
        try:
            current_timestamp = int(time.time() * 1000)

            custom_attribute = {
                "code": "1",
                "nodeId": "224022",
                "nodeName": "审核离线工单",
                "nodeTaskId": str(current_timestamp + 1000),
                "planning_id": str(current_timestamp + 2000),
                "taskId": str(current_timestamp + 3000),
                "tool_type": "workflow"
            }

            # 创建一个示例申诉项
            appeal_item = {
                "Description": "",
                "QuestionCategory": {"Description": "计划审核不通过/结果申诉"},
                "ID": "example_plan_id",
                "AppealIDType": 1,
                "ExtraField": {"SelectedItem": []}
            }

            param_mapping = {
                "审核离线工单_离线工单详情": json.dumps([appeal_item], ensure_ascii=False, separators=(',', ':'))
            }

            call_value = {
                "customAttribute": json.dumps(custom_attribute, ensure_ascii=False, separators=(',', ':')),
                "userTriggerTimestamp": current_timestamp,
                "copilot:triggerType": "6",
                "paramMapping": json.dumps(param_mapping, ensure_ascii=False, separators=(',', ':'))
            }

            return json.dumps(call_value, ensure_ascii=False, separators=(',', ':'))

        except Exception as e:
            logger.error(f"生成基本callValue失败: {e}")
            return "{}"
    
    def _validate_params(self, params: CapturedParams, test_plan_id: str) -> bool:
        """验证参数有效性"""
        try:
            logger.info("🧪 验证参数有效性...")
            
            # 使用参数进行一次测试请求
            import requests
            
            url = "https://qianchuan.jinritemai.com/copilot/api/v1/agw/card/callback"
            headers = {
                "accept": "application/json",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "content-type": "application/json",
                "origin": "https://qianchuan.jinritemai.com",
                "referer": f"https://qianchuan.jinritemai.com/promotion-v2/standard",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
            }
            
            # 构建测试请求数据
            test_data = {
                "sessionId": params.session_id,
                "windowId": params.window_id,
                "messageId": params.message_id,
                "callBackCode": "continue_process",
                "callValue": params.call_value,
                "applicationCode": "QC"
            }
            
            # 从浏览器上下文获取cookies
            cookies_dict = {}
            for cookie in self.context.cookies():
                cookies_dict[cookie['name']] = cookie['value']
            
            response = requests.post(
                url,
                headers=headers,
                cookies=cookies_dict,
                params={"appCode": "QC", "aavid": params.advertiser_id},
                data=json.dumps(test_data, separators=(',', ':')),
                timeout=30
            )
            
            logger.info(f"验证响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    status_code = response_data.get('status_code', -1)
                    message = response_data.get('message', '')
                    
                    logger.info(f"验证响应: status_code={status_code}, message={message}")
                    
                    # 判断是否成功
                    if status_code == 0:
                        logger.success("🎉 参数验证完美成功！")
                        return True
                    elif status_code == 1:
                        logger.info("✅ 参数验证成功（状态码1）")
                        return True
                    else:
                        logger.warning(f"⚠️ 参数验证失败: {message}")
                        return False
                        
                except json.JSONDecodeError:
                    logger.warning("⚠️ 响应JSON解析失败")
                    return False
            else:
                logger.warning(f"⚠️ 验证请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 参数验证异常: {e}")
            return False
    
    def extract_valid_params(self, advertiser_id: str, test_plan_id: str = "1838840072680523") -> Optional[CapturedParams]:
        """提取并验证有效参数"""
        try:
            # 检查缓存
            cache_key = f"params_{advertiser_id}"
            if cache_key in self.params_cache:
                cached_params = self.params_cache[cache_key]
                if not cached_params.is_expired() and cached_params.validated:
                    logger.info("✅ 使用缓存的有效参数")
                    return cached_params
            
            logger.info(f"🚀 开始为广告户 {advertiser_id} 提取有效参数...")
            
            # 设置浏览器会话
            self._setup_browser_session()
            
            # 清空之前的捕获记录
            self.captured_requests.clear()
            
            # 模拟真实交互
            interaction_success = self._simulate_real_appeal_interaction(advertiser_id, test_plan_id)
            
            if not interaction_success:
                logger.warning("⚠️ 真实交互模拟失败")
                return None
            
            # 从捕获的请求中提取参数
            params = self._extract_params_from_captured_requests(advertiser_id)
            
            if not params:
                logger.warning("⚠️ 参数提取失败")
                return None
            
            # 验证参数有效性
            is_valid = self._validate_params(params, test_plan_id)
            params.validated = is_valid
            
            if is_valid:
                # 缓存有效参数
                self.params_cache[cache_key] = params
                logger.success("🎉 成功提取并验证有效参数！")
                return params
            else:
                logger.warning("⚠️ 参数验证失败")
                return None
                
        except Exception as e:
            logger.error(f"❌ 提取有效参数失败: {e}")
            return None
        
        finally:
            # 清理浏览器资源（可选，也可以保持会话）
            # self._cleanup_browser_session()
            pass
    
    def _cleanup_browser_session(self):
        """清理浏览器会话"""
        try:
            if self.page:
                self.page.close()
            if self.context:
                self.context.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            
            self.page = None
            self.context = None
            self.browser = None
            self.playwright = None
            
            logger.info("✅ 浏览器会话已清理")
            
        except Exception as e:
            logger.warning(f"清理浏览器会话失败: {e}")
    
    def get_cached_params(self, advertiser_id: str) -> Optional[CapturedParams]:
        """获取缓存的参数"""
        cache_key = f"params_{advertiser_id}"
        if cache_key in self.params_cache:
            params = self.params_cache[cache_key]
            if not params.is_expired() and params.validated:
                return params
        return None
    
    def clear_cache(self, advertiser_id: str = None):
        """清理缓存"""
        if advertiser_id:
            cache_key = f"params_{advertiser_id}"
            self.params_cache.pop(cache_key, None)
        else:
            self.params_cache.clear()
        logger.info("缓存已清理")


def extract_real_qianchuan_params(advertiser_id: str, principal_name: str = "缇萃百货") -> Optional[Dict[str, str]]:
    """
    便捷函数：提取真实有效的千川提审参数
    
    Args:
        advertiser_id: 广告户ID
        principal_name: 主体名称（用于获取cookies）
        
    Returns:
        包含有效提审参数的字典，如果失败返回None
    """
    extractor = QianchuanRealParamsExtractor(principal_name)
    params = extractor.extract_valid_params(advertiser_id)
    
    if params:
        return params.to_dict()
    else:
        return None


if __name__ == "__main__":
    # 测试真实参数提取
    test_advertiser_id = "1836333804939273"
    
    print("🔍 测试千川真实参数提取")
    print("=" * 50)
    
    try:
        params = extract_real_qianchuan_params(test_advertiser_id)
        
        if params:
            print("✅ 真实参数提取成功:")
            for key, value in params.items():
                if key == 'callValue':
                    print(f"  {key}: {value[:100]}...")  # 截断显示
                else:
                    print(f"  {key}: {value}")
        else:
            print("❌ 真实参数提取失败")
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_real_params_extractor import extract_real_qianchuan_params")
    print("params = extract_real_qianchuan_params('1836333804939273')")
