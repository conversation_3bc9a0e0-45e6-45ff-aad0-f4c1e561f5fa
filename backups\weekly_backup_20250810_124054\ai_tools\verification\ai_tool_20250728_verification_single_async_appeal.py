#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 单个计划异步提审测试（调试版本）
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import asyncio
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def get_target_plan():
    """获取目标测试计划"""
    logger.info("🔍 查找目标测试计划...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 查找最新的计划
        cursor.execute("""
            SELECT 
                c.campaign_id_qc,
                c.status,
                c.appeal_status,
                c.appeal_error_message,
                c.created_at,
                a.account_id_qc,
                p.name as principal_name
            FROM campaigns c
            JOIN ad_accounts a ON c.account_id = a.id
            JOIN principals p ON a.principal_id = p.id
            WHERE c.created_at >= CURRENT_DATE
            AND c.status = 'AUDITING'
            ORDER BY c.created_at DESC
            LIMIT 1
        """)
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            campaign_id_qc, status, appeal_status, appeal_error_message, created_at, account_id_qc, principal_name = result
            
            plan_info = {
                'campaign_id': campaign_id_qc,
                'status': status,
                'appeal_status': appeal_status,
                'appeal_error_message': appeal_error_message,
                'created_at': created_at,
                'account_id': account_id_qc,
                'principal_name': principal_name
            }
            
            logger.success(f"✅ 找到目标计划: {campaign_id_qc}")
            logger.info(f"   👤 主体: {principal_name}")
            logger.info(f"   🏢 账户: {account_id_qc}")
            logger.info(f"   📊 状态: {status}")
            logger.info(f"   📤 提审状态: {appeal_status}")
            logger.info(f"   📅 创建时间: {created_at}")
            
            return plan_info
        else:
            logger.error("❌ 没有找到合适的测试计划")
            return None
            
    except Exception as e:
        logger.error(f"❌ 查找目标计划失败: {e}")
        return None

def reset_plan_for_async_test(campaign_id):
    """重置计划状态用于异步测试"""
    logger.info(f"🔄 重置计划状态: {campaign_id}")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 重置提审状态
        cursor.execute("""
            UPDATE campaigns 
            SET appeal_status = NULL,
                appeal_error_message = NULL,
                first_appeal_at = NULL
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        affected_rows = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        if affected_rows > 0:
            logger.success(f"✅ 计划状态已重置: {campaign_id}")
            return True
        else:
            logger.warning(f"⚠️ 没有找到需要重置的计划: {campaign_id}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 重置计划状态失败: {e}")
        return False

async def test_async_copilot_session_step_by_step(plan_info):
    """逐步测试AsyncCopilotSession"""
    logger.info("🧪 逐步测试AsyncCopilotSession...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_copilot_service import AsyncCopilotSession
        
        app_settings = load_config()
        session_id = f"debug_session_{int(time.time())}"
        
        logger.info(f"1. 创建AsyncCopilotSession实例...")
        session = AsyncCopilotSession(
            session_id, 
            plan_info['principal_name'], 
            plan_info['account_id'], 
            app_settings
        )
        logger.success("✅ AsyncCopilotSession实例创建成功")
        
        logger.info(f"2. 开始初始化会话...")
        try:
            await session.initialize()
            logger.success("✅ AsyncCopilotSession初始化成功")
        except Exception as init_error:
            logger.error(f"❌ 初始化失败: {init_error}")
            return False, f"初始化失败: {init_error}"
        
        logger.info(f"3. 检查会话状态...")
        if not session.is_initialized:
            logger.error("❌ 会话未正确初始化")
            return False, "会话未正确初始化"
        
        if session.page is None:
            logger.error("❌ 页面对象为None")
            return False, "页面对象为None"
        
        logger.success("✅ 会话状态检查通过")
        
        logger.info(f"4. 尝试执行提审...")
        try:
            result = await session.appeal_via_text_command(plan_info['campaign_id'])
            logger.info(f"📊 提审结果: {result}")
            
            if result.get('success'):
                logger.success(f"✅ 异步提审成功: {plan_info['campaign_id']}")
                success_result = True, result.get('message', '提审成功')
            else:
                error_msg = result.get('error', result.get('message', '未知错误'))
                logger.error(f"❌ 异步提审失败: {error_msg}")
                success_result = False, error_msg
                
        except Exception as appeal_error:
            logger.error(f"❌ 提审执行异常: {appeal_error}")
            success_result = False, f"提审执行异常: {appeal_error}"
        
        logger.info(f"5. 清理会话...")
        try:
            await session.cleanup()
            logger.success("✅ 会话清理成功")
        except Exception as cleanup_error:
            logger.warning(f"⚠️ 会话清理异常: {cleanup_error}")
        
        return success_result
        
    except Exception as e:
        logger.error(f"❌ 逐步测试失败: {e}")
        return False, str(e)

async def test_async_appeal_service_direct(plan_info):
    """直接测试AsyncAppealService"""
    logger.info("🧪 直接测试AsyncAppealService...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_appeal_service import AsyncAppealService
        
        app_settings = load_config()
        
        logger.info("1. 创建AsyncAppealService实例...")
        service = AsyncAppealService(app_settings)
        logger.success("✅ AsyncAppealService实例创建成功")
        
        logger.info("2. 执行异步提审...")
        result = await service.appeal_plan_async(
            plan_info['principal_name'],
            plan_info['account_id'],
            plan_info['campaign_id']
        )
        
        logger.info(f"📊 AsyncAppealService结果: {result}")
        
        if result.get('success'):
            logger.success(f"✅ AsyncAppealService提审成功")
            return True, result.get('message', '提审成功')
        else:
            error_msg = result.get('error', '未知错误')
            logger.error(f"❌ AsyncAppealService提审失败: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        logger.error(f"❌ AsyncAppealService测试失败: {e}")
        return False, str(e)

def check_final_status(campaign_id, wait_seconds=10):
    """检查最终状态"""
    logger.info(f"🔍 检查最终状态: {campaign_id}")
    
    # 等待一段时间让数据库更新
    logger.info(f"⏳ 等待 {wait_seconds} 秒...")
    time.sleep(wait_seconds)
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                status,
                appeal_status,
                appeal_error_message,
                first_appeal_at
            FROM campaigns 
            WHERE campaign_id_qc = %s
        """, (campaign_id,))
        
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if result:
            status, appeal_status, appeal_error_message, first_appeal_at = result
            
            logger.info("📊 最终状态:")
            logger.info(f"   📋 计划状态: {status}")
            logger.info(f"   📤 提审状态: {appeal_status}")
            logger.info(f"   📅 提审时间: {first_appeal_at}")
            
            if appeal_error_message:
                logger.error(f"   ❌ 错误信息: {appeal_error_message}")
                return False, appeal_error_message
            elif appeal_status == 'appeal_pending':
                logger.success("✅ 提审状态正常：appeal_pending")
                return True, "提审成功，状态已更新"
            elif appeal_status is None:
                logger.warning("⚠️ 提审状态未更新，可能仍在处理中")
                return False, "状态未更新"
            else:
                logger.info(f"📋 当前提审状态: {appeal_status}")
                return True, f"状态: {appeal_status}"
        else:
            logger.error(f"❌ 未找到计划: {campaign_id}")
            return False, "计划不存在"
            
    except Exception as e:
        logger.error(f"❌ 检查状态失败: {e}")
        return False, str(e)

async def main():
    """主函数"""
    logger.info("🎯 开始单个计划异步提审调试测试")
    logger.info("="*80)
    logger.info("🖥️ 此测试将逐步调试异步版本的提审功能")
    logger.info("="*80)
    
    try:
        # 1. 获取目标计划
        plan_info = get_target_plan()
        if not plan_info:
            logger.error("❌ 无法获取目标计划")
            return False
        
        # 2. 重置计划状态
        if not reset_plan_for_async_test(plan_info['campaign_id']):
            logger.error("❌ 无法重置计划状态")
            return False
        
        # 3. 逐步测试AsyncCopilotSession
        logger.info("\n" + "="*50)
        logger.info("📋 测试方法1: AsyncCopilotSession逐步测试")
        logger.info("="*50)
        
        session_success, session_message = await test_async_copilot_session_step_by_step(plan_info)
        
        # 4. 测试AsyncAppealService
        logger.info("\n" + "="*50)
        logger.info("📋 测试方法2: AsyncAppealService直接测试")
        logger.info("="*50)
        
        service_success, service_message = await test_async_appeal_service_direct(plan_info)
        
        # 5. 检查最终状态
        status_success, status_message = check_final_status(plan_info['campaign_id'])
        
        # 生成测试报告
        logger.info("\n" + "="*80)
        logger.info("🎯 单个计划异步提审调试结果")
        logger.info("="*80)
        
        logger.info(f"📋 测试计划: {plan_info['campaign_id']}")
        logger.info(f"🔄 状态重置: ✅ 成功")
        logger.info(f"🧪 AsyncCopilotSession: {'✅' if session_success else '❌'} {session_message}")
        logger.info(f"🧪 AsyncAppealService: {'✅' if service_success else '❌'} {service_message}")
        logger.info(f"📊 最终状态: {'✅' if status_success else '❌'} {status_message}")
        
        # 计算成功率
        tests = [session_success, service_success, status_success]
        success_count = sum(tests)
        total_count = len(tests)
        success_rate = (success_count / total_count) * 100
        
        logger.info(f"\n📈 调试成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 66:
            logger.success("\n🎉 异步版本提审功能基本可用！")
            logger.info("\n📋 调试总结:")
            logger.info("✅ 异步版本核心功能正常")
            logger.info("✅ 问题已定位和修复")
            logger.info("✅ 可以进行批量测试")
        else:
            logger.error("\n❌ 异步版本仍存在问题")
            logger.info("\n📋 问题分析:")
            if not session_success:
                logger.info(f"   ❌ AsyncCopilotSession问题: {session_message}")
            if not service_success:
                logger.info(f"   ❌ AsyncAppealService问题: {service_message}")
            if not status_success:
                logger.info(f"   ❌ 状态更新问题: {status_message}")
        
        return success_rate >= 66
        
    except Exception as e:
        logger.error(f"❌ 调试测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        logger.success("\n🎉 异步版本提审功能调试成功！")
        logger.info("💡 可以继续进行批量异步提审测试")
    else:
        logger.error("\n❌ 异步版本调试失败，需要进一步修复")
    
    sys.exit(0 if success else 1)
