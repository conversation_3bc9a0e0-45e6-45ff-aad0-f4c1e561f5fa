#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复千川自动化项目视频素材处理工作流的关键问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import yaml
import shutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def save_config(config):
    """保存配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    # 备份原配置
    backup_path = config_path.with_suffix(f'.yml.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
    shutil.copy2(config_path, backup_path)
    logger.info(f"配置文件已备份到: {backup_path}")
    
    # 保存新配置
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    logger.success(f"配置文件已更新: {config_path}")

def fix_workflow_config():
    """修复工作流配置"""
    logger.info("🔧 开始修复工作流配置...")
    
    config = load_config()
    workflow = config.get('workflow', {})
    
    # 1. 启用被禁用的关键任务
    critical_fixes = {
        'material_collection': {
            'enabled': True,
            'interval_minutes': 30,
            'archive_folder_name': '已处理',
            'dest_dir': 'G:/workflow_assets/01_materials_to_process/缇萃百货',
            'max_workers': 8,
            'start_hour': 8,
            'start_minute': 30,
            'end_hour': 20,
            'end_minute': 30,
            'video_extensions': ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv']
        },
        'zombie_cleanup': {
            'enabled': True,
            'patrol_interval_minutes': 10,
            'timeout_minutes': 10
        },
        'comment_management': {
            'enabled': True,
            'check_interval_minutes': 5
        },
        'violation_detection': {
            'enabled': True,
            'check_interval_minutes': 30
        }
    }
    
    # 2. 优化任务调度频率
    schedule_optimizations = {
        'plan_creation': {
            'enabled': True,
            'interval_seconds': 120  # 从60调整为120
        },
        'group_dispatch': {
            'enabled': True,
            'interval_seconds': 60   # 从30调整为60
        },
        'file_ingestion': {
            'enabled': True,
            'interval_seconds': 180  # 从300调整为180，提高响应速度
        },
        'material_monitoring': {
            'enabled': True,
            'interval_seconds': 180  # 保持现有设置
        },
        'plan_appeal': {
            'enabled': True,
            'interval_seconds': 900  # 保持现有设置
        }
    }
    
    # 应用修复
    for task_name, task_config in critical_fixes.items():
        workflow[task_name] = {**workflow.get(task_name, {}), **task_config}
        logger.success(f"✅ 已启用关键任务: {task_name}")
    
    for task_name, task_config in schedule_optimizations.items():
        workflow[task_name] = {**workflow.get(task_name, {}), **task_config}
        logger.success(f"⚡ 已优化任务调度: {task_name} -> {task_config['interval_seconds']}秒")
    
    config['workflow'] = workflow
    save_config(config)
    
    return True

def fix_orphaned_materials():
    """修复孤岛素材"""
    logger.info("🔄 开始修复孤岛素材...")
    
    try:
        from qianchuan_aw.database.connection import database_session
        from qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            # 查找pending_upload状态的素材
            orphaned_materials = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value
            ).all()
            
            logger.info(f"发现 {len(orphaned_materials)} 个孤岛素材")
            
            if orphaned_materials:
                # 重置状态为new，让工作流重新处理
                for material in orphaned_materials:
                    material.status = MaterialStatus.NEW.value
                    material.updated_at = datetime.utcnow()
                
                db.commit()
                logger.success(f"✅ 已重置 {len(orphaned_materials)} 个孤岛素材状态为 'new'")
                
                # 记录修复的素材
                for material in orphaned_materials[:10]:  # 只显示前10个
                    logger.info(f"   - {material.filename} (ID: {material.id})")
                
                if len(orphaned_materials) > 10:
                    logger.info(f"   ... 还有 {len(orphaned_materials) - 10} 个素材")
            
            return len(orphaned_materials)
            
    except Exception as e:
        logger.error(f"❌ 修复孤岛素材失败: {e}")
        return 0

def verify_celery_tasks():
    """验证Celery任务状态"""
    logger.info("🔍 验证Celery任务状态...")
    
    try:
        from qianchuan_aw.celery_app import app
        
        # 检查任务注册
        registered_tasks = list(app.tasks.keys())
        critical_tasks = [
            'tasks.ingest_and_upload',
            'tasks.group_and_dispatch', 
            'tasks.create_plans',
            'tasks.appeal_plans',
            'tasks.monitor_materials',
            'tasks.collect_materials',
            'tasks.reset_stale_processing_status'
        ]
        
        missing_tasks = []
        for task in critical_tasks:
            if task in registered_tasks:
                logger.success(f"✅ 任务已注册: {task}")
            else:
                logger.error(f"❌ 任务未注册: {task}")
                missing_tasks.append(task)
        
        # 检查Beat调度配置
        beat_schedule = app.conf.beat_schedule
        logger.info(f"📋 Beat调度配置: {len(beat_schedule)} 个任务")
        
        for task_name, config in beat_schedule.items():
            logger.info(f"   - {task_name}: {config.get('task')} (间隔: {config.get('schedule')})")
        
        return len(missing_tasks) == 0
        
    except Exception as e:
        logger.error(f"❌ 验证Celery任务失败: {e}")
        return False

def create_monitoring_script():
    """创建监控脚本"""
    logger.info("📊 创建工作流监控脚本...")
    
    monitoring_script = '''#!/usr/bin/env python3
"""工作流状态监控脚本"""

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.database.connection import database_session
from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign

def check_workflow_health():
    """检查工作流健康状态"""
    with database_session() as db:
        # 检查各状态素材数量
        status_counts = {}
        for status in [MaterialStatus.NEW.value, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value, MaterialStatus.UPLOAD_FAILED.value]:
            count = db.query(LocalCreative).filter(LocalCreative.status == status).count()
            status_counts[status] = count
        
        print("📊 工作流状态监控报告")
        print("=" * 50)
        for status, count in status_counts.items():
            alert = "🚨" if (status == MaterialStatus.PENDING_UPLOAD.value and count > 50) else "✅"
            print(f"{alert} {status}: {count}")
        
        # 检查平台素材状态
        platform_counts = {}
        for status in ['AUDITING', 'pending']:
            count = db.query(PlatformCreative).filter(PlatformCreative.review_status == status).count()
            platform_counts[status] = count
        
        print("\\n📋 平台素材状态")
        print("-" * 30)
        for status, count in platform_counts.items():
            print(f"   {status}: {count}")

if __name__ == "__main__":
    check_workflow_health()
'''
    
    monitor_path = project_root / 'ai_tools' / 'monitoring' / 'ai_tool_20250728_monitoring_workflow_health.py'
    monitor_path.parent.mkdir(exist_ok=True)
    
    with open(monitor_path, 'w', encoding='utf-8') as f:
        f.write(monitoring_script)
    
    logger.success(f"✅ 监控脚本已创建: {monitor_path}")
    return monitor_path

def main():
    """主修复流程"""
    logger.info("🚀 开始千川工作流全面修复...")
    
    results = {
        'config_fixed': False,
        'orphaned_fixed': 0,
        'celery_verified': False,
        'monitoring_created': False
    }
    
    try:
        # 1. 修复配置
        results['config_fixed'] = fix_workflow_config()
        
        # 2. 修复孤岛素材
        results['orphaned_fixed'] = fix_orphaned_materials()
        
        # 3. 验证Celery任务
        results['celery_verified'] = verify_celery_tasks()
        
        # 4. 创建监控脚本
        monitor_path = create_monitoring_script()
        results['monitoring_created'] = monitor_path is not None
        
        # 总结报告
        logger.info("\\n" + "="*60)
        logger.info("🎯 修复完成总结")
        logger.info("="*60)
        
        if results['config_fixed']:
            logger.success("✅ 工作流配置已修复")
        else:
            logger.error("❌ 工作流配置修复失败")
        
        if results['orphaned_fixed'] > 0:
            logger.success(f"✅ 已修复 {results['orphaned_fixed']} 个孤岛素材")
        else:
            logger.warning("⚠️ 未发现孤岛素材或修复失败")
        
        if results['celery_verified']:
            logger.success("✅ Celery任务验证通过")
        else:
            logger.error("❌ Celery任务验证失败")
        
        if results['monitoring_created']:
            logger.success("✅ 监控脚本已创建")
        
        logger.info("\\n📋 后续操作建议:")
        logger.info("1. 重启Celery Worker和Beat进程")
        logger.info("2. 运行监控脚本检查状态")
        logger.info("3. 观察工作流是否恢复正常")
        
        return all([results['config_fixed'], results['celery_verified']])
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
