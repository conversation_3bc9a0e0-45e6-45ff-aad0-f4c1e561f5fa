#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时测试工具
生命周期: 7天
创建目的: 测试全局账户选择器功能
依赖关系: Streamlit, 全局账户选择器组件
清理条件: 测试完成后可删除
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

def test_global_selector_import():
    """测试全局账户选择器导入"""
    print("🔍 测试全局账户选择器导入...")
    
    try:
        from ai_tool_20250718_maintenance_global_account_selector import (
            render_global_account_selector,
            render_global_favorite_manager,
            get_global_selected_account,
            get_current_account_info,
            require_account_selection,
            get_accounts_with_favorites
        )
        print("✅ 全局账户选择器导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 全局账户选择器导入失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("🗄️ 测试数据库连接...")
    
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import AdAccount
        
        with database_session() as db:
            count = db.query(AdAccount).count()
            print(f"✅ 数据库连接成功，找到 {count} 个广告账户")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_get_accounts_function():
    """测试获取账户函数"""
    print("📋 测试获取账户函数...")
    
    try:
        from ai_tool_20250718_maintenance_global_account_selector import get_accounts_with_favorites
        
        accounts = get_accounts_with_favorites()
        print(f"✅ 成功获取 {len(accounts)} 个账户")
        
        # 检查收藏状态
        favorite_count = len([acc for acc in accounts if getattr(acc, 'is_favorite', False)])
        print(f"   其中收藏账户: {favorite_count} 个")
        
        # 显示前几个账户信息
        if accounts:
            print("   前3个账户:")
            for i, acc in enumerate(accounts[:3]):
                is_favorite = getattr(acc, 'is_favorite', False)
                star = "⭐ " if is_favorite else ""
                aweme_status = " 📱" if hasattr(acc, 'aweme_id') and acc.aweme_id else ""
                print(f"   {i+1}. {star}{acc.name} ({acc.account_id_qc}){aweme_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取账户函数测试失败: {e}")
        return False

def test_global_state_functions():
    """测试全局状态管理函数"""
    print("🌐 测试全局状态管理函数...")
    
    try:
        from ai_tool_20250718_maintenance_global_account_selector import (
            get_global_selected_account,
            set_global_selected_account,
            get_current_account_info,
            get_accounts_with_favorites
        )
        
        # 获取一个测试账户
        accounts = get_accounts_with_favorites()
        if not accounts:
            print("❌ 没有可用的测试账户")
            return False
        
        test_account = accounts[0]
        
        # 测试设置全局账户
        set_global_selected_account(test_account)
        print(f"✅ 成功设置全局账户: {test_account.name}")
        
        # 测试获取全局账户
        selected_account = get_global_selected_account()
        if selected_account and selected_account.id == test_account.id:
            print(f"✅ 成功获取全局账户: {selected_account.name}")
        else:
            print("❌ 全局账户获取失败")
            return False
        
        # 测试获取账户信息
        account_info = get_current_account_info()
        if account_info:
            print(f"✅ 成功获取账户信息: {account_info['name']}")
            print(f"   千川ID: {account_info['account_id']}")
            print(f"   收藏状态: {'⭐ 已收藏' if account_info['is_favorite'] else '未收藏'}")
            print(f"   抖音号: {account_info['aweme_id'] or '未授权'}")
        else:
            print("❌ 获取账户信息失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 全局状态管理函数测试失败: {e}")
        return False

def test_new_launch_center_import():
    """测试新版投放中心导入"""
    print("🚀 测试新版投放中心导入...")
    
    try:
        from ai_tool_20250718_maintenance_new_launch_center import render_new_launch_center_page
        print("✅ 新版投放中心导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 新版投放中心导入失败: {e}")
        return False

def test_web_ui_integration():
    """测试Web UI集成"""
    print("🌐 测试Web UI集成...")
    
    try:
        # 检查web_ui.py中的导入
        web_ui_path = project_root / "web_ui.py"
        if not web_ui_path.exists():
            print("❌ web_ui.py文件不存在")
            return False
        
        with open(web_ui_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含全局选择器的导入
        if "ai_tool_20250718_maintenance_global_account_selector" in content:
            print("✅ Web UI已集成全局账户选择器导入")
        else:
            print("⚠️ Web UI未找到全局账户选择器导入")
            return False
        
        # 检查是否包含新版投放中心的导入
        if "ai_tool_20250718_maintenance_new_launch_center" in content:
            print("✅ Web UI已集成新版投放中心导入")
        else:
            print("⚠️ Web UI未找到新版投放中心导入")
            return False
        
        # 检查是否调用了渲染函数
        if "render_global_account_selector()" in content:
            print("✅ Web UI调用了全局账户选择器渲染函数")
        else:
            print("⚠️ Web UI未调用全局账户选择器渲染函数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Web UI集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 全局账户选择器功能测试")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_global_selector_import),
        ("数据库连接测试", test_database_connection),
        ("获取账户函数测试", test_get_accounts_function),
        ("全局状态管理测试", test_global_state_functions),
        ("新版投放中心导入测试", test_new_launch_center_import),
        ("Web UI集成测试", test_web_ui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！全局账户选择器功能正常")
        print("\n🚀 可以启动Web UI测试新功能:")
        print("   streamlit run web_ui.py")
        print("   然后选择 '🚀 投放中心 (新版)' 体验全局账户选择")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
