#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 诊断工具
生命周期: 永久保留
创建目的: 诊断和修复浏览器实例过多及提审逻辑异常问题
清理条件: 问题解决后可归档保留
"""

import os
import sys
import psutil
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class BrowserPerformanceDiagnostic:
    """浏览器性能诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'browser_processes': [],
            'appeal_status_analysis': {},
            'configuration_issues': [],
            'recommendations': []
        }

    def check_browser_processes(self):
        """检查当前运行的浏览器进程"""
        print("🔍 检查浏览器进程...")
        
        browser_processes = []
        total_memory = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'create_time', 'cmdline']):
            try:
                if proc.info['name'] and any(browser in proc.info['name'].lower() 
                                           for browser in ['chrome', 'chromium', 'playwright']):
                    memory_mb = proc.info['memory_info'].rss / 1024 / 1024
                    total_memory += memory_mb
                    
                    browser_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': round(memory_mb, 1),
                        'create_time': datetime.fromtimestamp(proc.info['create_time']).isoformat(),
                        'cmdline': ' '.join(proc.info['cmdline'][:3]) if proc.info['cmdline'] else ''
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.results['browser_processes'] = browser_processes
        
        print(f"📊 发现 {len(browser_processes)} 个浏览器进程")
        print(f"💾 总内存占用: {round(total_memory, 1)} MB")
        
        if len(browser_processes) > 5:
            print(f"⚠️ 浏览器进程过多！建议数量: ≤5个，当前: {len(browser_processes)}个")
            self.results['recommendations'].append(f"浏览器进程过多: {len(browser_processes)}个，建议清理")
        
        return browser_processes

    def analyze_appeal_logic(self):
        """分析提审逻辑问题"""
        print("🔍 分析提审逻辑...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import Campaign
            
            with database_session() as db:
                # 检查重复提审的计划
                repeat_appeals = db.query(Campaign).filter(
                    Campaign.appeal_attempt_count > 1,
                    Campaign.created_at >= datetime.utcnow() - timedelta(days=2)
                ).all()
                
                # 检查appeal_pending状态的计划
                pending_appeals = db.query(Campaign).filter(
                    Campaign.appeal_status == 'appeal_pending',
                    Campaign.created_at >= datetime.utcnow() - timedelta(days=1)
                ).count()
                
                # 检查状态分布
                status_distribution = {}
                for status, count in db.query(Campaign.appeal_status, db.func.count(Campaign.id)).filter(
                    Campaign.created_at >= datetime.utcnow() - timedelta(days=2)
                ).group_by(Campaign.appeal_status).all():
                    status_distribution[status or 'null'] = count
                
                self.results['appeal_status_analysis'] = {
                    'repeat_appeals_count': len(repeat_appeals),
                    'pending_appeals_count': pending_appeals,
                    'status_distribution': status_distribution,
                    'repeat_appeal_details': [
                        {
                            'campaign_id': c.campaign_id_qc,
                            'attempt_count': c.appeal_attempt_count,
                            'status': c.appeal_status,
                            'first_appeal': c.first_appeal_at.isoformat() if c.first_appeal_at else None
                        } for c in repeat_appeals[:10]
                    ]
                }
                
                print(f"📊 重复提审计划: {len(repeat_appeals)} 个")
                print(f"📊 待提审计划: {pending_appeals} 个")
                print(f"📊 状态分布: {status_distribution}")
                
                if len(repeat_appeals) > 0:
                    print("⚠️ 发现违反'每个计划只提审一次'铁律的情况！")
                    self.results['recommendations'].append("修复重复提审逻辑，确保每个计划只提审一次")
                
        except Exception as e:
            print(f"❌ 分析提审逻辑失败: {e}")
            self.results['appeal_status_analysis']['error'] = str(e)

    def check_configuration_issues(self):
        """检查配置问题"""
        print("🔍 检查配置问题...")
        
        config_file = self.project_root / 'config' / 'settings.yml'
        if not config_file.exists():
            self.results['configuration_issues'].append("配置文件不存在")
            return
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            issues = []
            
            # 检查浏览器配置
            browser_config = config.get('browser', {})
            async_playwright_config = config.get('async_playwright', {})
            appeal_scheduler_config = config.get('appeal_scheduler', {})
            
            # 检查最大浏览器会话数
            max_sessions = async_playwright_config.get('max_browser_sessions', 2)
            if max_sessions > 3:
                issues.append(f"max_browser_sessions过高: {max_sessions}，建议≤3")
            
            # 检查提审间隔
            appeal_interval = config.get('workflow', {}).get('plan_appeal', {}).get('interval_seconds', 600)
            if appeal_interval < 300:
                issues.append(f"提审间隔过短: {appeal_interval}秒，建议≥300秒")
            
            # 检查批处理配置
            batch_enabled = appeal_scheduler_config.get('batch_processing_enabled', False)
            batch_size = appeal_scheduler_config.get('batch_size_per_account', 10)
            if batch_enabled and batch_size > 5:
                issues.append(f"批处理大小过大: {batch_size}，建议≤5")
            
            # 检查账户间隔
            account_interval = appeal_scheduler_config.get('account_interval_seconds', 5)
            if account_interval < 10:
                issues.append(f"账户间隔过短: {account_interval}秒，建议≥10秒")
            
            self.results['configuration_issues'] = issues
            
            if issues:
                print("⚠️ 发现配置问题:")
                for issue in issues:
                    print(f"  - {issue}")
            else:
                print("✅ 配置检查通过")
                
        except Exception as e:
            print(f"❌ 配置检查失败: {e}")
            self.results['configuration_issues'].append(f"配置检查失败: {e}")

    def generate_recommendations(self):
        """生成修复建议"""
        print("💡 生成修复建议...")
        
        recommendations = []
        
        # 浏览器进程建议
        if len(self.results['browser_processes']) > 5:
            recommendations.append({
                'priority': 'HIGH',
                'category': '浏览器管理',
                'issue': f"浏览器进程过多: {len(self.results['browser_processes'])}个",
                'solution': '实施浏览器实例池管理，限制最大并发数量'
            })
        
        # 提审逻辑建议
        repeat_count = self.results['appeal_status_analysis'].get('repeat_appeals_count', 0)
        if repeat_count > 0:
            recommendations.append({
                'priority': 'CRITICAL',
                'category': '提审逻辑',
                'issue': f"发现{repeat_count}个计划重复提审",
                'solution': '修复提审逻辑，确保appeal_attempt_count检查生效'
            })
        
        # 配置优化建议
        if self.results['configuration_issues']:
            recommendations.append({
                'priority': 'MEDIUM',
                'category': '配置优化',
                'issue': '配置参数不合理',
                'solution': '调整浏览器会话数、提审间隔等参数'
            })
        
        self.results['recommendations'].extend(recommendations)
        
        print(f"📋 生成了 {len(recommendations)} 条修复建议")
        return recommendations

    def kill_excess_browser_processes(self, max_allowed=3):
        """清理多余的浏览器进程"""
        print(f"🧹 清理多余的浏览器进程 (保留{max_allowed}个)...")
        
        browser_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'create_time']):
            try:
                if proc.info['name'] and any(browser in proc.info['name'].lower() 
                                           for browser in ['chrome', 'chromium']):
                    browser_processes.append((proc, proc.info['create_time']))
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # 按创建时间排序，保留最新的几个
        browser_processes.sort(key=lambda x: x[1], reverse=True)
        
        killed_count = 0
        for proc, _ in browser_processes[max_allowed:]:
            try:
                proc.terminate()
                killed_count += 1
                print(f"  ✅ 终止进程 PID: {proc.pid}")
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"  ❌ 无法终止进程 PID: {proc.pid} - {e}")
        
        print(f"🧹 清理完成，终止了 {killed_count} 个进程")
        return killed_count

    def run_comprehensive_diagnosis(self):
        """运行综合诊断"""
        print("🚀 开始浏览器性能综合诊断...")
        print("="*60)
        
        try:
            # 1. 检查浏览器进程
            self.check_browser_processes()
            print()
            
            # 2. 分析提审逻辑
            self.analyze_appeal_logic()
            print()
            
            # 3. 检查配置问题
            self.check_configuration_issues()
            print()
            
            # 4. 生成建议
            self.generate_recommendations()
            print()
            
            # 5. 生成报告
            self.generate_report()
            
            print("="*60)
            print("📊 诊断结果汇总:")
            print(f"  🖥️ 浏览器进程: {len(self.results['browser_processes'])} 个")
            print(f"  🔄 重复提审: {self.results['appeal_status_analysis'].get('repeat_appeals_count', 0)} 个")
            print(f"  ⚙️ 配置问题: {len(self.results['configuration_issues'])} 个")
            print(f"  💡 修复建议: {len(self.results['recommendations'])} 条")
            
            return self.results
            
        except Exception as e:
            print(f"❌ 诊断过程发生错误: {e}")
            return None

    def generate_report(self):
        """生成诊断报告"""
        report_path = self.project_root / 'ai_reports' / 'diagnosis' / f'ai_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}_browser_performance_diagnosis.json'
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        import json
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"📄 诊断报告已保存: {report_path}")

def main():
    """主函数"""
    diagnostic = BrowserPerformanceDiagnostic()
    
    print("🎯 千川自动化项目 - 浏览器性能诊断工具")
    print("📌 目标: 解决浏览器实例过多和提审逻辑异常问题")
    print()
    
    results = diagnostic.run_comprehensive_diagnosis()
    
    if results:
        print("\n🎉 诊断完成！")
        
        # 询问是否清理浏览器进程
        if len(results['browser_processes']) > 5:
            print(f"\n⚠️ 发现 {len(results['browser_processes'])} 个浏览器进程")
            print("建议清理多余进程以改善性能")
            
            # 在脚本环境中自动执行清理
            diagnostic.kill_excess_browser_processes(max_allowed=3)
    else:
        print("❌ 诊断失败！")

if __name__ == "__main__":
    main()
