
# 建议在config/settings.yml中更新以下配置：

workflow:
  plan_creation:
    enabled: true
    interval_seconds: 300  # 从180调整为300秒，降低调度频率
  
  plan_appeal:
    enabled: true
    interval_seconds: 300  # 从180调整为300秒
  
  file_ingestion:
    enabled: true
    interval_seconds: 120  # 保持较高频率用于文件摄取
  
  material_monitoring:
    enabled: true
    interval_seconds: 600  # 10分钟监控一次

# Redis配置优化
redis:
  url: "redis://localhost:6379/0"
  max_connections: 20
  socket_timeout: 30
  socket_connect_timeout: 30

# Celery配置优化
celery:
  worker_concurrency: 5
  task_soft_time_limit: 300
  task_time_limit: 600
  worker_prefetch_multiplier: 1
