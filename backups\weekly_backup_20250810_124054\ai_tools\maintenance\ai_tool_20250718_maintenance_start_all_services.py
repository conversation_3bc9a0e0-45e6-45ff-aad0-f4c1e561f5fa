#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 千川自动化项目服务启动工具
依赖关系: 依赖run_celery_worker.py, run_celery_beat.py, web_ui.py
清理条件: 项目不再需要多服务启动时
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path
from typing import List, Dict
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class QianchuanServiceManager:
    """千川自动化项目服务管理器"""
    
    def __init__(self):
        self.project_root = project_root
        self.processes: List[subprocess.Popen] = []
        self.services = {
            "celery_worker": {
                "script": "run_celery_worker.py",
                "name": "Celery Worker",
                "description": "异步任务处理器"
            },
            "celery_beat": {
                "script": "run_celery_beat.py", 
                "name": "Celery Beat",
                "description": "定时任务调度器"
            },
            "web_ui": {
                "script": "web_ui.py",
                "name": "Web UI",
                "description": "Web管理界面"
            }
        }
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('service_manager')
        logger.setLevel(logging.INFO)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，正在关闭所有服务...")
        self.stop_all_services()
        sys.exit(0)
    
    def start_service(self, service_key: str) -> bool:
        """启动单个服务"""
        if service_key not in self.services:
            self.logger.error(f"未知服务: {service_key}")
            return False
        
        service = self.services[service_key]
        script_path = self.project_root / service["script"]
        
        if not script_path.exists():
            self.logger.error(f"服务脚本不存在: {script_path}")
            return False
        
        try:
            self.logger.info(f"启动服务: {service['name']} ({service['description']})")
            
            # 启动进程
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.processes.append(process)
            
            # 等待一小段时间检查进程是否正常启动
            time.sleep(2)
            
            if process.poll() is None:
                self.logger.info(f"✅ {service['name']} 启动成功 (PID: {process.pid})")
                return True
            else:
                stdout, stderr = process.communicate()
                self.logger.error(f"❌ {service['name']} 启动失败")
                if stderr:
                    self.logger.error(f"错误信息: {stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"启动服务 {service['name']} 时发生错误: {e}")
            return False
    
    def start_all_services(self) -> Dict[str, bool]:
        """启动所有服务"""
        self.logger.info("🚀 开始启动千川自动化项目所有服务...")
        
        results = {}
        
        # 按顺序启动服务
        service_order = ["celery_worker", "celery_beat", "web_ui"]
        
        for service_key in service_order:
            results[service_key] = self.start_service(service_key)
            
            # 服务间启动间隔
            if service_key != service_order[-1]:
                time.sleep(3)
        
        # 显示启动结果
        self.logger.info("=" * 60)
        self.logger.info("📊 服务启动结果:")
        
        success_count = 0
        for service_key, success in results.items():
            service = self.services[service_key]
            status = "✅ 成功" if success else "❌ 失败"
            self.logger.info(f"  {service['name']}: {status}")
            if success:
                success_count += 1
        
        self.logger.info(f"总计: {success_count}/{len(results)} 个服务启动成功")
        
        if success_count == len(results):
            self.logger.info("🎉 所有服务启动成功！")
            self.logger.info("💡 按 Ctrl+C 停止所有服务")
        else:
            self.logger.warning("⚠️ 部分服务启动失败，请检查错误信息")
        
        return results
    
    def stop_all_services(self):
        """停止所有服务"""
        if not self.processes:
            self.logger.info("没有运行中的服务")
            return
        
        self.logger.info("🛑 正在停止所有服务...")
        
        for i, process in enumerate(self.processes):
            if process.poll() is None:  # 进程仍在运行
                try:
                    self.logger.info(f"停止进程 PID: {process.pid}")
                    process.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        process.wait(timeout=10)
                        self.logger.info(f"✅ 进程 {process.pid} 已优雅退出")
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"⚠️ 进程 {process.pid} 未在10秒内退出，强制终止")
                        process.kill()
                        process.wait()
                        self.logger.info(f"✅ 进程 {process.pid} 已强制终止")
                        
                except Exception as e:
                    self.logger.error(f"停止进程 {process.pid} 时发生错误: {e}")
        
        self.processes.clear()
        self.logger.info("🎉 所有服务已停止")
    
    def monitor_services(self):
        """监控服务状态"""
        if not self.processes:
            self.logger.info("没有运行中的服务需要监控")
            return
        
        self.logger.info("👀 开始监控服务状态...")
        
        try:
            while True:
                running_count = 0
                
                for i, process in enumerate(self.processes):
                    if process.poll() is None:
                        running_count += 1
                    else:
                        # 进程已退出
                        stdout, stderr = process.communicate()
                        self.logger.warning(f"⚠️ 进程 {process.pid} 已退出")
                        if stderr:
                            self.logger.error(f"错误信息: {stderr}")
                
                if running_count == 0:
                    self.logger.info("所有服务都已停止")
                    break
                
                # 每30秒检查一次
                time.sleep(30)
                
        except KeyboardInterrupt:
            self.logger.info("监控被用户中断")
    
    def get_service_status(self) -> Dict[str, Dict]:
        """获取服务状态"""
        status = {}
        
        for i, process in enumerate(self.processes):
            if i < len(self.services):
                service_key = list(self.services.keys())[i]
                service = self.services[service_key]
                
                status[service_key] = {
                    "name": service["name"],
                    "pid": process.pid,
                    "running": process.poll() is None,
                    "returncode": process.returncode
                }
        
        return status

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='千川自动化项目服务管理器')
    parser.add_argument('action', choices=['start', 'stop', 'status', 'monitor'],
                       help='执行的操作')
    parser.add_argument('--service', choices=['celery_worker', 'celery_beat', 'web_ui'],
                       help='指定单个服务')
    
    args = parser.parse_args()
    
    manager = QianchuanServiceManager()
    
    if args.action == 'start':
        if args.service:
            success = manager.start_service(args.service)
            if success:
                manager.monitor_services()
        else:
            results = manager.start_all_services()
            if any(results.values()):
                manager.monitor_services()
    
    elif args.action == 'stop':
        manager.stop_all_services()
    
    elif args.action == 'status':
        status = manager.get_service_status()
        print("服务状态:")
        for service_key, info in status.items():
            running_status = "运行中" if info["running"] else f"已停止 (退出码: {info['returncode']})"
            print(f"  {info['name']} (PID: {info['pid']}): {running_status}")
    
    elif args.action == 'monitor':
        manager.monitor_services()

if __name__ == "__main__":
    main()
