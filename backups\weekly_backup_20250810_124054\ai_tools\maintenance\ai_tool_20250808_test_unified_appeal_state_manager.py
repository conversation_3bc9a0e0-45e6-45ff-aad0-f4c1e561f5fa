#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 测试统一提审状态管理器
清理条件: 测试完成后删除
"""

import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.unified_appeal_state_manager import (
    appeal_state_manager_context, create_appeal_state_manager
)
from qianchuan_aw.database.database import SessionLocal


class UnifiedAppealStateManagerTester:
    """统一状态管理器测试器"""
    
    def __init__(self):
        self.test_campaign_id = "1839848221817219"  # 使用之前修复的计划ID
        self.test_results = []
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🧪 开始测试统一提审状态管理器")
        logger.info("="*60)
        
        try:
            # 测试1：基本状态转换
            self._test_basic_state_transitions()
            
            # 测试2：状态一致性检查
            self._test_state_consistency_validation()
            
            # 测试3：错误处理
            self._test_error_handling()
            
            # 测试4：并发安全（模拟）
            self._test_concurrent_safety()
            
            # 生成测试报告
            return self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ 测试执行失败: {e}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    def _test_basic_state_transitions(self):
        """测试基本状态转换"""
        logger.info("🔄 测试1: 基本状态转换")
        
        with appeal_state_manager_context() as manager:
            # 步骤1：设置需要提审
            result = manager.set_appeal_needed(self.test_campaign_id, "测试：设置需要提审")
            self._record_test_result("set_appeal_needed", result)
            
            # 步骤2：开始提审
            result = manager.set_appeal_executing(self.test_campaign_id)
            self._record_test_result("set_appeal_executing", result)
            
            # 步骤3：提审成功
            result = manager.set_appeal_success(self.test_campaign_id, "测试：提审表单提交成功")
            self._record_test_result("set_appeal_success", result)
            
            # 步骤4：开始监控
            result = manager.set_appeal_monitoring(self.test_campaign_id)
            self._record_test_result("set_appeal_monitoring", result)
            
            # 步骤5：最终结果
            result = manager.set_appeal_final_result(self.test_campaign_id, True, "测试：申诉成功")
            self._record_test_result("set_appeal_final_result", result)
    
    def _test_state_consistency_validation(self):
        """测试状态一致性检查"""
        logger.info("✅ 测试2: 状态一致性检查")
        
        with appeal_state_manager_context() as manager:
            # 验证当前状态一致性
            validation = manager.validate_state_consistency(self.test_campaign_id)
            self._record_test_result("validate_state_consistency", {
                "success": validation["valid"],
                "message": f"一致性检查: {'通过' if validation['valid'] else '失败'}",
                "details": validation
            })
    
    def _test_error_handling(self):
        """测试错误处理"""
        logger.info("⚠️ 测试3: 错误处理")
        
        with appeal_state_manager_context() as manager:
            # 测试不存在的计划
            result = manager.set_appeal_needed("nonexistent_campaign", "测试不存在的计划")
            self._record_test_result("error_nonexistent_campaign", result)
            
            # 测试非法状态转换（从终态转换）
            result = manager.set_appeal_executing(self.test_campaign_id)  # 从APPEAL_SUCCESS转换
            self._record_test_result("error_invalid_transition", result)
    
    def _test_concurrent_safety(self):
        """测试并发安全（模拟）"""
        logger.info("🔒 测试4: 并发安全（模拟）")
        
        # 这里模拟并发操作，实际项目中可以使用多线程测试
        with appeal_state_manager_context() as manager1:
            with appeal_state_manager_context() as manager2:
                # 模拟两个管理器同时操作同一个计划
                result1 = manager1.validate_state_consistency(self.test_campaign_id)
                result2 = manager2.validate_state_consistency(self.test_campaign_id)
                
                self._record_test_result("concurrent_validation", {
                    "success": result1["valid"] and result2["valid"],
                    "message": "并发验证测试",
                    "details": {"manager1": result1, "manager2": result2}
                })
    
    def _record_test_result(self, test_name: str, result: Dict[str, Any]):
        """记录测试结果"""
        success = result.get("success", False)
        message = result.get("message", "")
        
        self.test_results.append({
            "test_name": test_name,
            "success": success,
            "message": message,
            "result": result,
            "timestamp": datetime.now(timezone.utc)
        })
        
        status = "✅" if success else "❌"
        logger.info(f"   {status} {test_name}: {message}")
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - successful_tests
        
        report = {
            "success": failed_tests == 0,
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "success_rate": (successful_tests / total_tests * 100) if total_tests > 0 else 0
            },
            "test_results": self.test_results,
            "report_time": datetime.now(timezone.utc)
        }
        
        logger.info("📋 测试报告:")
        logger.info(f"   总测试数: {total_tests}")
        logger.info(f"   成功: {successful_tests}")
        logger.info(f"   失败: {failed_tests}")
        logger.info(f"   成功率: {report['summary']['success_rate']:.1f}%")
        
        if failed_tests > 0:
            logger.warning("❌ 存在失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    logger.warning(f"   - {result['test_name']}: {result['message']}")
        else:
            logger.success("✅ 所有测试通过！")
        
        return report


def main():
    """主函数"""
    tester = UnifiedAppealStateManagerTester()
    report = tester.run_all_tests()
    
    if report["success"]:
        logger.success("🎉 统一状态管理器测试完成，所有功能正常！")
    else:
        logger.error("❌ 统一状态管理器测试发现问题，需要修复")
    
    return report


if __name__ == "__main__":
    main()
