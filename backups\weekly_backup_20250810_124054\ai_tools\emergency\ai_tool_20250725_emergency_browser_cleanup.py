#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急清理浏览器过载问题
清理条件: 问题解决后可删除

紧急浏览器清理工具
================

立即清理所有浏览器进程，停止相关服务，恢复系统正常状态。
"""

import os
import sys
import time
import psutil
import subprocess

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class EmergencyBrowserCleanup:
    """紧急浏览器清理器"""
    
    def __init__(self):
        self.cleanup_log = []
        
    def log_action(self, action: str, status: str, message: str):
        """记录清理操作"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'action': action,
            'status': status,
            'message': message
        }
        self.cleanup_log.append(log_entry)
        
        if status == 'success':
            logger.info(f"✅ {action}: {message}")
        elif status == 'warning':
            logger.warning(f"⚠️ {action}: {message}")
        else:
            logger.error(f"❌ {action}: {message}")
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_action("系统状态检查", "info", "开始检查系统状态...")
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            
            # 浏览器进程统计
            browser_processes = self._count_browser_processes()
            
            # Python进程统计
            python_processes = self._count_python_processes()
            
            self.log_action("系统状态检查", "info", 
                          f"CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, "
                          f"浏览器进程: {browser_processes}, Python进程: {python_processes}")
            
            # 判断是否需要紧急清理
            needs_cleanup = (
                cpu_percent > 80 or
                memory.percent > 85 or
                browser_processes > 50  # 已放宽限制
            )
            
            if needs_cleanup:
                self.log_action("系统状态检查", "warning", "检测到系统过载，需要紧急清理")
                return True
            else:
                self.log_action("系统状态检查", "success", "系统状态正常")
                return False
                
        except Exception as e:
            self.log_action("系统状态检查", "error", f"检查失败: {e}")
            return True  # 出错时也进行清理
    
    def _count_browser_processes(self):
        """统计浏览器进程"""
        try:
            browser_count = 0
            browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
            
            for proc in psutil.process_iter(['name', 'pid']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        browser_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return browser_count
        except:
            return 0
    
    def _count_python_processes(self):
        """统计Python进程"""
        try:
            python_count = 0
            for proc in psutil.process_iter(['name', 'pid']):
                try:
                    proc_name = proc.info['name'].lower()
                    if 'python' in proc_name:
                        python_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return python_count
        except:
            return 0
    
    def stop_celery_services(self):
        """停止Celery服务"""
        self.log_action("停止Celery服务", "info", "开始停止Celery相关服务...")
        
        try:
            stopped_count = 0
            
            # 查找并停止Celery进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                    if ('celery' in cmdline or 
                        'run_celery_worker.py' in cmdline or 
                        'run_celery_beat.py' in cmdline):
                        
                        proc.terminate()
                        stopped_count += 1
                        self.log_action("停止Celery服务", "info", 
                                      f"终止进程 PID {proc.info['pid']}: {proc.info['name']}")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # 等待进程终止
            time.sleep(3)
            
            # 强制杀死未响应的进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                    if ('celery' in cmdline or 
                        'run_celery_worker.py' in cmdline or 
                        'run_celery_beat.py' in cmdline):
                        
                        proc.kill()
                        self.log_action("停止Celery服务", "warning", 
                                      f"强制杀死进程 PID {proc.info['pid']}")
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            self.log_action("停止Celery服务", "success", f"已停止 {stopped_count} 个Celery进程")
            return True
            
        except Exception as e:
            self.log_action("停止Celery服务", "error", f"停止失败: {e}")
            return False
    
    def cleanup_browser_processes(self):
        """清理浏览器进程"""
        self.log_action("清理浏览器进程", "info", "开始清理浏览器进程...")
        
        try:
            killed_count = 0
            browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
            
            # 首先尝试优雅终止
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        proc.terminate()
                        killed_count += 1
                        self.log_action("清理浏览器进程", "info", 
                                      f"终止浏览器进程 PID {proc.info['pid']}: {proc.info['name']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # 等待进程终止
            time.sleep(5)
            
            # 强制杀死未响应的浏览器进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        proc.kill()
                        self.log_action("清理浏览器进程", "warning", 
                                      f"强制杀死浏览器进程 PID {proc.info['pid']}")
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            self.log_action("清理浏览器进程", "success", f"已清理 {killed_count} 个浏览器进程")
            return True
            
        except Exception as e:
            self.log_action("清理浏览器进程", "error", f"清理失败: {e}")
            return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        self.log_action("清理临时文件", "info", "开始清理临时文件...")
        
        try:
            import tempfile
            import shutil
            
            cleaned_count = 0
            temp_dir = tempfile.gettempdir()
            
            # 清理Chrome临时文件
            for item in os.listdir(temp_dir):
                if ('chrome' in item.lower() or 
                    'chromium' in item.lower() or
                    item.startswith('playwright')):
                    try:
                        item_path = os.path.join(temp_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        cleaned_count += 1
                    except:
                        pass
            
            self.log_action("清理临时文件", "success", f"已清理 {cleaned_count} 个临时文件/目录")
            return True
            
        except Exception as e:
            self.log_action("清理临时文件", "error", f"清理失败: {e}")
            return False
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        self.log_action("强制垃圾回收", "info", "执行Python垃圾回收...")
        
        try:
            import gc
            
            # 执行垃圾回收
            collected = gc.collect()
            
            self.log_action("强制垃圾回收", "success", f"回收了 {collected} 个对象")
            return True
            
        except Exception as e:
            self.log_action("强制垃圾回收", "error", f"垃圾回收失败: {e}")
            return False
    
    def verify_cleanup_result(self):
        """验证清理结果"""
        self.log_action("验证清理结果", "info", "验证清理效果...")
        
        try:
            # 等待系统稳定
            time.sleep(5)
            
            # 重新检查系统状态
            cpu_percent = psutil.cpu_percent(interval=2)
            memory = psutil.virtual_memory()
            browser_processes = self._count_browser_processes()
            
            self.log_action("验证清理结果", "info", 
                          f"清理后状态 - CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, "
                          f"浏览器进程: {browser_processes}")
            
            # 判断清理效果
            cleanup_successful = (
                cpu_percent < 50 and
                memory.percent < 70 and
                browser_processes < 3
            )
            
            if cleanup_successful:
                self.log_action("验证清理结果", "success", "清理成功，系统状态已恢复正常")
                return True
            else:
                self.log_action("验证清理结果", "warning", "清理部分成功，系统状态有所改善")
                return True
                
        except Exception as e:
            self.log_action("验证清理结果", "error", f"验证失败: {e}")
            return False
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        logger.info("📋 生成紧急清理报告...")
        
        success_count = sum(1 for log in self.cleanup_log if log['status'] == 'success')
        warning_count = sum(1 for log in self.cleanup_log if log['status'] == 'warning')
        error_count = sum(1 for log in self.cleanup_log if log['status'] == 'error')
        total_count = len([log for log in self.cleanup_log if log['status'] in ['success', 'warning', 'error']])
        
        report = f"""
紧急浏览器清理报告
================

清理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

清理结果概览:
- 总操作数: {total_count}
- 成功操作: {success_count}
- 警告操作: {warning_count}
- 失败操作: {error_count}
- 成功率: {success_count/max(total_count, 1)*100:.1f}%

详细清理日志:
"""
        
        for log_entry in self.cleanup_log:
            if log_entry['status'] == 'success':
                status_icon = "✅"
            elif log_entry['status'] == 'warning':
                status_icon = "⚠️"
            elif log_entry['status'] == 'error':
                status_icon = "❌"
            else:
                status_icon = "ℹ️"
            
            report += f"[{log_entry['timestamp']}] {status_icon} {log_entry['action']}: {log_entry['message']}\n"
        
        report += f"""
清理效果评估:
"""
        
        if error_count == 0:
            if warning_count == 0:
                report += "🎉 清理完全成功！系统已恢复正常状态。\n"
            else:
                report += "✅ 清理基本成功，有少量警告需要关注。\n"
        else:
            report += "⚠️ 清理存在问题，可能需要手动干预。\n"
        
        report += f"""
后续建议:
1. 重新启动千川自动化服务时使用新的异步Playwright配置
2. 监控系统状态确保不再出现过载
3. 如果问题重现，立即停止服务并检查配置
"""
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'emergency_cleanup_report_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 紧急清理报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚨 开始紧急浏览器清理...")
    
    cleanup = EmergencyBrowserCleanup()
    
    try:
        # 1. 检查系统状态
        needs_cleanup = cleanup.check_system_status()
        
        if not needs_cleanup:
            logger.info("✅ 系统状态正常，无需清理")
            return 0
        
        logger.info("🚨 检测到系统过载，开始紧急清理...")
        
        # 2. 停止Celery服务
        cleanup.stop_celery_services()
        
        # 3. 清理浏览器进程
        cleanup.cleanup_browser_processes()
        
        # 4. 清理临时文件
        cleanup.cleanup_temp_files()
        
        # 5. 强制垃圾回收
        cleanup.force_garbage_collection()
        
        # 6. 验证清理结果
        cleanup.verify_cleanup_result()
        
        # 7. 生成清理报告
        report = cleanup.generate_cleanup_report()
        
        logger.info("🎯 紧急清理完成！")
        print(report)
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 紧急清理过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
