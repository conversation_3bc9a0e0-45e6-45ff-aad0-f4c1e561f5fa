#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急清理任务堆积问题
清理条件: 问题解决后可删除

紧急任务清理工具
==============

清理Celery任务队列堆积，重置数据库中的异常状态，解决CPU过载问题。
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


class EmergencyTaskCleanup:
    """紧急任务清理器"""
    
    def __init__(self):
        self.cleanup_log = []
        
    def log_action(self, action: str, status: str, message: str):
        """记录清理操作"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_entry = {
            'timestamp': timestamp,
            'action': action,
            'status': status,
            'message': message
        }
        self.cleanup_log.append(log_entry)
        
        if status == 'success':
            logger.info(f"✅ {action}: {message}")
        elif status == 'warning':
            logger.warning(f"⚠️ {action}: {message}")
        else:
            logger.error(f"❌ {action}: {message}")
    
    def stop_all_celery_processes(self):
        """停止所有Celery进程"""
        self.log_action("停止Celery进程", "info", "开始停止所有Celery相关进程...")
        
        try:
            # 停止Python进程（包括Celery）
            result = subprocess.run(['taskkill', '/F', '/IM', 'python.exe'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_action("停止Celery进程", "success", "已停止所有Python进程")
            else:
                self.log_action("停止Celery进程", "warning", "部分进程可能已经停止")
            
            # 等待进程完全停止
            time.sleep(3)
            
            return True
            
        except Exception as e:
            self.log_action("停止Celery进程", "error", f"停止进程失败: {e}")
            return False
    
    def reset_database_status(self):
        """重置数据库中的异常状态"""
        self.log_action("重置数据库状态", "info", "开始重置数据库中的异常状态...")
        
        try:
            with database_session() as db:
                # 查找卡在处理状态的素材
                processing_materials = db.query(LocalCreative).filter(
                    LocalCreative.status.in_([MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADING.value, 'creating_plan', MaterialStatus.PENDING_UPLOAD.value])
                ).all()
                
                reset_count = len(processing_materials)
                self.log_action("重置数据库状态", "info", f"找到 {reset_count} 个需要重置的素材")
                
                if reset_count > 0:
                    # 重置状态为pending_grouping
                    for material in processing_materials:
                        old_status = material.status
                        material.status = MaterialStatus.PENDING_GROUPING.value
                        self.log_action("重置数据库状态", "info", 
                                      f"重置素材 {material.filename}: {old_status} -> pending_grouping")
                    
                    db.commit()
                    self.log_action("重置数据库状态", "success", f"已重置 {reset_count} 个素材状态")
                else:
                    self.log_action("重置数据库状态", "success", "没有需要重置的素材")
                
                return reset_count
                
        except Exception as e:
            self.log_action("重置数据库状态", "error", f"重置失败: {e}")
            return 0
    
    def clear_redis_queues(self):
        """清理Redis队列"""
        self.log_action("清理Redis队列", "info", "尝试清理Redis队列...")
        
        try:
            # 尝试使用redis-cli清理
            result = subprocess.run(['redis-cli', 'FLUSHDB'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log_action("清理Redis队列", "success", "Redis队列已清理")
                return True
            else:
                self.log_action("清理Redis队列", "warning", f"redis-cli返回错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            self.log_action("清理Redis队列", "warning", "redis-cli执行超时")
        except FileNotFoundError:
            self.log_action("清理Redis队列", "warning", "未找到redis-cli命令")
        except Exception as e:
            self.log_action("清理Redis队列", "warning", f"清理Redis失败: {e}")
        
        # 尝试重启Redis服务
        try:
            self.log_action("清理Redis队列", "info", "尝试重启Redis服务...")
            
            # 停止Redis服务
            subprocess.run(['net', 'stop', 'Redis'], capture_output=True, text=True, timeout=10)
            time.sleep(2)
            
            # 启动Redis服务
            result = subprocess.run(['net', 'start', 'Redis'], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.log_action("清理Redis队列", "success", "Redis服务已重启")
                return True
            else:
                self.log_action("清理Redis队列", "warning", "Redis服务重启失败")
                
        except Exception as e:
            self.log_action("清理Redis队列", "warning", f"重启Redis服务失败: {e}")
        
        return False
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        self.log_action("清理临时文件", "info", "开始清理临时文件...")
        
        try:
            import tempfile
            import shutil
            
            cleaned_count = 0
            temp_dir = tempfile.gettempdir()
            
            # 清理Celery相关临时文件
            for item in os.listdir(temp_dir):
                if ('celery' in item.lower() or 
                    'redis' in item.lower() or
                    item.startswith('tmp')):
                    try:
                        item_path = os.path.join(temp_dir, item)
                        if os.path.isfile(item_path):
                            os.remove(item_path)
                        elif os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                        cleaned_count += 1
                    except:
                        pass
            
            self.log_action("清理临时文件", "success", f"已清理 {cleaned_count} 个临时文件/目录")
            return True
            
        except Exception as e:
            self.log_action("清理临时文件", "error", f"清理失败: {e}")
            return False
    
    def check_system_status(self):
        """检查系统状态"""
        self.log_action("检查系统状态", "info", "检查清理后的系统状态...")
        
        try:
            import psutil
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=2)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            
            # Python进程数
            python_processes = 0
            for proc in psutil.process_iter(['name']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_processes += 1
                except:
                    pass
            
            # 浏览器进程数
            browser_processes = 0
            browser_names = ['chrome', 'chromium', 'msedge', 'firefox']
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if any(browser in proc_name for browser in browser_names):
                        browser_processes += 1
                except:
                    pass
            
            status_msg = (f"CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%, "
                         f"Python进程: {python_processes}, 浏览器进程: {browser_processes}")
            
            if cpu_percent < 30 and memory.percent < 60:
                self.log_action("检查系统状态", "success", f"系统状态良好 - {status_msg}")
                return True
            elif cpu_percent < 50 and memory.percent < 80:
                self.log_action("检查系统状态", "warning", f"系统状态可接受 - {status_msg}")
                return True
            else:
                self.log_action("检查系统状态", "error", f"系统状态仍然异常 - {status_msg}")
                return False
                
        except Exception as e:
            self.log_action("检查系统状态", "error", f"状态检查失败: {e}")
            return False
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        logger.info("📋 生成紧急任务清理报告...")
        
        success_count = sum(1 for log in self.cleanup_log if log['status'] == 'success')
        warning_count = sum(1 for log in self.cleanup_log if log['status'] == 'warning')
        error_count = sum(1 for log in self.cleanup_log if log['status'] == 'error')
        total_count = len([log for log in self.cleanup_log if log['status'] in ['success', 'warning', 'error']])
        
        report = f"""
紧急任务清理报告
==============

清理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

清理结果概览:
- 总操作数: {total_count}
- 成功操作: {success_count}
- 警告操作: {warning_count}
- 失败操作: {error_count}
- 成功率: {success_count/max(total_count, 1)*100:.1f}%

详细清理日志:
"""
        
        for log_entry in self.cleanup_log:
            if log_entry['status'] == 'success':
                status_icon = "✅"
            elif log_entry['status'] == 'warning':
                status_icon = "⚠️"
            elif log_entry['status'] == 'error':
                status_icon = "❌"
            else:
                status_icon = "ℹ️"
            
            report += f"[{log_entry['timestamp']}] {status_icon} {log_entry['action']}: {log_entry['message']}\n"
        
        report += f"""
清理效果评估:
"""
        
        if error_count == 0:
            if warning_count == 0:
                report += "🎉 清理完全成功！任务堆积问题已解决。\n"
            else:
                report += "✅ 清理基本成功，有少量警告需要关注。\n"
        else:
            report += "⚠️ 清理存在问题，可能需要手动干预。\n"
        
        report += f"""
后续建议:
1. 重新启动千川自动化服务前，确认系统资源正常
2. 使用异步Playwright配置启动服务
3. 监控系统状态，防止任务再次堆积
4. 如果问题重现，检查代码逻辑是否存在无限循环
"""
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'emergency_task_cleanup_report_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 紧急任务清理报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    logger.info("🚨 开始紧急任务清理...")
    
    cleanup = EmergencyTaskCleanup()
    
    try:
        # 1. 停止所有Celery进程
        cleanup.stop_all_celery_processes()
        
        # 2. 重置数据库状态
        reset_count = cleanup.reset_database_status()
        
        # 3. 清理Redis队列
        cleanup.clear_redis_queues()
        
        # 4. 清理临时文件
        cleanup.cleanup_temp_files()
        
        # 5. 检查系统状态
        cleanup.check_system_status()
        
        # 6. 生成清理报告
        report = cleanup.generate_cleanup_report()
        
        logger.info("🎯 紧急任务清理完成！")
        print(report)
        
        logger.info("💡 下一步建议:")
        logger.info("1. 等待系统稳定（建议等待1-2分钟）")
        logger.info("2. 使用异步Playwright配置重新启动服务")
        logger.info("3. 监控系统资源使用情况")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 紧急任务清理过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
