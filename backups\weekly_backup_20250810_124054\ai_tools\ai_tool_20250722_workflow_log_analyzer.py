#!/usr/bin/env python3
"""
千川工作流日志分析器
分析最近30分钟的工作流日志，检查异常情况
"""

import sys
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class WorkflowLogAnalyzer:
    """工作流日志分析器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流日志分析 - 最近30分钟")
        logger.critical("=" * 60)
        self.log_dir = Path("logs")
        self.analysis_start_time = datetime.now() - timedelta(minutes=30)
        self.current_time = datetime.now()
        
        # 异常模式定义
        self.error_patterns = {
            'duplicate_upload': [
                r'重复上传.*?(\S+\.mp4)',
                r'视频.*?(\S+\.mp4).*?已存在',
                r'duplicate.*?upload.*?(\S+\.mp4)',
            ],
            'duplicate_plan': [
                r'重复创建计划.*?(\S+\.mp4)',
                r'(\S+\.mp4).*?已有.*?计划',
                r'duplicate.*?campaign.*?(\S+\.mp4)',
            ],
            'workflow_block': [
                r'任务.*?超时',
                r'队列.*?积压',
                r'处理.*?停滞',
                r'timeout.*?task',
                r'queue.*?blocked',
            ],
            'data_inconsistency': [
                r'状态.*?不一致',
                r'数据库.*?同步.*?失败',
                r'状态.*?异常',
                r'inconsistent.*?state',
            ],
            'system_error': [
                r'数据库.*?连接.*?失败',
                r'API.*?认证.*?失败',
                r'网络.*?连接.*?异常',
                r'内存.*?不足',
                r'database.*?connection.*?failed',
                r'authentication.*?failed',
                r'network.*?error',
                r'memory.*?error',
            ]
        }
        
        self.analysis_results = {
            'workflow_status': {},
            'anomalies': [],
            'log_evidence': [],
            'recommendations': []
        }
    
    def read_recent_logs(self):
        """读取最近30分钟的日志"""
        logger.critical("📊 读取最近30分钟的日志")
        logger.critical("=" * 60)
        
        today_log = self.log_dir / f"app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        if not today_log.exists():
            logger.error(f"❌ 今天的日志文件不存在: {today_log}")
            return []
        
        try:
            with open(today_log, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # 筛选最近30分钟的日志
            recent_lines = []
            for line in all_lines:
                # 提取时间戳
                time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if time_match:
                    try:
                        log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if log_time >= self.analysis_start_time:
                            recent_lines.append((log_time, line.strip()))
                    except ValueError:
                        continue
            
            logger.critical(f"📊 读取日志统计:")
            logger.critical(f"  总日志行数: {len(all_lines)}")
            logger.critical(f"  最近30分钟: {len(recent_lines)} 行")
            logger.critical(f"  分析时间范围: {self.analysis_start_time.strftime('%H:%M:%S')} - {self.current_time.strftime('%H:%M:%S')}")
            
            return recent_lines
            
        except Exception as e:
            logger.error(f"❌ 读取日志失败: {e}")
            return []
    
    def analyze_workflow_status(self, log_lines):
        """分析工作流整体状态"""
        logger.critical("\n📊 分析工作流整体状态")
        logger.critical("=" * 60)
        
        status_stats = {
            'total_logs': len(log_lines),
            'error_count': 0,
            'warning_count': 0,
            'info_count': 0,
            'critical_count': 0,
            'task_starts': 0,
            'task_ends': 0,
            'uploads': 0,
            'plan_creations': 0,
            'api_calls': 0,
            'database_operations': 0
        }
        
        # 统计各类日志
        for log_time, line in log_lines:
            if 'ERROR' in line:
                status_stats['error_count'] += 1
            elif 'WARNING' in line:
                status_stats['warning_count'] += 1
            elif 'INFO' in line:
                status_stats['info_count'] += 1
            elif 'CRITICAL' in line:
                status_stats['critical_count'] += 1
            
            # 统计关键活动
            if 'Task Start' in line or '任务开始' in line:
                status_stats['task_starts'] += 1
            elif 'Task End' in line or '任务结束' in line:
                status_stats['task_ends'] += 1
            elif '上传' in line and ('成功' in line or 'success' in line.lower()):
                status_stats['uploads'] += 1
            elif '创建计划' in line or 'create.*campaign' in line.lower():
                status_stats['plan_creations'] += 1
            elif 'API' in line or 'api' in line.lower():
                status_stats['api_calls'] += 1
            elif '数据库' in line or 'database' in line.lower():
                status_stats['database_operations'] += 1
        
        # 评估工作流健康状态
        health_score = 100
        health_issues = []
        
        # 错误率检查
        if status_stats['total_logs'] > 0:
            error_rate = (status_stats['error_count'] + status_stats['critical_count']) / status_stats['total_logs']
            if error_rate > 0.1:  # 错误率超过10%
                health_score -= 30
                health_issues.append(f"高错误率: {error_rate:.1%}")
        
        # 任务完成率检查
        if status_stats['task_starts'] > 0:
            completion_rate = status_stats['task_ends'] / status_stats['task_starts']
            if completion_rate < 0.8:  # 完成率低于80%
                health_score -= 20
                health_issues.append(f"低任务完成率: {completion_rate:.1%}")
        
        # 活动水平检查
        if status_stats['uploads'] == 0 and status_stats['plan_creations'] == 0:
            health_score -= 15
            health_issues.append("无上传或计划创建活动")
        
        self.analysis_results['workflow_status'] = {
            'stats': status_stats,
            'health_score': health_score,
            'health_issues': health_issues,
            'status': 'healthy' if health_score >= 80 else 'warning' if health_score >= 60 else 'critical'
        }
        
        logger.critical(f"📊 工作流状态统计:")
        logger.critical(f"  总日志: {status_stats['total_logs']} 条")
        logger.critical(f"  错误: {status_stats['error_count']} 条")
        logger.critical(f"  警告: {status_stats['warning_count']} 条")
        logger.critical(f"  任务开始: {status_stats['task_starts']} 个")
        logger.critical(f"  任务结束: {status_stats['task_ends']} 个")
        logger.critical(f"  上传操作: {status_stats['uploads']} 次")
        logger.critical(f"  计划创建: {status_stats['plan_creations']} 次")
        
        logger.critical(f"\n🏥 健康状态评估:")
        logger.critical(f"  健康评分: {health_score}/100")
        logger.critical(f"  状态等级: {self.analysis_results['workflow_status']['status'].upper()}")
        
        if health_issues:
            logger.critical(f"  发现问题:")
            for issue in health_issues:
                logger.critical(f"    - {issue}")
    
    def detect_anomalies(self, log_lines):
        """检测异常情况"""
        logger.critical("\n🔍 检测异常情况")
        logger.critical("=" * 60)
        
        anomalies = []
        
        # 检查每种异常模式
        for anomaly_type, patterns in self.error_patterns.items():
            type_anomalies = []
            
            for log_time, line in log_lines:
                for pattern in patterns:
                    matches = re.findall(pattern, line, re.IGNORECASE)
                    if matches:
                        type_anomalies.append({
                            'time': log_time,
                            'line': line,
                            'pattern': pattern,
                            'matches': matches
                        })
            
            if type_anomalies:
                anomalies.append({
                    'type': anomaly_type,
                    'count': len(type_anomalies),
                    'instances': type_anomalies,
                    'severity': self.get_severity(anomaly_type)
                })
        
        # 特殊检查：重复计划创建违规
        duplicate_plan_violations = self.check_duplicate_plan_violations(log_lines)
        if duplicate_plan_violations:
            anomalies.append({
                'type': 'duplicate_plan_violation',
                'count': len(duplicate_plan_violations),
                'instances': duplicate_plan_violations,
                'severity': 'critical'
            })
        
        # 特殊检查：工作流阻塞
        workflow_blocks = self.check_workflow_blocks(log_lines)
        if workflow_blocks:
            anomalies.append({
                'type': 'workflow_block_detected',
                'count': len(workflow_blocks),
                'instances': workflow_blocks,
                'severity': 'high'
            })
        
        # 按严重程度排序
        severity_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
        anomalies.sort(key=lambda x: severity_order.get(x['severity'], 4))
        
        self.analysis_results['anomalies'] = anomalies
        
        logger.critical(f"📊 异常检测结果:")
        logger.critical(f"  发现异常类型: {len(anomalies)} 种")
        
        for anomaly in anomalies:
            severity_icon = {'critical': '🚨', 'high': '⚠️', 'medium': '🟡', 'low': '🔵'}.get(anomaly['severity'], '❓')
            logger.critical(f"  {severity_icon} {anomaly['type']}: {anomaly['count']} 次 ({anomaly['severity']})")
    
    def check_duplicate_plan_violations(self, log_lines):
        """检查重复计划创建违规"""
        violations = []
        created_plans = defaultdict(list)  # filename -> [plan_ids]
        
        for log_time, line in log_lines:
            # 查找计划创建记录
            plan_creation_patterns = [
                r'成功创建计划.*?(\d+).*?(\S+\.mp4)',
                r'Campaign created.*?(\d+).*?(\S+\.mp4)',
                r'(\S+\.mp4).*?创建计划.*?(\d+)',
            ]
            
            for pattern in plan_creation_patterns:
                matches = re.findall(pattern, line, re.IGNORECASE)
                for match in matches:
                    if len(match) == 2:
                        plan_id, filename = match
                        created_plans[filename].append({
                            'plan_id': plan_id,
                            'time': log_time,
                            'line': line
                        })
        
        # 检查是否有视频创建了多个计划
        for filename, plans in created_plans.items():
            if len(plans) > 1:
                violations.append({
                    'filename': filename,
                    'plan_count': len(plans),
                    'plans': plans,
                    'severity': 'critical',
                    'description': f'视频 {filename} 创建了 {len(plans)} 个测试计划，违反业务规则'
                })
        
        return violations
    
    def check_workflow_blocks(self, log_lines):
        """检查工作流阻塞"""
        blocks = []
        
        # 检查长时间无活动
        if len(log_lines) < 10:  # 30分钟内日志少于10条可能表示阻塞
            blocks.append({
                'type': 'low_activity',
                'description': f'30分钟内仅有 {len(log_lines)} 条日志，可能存在工作流阻塞',
                'severity': 'medium'
            })
        
        # 检查重复的错误模式
        error_patterns = defaultdict(int)
        for log_time, line in log_lines:
            if 'ERROR' in line or 'CRITICAL' in line:
                # 提取错误的关键部分
                error_key = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', line)[:100]
                error_patterns[error_key] += 1
        
        for error_pattern, count in error_patterns.items():
            if count >= 5:  # 同样的错误重复5次以上
                blocks.append({
                    'type': 'repeated_error',
                    'pattern': error_pattern.strip(),
                    'count': count,
                    'description': f'重复错误 {count} 次，可能导致工作流阻塞',
                    'severity': 'high'
                })
        
        return blocks
    
    def get_severity(self, anomaly_type):
        """获取异常严重程度"""
        severity_map = {
            'duplicate_upload': 'high',
            'duplicate_plan': 'critical',
            'workflow_block': 'high',
            'data_inconsistency': 'medium',
            'system_error': 'high'
        }
        return severity_map.get(anomaly_type, 'medium')
    
    def extract_log_evidence(self, log_lines):
        """提取日志证据"""
        logger.critical("\n📋 提取关键日志证据")
        logger.critical("=" * 60)
        
        evidence = []
        
        # 提取错误和关键日志
        for log_time, line in log_lines:
            if any(keyword in line.upper() for keyword in ['ERROR', 'CRITICAL', '失败', '异常', '错误']):
                # 排除预期的素材问题错误
                if not any(excluded in line for excluded in [
                    '文件不存在', '文件不符合规格', '格式不符合', '大小不符合', '时长不符合'
                ]):
                    evidence.append({
                        'time': log_time,
                        'level': 'error',
                        'content': line,
                        'category': self.categorize_log(line)
                    })
            elif any(keyword in line for keyword in ['成功创建计划', '上传成功', 'Campaign created']):
                evidence.append({
                    'time': log_time,
                    'level': 'success',
                    'content': line,
                    'category': 'operation_success'
                })
        
        # 按时间排序
        evidence.sort(key=lambda x: x['time'])
        
        self.analysis_results['log_evidence'] = evidence
        
        logger.critical(f"📊 日志证据统计:")
        logger.critical(f"  关键日志: {len(evidence)} 条")
        
        # 显示最近的关键日志
        recent_evidence = evidence[-10:] if len(evidence) > 10 else evidence
        for ev in recent_evidence:
            level_icon = {'error': '❌', 'success': '✅', 'warning': '⚠️'}.get(ev['level'], '📋')
            logger.critical(f"  {level_icon} {ev['time'].strftime('%H:%M:%S')} - {ev['content'][:100]}...")
    
    def categorize_log(self, line):
        """分类日志"""
        if '数据库' in line or 'database' in line.lower():
            return 'database'
        elif 'API' in line or 'api' in line.lower():
            return 'api'
        elif '上传' in line or 'upload' in line.lower():
            return 'upload'
        elif '计划' in line or 'campaign' in line.lower():
            return 'campaign'
        elif '网络' in line or 'network' in line.lower():
            return 'network'
        else:
            return 'general'
    
    def generate_recommendations(self):
        """生成处理建议"""
        logger.critical("\n💡 生成处理建议")
        logger.critical("=" * 60)
        
        recommendations = []
        
        # 基于工作流状态的建议
        workflow_status = self.analysis_results['workflow_status']
        if workflow_status['health_score'] < 80:
            recommendations.append({
                'priority': 'high',
                'category': 'workflow_health',
                'issue': f"工作流健康评分低 ({workflow_status['health_score']}/100)",
                'action': '立即检查工作流配置和资源状态',
                'details': workflow_status['health_issues']
            })
        
        # 基于异常的建议
        for anomaly in self.analysis_results['anomalies']:
            if anomaly['severity'] == 'critical':
                recommendations.append({
                    'priority': 'critical',
                    'category': anomaly['type'],
                    'issue': f"发现严重异常: {anomaly['type']} ({anomaly['count']} 次)",
                    'action': '立即停止工作流，检查并修复问题',
                    'details': [f"异常实例: {len(anomaly['instances'])} 个"]
                })
            elif anomaly['severity'] == 'high':
                recommendations.append({
                    'priority': 'high',
                    'category': anomaly['type'],
                    'issue': f"发现高优先级异常: {anomaly['type']} ({anomaly['count']} 次)",
                    'action': '尽快检查并修复问题',
                    'details': [f"异常实例: {len(anomaly['instances'])} 个"]
                })
        
        # 基于日志证据的建议
        error_evidence = [ev for ev in self.analysis_results['log_evidence'] if ev['level'] == 'error']
        if len(error_evidence) > 10:
            recommendations.append({
                'priority': 'medium',
                'category': 'error_frequency',
                'issue': f"错误日志频率过高 ({len(error_evidence)} 条)",
                'action': '分析错误模式，优化错误处理机制',
                'details': ['检查最频繁的错误类型', '优化重试机制', '改进错误恢复逻辑']
            })
        
        # 如果没有发现问题
        if not recommendations:
            recommendations.append({
                'priority': 'info',
                'category': 'status',
                'issue': '工作流运行正常',
                'action': '继续监控',
                'details': ['保持当前配置', '定期检查日志', '监控性能指标']
            })
        
        # 按优先级排序
        priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3, 'info': 4}
        recommendations.sort(key=lambda x: priority_order.get(x['priority'], 5))
        
        self.analysis_results['recommendations'] = recommendations
        
        logger.critical(f"📊 处理建议:")
        for rec in recommendations:
            priority_icon = {'critical': '🚨', 'high': '⚠️', 'medium': '🟡', 'low': '🔵', 'info': '💡'}.get(rec['priority'], '❓')
            logger.critical(f"  {priority_icon} {rec['priority'].upper()}: {rec['issue']}")
            logger.critical(f"    建议: {rec['action']}")
    
    def generate_final_report(self):
        """生成最终报告"""
        logger.critical("\n📋 千川工作流日志分析报告")
        logger.critical("=" * 60)
        
        workflow_status = self.analysis_results['workflow_status']
        anomalies = self.analysis_results['anomalies']
        recommendations = self.analysis_results['recommendations']
        
        logger.critical("🎯 分析概要:")
        logger.critical(f"  分析时间: {self.analysis_start_time.strftime('%H:%M:%S')} - {self.current_time.strftime('%H:%M:%S')}")
        logger.critical(f"  工作流状态: {workflow_status['status'].upper()}")
        logger.critical(f"  健康评分: {workflow_status['health_score']}/100")
        logger.critical(f"  发现异常: {len(anomalies)} 种")
        logger.critical(f"  处理建议: {len(recommendations)} 条")
        
        logger.critical("\n📊 关键指标:")
        stats = workflow_status['stats']
        logger.critical(f"  日志总数: {stats['total_logs']}")
        logger.critical(f"  错误数量: {stats['error_count']}")
        logger.critical(f"  任务完成率: {stats['task_ends']}/{stats['task_starts']} = {stats['task_ends']/max(stats['task_starts'], 1):.1%}")
        logger.critical(f"  上传操作: {stats['uploads']} 次")
        logger.critical(f"  计划创建: {stats['plan_creations']} 次")
        
        if anomalies:
            logger.critical("\n🚨 发现的异常 (按严重程度排序):")
            for anomaly in anomalies:
                severity_icon = {'critical': '🚨', 'high': '⚠️', 'medium': '🟡', 'low': '🔵'}.get(anomaly['severity'], '❓')
                logger.critical(f"  {severity_icon} {anomaly['type']}: {anomaly['count']} 次")
        
        logger.critical("\n💡 处理建议 (按优先级排序):")
        for rec in recommendations:
            priority_icon = {'critical': '🚨', 'high': '⚠️', 'medium': '🟡', 'low': '🔵', 'info': '💡'}.get(rec['priority'], '❓')
            logger.critical(f"  {priority_icon} {rec['issue']}")
            logger.critical(f"    → {rec['action']}")
        
        # 业务规则合规性检查
        duplicate_violations = [a for a in anomalies if a['type'] in ['duplicate_plan', 'duplicate_plan_violation']]
        if duplicate_violations:
            logger.critical("\n🚨 业务规则违规警告:")
            logger.critical("  发现重复计划创建，严重违反'测试阶段每个视频只创建一个计划'规则")
            logger.critical("  建议立即停止工作流并修复重复检测逻辑")
        else:
            logger.critical("\n✅ 业务规则合规性:")
            logger.critical("  未发现重复计划创建违规")
            logger.critical("  '测试阶段每个视频只创建一个计划'规则得到遵守")

def main():
    """主分析函数"""
    try:
        analyzer = WorkflowLogAnalyzer()
        
        # 1. 读取最近30分钟的日志
        log_lines = analyzer.read_recent_logs()
        
        if not log_lines:
            logger.error("❌ 没有找到最近30分钟的日志")
            return False
        
        # 2. 分析工作流整体状态
        analyzer.analyze_workflow_status(log_lines)
        
        # 3. 检测异常情况
        analyzer.detect_anomalies(log_lines)
        
        # 4. 提取日志证据
        analyzer.extract_log_evidence(log_lines)
        
        # 5. 生成处理建议
        analyzer.generate_recommendations()
        
        # 6. 生成最终报告
        analyzer.generate_final_report()
        
        logger.critical(f"\n🎉 千川工作流日志分析完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 日志分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
