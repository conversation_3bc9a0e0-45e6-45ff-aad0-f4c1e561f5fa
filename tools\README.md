# 千川自动化系统核心工具

本目录包含千川自动化系统的核心维护和管理工具。

## 📁 目录结构

```
tools/
├── maintenance/           # 系统维护工具
│   ├── error_analysis_and_fix.py      # 错误分析和修复工具
│   └── smart_retry_optimizer.py       # 智能重试逻辑优化器
├── quality/              # 质量检查工具
│   └── video_quality_precheck.py      # 视频质量预检查工具
├── system_health_checker.py           # 系统健康检查器（集成工具）
└── README.md            # 本文档
```

## 🛠️ 核心工具说明

### 1. 系统健康检查器 (system_health_checker.py)

**用途**: 综合系统健康检查，集成所有维护工具
**推荐频率**: 每周运行一次

```bash
python tools/system_health_checker.py
```

**功能**:
- 数据库健康检查
- 文件系统健康检查
- 上传系统健康检查
- 视频质量健康检查
- 生成健康报告和评分

### 2. 错误分析和修复工具 (maintenance/error_analysis_and_fix.py)

**用途**: 分析和修复系统中的重复错误
**推荐频率**: 发现错误时运行

```bash
python tools/maintenance/error_analysis_and_fix.py
```

**功能**:
- 分析文件缺失问题并自动修复
- 修复卡住的uploading素材
- 分析视频质量问题
- 创建错误预防规则

### 3. 智能重试逻辑优化器 (maintenance/smart_retry_optimizer.py)

**用途**: 分析和优化重试逻辑，减少无效重试
**推荐频率**: 系统优化时运行

```bash
python tools/maintenance/smart_retry_optimizer.py
```

**功能**:
- 错误分类（永久性、临时性、特殊错误）
- 重试策略分析
- 失败率统计
- 重试逻辑优化建议

### 4. 视频质量预检查工具 (quality/video_quality_precheck.py)

**用途**: 在上传前检查视频质量，避免无效上传
**推荐频率**: 批量上传前运行

```bash
python tools/quality/video_quality_precheck.py
```

**功能**:
- 文件基本信息检查（格式、大小、存在性）
- 技术规格检查（时长、尺寸、编码、帧率）
- 完整性检查（moov atom、数据有效性）
- 批量检查待上传素材

## 🚀 快速开始

### 日常维护流程

1. **每周健康检查**:
   ```bash
   python tools/system_health_checker.py
   ```

2. **发现错误时**:
   ```bash
   python tools/maintenance/error_analysis_and_fix.py
   ```

3. **批量上传前**:
   ```bash
   python tools/quality/video_quality_precheck.py
   ```

### 系统优化流程

1. **分析重试模式**:
   ```bash
   python tools/maintenance/smart_retry_optimizer.py
   ```

2. **应用优化建议**:
   - 根据工具输出的建议优化系统配置
   - 实施智能重试逻辑
   - 添加预检查机制

## 📊 工具输出说明

### 健康评分等级
- **90-100分**: 🟢 优秀 - 系统运行良好
- **70-89分**: 🟡 良好 - 系统基本正常，有小问题需要关注
- **50-69分**: 🟠 一般 - 系统存在一些问题，需要处理
- **0-49分**: 🔴 需要关注 - 系统存在较多问题，需要立即处理

### 错误分类
- **永久性错误**: 文件格式错误、视频尺寸错误、文件不存在等（不重试）
- **临时性错误**: 网络连接超时、服务器内部错误等（可重试）
- **特殊错误**: Token权限不足、账户状态异常等（特殊处理）

### 视频质量标准
- **时长**: 3-60秒
- **尺寸**: 最小480x480
- **格式**: mp4, mov, avi, mkv
- **编码**: h264, h265, hevc
- **文件大小**: 最大500MB

## 🔧 故障排除

### 常见问题

1. **导入错误**:
   ```
   确保项目根目录在Python路径中
   检查src/目录是否存在
   ```

2. **数据库连接失败**:
   ```
   检查数据库配置
   确保数据库服务正在运行
   ```

3. **ffprobe命令失败**:
   ```
   确保系统已安装ffmpeg
   检查PATH环境变量
   ```

### 日志文件位置
- 健康报告: `logs/health_report_YYYYMMDD_HHMMSS.txt`
- 系统日志: 根据项目配置

## 📝 开发指南

### 添加新工具

1. 在相应目录下创建工具文件
2. 遵循命名规范: `工具名称.py`
3. 添加完整的文档字符串
4. 更新本README文档

### 工具集成

新工具可以集成到 `system_health_checker.py` 中：

```python
# 导入新工具
from tools.new_category.new_tool import NewTool

# 在SystemHealthChecker类中添加检查方法
def check_new_feature_health(self) -> dict:
    # 实现检查逻辑
    pass
```

## 📞 支持

如果遇到问题或需要帮助：
1. 查看工具输出的错误信息和建议
2. 检查日志文件获取详细信息
3. 参考本文档的故障排除部分
4. 联系技术团队获取支持

---

**注意**: 这些工具是系统的核心维护工具，请谨慎使用，特别是涉及数据库修改的操作。建议在测试环境中先验证工具的效果。
