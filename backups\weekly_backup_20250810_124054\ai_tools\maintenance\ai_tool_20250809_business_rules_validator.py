#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目铁律验证器
清理条件: 成为铁律验证工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class BusinessRulesValidator:
    """千川自动化项目铁律验证器"""
    
    def __init__(self):
        self.violations = []
        self.passed_rules = []
    
    def run_complete_validation(self):
        """运行完整的铁律验证"""
        logger.info("🛡️ 千川自动化项目铁律验证")
        logger.info("="*100)
        
        # 验证核心业务铁律
        self._validate_core_business_rules()
        
        # 验证技术约束铁律
        self._validate_technical_constraints()

        # 验证配置一致性
        self._validate_configuration_consistency()

        # 验证安全和合规铁律
        self._validate_security_compliance()
        
        # 生成验证报告
        self._generate_validation_report()
    
    def _validate_core_business_rules(self):
        """验证核心业务铁律"""
        logger.info("\n🔍 第一部分：核心业务铁律验证")
        logger.info("-" * 80)
        
        # 铁律1：账户类型严格分离
        self._validate_account_type_separation()
        
        # 铁律2：素材唯一性测试约束
        self._validate_material_uniqueness()
        
        # 铁律3：账户状态操作权限
        self._validate_account_status_permissions()
        
        # 铁律4：防重复提审约束
        self._validate_duplicate_appeal_prevention()
    
    def _validate_account_type_separation(self):
        """验证账户类型分离"""
        logger.info("📊 验证铁律1：账户类型严格分离...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import AdAccount, Principal, Campaign
            
            with SessionLocal() as db:
                # 检查TEST账户是否被用于正式投放
                test_accounts_in_delivery = db.query(Campaign).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.account_type == 'TEST',
                    Campaign.status.in_(['DELIVERY_ACTIVE', 'DELIVERY_PAUSED'])
                ).count()
                
                if test_accounts_in_delivery > 0:
                    self.violations.append({
                        'rule': '铁律1：账户类型分离',
                        'severity': 'CRITICAL',
                        'issue': f'发现 {test_accounts_in_delivery} 个TEST账户被用于正式投放',
                        'impact': '违反账户分离原则，可能污染测试环境'
                    })
                else:
                    self.passed_rules.append('铁律1：账户类型分离')
                    logger.success("✅ 铁律1验证通过：账户类型严格分离")
                
        except Exception as e:
            logger.error(f"❌ 验证铁律1失败: {e}")
    
    def _validate_material_uniqueness(self):
        """验证素材唯一性"""
        logger.info("📊 验证铁律2：素材唯一性测试约束...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import LocalCreative, PlatformCreative, Campaign, AdAccount
            
            with SessionLocal() as db:
                # 检查是否有相同file_hash的素材创建了多个TEST计划
                duplicate_materials = db.execute("""
                    SELECT 
                        lc.file_hash,
                        lc.filename,
                        COUNT(DISTINCT c.id) as plan_count
                    FROM local_creatives lc
                    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
                    JOIN campaigns c ON cpca.campaign_id = c.id
                    JOIN ad_accounts aa ON c.account_id = aa.id
                    JOIN principals p ON aa.principal_id = p.id
                    WHERE p.name = '缇萃百货'
                    AND aa.account_type = 'TEST'
                    GROUP BY lc.file_hash, lc.filename
                    HAVING COUNT(DISTINCT c.id) > 1
                """).fetchall()
                
                if duplicate_materials:
                    for material in duplicate_materials:
                        self.violations.append({
                            'rule': '铁律2：素材唯一性约束',
                            'severity': 'HIGH',
                            'issue': f'素材 {material.filename} 创建了 {material.plan_count} 个TEST计划',
                            'impact': '违反素材唯一性原则，浪费测试资源'
                        })
                else:
                    self.passed_rules.append('铁律2：素材唯一性约束')
                    logger.success("✅ 铁律2验证通过：素材唯一性得到保证")
                
        except Exception as e:
            logger.error(f"❌ 验证铁律2失败: {e}")
    
    def _validate_account_status_permissions(self):
        """验证账户状态操作权限"""
        logger.info("📊 验证铁律3：账户状态操作权限...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            
            with SessionLocal() as db:
                # 检查deleted账户是否有活跃计划
                deleted_account_active_plans = db.query(Campaign).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.status == 'deleted',
                    Campaign.status.in_(['MONITORING', 'AUDITING', 'CREATED'])
                ).count()
                
                if deleted_account_active_plans > 0:
                    self.violations.append({
                        'rule': '铁律3：账户状态权限',
                        'severity': 'CRITICAL',
                        'issue': f'发现 {deleted_account_active_plans} 个deleted账户的活跃计划',
                        'impact': '违反deleted账户严禁操作原则'
                    })
                else:
                    self.passed_rules.append('铁律3：账户状态权限')
                    logger.success("✅ 铁律3验证通过：deleted账户无活跃计划")
                
        except Exception as e:
            logger.error(f"❌ 验证铁律3失败: {e}")
    
    def _validate_duplicate_appeal_prevention(self):
        """验证防重复提审"""
        logger.info("📊 验证铁律4：防重复提审约束...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, Principal, AdAccount
            
            with SessionLocal() as db:
                # 检查是否有重复提审的计划
                duplicate_appeals = db.query(Campaign).join(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    Campaign.appeal_status == 'appeal_pending',
                    Campaign.appeal_attempt_count > 1
                ).count()
                
                if duplicate_appeals > 0:
                    self.violations.append({
                        'rule': '铁律4：防重复提审',
                        'severity': 'HIGH',
                        'issue': f'发现 {duplicate_appeals} 个重复提审的计划',
                        'impact': '违反一次性提审原则，可能导致账户风险'
                    })
                else:
                    self.passed_rules.append('铁律4：防重复提审')
                    logger.success("✅ 铁律4验证通过：无重复提审计划")
                
        except Exception as e:
            logger.error(f"❌ 验证铁律4失败: {e}")
    
    def _validate_technical_constraints(self):
        """验证技术约束铁律"""
        logger.info("\n🔍 第二部分：技术约束铁律验证")
        logger.info("-" * 80)
        
        # 铁律5：API频率限制
        self._validate_api_rate_limits()
        
        # 铁律6：并发操作限制
        self._validate_concurrency_limits()

    def _validate_configuration_consistency(self):
        """验证配置一致性"""
        logger.info("\n🔍 第三部分：配置一致性验证")
        logger.info("-" * 80)

        # 验证计划创建数量配置
        self._validate_plan_creation_config()

    def _validate_plan_creation_config(self):
        """验证计划创建数量配置"""
        logger.info("📊 验证铁律12：计划创建数量配置...")

        try:
            from qianchuan_aw.utils.config_manager import get_config_manager

            config_manager = get_config_manager()
            app_settings = config_manager.get_config()

            # 检查TEST账户配置
            test_creative_count = app_settings.get('plan_creation_defaults', {}).get('test_workflow', {}).get('creative_count', 9)
            manual_creative_count = app_settings.get('plan_creation_defaults', {}).get('manual_workflow', {}).get('creative_count', 3)

            # 检查灵活分组配置
            flexible_max = app_settings.get('flexible_grouping', {}).get('max_creative_count', 9)

            # 验证配置一致性
            config_issues = []

            if test_creative_count != 9:
                config_issues.append(f"TEST账户creative_count应为9，当前为{test_creative_count}")

            if manual_creative_count != 3:
                config_issues.append(f"DELIVERY账户creative_count应为3，当前为{manual_creative_count}")

            if flexible_max != 9:
                config_issues.append(f"灵活分组max_creative_count应为9，当前为{flexible_max}")

            if config_issues:
                for issue in config_issues:
                    self.violations.append({
                        'rule': '铁律12：计划创建数量配置',
                        'severity': 'MEDIUM',
                        'issue': issue,
                        'impact': '配置不一致可能导致业务逻辑错误'
                    })
            else:
                self.passed_rules.append('铁律12：计划创建数量配置')
                logger.success("✅ 铁律12验证通过：计划创建数量配置正确")
                logger.info(f"   TEST账户: {test_creative_count}个素材/计划")
                logger.info(f"   DELIVERY账户: {manual_creative_count}个素材/计划")

        except Exception as e:
            logger.error(f"❌ 验证铁律12失败: {e}")
    
    def _validate_api_rate_limits(self):
        """验证API频率限制"""
        logger.info("📊 验证铁律5：API频率限制...")
        
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            
            config_manager = get_config_manager()
            app_settings = config_manager.get_config()
            
            rate_limiting = app_settings.get('rate_limiting', {})
            
            # 🔧 [业务规则修正] API频率限制应该禁用以提升性能
            if rate_limiting.get('enabled', False):
                self.violations.append({
                    'rule': '铁律5：API频率限制',
                    'severity': 'MEDIUM',
                    'issue': 'API频率限制仍然启用',
                    'impact': '可能影响系统性能，建议禁用'
                })
            else:
                self.passed_rules.append('铁律5：API频率限制')
                logger.success("✅ 铁律5验证通过：API频率限制已正确禁用")
                
        except Exception as e:
            logger.error(f"❌ 验证铁律5失败: {e}")
    
    def _validate_concurrency_limits(self):
        """验证并发限制"""
        logger.info("📊 验证铁律6：并发操作限制...")
        
        try:
            # 检查配置文件中的并发限制
            config_file = os.path.join(project_root, 'config', 'violation_detection.yml')
            
            if os.path.exists(config_file):
                self.passed_rules.append('铁律6：并发操作限制')
                logger.success("✅ 铁律6验证通过：并发限制配置存在")
            else:
                self.violations.append({
                    'rule': '铁律6：并发操作限制',
                    'severity': 'MEDIUM',
                    'issue': '并发限制配置文件缺失',
                    'impact': '可能导致系统过载'
                })
                
        except Exception as e:
            logger.error(f"❌ 验证铁律6失败: {e}")
    
    def _validate_security_compliance(self):
        """验证安全和合规铁律"""
        logger.info("\n🔍 第四部分：安全和合规铁律验证")
        logger.info("-" * 80)
        
        # 铁律14：敏感数据保护
        self._validate_sensitive_data_protection()
        
        # 铁律16：AI文件管理规范
        self._validate_ai_file_management()
    
    def _validate_sensitive_data_protection(self):
        """验证敏感数据保护"""
        logger.info("📊 验证铁律14：敏感数据保护...")
        
        try:
            # 检查敏感文件是否被正确保护
            gitignore_file = os.path.join(project_root, '.gitignore')
            
            if os.path.exists(gitignore_file):
                with open(gitignore_file, 'r', encoding='utf-8') as f:
                    gitignore_content = f.read()
                
                sensitive_patterns = ['auth_tokens.json', '*.key', '*.secret']
                protected_count = sum(1 for pattern in sensitive_patterns if pattern in gitignore_content)
                
                if protected_count >= 2:
                    self.passed_rules.append('铁律14：敏感数据保护')
                    logger.success("✅ 铁律14验证通过：敏感文件已保护")
                else:
                    self.violations.append({
                        'rule': '铁律14：敏感数据保护',
                        'severity': 'HIGH',
                        'issue': '敏感文件保护不完整',
                        'impact': '可能导致敏感信息泄露'
                    })
            else:
                self.violations.append({
                    'rule': '铁律14：敏感数据保护',
                    'severity': 'CRITICAL',
                    'issue': '.gitignore文件缺失',
                    'impact': '所有文件可能被意外提交'
                })
                
        except Exception as e:
            logger.error(f"❌ 验证铁律14失败: {e}")
    
    def _validate_ai_file_management(self):
        """验证AI文件管理规范"""
        logger.info("📊 验证铁律16：AI文件管理规范...")
        
        try:
            # 检查AI目录结构
            ai_directories = ['ai_tools', 'ai_temp', 'ai_reports', 'ai_templates']
            existing_dirs = []
            
            for dir_name in ai_directories:
                dir_path = os.path.join(project_root, dir_name)
                if os.path.exists(dir_path):
                    existing_dirs.append(dir_name)
            
            if len(existing_dirs) >= 3:
                self.passed_rules.append('铁律16：AI文件管理规范')
                logger.success(f"✅ 铁律16验证通过：AI目录结构完整 ({len(existing_dirs)}/4)")
            else:
                self.violations.append({
                    'rule': '铁律16：AI文件管理规范',
                    'severity': 'MEDIUM',
                    'issue': f'AI目录结构不完整 ({len(existing_dirs)}/4)',
                    'impact': 'AI文件管理可能不规范'
                })
                
        except Exception as e:
            logger.error(f"❌ 验证铁律16失败: {e}")
    
    def _generate_validation_report(self):
        """生成验证报告"""
        logger.info("\n📋 千川自动化项目铁律验证报告")
        logger.info("="*100)
        
        # 统计结果
        total_rules = len(self.passed_rules) + len(self.violations)
        passed_count = len(self.passed_rules)
        violation_count = len(self.violations)
        
        logger.info(f"📊 验证统计:")
        logger.info(f"   ✅ 通过: {passed_count} 条铁律")
        logger.info(f"   ❌ 违规: {violation_count} 条铁律")
        logger.info(f"   📈 合规率: {(passed_count/total_rules*100):.1f}%" if total_rules > 0 else "   📈 合规率: 0%")
        
        # 通过的铁律
        if self.passed_rules:
            logger.info(f"\n✅ 通过验证的铁律:")
            for rule in self.passed_rules:
                logger.info(f"   ✅ {rule}")
        
        # 违规的铁律
        if self.violations:
            logger.info(f"\n❌ 发现的违规:")
            
            # 按严重程度分组
            critical_violations = [v for v in self.violations if v['severity'] == 'CRITICAL']
            high_violations = [v for v in self.violations if v['severity'] == 'HIGH']
            medium_violations = [v for v in self.violations if v['severity'] == 'MEDIUM']
            
            for violations, icon in [(critical_violations, '🚨'), (high_violations, '🔴'), (medium_violations, '🟡')]:
                for violation in violations:
                    logger.info(f"   {icon} {violation['rule']}")
                    logger.info(f"      问题: {violation['issue']}")
                    logger.info(f"      影响: {violation['impact']}")
        
        # 总体评估
        if violation_count == 0:
            logger.success("🎊 所有铁律验证通过，系统完全合规")
            return 100
        elif critical_violations:
            logger.error("🚨 发现Critical级别违规，需要立即修复")
            return 0
        elif high_violations:
            logger.warning("🔴 发现High级别违规，需要优先修复")
            return 60
        else:
            logger.info("🟡 发现Medium级别违规，建议修复")
            return 80


def main():
    """主函数"""
    validator = BusinessRulesValidator()
    
    logger.info("🚀 启动千川自动化项目铁律验证")
    logger.info("🎯 目标：确保所有业务铁律得到严格执行")
    
    compliance_score = validator.run_complete_validation()
    
    if compliance_score >= 90:
        logger.success("🎊 铁律验证完成，系统高度合规")
        return 0
    elif compliance_score >= 70:
        logger.warning("⚠️ 铁律验证完成，系统基本合规")
        return 1
    else:
        logger.error("❌ 铁律验证完成，发现严重违规")
        return 2


if __name__ == "__main__":
    exit(main())
