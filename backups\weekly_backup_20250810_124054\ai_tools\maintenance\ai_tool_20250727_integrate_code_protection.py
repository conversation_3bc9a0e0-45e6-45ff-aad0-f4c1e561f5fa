#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 集成代码层面素材唯一性保护
清理条件: 完全集成到主系统后可删除

千川自动化代码层面保护集成工具
========================

将MaterialUniquenessGuard保护机制集成到计划创建逻辑中
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


def create_protection_wrapper():
    """创建保护包装器"""
    wrapper_code = '''
# 千川自动化素材唯一性保护包装器
# ===============================
# 集成到计划创建逻辑中的保护机制

import os
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from functools import wraps

# 添加AI工具路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
ai_tools_path = os.path.join(project_root, 'ai_tools', 'maintenance')
sys.path.insert(0, ai_tools_path)

try:
    from ai_tool_20250727_code_level_protection import (
        MaterialUniquenessGuard,
        MaterialUniquenessViolationError,
        material_uniqueness_required
    )
    PROTECTION_AVAILABLE = True
    logger.info("✅ 素材唯一性保护模块加载成功")
except ImportError as e:
    PROTECTION_AVAILABLE = False
    logger.warning(f"⚠️ 素材唯一性保护模块加载失败: {e}")

# 全局保护实例
if PROTECTION_AVAILABLE:
    material_guard = MaterialUniquenessGuard()
else:
    material_guard = None


def enforce_material_uniqueness(platform_creatives: List, operation_name: str = "计划创建"):
    """
    强制执行素材唯一性检查
    
    Args:
        platform_creatives: 平台素材列表
        operation_name: 操作名称
        
    Raises:
        MaterialUniquenessViolationError: 违反唯一性时抛出
    """
    if not PROTECTION_AVAILABLE or not material_guard:
        logger.warning("⚠️ 素材唯一性保护不可用，跳过检查")
        return
    
    logger.info(f"🔍 执行素材唯一性检查: {operation_name}")
    
    violations = []
    
    for pc in platform_creatives:
        try:
            # 获取本地素材信息
            from qianchuan_aw.database.models import LocalCreative
            from qianchuan_aw.utils.db_utils import database_session
            
            with database_session() as db:
                local_creative = db.query(LocalCreative).filter(
                    LocalCreative.id == pc.local_creative_id
                ).first()
                
                if not local_creative:
                    violations.append(f"平台素材 {pc.id} 找不到对应的本地素材")
                    continue
                
                # 执行唯一性检查
                check_result = material_guard.check_material_uniqueness(
                    local_creative.file_hash,
                    exclude_material_id=local_creative.id
                )
                
                if not check_result['is_unique']:
                    violation_msg = (
                        f"素材 '{local_creative.filename}' "
                        f"已存在 {check_result['existing_count']} 个测试计划"
                    )
                    violations.append(violation_msg)
                    
                    # 记录详细违规信息
                    logger.error(f"🚨 素材唯一性违规: {violation_msg}")
                    for plan in check_result['existing_plans']:
                        logger.error(f"   现有计划: {plan['campaign_id_qc']} ({plan['campaign_status']})")
        
        except Exception as e:
            violations.append(f"检查素材 {pc.id} 时发生异常: {str(e)}")
            logger.error(f"素材唯一性检查异常: {e}")
    
    # 如果有违规，阻止操作
    if violations:
        violation_summary = f"发现 {len(violations)} 个素材违规:\\n" + "\\n".join(f"- {v}" for v in violations)
        
        if PROTECTION_AVAILABLE:
            raise MaterialUniquenessViolationError(
                f"🚨 {operation_name}被阻止，素材唯一性测试铁律违规:\\n{violation_summary}"
            )
        else:
            raise Exception(f"🚨 {operation_name}被阻止，素材唯一性违规:\\n{violation_summary}")
    
    logger.info(f"✅ 素材唯一性检查通过: {len(platform_creatives)} 个素材")


def protected_plan_creation(original_func):
    """
    计划创建保护装饰器
    
    Args:
        original_func: 原始的计划创建函数
        
    Returns:
        包装后的函数
    """
    @wraps(original_func)
    def wrapper(*args, **kwargs):
        # 尝试从参数中提取platform_creatives
        platform_creatives = None
        
        # 检查关键字参数
        if 'platform_creatives' in kwargs:
            platform_creatives = kwargs['platform_creatives']
        
        # 检查位置参数
        for arg in args:
            if hasattr(arg, '__iter__') and arg:
                # 检查是否是PlatformCreative列表
                first_item = next(iter(arg), None)
                if first_item and hasattr(first_item, 'local_creative_id'):
                    platform_creatives = arg
                    break
        
        # 执行保护检查
        if platform_creatives:
            try:
                enforce_material_uniqueness(platform_creatives, "计划创建")
            except Exception as e:
                logger.error(f"🚨 计划创建被保护机制阻止: {e}")
                raise
        else:
            logger.warning("⚠️ 无法从参数中提取platform_creatives，跳过唯一性检查")
        
        # 执行原始函数
        logger.info("✅ 素材唯一性检查通过，执行计划创建")
        return original_func(*args, **kwargs)
    
    return wrapper


# 使用示例和集成指南
INTEGRATION_GUIDE = """
千川自动化素材唯一性保护集成指南
=============================

1. 在计划创建函数上添加保护装饰器:

```python
from protection_wrapper import protected_plan_creation

@protected_plan_creation
def create_ad_plan(db, principal, account, platform_creatives, **kwargs):
    # 原有的计划创建逻辑
    pass
```

2. 手动调用保护检查:

```python
from protection_wrapper import enforce_material_uniqueness

def some_plan_creation_function(platform_creatives):
    # 执行保护检查
    enforce_material_uniqueness(platform_creatives, "手动计划创建")
    
    # 继续执行计划创建逻辑
    pass
```

3. 异常处理:

```python
try:
    create_ad_plan(db, principal, account, platform_creatives)
except MaterialUniquenessViolationError as e:
    logger.error(f"素材唯一性违规: {e}")
    # 处理违规情况
except Exception as e:
    logger.error(f"计划创建失败: {e}")
    # 处理其他异常
```

集成完成后，所有计划创建操作都将受到素材唯一性保护！
"""

def main():
    print("集成指南:")
    print(INTEGRATION_GUIDE)

if __name__ == '__main__':
    main()
'''
    
    return wrapper_code


def integrate_protection():
    """集成保护机制"""
    logger.info("🛡️ 开始集成代码层面素材唯一性保护...")
    
    try:
        # 创建保护包装器
        wrapper_code = create_protection_wrapper()
        
        # 保存包装器文件
        wrapper_file = os.path.join(project_root, 'src', 'qianchuan_aw', 'utils', 'protection_wrapper.py')
        os.makedirs(os.path.dirname(wrapper_file), exist_ok=True)
        
        with open(wrapper_file, 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        logger.info(f"✅ 保护包装器已创建: {wrapper_file}")
        
        # 创建集成示例
        integration_example = '''
# 千川自动化计划创建保护集成示例
# ===============================

# 在 src/qianchuan_aw/workflows/common/plan_creation.py 中添加:

from qianchuan_aw.utils.protection_wrapper import protected_plan_creation, enforce_material_uniqueness
from qianchuan_aw.utils.logger import logger

@protected_plan_creation
def create_ad_plan(db, principal, account, platform_creatives, **kwargs):
    """
    增强的广告计划创建函数 - 带素材唯一性保护
    
    Args:
        db: 数据库会话
        principal: 主体
        account: 广告账户
        platform_creatives: 平台素材列表
        **kwargs: 其他参数
    """
    logger.info(f"开始创建广告计划，素材数量: {len(platform_creatives)}")
    
    # 保护装饰器会自动执行素材唯一性检查
    # 如果违规，会抛出MaterialUniquenessViolationError异常
    
    # 原有的计划创建逻辑保持不变...
    # [在这里插入原有的 create_ad_plan 函数体]
    
    logger.info("✅ 广告计划创建成功")
    return result

# 在调度器中的使用示例:
def scheduled_plan_creation(db, platform_creatives_batch):
    """调度器中的计划创建"""
    
    try:
        # 手动执行保护检查
        enforce_material_uniqueness(platform_creatives_batch, "调度器计划创建")
        
        # 执行计划创建
        result = create_ad_plan(db, platform_creatives_batch)
        
        logger.info("✅ 调度器计划创建成功")
        return result
        
    except MaterialUniquenessViolationError as e:
        logger.error(f"🚨 调度器计划创建被阻止: {e}")
        # 记录违规尝试，但不中断调度器运行
        return None
        
    except Exception as e:
        logger.error(f"❌ 调度器计划创建失败: {e}")
        raise
'''
        
        example_file = os.path.join(project_root, 'ai_tools', 'maintenance', 'integration_example.py')
        with open(example_file, 'w', encoding='utf-8') as f:
            f.write(integration_example)
        
        logger.info(f"✅ 集成示例已创建: {example_file}")
        
        # 测试保护包装器
        logger.info("🧪 测试保护包装器...")
        
        # 导入测试
        sys.path.insert(0, os.path.join(project_root, 'src'))
        try:
            from qianchuan_aw.utils.protection_wrapper import enforce_material_uniqueness
            logger.info("✅ 保护包装器导入成功")
        except ImportError as e:
            logger.warning(f"⚠️ 保护包装器导入失败: {e}")
        
        logger.info("✅ 代码层面保护集成完成")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 代码层面保护集成失败: {e}")
        return False


def main():
    """主函数"""
    print("🛡️ 千川自动化代码层面保护集成")
    print("=" * 50)
    
    success = integrate_protection()
    
    if success:
        print("✅ 代码层面保护集成成功！")
        print("\n📋 下一步操作:")
        print("1. 在计划创建函数上添加 @protected_plan_creation 装饰器")
        print("2. 在关键位置调用 enforce_material_uniqueness() 函数")
        print("3. 添加 MaterialUniquenessViolationError 异常处理")
        print("4. 测试保护机制是否正常工作")
        
        print("\n📄 相关文件:")
        print(f"- 保护包装器: src/qianchuan_aw/utils/protection_wrapper.py")
        print(f"- 集成示例: ai_tools/maintenance/integration_example.py")
        
        return 0
    else:
        print("❌ 代码层面保护集成失败")
        return 1


if __name__ == '__main__':
    exit(main())
