#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证优化的批量提审服务
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def get_database_connection():
    """获取数据库连接"""
    config = load_config()
    db_config = config['database']['postgresql']
    return psycopg2.connect(
        host=db_config['host'],
        port=db_config['port'],
        database=db_config['dbname'],
        user=db_config['user'],
        password=db_config['password']
    )

def get_test_plans_for_optimization():
    """获取用于优化测试的计划"""
    logger.info("🔍 查找用于优化测试的计划...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        # 查找今天创建的所有计划，按广告户分组
        cursor.execute("""
            SELECT 
                c.campaign_id_qc,
                c.status,
                c.appeal_status,
                c.appeal_error_message,
                c.created_at,
                a.account_id_qc,
                p.name as principal_name
            FROM campaigns c
            JOIN ad_accounts a ON c.account_id = a.id
            JOIN principals p ON a.principal_id = p.id
            WHERE c.created_at >= CURRENT_DATE
            AND c.status = 'AUDITING'
            ORDER BY p.name, a.account_id_qc, c.created_at DESC
        """)
        
        results = cursor.fetchall()
        cursor.close()
        conn.close()
        
        plans = []
        for result in results:
            campaign_id_qc, status, appeal_status, appeal_error_message, created_at, account_id_qc, principal_name = result
            
            plan_info = {
                'campaign_id': campaign_id_qc,
                'status': status,
                'appeal_status': appeal_status,
                'appeal_error_message': appeal_error_message,
                'created_at': created_at,
                'account_id': account_id_qc,
                'principal_name': principal_name
            }
            plans.append(plan_info)
        
        logger.success(f"✅ 找到 {len(plans)} 个测试计划")
        
        # 按广告户统计
        from collections import defaultdict
        account_stats = defaultdict(int)
        for plan in plans:
            account_key = f"{plan['principal_name']}_{plan['account_id']}"
            account_stats[account_key] += 1
        
        logger.info(f"📊 广告户分布:")
        for account_key, count in account_stats.items():
            logger.info(f"   📋 {account_key}: {count} 个计划")
        
        return plans
        
    except Exception as e:
        logger.error(f"❌ 查找测试计划失败: {e}")
        return []

def reset_plans_for_optimization_test(plans):
    """重置计划状态用于优化测试"""
    logger.info(f"🔄 重置 {len(plans)} 个计划状态用于优化测试...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        campaign_ids = [plan['campaign_id'] for plan in plans]
        
        # 批量重置提审状态
        cursor.execute("""
            UPDATE campaigns 
            SET appeal_status = NULL,
                appeal_error_message = NULL,
                first_appeal_at = NULL
            WHERE campaign_id_qc = ANY(%s)
        """, (campaign_ids,))
        
        affected_rows = cursor.rowcount
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.success(f"✅ 已重置 {affected_rows} 个计划的提审状态")
        return affected_rows == len(plans)
        
    except Exception as e:
        logger.error(f"❌ 重置计划状态失败: {e}")
        return False

def test_optimized_batch_appeal(plans):
    """测试优化的批量提审"""
    logger.info("🚀 测试优化的批量提审服务...")
    
    try:
        sys.path.insert(0, str(project_root / 'ai_tools' / 'optimization'))
        from ai_tool_20250728_optimization_batch_appeal_service import create_optimized_batch_appeal_service
        
        app_settings = load_config()
        
        # 创建优化的批量提审服务
        service = create_optimized_batch_appeal_service(app_settings)
        
        # 获取性能统计
        stats = service.get_performance_statistics(plans)
        
        logger.info("📊 性能优化预期效果:")
        logger.info(f"   📋 总计划数: {stats['total_plans']}")
        logger.info(f"   🏢 总广告户数: {stats['total_accounts']}")
        logger.info(f"   🔄 旧方式浏览器会话: {stats['old_browser_sessions']}")
        logger.info(f"   ✨ 新方式浏览器会话: {stats['new_browser_sessions']}")
        logger.info(f"   💾 资源节约: {stats['resource_savings_percent']:.1f}%")
        logger.info(f"   ⏱️ 预计时间节约: {stats['time_savings_percent']:.1f}%")
        logger.info(f"   📈 旧方式预计时间: {stats['old_time_estimate_seconds']} 秒")
        logger.info(f"   🚀 新方式预计时间: {stats['new_time_estimate_seconds']} 秒")
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行优化的批量提审
        logger.info("\n🎯 开始执行优化的批量提审...")
        results = service.batch_appeal_all_plans(plans)
        
        # 记录结束时间
        end_time = time.time()
        actual_time = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        logger.info(f"\n📊 优化批量提审结果:")
        logger.info(f"   ✅ 成功: {success_count}/{total_count}")
        logger.info(f"   📈 成功率: {success_rate:.1f}%")
        logger.info(f"   ⏱️ 实际用时: {actual_time:.1f} 秒")
        logger.info(f"   🚀 预计用时: {stats['new_time_estimate_seconds']} 秒")
        logger.info(f"   📊 时间效率: {(stats['new_time_estimate_seconds'] / actual_time * 100):.1f}%")
        
        return results, stats, actual_time
        
    except Exception as e:
        logger.error(f"❌ 优化批量提审测试失败: {e}")
        return [], {}, 0

def test_optimized_batch_query(plans):
    """测试优化的批量查询申诉进度"""
    logger.info("🔍 测试优化的批量查询申诉进度...")
    
    try:
        sys.path.insert(0, str(project_root / 'ai_tools' / 'optimization'))
        from ai_tool_20250728_optimization_batch_appeal_service import create_optimized_batch_appeal_service
        
        app_settings = load_config()
        
        # 创建优化的批量提审服务
        service = create_optimized_batch_appeal_service(app_settings)
        
        # 记录开始时间
        start_time = time.time()
        
        # 执行优化的批量查询
        logger.info("\n🔍 开始执行优化的批量查询...")
        results = service.batch_query_all_plans(plans)
        
        # 记录结束时间
        end_time = time.time()
        actual_time = end_time - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        logger.info(f"\n📊 优化批量查询结果:")
        logger.info(f"   ✅ 成功: {success_count}/{total_count}")
        logger.info(f"   📈 成功率: {success_rate:.1f}%")
        logger.info(f"   ⏱️ 实际用时: {actual_time:.1f} 秒")
        
        return results, actual_time
        
    except Exception as e:
        logger.error(f"❌ 优化批量查询测试失败: {e}")
        return [], 0

def update_database_with_results(results):
    """根据结果更新数据库"""
    logger.info("💾 根据提审结果更新数据库...")
    
    try:
        conn = get_database_connection()
        cursor = conn.cursor()
        
        updated_count = 0
        
        for result in results:
            if result['success']:
                # 提审成功，更新状态
                cursor.execute("""
                    UPDATE campaigns 
                    SET appeal_status = 'appeal_pending',
                        first_appeal_at = NOW(),
                        appeal_error_message = NULL
                    WHERE campaign_id_qc = %s
                """, (result['campaign_id'],))
                
                if cursor.rowcount > 0:
                    updated_count += 1
            else:
                # 提审失败，记录错误
                cursor.execute("""
                    UPDATE campaigns 
                    SET appeal_status = 'submission_failed',
                        appeal_error_message = %s
                    WHERE campaign_id_qc = %s
                """, (result['message'][:500], result['campaign_id']))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.success(f"✅ 数据库更新完成: {updated_count} 个成功状态")
        return updated_count
        
    except Exception as e:
        logger.error(f"❌ 数据库更新失败: {e}")
        return 0

def main():
    """主函数"""
    logger.info("🎯 开始优化的批量提审验证")
    logger.info("="*80)
    logger.info("🚀 此测试将验证按广告户分组的优化批量提审功能")
    logger.info("💡 优化目标：减少浏览器会话数量，提高处理效率")
    logger.info("="*80)
    
    try:
        # 1. 获取测试计划
        plans = get_test_plans_for_optimization()
        if not plans:
            logger.error("❌ 无法获取测试计划")
            return False
        
        # 2. 重置计划状态
        if not reset_plans_for_optimization_test(plans):
            logger.error("❌ 无法重置计划状态")
            return False
        
        # 3. 测试优化的批量提审
        appeal_results, stats, appeal_time = test_optimized_batch_appeal(plans)
        
        # 4. 更新数据库
        updated_count = update_database_with_results(appeal_results)
        
        # 5. 测试优化的批量查询（使用部分计划）
        query_plans = plans[:3] if len(plans) > 3 else plans  # 只查询前3个计划作为示例
        query_results, query_time = test_optimized_batch_query(query_plans)
        
        # 生成最终报告
        logger.info("\n" + "="*80)
        logger.info("🎯 优化批量提审验证结果")
        logger.info("="*80)
        
        appeal_success_count = sum(1 for r in appeal_results if r['success'])
        query_success_count = sum(1 for r in query_results if r['success'])
        
        logger.info(f"📊 提审测试结果:")
        logger.info(f"   📋 测试计划: {len(plans)} 个")
        logger.info(f"   🏢 涉及广告户: {stats.get('total_accounts', 0)} 个")
        logger.info(f"   ✅ 提审成功: {appeal_success_count}/{len(appeal_results)}")
        logger.info(f"   💾 数据库更新: {updated_count} 个")
        logger.info(f"   ⏱️ 提审用时: {appeal_time:.1f} 秒")
        
        logger.info(f"\n📊 查询测试结果:")
        logger.info(f"   📋 查询计划: {len(query_plans)} 个")
        logger.info(f"   ✅ 查询成功: {query_success_count}/{len(query_results)}")
        logger.info(f"   ⏱️ 查询用时: {query_time:.1f} 秒")
        
        logger.info(f"\n🚀 性能优化效果:")
        logger.info(f"   💾 资源节约: {stats.get('resource_savings_percent', 0):.1f}%")
        logger.info(f"   ⏱️ 时间节约: {stats.get('time_savings_percent', 0):.1f}%")
        logger.info(f"   🔄 浏览器会话: {stats.get('old_browser_sessions', 0)} → {stats.get('new_browser_sessions', 0)}")
        
        # 计算总体成功率
        total_operations = len(appeal_results) + len(query_results)
        total_success = appeal_success_count + query_success_count
        overall_success_rate = (total_success / total_operations) * 100 if total_operations > 0 else 0
        
        logger.info(f"\n📈 总体成功率: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 80:
            logger.success("\n🎉 优化批量提审验证完全成功！")
            logger.info("\n📋 验证总结:")
            logger.info("✅ 按广告户分组优化工作正常")
            logger.info("✅ 单会话多计划处理正常")
            logger.info("✅ 资源和时间显著节约")
            logger.info("✅ 批量提审和查询功能正常")
            logger.info("✅ 优化方案完全可行")
            
            logger.info("\n🎯 优化效果:")
            logger.info("- 大幅减少浏览器启动次数")
            logger.info("- 显著提高处理效率")
            logger.info("- 降低系统资源消耗")
            logger.info("- 保持功能完整性")
            
        elif overall_success_rate >= 60:
            logger.warning("⚠️ 优化批量提审基本成功，但仍有改进空间")
        else:
            logger.error("❌ 优化批量提审验证失败")
        
        return overall_success_rate >= 60
        
    except Exception as e:
        logger.error(f"❌ 优化验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 千川自动化项目优化批量提审验证成功！")
        logger.info("💡 按广告户分组的优化方案完全可行")
        logger.info("💡 可以显著提高提审处理效率")
    else:
        logger.error("\n❌ 优化验证失败，请检查具体错误信息")
    
    sys.exit(0 if success else 1)
