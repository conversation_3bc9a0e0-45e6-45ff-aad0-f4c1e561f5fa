#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 最终验证计划提审功能是否完全修复
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psycopg2
import yaml
import json
from pathlib import Path
from datetime import datetime, timedelta
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def load_config():
    """加载配置文件"""
    config_path = project_root / 'config' / 'settings.yml'
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_cookies_file():
    """检查cookies文件"""
    logger.info("🔍 检查cookies文件...")
    
    cookies_file = project_root / 'config' / 'cookies.json'
    
    if not cookies_file.exists():
        logger.error("❌ cookies.json文件不存在")
        return False, []
    
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        principals = list(cookies_data.keys())
        logger.success(f"✅ cookies文件存在，包含 {len(principals)} 个主体")
        
        for principal in principals:
            cookies_count = len(cookies_data[principal])
            logger.info(f"   🔹 {principal}: {cookies_count} 个cookies")
        
        return True, principals
        
    except Exception as e:
        logger.error(f"❌ 读取cookies文件失败: {e}")
        return False, []

def check_database_principals():
    """检查数据库中的主体"""
    logger.info("🔍 检查数据库中的主体...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM principals ORDER BY name")
        db_principals = [row[0] for row in cursor.fetchall()]
        cursor.close()
        conn.close()
        
        logger.success(f"✅ 数据库中有 {len(db_principals)} 个主体")
        for principal in db_principals:
            logger.info(f"   🔹 {principal}")
        
        return True, db_principals
        
    except Exception as e:
        logger.error(f"❌ 检查数据库主体失败: {e}")
        return False, []

def check_principal_mapping():
    """检查主体映射"""
    logger.info("🔍 检查主体映射...")
    
    cookies_ok, cookies_principals = check_cookies_file()
    db_ok, db_principals = check_database_principals()
    
    if not cookies_ok or not db_ok:
        logger.error("❌ 无法检查主体映射")
        return False, []
    
    missing_principals = []
    for db_principal in db_principals:
        if db_principal not in cookies_principals:
            missing_principals.append(db_principal)
            logger.error(f"❌ 主体 '{db_principal}' 缺少cookies")
        else:
            logger.success(f"✅ 主体 '{db_principal}' 有对应cookies")
    
    if missing_principals:
        logger.error(f"❌ {len(missing_principals)} 个主体缺少cookies")
        return False, missing_principals
    else:
        logger.success("✅ 所有主体都有对应的cookies")
        return True, []

def check_unsubmitted_plans():
    """检查未提审的计划"""
    logger.info("🔍 检查未提审的计划...")
    
    try:
        config = load_config()
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 检查未提审的计划
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '1 hour' THEN 1 END) as recent_1h
            FROM campaigns 
            WHERE status = 'AUDITING' 
            AND appeal_status IS NULL 
            AND first_appeal_at IS NULL
        """)
        
        total, recent_1h = cursor.fetchone()
        
        logger.info(f"📊 未提审计划统计:")
        logger.info(f"   📋 总计: {total}")
        logger.info(f"   🕐 最近1小时: {recent_1h}")
        
        if total > 0:
            # 显示最近的几个未提审计划
            cursor.execute("""
                SELECT campaign_id_qc, created_at
                FROM campaigns 
                WHERE status = 'AUDITING' 
                AND appeal_status IS NULL 
                AND first_appeal_at IS NULL
                ORDER BY created_at DESC
                LIMIT 5
            """)
            
            logger.info("📋 最近未提审的计划:")
            for campaign_id, created_at in cursor.fetchall():
                logger.info(f"   📌 {campaign_id} - 创建于 {created_at}")
        
        cursor.close()
        conn.close()
        
        return total, recent_1h
        
    except Exception as e:
        logger.error(f"❌ 检查未提审计划失败: {e}")
        return -1, -1

def manual_trigger_submission():
    """手动触发提审任务"""
    logger.info("🎯 手动触发提审任务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        # 记录触发前的状态
        total_before, _ = check_unsubmitted_plans()
        
        logger.info(f"📊 触发前未提审计划: {total_before}")
        
        # 手动触发任务
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 任务已派发，ID: {result.id}")
        
        # 等待任务执行
        logger.info("⏳ 等待任务执行...")
        
        for i in range(30):  # 等待30秒
            time.sleep(1)
            
            if result.ready():
                if result.successful():
                    logger.success("✅ 任务执行成功")
                    break
                else:
                    logger.error(f"❌ 任务执行失败: {result.result}")
                    return False, str(result.result)
            
            if i % 5 == 0 and i > 0:
                logger.info(f"⏳ 等待中... ({i}秒)")
        
        if not result.ready():
            logger.warning("⚠️ 任务仍在执行中")
        
        # 等待一段时间后检查结果
        time.sleep(5)
        total_after, _ = check_unsubmitted_plans()
        
        logger.info(f"📊 触发后未提审计划: {total_after}")
        
        if total_after < total_before:
            processed = total_before - total_after
            logger.success(f"✅ 成功处理了 {processed} 个计划")
            return True, f"处理了 {processed} 个计划"
        elif total_before == 0:
            logger.info("📋 没有需要提审的计划")
            return True, "没有需要提审的计划"
        else:
            logger.warning("⚠️ 未检测到计划状态变化")
            return False, "未检测到状态变化"
            
    except Exception as e:
        logger.error(f"❌ 手动触发失败: {e}")
        return False, str(e)

def check_recent_logs():
    """检查最近的日志"""
    logger.info("📋 检查最近的提审日志...")
    
    try:
        log_dir = project_root / 'logs'
        
        if not log_dir.exists():
            logger.warning("⚠️ 日志目录不存在")
            return False
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob('*.log'))
        if not log_files:
            logger.warning("⚠️ 未找到日志文件")
            return False
        
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        logger.info(f"📄 检查日志文件: {latest_log}")
        
        # 读取最近的日志内容
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近5分钟的提审相关日志
        recent_logs = []
        error_logs = []
        success_logs = []
        
        for line in lines[-500:]:  # 只检查最后500行
            if any(keyword in line for keyword in ['提审', 'appeal', 'submit_plans']):
                recent_logs.append(line.strip())
                
                if any(error in line for error in ['失败', 'ERROR', '❌']):
                    error_logs.append(line.strip())
                elif any(success in line for success in ['成功', 'SUCCESS', '✅']):
                    success_logs.append(line.strip())
        
        logger.info(f"📊 最近提审相关日志: {len(recent_logs)} 条")
        logger.info(f"📊 成功日志: {len(success_logs)} 条")
        logger.info(f"📊 错误日志: {len(error_logs)} 条")
        
        if recent_logs:
            logger.info("📋 最近的提审日志（最后5条）:")
            for log_line in recent_logs[-5:]:
                if any(error in log_line for error in ['失败', 'ERROR', '❌']):
                    logger.error(f"   🚨 {log_line}")
                elif any(success in log_line for success in ['成功', 'SUCCESS', '✅']):
                    logger.success(f"   ✅ {log_line}")
                else:
                    logger.info(f"   📋 {log_line}")
        
        # 检查是否还有cookies或资源相关的错误
        cookies_errors = [line for line in error_logs if 'cookies' in line.lower()]
        resource_errors = [line for line in error_logs if '系统资源不足' in line]
        
        if cookies_errors:
            logger.error(f"❌ 仍有cookies相关错误: {len(cookies_errors)} 条")
            return False
        
        if resource_errors:
            logger.error(f"❌ 仍有资源不足错误: {len(resource_errors)} 条")
            return False
        
        logger.success("✅ 未发现cookies或资源相关错误")
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查日志失败: {e}")
        return False

def run_final_verification():
    """运行最终验证"""
    logger.info("🚀 开始计划提审功能最终验证...")
    logger.info("="*80)
    
    verification_results = {
        'cookies_file_ok': False,
        'principal_mapping_ok': False,
        'manual_trigger_ok': False,
        'logs_clean': False
    }
    
    issues_found = []
    recommendations = []
    
    # 1. 检查cookies文件和主体映射
    mapping_ok, missing = check_principal_mapping()
    verification_results['cookies_file_ok'] = mapping_ok
    verification_results['principal_mapping_ok'] = mapping_ok
    
    if not mapping_ok:
        issues_found.append(f"主体映射问题: {missing}")
        recommendations.append("确保cookies文件包含所有数据库主体")
    
    # 2. 手动触发测试
    trigger_ok, trigger_msg = manual_trigger_submission()
    verification_results['manual_trigger_ok'] = trigger_ok
    
    if not trigger_ok:
        issues_found.append(f"手动触发失败: {trigger_msg}")
        recommendations.append("检查提审任务执行逻辑")
    
    # 3. 检查日志
    logs_ok = check_recent_logs()
    verification_results['logs_clean'] = logs_ok
    
    if not logs_ok:
        issues_found.append("日志中仍有错误")
        recommendations.append("检查具体的错误信息")
    
    # 生成验证报告
    logger.info("\n" + "="*80)
    logger.info("🎯 最终验证结果")
    logger.info("="*80)
    
    success_count = sum(verification_results.values())
    total_count = len(verification_results)
    
    for check_name, result in verification_results.items():
        status = "✅" if result else "❌"
        check_display = check_name.replace('_', ' ').title()
        logger.info(f"{status} {check_display}")
    
    success_rate = (success_count / total_count) * 100
    logger.info(f"\n📈 验证通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 输出问题和建议
    if issues_found:
        logger.error("\n🚨 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            logger.error(f"   {i}. {issue}")
    
    if recommendations:
        logger.info("\n💡 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
    
    # 最终判断
    if success_rate >= 100:
        logger.success("\n🎉 计划提审功能完全修复成功！")
        logger.info("\n📋 修复总结:")
        logger.info("✅ 系统资源检查已移除")
        logger.info("✅ cookies文件问题已解决")
        logger.info("✅ 主体映射完全正确")
        logger.info("✅ 提审任务可以正常执行")
        logger.info("✅ 日志中无相关错误")
        
        logger.info("\n🎯 预期效果:")
        logger.info("- 新创建的计划将在90秒内自动提审")
        logger.info("- 不再出现'系统资源不足'或'cookies缺失'错误")
        logger.info("- 完整的工作流：创建→提审→监控→收割")
        
    elif success_rate >= 75:
        logger.warning("\n⚠️ 计划提审功能基本修复，但仍有小问题")
    else:
        logger.error("\n❌ 计划提审功能仍存在严重问题")
    
    return verification_results, issues_found, recommendations

def main():
    """主函数"""
    try:
        results, issues, recommendations = run_final_verification()
        
        # 保存验证报告
        report_file = project_root / 'ai_temp' / f'final_plan_submission_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'verification_results': results,
            'issues_found': issues,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 最终验证报告已保存: {report_file}")
        
        # 返回成功率
        success_rate = sum(results.values()) / len(results) * 100
        return success_rate >= 75
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
