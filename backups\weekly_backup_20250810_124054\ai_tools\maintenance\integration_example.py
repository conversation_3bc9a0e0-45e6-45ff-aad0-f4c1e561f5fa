
# 千川自动化计划创建保护集成示例
# ===============================

# 在 src/qianchuan_aw/workflows/common/plan_creation.py 中添加:

from qianchuan_aw.utils.protection_wrapper import protected_plan_creation, enforce_material_uniqueness
from qianchuan_aw.utils.logger import logger

@protected_plan_creation
def create_ad_plan(db, principal, account, platform_creatives, **kwargs):
    """
    增强的广告计划创建函数 - 带素材唯一性保护
    
    Args:
        db: 数据库会话
        principal: 主体
        account: 广告账户
        platform_creatives: 平台素材列表
        **kwargs: 其他参数
    """
    logger.info(f"开始创建广告计划，素材数量: {len(platform_creatives)}")
    
    # 保护装饰器会自动执行素材唯一性检查
    # 如果违规，会抛出MaterialUniquenessViolationError异常
    
    # 原有的计划创建逻辑保持不变...
    # [在这里插入原有的 create_ad_plan 函数体]
    
    logger.info("✅ 广告计划创建成功")
    return result

# 在调度器中的使用示例:
def scheduled_plan_creation(db, platform_creatives_batch):
    """调度器中的计划创建"""
    
    try:
        # 手动执行保护检查
        enforce_material_uniqueness(platform_creatives_batch, "调度器计划创建")
        
        # 执行计划创建
        result = create_ad_plan(db, platform_creatives_batch)
        
        logger.info("✅ 调度器计划创建成功")
        return result
        
    except MaterialUniquenessViolationError as e:
        logger.error(f"🚨 调度器计划创建被阻止: {e}")
        # 记录违规尝试，但不中断调度器运行
        return None
        
    except Exception as e:
        logger.error(f"❌ 调度器计划创建失败: {e}")
        raise
