#!/usr/bin/env python3
"""
千川工作流监控脚本
监控恢复后的简单工作流是否正常运行
"""

import sys
import os
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from sqlalchemy import text

def monitor_workflow_health():
    """监控工作流健康状态"""
    logger.info("🔍 千川工作流健康监控")
    logger.info("=" * 50)
    
    try:
        with database_session() as db:
            # 1. 检查状态分布
            status_query = text("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM local_creatives
                GROUP BY status
                ORDER BY count DESC
            """)
            
            results = db.execute(status_query).fetchall()
            
            logger.info("📊 当前状态分布:")
            new_count = 0
            for row in results:
                logger.info(f"  {row.status}: {row.count}个")
                if row.status == MaterialStatus.NEW.value:
                    new_count = row.count
            
            # 2. 检查可创建的计划数
            potential_campaigns = new_count // 9
            logger.info(f"\n🎯 工作流分析:")
            logger.info(f"  new状态素材: {new_count}个")
            logger.info(f"  理论可创建计划: {potential_campaigns}个 (按9个一组)")
            
            # 3. 检查账户准备情况
            account_query = text("""
                SELECT 
                    name,
                    account_type,
                    status
                FROM ad_accounts
                WHERE status = 'active' AND account_type = 'TEST'
                ORDER BY name
            """)
            
            accounts = db.execute(account_query).fetchall()
            
            logger.info(f"\n📋 可用TEST账户: {len(accounts)}个")
            for account in accounts[:5]:
                logger.info(f"  - {account.name} ({account.account_type})")
            
            # 4. 检查最近的活动
            recent_activity_query = text("""
                SELECT 
                    status,
                    COUNT(*) as count
                FROM local_creatives
                WHERE updated_at > NOW() - INTERVAL '1 hour'
                GROUP BY status
                ORDER BY count DESC
            """)
            
            recent_results = db.execute(recent_activity_query).fetchall()
            
            if recent_results:
                logger.info(f"\n📈 最近1小时活动:")
                for row in recent_results:
                    logger.info(f"  {row.status}: {row.count}个状态变更")
            else:
                logger.info(f"\n📈 最近1小时无状态变更")
            
            # 5. 检查最近创建的计划
            recent_campaigns_query = text("""
                SELECT COUNT(*) as count
                FROM campaigns
                WHERE created_at > NOW() - INTERVAL '1 hour'
            """)
            
            recent_campaigns = db.execute(recent_campaigns_query).fetchone()
            logger.info(f"📈 最近1小时创建计划: {recent_campaigns.count}个")
            
            # 6. 健康评估
            logger.info(f"\n💡 健康评估:")
            
            if new_count >= 9:
                logger.info(f"  ✅ 有足够素材创建计划 ({new_count}个)")
            else:
                logger.warning(f"  ⚠️ 素材不足，需要{9-new_count}个")
            
            if len(accounts) > 0:
                logger.info(f"  ✅ 有可用TEST账户 ({len(accounts)}个)")
            else:
                logger.error(f"  ❌ 没有可用TEST账户")
            
            if potential_campaigns > 0:
                logger.info(f"  ✅ 可以创建 {potential_campaigns} 个计划")
            else:
                logger.warning(f"  ⚠️ 暂时无法创建计划")
            
            return {
                'new_count': new_count,
                'potential_campaigns': potential_campaigns,
                'active_accounts': len(accounts),
                'recent_campaigns': recent_campaigns.count
            }
            
    except Exception as e:
        logger.error(f"❌ 监控失败: {e}")
        return {}

def check_workflow_progress():
    """检查工作流进展"""
    logger.info("\n🔄 检查工作流进展")
    logger.info("=" * 50)
    
    try:
        with database_session() as db:
            # 检查各阶段的素材数量
            progress_query = text("""
                SELECT 
                    CASE 
                        WHEN status = MaterialStatus.NEW.value THEN '1-待处理'
                        WHEN status = MaterialStatus.PENDING_UPLOAD.value THEN '2-待上传'
                        WHEN status = MaterialStatus.PROCESSING.value THEN '3-上传中'
                        WHEN status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN '4-待建计划'
                        WHEN status = 'creating_plan' THEN '5-建计划中'
                        WHEN status = MaterialStatus.TESTING_PENDING_REVIEW.value THEN '6-待审核'
                        WHEN status = MaterialStatus.APPROVED.value THEN '7-已通过'
                        WHEN status = MaterialStatus.REJECTED.value THEN '8-已拒绝'
                        ELSE '9-其他'
                    END as stage,
                    COUNT(*) as count
                FROM local_creatives
                WHERE status IN (MaterialStatus.NEW.value, MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value, 
                               MaterialStatus.UPLOADED_PENDING_PLAN.value, 'creating_plan', 
                               MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value)
                GROUP BY stage
                ORDER BY stage
            """)
            
            results = db.execute(progress_query).fetchall()
            
            logger.info("📊 工作流各阶段进展:")
            total_in_flow = 0
            for row in results:
                logger.info(f"  {row.stage}: {row.count}个")
                total_in_flow += row.count
            
            logger.info(f"\n📈 工作流统计:")
            logger.info(f"  流程中素材总数: {total_in_flow}个")
            
            # 检查流程是否有阻塞
            bottlenecks = []
            for row in results:
                if '待' in row.stage and row.count > 100:
                    bottlenecks.append(f"{row.stage}: {row.count}个")
            
            if bottlenecks:
                logger.warning(f"⚠️ 发现可能的瓶颈:")
                for bottleneck in bottlenecks:
                    logger.warning(f"  - {bottleneck}")
            else:
                logger.info(f"✅ 工作流运行顺畅，无明显瓶颈")
            
    except Exception as e:
        logger.error(f"❌ 检查工作流进展失败: {e}")

def main():
    """主监控函数"""
    logger.info(f"🚀 开始工作流监控 - {datetime.now()}")
    
    # 监控工作流健康状态
    health_status = monitor_workflow_health()
    
    # 检查工作流进展
    check_workflow_progress()
    
    logger.info("\n💡 监控建议:")
    
    if health_status.get('new_count', 0) >= 9:
        logger.info("  1. ✅ 可以开始创建计划，重启Celery服务")
        logger.info("  2. 🔄 运行: python run_celery_beat.py")
        logger.info("  3. 🔄 运行: python run_celery_worker.py")
    else:
        logger.info("  1. ⚠️ 等待更多素材进入new状态")
        logger.info("  2. 📁 检查文件摄取是否正常")
    
    logger.info("  4. 📊 定期运行此监控脚本观察进展")
    logger.info("  5. 🔍 观察creating_plan跳过次数是否减少")
    
    logger.info(f"\n🎯 预期效果:")
    logger.info(f"  - new状态素材开始正常处理")
    logger.info(f"  - 按9个一组创建测试计划")
    logger.info(f"  - creating_plan跳过次数大幅减少")
    logger.info(f"  - 工作流恢复正常运行")

if __name__ == "__main__":
    main()
