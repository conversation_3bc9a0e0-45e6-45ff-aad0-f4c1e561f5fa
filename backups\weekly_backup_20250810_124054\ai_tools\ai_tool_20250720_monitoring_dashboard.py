#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI工具 - 千川自动化系统监控仪表板
生命周期：永久
用途：基于Streamlit的系统监控界面，与项目架构保持一致

功能：
- 实时状态分布监控
- 收割和提审性能指标
- 卡住素材告警
- 系统健康评分
- 历史趋势分析
"""

import sys
import os
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.monitoring_mcp import create_mcp_monitoring_collector


def execute_sql_wrapper(sql: str) -> str:
    """
    SQL执行包装器，模拟MCP的execute_sql功能
    在实际使用中，这里应该调用真正的MCP execute_sql函数
    """
    try:
        # 这里应该调用实际的MCP execute_sql函数
        # 暂时返回模拟数据用于演示
        if "GROUP BY status" in sql and "local_creatives" in sql:
            return '''{"status":MaterialStatus.APPROVED.value,"count":1481}
{"status":MaterialStatus.PENDING_UPLOAD.value,"count":25}
{"status":MaterialStatus.UPLOADING.value,"count":8}
{"status":"plan_pending","count":12}
{"status":"under_review","count":45}
{"status":MaterialStatus.REJECTED.value,"count":23}'''
        elif "harvest_status" in sql:
            return '''{"harvest_status":"not_harvested","count":1200}
{"harvest_status":MaterialStatus.HARVESTED.value,"count":281}
{"harvest_status":"harvest_failed","count":15}'''
        elif "appeal_status" in sql:
            return '''{"appeal_status":"appeal_pending","count":35}
{"appeal_status":"appeal_completed","count":120}
{"appeal_status":"appeal_failed","count":8}'''
        else:
            return '{"total_approved":1481,"harvested_count":281,"pending_harvest":1200,"avg_harvest_delay_seconds":125.5}'
    except Exception as e:
        logger.error(f"SQL执行失败: {e}")
        return ""


def render_monitoring_dashboard():
    """渲染监控仪表板"""
    st.title("🎯 千川自动化系统监控")
    st.markdown("---")
    
    # 创建监控收集器
    collector = create_mcp_monitoring_collector(execute_sql_wrapper)
    
    # 获取系统健康摘要
    with st.spinner("正在获取系统状态..."):
        health_summary = collector.get_system_health_summary()
    
    # 系统健康概览
    render_health_overview(health_summary)
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📊 状态分布", "⚡ 性能指标", "🚨 告警监控", "📈 历史趋势"])
    
    with tab1:
        render_state_distribution(health_summary)
    
    with tab2:
        render_performance_metrics(health_summary)
    
    with tab3:
        render_alert_monitoring(health_summary)
    
    with tab4:
        render_historical_trends(collector)


def render_health_overview(health_summary: dict):
    """渲染系统健康概览"""
    st.subheader("🏥 系统健康概览")
    
    # 健康评分
    health_score = health_summary.get('health_score', 0)
    status = health_summary.get('status', 'unknown')
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        # 健康评分指示器
        if status == 'healthy':
            st.success(f"🟢 系统健康")
        elif status == 'warning':
            st.warning(f"🟡 系统警告")
        else:
            st.error(f"🔴 系统异常")
        
        st.metric("健康评分", f"{health_score}/100")
    
    with col2:
        harvest_metrics = health_summary.get('harvest_metrics', {})
        harvest_rate = harvest_metrics.get('harvest_success_rate', 0)
        st.metric("收割成功率", f"{harvest_rate:.1%}")
    
    with col3:
        appeal_metrics = health_summary.get('appeal_metrics', {})
        appeal_rate = appeal_metrics.get('appeal_success_rate', 0)
        st.metric("提审成功率", f"{appeal_rate:.1%}")
    
    with col4:
        stuck_alerts = health_summary.get('stuck_alerts', {})
        stuck_count = stuck_alerts.get('total_stuck', 0)
        st.metric("卡住素材", f"{stuck_count}个", delta=None if stuck_count == 0 else f"-{stuck_count}")


def render_state_distribution(health_summary: dict):
    """渲染状态分布"""
    st.subheader("📊 状态分布监控")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**素材状态分布**")
        material_dist = health_summary.get('material_distribution', {})
        if material_dist:
            df_material = pd.DataFrame(list(material_dist.items()), columns=['状态', '数量'])
            fig_material = px.pie(df_material, values='数量', names='状态', 
                                title="素材状态分布")
            st.plotly_chart(fig_material, use_container_width=True)
        else:
            st.info("暂无素材状态数据")
    
    with col2:
        st.markdown("**收割状态分布**")
        harvest_dist = health_summary.get('harvest_distribution', {})
        if harvest_dist:
            df_harvest = pd.DataFrame(list(harvest_dist.items()), columns=['状态', '数量'])
            fig_harvest = px.pie(df_harvest, values='数量', names='状态', 
                               title="收割状态分布")
            st.plotly_chart(fig_harvest, use_container_width=True)
        else:
            st.info("暂无收割状态数据")
    
    # 提审状态分布
    st.markdown("**提审状态分布**")
    appeal_dist = health_summary.get('appeal_distribution', {})
    if appeal_dist:
        df_appeal = pd.DataFrame(list(appeal_dist.items()), columns=['状态', '数量'])
        fig_appeal = px.bar(df_appeal, x='状态', y='数量', title="提审状态分布")
        st.plotly_chart(fig_appeal, use_container_width=True)
    else:
        st.info("暂无提审状态数据")


def render_performance_metrics(health_summary: dict):
    """渲染性能指标"""
    st.subheader("⚡ 性能指标监控")
    
    harvest_metrics = health_summary.get('harvest_metrics', {})
    appeal_metrics = health_summary.get('appeal_metrics', {})
    
    # 收割性能指标
    st.markdown("**收割性能指标**")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_approved = harvest_metrics.get('total_approved', 0)
        st.metric("已审核通过", f"{total_approved}个")
    
    with col2:
        harvested_count = harvest_metrics.get('harvested_count', 0)
        st.metric("已收割", f"{harvested_count}个")
    
    with col3:
        pending_harvest = harvest_metrics.get('pending_harvest', 0)
        st.metric("待收割", f"{pending_harvest}个")
    
    with col4:
        avg_delay = harvest_metrics.get('avg_harvest_delay_seconds', 0)
        st.metric("平均延迟", f"{avg_delay:.1f}秒")
    
    # 提审性能指标
    st.markdown("**提审性能指标**")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_campaigns = appeal_metrics.get('total_campaigns', 0)
        st.metric("总计划数", f"{total_campaigns}个")
    
    with col2:
        completed_appeals = appeal_metrics.get('completed_appeals', 0)
        st.metric("提审完成", f"{completed_appeals}个")
    
    with col3:
        pending_appeals = appeal_metrics.get('pending_appeals', 0)
        st.metric("提审待处理", f"{pending_appeals}个")
    
    with col4:
        avg_duration = appeal_metrics.get('avg_appeal_duration_seconds', 0)
        st.metric("平均耗时", f"{avg_duration:.1f}秒")


def render_alert_monitoring(health_summary: dict):
    """渲染告警监控"""
    st.subheader("🚨 告警监控")
    
    stuck_alerts = health_summary.get('stuck_alerts', {})
    alerts = stuck_alerts.get('alerts', [])
    
    if alerts:
        st.warning(f"发现 {len(alerts)} 个状态告警")
        
        # 创建告警表格
        alert_data = []
        for alert in alerts:
            alert_data.append({
                '状态': alert['status'],
                '卡住数量': alert['stuck_count'],
                '最早卡住时间': alert['oldest_stuck_time'],
                '严重程度': alert['severity']
            })
        
        df_alerts = pd.DataFrame(alert_data)
        st.dataframe(df_alerts, use_container_width=True)
        
        # 告警图表
        fig_alerts = px.bar(df_alerts, x='状态', y='卡住数量', 
                           color='严重程度', title="卡住状态告警分布")
        st.plotly_chart(fig_alerts, use_container_width=True)
    else:
        st.success("✅ 当前无告警")
    
    # 告警阈值设置
    st.markdown("**告警阈值设置**")
    col1, col2 = st.columns(2)
    
    with col1:
        harvest_threshold = st.slider("收割成功率告警阈值", 0.0, 1.0, 0.95, 0.01)
        stuck_timeout = st.slider("卡住状态超时阈值(分钟)", 10, 180, 60, 10)
    
    with col2:
        appeal_threshold = st.slider("提审成功率告警阈值", 0.0, 1.0, 0.95, 0.01)
        delay_threshold = st.slider("收割延迟告警阈值(秒)", 60, 600, 300, 30)


def render_historical_trends(collector):
    """渲染历史趋势"""
    st.subheader("📈 历史趋势分析")
    
    # 时间范围选择
    time_range = st.selectbox("选择时间范围", 
                             ["最近1小时", "最近6小时", "最近24小时", "最近7天"])
    
    hours_map = {
        "最近1小时": 1,
        "最近6小时": 6, 
        "最近24小时": 24,
        "最近7天": 168
    }
    hours = hours_map[time_range]
    
    # 获取历史数据
    with st.spinner("正在获取历史数据..."):
        harvest_metrics = collector.get_harvest_performance_metrics(hours)
        appeal_metrics = collector.get_appeal_performance_metrics(hours)
    
    # 模拟时间序列数据（实际应该从数据库获取）
    time_points = []
    harvest_rates = []
    appeal_rates = []
    
    for i in range(10):  # 模拟10个时间点
        time_points.append(datetime.utcnow() - timedelta(hours=hours*i/10))
        harvest_rates.append(0.95 + (i % 3 - 1) * 0.02)  # 模拟波动
        appeal_rates.append(0.93 + (i % 4 - 1) * 0.03)   # 模拟波动
    
    # 创建趋势图
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=time_points, y=harvest_rates, 
                            mode='lines+markers', name='收割成功率'))
    fig.add_trace(go.Scatter(x=time_points, y=appeal_rates, 
                            mode='lines+markers', name='提审成功率'))
    
    fig.update_layout(title="成功率历史趋势", 
                     xaxis_title="时间", 
                     yaxis_title="成功率",
                     yaxis=dict(range=[0, 1]))
    
    st.plotly_chart(fig, use_container_width=True)
    
    # 数据导出
    if st.button("导出监控数据"):
        # 创建导出数据
        export_data = {
            'harvest_metrics': harvest_metrics,
            'appeal_metrics': appeal_metrics,
            'export_time': datetime.utcnow().isoformat()
        }
        
        st.download_button(
            label="下载JSON格式",
            data=json.dumps(export_data, indent=2, ensure_ascii=False),
            file_name=f"monitoring_data_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.json",
            mime="application/json"
        )


def main():
    """主函数"""
    st.set_page_config(
        page_title="千川监控仪表板",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 侧边栏
    with st.sidebar:
        st.title("🎯 监控设置")
        
        # 自动刷新设置
        auto_refresh = st.checkbox("自动刷新", value=True)
        if auto_refresh:
            refresh_interval = st.selectbox("刷新间隔", [30, 60, 120, 300], index=1)
            st.info(f"每{refresh_interval}秒自动刷新")
        
        # 监控范围设置
        st.markdown("**监控范围**")
        monitor_harvest = st.checkbox("收割监控", value=True)
        monitor_appeal = st.checkbox("提审监控", value=True)
        monitor_alerts = st.checkbox("告警监控", value=True)
        
        # 手动刷新按钮
        if st.button("🔄 立即刷新"):
            st.rerun()
    
    # 渲染主界面
    render_monitoring_dashboard()
    
    # 自动刷新逻辑
    if auto_refresh:
        import time
        time.sleep(refresh_interval)
        st.rerun()


if __name__ == "__main__":
    main()
