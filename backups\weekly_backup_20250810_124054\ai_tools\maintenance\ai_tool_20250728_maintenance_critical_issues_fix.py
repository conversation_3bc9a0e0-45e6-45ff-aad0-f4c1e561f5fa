#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复千川自动化项目中的4个严重问题
清理条件: 问题修复完成后可归档
"""

import os
import sys
import shutil
import yaml
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger

class CriticalIssuesFixer:
    """千川自动化项目关键问题修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.config_file = self.project_root / 'config' / 'settings.yml'
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def run_comprehensive_fix(self):
        """执行综合修复"""
        logger.critical("🚨 开始修复千川自动化项目的4个严重问题")
        logger.critical("=" * 80)
        
        try:
            # 创建备份目录
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 问题分析
            self.analyze_issues()
            
            # 执行修复
            self.fix_issue_4_creative_count()
            self.fix_issue_2_file_movement()
            self.fix_issue_1_harvest_scope()
            self.analyze_issue_3_concurrency()
            
            # 验证修复
            self.verify_fixes()
            
            logger.critical("✅ 所有问题修复完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 修复过程中发生错误: {e}")
            return False
    
    def analyze_issues(self):
        """分析问题"""
        logger.info("🔍 分析问题根本原因...")
        
        issues = {
            "问题1": "素材收割范围错误 - 系统检测投放广告户而非仅测试户",
            "问题2": "文件移动机制不稳定 - 总是第一次失败后使用备用方法",
            "问题3": "多线程调度异常 - 50分钟内任务似乎串行执行",
            "问题4": "测试计划素材数量配置错误 - 配置为5个但期望9个"
        }
        
        for issue, description in issues.items():
            logger.warning(f"  {issue}: {description}")
    
    def fix_issue_4_creative_count(self):
        """修复问题4：测试计划素材数量配置"""
        logger.info("🔧 修复问题4：测试计划素材数量配置")
        
        try:
            # 备份配置文件
            backup_config = self.backup_dir / 'settings.yml.backup'
            shutil.copy2(self.config_file, backup_config)
            logger.info(f"✅ 配置文件已备份到: {backup_config}")
            
            # 读取配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 修改配置
            if 'plan_creation_defaults' not in config:
                config['plan_creation_defaults'] = {}
            if 'test_workflow' not in config['plan_creation_defaults']:
                config['plan_creation_defaults']['test_workflow'] = {}
            
            old_count = config['plan_creation_defaults']['test_workflow'].get('creative_count', 5)
            config['plan_creation_defaults']['test_workflow']['creative_count'] = 9
            
            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
            logger.success(f"✅ 测试计划素材数量已修改: {old_count} → 9")
            
        except Exception as e:
            logger.error(f"❌ 修复问题4失败: {e}")
    
    def fix_issue_2_file_movement(self):
        """修复问题2：文件移动机制优化"""
        logger.info("🔧 修复问题2：文件移动机制优化")
        
        try:
            harvest_file = self.project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'independent_material_harvest.py'
            
            # 备份原文件
            backup_harvest = self.backup_dir / 'independent_material_harvest.py.backup'
            shutil.copy2(harvest_file, backup_harvest)
            
            # 读取文件内容
            with open(harvest_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找并替换文件移动逻辑
            old_logic = '''        # 执行文件移动（只有确认未被收割过的文件才移动）
        if not os.path.exists(target_path):
            if not os.path.exists(source_path):
                logger.error(f"文件移动失败：在源归档目录 '{archive_dir}' 中找不到文件 '{filename}'")
                # 尝试使用当前文件路径作为源
                if os.path.exists(local_creative.file_path):
                    source_path = local_creative.file_path
                    logger.info(f"使用当前文件路径作为源: {source_path}")
                else:
                    logger.error(f"文件移动失败：文件 '{filename}' 在任何位置都找不到")
                    return'''
            
            new_logic = '''        # 执行文件移动（优化版：先检查当前路径，减少错误日志）
        if not os.path.exists(target_path):
            # 优先使用当前文件路径
            if os.path.exists(local_creative.file_path):
                source_path = local_creative.file_path
                logger.debug(f"使用当前文件路径作为源: {source_path}")
            elif os.path.exists(source_path):
                logger.debug(f"使用归档目录路径作为源: {source_path}")
            else:
                logger.error(f"文件移动失败：文件 '{filename}' 在任何位置都找不到")
                return'''
            
            if old_logic in content:
                content = content.replace(old_logic, new_logic)
                
                with open(harvest_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.success("✅ 文件移动逻辑已优化，减少不必要的错误日志")
            else:
                logger.warning("⚠️ 未找到预期的文件移动逻辑，可能已被修改")
                
        except Exception as e:
            logger.error(f"❌ 修复问题2失败: {e}")
    
    def fix_issue_1_harvest_scope(self):
        """修复问题1：素材收割范围限制"""
        logger.info("🔧 修复问题1：添加素材收割范围配置和代码修改")

        try:
            # 1. 在配置文件中添加素材收割范围选项
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 添加独立素材收割配置
            if 'workflow' not in config:
                config['workflow'] = {}
            if 'independent_harvest' not in config['workflow']:
                config['workflow']['independent_harvest'] = {}

            config['workflow']['independent_harvest'].update({
                'enabled': True,
                'interval_seconds': 600,
                'scope': {
                    'test_accounts_only': True,  # 新增：是否仅收割测试账户
                    'include_delivery_accounts': False,  # 新增：是否包含投放账户
                    'account_type_filter': ['TEST']  # 新增：账户类型过滤
                }
            })

            # 写回配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)

            # 2. 修改代码以支持账户类型过滤
            self._update_harvest_code_for_scope_filtering()

            logger.success("✅ 已添加素材收割范围配置，默认仅收割测试账户")
            logger.info("💡 如需包含投放账户，请修改配置文件中的 workflow.independent_harvest.scope 设置")

        except Exception as e:
            logger.error(f"❌ 修复问题1失败: {e}")

    def _update_harvest_code_for_scope_filtering(self):
        """更新素材收割代码以支持范围过滤"""
        try:
            harvest_file = self.project_root / 'src' / 'qianchuan_aw' / 'workflows' / 'independent_material_harvest.py'

            with open(harvest_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找并替换账户查询逻辑
            old_query = '''    # 获取所有活跃的广告账户
    accounts = session.query(QianchuanAccount).filter(
        QianchuanAccount.status == 'ACTIVE'
    ).all()'''

            new_query = '''    # 获取所有活跃的广告账户（根据配置过滤账户类型）
    from qianchuan_aw.utils.config_manager import load_config
    config = load_config()
    harvest_scope = config.get('workflow', {}).get('independent_harvest', {}).get('scope', {})

    query = session.query(QianchuanAccount).filter(QianchuanAccount.status == 'ACTIVE')

    # 根据配置过滤账户类型
    if harvest_scope.get('test_accounts_only', False):
        account_types = harvest_scope.get('account_type_filter', ['TEST'])
        query = query.filter(QianchuanAccount.account_type.in_(account_types))
        logger.info(f"🎯 素材收割范围限制为账户类型: {account_types}")

    accounts = query.all()'''

            if old_query in content:
                content = content.replace(old_query, new_query)

                with open(harvest_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                logger.success("✅ 素材收割代码已更新，支持账户类型过滤")
            else:
                logger.warning("⚠️ 未找到预期的账户查询逻辑，可能需要手动修改")

        except Exception as e:
            logger.error(f"❌ 更新素材收割代码失败: {e}")
    
    def analyze_issue_3_concurrency(self):
        """分析问题3：多线程调度问题"""
        logger.info("🔍 分析问题3：多线程调度异常")
        
        try:
            # 检查Celery配置
            celery_worker_file = self.project_root / 'run_celery_worker.py'
            celery_app_file = self.project_root / 'src' / 'qianchuan_aw' / 'celery_app.py'
            
            logger.info("📋 Celery配置分析:")
            
            # 分析worker配置
            if celery_worker_file.exists():
                with open(celery_worker_file, 'r', encoding='utf-8') as f:
                    worker_content = f.read()
                    if "'-c', '5'" in worker_content:
                        logger.info("  ✅ Worker并发数: 5个线程")
                    if "'-P', 'threads'" in worker_content:
                        logger.info("  ✅ 使用线程池模式")
            
            # 分析任务间隔
            if celery_app_file.exists():
                with open(celery_app_file, 'r', encoding='utf-8') as f:
                    app_content = f.read()
                    logger.info("  📊 任务调度间隔分析:")
                    if "plan_creation_interval" in app_content:
                        logger.info("    - 计划创建任务: 配置化间隔")
                    if "independent_harvest_interval" in app_content:
                        logger.info("    - 独立收割任务: 配置化间隔")
            
            # 提供优化建议
            logger.warning("⚠️ 可能的串行执行原因:")
            logger.warning("  1. 数据库会话锁定时间过长")
            logger.warning("  2. API频率限制导致任务排队")
            logger.warning("  3. 任务间存在隐式依赖关系")
            
            logger.info("💡 优化建议:")
            logger.info("  1. 检查数据库事务时长，避免长时间锁定")
            logger.info("  2. 优化API调用频率，使用批量操作")
            logger.info("  3. 监控Redis队列状态，确保任务正常分发")
            
        except Exception as e:
            logger.error(f"❌ 分析问题3失败: {e}")
    
    def verify_fixes(self):
        """验证修复结果"""
        logger.info("🔍 验证修复结果...")
        
        try:
            # 验证配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 检查问题4修复
            creative_count = config.get('plan_creation_defaults', {}).get('test_workflow', {}).get('creative_count')
            if creative_count == 9:
                logger.success("✅ 问题4已修复：测试计划素材数量 = 9")
            else:
                logger.error(f"❌ 问题4修复失败：当前值 = {creative_count}")
            
            # 检查问题1修复
            harvest_scope = config.get('workflow', {}).get('independent_harvest', {}).get('scope', {})
            if harvest_scope.get('test_accounts_only'):
                logger.success("✅ 问题1已修复：添加了素材收割范围配置")
            else:
                logger.warning("⚠️ 问题1配置可能需要手动验证")
            
            logger.info(f"📁 备份文件位置: {self.backup_dir}")
            
        except Exception as e:
            logger.error(f"❌ 验证修复结果失败: {e}")

def main():
    """主函数"""
    fixer = CriticalIssuesFixer()
    success = fixer.run_comprehensive_fix()
    
    if success:
        logger.critical("🎉 修复完成！建议重启Celery服务以应用更改")
        logger.critical("📋 重启命令:")
        logger.critical("  1. 停止现有服务 (Ctrl+C)")
        logger.critical("  2. python run_celery_worker.py")
        logger.critical("  3. python run_celery_beat.py")
    else:
        logger.critical("❌ 修复失败，请检查错误日志")

if __name__ == "__main__":
    main()
