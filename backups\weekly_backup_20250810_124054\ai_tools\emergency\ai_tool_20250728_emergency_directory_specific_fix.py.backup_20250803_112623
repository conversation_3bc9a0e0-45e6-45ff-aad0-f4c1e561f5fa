#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 专门修复G:\workflow_assets\01_to_process\缇萃百货目录中的素材问题
清理条件: 问题修复并验证稳定后可归档
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import time

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import PlatformCreative, Campaign, LocalCreative
from qianchuan_aw.sdk_qc.client import QianchuanClient
from qianchuan_aw.utils.config_loader import load_settings
from sqlalchemy import text

class DirectorySpecificFixer:
    """专门修复01_to_process\缇萃百货目录中素材的工具"""
    
    def __init__(self):
        self.project_root = project_root
        self.config = load_settings()
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"directory_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 目标目录中需要修复的素材（包含video_id和video_cover_id）
        self.target_materials = [
            {
                'material_id': '7530566648211882010',
                'filename': '7.23-王梦珂-18.mp4',
                'author': '王梦珂',
                'video_id': 'v1e033gi0000d2160inog65sip05fdf0',
                'video_cover_id': 'tos-cn-p-0051-ce/oUsECBoICYNaEKLDFmAeIIQDCwtegZvC81fiE5'
            },
            {
                'material_id': '7530567787405770791',
                'filename': '7.23-王梦珂-27.mp4',
                'author': '王梦珂',
                'video_id': 'v1e033gi0000d210hcvog65kq7tiouj0',
                'video_cover_id': 'tos-cn-p-0051-ce/oYFRTAef8BQEMEbMGngZIYLIMum91REWGfJBCy'
            },
            {
                'material_id': '7530565588068450343',
                'filename': '7.23-杨婷婷-28.mp4',
                'author': '杨婷婷',
                'video_id': 'v1e033gi0000d20vv8fog65reg4vhuig',
                'video_cover_id': 'tos-cn-p-0051-ce/o06iRHePJA12D2YkAIFKBf4DhfB9x6fETMFsDQ'
            },
            {
                'material_id': '7530565692832661543',
                'filename': '7.23-杨婷婷-延1.mp4',
                'author': '杨婷婷',
                'video_id': 'v1e033gi0000d2102jnog65pk90a57kg',
                'video_cover_id': 'tos-cn-p-0051-ce/oIAoiaVIvIpIiAwi1AB5BhMTkBX6CuAFzCznE'
            }
        ]
        
    def run_directory_fix(self):
        """执行目录专门修复"""
        logger.critical("🚨 开始修复01_to_process\\缇萃百货目录中的素材")
        logger.critical("=" * 80)
        
        try:
            # 1. 分析目录状态
            self._analyze_directory_status()
            
            # 2. 配置测试账户
            self._configure_test_account()
            
            # 3. 为遗漏素材创建测试计划
            self._create_missing_campaigns()
            
            # 4. 验证修复效果
            self._verify_directory_fix()
            
            # 5. 运行监控检查
            self._run_integrity_check()
            
            logger.critical("✅ 目录专门修复完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 目录修复失败: {e}")
            return False
    
    def _analyze_directory_status(self):
        """分析目录状态"""
        logger.critical("🔍 分析01_to_process\\缇萃百货目录状态...")
        
        with database_session() as db:
            # 统计目录中的素材状态
            result = db.execute(text("""
                SELECT 
                    CASE 
                        WHEN lc.file_path LIKE '%付珂佳%' THEN '付珂佳'
                        WHEN lc.file_path LIKE '%杨婷婷%' THEN '杨婷婷'
                        WHEN lc.file_path LIKE '%王梦珂%' THEN '王梦珂'
                        WHEN lc.file_path LIKE '%郭世攀%' THEN '郭世攀'
                        ELSE '其他作者'
                    END as author,
                    COUNT(*) as total_materials,
                    COUNT(pc.material_id_qc) as uploaded_materials,
                    COUNT(cpc.campaign_id) as with_campaigns
                FROM local_creatives lc
                LEFT JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE lc.file_path LIKE '%01_to_process\\\\缇萃百货%'
                  AND lc.created_at >= NOW() - INTERVAL '4 days'
                GROUP BY 
                    CASE 
                        WHEN lc.file_path LIKE '%付珂佳%' THEN '付珂佳'
                        WHEN lc.file_path LIKE '%杨婷婷%' THEN '杨婷婷'
                        WHEN lc.file_path LIKE '%王梦珂%' THEN '王梦珂'
                        WHEN lc.file_path LIKE '%郭世攀%' THEN '郭世攀'
                        ELSE '其他作者'
                    END
                ORDER BY author
            """)).fetchall()
            
            logger.critical("📊 目录素材状态统计:")
            total_missing = 0
            for row in result:
                missing = row.uploaded_materials - row.with_campaigns
                total_missing += missing
                logger.critical(f"  - {row.author}: 总数{row.total_materials}, 已上传{row.uploaded_materials}, 已创建计划{row.with_campaigns}, 遗漏{missing}")
            
            logger.critical(f"📋 总计遗漏测试计划的素材: {total_missing}个")
            return total_missing
    
    def _configure_test_account(self):
        """配置测试账户"""
        logger.critical("🔧 配置测试账户...")
        
        # 检查当前配置
        accounts = self.config.get('accounts', {})
        test_account_found = False
        
        for account_id, account_info in accounts.items():
            if account_info.get('is_test_account', False):
                test_account_found = True
                logger.success(f"✅ 找到测试账户: {account_id}")
                break
        
        if not test_account_found:
            logger.warning("⚠️ 未找到配置的测试账户")
            
            # 使用第一个可用账户作为测试账户
            if accounts:
                first_account_id = list(accounts.keys())[0]
                logger.info(f"📋 将使用账户 {first_account_id} 作为测试账户")
                
                # 临时配置测试账户（仅在内存中）
                self.test_account = {
                    'advertiser_id': first_account_id,
                    'access_token': accounts[first_account_id].get('access_token')
                }
                
                if self.test_account['access_token']:
                    logger.success(f"✅ 测试账户配置成功: {first_account_id}")
                else:
                    logger.error(f"❌ 账户 {first_account_id} 缺少access_token")
                    return False
            else:
                logger.error("❌ 配置文件中没有可用账户")
                return False
        else:
            # 使用已配置的测试账户
            for account_id, account_info in accounts.items():
                if account_info.get('is_test_account', False):
                    self.test_account = {
                        'advertiser_id': account_id,
                        'access_token': account_info.get('access_token')
                    }
                    break
        
        return True
    
    def _create_missing_campaigns(self):
        """为遗漏的素材创建测试计划"""
        logger.critical("🔧 为目录中遗漏的素材创建测试计划...")
        
        if not hasattr(self, 'test_account') or not self.test_account:
            logger.error("❌ 测试账户未配置，无法创建测试计划")
            return
        
        # 初始化千川客户端
        app_id = self.config['api_credentials']['app_id']
        secret = self.config['api_credentials']['secret']
        principal_id = 1  # 使用默认主体ID

        client = QianchuanClient(app_id, secret, principal_id)
        
        created_count = 0
        with database_session() as db:
            for material in self.target_materials:
                material_id = material['material_id']
                filename = material['filename']
                author = material['author']
                
                try:
                    # 查找platform_creative记录
                    pc = db.query(PlatformCreative).filter(
                        PlatformCreative.material_id_qc == material_id
                    ).first()
                    
                    if not pc:
                        logger.error(f"  ❌ {filename} - 未找到platform_creative记录")
                        continue
                    
                    # 检查是否已有测试计划
                    existing_association = db.execute(text("""
                        SELECT campaign_id FROM campaign_platform_creative_association 
                        WHERE platform_creative_id = :pc_id
                    """), {"pc_id": pc.id}).first()
                    
                    if existing_association:
                        logger.info(f"  ✅ {filename} - 已有测试计划，跳过")
                        continue
                    
                    # 创建测试计划
                    campaign_result = self._create_test_campaign(
                        client, self.test_account['advertiser_id'], material, filename, author
                    )
                    
                    if campaign_result:
                        # 保存到数据库
                        campaign = Campaign(
                            campaign_id_qc=campaign_result['campaign_id'],
                            account_id=5,  # 测试账户的数据库ID
                            status='AUDITING',
                            created_at=datetime.now()
                        )
                        db.add(campaign)
                        db.flush()
                        
                        # 创建关联记录
                        db.execute(text("""
                            INSERT INTO campaign_platform_creative_association 
                            (campaign_id, platform_creative_id) 
                            VALUES (:campaign_id, :platform_creative_id)
                        """), {
                            "campaign_id": campaign.id,
                            "platform_creative_id": pc.id
                        })
                        
                        created_count += 1
                        logger.success(f"  ✅ {filename} - 测试计划创建成功: {campaign_result['campaign_id']}")
                        
                        # 避免API频率限制
                        time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"  ❌ {filename} - 创建测试计划失败: {e}")
            
            db.commit()
            logger.critical(f"✅ 成功为目录中的素材创建{created_count}个测试计划")
    
    def _create_test_campaign(self, client, advertiser_id, material, filename, author):
        """创建测试计划"""
        try:
            # 构建正确的素材对象
            media_object = {
                "aweme_item_id": 0,
                "image_mode": "VIDEO_VERTICAL",
                "is_auto_generate": 0,
                "video_cover_id": material['video_cover_id'],
                "video_id": material['video_id'],
                "video_poster_url": "",
                "video_url": ""
            }

            # 构建delivery_setting
            delivery_setting = {
                "budget_mode": "BUDGET_MODE_DAY",
                "budget": 5000,  # 50元日预算（单位：分）
                "external_action": "AD_CONVERT_TYPE_LIVE_SUCCESSORDER_PAY",
                "live_schedule_type": "SCHEDULE_FROM_NOW",
                "smart_bid_type": "SMART_BID_CUSTOM",
                "cpa_bid": 1000  # 10元CPA出价（单位：分）
            }

            # 构建audience设置
            audience = {
                "audience_mode": "CUSTOM",
                "district": "NONE",
                "location_type": "ALL",
                "exclude_limited_region": 1,
                "age": [
                    "AGE_BETWEEN_31_40",
                    "AGE_BETWEEN_41_49",
                    "AGE_ABOVE_50"
                ],
                "gender": "NONE",
                "smart_interest_action": "RECOMMEND"
            }

            # 获取广告组ID（自定义计划需要）
            campaign_groups = client.get_campaign_group_list(advertiser_id=advertiser_id)
            if not campaign_groups:
                logger.error(f"账户 {advertiser_id} 下没有任何广告组，无法创建自定义计划")
                return None

            campaign_id = campaign_groups[0]['id']  # 使用第一个广告组
            logger.info(f"使用广告组ID: {campaign_id}")

            # 构建标题素材
            title_materials = [
                {
                    "title": f"精选{author}推荐好物，直播间限时优惠！",
                    "title_type": "CUSTOM"
                }
            ]

            # 构建测试计划参数
            plan_config = {
                'advertiser_id': advertiser_id,
                'name': f"测试-{author}-{filename[:20]}-{datetime.now().strftime('%m%d%H%M')}",
                'operation_type': 'LIVE_PROM',
                'marketing_goal': 'LIVE_PROM_GOODS',
                'campaign_scene': 'DAILY_SALE',
                'marketing_scene': 'FEED',
                'lab_ad_type': 'NOT_LAB_AD',
                'creative_material_mode': 'PROGRAMMATIC_CREATIVE',
                'is_homepage_hide': 1,
                'campaign_id': campaign_id,  # 添加广告组ID
                'aweme_id': 7410578812859860027,  # 添加抖音号
                'delivery_setting': delivery_setting,
                'audience': audience,
                'programmatic_creative_media_list': [media_object],
                'programmatic_creative_title_list': title_materials  # 添加标题素材
            }

            # 调用千川API创建计划
            result = client.create_ad_plan(plan_config=plan_config)

            if result and result.get('ad_id'):
                return {
                    'campaign_id': result['ad_id'],
                    'status': 'success'
                }
            else:
                logger.error(f"API返回结果异常: {result}")
                return None

        except Exception as e:
            logger.error(f"创建测试计划API调用失败: {e}")
            return None
    
    def _verify_directory_fix(self):
        """验证目录修复效果"""
        logger.critical("🔍 验证目录修复效果...")
        
        with database_session() as db:
            success_count = 0
            for material in self.target_materials:
                material_id = material['material_id']
                filename = material['filename']
                
                # 查找platform_creative记录
                pc = db.query(PlatformCreative).filter(
                    PlatformCreative.material_id_qc == material_id
                ).first()
                
                if pc:
                    # 检查是否有关联的测试计划
                    association = db.execute(text("""
                        SELECT c.campaign_id_qc FROM campaign_platform_creative_association cpc
                        JOIN campaigns c ON cpc.campaign_id = c.id
                        WHERE cpc.platform_creative_id = :pc_id
                    """), {"pc_id": pc.id}).first()
                    
                    if association:
                        success_count += 1
                        logger.success(f"  ✅ {filename} - 已关联测试计划: {association.campaign_id_qc}")
                    else:
                        logger.error(f"  ❌ {filename} - 仍无测试计划关联")
                else:
                    logger.error(f"  ❌ {filename} - 未找到platform_creative记录")
            
            logger.critical(f"📊 目录修复成功率: {success_count}/{len(self.target_materials)} ({success_count/len(self.target_materials)*100:.1f}%)")
            
            if success_count == len(self.target_materials):
                logger.critical("🎉 目录中所有遗漏素材都已成功创建测试计划！")
                return True
            else:
                logger.error(f"⚠️ 仍有{len(self.target_materials) - success_count}个素材未成功修复")
                return False
    
    def _run_integrity_check(self):
        """运行完整性检查"""
        logger.critical("🔍 运行目录完整性检查...")
        
        with database_session() as db:
            # 检查目录中所有已上传但未创建计划的素材
            orphaned_materials = db.execute(text("""
                SELECT 
                    pc.material_id_qc,
                    SUBSTRING(lc.file_path FROM '[^\\\\]+$') as filename
                FROM local_creatives lc
                JOIN platform_creatives pc ON lc.id = pc.local_creative_id
                LEFT JOIN campaign_platform_creative_association cpc ON pc.id = cpc.platform_creative_id
                WHERE lc.file_path LIKE '%01_to_process\\\\缇萃百货%'
                  AND lc.created_at >= NOW() - INTERVAL '4 days'
                  AND pc.material_id_qc IS NOT NULL
                  AND cpc.campaign_id IS NULL
            """)).fetchall()
            
            if orphaned_materials:
                logger.warning(f"⚠️ 目录中仍有{len(orphaned_materials)}个已上传但未创建计划的素材:")
                for material in orphaned_materials:
                    logger.warning(f"  - {material.filename} (ID: {material.material_id_qc})")
            else:
                logger.success("✅ 目录中所有已上传素材都已创建测试计划")
            
            return len(orphaned_materials)

def main():
    """主函数"""
    fixer = DirectorySpecificFixer()
    success = fixer.run_directory_fix()
    
    if success:
        logger.critical("🎉 目录专门修复完成！")
        logger.critical("📋 修复结果:")
        logger.critical("  1. 已为目录中遗漏素材创建测试计划")
        logger.critical("  2. 已验证修复效果")
        logger.critical("  3. 已运行完整性检查")
        logger.critical("")
        logger.critical("📊 验证命令:")
        logger.critical("  python ai_tools/monitoring/ai_tool_20250728_monitoring_material_campaign_integrity.py")
    else:
        logger.critical("❌ 目录修复失败，请检查错误日志")

if __name__ == "__main__":
    main()
