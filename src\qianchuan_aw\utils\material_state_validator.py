#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
千川自动化系统 - 素材状态转换验证器

提供状态转换的验证、路径检查和业务规则校验功能。
确保所有状态转换都符合业务逻辑和系统设计要求。

创建时间: 2025-08-10
维护责任: 千川自动化系统核心团队
"""

from qianchuan_aw.utils.unified_material_status import MaterialStatus
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from datetime import datetime, timezone
import logging

from .unified_material_status import (
    MaterialStatus, 
    MaterialStateTransitions, 
    StatusTransition,
    StatusGroups
)

logger = logging.getLogger(__name__)


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    suggested_action: Optional[str] = None


@dataclass
class TransitionContext:
    """状态转换上下文"""
    creative_id: int
    from_status: str
    to_status: str
    metadata: Dict = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc)


class MaterialStateValidator:
    """
    素材状态转换验证器
    
    提供全面的状态转换验证功能，包括：
    1. 基础状态转换有效性检查
    2. 业务规则验证
    3. 状态转换路径验证
    4. 并发安全性检查
    """
    
    def __init__(self):
        self.transitions = MaterialStateTransitions()
        self.status_enum = MaterialStatus
        
        # 业务规则配置
        self.business_rules = self._init_business_rules()
        
        # 状态转换统计
        self.transition_stats = {}
    
    def _init_business_rules(self) -> Dict:
        """初始化业务规则"""
        return {
            # 状态转换时间限制（分钟）
            'max_processing_time': {
                MaterialStatus.PROCESSING.value: 60,  # 处理状态最多60分钟
                MaterialStatus.TESTING_PENDING_REVIEW.value: 1440,  # 测试状态最多24小时
            },
            
            # 需要特殊权限的转换
            'privileged_transitions': {
                (MaterialStatus.QUALITY_FAILED.value, MaterialStatus.PENDING_GROUPING.value),
                (MaterialStatus.REJECTED.value, MaterialStatus.HARVESTED.value),
            },
            
            # 禁止的转换（即使在转换表中定义）
            'forbidden_transitions': set(),
            
            # 需要额外验证的转换
            'requires_validation': {
                MaterialStatus.APPROVED.value: ['account_balance', 'material_quality'],
                MaterialStatus.HARVESTED.value: ['harvest_conditions'],
            }
        }
    
    def validate_transition(self, context: TransitionContext) -> ValidationResult:
        """
        验证状态转换
        
        Args:
            context: 转换上下文
            
        Returns:
            ValidationResult: 验证结果
        """
        try:
            # 1. 基础有效性检查
            basic_result = self._validate_basic_transition(context)
            if not basic_result.is_valid:
                return basic_result
            
            # 2. 业务规则检查
            business_result = self._validate_business_rules(context)
            if not business_result.is_valid:
                return business_result
            
            # 3. 状态一致性检查
            consistency_result = self._validate_state_consistency(context)
            if not consistency_result.is_valid:
                return consistency_result
            
            # 4. 记录转换统计
            self._record_transition_stats(context)
            
            logger.info(f"状态转换验证通过: 素材{context.creative_id} {context.from_status} -> {context.to_status}")
            
            return ValidationResult(
                is_valid=True,
                warning_message=business_result.warning_message or consistency_result.warning_message
            )
            
        except Exception as e:
            logger.error(f"状态转换验证异常: {context.creative_id}, {e}")
            return ValidationResult(
                is_valid=False,
                error_message=f"验证过程异常: {str(e)}"
            )
    
    def _validate_basic_transition(self, context: TransitionContext) -> ValidationResult:
        """基础转换有效性检查"""
        
        # 检查状态值有效性
        if not self.status_enum.is_valid_status(context.from_status):
            return ValidationResult(
                is_valid=False,
                error_message=f"无效的源状态: {context.from_status}"
            )
        
        if not self.status_enum.is_valid_status(context.to_status):
            return ValidationResult(
                is_valid=False,
                error_message=f"无效的目标状态: {context.to_status}"
            )
        
        # 检查转换路径有效性
        if not self.transitions.can_transition(context.from_status, context.to_status):
            valid_next = self.transitions.get_valid_next_states(context.from_status)
            return ValidationResult(
                is_valid=False,
                error_message=f"无效的状态转换: {context.from_status} -> {context.to_status}",
                suggested_action=f"有效的下一状态: {', '.join(valid_next)}"
            )
        
        # 检查是否为禁止转换
        transition_pair = (context.from_status, context.to_status)
        if transition_pair in self.business_rules['forbidden_transitions']:
            return ValidationResult(
                is_valid=False,
                error_message=f"禁止的状态转换: {context.from_status} -> {context.to_status}"
            )
        
        return ValidationResult(is_valid=True)
    
    def _validate_business_rules(self, context: TransitionContext) -> ValidationResult:
        """业务规则验证"""
        
        warnings = []
        
        # 检查状态停留时间
        max_times = self.business_rules['max_processing_time']
        if context.from_status in max_times:
            # 这里需要从数据库获取状态开始时间，暂时跳过
            # max_minutes = max_times[context.from_status]
            # if current_duration > max_minutes:
            #     warnings.append(f"状态 {context.from_status} 停留时间过长")
            pass
        
        # 检查特权转换
        transition_pair = (context.from_status, context.to_status)
        if transition_pair in self.business_rules['privileged_transitions']:
            # 这里需要检查用户权限，暂时只记录警告
            warnings.append(f"特权转换: {context.from_status} -> {context.to_status}")
        
        # 检查额外验证要求
        if context.to_status in self.business_rules['requires_validation']:
            required_checks = self.business_rules['requires_validation'][context.to_status]
            for check in required_checks:
                if check not in context.metadata:
                    warnings.append(f"缺少必要的验证信息: {check}")
        
        warning_message = "; ".join(warnings) if warnings else None
        
        return ValidationResult(
            is_valid=True,
            warning_message=warning_message
        )
    
    def _validate_state_consistency(self, context: TransitionContext) -> ValidationResult:
        """状态一致性检查"""
        
        warnings = []
        
        # 检查终态状态转换
        if self.status_enum.is_terminal_status(context.from_status):
            warnings.append(f"从终态状态 {context.from_status} 转换，请确认是否正确")
        
        # 检查错误状态恢复
        if self.status_enum.is_error_status(context.from_status):
            if context.to_status not in StatusGroups.ACTIVE_WORKFLOW:
                warnings.append(f"错误状态 {context.from_status} 恢复到非活跃状态 {context.to_status}")
        
        # 检查废弃状态使用
        deprecated_statuses = {
            MaterialStatus.PENDING_UPLOAD.value,
            MaterialStatus.UPLOADING.value,
            MaterialStatus.UPLOADED.value
        }
        
        if context.to_status in deprecated_statuses:
            warnings.append(f"使用了废弃状态 {context.to_status}，建议使用新状态")
        
        warning_message = "; ".join(warnings) if warnings else None
        
        return ValidationResult(
            is_valid=True,
            warning_message=warning_message
        )
    
    def _record_transition_stats(self, context: TransitionContext):
        """记录转换统计"""
        transition_key = f"{context.from_status}->{context.to_status}"
        
        if transition_key not in self.transition_stats:
            self.transition_stats[transition_key] = {
                'count': 0,
                'last_transition': None,
                'creative_ids': set()
            }
        
        self.transition_stats[transition_key]['count'] += 1
        self.transition_stats[transition_key]['last_transition'] = context.timestamp
        self.transition_stats[transition_key]['creative_ids'].add(context.creative_id)
    
    def validate_transition_path(self, status_path: List[str]) -> ValidationResult:
        """
        验证状态转换路径
        
        Args:
            status_path: 状态路径列表
            
        Returns:
            ValidationResult: 验证结果
        """
        if len(status_path) < 2:
            return ValidationResult(is_valid=True)
        
        for i in range(len(status_path) - 1):
            current_state = status_path[i]
            next_state = status_path[i + 1]
            
            context = TransitionContext(
                creative_id=0,  # 路径验证不需要具体ID
                from_status=current_state,
                to_status=next_state
            )
            
            result = self._validate_basic_transition(context)
            if not result.is_valid:
                return ValidationResult(
                    is_valid=False,
                    error_message=f"路径验证失败在步骤 {i+1}: {result.error_message}"
                )
        
        return ValidationResult(is_valid=True)
    
    def get_recommended_next_states(self, current_state: str, context: Dict = None) -> List[str]:
        """
        获取推荐的下一状态
        
        Args:
            current_state: 当前状态
            context: 上下文信息
            
        Returns:
            List[str]: 推荐的下一状态列表
        """
        valid_next = self.transitions.get_valid_next_states(current_state)
        
        if not context:
            return valid_next
        
        # 根据上下文过滤推荐状态
        recommended = []
        
        for next_state in valid_next:
            # 过滤废弃状态
            deprecated_statuses = {
                MaterialStatus.PENDING_UPLOAD.value,
                MaterialStatus.UPLOADING.value,
                MaterialStatus.UPLOADED.value
            }
            
            if next_state in deprecated_statuses:
                continue
            
            # 根据业务逻辑推荐
            if current_state == MaterialStatus.PROCESSING.value:
                # 处理状态优先推荐成功状态
                if next_state == MaterialStatus.UPLOADED_PENDING_PLAN.value:
                    recommended.insert(0, next_state)  # 优先推荐
                else:
                    recommended.append(next_state)
            else:
                recommended.append(next_state)
        
        return recommended
    
    def get_transition_statistics(self) -> Dict:
        """获取转换统计信息"""
        stats = {}
        
        for transition, data in self.transition_stats.items():
            stats[transition] = {
                'count': data['count'],
                'last_transition': data['last_transition'].isoformat() if data['last_transition'] else None,
                'unique_creatives': len(data['creative_ids'])
            }
        
        return stats
    
    def reset_statistics(self):
        """重置统计信息"""
        self.transition_stats.clear()
        logger.info("状态转换统计信息已重置")


# === 便捷函数 ===

def validate_material_transition(creative_id: int, from_status: str, to_status: str, 
                                metadata: Dict = None) -> ValidationResult:
    """
    便捷的状态转换验证函数
    
    Args:
        creative_id: 素材ID
        from_status: 源状态
        to_status: 目标状态
        metadata: 元数据
        
    Returns:
        ValidationResult: 验证结果
    """
    validator = MaterialStateValidator()
    context = TransitionContext(
        creative_id=creative_id,
        from_status=from_status,
        to_status=to_status,
        metadata=metadata or {}
    )
    
    return validator.validate_transition(context)


def get_valid_transitions_for_status(status: str) -> List[str]:
    """
    获取指定状态的有效转换
    
    Args:
        status: 当前状态
        
    Returns:
        List[str]: 有效的下一状态列表
    """
    return MaterialStateTransitions.get_valid_next_states(status)


def is_transition_allowed(from_status: str, to_status: str) -> bool:
    """
    检查状态转换是否被允许
    
    Args:
        from_status: 源状态
        to_status: 目标状态
        
    Returns:
        bool: 是否允许转换
    """
    return MaterialStateTransitions.can_transition(from_status, to_status)


# === 导出接口 ===

__all__ = [
    'MaterialStateValidator',
    'ValidationResult',
    'TransitionContext',
    'validate_material_transition',
    'get_valid_transitions_for_status',
    'is_transition_allowed'
]
