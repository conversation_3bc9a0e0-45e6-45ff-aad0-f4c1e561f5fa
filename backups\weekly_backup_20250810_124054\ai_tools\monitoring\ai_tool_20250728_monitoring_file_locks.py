#!/usr/bin/env python3
"""文件锁监控工具"""

import sys
sys.path.insert(0, r"D:\Project\qianchuangzl\src")

from qianchuan_aw.utils.file_lock_manager import get_file_lock_manager
from qianchuan_aw.utils.logger import logger
import time

def monitor_file_locks():
    """监控文件锁状态"""
    lock_manager = get_file_lock_manager()
    
    logger.info("🔍 文件锁监控启动...")
    
    while True:
        try:
            with lock_manager.lock_manager_lock:
                locked_files = [
                    path for path, lock in lock_manager.file_locks.items()
                    if lock.locked()
                ]
            
            if locked_files:
                logger.info(f"🔒 当前锁定文件数: {len(locked_files)}")
                for file_path in locked_files[:3]:  # 只显示前3个
                    logger.info(f"  - {file_path}")
            else:
                logger.debug("✅ 当前无文件被锁定")
            
            time.sleep(30)  # 每30秒检查一次
            
        except KeyboardInterrupt:
            logger.info("⏹️ 文件锁监控停止")
            break
        except Exception as e:
            logger.error(f"❌ 监控错误: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_file_locks()
