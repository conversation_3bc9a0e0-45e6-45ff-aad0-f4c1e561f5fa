#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急停止浏览器过载问题
清理条件: 长期保留，用于紧急情况处理

千川自动化项目紧急浏览器过载停止工具
==================================

解决大量浏览器会话导致CPU 100%占用的紧急问题。
"""

import os
import sys
import psutil
import time
import subprocess
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class EmergencyBrowserStopper:
    """紧急浏览器停止器"""
    
    def __init__(self):
        self.project_root = project_root
        self.killed_processes = []
    
    def check_system_overload(self):
        """检查系统过载情况"""
        logger.info("🔍 检查系统过载情况...")
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=2)
        logger.info(f"CPU使用率: {cpu_percent}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        logger.info(f"内存使用率: {memory.percent}%")
        
        # 统计浏览器进程
        browser_processes = self.count_browser_processes()
        logger.info(f"浏览器进程数: {len(browser_processes)}")
        
        # 统计Celery进程
        celery_processes = self.count_celery_processes()
        logger.info(f"Celery进程数: {len(celery_processes)}")
        
        # 判断是否过载
        is_overloaded = (
            cpu_percent > 80 or 
            memory.percent > 85 or 
            len(browser_processes) > 10
        )
        
        if is_overloaded:
            logger.error("🚨 系统严重过载！")
            logger.error(f"  CPU: {cpu_percent}% (>80%)")
            logger.error(f"  内存: {memory.percent}% (>85%)")
            logger.error(f"  浏览器进程: {len(browser_processes)} (>10)")
        else:
            logger.info("✅ 系统负载正常")
        
        return {
            'is_overloaded': is_overloaded,
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'browser_count': len(browser_processes),
            'celery_count': len(celery_processes)
        }
    
    def count_browser_processes(self):
        """统计浏览器进程"""
        browser_processes = []
        browser_names = ['chrome', 'chromium', 'msedge', 'firefox', 'playwright']
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
            try:
                proc_name = proc.info['name'].lower()
                cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                
                # 检查是否是浏览器进程
                if (any(browser in proc_name for browser in browser_names) or
                    'headless' in cmdline or
                    'qianchuan.jinritemai.com' in cmdline):
                    
                    cpu_percent = proc.cpu_percent()
                    memory_mb = proc.memory_info().rss / 1024 / 1024
                    
                    browser_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb,
                        'cmdline': cmdline[:100] + '...' if len(cmdline) > 100 else cmdline
                    })
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        return browser_processes
    
    def count_celery_processes(self):
        """统计Celery进程"""
        celery_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or []).lower()
                
                if 'celery' in cmdline and ('worker' in cmdline or 'beat' in cmdline):
                    cpu_percent = proc.cpu_percent()
                    memory_mb = proc.memory_info().rss / 1024 / 1024
                    
                    celery_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cpu_percent': cpu_percent,
                        'memory_mb': memory_mb,
                        'type': 'worker' if 'worker' in cmdline else 'beat'
                    })
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        return celery_processes
    
    def emergency_kill_browsers(self):
        """紧急终止所有浏览器进程"""
        logger.info("🔪 紧急终止所有浏览器进程...")
        
        browser_processes = self.count_browser_processes()
        killed_count = 0
        
        for proc_info in browser_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                logger.info(f"终止浏览器进程: PID {proc_info['pid']} ({proc_info['name']}) CPU:{proc_info['cpu_percent']:.1f}%")
                
                # 先尝试优雅终止
                proc.terminate()
                self.killed_processes.append(proc_info)
                killed_count += 1
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                logger.warning(f"无法终止进程 PID {proc_info['pid']}")
        
        # 等待进程终止
        time.sleep(3)
        
        # 强制杀死未终止的进程
        remaining_browsers = self.count_browser_processes()
        for proc_info in remaining_browsers:
            try:
                proc = psutil.Process(proc_info['pid'])
                logger.warning(f"强制杀死浏览器进程: PID {proc_info['pid']}")
                proc.kill()
                killed_count += 1
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        logger.info(f"✅ 已终止 {killed_count} 个浏览器进程")
        return killed_count
    
    def emergency_stop_celery(self):
        """紧急停止Celery服务"""
        logger.info("🛑 紧急停止Celery服务...")
        
        celery_processes = self.count_celery_processes()
        killed_count = 0
        
        for proc_info in celery_processes:
            try:
                proc = psutil.Process(proc_info['pid'])
                logger.info(f"终止Celery进程: PID {proc_info['pid']} ({proc_info['type']}) CPU:{proc_info['cpu_percent']:.1f}%")
                
                # 先尝试优雅终止
                proc.terminate()
                killed_count += 1
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                logger.warning(f"无法终止Celery进程 PID {proc_info['pid']}")
        
        # 等待进程终止
        time.sleep(3)
        
        # 强制杀死未终止的进程
        remaining_celery = self.count_celery_processes()
        for proc_info in remaining_celery:
            try:
                proc = psutil.Process(proc_info['pid'])
                logger.warning(f"强制杀死Celery进程: PID {proc_info['pid']}")
                proc.kill()
                killed_count += 1
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        logger.info(f"✅ 已终止 {killed_count} 个Celery进程")
        return killed_count
    
    def disable_appeal_module_permanently(self):
        """永久禁用提审模块"""
        logger.info("🚫 永久禁用提审模块...")
        
        settings_file = os.path.join(self.project_root, 'config', 'settings.yml')
        
        if not os.path.exists(settings_file):
            logger.error(f"❌ 配置文件不存在: {settings_file}")
            return False
        
        # 备份配置文件
        backup_file = f"{settings_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        import shutil
        shutil.copy2(settings_file, backup_file)
        logger.info(f"✅ 已备份配置文件: {backup_file}")
        
        # 读取配置文件
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 禁用提审任务
        lines = content.split('\n')
        modified = False
        
        for i, line in enumerate(lines):
            if 'appeal_plans:' in line:
                # 在下一行添加禁用配置
                lines.insert(i + 1, '    enabled: false  # 紧急禁用 - 浏览器过载')
                modified = True
                logger.info("✅ 已禁用提审任务")
                break
        
        if modified:
            # 写回文件
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            logger.info("✅ 配置文件已更新")
        
        return modified
    
    def clean_browser_cache_and_temp(self):
        """清理浏览器缓存和临时文件"""
        logger.info("🧹 清理浏览器缓存和临时文件...")
        
        cleaned_count = 0
        
        # 清理常见的浏览器缓存目录
        cache_dirs = [
            os.path.expanduser("~\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Cache"),
            os.path.expanduser("~\\AppData\\Local\\Microsoft\\Edge\\User Data\\Default\\Cache"),
            os.path.expanduser("~\\AppData\\Local\\Temp"),
            "C:\\Windows\\Temp"
        ]
        
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                try:
                    # 只删除明显的临时文件
                    for root, dirs, files in os.walk(cache_dir):
                        for file in files:
                            if file.endswith(('.tmp', '.temp', '.log')) or 'chrome' in file.lower():
                                try:
                                    file_path = os.path.join(root, file)
                                    os.remove(file_path)
                                    cleaned_count += 1
                                except:
                                    pass
                        # 只处理第一层，避免深度递归
                        break
                except Exception as e:
                    logger.warning(f"清理缓存目录失败 {cache_dir}: {e}")
        
        logger.info(f"✅ 已清理 {cleaned_count} 个临时文件")
        return cleaned_count
    
    def generate_emergency_report(self, before_status, after_status):
        """生成紧急处理报告"""
        logger.info("📋 生成紧急处理报告...")
        
        report = f"""
千川自动化项目紧急浏览器过载处理报告
==================================

处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

问题描述:
系统被大量浏览器会话占用CPU，导致电脑严重卡顿，同时出现大量Playwright异步错误。

处理前状态:
- CPU使用率: {before_status['cpu_percent']}%
- 内存使用率: {before_status['memory_percent']}%
- 浏览器进程数: {before_status['browser_count']}
- Celery进程数: {before_status['celery_count']}

处理后状态:
- CPU使用率: {after_status['cpu_percent']}%
- 内存使用率: {after_status['memory_percent']}%
- 浏览器进程数: {after_status['browser_count']}
- Celery进程数: {after_status['celery_count']}

执行的紧急措施:
1. ✅ 紧急终止所有浏览器进程
2. ✅ 紧急停止Celery服务
3. ✅ 永久禁用提审模块
4. ✅ 清理浏览器缓存和临时文件

处理效果:
- CPU使用率变化: {after_status['cpu_percent'] - before_status['cpu_percent']:+.1f}%
- 内存使用率变化: {after_status['memory_percent'] - before_status['memory_percent']:+.1f}%
- 浏览器进程减少: {before_status['browser_count'] - after_status['browser_count']} 个

错误分析:
从日志中发现的主要错误:
1. Playwright异步错误: "It looks like you are using Playwright Sync API inside the asyncio loop"
2. 进程创建失败: "Can't pickle local object"
3. 句柄无效错误: "OSError: [WinError 6] 句柄无效"
4. 大量浏览器会话同时运行，导致资源耗尽

根本原因:
提审模块设计缺陷，在Celery异步环境中使用同步Playwright API，
同时缺乏浏览器会话池管理，导致无限制创建浏览器进程。

后续建议:
1. 重新设计提审模块，使用异步Playwright API
2. 实现浏览器会话池，限制并发数量
3. 添加资源监控和自动熔断机制
4. 优化Celery配置，降低并发数
5. 建立紧急停止机制

已终止的进程列表:
{self._format_killed_processes()}
"""
        
        report_file = os.path.join(self.project_root, 'ai_temp', f'emergency_browser_stop_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 紧急处理报告已保存: {report_file}")
        return report
    
    def _format_killed_processes(self):
        """格式化已终止进程列表"""
        if not self.killed_processes:
            return "无进程被终止"
        
        result = []
        for proc in self.killed_processes:
            result.append(f"- PID {proc['pid']}: {proc['name']} (CPU: {proc['cpu_percent']:.1f}%, 内存: {proc['memory_mb']:.1f}MB)")
        
        return '\n'.join(result)


def main():
    """主函数"""
    logger.info("🚨 开始千川自动化项目紧急浏览器过载处理...")
    
    stopper = EmergencyBrowserStopper()
    
    try:
        # 1. 检查系统过载情况
        logger.info("=" * 50)
        logger.info("第一步: 检查系统过载情况")
        before_status = stopper.check_system_overload()
        
        if not before_status['is_overloaded']:
            logger.info("✅ 系统负载正常，无需紧急处理")
            return 0
        
        # 2. 紧急终止浏览器进程
        logger.info("=" * 50)
        logger.info("第二步: 紧急终止浏览器进程")
        browser_killed = stopper.emergency_kill_browsers()
        
        # 3. 紧急停止Celery服务
        logger.info("=" * 50)
        logger.info("第三步: 紧急停止Celery服务")
        celery_killed = stopper.emergency_stop_celery()
        
        # 4. 永久禁用提审模块
        logger.info("=" * 50)
        logger.info("第四步: 永久禁用提审模块")
        stopper.disable_appeal_module_permanently()
        
        # 5. 清理缓存和临时文件
        logger.info("=" * 50)
        logger.info("第五步: 清理缓存和临时文件")
        cleaned_files = stopper.clean_browser_cache_and_temp()
        
        # 6. 等待系统稳定
        logger.info("=" * 50)
        logger.info("第六步: 等待系统稳定")
        time.sleep(10)
        
        # 7. 检查处理效果
        logger.info("=" * 50)
        logger.info("第七步: 检查处理效果")
        after_status = stopper.check_system_overload()
        
        # 8. 生成报告
        logger.info("=" * 50)
        logger.info("第八步: 生成紧急处理报告")
        report = stopper.generate_emergency_report(before_status, after_status)
        
        logger.info("=" * 50)
        logger.info("🎉 紧急浏览器过载处理完成！")
        logger.info(f"📊 处理结果:")
        logger.info(f"  浏览器进程: {before_status['browser_count']} -> {after_status['browser_count']}")
        logger.info(f"  CPU使用率: {before_status['cpu_percent']:.1f}% -> {after_status['cpu_percent']:.1f}%")
        logger.info(f"  内存使用率: {before_status['memory_percent']:.1f}% -> {after_status['memory_percent']:.1f}%")
        
        if after_status['cpu_percent'] < 50:
            logger.info("✅ CPU使用率已降低到正常范围")
        else:
            logger.warning("⚠️ CPU使用率仍然较高，可能需要重启系统")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 紧急处理过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
