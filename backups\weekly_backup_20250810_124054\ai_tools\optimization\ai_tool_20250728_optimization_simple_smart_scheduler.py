#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 简化版智能提审调度器
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional
from collections import defaultdict
from loguru import logger

# 添加项目路径 - 使用更可靠的方法
try:
    # 从当前文件向上查找项目根目录
    current_path = Path(__file__).resolve()
    project_root = current_path.parent.parent.parent

    # 验证是否找到正确的项目根目录
    config_file = project_root / 'config' / 'settings.yml'
    if not config_file.exists():
        # 使用当前工作目录
        project_root = Path.cwd()
        config_file = project_root / 'config' / 'settings.yml'

        if not config_file.exists():
            # 尝试常见的项目路径
            possible_roots = [
                Path('D:/Project/qianchuangzl'),
                Path.cwd().parent,
                Path.cwd()
            ]

            for root in possible_roots:
                test_config = root / 'config' / 'settings.yml'
                if test_config.exists():
                    project_root = root
                    break

    sys.path.insert(0, str(project_root))

except Exception as e:
    # 如果所有方法都失败，使用当前工作目录
    project_root = Path.cwd()
    sys.path.insert(0, str(project_root))

def get_plans_grouped_by_account_simple(min_age_minutes: int = 60, app_settings: Optional[dict] = None) -> Dict[str, List[dict]]:
    """
    简化版：获取按广告户分组的待提审计划
    """
    logger.info("🔍 智能查找待提审计划（简化版）...")

    # 🚨 安全检查：检查提审策略配置
    if app_settings:
        appeal_for_prod_plans = app_settings.get('appeal_strategy', {}).get('appeal_for_prod_plans', False)

        if appeal_for_prod_plans:
            logger.info("⚠️ 提审策略：允许对所有账户类型进行提审")
            account_type_filter = None  # 不过滤账户类型
        else:
            logger.info("🛡️ 提审策略：仅对测试账户进行提审，正式投放账户受保护")
            account_type_filter = ['TEST']  # 只处理测试账户
    else:
        # 如果没有配置，默认只处理测试账户
        logger.warning("⚠️ 未找到提审策略配置，默认仅对测试账户进行提审")
        account_type_filter = ['TEST']

    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from sqlalchemy.orm import joinedload
        from sqlalchemy import or_
        
        db = SessionLocal()
        try:
            # 计算时间阈值
            now = datetime.now(timezone.utc)
            min_creation_time = now - timedelta(minutes=min_age_minutes)
            
            logger.info(f"⏰ 时间策略: 只处理 {min_age_minutes} 分钟前创建的计划")
            logger.info(f"   当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"   最早处理时间: {min_creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 构建基础查询
            query = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None),
                or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0),
                Campaign.created_at <= min_creation_time
            )

            # 根据安全策略过滤账户类型
            if account_type_filter:
                query = query.join(AdAccount).filter(AdAccount.account_type.in_(account_type_filter))
                logger.info(f"🎯 提审范围限制为账户类型: {account_type_filter}")

            plans = query.all()
            
            if not plans:
                logger.info("✅ 没有符合条件的待提审计划")
                return {}
            
            logger.info(f"📊 找到 {len(plans)} 个符合条件的计划")
            
            # 按广告户分组
            grouped_plans = defaultdict(list)
            
            for plan in plans:
                principal_name = plan.account.principal.name
                account_id = plan.account.account_id_qc
                account_key = f"{principal_name}_{account_id}"
                
                # 计算计划年龄
                plan_age_minutes = (now - plan.created_at.replace(tzinfo=timezone.utc)).total_seconds() / 60
                
                plan_info = {
                    'campaign_id': plan.campaign_id_qc,
                    'principal_name': principal_name,
                    'account_id': account_id,
                    'created_at': plan.created_at,
                    'age_minutes': int(plan_age_minutes)
                }
                
                grouped_plans[account_key].append(plan_info)
            
            # 统计信息
            logger.info(f"📋 智能分组结果:")
            total_plans = 0
            for account_key, account_plans in grouped_plans.items():
                principal_name = account_plans[0]['principal_name']
                account_id = account_plans[0]['account_id']
                avg_age = sum(p['age_minutes'] for p in account_plans) / len(account_plans)
                logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划 (平均年龄: {avg_age:.0f}分钟)")
                total_plans += len(account_plans)
            
            logger.info(f"📊 总计: {len(grouped_plans)} 个广告户，{total_plans} 个计划")
            
            return dict(grouped_plans)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 智能查找待提审计划失败: {e}")
        return {}

def get_failed_plans_simple(max_retry_attempts: int = 3, app_settings: Optional[dict] = None) -> Dict[str, List[dict]]:
    """
    简化版：获取需要重试的失败计划
    """
    logger.info("🔍 智能查找需要重试的失败计划（简化版）...")

    # 🚨 安全检查：检查提审策略配置
    if app_settings:
        appeal_for_prod_plans = app_settings.get('appeal_strategy', {}).get('appeal_for_prod_plans', False)

        if appeal_for_prod_plans:
            logger.info("⚠️ 提审策略：允许对所有账户类型进行重试")
            account_type_filter = None  # 不过滤账户类型
        else:
            logger.info("🛡️ 提审策略：仅对测试账户进行重试，正式投放账户受保护")
            account_type_filter = ['TEST']  # 只处理测试账户
    else:
        # 如果没有配置，默认只处理测试账户
        logger.warning("⚠️ 未找到提审策略配置，默认仅对测试账户进行重试")
        account_type_filter = ['TEST']
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from sqlalchemy.orm import joinedload
        from sqlalchemy import or_
        
        db = SessionLocal()
        try:
            # 构建基础查询
            query = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status == 'submission_failed',
                or_(Campaign.appeal_attempt_count.is_(None),
                    Campaign.appeal_attempt_count < max_retry_attempts)
            )

            # 根据安全策略过滤账户类型
            if account_type_filter:
                query = query.join(AdAccount).filter(AdAccount.account_type.in_(account_type_filter))
                logger.info(f"🎯 重试范围限制为账户类型: {account_type_filter}")

            failed_plans = query.all()
            
            if not failed_plans:
                logger.info("✅ 没有需要重试的失败计划")
                return {}
            
            # 过滤掉明显因为计划太新导致的失败
            filtered_plans = []
            for plan in failed_plans:
                error_msg = plan.appeal_error_message or ""
                if "暂无广告审核建议" not in error_msg and "查询到该内容暂无广告审核建议" not in error_msg:
                    filtered_plans.append(plan)
            
            logger.info(f"📊 找到 {len(filtered_plans)} 个需要重试的失败计划（已过滤明显的时间问题）")
            
            # 按广告户分组
            grouped_plans = defaultdict(list)
            
            for plan in filtered_plans:
                principal_name = plan.account.principal.name
                account_id = plan.account.account_id_qc
                account_key = f"{principal_name}_{account_id}"
                
                plan_info = {
                    'campaign_id': plan.campaign_id_qc,
                    'principal_name': principal_name,
                    'account_id': account_id,
                    'created_at': plan.created_at,
                    'error_message': plan.appeal_error_message,
                    'attempt_count': plan.appeal_attempt_count or 0
                }
                
                grouped_plans[account_key].append(plan_info)
            
            # 统计信息
            if grouped_plans:
                logger.info(f"📋 失败计划智能分组:")
                for account_key, account_plans in grouped_plans.items():
                    principal_name = account_plans[0]['principal_name']
                    account_id = account_plans[0]['account_id']
                    logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个失败计划")
            
            return dict(grouped_plans)
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 智能查找失败计划失败: {e}")
        return {}

def execute_smart_batch_appeal_simple(grouped_plans: Dict[str, List[dict]], 
                                    app_settings: dict, 
                                    is_retry: bool = False) -> Dict[str, int]:
    """
    简化版：执行智能批量提审
    """
    operation_type = "重试" if is_retry else "提审"
    logger.info(f"🚀 开始执行智能批量{operation_type}（简化版）...")
    
    if not grouped_plans:
        logger.info(f"✅ 没有需要{operation_type}的计划")
        return {}
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
        from qianchuan_aw.database.database import SessionLocal
        
        # 创建提审服务
        appeal_service = create_production_appeal_service(app_settings)
        
        results = {}
        total_accounts = len(grouped_plans)
        batch_size_per_account = app_settings.get('appeal_scheduler', {}).get('batch_size_per_account', 10)
        account_interval_seconds = app_settings.get('appeal_scheduler', {}).get('account_interval_seconds', 5)
        
        for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
            principal_name = account_plans[0]['principal_name']
            account_id = account_plans[0]['account_id']
            
            logger.info(f"\n📋 处理第 {i}/{total_accounts} 个广告户")
            logger.info(f"🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划")
            logger.info("="*60)
            
            # 限制每批次的计划数量
            batch_plans = account_plans[:batch_size_per_account]
            if len(account_plans) > batch_size_per_account:
                logger.info(f"⚠️ 计划数量超过批次限制，本次处理前 {batch_size_per_account} 个")
            
            # 如果是重试，需要先重置状态
            if is_retry:
                reset_failed_plans_status_simple(batch_plans)
            
            # 准备提审数据
            plans_data = []
            for plan in batch_plans:
                plans_data.append({
                    'campaign_id': plan['campaign_id'],
                    'principal_name': plan['principal_name'],
                    'account_id': plan['account_id']
                })
            
            try:
                # 执行批量提审（关键优化：同一广告户在一个浏览器会话中）
                appeal_results = appeal_service.batch_appeal_all_plans(plans_data)
                
                # 更新数据库
                db = SessionLocal()
                try:
                    updated_count = appeal_service.update_database_with_results(db, appeal_results)
                    
                    # 统计成功数量
                    success_count = sum(1 for result in appeal_results if result['success'])
                    results[account_key] = success_count
                    
                    logger.info(f"📊 广告户 {principal_name} 处理完成: {success_count}/{len(batch_plans)} 个成功")
                    
                    # 显示详细结果
                    for result in appeal_results:
                        if result['success']:
                            logger.success(f"   ✅ {result['campaign_id']}: 提审成功")
                        else:
                            logger.error(f"   ❌ {result['campaign_id']}: {result['message'][:50]}...")
                    
                finally:
                    db.close()
                
            except Exception as e:
                logger.error(f"❌ 广告户 {principal_name} 处理失败: {e}")
                results[account_key] = 0
            
            # 广告户之间等待
            if i < total_accounts:
                logger.info(f"⏳ 等待{account_interval_seconds}秒后处理下一个广告户...")
                time.sleep(account_interval_seconds)
        
        # 生成总结报告
        total_success = sum(results.values())
        total_plans = sum(len(plans) for plans in grouped_plans.values())
        
        logger.info(f"\n🎉 智能批量{operation_type}完成!")
        logger.info(f"📊 总体结果: {total_success}/{total_plans} 个计划{operation_type}成功")
        logger.info(f"📊 广告户处理: {len(results)}/{total_accounts} 个广告户")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 执行智能批量{operation_type}失败: {e}")
        return {}

def reset_failed_plans_status_simple(plans: List[dict]):
    """简化版：重置失败计划的状态"""
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign
        
        db = SessionLocal()
        try:
            for plan in plans:
                campaign = db.query(Campaign).filter(
                    Campaign.campaign_id_qc == plan['campaign_id']
                ).first()
                
                if campaign:
                    campaign.appeal_status = None
                    campaign.appeal_error_message = None
            
            db.commit()
            logger.info(f"✅ 已重置 {len(plans)} 个失败计划的状态")
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"❌ 重置失败计划状态失败: {e}")

def main():
    """主函数"""
    logger.info("🎯 简化版智能提审调度器")
    logger.info("="*80)
    logger.info("🔧 优化特性:")
    logger.info("1. 按广告户分组批量提审，减少浏览器启动次数")
    logger.info("2. 新建计划延迟提审，避免'无审核建议'错误")
    logger.info("3. 智能重试失败的提审，排除明显的时间问题")
    logger.info("4. 简化SQL查询，提高稳定性")
    logger.info("="*80)
    
    try:
        # 加载配置
        import yaml
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 获取配置参数
        appeal_config = app_settings.get('appeal_scheduler', {})
        min_plan_age_minutes = appeal_config.get('min_plan_age_minutes', 60)
        max_retry_attempts = appeal_config.get('max_retry_attempts', 3)
        
        total_success = 0
        
        # 第一阶段: 处理新建计划
        logger.info("\n🆕 第一阶段: 智能处理新建计划")
        logger.info("-" * 50)

        new_plans = get_plans_grouped_by_account_simple(min_plan_age_minutes, app_settings)
        if new_plans:
            new_results = execute_smart_batch_appeal_simple(new_plans, app_settings, is_retry=False)
            new_success = sum(new_results.values())
            total_success += new_success
            logger.info(f"✅ 新计划处理完成: {new_success} 个成功")
        else:
            logger.info("✅ 没有符合条件的新计划")
        
        # 第二阶段: 智能重试失败计划
        logger.info("\n🔄 第二阶段: 智能重试失败计划")
        logger.info("-" * 50)
        
        failed_plans = get_failed_plans_simple(max_retry_attempts, app_settings)
        if failed_plans:
            retry_results = execute_smart_batch_appeal_simple(failed_plans, app_settings, is_retry=True)
            retry_success = sum(retry_results.values())
            total_success += retry_success
            logger.info(f"✅ 失败重试完成: {retry_success} 个成功")
        else:
            logger.info("✅ 没有需要重试的失败计划")
        
        # 生成总结报告
        logger.success(f"\n🎉 智能提审调度完成!")
        logger.info(f"📊 总计: {total_success} 个计划提审成功")
        
        if total_success > 0:
            logger.info("\n💡 智能优化效果:")
            logger.info("- ✅ 按广告户分组，大幅减少浏览器启动次数")
            logger.info("- ✅ 延迟提审策略，避免'无审核建议'错误")
            logger.info("- ✅ 智能重试机制，提高整体成功率")
            logger.info("- ✅ 简化查询逻辑，提升系统稳定性")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能提审调度失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 简化版智能提审调度器执行成功！")
        logger.info("💡 系统现在更加智能、高效和稳定")
    else:
        logger.error("\n❌ 简化版智能提审调度器执行失败")
    
    sys.exit(0 if success else 1)
