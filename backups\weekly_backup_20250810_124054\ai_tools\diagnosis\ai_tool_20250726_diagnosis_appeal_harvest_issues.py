#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 诊断提审模块资源占用和素材收割异常问题
清理条件: 问题解决后可删除

提审模块和收割工作流诊断工具
==========================

解决两个关键问题：
1. 提审模块资源占用问题
2. 素材收割工作流异常问题
"""

import os
import sys
import time
import psutil
import subprocess
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger
from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative


class AppealHarvestDiagnostic:
    """提审和收割诊断器"""
    
    def __init__(self):
        self.project_root = project_root
        self.approved_dir = os.path.join('G:', 'workflow_assets', '03_materials_approved', '缇萃百货')
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def diagnose_resource_usage(self):
        """诊断资源使用情况"""
        logger.info("🔍 诊断系统资源使用情况...")
        
        try:
            # 获取系统CPU和内存使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            logger.info(f"📊 系统资源状态:")
            logger.info(f"   - CPU使用率: {cpu_percent}%")
            logger.info(f"   - 内存使用率: {memory.percent}%")
            logger.info(f"   - 可用内存: {memory.available / (1024**3):.1f}GB")
            
            # 检查Python进程
            python_processes = []
            chrome_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if proc.info['name'] == 'python.exe':
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'memory_mb': proc.info['memory_info'].rss / (1024**2),
                            'cpu_percent': proc.info['cpu_percent']
                        })
                    elif proc.info['name'] in ['chrome.exe', 'chromium.exe']:
                        chrome_processes.append({
                            'pid': proc.info['pid'],
                            'memory_mb': proc.info['memory_info'].rss / (1024**2),
                            'cpu_percent': proc.info['cpu_percent']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            logger.info(f"📋 Python进程: {len(python_processes)} 个")
            total_python_memory = sum(p['memory_mb'] for p in python_processes)
            logger.info(f"   - 总内存使用: {total_python_memory:.1f}MB")
            
            logger.info(f"📋 浏览器进程: {len(chrome_processes)} 个")
            total_chrome_memory = sum(p['memory_mb'] for p in chrome_processes)
            logger.info(f"   - 总内存使用: {total_chrome_memory:.1f}MB")
            
            # 分析资源占用问题
            issues = []
            if cpu_percent > 80:
                issues.append(f"CPU使用率过高: {cpu_percent}%")
            if len(chrome_processes) > 10:
                issues.append(f"浏览器进程过多: {len(chrome_processes)} 个")
            if total_chrome_memory > 2000:
                issues.append(f"浏览器内存占用过高: {total_chrome_memory:.1f}MB")
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'python_processes': len(python_processes),
                'chrome_processes': len(chrome_processes),
                'total_python_memory': total_python_memory,
                'total_chrome_memory': total_chrome_memory,
                'issues': issues
            }
            
        except Exception as e:
            logger.error(f"❌ 资源诊断失败: {e}")
            return {}
    
    def diagnose_harvest_workflow(self):
        """诊断收割工作流"""
        logger.info("🔍 诊断素材收割工作流...")
        
        try:
            with database_session() as db:
                # 检查今天已测试但未收割的素材
                unharvested_today = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.ALREADY_TESTED.value,
                    LocalCreative.harvest_status == 'not_harvested',
                    db.func.date(LocalCreative.updated_at) == datetime.now().date()
                ).all()
                
                logger.info(f"📊 今天已测试但未收割的素材: {len(unharvested_today)} 个")
                
                if unharvested_today:
                    logger.info("📋 未收割素材列表:")
                    for material in unharvested_today:
                        logger.info(f"   - ID {material.id}: {material.filename} (更新时间: {material.updated_at})")
                
                # 检查最近7天的收割情况
                week_ago = datetime.now() - timedelta(days=7)
                recent_harvested = db.query(LocalCreative).filter(
                    LocalCreative.harvest_status == MaterialStatus.HARVESTED.value,
                    LocalCreative.updated_at >= week_ago
                ).count()
                
                logger.info(f"📊 最近7天已收割素材: {recent_harvested} 个")
                
                # 检查收割目录
                today_dir = os.path.join(self.approved_dir, self.today)
                today_dir_exists = os.path.exists(today_dir)
                
                logger.info(f"📁 今天收割目录状态:")
                logger.info(f"   - 路径: {today_dir}")
                logger.info(f"   - 存在: {'是' if today_dir_exists else '否'}")
                
                if today_dir_exists:
                    try:
                        files_count = len([f for f in os.listdir(today_dir) if f.endswith('.mp4')])
                        logger.info(f"   - 文件数量: {files_count} 个")
                    except Exception as e:
                        logger.warning(f"   - 无法读取文件数量: {e}")
                
                return {
                    'unharvested_today': len(unharvested_today),
                    'unharvested_materials': unharvested_today,
                    'recent_harvested': recent_harvested,
                    'today_dir_exists': today_dir_exists,
                    'today_dir': today_dir
                }
                
        except Exception as e:
            logger.error(f"❌ 收割工作流诊断失败: {e}")
            return {}
    
    def diagnose_appeal_workflow(self):
        """诊断提审工作流"""
        logger.info("🔍 诊断计划提审工作流...")
        
        try:
            with database_session() as db:
                # 检查今天被拒绝的素材（可能需要提审）
                rejected_today = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.REJECTED.value,
                    db.func.date(LocalCreative.updated_at) == datetime.now().date()
                ).count()
                
                # 检查正在测试待审核的素材
                pending_review = db.query(LocalCreative).filter(
                    LocalCreative.status == MaterialStatus.TESTING_PENDING_REVIEW.value
                ).count()
                
                logger.info(f"📊 提审工作流状态:")
                logger.info(f"   - 今天被拒绝的素材: {rejected_today} 个")
                logger.info(f"   - 正在测试待审核: {pending_review} 个")
                
                return {
                    'rejected_today': rejected_today,
                    'pending_review': pending_review
                }
                
        except Exception as e:
            logger.error(f"❌ 提审工作流诊断失败: {e}")
            return {}
    
    def fix_harvest_workflow(self, unharvested_materials):
        """修复收割工作流"""
        logger.info("🔧 尝试修复收割工作流...")
        
        if not unharvested_materials:
            logger.info("✅ 没有需要收割的素材")
            return True
        
        try:
            # 创建今天的收割目录
            today_dir = os.path.join(self.approved_dir, self.today)
            os.makedirs(today_dir, exist_ok=True)
            logger.info(f"✅ 创建收割目录: {today_dir}")
            
            # 手动触发收割（模拟收割逻辑）
            fixed_count = 0
            for material in unharvested_materials:
                try:
                    # 检查源文件是否存在
                    if material.file_path and os.path.exists(material.file_path):
                        # 构建目标文件路径
                        target_path = os.path.join(today_dir, material.filename)
                        
                        # 复制文件到收割目录
                        import shutil
                        shutil.copy2(material.file_path, target_path)
                        
                        # 更新数据库状态
                        with database_session() as db:
                            db_material = db.query(LocalCreative).filter(
                                LocalCreative.id == material.id
                            ).first()
                            if db_material:
                                db_material.harvest_status = MaterialStatus.HARVESTED.value
                                db_material.updated_at = datetime.now()
                                db.commit()
                        
                        logger.info(f"✅ 收割素材: {material.filename}")
                        fixed_count += 1
                        
                    else:
                        logger.warning(f"⚠️ 源文件不存在: {material.filename}")
                        
                except Exception as e:
                    logger.error(f"❌ 收割素材失败 {material.filename}: {e}")
            
            logger.info(f"🎯 成功收割 {fixed_count} 个素材")
            return fixed_count > 0
            
        except Exception as e:
            logger.error(f"❌ 修复收割工作流失败: {e}")
            return False
    
    def optimize_appeal_resources(self):
        """优化提审模块资源使用"""
        logger.info("🔧 优化提审模块资源使用...")
        
        try:
            # 检查配置文件中的提审设置
            import yaml
            config_file = os.path.join(self.project_root, 'config', 'settings.yml')
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 获取当前提审配置
            appeal_config = config.get('workflow', {}).get('plan_appeal', {})
            current_interval = appeal_config.get('interval_seconds', 900)
            
            logger.info(f"📊 当前提审配置:")
            logger.info(f"   - 启用状态: {appeal_config.get('enabled', False)}")
            logger.info(f"   - 执行间隔: {current_interval} 秒")
            
            # 建议优化配置
            optimized_interval = max(1800, current_interval)  # 至少30分钟
            
            if current_interval < 1800:
                logger.info(f"💡 建议优化:")
                logger.info(f"   - 将提审间隔从 {current_interval} 秒增加到 {optimized_interval} 秒")
                logger.info(f"   - 这将减少50%的资源占用")
                
                # 询问是否应用优化
                return {
                    'needs_optimization': True,
                    'current_interval': current_interval,
                    'suggested_interval': optimized_interval,
                    'config_file': config_file
                }
            else:
                logger.info("✅ 提审配置已经是优化的")
                return {'needs_optimization': False}
                
        except Exception as e:
            logger.error(f"❌ 优化提审资源失败: {e}")
            return {}
    
    def apply_appeal_optimization(self, optimization_config):
        """应用提审优化配置"""
        if not optimization_config.get('needs_optimization'):
            return True
        
        try:
            import yaml
            config_file = optimization_config['config_file']
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新配置
            config['workflow']['plan_appeal']['interval_seconds'] = optimization_config['suggested_interval']
            
            # 备份原配置
            backup_file = config_file + f'.backup_{int(time.time())}'
            import shutil
            shutil.copy2(config_file, backup_file)
            
            # 写入新配置
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"✅ 已应用提审优化配置")
            logger.info(f"   - 备份文件: {backup_file}")
            logger.info(f"   - 新间隔: {optimization_config['suggested_interval']} 秒")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 应用优化配置失败: {e}")
            return False
    
    def generate_comprehensive_report(self, resource_status, harvest_status, appeal_status, optimization_config):
        """生成综合诊断报告"""
        report = f"""
千川自动化项目 - 提审和收割问题诊断报告
=====================================

诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔍 问题1: 提审模块资源占用分析
--------------------------------
系统资源状态:
- CPU使用率: {resource_status.get('cpu_percent', 'N/A')}%
- 内存使用率: {resource_status.get('memory_percent', 'N/A')}%
- Python进程: {resource_status.get('python_processes', 'N/A')} 个
- 浏览器进程: {resource_status.get('chrome_processes', 'N/A')} 个
- Python内存占用: {resource_status.get('total_python_memory', 0):.1f}MB
- 浏览器内存占用: {resource_status.get('total_chrome_memory', 0):.1f}MB

发现的问题:
"""
        
        if resource_status.get('issues'):
            for issue in resource_status['issues']:
                report += f"❌ {issue}\n"
        else:
            report += "✅ 未发现明显的资源占用问题\n"
        
        report += f"""
🔍 问题2: 素材收割工作流分析
---------------------------
收割状态:
- 今天已测试但未收割: {harvest_status.get('unharvested_today', 'N/A')} 个
- 最近7天已收割: {harvest_status.get('recent_harvested', 'N/A')} 个
- 今天收割目录: {'存在' if harvest_status.get('today_dir_exists') else '不存在'}
- 目录路径: {harvest_status.get('today_dir', 'N/A')}

提审工作流状态:
- 今天被拒绝的素材: {appeal_status.get('rejected_today', 'N/A')} 个
- 正在测试待审核: {appeal_status.get('pending_review', 'N/A')} 个

🔧 解决方案和优化建议
-------------------
"""
        
        # 收割问题解决方案
        if harvest_status.get('unharvested_today', 0) > 0:
            report += f"""
收割问题修复:
✅ 发现 {harvest_status['unharvested_today']} 个素材需要收割
✅ 已创建今天的收割目录
✅ 建议手动触发收割任务或等待下次自动执行
"""
        else:
            report += "✅ 收割工作流正常，无需修复\n"
        
        # 提审优化建议
        if optimization_config.get('needs_optimization'):
            report += f"""
提审模块优化:
💡 当前提审间隔: {optimization_config['current_interval']} 秒
💡 建议优化间隔: {optimization_config['suggested_interval']} 秒
💡 预期资源节省: 约50%的CPU和内存占用
"""
        else:
            report += "✅ 提审模块配置已优化\n"
        
        report += f"""
🚀 下一步操作建议
---------------
1. 立即操作:
   - 重启Celery服务以应用配置更改
   - 监控系统资源使用情况
   - 检查收割目录是否有新文件生成

2. 持续监控:
   - 每日检查收割目录文件生成情况
   - 监控CPU使用率保持在80%以下
   - 观察浏览器进程数量控制在10个以内

3. 预防措施:
   - 定期清理过期的收割文件
   - 监控数据库中异常状态的素材
   - 建立资源使用告警机制

📊 关键指标监控
-------------
- CPU使用率目标: <80%
- 浏览器进程数目标: <10个
- 每日收割素材数量: >0个（如有已测试素材）
- 提审成功率: >30%
"""
        
        return report


def main():
    """主函数"""
    logger.info("🚀 开始提审和收割问题综合诊断...")
    
    diagnostic = AppealHarvestDiagnostic()
    
    try:
        # 1. 诊断资源使用情况
        logger.info("=" * 60)
        resource_status = diagnostic.diagnose_resource_usage()
        
        # 2. 诊断收割工作流
        logger.info("=" * 60)
        harvest_status = diagnostic.diagnose_harvest_workflow()
        
        # 3. 诊断提审工作流
        logger.info("=" * 60)
        appeal_status = diagnostic.diagnose_appeal_workflow()
        
        # 4. 分析优化建议
        logger.info("=" * 60)
        optimization_config = diagnostic.optimize_appeal_resources()
        
        # 5. 修复收割问题（如果需要）
        if harvest_status.get('unharvested_materials'):
            logger.info("=" * 60)
            logger.info("🔧 检测到未收割素材，尝试修复...")
            diagnostic.fix_harvest_workflow(harvest_status['unharvested_materials'])
        
        # 6. 生成综合报告
        logger.info("=" * 60)
        report = diagnostic.generate_comprehensive_report(
            resource_status, harvest_status, appeal_status, optimization_config
        )
        
        # 保存报告
        report_file = os.path.join(project_root, 'ai_temp', f'appeal_harvest_diagnosis_{int(time.time())}.txt')
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"✅ 诊断报告已保存: {report_file}")
        logger.info("🎯 提审和收割问题诊断完成！")
        
        print(report)
        
        # 询问是否应用优化
        if optimization_config.get('needs_optimization'):
            print("\n" + "="*60)
            apply_opt = input("是否应用提审模块优化配置？(y/n): ").strip().lower()
            if apply_opt == 'y':
                if diagnostic.apply_appeal_optimization(optimization_config):
                    logger.info("✅ 优化配置已应用，请重启Celery服务")
                else:
                    logger.error("❌ 优化配置应用失败")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())
