#!/usr/bin/env python3
"""
千川工作流详细错误分析
分析具体的错误模式和频率限制问题
"""

import sys
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, Counter

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.logger import logger

class DetailedErrorAnalyzer:
    """详细错误分析器"""
    
    def __init__(self):
        logger.critical("🔍 千川工作流详细错误分析")
        logger.critical("=" * 60)
        self.analysis_start_time = datetime(2025, 7, 22, 21, 0, 0)
        self.current_time = datetime.now()
        self.log_dir = Path("logs")
    
    def analyze_too_many_requests_pattern(self):
        """分析Too many requests错误模式"""
        logger.critical("📊 分析Too many requests错误模式")
        logger.critical("=" * 60)
        
        log_file = self.log_dir / f"app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        if not log_file.exists():
            logger.error(f"日志文件不存在: {log_file}")
            return
        
        too_many_requests_errors = []
        api_call_timeline = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    # 提取时间戳
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if not time_match:
                        continue
                    
                    try:
                        log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if log_time < self.analysis_start_time:
                            continue
                    except ValueError:
                        continue
                    
                    # 查找Too many requests错误
                    if 'Too many requests' in line or '40003' in line:
                        # 提取API接口信息
                        api_match = re.search(r'URL: (https://[^\s]+)', line)
                        api_url = api_match.group(1) if api_match else 'unknown'
                        
                        too_many_requests_errors.append({
                            'time': log_time,
                            'api_url': api_url,
                            'line': line.strip()
                        })
                    
                    # 记录所有API调用
                    if 'API请求' in line or '_make_api_call' in line:
                        api_match = re.search(r'URL: (https://[^\s]+)', line)
                        if api_match:
                            api_call_timeline.append({
                                'time': log_time,
                                'api_url': api_match.group(1)
                            })
        
        except Exception as e:
            logger.error(f"分析日志文件失败: {e}")
            return
        
        # 分析错误模式
        api_error_counts = defaultdict(int)
        time_distribution = defaultdict(int)
        
        for error in too_many_requests_errors:
            # 提取API接口路径
            api_path = error['api_url'].split('/')[-1] if '/' in error['api_url'] else error['api_url']
            api_error_counts[api_path] += 1
            
            # 按小时分组
            hour_key = error['time'].strftime('%H:00')
            time_distribution[hour_key] += 1
        
        logger.critical(f"📊 Too many requests错误统计:")
        logger.critical(f"  总错误数: {len(too_many_requests_errors)}")
        logger.critical(f"  影响的API接口: {len(api_error_counts)}")
        
        if api_error_counts:
            logger.critical("  错误最多的API接口:")
            for api, count in sorted(api_error_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.critical(f"    {api}: {count} 次")
        
        if time_distribution:
            logger.critical("  错误时间分布:")
            for hour, count in sorted(time_distribution.items()):
                logger.critical(f"    {hour}: {count} 次")
        
        # 分析API调用频率
        if len(api_call_timeline) > 1:
            total_time = (api_call_timeline[-1]['time'] - api_call_timeline[0]['time']).total_seconds()
            call_frequency = len(api_call_timeline) / total_time if total_time > 0 else 0
            
            logger.critical(f"\n📊 API调用频率分析:")
            logger.critical(f"  总API调用: {len(api_call_timeline)} 次")
            logger.critical(f"  时间跨度: {total_time:.0f} 秒")
            logger.critical(f"  平均频率: {call_frequency:.2f} 次/秒")
            logger.critical(f"  每分钟: {call_frequency * 60:.1f} 次/分钟")
            
            if call_frequency > 1:  # 超过1次/秒
                logger.critical(f"  ⚠️ API调用频率过高，可能触发限制")
            else:
                logger.critical(f"  ✅ API调用频率正常")
        
        return {
            'total_errors': len(too_many_requests_errors),
            'api_error_counts': dict(api_error_counts),
            'time_distribution': dict(time_distribution),
            'call_frequency': call_frequency if 'call_frequency' in locals() else 0
        }
    
    def analyze_duplicate_check_issues(self):
        """分析重复检查问题"""
        logger.critical("\n📊 分析重复检查问题")
        logger.critical("=" * 60)
        
        log_file = self.log_dir / f"app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        duplicate_logs = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if not time_match:
                        continue
                    
                    try:
                        log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if log_time < self.analysis_start_time:
                            continue
                    except ValueError:
                        continue
                    
                    # 查找重复检查相关日志
                    if '重复' in line and ('检查' in line or '创建' in line):
                        duplicate_logs.append({
                            'time': log_time,
                            'line': line.strip()
                        })
        
        except Exception as e:
            logger.error(f"分析重复检查日志失败: {e}")
            return
        
        # 分析重复检查模式
        check_patterns = defaultdict(int)
        
        for log_entry in duplicate_logs:
            line = log_entry['line']
            
            # 提取关键信息
            if '重复检查通过' in line:
                check_patterns['通过'] += 1
            elif '重复检查失败' in line:
                check_patterns['失败'] += 1
            elif '发现重复' in line:
                check_patterns['发现重复'] += 1
            else:
                check_patterns['其他'] += 1
        
        logger.critical(f"📊 重复检查统计:")
        logger.critical(f"  总重复检查日志: {len(duplicate_logs)}")
        
        if check_patterns:
            logger.critical("  检查结果分布:")
            for pattern, count in sorted(check_patterns.items(), key=lambda x: x[1], reverse=True):
                logger.critical(f"    {pattern}: {count} 次")
        
        # 显示最近的重复检查日志
        if duplicate_logs:
            logger.critical("  最近的重复检查日志:")
            for log_entry in duplicate_logs[-5:]:  # 显示最后5条
                logger.critical(f"    {log_entry['time']}: {log_entry['line'][:100]}...")
        
        return {
            'total_duplicate_logs': len(duplicate_logs),
            'check_patterns': dict(check_patterns)
        }
    
    def analyze_workflow_performance(self):
        """分析工作流性能"""
        logger.critical("\n📊 分析工作流性能")
        logger.critical("=" * 60)
        
        log_file = self.log_dir / f"app_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        task_performance = defaultdict(list)
        task_starts = {}
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if not time_match:
                        continue
                    
                    try:
                        log_time = datetime.strptime(time_match.group(1), '%Y-%m-%d %H:%M:%S')
                        if log_time < self.analysis_start_time:
                            continue
                    except ValueError:
                        continue
                    
                    # 查找任务开始和结束
                    if '[Task Start]' in line:
                        task_match = re.search(r'\[Task Start\]\s+(\w+)', line)
                        if task_match:
                            task_name = task_match.group(1)
                            task_starts[task_name] = log_time
                    
                    elif '[Task End]' in line:
                        task_match = re.search(r'\[Task End\]\s+(\w+)', line)
                        if task_match:
                            task_name = task_match.group(1)
                            if task_name in task_starts:
                                duration = (log_time - task_starts[task_name]).total_seconds()
                                task_performance[task_name].append(duration)
                                del task_starts[task_name]
        
        except Exception as e:
            logger.error(f"分析工作流性能失败: {e}")
            return
        
        logger.critical(f"📊 工作流任务性能:")
        
        if task_performance:
            for task_name, durations in task_performance.items():
                avg_duration = sum(durations) / len(durations)
                max_duration = max(durations)
                min_duration = min(durations)
                
                logger.critical(f"  {task_name}:")
                logger.critical(f"    执行次数: {len(durations)}")
                logger.critical(f"    平均耗时: {avg_duration:.2f} 秒")
                logger.critical(f"    最长耗时: {max_duration:.2f} 秒")
                logger.critical(f"    最短耗时: {min_duration:.2f} 秒")
        else:
            logger.critical("  未找到完整的任务执行记录")
        
        # 检查未完成的任务
        if task_starts:
            logger.critical(f"  未完成的任务: {len(task_starts)} 个")
            for task_name, start_time in task_starts.items():
                running_time = (self.current_time - start_time).total_seconds()
                logger.critical(f"    {task_name}: 运行中 {running_time:.0f} 秒")
        
        return {
            'task_performance': dict(task_performance),
            'incomplete_tasks': dict(task_starts)
        }
    
    def generate_comprehensive_report(self, api_analysis, duplicate_analysis, performance_analysis):
        """生成综合报告"""
        logger.critical("\n📋 千川工作流详细错误分析报告")
        logger.critical("=" * 60)
        
        logger.critical("🎯 分析概要:")
        logger.critical(f"  分析时间: {self.analysis_start_time} - {self.current_time}")
        
        # API频率限制问题
        if api_analysis:
            logger.critical(f"\n🚨 API频率限制问题:")
            logger.critical(f"  Too many requests错误: {api_analysis['total_errors']} 次")
            logger.critical(f"  API调用频率: {api_analysis['call_frequency']:.2f} 次/秒")
            
            if api_analysis['call_frequency'] > 1:
                logger.critical(f"  ⚠️ 建议: 降低API调用频率，实施限流策略")
            else:
                logger.critical(f"  ✅ API调用频率在合理范围内")
        
        # 重复检查问题
        if duplicate_analysis:
            logger.critical(f"\n📊 重复检查机制:")
            logger.critical(f"  重复检查日志: {duplicate_analysis['total_duplicate_logs']} 条")
            
            if duplicate_analysis['check_patterns']:
                success_rate = duplicate_analysis['check_patterns'].get('通过', 0) / duplicate_analysis['total_duplicate_logs']
                logger.critical(f"  检查成功率: {success_rate:.1%}")
                
                if success_rate > 0.9:
                    logger.critical(f"  ✅ 重复检查机制运行正常")
                else:
                    logger.critical(f"  ⚠️ 重复检查成功率偏低，需要优化")
        
        # 工作流性能
        if performance_analysis:
            logger.critical(f"\n⚡ 工作流性能:")
            if performance_analysis['task_performance']:
                total_tasks = sum(len(durations) for durations in performance_analysis['task_performance'].values())
                logger.critical(f"  完成任务: {total_tasks} 个")
            
            if performance_analysis['incomplete_tasks']:
                logger.critical(f"  未完成任务: {len(performance_analysis['incomplete_tasks'])} 个")
                logger.critical(f"  ⚠️ 可能存在任务阻塞或超时问题")
        
        # 总体建议
        logger.critical(f"\n💡 优化建议:")
        
        if api_analysis and api_analysis['call_frequency'] > 1:
            logger.critical("  1. 实施API限流策略:")
            logger.critical("     - 添加请求间隔控制")
            logger.critical("     - 实现指数退避重试")
            logger.critical("     - 监控API调用频率")
        
        if duplicate_analysis and duplicate_analysis['total_duplicate_logs'] > 1000:
            logger.critical("  2. 优化重复检查机制:")
            logger.critical("     - 减少不必要的重复检查")
            logger.critical("     - 缓存检查结果")
            logger.critical("     - 批量处理检查请求")
        
        if performance_analysis and performance_analysis['incomplete_tasks']:
            logger.critical("  3. 解决任务阻塞问题:")
            logger.critical("     - 检查长时间运行的任务")
            logger.critical("     - 添加任务超时机制")
            logger.critical("     - 实现任务状态监控")

def main():
    """主分析函数"""
    try:
        analyzer = DetailedErrorAnalyzer()
        
        # 1. 分析Too many requests错误模式
        api_analysis = analyzer.analyze_too_many_requests_pattern()
        
        # 2. 分析重复检查问题
        duplicate_analysis = analyzer.analyze_duplicate_check_issues()
        
        # 3. 分析工作流性能
        performance_analysis = analyzer.analyze_workflow_performance()
        
        # 4. 生成综合报告
        analyzer.generate_comprehensive_report(api_analysis, duplicate_analysis, performance_analysis)
        
        logger.critical(f"\n🎉 千川工作流详细错误分析完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 详细错误分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
