{"timestamp": "2025-07-20T12:36:36.785767", "celery_worker_test": {"startup": {"status": "PASS", "details": "Celery Worker启动成功", "error": "", "timestamp": "2025-07-20T12:36:47.619239"}, "ready_status": {"status": "WARN", "details": "Worker状态检查超时", "error": "", "timestamp": "2025-07-20T12:36:52.626023"}}, "celery_beat_test": {"startup": {"status": "PASS", "details": "Celery Beat启动成功", "error": "", "timestamp": "2025-07-20T12:37:02.756359"}}, "task_registration_test": {"core_tasks": {"status": "PASS", "details": "已注册5个核心任务", "error": "", "timestamp": "2025-07-20T12:36:37.471175"}, "missing_tasks": {"status": "WARN", "details": "缺少任务: ['tasks.zombie_patrol', 'tasks.violation_check', 'tasks.comment_management']...", "error": "", "timestamp": "2025-07-20T12:36:37.471175"}}, "core_workflow_test": {"config_loading": {"status": "PASS", "details": "工作流配置加载成功", "error": "", "timestamp": "2025-07-20T12:36:37.471175"}, "database_access": {"status": "PASS", "details": "数据库访问正常，素材数量: 3288", "error": "", "timestamp": "2025-07-20T12:36:37.500436"}, "scheduler_module": {"status": "PASS", "details": "调度器模块导入成功", "error": "", "timestamp": "2025-07-20T12:36:37.501442"}}, "recommendations": ["⚠️ 系统基本可用，建议解决警告项后启动", "可以先启动核心功能，逐步完善其他组件", "监控建议: 启动后观察日志输出，确认任务正常执行", "性能建议: 根据系统负载调整worker并发数和任务间隔", "安全建议: 定期检查工作流目录权限和文件处理状态"]}