#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 基于浏览器调试结果的账户选择增强修复
依赖关系: 全局账户选择器
清理条件: 功能被替代时删除
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_enhanced_account_selector():
    """创建增强版账户选择器修复"""

    enhanced_code = '''
def render_global_account_selector():
    """渲染账户选择器 - 增强版本，解决间歇性选择失败问题"""
    
    # 获取账户筛选条件
    filter_option = st.sidebar.selectbox(
        "账户筛选",
        options=["显示全部账户", "仅显示收藏账户", "仅显示已授权抖音号"],
        key="account_filter",
        help="筛选显示的账户类型"
    )
    
    # 获取筛选后的账户列表
    accounts = get_filtered_accounts(filter_option)
    
    if not accounts:
        st.sidebar.warning("⚠️ 没有找到符合条件的账户")
        return None
    
    # 显示账户统计
    total_accounts = len(get_all_accounts())
    favorite_count = len([acc for acc in get_all_accounts() if getattr(acc, 'is_favorite', False)])
    st.sidebar.info(f"📊 显示 {len(accounts)} 个账户（共 {total_accounts} 个，收藏 {favorite_count} 个）")
    
    # 构建账户选项
    account_options = {}
    for account in accounts:
        is_favorite = getattr(account, 'is_favorite', False)
        star = "⭐ " if is_favorite else ""
        
        # 检查是否有授权的抖音号
        phone_icon = " 📱" if getattr(account, 'douyin_id', None) else ""
        
        display_name = f"{star}{account.name} ({account.account_id_qc}){phone_icon}"
        account_options[display_name] = account
    
    # 获取当前选中的账户
    current_selected = get_global_selected_account()
    
    # 确定当前选中项的索引
    current_index = 0
    if current_selected:
        for i, (display_name, account) in enumerate(account_options.items()):
            if hasattr(account, 'account_id_qc') and hasattr(current_selected, 'account_id_qc'):
                if account.account_id_qc == current_selected.account_id_qc:
                    current_index = i
                    break
    
    # 渲染选择器（不使用回调函数）
    selected_display = st.sidebar.selectbox(
        "选择当前操作账户",
        options=list(account_options.keys()),
        index=current_index,
        key="global_account_selector",
        help="⭐ 标记表示收藏账户，📱 表示已授权抖音号"
    )
    
    # 获取选中的账户对象
    selected_account = account_options[selected_display]
    
    # === 增强的状态验证和同步机制 ===
    
    # 1. 多重状态验证
    def verify_selection_consistency():
        """验证选择状态的一致性"""
        try:
            # 检查 selectbox 的实际值
            selectbox_value = st.session_state.get("global_account_selector")
            
            # 检查全局状态
            global_selected = get_global_selected_account()
            
            # 检查 session_state 记录
            recorded_id = st.session_state.get("_last_selected_account_id")
            
            # 检查显示值是否匹配
            display_match = selectbox_value == selected_display
            
            # 检查全局状态是否存在
            global_exists = global_selected is not None
            
            # 检查ID是否一致
            id_match = False
            if global_selected and hasattr(global_selected, 'account_id_qc'):
                id_match = str(recorded_id) == str(global_selected.account_id_qc)
            
            return {
                'display_match': display_match,
                'global_exists': global_exists,
                'id_match': id_match,
                'overall': display_match and global_exists and id_match
            }
        except Exception as e:
            logger.error(f"状态验证失败: {e}")
            return {
                'display_match': False,
                'global_exists': False,
                'id_match': False,
                'overall': False
            }
    
    # 2. 检查是否需要更新
    should_update = False
    update_reason = ""
    
    if not current_selected:
        should_update = True
        update_reason = "无当前选中账户"
    else:
        # 优先使用千川ID比较（更稳定）
        if hasattr(selected_account, 'account_id_qc') and hasattr(current_selected, 'account_id_qc'):
            if selected_account.account_id_qc != current_selected.account_id_qc:
                should_update = True
                update_reason = f"千川ID不匹配: {selected_account.account_id_qc} != {current_selected.account_id_qc}"
        # 备用：使用数据库ID比较
        elif hasattr(selected_account, 'id') and hasattr(current_selected, 'id'):
            if selected_account.id != current_selected.id:
                should_update = True
                update_reason = f"数据库ID不匹配: {selected_account.id} != {current_selected.id}"
        else:
            should_update = True
            update_reason = "无法比较账户ID"
    
    # 3. 执行状态更新
    if should_update:
        try:
            # 更新全局状态
            set_global_selected_account(selected_account)
            
            # 强制更新session_state记录
            st.session_state["_last_selected_account_id"] = selected_account.account_id_qc
            st.session_state["_last_selected_display"] = selected_display
            
            # 显示更新提示
            st.sidebar.success(f"✅ 账户已更新: {selected_account.name} ({selected_account.account_id_qc})")
            
            logger.info(f"账户选择更新: {update_reason}")
            
        except Exception as e:
            st.sidebar.error(f"❌ 账户更新失败: {e}")
            logger.error(f"账户选择更新失败: {e}")
    
    # 4. 验证状态同步（增强版）
    consistency_check = verify_selection_consistency()
    
    # 如果状态不一致，尝试修复
    if not consistency_check['overall']:
        try:
            # 强制重新设置全局状态
            set_global_selected_account(selected_account)
            st.session_state["_last_selected_account_id"] = selected_account.account_id_qc
            
            # 重新验证
            consistency_check = verify_selection_consistency()
            
            if consistency_check['overall']:
                logger.info("状态不一致已自动修复")
            else:
                logger.warning("状态不一致修复失败，可能需要页面刷新")
                
        except Exception as e:
            logger.error(f"状态修复失败: {e}")
    
    # 5. 调试信息显示
    debug_mode = st.session_state.get("debug_account_selection", False)
    
    if debug_mode:
        current_selected = get_global_selected_account()
        if current_selected:
            st.sidebar.success(f"✅ 当前选中: {current_selected.name}")
            
            # 显示详细的状态验证信息
            last_id = st.session_state.get("_last_selected_account_id", "未知")
            current_id = current_selected.account_id_qc
            
            # 状态一致性指示器
            if consistency_check['overall']:
                st.sidebar.success("🔧 状态: ✅ 完全同步")
            else:
                st.sidebar.warning("🔧 状态: ⚠️ 部分不同步")
                
                # 详细的不一致信息
                if not consistency_check['display_match']:
                    st.sidebar.warning("  - 显示值不匹配")
                if not consistency_check['global_exists']:
                    st.sidebar.warning("  - 全局状态缺失")
                if not consistency_check['id_match']:
                    st.sidebar.warning("  - ID记录不匹配")
            
            # ID对比信息
            st.sidebar.info(f"🔧 当前ID: {current_id}")
            st.sidebar.info(f"🔧 记录ID: {last_id}")
            
            # 选择器状态
            selectbox_value = st.session_state.get("global_account_selector", "未知")
            st.sidebar.info(f"🔧 选择器: {selectbox_value[:30]}...")
            
            # 一致性检查详情
            st.sidebar.info(f"🔧 一致性: 显示{'✅' if consistency_check['display_match'] else '❌'} "
                          f"全局{'✅' if consistency_check['global_exists'] else '❌'} "
                          f"ID{'✅' if consistency_check['id_match'] else '❌'}")
            
        else:
            st.sidebar.warning("⚠️ 无当前选中账户")
    else:
        # 非调试模式下也始终显示当前账户状态
        current_selected = get_global_selected_account()
        if current_selected:
            is_favorite = getattr(current_selected, 'is_favorite', False)
            star = "⭐ " if is_favorite else ""
            
            if consistency_check['overall']:
                st.sidebar.success(f"✅ {star}{current_selected.name}")
            else:
                st.sidebar.warning(f"⚠️ {star}{current_selected.name} (状态异常)")
        else:
            st.sidebar.warning("⚠️ 请选择一个广告账户")
    
    # 6. 检测到严重不一致时的处理
    if not consistency_check['overall'] and not debug_mode:
        # 在非调试模式下，如果检测到严重的状态不一致，显示警告
        st.sidebar.warning("⚠️ 检测到状态异常，建议启用调试模式查看详情")
        
        # 提供快速修复按钮
        if st.sidebar.button("🔄 修复状态"):
            set_global_selected_account(selected_account)
            st.session_state["_last_selected_account_id"] = selected_account.account_id_qc
            st.rerun()
    
    return selected_account
'''
    
    return enhanced_code

def apply_enhanced_fix():
    """应用增强修复到全局账户选择器"""
    
    selector_path = project_root / 'ai_tools' / 'maintenance' / 'ai_tool_20250718_maintenance_global_account_selector.py'
    
    print("🔧 应用增强版账户选择修复...")
    
    try:
        # 读取当前文件
        with open(selector_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生成增强代码
        enhanced_code = create_enhanced_account_selector()
        
        # 查找并替换 render_global_account_selector 函数
        import re

        # 匹配函数定义到下一个函数或文件结尾
        pattern = r'def render_global_account_selector\(\):.*?(?=\ndef \w+|\nclass \w+|\Z)'
        
        if re.search(pattern, content, re.DOTALL):
            # 替换现有函数
            new_content = re.sub(pattern, enhanced_code.strip(), content, flags=re.DOTALL)
            
            # 写入文件
            with open(selector_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 增强修复应用成功")
            return True
        else:
            print("❌ 未找到 render_global_account_selector 函数")
            return False
            
    except Exception as e:
        print(f"❌ 应用修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 账户选择增强修复工具")
    print("=" * 50)
    print("基于 MCP Playwright 浏览器调试结果的修复方案")
    print()
    
    print("📋 修复内容:")
    print("  ✅ 多重状态验证机制")
    print("  ✅ 增强的一致性检查")
    print("  ✅ 自动状态修复功能")
    print("  ✅ 详细的调试信息")
    print("  ✅ 快速修复按钮")
    print("  ✅ 状态异常检测")
    print()
    
    # 应用修复
    if apply_enhanced_fix():
        print("🎉 增强修复应用成功！")
        print()
        print("💡 主要改进:")
        print("  - 解决了 Streamlit 组件状态更新竞争条件")
        print("  - 添加了多重状态验证机制")
        print("  - 实现了自动状态修复功能")
        print("  - 增强了调试信息显示")
        print("  - 提供了用户友好的修复界面")
        print()
        print("🧪 测试建议:")
        print("  1. 重启 Streamlit 应用")
        print("  2. 启用调试模式测试")
        print("  3. 快速连续切换账户")
        print("  4. 观察状态一致性指示器")
        print("  5. 测试自动修复功能")
        
        return True
    else:
        print("❌ 修复应用失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
