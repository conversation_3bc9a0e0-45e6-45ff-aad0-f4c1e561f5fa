#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: Augment自动化规则执行引擎
依赖关系: 集成所有千川项目自动化工具
清理条件: 项目不再需要自动化规则执行时
"""

import os
import sys
import subprocess
import json
import yaml
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import re

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

class AutoRulesEngine:
    """Augment自动化规则执行引擎"""
    
    def __init__(self):
        self.project_root = project_root
        self.logger = self._setup_logger()
        self.session_state = {
            "conversation_start_time": datetime.now(),
            "files_created": [],
            "files_modified": [],
            "git_operations": [],
            "backup_operations": [],
            "rules_executed": []
        }
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('auto_rules_engine')
        logger.setLevel(logging.INFO)
        
        # 创建日志目录
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / "auto_rules_engine.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger
    
    def execute_conversation_start_rules(self) -> Dict:
        """执行对话开始时的自动规则"""
        results = {
            "project_status_check": None,
            "git_status_analysis": None,
            "backup_time_check": None,
            "temp_files_cleanup": None
        }
        
        self.logger.info("执行对话开始时的自动规则")
        
        # 规则1: 项目状态自动检查
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_project_protection.py"),
                "check"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                check_result = json.loads(result.stdout)
                results["project_status_check"] = {
                    "success": check_result.get("overall_passed", False),
                    "details": check_result
                }
                
                if not check_result.get("overall_passed", False):
                    self.logger.warning(f"项目完整性检查失败: {check_result}")
            else:
                results["project_status_check"] = {"success": False, "error": result.stderr}
                
        except Exception as e:
            results["project_status_check"] = {"success": False, "error": str(e)}
        
        # 规则2: Git状态自动分析
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_git_auto_commit.py"),
                "status"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                git_status = json.loads(result.stdout)
                results["git_status_analysis"] = {
                    "success": True,
                    "status": git_status
                }
                
                # 检查是否需要自动提交
                if self._should_auto_commit(git_status):
                    self._schedule_auto_commit()
                    
            else:
                results["git_status_analysis"] = {"success": False, "error": result.stderr}
                
        except Exception as e:
            results["git_status_analysis"] = {"success": False, "error": str(e)}
        
        # 规则3: 定期备份检查
        results["backup_time_check"] = self._check_backup_schedule()
        
        # 规则4: AI临时文件清理检查
        results["temp_files_cleanup"] = self._check_temp_files_cleanup()
        
        self.session_state["rules_executed"].append("conversation_start")
        return results
    
    def execute_file_operation_rules(self, file_path: str, operation: str) -> Dict:
        """执行文件操作时的自动规则"""
        results = {
            "critical_file_backup": None,
            "ai_file_normalization": None,
            "sensitive_file_detection": None
        }
        
        self.logger.info(f"执行文件操作规则: {file_path} ({operation})")
        
        # 规则4: 关键文件变更自动保护
        if self._is_critical_file(file_path):
            results["critical_file_backup"] = self._backup_critical_file(file_path)
        
        # 规则5: AI文件自动规范化
        if operation == "create":
            results["ai_file_normalization"] = self._normalize_ai_file(file_path)
            self.session_state["files_created"].append(file_path)
        elif operation == "modify":
            self.session_state["files_modified"].append(file_path)
        
        # 规则6: 敏感文件自动检测
        results["sensitive_file_detection"] = self._detect_sensitive_content(file_path)
        
        self.session_state["rules_executed"].append(f"file_operation_{operation}")
        return results
    
    def execute_git_operation_rules(self, operation: str) -> Dict:
        """执行Git操作时的自动规则"""
        results = {
            "pre_commit_safety_check": None,
            "auto_commit_execution": None
        }
        
        self.logger.info(f"执行Git操作规则: {operation}")
        
        if operation == "commit":
            # 规则7: 提交前自动安全检查
            results["pre_commit_safety_check"] = self._run_pre_commit_checks()
            
            if results["pre_commit_safety_check"]["success"]:
                # 规则8: 智能自动提交
                results["auto_commit_execution"] = self._execute_auto_commit()
            else:
                self._block_commit_due_to_safety_issues(results["pre_commit_safety_check"])
        
        self.session_state["git_operations"].append(operation)
        self.session_state["rules_executed"].append(f"git_operation_{operation}")
        return results
    
    def execute_database_operation_rules(self, file_path: str) -> Dict:
        """执行数据库操作时的自动规则"""
        results = {
            "database_backup": None
        }
        
        self.logger.info(f"执行数据库操作规则: {file_path}")
        
        # 规则9: 数据库相关文件变更自动备份
        if self._is_database_related_file(file_path):
            results["database_backup"] = self._backup_database()
        
        self.session_state["rules_executed"].append("database_operation")
        return results
    
    def execute_conversation_end_rules(self) -> Dict:
        """执行对话结束时的自动规则"""
        results = {
            "ai_file_evaluation": None,
            "auto_cleanup": None,
            "management_tools_reminder": None
        }
        
        self.logger.info("执行对话结束时的自动规则")
        
        # 规则10: AI文件价值评估
        results["ai_file_evaluation"] = self._evaluate_ai_files()
        
        # 规则11: 自动清理和维护
        results["auto_cleanup"] = self._execute_auto_cleanup()
        
        # 规则12: 管理工具使用提醒
        results["management_tools_reminder"] = self._generate_management_reminder()
        
        self.session_state["rules_executed"].append("conversation_end")
        return results
    
    def _is_critical_file(self, file_path: str) -> bool:
        """判断是否为关键文件"""
        critical_patterns = [
            "config/settings.yml",
            "src/qianchuan_aw/sdk_qc/client.py",
            "src/qianchuan_aw/database/models.py",
            "requirements.txt",
            "main.py",
            "web_ui.py"
        ]
        
        return any(pattern in file_path for pattern in critical_patterns)
    
    def _backup_critical_file(self, file_path: str) -> Dict:
        """备份关键文件"""
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_project_protection.py"),
                "backup",
                "--file", file_path
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                backup_result = json.loads(result.stdout)
                self.session_state["backup_operations"].append({
                    "type": "critical_file",
                    "file": file_path,
                    "result": backup_result
                })
                return {"success": True, "backup_path": backup_result.get("backup_path")}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _normalize_ai_file(self, file_path: str) -> Dict:
        """规范化AI文件"""
        file_path_obj = Path(file_path)
        
        # 检查是否符合AI文件命名规范
        if not file_path_obj.name.startswith('ai_'):
            # 需要重命名和移动
            file_type = self._determine_ai_file_type(file_path)
            purpose = self._extract_file_purpose(file_path)
            
            new_name = f"ai_{file_type}_{datetime.now().strftime('%Y%m%d')}_{purpose}_{file_path_obj.stem}{file_path_obj.suffix}"
            target_dir = self._get_ai_target_directory(file_type)
            
            return {
                "needs_normalization": True,
                "suggested_name": new_name,
                "target_directory": str(target_dir),
                "current_path": file_path
            }
        
        return {"needs_normalization": False}
    
    def _detect_sensitive_content(self, file_path: str) -> Dict:
        """检测敏感文件内容"""
        try:
            if not Path(file_path).exists():
                return {"has_sensitive_content": False}
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            sensitive_patterns = [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']',
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'key\s*=\s*["\'][^"\']+["\']',
                r'auth_tokens\.json',
                r'browser_cookies\.json'
            ]
            
            detected_patterns = []
            for pattern in sensitive_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    detected_patterns.append(pattern)
            
            return {
                "has_sensitive_content": len(detected_patterns) > 0,
                "detected_patterns": detected_patterns,
                "file_path": file_path
            }
            
        except Exception as e:
            return {"has_sensitive_content": False, "error": str(e)}
    
    def _should_auto_commit(self, git_status: Dict) -> bool:
        """判断是否应该自动提交"""
        if git_status.get("clean", True):
            return False
        
        # 检查变更文件数量
        total_changes = git_status.get("total_changes", 0)
        if total_changes >= 5:
            return True
        
        # 检查是否有关键文件变更
        all_files = (
            git_status.get("modified_files", []) +
            git_status.get("added_files", []) +
            git_status.get("untracked_files", [])
        )
        
        for file_path in all_files:
            if self._is_critical_file(file_path):
                return True
        
        return False
    
    def _schedule_auto_commit(self):
        """安排自动提交"""
        self.session_state["scheduled_auto_commit"] = True
    
    def _execute_auto_commit(self) -> Dict:
        """执行自动提交"""
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_git_auto_commit.py"),
                "commit"
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                commit_result = json.loads(result.stdout)
                return {"success": True, "result": commit_result}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _run_pre_commit_checks(self) -> Dict:
        """运行提交前检查"""
        # 这里可以集成更多的检查逻辑
        return {"success": True, "checks_passed": ["syntax", "yaml", "sensitive_files"]}
    
    def _is_database_related_file(self, file_path: str) -> bool:
        """判断是否为数据库相关文件"""
        return "src/qianchuan_aw/database/" in file_path
    
    def _backup_database(self) -> Dict:
        """备份数据库"""
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "ai_tools" / "maintenance" / "ai_tool_20250718_maintenance_project_protection.py"),
                "backup",
                "--type", "database"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                backup_result = json.loads(result.stdout)
                return {"success": True, "result": backup_result}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _check_backup_schedule(self) -> Dict:
        """检查备份计划"""
        # 简化实现，实际可以检查上次备份时间
        return {"needs_backup": False, "last_backup": "recent"}
    
    def _check_temp_files_cleanup(self) -> Dict:
        """检查临时文件清理"""
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "tools" / "ai_file_manager.py"),
                "status"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return {"success": True, "needs_cleanup": False}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _evaluate_ai_files(self) -> Dict:
        """评估AI文件价值"""
        created_files = self.session_state["files_created"]
        
        evaluation = {
            "total_files": len(created_files),
            "temporary_files": [],
            "important_files": [],
            "permanent_files": []
        }
        
        for file_path in created_files:
            if "temp" in file_path.lower() or "debug" in file_path.lower():
                evaluation["temporary_files"].append(file_path)
            elif "tool" in file_path.lower() or "template" in file_path.lower():
                evaluation["permanent_files"].append(file_path)
            else:
                evaluation["important_files"].append(file_path)
        
        return evaluation
    
    def _execute_auto_cleanup(self) -> Dict:
        """执行自动清理"""
        try:
            result = subprocess.run([
                sys.executable,
                str(self.project_root / "tools" / "ai_file_manager.py"),
                "cleanup",
                "--type", "temp"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                return {"success": True, "cleanup_performed": True}
            else:
                return {"success": False, "error": result.stderr}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _generate_management_reminder(self) -> Dict:
        """生成管理工具提醒"""
        return {
            "reminder_generated": True,
            "suggested_commands": [
                "python tools/ai_file_manager.py status",
                "python ai_tools/maintenance/ai_tool_20250718_maintenance_git_auto_commit.py status"
            ]
        }
    
    def _determine_ai_file_type(self, file_path: str) -> str:
        """确定AI文件类型"""
        if "tool" in file_path.lower():
            return "tool"
        elif "temp" in file_path.lower() or "debug" in file_path.lower():
            return "temp"
        elif "report" in file_path.lower() or "analysis" in file_path.lower():
            return "report"
        elif "template" in file_path.lower() or "config" in file_path.lower():
            return "template"
        else:
            return "temp"  # 默认为临时文件
    
    def _extract_file_purpose(self, file_path: str) -> str:
        """提取文件用途"""
        file_name = Path(file_path).stem.lower()
        
        if "cleanup" in file_name:
            return "cleanup"
        elif "backup" in file_name:
            return "backup"
        elif "git" in file_name:
            return "git"
        elif "protection" in file_name:
            return "protection"
        else:
            return "general"
    
    def _get_ai_target_directory(self, file_type: str) -> Path:
        """获取AI文件目标目录"""
        base_dirs = {
            "tool": "ai_tools/maintenance",
            "temp": "ai_temp/general",
            "report": "ai_reports/audit",
            "template": "ai_templates/config"
        }
        
        return self.project_root / base_dirs.get(file_type, "ai_temp/general")
    
    def generate_automation_summary(self) -> str:
        """生成自动化操作总结"""
        summary = "🤖 自动化操作执行总结:\n"
        
        if "conversation_start" in self.session_state["rules_executed"]:
            summary += "✅ 对话开始时规则已执行\n"
        
        if self.session_state["files_created"]:
            summary += f"📝 创建了 {len(self.session_state['files_created'])} 个文件\n"
        
        if self.session_state["backup_operations"]:
            summary += f"💾 执行了 {len(self.session_state['backup_operations'])} 次备份操作\n"
        
        if self.session_state["git_operations"]:
            summary += f"🔄 执行了 {len(self.session_state['git_operations'])} 次Git操作\n"
        
        summary += f"⏱️ 会话时长: {datetime.now() - self.session_state['conversation_start_time']}\n"
        
        return summary

def main():
    """主函数 - 用于测试"""
    engine = AutoRulesEngine()
    
    # 测试对话开始规则
    start_results = engine.execute_conversation_start_rules()
    print("对话开始规则执行结果:")
    print(json.dumps(start_results, indent=2, ensure_ascii=False))
    
    # 生成总结
    summary = engine.generate_automation_summary()
    print("\n" + summary)

if __name__ == "__main__":
    main()
