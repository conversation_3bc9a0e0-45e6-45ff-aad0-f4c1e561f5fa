#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账户权限管理器 - 统一版本
确保所有工作流使用一致的账户状态权限检查
"""

from typing import Dict, Any, List
from enum import Enum
from qianchuan_aw.database.models import AdAccount
from qianchuan_aw.utils.logger import logger


class AccountOperation(Enum):
    """账户操作类型枚举"""
    UPLOAD = "upload"
    CREATE_PLAN = "create_plan"
    SUBMIT_PLAN = "submit_plan"
    HARVEST = "harvest"
    MONITOR = "monitor"
    SYNC = "sync"


class AccountPermissionManager:
    """账户权限管理器 - 铁律3统一实施"""
    
    def __init__(self):
        # 定义账户状态与操作权限的映射关系
        self.permission_matrix = {
            'active': {
                AccountOperation.UPLOAD: True,
                AccountOperation.CREATE_PLAN: True,
                AccountOperation.SUBMIT_PLAN: True,
                AccountOperation.HARVEST: True,
                AccountOperation.MONITOR: True,
                AccountOperation.SYNC: True,
            },
            'temporarily_blocked': {
                AccountOperation.UPLOAD: False,        # 禁止新建操作
                AccountOperation.CREATE_PLAN: False,   # 禁止新建操作
                AccountOperation.SUBMIT_PLAN: True,    # 允许已有操作
                AccountOperation.HARVEST: True,        # 允许已有操作
                AccountOperation.MONITOR: True,        # 允许监控
                AccountOperation.SYNC: True,           # 允许同步
            },
            'deleted': {
                AccountOperation.UPLOAD: False,        # 严禁任何操作
                AccountOperation.CREATE_PLAN: False,   # 严禁任何操作
                AccountOperation.SUBMIT_PLAN: False,   # 严禁任何操作
                AccountOperation.HARVEST: False,       # 严禁任何操作
                AccountOperation.MONITOR: False,       # 严禁任何操作
                AccountOperation.SYNC: False,          # 严禁任何操作
            }
        }
    
    def check_operation_permission(self, account: AdAccount, operation: AccountOperation) -> Dict[str, Any]:
        """检查账户操作权限"""
        
        account_status = account.status.lower()
        
        # 检查账户状态是否在权限矩阵中
        if account_status not in self.permission_matrix:
            return {
                'is_allowed': False,
                'reason': 'unknown_account_status',
                'message': f'未知的账户状态: {account_status}',
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
        
        # 获取权限
        is_allowed = self.permission_matrix[account_status].get(operation, False)
        
        if is_allowed:
            return {
                'is_allowed': True,
                'reason': 'permission_granted',
                'message': f'{account_status}账户允许{operation.value}操作',
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
        else:
            violation_rule = self._get_violation_rule(account_status, operation)
            return {
                'is_allowed': False,
                'reason': 'permission_denied',
                'message': f'{account_status}账户禁止{operation.value}操作',
                'violation_rule': violation_rule,
                'account_id': account.account_id_qc,
                'account_name': account.name
            }
    
    def _get_violation_rule(self, account_status: str, operation: AccountOperation) -> str:
        """获取违规的业务铁律"""
        
        if account_status == 'deleted':
            return '铁律3：deleted账户严禁任何操作'
        elif account_status == 'temporarily_blocked':
            if operation in [AccountOperation.UPLOAD, AccountOperation.CREATE_PLAN]:
                return '铁律3：temporarily_blocked账户禁止新建操作'
        
        return '铁律3：账户状态操作权限约束'
    
    def get_allowed_operations(self, account: AdAccount) -> List[AccountOperation]:
        """获取账户允许的操作列表"""
        
        account_status = account.status.lower()
        
        if account_status not in self.permission_matrix:
            return []
        
        allowed_operations = []
        for operation, is_allowed in self.permission_matrix[account_status].items():
            if is_allowed:
                allowed_operations.append(operation)
        
        return allowed_operations
    
    def validate_batch_operations(self, accounts: List[AdAccount], operation: AccountOperation) -> Dict[str, Any]:
        """批量验证账户操作权限"""
        
        results = {
            'total_accounts': len(accounts),
            'allowed_accounts': [],
            'denied_accounts': [],
            'unknown_status_accounts': []
        }
        
        for account in accounts:
            permission_result = self.check_operation_permission(account, operation)
            
            if permission_result['is_allowed']:
                results['allowed_accounts'].append({
                    'account_id': account.account_id_qc,
                    'account_name': account.name,
                    'status': account.status
                })
            else:
                denied_info = {
                    'account_id': account.account_id_qc,
                    'account_name': account.name,
                    'status': account.status,
                    'reason': permission_result['reason'],
                    'violation_rule': permission_result.get('violation_rule')
                }
                
                if permission_result['reason'] == 'unknown_account_status':
                    results['unknown_status_accounts'].append(denied_info)
                else:
                    results['denied_accounts'].append(denied_info)
        
        results['allowed_count'] = len(results['allowed_accounts'])
        results['denied_count'] = len(results['denied_accounts'])
        results['unknown_count'] = len(results['unknown_status_accounts'])
        
        return results
    
    def get_account_type_permissions(self, account_type: str) -> Dict[str, Any]:
        """获取账户类型的权限说明"""
        
        type_permissions = {
            'TEST': {
                'description': '测试账户 - 专门用于测试工作流',
                'allowed_operations': ['upload', 'create_plan', 'submit_plan', 'harvest', 'monitor'],
                'restrictions': ['仅限测试素材', '素材唯一性约束'],
                'business_rules': ['铁律2：测试视频全局唯一性']
            },
            'DELIVERY': {
                'description': '投放账户 - 正式投放工作流',
                'allowed_operations': ['upload', 'create_plan', 'submit_plan', 'harvest', 'monitor'],
                'restrictions': ['正式投放素材', '投放量限制'],
                'business_rules': ['铁律12：计划创建数量限制']
            },
            'UNSET': {
                'description': 'API同步账户 - 仅用于数据同步',
                'allowed_operations': ['sync', 'monitor'],
                'restrictions': ['不参与工作流', '仅数据同步'],
                'business_rules': ['铁律1：账户类型分离']
            }
        }
        
        return type_permissions.get(account_type, {
            'description': '未知账户类型',
            'allowed_operations': [],
            'restrictions': ['禁止所有操作'],
            'business_rules': ['铁律1：账户类型必须明确']
        })
    
    def validate_account_type_operation(self, account: AdAccount, operation: AccountOperation) -> Dict[str, Any]:
        """验证账户类型是否允许特定操作"""
        
        # 首先检查账户状态权限
        status_permission = self.check_operation_permission(account, operation)
        if not status_permission['is_allowed']:
            return status_permission
        
        # 然后检查账户类型权限
        account_type = account.account_type
        type_permissions = self.get_account_type_permissions(account_type)
        
        if operation.value not in type_permissions['allowed_operations']:
            return {
                'is_allowed': False,
                'reason': 'account_type_restriction',
                'message': f'{account_type}账户类型不允许{operation.value}操作',
                'violation_rule': f'铁律1：{account_type}账户类型操作限制',
                'account_id': account.account_id_qc,
                'account_name': account.name,
                'account_type': account_type
            }
        
        return {
            'is_allowed': True,
            'reason': 'type_and_status_allowed',
            'message': f'{account_type}账户({account.status})允许{operation.value}操作',
            'account_id': account.account_id_qc,
            'account_name': account.name,
            'account_type': account_type
        }
    
    def get_permission_summary(self, account: AdAccount) -> Dict[str, Any]:
        """获取账户权限摘要"""
        
        allowed_operations = self.get_allowed_operations(account)
        type_permissions = self.get_account_type_permissions(account.account_type)
        
        return {
            'account_id': account.account_id_qc,
            'account_name': account.name,
            'account_type': account.account_type,
            'account_status': account.status,
            'allowed_operations': [op.value for op in allowed_operations],
            'type_description': type_permissions['description'],
            'type_restrictions': type_permissions['restrictions'],
            'applicable_business_rules': type_permissions['business_rules']
        }
