{"timestamp": "2025-07-20T12:31:54.412578", "config_validation": {"required_sections": {"status": "PASS", "details": "所有必需配置节都存在", "error": "", "timestamp": "2025-07-20T12:31:54.439177"}, "database_config": {"status": "PASS", "details": "数据库配置完整", "error": "", "timestamp": "2025-07-20T12:31:54.440176"}, "api_credentials": {"status": "PASS", "details": "API凭据配置完整", "error": "", "timestamp": "2025-07-20T12:31:54.440176"}}, "dependency_check": {"critical_packages": {"status": "PASS", "details": "所有关键包已安装: ['celery', 'redis', 'sqlalchemy', 'psycopg2-binary', 'playwright', 'streamlit', 'pyyaml', 'requests']", "error": "", "timestamp": "2025-07-20T12:31:55.164101"}}, "database_connectivity": {"redis_connection": {"status": "PASS", "details": "Redis连接成功: localhost:6379", "error": "", "timestamp": "2025-07-20T12:31:55.328909"}, "postgresql_connection": {"status": "FAIL", "details": "", "error": "PostgreSQL连接失败: Textual SQL expression 'SELECT 1' should be explicitly declared as text('SELECT 1')", "timestamp": "2025-07-20T12:31:55.328909"}}, "core_functionality": {"config_manager": {"status": "PASS", "details": "配置管理器工作正常", "error": "", "timestamp": "2025-07-20T12:31:55.328909"}, "fault_tolerance_service": {"status": "FAIL", "details": "", "error": "容错服务测试失败: FaultToleranceService.__init__() missing 1 required positional argument: 'execute_sql_func'", "timestamp": "2025-07-20T12:31:55.335423"}, "celery_app": {"status": "PASS", "details": "Celery应用初始化成功", "error": "", "timestamp": "2025-07-20T12:31:55.419496"}, "celery_tasks": {"status": "WARN", "details": "部分任务未注册: ['tasks.ingest_and_upload', 'tasks.group_and_dispatch', 'tasks.create_plans', 'tasks.appeal_plans']", "error": "", "timestamp": "2025-07-20T12:31:55.426034"}}, "workflow_readiness": {"assets_directory": {"status": "PASS", "details": "工作流资产目录存在: G:/workflow_assets", "error": "", "timestamp": "2025-07-20T12:31:55.426034"}, "workflow_subdirs": {"status": "WARN", "details": "缺少工作流子目录: ['02_materials_in_testing', '04_materials_in_production', '05_manual_promotion', '06_materials_rejected', '07_materials_cleaned']", "error": "", "timestamp": "2025-07-20T12:31:55.427557"}, "source_directories": {"status": "PASS", "details": "所有源目录可访问: 5", "error": "", "timestamp": "2025-07-20T12:31:55.435709"}, "run_celery_worker.py_exists": {"status": "PASS", "details": "启动脚本存在: run_celery_worker.py", "error": "", "timestamp": "2025-07-20T12:31:55.436706"}, "run_celery_beat.py_exists": {"status": "PASS", "details": "启动脚本存在: run_celery_beat.py", "error": "", "timestamp": "2025-07-20T12:31:55.436706"}, "startup_guide": {"status": "PASS", "details": "启动指南已生成: D:\\Project\\qianchuangzl\\ai_tools\\startup_guide.md", "error": "", "timestamp": "2025-07-20T12:31:55.437708"}}, "recommendations": ["检查PostgreSQL服务和数据库配置", "创建缺少的工作流目录: ['02_materials_in_testing', '04_materials_in_production', '05_manual_promotion', '06_materials_rejected', '07_materials_cleaned']"]}