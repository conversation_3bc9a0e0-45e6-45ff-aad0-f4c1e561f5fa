
-- 千川自动化批量合规性修复SQL脚本
-- 生成时间: 2025-07-27 18:36:20
-- 目的: 修复所有素材重复使用违规问题

-- 第一步：查找所有违规素材和需要删除的计划
WITH violation_analysis AS (
    SELECT 
        lc.id as material_id,
        lc.filename,
        lc.file_hash,
        COUNT(DISTINCT c.id) as plan_count,
        MIN(c.id) as keep_plan_id,
        ARRAY_AGG(DISTINCT c.id ORDER BY c.created_at DESC) as all_plan_ids
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.id, lc.filename, lc.file_hash
    HAVING COUNT(DISTINCT c.id) > 1
),
plans_to_delete AS (
    SELECT 
        material_id,
        filename,
        keep_plan_id,
        UNNEST(all_plan_ids[2:]) as delete_plan_id
    FROM violation_analysis
)

-- 第二步：删除关联表记录
DELETE FROM campaign_platform_creative_association 
WHERE campaign_id IN (
    SELECT delete_plan_id FROM plans_to_delete
);

-- 第三步：删除重复计划
DELETE FROM campaigns 
WHERE id IN (
    SELECT delete_plan_id FROM plans_to_delete
);

-- 第四步：更新素材状态
UPDATE local_creatives 
SET status = 'already_tested'
WHERE id IN (
    SELECT DISTINCT material_id FROM violation_analysis
);

-- 第五步：验证修复结果
SELECT 
    '修复验证' as check_type,
    COUNT(*) as remaining_violations
FROM (
    SELECT lc.id
    FROM local_creatives lc
    JOIN platform_creatives pc ON lc.id = pc.local_creative_id
    JOIN campaign_platform_creative_association cpca ON pc.id = cpca.platform_creative_id
    JOIN campaigns c ON cpca.campaign_id = c.id
    JOIN ad_accounts aa ON c.account_id = aa.id
    WHERE aa.account_type = 'TEST'
    GROUP BY lc.id
    HAVING COUNT(DISTINCT c.id) > 1
) remaining_violations;
