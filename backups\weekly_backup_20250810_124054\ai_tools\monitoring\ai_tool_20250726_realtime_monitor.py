#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 千川工作流实时监控
清理条件: 监控任务完成后删除

千川工作流实时监控器
==================
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


def execute_sql_query(query):
    """执行SQL查询"""
    try:
        # 使用psql命令执行查询
        cmd = [
            'psql', 
            '-h', 'localhost',
            '-p', '5432', 
            '-U', 'postgres',
            '-d', 'qianchuan_aw',
            '-t',  # 只输出数据，不输出表头
            '-c', query
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            logger.error(f"SQL查询失败: {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"执行SQL查询异常: {e}")
        return None


def get_current_status():
    """获取当前系统状态"""
    try:
        # 查询素材状态
        materials_query = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN status = MaterialStatus.NEW.value THEN 1 END) as new_status,
            COUNT(CASE WHEN status = MaterialStatus.PENDING_UPLOAD.value THEN 1 END) as pending_upload,
            COUNT(CASE WHEN status = MaterialStatus.UPLOADED_PENDING_PLAN.value THEN 1 END) as uploaded_pending_plan,
            COUNT(CASE WHEN status = MaterialStatus.ALREADY_TESTED.value THEN 1 END) as already_tested,
            COUNT(CASE WHEN status = MaterialStatus.REJECTED.value THEN 1 END) as rejected
        FROM local_creatives;
        """
        
        materials_result = execute_sql_query(materials_query)
        
        # 查询计划状态
        campaigns_query = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN appeal_status = 'appeal_pending' THEN 1 END) as appeal_pending,
            COUNT(CASE WHEN appeal_status = 'appeal_submitted' THEN 1 END) as appeal_submitted,
            COUNT(CASE WHEN appeal_status = 'appeal_approved' THEN 1 END) as appeal_approved,
            COUNT(CASE WHEN appeal_status = 'appeal_rejected' THEN 1 END) as appeal_rejected
        FROM campaigns;
        """
        
        campaigns_result = execute_sql_query(campaigns_query)
        
        return {
            'materials_raw': materials_result,
            'campaigns_raw': campaigns_result,
            'timestamp': datetime.now()
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return None


def parse_status_result(result_line):
    """解析状态查询结果"""
    if not result_line:
        return {}
    
    try:
        # 假设结果是用|分隔的数字
        parts = [p.strip() for p in result_line.split('|')]
        return {
            'total': int(parts[0]) if len(parts) > 0 else 0,
            'col1': int(parts[1]) if len(parts) > 1 else 0,
            'col2': int(parts[2]) if len(parts) > 2 else 0,
            'col3': int(parts[3]) if len(parts) > 3 else 0,
            'col4': int(parts[4]) if len(parts) > 4 else 0,
            'col5': int(parts[5]) if len(parts) > 5 else 0,
        }
    except:
        return {'raw': result_line}


def monitor_workflow():
    """监控工作流"""
    logger.info("🚀 开始千川工作流实时监控...")
    
    # 建立基线
    baseline = get_current_status()
    if not baseline:
        logger.error("❌ 无法建立监控基线")
        return
    
    logger.info("📊 监控基线已建立")
    logger.info(f"   素材数据: {baseline['materials_raw']}")
    logger.info(f"   计划数据: {baseline['campaigns_raw']}")
    
    monitoring_count = 0
    
    try:
        while monitoring_count < 120:  # 监控1小时 (120次 * 30秒)
            monitoring_count += 1
            elapsed_minutes = (monitoring_count * 30) // 60
            elapsed_seconds = (monitoring_count * 30) % 60
            
            logger.info(f"🔍 监控检查 #{monitoring_count} (已运行 {elapsed_minutes}:{elapsed_seconds:02d})")
            
            # 获取当前状态
            current = get_current_status()
            if not current:
                logger.warning("⚠️ 无法获取当前状态，跳过本次检查")
                time.sleep(30)
                continue
            
            # 比较状态变化
            if current['materials_raw'] != baseline['materials_raw']:
                logger.success(f"📈 素材状态变化: {current['materials_raw']}")
            
            if current['campaigns_raw'] != baseline['campaigns_raw']:
                logger.success(f"📈 计划状态变化: {current['campaigns_raw']}")
            
            # 每10次检查输出详细状态
            if monitoring_count % 10 == 0:
                logger.info("=" * 60)
                logger.info(f"📊 第 {monitoring_count//10} 轮详细状态:")
                logger.info(f"   当前时间: {current['timestamp'].strftime('%H:%M:%S')}")
                logger.info(f"   素材状态: {current['materials_raw']}")
                logger.info(f"   计划状态: {current['campaigns_raw']}")
                logger.info("=" * 60)
            
            # 更新基线
            baseline = current
            
            # 等待30秒
            time.sleep(30)
        
        logger.info("✅ 监控完成")
        
    except KeyboardInterrupt:
        logger.warning("⚠️ 监控被用户中断")
    except Exception as e:
        logger.error(f"❌ 监控过程中发生错误: {e}")


if __name__ == '__main__':
    monitor_workflow()
