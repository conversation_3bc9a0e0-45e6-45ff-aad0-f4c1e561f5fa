#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 封禁账户工作流保护机制
清理条件: 成为账户保护工具，长期保留
"""

import os
import sys

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class BlockedAccountWorkflowProtector:
    """封禁账户工作流保护器"""
    
    def run_protection_analysis(self):
        """运行保护分析"""
        logger.info("🛡️ 封禁账户工作流保护分析")
        logger.info("="*100)
        
        # 1. 分析封禁账户的计划状态
        blocked_account_analysis = self._analyze_blocked_account_plans()
        
        # 2. 评估工作流风险
        workflow_risks = self._assess_workflow_risks(blocked_account_analysis)
        
        # 3. 提供保护建议
        protection_recommendations = self._generate_protection_recommendations(workflow_risks)
        
        # 4. 生成保护报告
        self._generate_protection_report({
            'blocked_account_analysis': blocked_account_analysis,
            'workflow_risks': workflow_risks,
            'protection_recommendations': protection_recommendations
        })
    
    def _analyze_blocked_account_plans(self):
        """分析封禁账户的计划状态"""
        logger.info("📊 分析封禁账户的计划状态...")
        
        try:
            from qianchuan_aw.database.database import SessionLocal
            from qianchuan_aw.database.models import Campaign, AdAccount, Principal
            
            with SessionLocal() as db:
                # 查找封禁账户
                blocked_accounts = db.query(AdAccount).join(Principal).filter(
                    Principal.name == '缇萃百货',
                    AdAccount.status == 'temporarily_blocked'
                ).all()
                
                analysis_result = {}
                
                for account in blocked_accounts:
                    logger.info(f"\n🔍 分析封禁账户: {account.name}")
                    logger.info(f"   封禁到期: {account.blocked_until}")
                    
                    # 查询该账户的计划分布
                    plan_status_distribution = db.query(
                        Campaign.status,
                        Campaign.appeal_status,
                        db.func.count(Campaign.id).label('count')
                    ).filter(
                        Campaign.account_id == account.id,
                        Campaign.created_at >= db.func.now() - db.text("INTERVAL '7 days'")
                    ).group_by(Campaign.status, Campaign.appeal_status).all()
                    
                    # 统计各状态计划数量
                    status_summary = {}
                    total_plans = 0
                    
                    for status, appeal_status, count in plan_status_distribution:
                        key = f"{status}_{appeal_status}" if appeal_status else status
                        status_summary[key] = count
                        total_plans += count
                        logger.info(f"   {status} ({appeal_status}): {count} 个")
                    
                    # 评估风险等级
                    risk_level = self._assess_account_risk_level(status_summary, total_plans)
                    
                    analysis_result[account.name] = {
                        'account_id': account.id,
                        'blocked_until': account.blocked_until,
                        'total_plans': total_plans,
                        'status_summary': status_summary,
                        'risk_level': risk_level
                    }
                    
                    logger.info(f"   风险等级: {risk_level}")
                
                return analysis_result
                
        except Exception as e:
            logger.error(f"❌ 分析封禁账户计划状态失败: {e}")
            return {}
    
    def _assess_account_risk_level(self, status_summary, total_plans):
        """评估账户风险等级"""
        if total_plans == 0:
            return "LOW"
        
        # 检查是否有大量活跃计划
        monitoring_count = status_summary.get('MONITORING_appeal_pending', 0)
        completed_count = status_summary.get('COMPLETED_None', 0) + status_summary.get('COMPLETED_appeal_pending', 0)
        
        if monitoring_count > 20:
            return "HIGH"
        elif monitoring_count > 10:
            return "MEDIUM"
        elif completed_count > 0:
            return "LOW"
        else:
            return "MEDIUM"
    
    def _assess_workflow_risks(self, blocked_account_analysis):
        """评估工作流风险"""
        logger.info("\n🔍 评估工作流风险...")
        
        risks = []
        
        for account_name, analysis in blocked_account_analysis.items():
            risk_level = analysis['risk_level']
            total_plans = analysis['total_plans']
            
            if risk_level == "HIGH":
                risks.append({
                    'severity': 'HIGH',
                    'account': account_name,
                    'issue': f'封禁账户有 {total_plans} 个活跃计划',
                    'impact': 'API调用可能受限，影响提审和收割成功率',
                    'recommendation': '监控API调用成功率，考虑暂停部分操作'
                })
            elif risk_level == "MEDIUM":
                risks.append({
                    'severity': 'MEDIUM',
                    'account': account_name,
                    'issue': f'封禁账户有 {total_plans} 个计划需要处理',
                    'impact': '可能影响处理效率',
                    'recommendation': '密切监控处理结果'
                })
        
        logger.info(f"识别到 {len(risks)} 个工作流风险")
        
        return risks
    
    def _generate_protection_recommendations(self, workflow_risks):
        """生成保护建议"""
        logger.info("\n💡 生成保护建议...")
        
        recommendations = []
        
        # 基于风险生成建议
        high_risk_count = sum(1 for risk in workflow_risks if risk['severity'] == 'HIGH')
        
        if high_risk_count > 0:
            recommendations.append({
                'priority': 'IMMEDIATE',
                'action': '实施封禁账户API调用监控',
                'details': '监控封禁账户的API调用成功率，失败率超过50%时暂停操作'
            })
            
            recommendations.append({
                'priority': 'IMMEDIATE',
                'action': '添加封禁账户工作流检查',
                'details': '在提审和收割前检查账户状态，封禁账户降低操作频率'
            })
        
        # 通用建议
        recommendations.append({
            'priority': 'HIGH',
            'action': '修复batch_upload_videos的负载均衡',
            'details': '将.first()改为轮询机制，避免集中分配事故'
        })
        
        recommendations.append({
            'priority': 'MEDIUM',
            'action': '实施账户状态实时监控',
            'details': '监控账户状态变化，及时调整工作流策略'
        })
        
        return recommendations
    
    def _generate_protection_report(self, results):
        """生成保护报告"""
        logger.info("\n📋 封禁账户工作流保护报告")
        logger.info("="*100)
        
        blocked_analysis = results['blocked_account_analysis']
        risks = results['workflow_risks']
        recommendations = results['protection_recommendations']
        
        # 封禁账户分析
        logger.info(f"📊 封禁账户分析: 发现 {len(blocked_analysis)} 个封禁账户")
        
        for account_name, analysis in blocked_analysis.items():
            logger.info(f"   🚫 {account_name}:")
            logger.info(f"      计划总数: {analysis['total_plans']}")
            logger.info(f"      风险等级: {analysis['risk_level']}")
            logger.info(f"      封禁到期: {analysis['blocked_until']}")
        
        # 风险评估
        logger.info(f"\n🔍 工作流风险: 发现 {len(risks)} 个风险点")
        
        for risk in risks:
            severity_icon = {'HIGH': '🚨', 'MEDIUM': '⚠️', 'LOW': '💡'}[risk['severity']]
            logger.info(f"   {severity_icon} {risk['severity']}: {risk['issue']}")
            logger.info(f"      影响: {risk['impact']}")
            logger.info(f"      建议: {risk['recommendation']}")
        
        # 保护建议
        logger.info(f"\n💡 保护建议: {len(recommendations)} 项行动建议")
        
        for rec in recommendations:
            priority_icon = {'IMMEDIATE': '🚨', 'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[rec['priority']]
            logger.info(f"   {priority_icon} {rec['priority']}: {rec['action']}")
            logger.info(f"      详情: {rec['details']}")
        
        # 总体评估
        if len(risks) == 0:
            logger.success("🎊 工作流保护状态良好")
        elif any(risk['severity'] == 'HIGH' for risk in risks):
            logger.error("❌ 发现高风险，需要立即处理")
        else:
            logger.warning("⚠️ 发现中等风险，建议关注")


def main():
    """主函数"""
    protector = BlockedAccountWorkflowProtector()
    
    logger.info("🚀 启动封禁账户工作流保护分析")
    logger.info("🎯 目标：评估封禁账户对工作流的影响")
    
    protector.run_protection_analysis()
    
    logger.success("🎊 封禁账户工作流保护分析完成")
    return 0


if __name__ == "__main__":
    exit(main())
