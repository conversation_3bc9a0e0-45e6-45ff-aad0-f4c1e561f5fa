#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 千川自动化项目主部署脚本，协调所有迁移步骤的执行
清理条件: 项目不再需要部署迁移时可删除
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import shutil

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

class QianchuanDeploymentMaster:
    """千川自动化项目主部署管理器"""
    
    def __init__(self, project_root: Path = None):
        self.project_root = project_root or Path(__file__).parent.parent.parent
        self.deployment_log = []
        self.deployment_status = {}
        self.report_dir = self.project_root / "ai_reports" / "deployment"
        self.report_dir.mkdir(parents=True, exist_ok=True)
        
    def log_step(self, step: str, status: str, details: str = ""):
        """记录部署步骤"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "step": step,
            "status": status,
            "details": details
        }
        self.deployment_log.append(log_entry)
        self.deployment_status[step] = status
        
        status_icon = "✅" if status == "成功" else "❌" if status == "失败" else "🔄"
        print(f"{status_icon} {step}: {status}")
        if details:
            print(f"   详情: {details}")
    
    def run_analyzer(self) -> bool:
        """运行部署分析器"""
        print("\n📊 步骤1: 运行部署分析器")
        print("-" * 40)
        
        try:
            analyzer_script = self.project_root / "ai_tools" / "deployment" / "ai_tool_20250725_deployment_analyzer.py"
            
            if not analyzer_script.exists():
                self.log_step("部署分析", "失败", "分析器脚本不存在")
                return False
            
            result = subprocess.run([sys.executable, str(analyzer_script)], 
                                  capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.log_step("部署分析", "成功", "项目分析完成")
                return True
            else:
                self.log_step("部署分析", "失败", f"分析器执行失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_step("部署分析", "失败", f"分析器运行异常: {e}")
            return False
    
    def run_config_migration(self) -> bool:
        """运行配置迁移"""
        print("\n⚙️ 步骤2: 运行配置迁移")
        print("-" * 40)
        
        try:
            migrator_script = self.project_root / "ai_tools" / "deployment" / "ai_tool_20250725_deployment_config_migrator.py"
            
            if not migrator_script.exists():
                self.log_step("配置迁移", "失败", "配置迁移脚本不存在")
                return False
            
            print("请按照提示完成配置迁移...")
            result = subprocess.run([sys.executable, str(migrator_script)], 
                                  cwd=self.project_root)
            
            if result.returncode == 0:
                self.log_step("配置迁移", "成功", "配置文件迁移完成")
                return True
            else:
                self.log_step("配置迁移", "失败", "配置迁移失败")
                return False
                
        except Exception as e:
            self.log_step("配置迁移", "失败", f"配置迁移异常: {e}")
            return False
    
    def run_database_migration(self) -> bool:
        """运行数据库迁移"""
        print("\n🗄️ 步骤3: 运行数据库迁移")
        print("-" * 40)
        
        try:
            db_migrator_script = self.project_root / "ai_tools" / "deployment" / "ai_tool_20250725_deployment_database_migrator.py"
            
            if not db_migrator_script.exists():
                self.log_step("数据库迁移", "失败", "数据库迁移脚本不存在")
                return False
            
            print("请按照提示完成数据库迁移...")
            result = subprocess.run([sys.executable, str(db_migrator_script)], 
                                  cwd=self.project_root)
            
            if result.returncode == 0:
                self.log_step("数据库迁移", "成功", "数据库迁移完成")
                return True
            else:
                self.log_step("数据库迁移", "失败", "数据库迁移失败")
                return False
                
        except Exception as e:
            self.log_step("数据库迁移", "失败", f"数据库迁移异常: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装Python依赖"""
        print("\n📦 步骤4: 安装Python依赖")
        print("-" * 40)
        
        try:
            requirements_file = self.project_root / "requirements.txt"
            
            if not requirements_file.exists():
                self.log_step("依赖安装", "失败", "requirements.txt文件不存在")
                return False
            
            print("正在安装Python依赖包...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_step("依赖安装", "成功", "Python依赖包安装完成")
                return True
            else:
                self.log_step("依赖安装", "失败", f"依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_step("依赖安装", "失败", f"依赖安装异常: {e}")
            return False
    
    def install_playwright_browsers(self) -> bool:
        """安装Playwright浏览器"""
        print("\n🌐 步骤5: 安装Playwright浏览器")
        print("-" * 40)
        
        try:
            print("正在安装Playwright浏览器...")
            result = subprocess.run([sys.executable, "-m", "playwright", "install", "chromium"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_step("浏览器安装", "成功", "Playwright浏览器安装完成")
                return True
            else:
                self.log_step("浏览器安装", "失败", f"浏览器安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.log_step("浏览器安装", "失败", f"浏览器安装异常: {e}")
            return False
    
    def run_validation(self) -> bool:
        """运行部署验证"""
        print("\n🔍 步骤6: 运行部署验证")
        print("-" * 40)
        
        try:
            validator_script = self.project_root / "ai_tools" / "deployment" / "ai_tool_20250725_deployment_validator.py"
            
            if not validator_script.exists():
                self.log_step("部署验证", "失败", "验证脚本不存在")
                return False
            
            result = subprocess.run([sys.executable, str(validator_script)], 
                                  capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.log_step("部署验证", "成功", "部署验证通过")
                return True
            else:
                self.log_step("部署验证", "部分通过", f"验证发现问题，请查看详细报告")
                print(result.stdout)
                return True  # 部分通过也算成功
                
        except Exception as e:
            self.log_step("部署验证", "失败", f"验证运行异常: {e}")
            return False
    
    def generate_deployment_summary(self) -> Dict[str, Any]:
        """生成部署总结报告"""
        summary = {
            "deployment_time": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "deployment_log": self.deployment_log,
            "deployment_status": self.deployment_status,
            "overall_success": all(status in ["成功", "部分通过"] for status in self.deployment_status.values()),
            "failed_steps": [step for step, status in self.deployment_status.items() if status == "失败"],
            "next_steps": []
        }
        
        # 生成下一步建议
        if summary["overall_success"]:
            summary["next_steps"] = [
                "检查配置文件是否正确",
                "启动Redis服务",
                "测试Web界面: streamlit run web_ui.py",
                "启动Celery服务: python run_celery_worker.py",
                "启动Celery调度器: python run_celery_beat.py",
                "验证核心功能是否正常"
            ]
        else:
            summary["next_steps"] = [
                "解决失败的部署步骤",
                "检查错误日志",
                "重新运行部署脚本",
                "联系技术支持"
            ]
        
        return summary
    
    def save_deployment_report(self, summary: Dict[str, Any]) -> str:
        """保存部署报告"""
        report_file = self.report_dir / f"deployment_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        return str(report_file)
    
    def run_full_deployment(self) -> bool:
        """运行完整部署流程"""
        print("🚀 千川自动化项目完整部署流程")
        print("=" * 60)
        print(f"项目根目录: {self.project_root}")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 部署步骤
        steps = [
            ("项目分析", self.run_analyzer),
            ("配置迁移", self.run_config_migration),
            ("数据库迁移", self.run_database_migration),
            ("依赖安装", self.install_dependencies),
            ("浏览器安装", self.install_playwright_browsers),
            ("部署验证", self.run_validation)
        ]
        
        success_count = 0
        
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
                else:
                    print(f"\n⚠️ 步骤 '{step_name}' 失败，但继续执行后续步骤...")
            except KeyboardInterrupt:
                print(f"\n❌ 用户中断部署流程")
                self.log_step("部署流程", "中断", "用户手动中断")
                break
            except Exception as e:
                print(f"\n❌ 步骤 '{step_name}' 执行异常: {e}")
                self.log_step(step_name, "异常", str(e))
        
        # 生成总结报告
        summary = self.generate_deployment_summary()
        report_file = self.save_deployment_report(summary)
        
        print("\n" + "=" * 60)
        print("📋 部署流程总结")
        print("=" * 60)
        print(f"成功步骤: {success_count}/{len(steps)}")
        print(f"总体状态: {'成功' if summary['overall_success'] else '失败'}")
        
        if summary["failed_steps"]:
            print(f"失败步骤: {', '.join(summary['failed_steps'])}")
        
        print(f"详细报告: {report_file}")
        
        print("\n📝 下一步操作:")
        for i, step in enumerate(summary["next_steps"], 1):
            print(f"{i}. {step}")
        
        return summary["overall_success"]

def main():
    """主函数"""
    print("欢迎使用千川自动化项目部署工具!")
    print("\n⚠️ 重要提醒:")
    print("1. 请确保在项目根目录运行此脚本")
    print("2. 建议在部署前备份重要数据")
    print("3. 确保网络连接正常")
    print("4. 准备好数据库连接信息")
    
    confirm = input("\n是否继续部署? (y/n): ").strip().lower()
    if confirm != 'y':
        print("部署已取消")
        return False
    
    master = QianchuanDeploymentMaster()
    success = master.run_full_deployment()
    
    if success:
        print("\n🎉 部署流程完成!")
        print("请按照上述下一步操作继续配置系统")
    else:
        print("\n❌ 部署流程存在问题")
        print("请查看详细报告并解决问题后重新部署")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
