#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
素材唯一性检查器 - 增强版本
确保严格执行测试视频工作流铁律
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount, PlatformCreative


class MaterialUniquenessChecker:
    """素材唯一性检查器 - 确保测试视频全局唯一性"""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    def check_test_video_global_uniqueness(self, file_hash: str, account_type: str) -> Dict[str, Any]:
        """检查测试视频全局唯一性 - 铁律2实施"""
        
        if account_type != 'TEST':
            return {'is_unique': True, 'reason': 'non_test_account'}
        
        try:
            # 查询是否已存在相同file_hash的测试计划
            existing_test_plans = self.db.execute(text("""
                SELECT 
                    c.campaign_id_qc,
                    aa.name as account_name,
                    lc.filename,
                    c.created_at
                FROM campaigns c
                JOIN ad_accounts aa ON c.account_id = aa.id
                JOIN campaign_platform_creative_association cpca ON c.id = cpca.campaign_id
                JOIN platform_creatives pc ON cpca.platform_creative_id = pc.id
                JOIN local_creatives lc ON pc.local_creative_id = lc.id
                WHERE aa.account_type = 'TEST'
                AND lc.file_hash = :file_hash
                LIMIT 1
            """), {'file_hash': file_hash}).fetchone()
            
            if existing_test_plans:
                return {
                    'is_unique': False,
                    'reason': 'duplicate_test_video',
                    'existing_plan': {
                        'campaign_id': existing_test_plans.campaign_id_qc,
                        'account_name': existing_test_plans.account_name,
                        'filename': existing_test_plans.filename,
                        'created_at': existing_test_plans.created_at.isoformat()
                    },
                    'violation_rule': '铁律2：素材唯一性测试约束'
                }
            
            return {'is_unique': True, 'reason': 'no_duplicate_found'}
            
        except Exception as e:
            logger.error(f"唯一性检查失败: {e}")
            # 安全起见，检查失败时返回不唯一，阻止创建
            return {
                'is_unique': False,
                'reason': 'check_failed',
                'error': str(e)
            }
    
    def validate_account_operation_permissions(self, account: AdAccount, operation: str) -> Dict[str, Any]:
        """验证账户操作权限 - 铁律3实施"""
        
        # 严禁deleted账户的任何操作
        if account.status == 'deleted':
            return {
                'is_allowed': False,
                'reason': 'deleted_account_forbidden',
                'violation_rule': '铁律3：账户状态操作权限 - deleted账户严禁任何操作'
            }
        
        # temporarily_blocked账户的操作限制
        if account.status == 'temporarily_blocked':
            forbidden_operations = ['upload', 'create_plan']
            allowed_operations = ['submit_plan', 'harvest']
            
            if operation in forbidden_operations:
                return {
                    'is_allowed': False,
                    'reason': 'temporarily_blocked_operation_forbidden',
                    'violation_rule': f'铁律3：temporarily_blocked账户禁止{operation}操作'
                }
            elif operation in allowed_operations:
                return {
                    'is_allowed': True,
                    'reason': 'temporarily_blocked_operation_allowed'
                }
        
        # active账户允许所有操作
        if account.status == 'active':
            return {
                'is_allowed': True,
                'reason': 'active_account_full_permission'
            }
        
        # 未知状态，安全起见拒绝
        return {
            'is_allowed': False,
            'reason': 'unknown_account_status',
            'account_status': account.status
        }
