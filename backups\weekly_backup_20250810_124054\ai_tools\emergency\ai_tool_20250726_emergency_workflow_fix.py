#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 紧急修复千川工作流停滞问题
清理条件: 工作流恢复正常后删除

千川工作流紧急修复脚本
====================

修复内容：
1. 重启Celery服务
2. 清理调度文件
3. 验证修复效果
4. 提供回滚方案
"""

import os
import sys
import time
import subprocess
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from src.qianchuan_aw.utils.logger import logger


class WorkflowEmergencyFix:
    """工作流紧急修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_files = []
        
    def backup_config(self):
        """备份关键配置文件"""
        logger.info("🔄 备份关键配置文件...")
        
        config_files = [
            'config/settings.yml',
            'src/qianchuan_aw/workflows/scheduler.py'
        ]
        
        for config_file in config_files:
            source_path = os.path.join(self.project_root, config_file)
            backup_path = f"{source_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            if os.path.exists(source_path):
                try:
                    import shutil
                    shutil.copy2(source_path, backup_path)
                    self.backup_files.append(backup_path)
                    logger.success(f"✅ 已备份: {config_file} -> {backup_path}")
                except Exception as e:
                    logger.error(f"❌ 备份失败: {config_file} - {e}")
    
    def clean_celery_schedule(self):
        """清理Celery调度文件"""
        logger.info("🧹 清理Celery调度文件...")
        
        schedule_files = [
            'celerybeat-schedule.db',
            'logs/celerybeat-schedule.db',
            'celerybeat-schedule.db.bak',
            'celerybeat-schedule.db.dat',
            'celerybeat-schedule.db.dir'
        ]
        
        for schedule_file in schedule_files:
            file_path = os.path.join(self.project_root, schedule_file)
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.success(f"✅ 已删除调度文件: {schedule_file}")
                except Exception as e:
                    logger.warning(f"⚠️ 删除调度文件失败: {schedule_file} - {e}")
    
    def check_celery_processes(self):
        """检查Celery进程状态"""
        logger.info("🔍 检查Celery进程状态...")
        
        try:
            # 检查Python进程中是否有Celery
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe', '/FO', 'TABLE'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                python_processes = result.stdout.count('python.exe')
                logger.info(f"📊 发现 {python_processes} 个Python进程")
                
                if 'celery' in result.stdout.lower():
                    logger.warning("⚠️ 发现Celery进程正在运行")
                else:
                    logger.info("ℹ️ 未发现Celery进程")
            
        except Exception as e:
            logger.warning(f"⚠️ 检查进程失败: {e}")
    
    def restart_celery_services(self):
        """重启Celery服务"""
        logger.info("🔄 准备重启Celery服务...")
        
        # 提示用户手动重启
        logger.warning("⚠️ 请手动执行以下步骤重启Celery服务：")
        logger.info("1. 停止当前的Celery Worker (Ctrl+C)")
        logger.info("2. 停止当前的Celery Beat (Ctrl+C)")
        logger.info("3. 等待5秒")
        logger.info("4. 重新运行: python run_celery_beat.py")
        logger.info("5. 重新运行: python run_celery_worker.py")
        
        return True
    
    def verify_fix(self):
        """验证修复效果"""
        logger.info("🔍 验证修复效果...")
        
        # 这里应该调用MCP查询验证
        logger.info("请在重启Celery后运行以下SQL查询验证：")
        logger.info("""
        -- 检查待创建计划数量是否减少
        SELECT COUNT(*) as uploaded_pending_plan 
        FROM local_creatives 
        WHERE status = MaterialStatus.UPLOADED_PENDING_PLAN.value;
        
        -- 检查是否有新计划创建
        SELECT COUNT(*) as new_plans 
        FROM campaigns 
        WHERE created_at > NOW() - INTERVAL '10 minutes';
        
        -- 检查申诉是否开始
        SELECT COUNT(*) as appeals_started 
        FROM campaigns 
        WHERE appeal_started_at IS NOT NULL;
        """)
    
    def generate_rollback_script(self):
        """生成回滚脚本"""
        rollback_script = f"""#!/usr/bin/env python3
# 工作流修复回滚脚本
# 生成时间: {datetime.now()}

import os
import shutil

project_root = r"{self.project_root}"
backup_files = {self.backup_files}

def rollback():
    print("🔄 开始回滚...")
    
    for backup_file in backup_files:
        if os.path.exists(backup_file):
            original_file = backup_file.split('.backup_')[0]
            try:
                shutil.copy2(backup_file, original_file)
                print(f"✅ 已回滚: {{original_file}}")
            except Exception as e:
                print(f"❌ 回滚失败: {{original_file}} - {{e}}")
    
    print("🎯 回滚完成，请重启Celery服务")

if __name__ == '__main__':
    rollback()
"""
        
        rollback_file = os.path.join(self.project_root, 'ai_tools', 'emergency', 
                                   f'rollback_workflow_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py')
        
        with open(rollback_file, 'w', encoding='utf-8') as f:
            f.write(rollback_script)
        
        logger.success(f"✅ 回滚脚本已生成: {rollback_file}")
        return rollback_file
    
    def run_emergency_fix(self):
        """执行紧急修复"""
        logger.info("🚨 开始千川工作流紧急修复...")
        
        try:
            # 1. 备份配置
            self.backup_config()
            
            # 2. 检查进程状态
            self.check_celery_processes()
            
            # 3. 清理调度文件
            self.clean_celery_schedule()
            
            # 4. 生成回滚脚本
            rollback_script = self.generate_rollback_script()
            
            # 5. 重启服务指导
            self.restart_celery_services()
            
            # 6. 验证指导
            self.verify_fix()
            
            logger.success("🎯 紧急修复准备完成！")
            logger.info(f"📋 回滚脚本: {rollback_script}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 紧急修复失败: {e}")
            return False


def main():
    """主函数"""
    fixer = WorkflowEmergencyFix()
    
    print("🚨 千川工作流紧急修复工具")
    print("=" * 50)
    print("修复内容:")
    print("1. 修复申诉任务状态查找错误")
    print("2. 降低计划创建素材数量要求")
    print("3. 清理Celery调度文件")
    print("4. 提供重启指导")
    print("=" * 50)
    
    success = fixer.run_emergency_fix()
    
    if success:
        print("\n🎉 紧急修复准备完成！")
        print("\n📋 下一步操作：")
        print("1. 手动重启Celery Worker和Beat")
        print("2. 监控系统状态变化")
        print("3. 验证修复效果")
    else:
        print("\n❌ 紧急修复失败，请检查日志")
    
    return 0 if success else 1


if __name__ == '__main__':
    exit(main())
