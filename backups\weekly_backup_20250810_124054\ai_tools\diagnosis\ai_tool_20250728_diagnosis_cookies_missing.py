#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 诊断cookies文件缺失问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_cookies_file_existence():
    """检查cookies文件是否存在"""
    logger.info("🔍 检查cookies文件状态...")
    
    # 检查可能的cookies文件位置
    possible_locations = [
        project_root / 'config' / 'cookies.json',
        project_root / 'cookies.json',
        project_root / 'config' / 'cookies',
        project_root / 'data' / 'cookies.json',
        project_root / 'src' / 'config' / 'cookies.json'
    ]
    
    found_files = []
    
    for location in possible_locations:
        if location.exists():
            stat = location.stat()
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            found_files.append({
                'path': location,
                'size': size,
                'modified': modified
            })
            logger.success(f"✅ 找到cookies文件: {location}")
            logger.info(f"   📊 大小: {size} 字节")
            logger.info(f"   📅 修改时间: {modified}")
        else:
            logger.warning(f"❌ 文件不存在: {location}")
    
    if not found_files:
        logger.error("❌ 未找到任何cookies文件")
        return False, []
    
    return True, found_files

def check_cookies_content():
    """检查cookies文件内容"""
    logger.info("🔍 检查cookies文件内容...")
    
    cookies_file = project_root / 'config' / 'cookies.json'
    
    if not cookies_file.exists():
        logger.error(f"❌ cookies文件不存在: {cookies_file}")
        return False, None
    
    try:
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logger.success("✅ cookies文件格式正确")
        
        # 检查cookies结构
        if isinstance(cookies_data, dict):
            logger.info(f"📊 cookies包含 {len(cookies_data)} 个主体")
            
            for principal_name, cookies in cookies_data.items():
                if isinstance(cookies, list):
                    logger.info(f"   🔹 {principal_name}: {len(cookies)} 个cookie")
                else:
                    logger.warning(f"   ⚠️ {principal_name}: 格式异常")
        else:
            logger.warning("⚠️ cookies文件格式不是预期的字典格式")
        
        return True, cookies_data
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ cookies文件JSON格式错误: {e}")
        return False, None
    except Exception as e:
        logger.error(f"❌ 读取cookies文件失败: {e}")
        return False, None

def check_principal_cookies():
    """检查特定主体的cookies"""
    logger.info("🔍 检查主体cookies...")
    
    try:
        # 查询数据库中的主体信息
        import psycopg2
        import yaml
        
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 查询所有主体
        cursor.execute("SELECT name FROM principals ORDER BY name")
        principals = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"📊 数据库中的主体: {len(principals)} 个")
        for principal in principals:
            logger.info(f"   🔹 {principal}")
        
        cursor.close()
        conn.close()
        
        # 检查cookies文件中是否包含这些主体
        cookies_exists, cookies_data = check_cookies_content()
        
        if cookies_exists and cookies_data:
            missing_principals = []
            for principal in principals:
                if principal not in cookies_data:
                    missing_principals.append(principal)
                    logger.warning(f"⚠️ 主体 '{principal}' 在cookies文件中缺失")
                else:
                    logger.success(f"✅ 主体 '{principal}' 在cookies文件中存在")
            
            if missing_principals:
                logger.error(f"❌ {len(missing_principals)} 个主体缺少cookies")
                return False, missing_principals
            else:
                logger.success("✅ 所有主体都有cookies")
                return True, []
        else:
            logger.error("❌ 无法检查主体cookies")
            return False, principals
        
    except Exception as e:
        logger.error(f"❌ 检查主体cookies失败: {e}")
        return False, []

def check_recent_cookies_usage():
    """检查最近的cookies使用情况"""
    logger.info("🔍 检查最近的cookies使用情况...")
    
    try:
        # 查找日志文件
        log_dir = project_root / 'logs'
        
        if not log_dir.exists():
            logger.warning("⚠️ 日志目录不存在")
            return False
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob('*.log'))
        if not log_files:
            logger.warning("⚠️ 未找到日志文件")
            return False
        
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        logger.info(f"📄 检查日志文件: {latest_log}")
        
        # 读取最近的日志内容
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找cookies相关的日志
        cookies_logs = []
        for line in lines[-1000:]:  # 只检查最后1000行
            if any(keyword in line for keyword in ['cookies', 'Cookies', 'cookie']):
                cookies_logs.append(line.strip())
        
        logger.info(f"📊 找到 {len(cookies_logs)} 条cookies相关日志")
        
        if cookies_logs:
            logger.info("📋 最近的cookies相关日志:")
            for log_line in cookies_logs[-10:]:  # 只显示最后10条
                if 'Cookies文件不存在' in log_line:
                    logger.error(f"   🚨 {log_line}")
                elif 'cookies' in log_line.lower():
                    logger.info(f"   📋 {log_line}")
        
        # 检查是否有cookies文件不存在的错误
        missing_errors = [line for line in cookies_logs if 'Cookies文件不存在' in line]
        
        if missing_errors:
            logger.error(f"❌ 发现 {len(missing_errors)} 个cookies文件缺失错误")
            return False
        else:
            logger.success("✅ 未发现cookies文件缺失错误")
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查cookies使用情况失败: {e}")
        return False

def check_cookies_backup():
    """检查是否有cookies备份文件"""
    logger.info("🔍 检查cookies备份文件...")
    
    backup_patterns = [
        'cookies*.json',
        'cookies*.backup',
        '*.cookies',
        'backup*cookies*'
    ]
    
    backup_files = []
    
    # 在多个目录中搜索
    search_dirs = [
        project_root / 'config',
        project_root,
        project_root / 'backup',
        project_root / 'data'
    ]
    
    for search_dir in search_dirs:
        if search_dir.exists():
            for pattern in backup_patterns:
                for backup_file in search_dir.glob(pattern):
                    if backup_file.is_file():
                        stat = backup_file.stat()
                        backup_files.append({
                            'path': backup_file,
                            'size': stat.st_size,
                            'modified': datetime.fromtimestamp(stat.st_mtime)
                        })
    
    if backup_files:
        logger.success(f"✅ 找到 {len(backup_files)} 个可能的cookies备份文件:")
        for backup in backup_files:
            logger.info(f"   📋 {backup['path']}")
            logger.info(f"      📊 大小: {backup['size']} 字节")
            logger.info(f"      📅 修改时间: {backup['modified']}")
        return True, backup_files
    else:
        logger.warning("⚠️ 未找到cookies备份文件")
        return False, []

def suggest_cookies_recovery():
    """建议cookies恢复方案"""
    logger.info("💡 建议cookies恢复方案...")
    
    # 检查备份文件
    has_backup, backup_files = check_cookies_backup()
    
    if has_backup:
        logger.info("🔧 恢复方案1: 从备份文件恢复")
        latest_backup = max(backup_files, key=lambda x: x['modified'])
        logger.info(f"   📋 推荐使用: {latest_backup['path']}")
        logger.info(f"   🔄 恢复命令: cp '{latest_backup['path']}' 'config/cookies.json'")
    
    logger.info("🔧 恢复方案2: 重新获取cookies")
    logger.info("   1. 手动登录千川平台")
    logger.info("   2. 使用浏览器开发者工具导出cookies")
    logger.info("   3. 保存到 config/cookies.json")
    
    logger.info("🔧 恢复方案3: 使用现有登录会话")
    logger.info("   1. 检查是否有活跃的浏览器会话")
    logger.info("   2. 从活跃会话中提取cookies")
    logger.info("   3. 更新cookies文件")

def run_cookies_diagnosis():
    """运行cookies诊断"""
    logger.info("🚀 开始cookies文件诊断...")
    logger.info("="*60)
    
    diagnosis_results = {
        'cookies_file_exists': False,
        'cookies_content_valid': False,
        'principal_cookies_complete': False,
        'recent_usage_normal': False,
        'backup_files_available': False
    }
    
    issues_found = []
    recommendations = []
    
    # 1. 检查文件存在
    exists, found_files = check_cookies_file_existence()
    diagnosis_results['cookies_file_exists'] = exists
    if not exists:
        issues_found.append("cookies.json文件不存在")
        recommendations.append("创建或恢复cookies.json文件")
    
    # 2. 检查文件内容
    if exists:
        valid, cookies_data = check_cookies_content()
        diagnosis_results['cookies_content_valid'] = valid
        if not valid:
            issues_found.append("cookies文件格式错误")
            recommendations.append("修复cookies文件格式")
    
    # 3. 检查主体cookies
    complete, missing = check_principal_cookies()
    diagnosis_results['principal_cookies_complete'] = complete
    if not complete:
        issues_found.append(f"缺少主体cookies: {missing}")
        recommendations.append("为缺失的主体添加cookies")
    
    # 4. 检查使用情况
    usage_normal = check_recent_cookies_usage()
    diagnosis_results['recent_usage_normal'] = usage_normal
    
    # 5. 检查备份
    has_backup, backup_files = check_cookies_backup()
    diagnosis_results['backup_files_available'] = has_backup
    
    # 生成诊断报告
    logger.info("\n" + "="*60)
    logger.info("🎯 Cookies诊断结果")
    logger.info("="*60)
    
    for check_name, result in diagnosis_results.items():
        status = "✅" if result else "❌"
        check_display = check_name.replace('_', ' ').title()
        logger.info(f"{status} {check_display}")
    
    success_count = sum(diagnosis_results.values())
    total_count = len(diagnosis_results)
    success_rate = (success_count / total_count) * 100
    
    logger.info(f"\n📈 诊断通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    # 输出问题和建议
    if issues_found:
        logger.error("\n🚨 发现的问题:")
        for i, issue in enumerate(issues_found, 1):
            logger.error(f"   {i}. {issue}")
    
    if recommendations:
        logger.info("\n💡 修复建议:")
        for i, rec in enumerate(recommendations, 1):
            logger.info(f"   {i}. {rec}")
    
    # 提供恢复方案
    logger.info("\n🔧 恢复方案:")
    suggest_cookies_recovery()
    
    return diagnosis_results, issues_found, recommendations

def main():
    """主函数"""
    try:
        results, issues, recommendations = run_cookies_diagnosis()
        
        # 保存诊断报告
        report_file = project_root / 'ai_temp' / f'cookies_diagnosis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        report_file.parent.mkdir(exist_ok=True)
        
        import json
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'diagnosis_results': results,
            'issues_found': issues,
            'recommendations': recommendations
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📋 Cookies诊断报告已保存: {report_file}")
        
        # 返回成功率
        success_rate = sum(results.values()) / len(results) * 100
        return success_rate >= 50
        
    except Exception as e:
        logger.error(f"❌ 诊断过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
