#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 监控工具
生命周期: 永久保留
创建目的: 监控数据库连接池状态，防止连接耗尽问题
清理条件: 成为系统监控组件后可归档
"""

import sys
import time
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

class DatabaseConnectionMonitor:
    """数据库连接池监控器"""
    
    def __init__(self):
        self.project_root = project_root
        self.monitoring = False
        self.stats_history = []
        self.alert_threshold = 0.8  # 连接使用率告警阈值
        
    def get_connection_pool_stats(self) -> Dict:
        """获取数据库连接池统计信息"""
        try:
            from qianchuan_aw.database.database import engine
            
            pool = engine.pool
            
            # 获取连接池状态
            pool_size = pool.size()
            checked_out = pool.checkedout()
            checked_in = pool.checkedin()
            overflow = pool.overflow()
            invalid = pool.invalid()
            
            # 计算使用率
            total_connections = pool_size + overflow
            usage_rate = checked_out / total_connections if total_connections > 0 else 0
            
            stats = {
                'timestamp': datetime.now().isoformat(),
                'pool_size': pool_size,
                'max_overflow': getattr(pool, '_max_overflow', 0),
                'total_capacity': total_connections,
                'checked_out': checked_out,
                'checked_in': checked_in,
                'overflow': overflow,
                'invalid': invalid,
                'usage_rate': round(usage_rate, 3),
                'available': total_connections - checked_out,
                'status': self._get_pool_status(usage_rate)
            }
            
            return stats
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'status': 'ERROR'
            }
    
    def _get_pool_status(self, usage_rate: float) -> str:
        """根据使用率确定连接池状态"""
        if usage_rate >= 0.9:
            return 'CRITICAL'
        elif usage_rate >= self.alert_threshold:
            return 'WARNING'
        elif usage_rate >= 0.5:
            return 'BUSY'
        else:
            return 'HEALTHY'
    
    def check_connection_config(self) -> Dict:
        """检查数据库连接配置"""
        try:
            from qianchuan_aw.database.database import engine, settings
            
            pool_config = settings.get('database', {}).get('connection_pool', {})
            
            config_info = {
                'configured_pool_size': pool_config.get('pool_size', 'NOT_SET'),
                'configured_max_overflow': pool_config.get('max_overflow', 'NOT_SET'),
                'configured_pool_timeout': pool_config.get('pool_timeout', 'NOT_SET'),
                'configured_pool_recycle': pool_config.get('pool_recycle', 'NOT_SET'),
                'actual_pool_size': engine.pool.size(),
                'actual_max_overflow': getattr(engine.pool, '_max_overflow', 'UNKNOWN'),
                'database_type': settings.get('database', {}).get('type', 'UNKNOWN')
            }
            
            # 检查配置是否生效
            config_applied = (
                config_info['configured_pool_size'] == config_info['actual_pool_size'] and
                config_info['configured_max_overflow'] == config_info['actual_max_overflow']
            )
            
            config_info['config_applied'] = config_applied
            
            return config_info
            
        except Exception as e:
            return {'error': str(e)}
    
    def monitor_once(self) -> Dict:
        """执行一次监控检查"""
        stats = self.get_connection_pool_stats()
        
        if 'error' not in stats:
            # 记录历史数据
            self.stats_history.append(stats)
            
            # 只保留最近100条记录
            if len(self.stats_history) > 100:
                self.stats_history = self.stats_history[-100:]
            
            # 输出状态
            status_emoji = {
                'HEALTHY': '✅',
                'BUSY': '🟡',
                'WARNING': '⚠️',
                'CRITICAL': '🚨'
            }
            
            emoji = status_emoji.get(stats['status'], '❓')
            print(f"{emoji} [{datetime.now().strftime('%H:%M:%S')}] "
                  f"连接池状态: {stats['status']} | "
                  f"使用: {stats['checked_out']}/{stats['total_capacity']} "
                  f"({stats['usage_rate']*100:.1f}%) | "
                  f"可用: {stats['available']}")
            
            # 告警检查
            if stats['status'] in ['WARNING', 'CRITICAL']:
                print(f"  ⚠️ 连接池使用率过高: {stats['usage_rate']*100:.1f}%")
                if stats['available'] <= 2:
                    print(f"  🚨 可用连接不足: 仅剩 {stats['available']} 个")
        else:
            print(f"❌ [{datetime.now().strftime('%H:%M:%S')}] 监控失败: {stats['error']}")
        
        return stats
    
    def continuous_monitor(self, duration_minutes: int = 30, interval_seconds: int = 10):
        """持续监控数据库连接池"""
        print(f"🚀 开始监控数据库连接池 (持续 {duration_minutes} 分钟)")
        print(f"📊 监控间隔: {interval_seconds} 秒")
        print("="*80)
        
        self.monitoring = True
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        warning_count = 0
        critical_count = 0
        max_usage = 0
        
        try:
            while time.time() < end_time and self.monitoring:
                stats = self.monitor_once()
                
                if 'error' not in stats:
                    # 统计告警次数
                    if stats['status'] == 'WARNING':
                        warning_count += 1
                    elif stats['status'] == 'CRITICAL':
                        critical_count += 1
                    
                    # 记录最高使用率
                    if stats['usage_rate'] > max_usage:
                        max_usage = stats['usage_rate']
                
                time.sleep(interval_seconds)
                
        except KeyboardInterrupt:
            print("\n⏹️ 监控被用户中断")
        finally:
            self.monitoring = False
        
        # 监控总结
        print("\n" + "="*80)
        print("📊 监控总结:")
        print(f"   监控时长: {(time.time() - start_time) / 60:.1f} 分钟")
        print(f"   最高使用率: {max_usage*100:.1f}%")
        print(f"   警告次数: {warning_count}")
        print(f"   严重告警次数: {critical_count}")
        
        if critical_count > 0:
            print("🚨 发现严重连接池问题，建议立即检查系统负载")
        elif warning_count > 0:
            print("⚠️ 连接池使用率较高，建议优化并发设置")
        else:
            print("✅ 连接池状态良好")
    
    def generate_report(self) -> Dict:
        """生成连接池使用报告"""
        if not self.stats_history:
            return {'error': '没有监控数据'}
        
        # 计算统计信息
        usage_rates = [s['usage_rate'] for s in self.stats_history if 'usage_rate' in s]
        checked_out_counts = [s['checked_out'] for s in self.stats_history if 'checked_out' in s]
        
        if not usage_rates:
            return {'error': '没有有效的统计数据'}
        
        report = {
            'monitoring_period': {
                'start': self.stats_history[0]['timestamp'],
                'end': self.stats_history[-1]['timestamp'],
                'data_points': len(self.stats_history)
            },
            'usage_statistics': {
                'avg_usage_rate': round(sum(usage_rates) / len(usage_rates), 3),
                'max_usage_rate': round(max(usage_rates), 3),
                'min_usage_rate': round(min(usage_rates), 3),
                'avg_checked_out': round(sum(checked_out_counts) / len(checked_out_counts), 1),
                'max_checked_out': max(checked_out_counts)
            },
            'status_distribution': {},
            'recommendations': []
        }
        
        # 状态分布统计
        status_counts = {}
        for stats in self.stats_history:
            status = stats.get('status', 'UNKNOWN')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        report['status_distribution'] = status_counts
        
        # 生成建议
        if report['usage_statistics']['max_usage_rate'] >= 0.9:
            report['recommendations'].append("连接池使用率过高，建议增加pool_size或减少并发度")
        
        if status_counts.get('CRITICAL', 0) > 0:
            report['recommendations'].append("发现严重连接池问题，需要立即优化")
        
        if status_counts.get('WARNING', 0) > len(self.stats_history) * 0.2:
            report['recommendations'].append("警告状态频繁出现，建议调整连接池配置")
        
        return report
    
    def quick_check(self):
        """快速检查连接池状态"""
        print("🎯 数据库连接池快速检查")
        print("="*50)
        
        # 1. 检查配置
        print("📋 检查连接池配置...")
        config_info = self.check_connection_config()
        
        if 'error' not in config_info:
            print(f"  配置的连接池大小: {config_info['configured_pool_size']}")
            print(f"  配置的最大溢出: {config_info['configured_max_overflow']}")
            print(f"  实际连接池大小: {config_info['actual_pool_size']}")
            print(f"  实际最大溢出: {config_info['actual_max_overflow']}")
            print(f"  数据库类型: {config_info['database_type']}")
            
            if config_info['config_applied']:
                print("  ✅ 连接池配置已正确应用")
            else:
                print("  ❌ 连接池配置未正确应用")
        else:
            print(f"  ❌ 配置检查失败: {config_info['error']}")
        
        print()
        
        # 2. 检查当前状态
        print("📊 检查当前连接池状态...")
        stats = self.monitor_once()
        
        if 'error' not in stats:
            print(f"\n💡 建议:")
            if stats['status'] == 'HEALTHY':
                print("  ✅ 连接池状态良好，可以正常使用")
            elif stats['status'] == 'BUSY':
                print("  🟡 连接池较忙，建议监控使用情况")
            elif stats['status'] == 'WARNING':
                print("  ⚠️ 连接池使用率较高，建议减少并发或增加连接数")
            elif stats['status'] == 'CRITICAL':
                print("  🚨 连接池严重不足，需要立即处理")
                print("     - 立即停止高并发任务")
                print("     - 增加连接池大小")
                print("     - 检查是否有连接泄漏")
        
        return stats

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库连接池监控工具')
    parser.add_argument('--mode', choices=['quick', 'monitor'], default='quick',
                       help='运行模式: quick=快速检查, monitor=持续监控')
    parser.add_argument('--duration', type=int, default=30,
                       help='持续监控时长(分钟)')
    parser.add_argument('--interval', type=int, default=10,
                       help='监控间隔(秒)')
    
    args = parser.parse_args()
    
    monitor = DatabaseConnectionMonitor()
    
    if args.mode == 'quick':
        monitor.quick_check()
    elif args.mode == 'monitor':
        monitor.continuous_monitor(args.duration, args.interval)
        
        # 生成报告
        report = monitor.generate_report()
        if 'error' not in report:
            print(f"\n📄 监控报告:")
            print(f"  平均使用率: {report['usage_statistics']['avg_usage_rate']*100:.1f}%")
            print(f"  最高使用率: {report['usage_statistics']['max_usage_rate']*100:.1f}%")
            print(f"  平均连接数: {report['usage_statistics']['avg_checked_out']}")
            
            if report['recommendations']:
                print(f"\n💡 建议:")
                for rec in report['recommendations']:
                    print(f"  - {rec}")

if __name__ == "__main__":
    main()
