#!/usr/bin/env python3
"""
批量提审现有未提审计划脚本
"""

import sys
from pathlib import Path
from datetime import datetime

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.database.connection import database_session
from qianchuan_aw.database.models import Campaign, AdAccount
from qianchuan_aw.services.async_appeal_adapter import AsyncAppealAdapter
from qianchuan_aw.utils.logger import logger

def batch_submit_unsubmitted_plans():
    """批量提审未提审的计划"""
    logger.info("🚀 开始批量提审未提审的计划...")
    
    try:
        with database_session() as db:
            # 查找未提审的计划
            unsubmitted_plans = db.query(Campaign).join(
                AdAccount, Campaign.account_id == AdAccount.id
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status.is_(None),
                Campaign.first_appeal_at.is_(None)
            ).all()
            
            if not unsubmitted_plans:
                logger.info("✅ 没有发现未提审的计划")
                return
            
            logger.info(f"📊 发现 {len(unsubmitted_plans)} 个未提审的计划")
            
            adapter = AsyncAppealAdapter()
            success_count = 0
            
            for plan in unsubmitted_plans:
                try:
                    account = plan.account
                    principal = account.principal
                    
                    if not principal:
                        logger.warning(f"⚠️ 计划 {plan.campaign_id_qc} 没有关联主体，跳过")
                        continue
                    
                    logger.info(f"📤 提审计划: {plan.campaign_id_qc}")
                    
                    result = adapter.appeal_plan(
                        principal_name=principal.name,
                        account_id=account.account_id_qc,
                        plan_id=plan.campaign_id_qc
                    )
                    
                    if result.get('success'):
                        plan.appeal_status = 'appeal_pending'
                        plan.first_appeal_at = datetime.utcnow()
                        success_count += 1
                        logger.success(f"✅ 计划 {plan.campaign_id_qc} 提审成功")
                    else:
                        error_msg = result.get('error', '未知错误')
                        plan.appeal_status = 'submission_failed'
                        plan.appeal_error_message = error_msg
                        logger.error(f"❌ 计划 {plan.campaign_id_qc} 提审失败: {error_msg}")
                    
                    # 避免API限流
                    import time
                    time.sleep(3)
                    
                except Exception as e:
                    logger.error(f"❌ 处理计划 {plan.campaign_id_qc} 时发生错误: {e}")
                    plan.appeal_status = 'submission_error'
                    plan.appeal_error_message = str(e)
            
            # 提交更改
            db.commit()
            
            logger.success(f"🎉 批量提审完成！成功提审 {success_count}/{len(unsubmitted_plans)} 个计划")
            
    except Exception as e:
        logger.error(f"❌ 批量提审失败: {e}")

if __name__ == "__main__":
    batch_submit_unsubmitted_plans()
