# AI_TOOLS 目录

## 用途
长期保留的AI工具

## 保留期限
永久保留

## 子目录说明
- `maintenance/`: maintenance相关文件
- `analysis/`: analysis相关文件
- `cleanup/`: cleanup相关文件
- `migration/`: migration相关文件
- `optimization/`: optimization相关文件

## 文件命名规范
- 使用统一前缀标识AI生成文件
- 包含生成日期 (YYYYMMDD格式)
- 描述文件用途和功能

## 管理命令
```bash
# 查看此目录统计
python tools/ai_file_manager.py status --dir ai_tools

# 清理过期文件
python tools/ai_file_manager.py cleanup --dir ai_tools
```

---
*此文件由AI文件管理器自动生成*
