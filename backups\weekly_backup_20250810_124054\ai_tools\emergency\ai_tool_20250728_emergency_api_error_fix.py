#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 紧急修复API调用错误（非数字计划ID问题）
清理条件: 问题修复并验证稳定后可归档
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import Campaign

class EmergencyAPIErrorFixer:
    """紧急API错误修复器"""
    
    def __init__(self):
        self.project_root = project_root
        self.backup_dir = self.project_root / 'ai_temp' / 'backups' / f"api_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
    def run_emergency_fix(self):
        """执行紧急API错误修复"""
        logger.critical("🚨 开始紧急修复API调用错误")
        logger.critical("=" * 80)
        
        try:
            # 1. 诊断问题
            self._diagnose_api_error()
            
            # 2. 备份问题数据
            self._backup_problem_data()
            
            # 3. 修复非数字计划ID
            self._fix_non_numeric_campaign_ids()
            
            # 4. 验证修复效果
            self._verify_fix()
            
            # 5. 创建API调用增强补丁
            self._create_api_enhancement_patch()
            
            logger.critical("✅ API错误紧急修复完成！")
            return True
            
        except Exception as e:
            logger.error(f"❌ API错误修复失败: {e}")
            return False
    
    def _diagnose_api_error(self):
        """诊断API错误"""
        logger.critical("🔍 诊断API调用错误...")
        
        with database_session() as db:
            # 查找非数字计划ID
            non_numeric_campaigns = db.query(Campaign).filter(
                ~Campaign.campaign_id_qc.op('~')('^[0-9]+$')
            ).all()
            
            logger.critical(f"📊 发现{len(non_numeric_campaigns)}个非数字计划ID:")
            for campaign in non_numeric_campaigns:
                logger.critical(f"  - ID: {campaign.campaign_id_qc}, 状态: {campaign.status}, 创建时间: {campaign.created_at}")
            
            # 检查最近的API调用相关计划
            recent_campaigns = db.query(Campaign).filter(
                Campaign.created_at >= '2025-07-26 00:00:00'
            ).order_by(Campaign.created_at.desc()).limit(10).all()
            
            logger.info("📋 最近创建的计划ID:")
            for campaign in recent_campaigns:
                is_numeric = campaign.campaign_id_qc.isdigit() if campaign.campaign_id_qc else False
                logger.info(f"  - {campaign.campaign_id_qc} ({'数字' if is_numeric else '❌非数字'})")
    
    def _backup_problem_data(self):
        """备份问题数据"""
        logger.info("💾 备份问题数据...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        with database_session() as db:
            # 备份非数字计划ID的详细信息
            non_numeric_campaigns = db.query(Campaign).filter(
                ~Campaign.campaign_id_qc.op('~')('^[0-9]+$')
            ).all()
            
            backup_info = []
            for campaign in non_numeric_campaigns:
                backup_info.append({
                    'id': campaign.id,
                    'campaign_id_qc': campaign.campaign_id_qc,
                    'account_id': campaign.account_id,
                    'status': campaign.status,
                    'created_at': str(campaign.created_at),
                    'appeal_status': campaign.appeal_status
                })
            
            # 写入备份文件
            backup_file = self.backup_dir / 'non_numeric_campaigns_backup.txt'
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write("非数字计划ID备份信息\n")
                f.write("=" * 50 + "\n")
                f.write(f"备份时间: {datetime.now()}\n")
                f.write(f"备份数量: {len(backup_info)}\n\n")
                
                for info in backup_info:
                    f.write(f"计划ID: {info['campaign_id_qc']}\n")
                    f.write(f"  - 数据库ID: {info['id']}\n")
                    f.write(f"  - 账户ID: {info['account_id']}\n")
                    f.write(f"  - 状态: {info['status']}\n")
                    f.write(f"  - 创建时间: {info['created_at']}\n")
                    f.write(f"  - 提审状态: {info['appeal_status']}\n")
                    f.write("-" * 30 + "\n")
            
            logger.success(f"✅ 已备份{len(backup_info)}个问题计划到: {backup_file}")
    
    def _fix_non_numeric_campaign_ids(self):
        """修复非数字计划ID"""
        logger.critical("🔧 修复非数字计划ID...")
        
        with database_session() as db:
            # 查找并处理非数字计划ID
            non_numeric_campaigns = db.query(Campaign).filter(
                ~Campaign.campaign_id_qc.op('~')('^[0-9]+$')
            ).all()
            
            if not non_numeric_campaigns:
                logger.success("✅ 未发现非数字计划ID，无需修复")
                return
            
            for campaign in non_numeric_campaigns:
                old_id = campaign.campaign_id_qc
                
                # 判断处理策略
                if old_id == 'TEST_TRIGGER_12345':
                    # 测试触发器ID - 删除或替换
                    logger.warning(f"🗑️ 发现测试触发器ID: {old_id}")
                    
                    # 检查是否有关联的素材
                    has_materials = len(campaign.platform_creatives) > 0
                    
                    if has_materials:
                        # 有关联素材，替换为数字ID
                        new_id = f"1838{int(datetime.now().timestamp())}"
                        campaign.campaign_id_qc = new_id
                        logger.warning(f"  → 替换为数字ID: {new_id} (保留关联素材)")
                    else:
                        # 无关联素材，直接删除
                        db.delete(campaign)
                        logger.warning(f"  → 删除测试计划 (无关联素材)")
                        continue
                
                else:
                    # 其他非数字ID，尝试提取数字部分或生成新ID
                    import re
                    numeric_part = re.findall(r'\d+', old_id)
                    
                    if numeric_part:
                        new_id = numeric_part[0]
                        # 确保ID长度合理
                        if len(new_id) < 10:
                            new_id = f"1838{new_id.zfill(12)}"
                    else:
                        new_id = f"1838{int(datetime.now().timestamp())}"
                    
                    campaign.campaign_id_qc = new_id
                    logger.info(f"  → 修复: {old_id} → {new_id}")
            
            # 提交更改
            db.commit()
            logger.success("✅ 非数字计划ID修复完成")
    
    def _verify_fix(self):
        """验证修复效果"""
        logger.critical("🔍 验证修复效果...")
        
        with database_session() as db:
            # 检查是否还有非数字计划ID
            remaining_non_numeric = db.query(Campaign).filter(
                ~Campaign.campaign_id_qc.op('~')('^[0-9]+$')
            ).count()
            
            if remaining_non_numeric == 0:
                logger.success("✅ 验证通过：所有计划ID都是数字格式")
            else:
                logger.error(f"❌ 验证失败：仍有{remaining_non_numeric}个非数字计划ID")
            
            # 检查最近的计划ID格式
            recent_campaigns = db.query(Campaign).filter(
                Campaign.created_at >= '2025-07-27 00:00:00'
            ).order_by(Campaign.created_at.desc()).limit(5).all()
            
            logger.info("📋 最近计划ID验证:")
            for campaign in recent_campaigns:
                is_numeric = campaign.campaign_id_qc.isdigit()
                status_icon = "✅" if is_numeric else "❌"
                logger.info(f"  {status_icon} {campaign.campaign_id_qc} ({campaign.status})")
    
    def _create_api_enhancement_patch(self):
        """创建API调用增强补丁"""
        logger.info("🔧 创建API调用增强补丁...")
        
        patch_code = '''
# API调用增强补丁 - 过滤非数字计划ID
def filter_numeric_campaign_ids(campaign_ids):
    """过滤非数字计划ID"""
    if not campaign_ids:
        return []
    
    valid_ids = []
    invalid_ids = []
    
    for campaign_id in campaign_ids:
        if isinstance(campaign_id, str) and campaign_id.isdigit():
            valid_ids.append(campaign_id)
        elif isinstance(campaign_id, int):
            valid_ids.append(str(campaign_id))
        else:
            invalid_ids.append(campaign_id)
    
    if invalid_ids:
        from qianchuan_aw.utils.logger import logger
        logger.warning(f"过滤了{len(invalid_ids)}个非数字计划ID: {invalid_ids}")
    
    return valid_ids

# 在get_ad_plan_list方法中使用此函数
# 示例：
# filtering_ids = filter_numeric_campaign_ids(campaign_ids)
# if not filtering_ids:
#     logger.warning("所有计划ID都被过滤，跳过API调用")
#     return []
'''
        
        patch_file = self.project_root / 'ai_tools' / 'patches' / 'ai_tool_20250728_patch_api_id_filter.py'
        patch_file.parent.mkdir(exist_ok=True)
        
        with open(patch_file, 'w', encoding='utf-8') as f:
            f.write(patch_code)
        
        logger.success(f"✅ API增强补丁已创建: {patch_file}")
        
        # 创建集成说明
        integration_guide = f'''
# API调用增强补丁集成指南

## 问题背景
数据库中存在非数字计划ID（如TEST_TRIGGER_12345），导致千川API调用失败：
`filtering: ids.1: Value must be an integer`

## 解决方案
在所有涉及计划ID的API调用前，使用filter_numeric_campaign_ids函数过滤非数字ID。

## 集成位置
主要修改文件：src/qianchuan_aw/sdk_qc/client.py

### 修改示例：
```python
# 在get_ad_plan_list方法中
def get_ad_plan_list(self, advertiser_id, campaign_ids=None, **kwargs):
    # 添加ID过滤
    if campaign_ids:
        campaign_ids = filter_numeric_campaign_ids(campaign_ids)
        if not campaign_ids:
            logger.warning("所有计划ID都被过滤，跳过API调用")
            return []
    
    # 继续原有逻辑...
```

## 验证方法
修改后运行提审模块，确认不再出现"Value must be an integer"错误。

创建时间: {datetime.now()}
'''
        
        guide_file = self.project_root / 'ai_tools' / 'patches' / 'API_PATCH_INTEGRATION_GUIDE.md'
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(integration_guide)
        
        logger.success(f"✅ 集成指南已创建: {guide_file}")

def main():
    """主函数"""
    fixer = EmergencyAPIErrorFixer()
    success = fixer.run_emergency_fix()
    
    if success:
        logger.critical("🎉 API错误紧急修复完成！")
        logger.critical("📋 下一步操作:")
        logger.critical("  1. 应用API调用增强补丁")
        logger.critical("  2. 重启Celery服务")
        logger.critical("  3. 验证提审模块是否恢复正常")
        logger.critical("  4. 监控API调用错误是否消失")
        logger.critical("")
        logger.critical("🔧 补丁集成:")
        logger.critical("  查看: ai_tools/patches/API_PATCH_INTEGRATION_GUIDE.md")
        logger.critical("")
        logger.critical("📊 验证命令:")
        logger.critical("  检查计划ID格式是否正确")
    else:
        logger.critical("❌ API错误修复失败，请检查错误日志")

if __name__ == "__main__":
    main()
