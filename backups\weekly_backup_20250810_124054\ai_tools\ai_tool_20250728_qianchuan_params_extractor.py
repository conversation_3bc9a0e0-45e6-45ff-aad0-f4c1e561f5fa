"""
千川提审参数自动获取器
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 自动从千川后台页面提取提审所需的动态参数
依赖关系: 需要项目的playwright浏览器管理和cookies系统
清理条件: 功能被官方API替代时可删除
"""

import json
import time
import re
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

# 导入项目的cookie工具
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanParamsExtractor:
    """千川提审参数自动提取器"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 参数缓存
        self.params_cache = {}
        self.cache_expiry = {}
        self.cache_duration = 300  # 5分钟缓存
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载指定主体的cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            if not cookies_array:
                raise ValueError(f"主体 '{self.principal_name}' 的cookies为空")
            
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _create_browser_context(self) -> tuple:
        """创建浏览器上下文"""
        try:
            cookies = self._load_cookies()
            
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(
                headless=True,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            )
            
            context.add_cookies(cookies)
            
            return playwright, browser, context
            
        except Exception as e:
            logger.error(f"❌ 创建浏览器上下文失败: {e}")
            raise
    
    def _extract_copilot_params(self, page: Page) -> Dict[str, str]:
        """从页面中提取copilot相关参数"""
        try:
            # 等待页面加载完成
            page.wait_for_load_state("domcontentloaded")
            time.sleep(3)
            
            # 方法1: 从页面JavaScript变量中提取
            params = {}
            
            # 尝试从window对象中提取参数
            try:
                js_code = """
                () => {
                    // 尝试获取各种可能的参数
                    const result = {};
                    
                    // 检查window对象中的copilot相关变量
                    if (window.copilotConfig) {
                        result.copilotConfig = window.copilotConfig;
                    }
                    
                    // 检查sessionStorage和localStorage
                    try {
                        const sessionData = {};
                        for (let i = 0; i < sessionStorage.length; i++) {
                            const key = sessionStorage.key(i);
                            if (key && (key.includes('copilot') || key.includes('session') || key.includes('window'))) {
                                sessionData[key] = sessionStorage.getItem(key);
                            }
                        }
                        if (Object.keys(sessionData).length > 0) {
                            result.sessionStorage = sessionData;
                        }
                    } catch (e) {}
                    
                    // 检查页面中的script标签
                    const scripts = document.querySelectorAll('script');
                    for (const script of scripts) {
                        const content = script.textContent || script.innerHTML;
                        if (content.includes('sessionId') || content.includes('windowId') || content.includes('messageId')) {
                            // 尝试提取ID
                            const sessionMatch = content.match(/sessionId['"\\s]*[:=]['"\\s]*([\\d]+)/);
                            const windowMatch = content.match(/windowId['"\\s]*[:=]['"\\s]*([a-f0-9]+)/);
                            const messageMatch = content.match(/messageId['"\\s]*[:=]['"\\s]*([\\d]+)/);
                            
                            if (sessionMatch) result.sessionId = sessionMatch[1];
                            if (windowMatch) result.windowId = windowMatch[1];
                            if (messageMatch) result.messageId = messageMatch[1];
                        }
                    }
                    
                    return result;
                }
                """
                
                js_result = page.evaluate(js_code)
                if js_result:
                    params.update(js_result)
                    logger.info(f"从JavaScript中提取到参数: {list(js_result.keys())}")
                
            except Exception as e:
                logger.warning(f"JavaScript参数提取失败: {e}")
            
            # 方法2: 监听网络请求
            network_params = self._extract_from_network_requests(page)
            if network_params:
                params.update(network_params)
            
            # 方法3: 生成基于时间戳的参数（作为备用）
            if not params.get('sessionId') or not params.get('messageId'):
                current_timestamp = int(time.time() * 1000)
                if not params.get('sessionId'):
                    params['sessionId'] = str(current_timestamp - 1000)
                if not params.get('messageId'):
                    params['messageId'] = str(current_timestamp - 2000)
                logger.info("使用时间戳生成备用参数")
            
            return params
            
        except Exception as e:
            logger.error(f"❌ 提取copilot参数失败: {e}")
            return {}
    
    def _extract_from_network_requests(self, page: Page) -> Dict[str, str]:
        """通过监听网络请求提取参数"""
        try:
            captured_params = {}
            
            def handle_request(request):
                """处理网络请求"""
                try:
                    url = request.url
                    if 'copilot/api' in url or 'callback' in url:
                        # 检查请求体
                        if request.method == 'POST':
                            try:
                                post_data = request.post_data
                                if post_data:
                                    data = json.loads(post_data)
                                    if 'sessionId' in data:
                                        captured_params['sessionId'] = data['sessionId']
                                    if 'windowId' in data:
                                        captured_params['windowId'] = data['windowId']
                                    if 'messageId' in data:
                                        captured_params['messageId'] = data['messageId']
                                    
                                    logger.info(f"从网络请求中捕获参数: {list(captured_params.keys())}")
                            except:
                                pass
                except Exception as e:
                    logger.debug(f"处理网络请求失败: {e}")
            
            # 监听请求
            page.on("request", handle_request)
            
            # 尝试触发一些可能产生copilot请求的操作
            try:
                # 查找并点击智投星图标
                copilot_selectors = [
                    ".copilot-icon",
                    "[class*='copilot']",
                    "[data-testid*='copilot']",
                    ".ai-assistant",
                    "[class*='ai-assistant']"
                ]
                
                for selector in copilot_selectors:
                    try:
                        element = page.locator(selector).first
                        if element.is_visible(timeout=2000):
                            logger.info(f"找到copilot元素: {selector}")
                            element.click(timeout=5000)
                            time.sleep(2)  # 等待请求发送
                            break
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"触发copilot操作失败: {e}")
            
            return captured_params
            
        except Exception as e:
            logger.warning(f"网络请求监听失败: {e}")
            return {}
    
    def extract_params_for_advertiser(self, advertiser_id: str, force_refresh: bool = False) -> Dict[str, str]:
        """为指定广告户提取参数"""
        cache_key = f"params_{advertiser_id}"
        
        # 检查缓存
        if not force_refresh and cache_key in self.params_cache:
            if time.time() < self.cache_expiry.get(cache_key, 0):
                logger.info(f"使用缓存的参数: {advertiser_id}")
                return self.params_cache[cache_key]
        
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"开始为广告户 {advertiser_id} 提取参数...")
            
            # 创建浏览器上下文
            playwright, browser, context = self._create_browser_context()
            page = context.new_page()
            page.set_default_timeout(30000)
            
            # 导航到千川页面
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"导航到: {url}")
            
            page.goto(url, wait_until="domcontentloaded")
            time.sleep(5)  # 等待页面完全加载
            
            # 检查页面是否正确加载
            title = page.title()
            current_url = page.url
            logger.info(f"页面标题: {title}")
            logger.info(f"当前URL: {current_url}")
            
            # 提取参数
            params = self._extract_copilot_params(page)
            
            # 添加广告户ID
            params['advertiser_id'] = advertiser_id
            
            # 生成planning_id（基于时间戳）
            if not params.get('planning_id'):
                params['planning_id'] = str(int(time.time() * 1000) + 1000)
            
            # 确保windowId存在（每个广告户应该有唯一的windowId）
            if not params.get('windowId'):
                # 生成一个基于广告户ID的windowId
                import hashlib
                hash_input = f"{advertiser_id}_{int(time.time())}"
                hash_obj = hashlib.sha256(hash_input.encode())
                params['windowId'] = hash_obj.hexdigest()[:64]
            
            if params:
                # 缓存参数
                self.params_cache[cache_key] = params
                self.cache_expiry[cache_key] = time.time() + self.cache_duration
                
                logger.success(f"✅ 成功提取参数: {list(params.keys())}")
                return params
            else:
                logger.warning("⚠️ 未能提取到有效参数")
                return {}
                
        except Exception as e:
            logger.error(f"❌ 参数提取失败: {e}")
            return {}
            
        finally:
            # 清理资源
            try:
                if context:
                    context.close()
                if browser:
                    browser.close()
                if playwright:
                    playwright.stop()
            except:
                pass
    
    def get_cached_params(self, advertiser_id: str) -> Optional[Dict[str, str]]:
        """获取缓存的参数"""
        cache_key = f"params_{advertiser_id}"
        if cache_key in self.params_cache:
            if time.time() < self.cache_expiry.get(cache_key, 0):
                return self.params_cache[cache_key]
        return None
    
    def clear_cache(self, advertiser_id: str = None):
        """清理缓存"""
        if advertiser_id:
            cache_key = f"params_{advertiser_id}"
            self.params_cache.pop(cache_key, None)
            self.cache_expiry.pop(cache_key, None)
        else:
            self.params_cache.clear()
            self.cache_expiry.clear()
        logger.info("缓存已清理")


def extract_qianchuan_params(advertiser_id: str, principal_name: str = "缇萃百货", force_refresh: bool = False) -> Dict[str, str]:
    """
    便捷函数：提取千川提审参数
    
    Args:
        advertiser_id: 广告户ID
        principal_name: 主体名称（用于获取cookies）
        force_refresh: 是否强制刷新（忽略缓存）
        
    Returns:
        包含提审所需参数的字典
    """
    extractor = QianchuanParamsExtractor(principal_name)
    return extractor.extract_params_for_advertiser(advertiser_id, force_refresh)


if __name__ == "__main__":
    # 测试参数提取
    test_advertiser_id = "1836333804939273"
    
    print("🔍 测试千川参数自动提取")
    print("=" * 50)
    
    try:
        params = extract_qianchuan_params(test_advertiser_id, force_refresh=True)
        
        if params:
            print("✅ 参数提取成功:")
            for key, value in params.items():
                print(f"  {key}: {value}")
        else:
            print("❌ 参数提取失败")
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_params_extractor import extract_qianchuan_params")
    print("params = extract_qianchuan_params('1836333804939273')")
