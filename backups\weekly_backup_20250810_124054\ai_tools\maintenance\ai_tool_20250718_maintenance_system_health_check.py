#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期维护工具
生命周期: 永久保留
创建目的: 千川自动化项目系统健康检查工具
依赖关系: 检查所有核心组件和配置文件
清理条件: 项目不再需要健康检查时
"""

import os
import sys
import json
from pathlib import Path
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def setup_logger():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/system_health_check.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def check_core_files():
    """检查核心文件"""
    logger = setup_logger()
    logger.info("🔍 检查核心文件...")
    
    core_files = {
        "config/settings.yml": "主配置文件",
        "config/plan_templates.json": "计划模板配置",
        "config/titles.txt": "标题库文件",
        "config/banned_terms.yml": "违禁词配置",
        "main.py": "主程序入口",
        "web_ui.py": "Web界面",
        "run_celery_worker.py": "Celery Worker启动脚本",
        "run_celery_beat.py": "Celery Beat启动脚本",
        "src/qianchuan_aw/sdk_qc/client.py": "千川API客户端",
        "src/qianchuan_aw/database/models.py": "数据库模型",
        "src/qianchuan_aw/workflows/scheduler.py": "工作流调度器",
        "src/qianchuan_aw/workflows/tasks.py": "Celery任务",
        "requirements.txt": "依赖包列表"
    }
    
    results = {"passed": True, "missing_files": [], "existing_files": []}
    
    for file_path, description in core_files.items():
        full_path = project_root / file_path
        if full_path.exists():
            results["existing_files"].append({"path": file_path, "description": description})
            logger.info(f"✅ {description}: {file_path}")
        else:
            results["missing_files"].append({"path": file_path, "description": description})
            logger.error(f"❌ {description}: {file_path} - 文件不存在")
            results["passed"] = False
    
    return results

def check_configuration_files():
    """检查配置文件内容"""
    logger = setup_logger()
    logger.info("🔧 检查配置文件内容...")
    
    results = {"passed": True, "config_issues": []}
    
    # 检查settings.yml
    try:
        from src.qianchuan_aw.utils.config_loader import load_settings
        settings = load_settings()
        
        required_keys = [
            "custom_workflow_assets_dir",
            "database",
            "celery",
            "qianchuan_api"
        ]
        
        for key in required_keys:
            if key not in settings:
                results["config_issues"].append(f"settings.yml缺少必需配置: {key}")
                results["passed"] = False
            else:
                logger.info(f"✅ settings.yml包含配置: {key}")
        
        # 检查文件路径配置
        workflow_dir = settings.get("custom_workflow_assets_dir")
        if workflow_dir and os.path.exists(workflow_dir):
            logger.info(f"✅ 工作流目录存在: {workflow_dir}")
        else:
            results["config_issues"].append(f"工作流目录不存在: {workflow_dir}")
            results["passed"] = False
            
    except Exception as e:
        results["config_issues"].append(f"加载settings.yml失败: {e}")
        results["passed"] = False
    
    # 检查plan_templates.json
    try:
        plan_templates_path = project_root / "config" / "plan_templates.json"
        with open(plan_templates_path, 'r', encoding='utf-8') as f:
            templates = json.load(f)
        
        if "base_daily_sale" in templates and "base_new_customer" in templates:
            logger.info("✅ plan_templates.json包含必需模板")
        else:
            results["config_issues"].append("plan_templates.json缺少必需模板")
            results["passed"] = False
            
    except Exception as e:
        results["config_issues"].append(f"加载plan_templates.json失败: {e}")
        results["passed"] = False
    
    # 检查titles.txt
    try:
        titles_path = project_root / "config" / "titles.txt"
        with open(titles_path, 'r', encoding='utf-8') as f:
            titles = [line.strip() for line in f if line.strip()]
        
        if len(titles) > 0:
            logger.info(f"✅ titles.txt包含 {len(titles)} 个标题")
        else:
            results["config_issues"].append("titles.txt为空")
            results["passed"] = False
            
    except Exception as e:
        results["config_issues"].append(f"加载titles.txt失败: {e}")
        results["passed"] = False
    
    return results

def check_database_connection():
    """检查数据库连接"""
    logger = setup_logger()
    logger.info("🗄️ 检查数据库连接...")
    
    results = {"passed": True, "error": None}
    
    try:
        from src.qianchuan_aw.utils.db_utils import database_session
        from src.qianchuan_aw.database.models import LocalCreative
        
        with database_session() as db:
            count = db.query(LocalCreative).count()
            logger.info(f"✅ 数据库连接成功，local_creatives表有 {count} 条记录")
            
    except Exception as e:
        results["passed"] = False
        results["error"] = str(e)
        logger.error(f"❌ 数据库连接失败: {e}")
    
    return results

def check_workflow_directories():
    """检查工作流目录"""
    logger = setup_logger()
    logger.info("📁 检查工作流目录...")
    
    results = {"passed": True, "directory_issues": []}
    
    try:
        from src.qianchuan_aw.utils.config_loader import load_settings
        settings = load_settings()
        base_dir = settings.get("custom_workflow_assets_dir")
        
        if not base_dir:
            results["directory_issues"].append("未配置工作流目录")
            results["passed"] = False
            return results
        
        required_dirs = [
            "00_materials_archived",
            "01_materials_to_process",
            "02_materials_in_testing", 
            "03_materials_approved",
            "04_materials_rejected",
            "05_manual_promotion"
        ]
        
        for dir_name in required_dirs:
            dir_path = os.path.join(base_dir, dir_name)
            if os.path.exists(dir_path):
                file_count = sum(len(files) for _, _, files in os.walk(dir_path))
                logger.info(f"✅ {dir_name}: 存在，包含 {file_count} 个文件")
            else:
                results["directory_issues"].append(f"目录不存在: {dir_name}")
                logger.warning(f"⚠️ {dir_name}: 不存在")
                
    except Exception as e:
        results["directory_issues"].append(f"检查工作流目录失败: {e}")
        results["passed"] = False
    
    return results

def check_import_dependencies():
    """检查关键模块导入"""
    logger = setup_logger()
    logger.info("📦 检查关键模块导入...")
    
    results = {"passed": True, "import_issues": []}
    
    critical_imports = [
        ("src.qianchuan_aw.utils.config_loader", "load_settings"),
        ("src.qianchuan_aw.utils.db_utils", "database_session"),
        ("src.qianchuan_aw.database.models", "LocalCreative"),
        ("src.qianchuan_aw.sdk_qc.client", "QianchuanClient"),
        ("src.qianchuan_aw.workflows.scheduler", "process_single_video_upload"),
        ("src.qianchuan_aw.workflows.tasks", "upload_single_video"),
        ("src.qianchuan_aw.utils.workflow_helpers", "get_random_titles")
    ]
    
    for module_name, item_name in critical_imports:
        try:
            module = __import__(module_name, fromlist=[item_name])
            getattr(module, item_name)
            logger.info(f"✅ 成功导入: {module_name}.{item_name}")
        except Exception as e:
            results["import_issues"].append(f"导入失败: {module_name}.{item_name} - {e}")
            results["passed"] = False
            logger.error(f"❌ 导入失败: {module_name}.{item_name} - {e}")
    
    return results

def generate_health_report():
    """生成健康检查报告"""
    logger = setup_logger()
    logger.info("📊 生成系统健康检查报告...")
    
    # 执行所有检查
    core_files_result = check_core_files()
    config_result = check_configuration_files()
    database_result = check_database_connection()
    workflow_dirs_result = check_workflow_directories()
    imports_result = check_import_dependencies()
    
    # 汇总结果
    overall_passed = all([
        core_files_result["passed"],
        config_result["passed"],
        database_result["passed"],
        workflow_dirs_result["passed"],
        imports_result["passed"]
    ])
    
    report = {
        "timestamp": str(datetime.now()),
        "overall_status": "HEALTHY" if overall_passed else "ISSUES_FOUND",
        "checks": {
            "core_files": core_files_result,
            "configuration": config_result,
            "database": database_result,
            "workflow_directories": workflow_dirs_result,
            "imports": imports_result
        }
    }
    
    # 保存报告
    report_path = project_root / "logs" / "system_health_report.json"
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 显示摘要
    logger.info("=" * 60)
    logger.info("📋 系统健康检查摘要")
    logger.info("=" * 60)
    
    if overall_passed:
        logger.info("🎉 系统状态: 健康")
        logger.info("✅ 所有检查项目都通过了")
    else:
        logger.warning("⚠️ 系统状态: 发现问题")
        
        if not core_files_result["passed"]:
            logger.error(f"❌ 核心文件: 缺少 {len(core_files_result['missing_files'])} 个文件")
        
        if not config_result["passed"]:
            logger.error(f"❌ 配置文件: {len(config_result['config_issues'])} 个问题")
        
        if not database_result["passed"]:
            logger.error("❌ 数据库连接: 失败")
        
        if not workflow_dirs_result["passed"]:
            logger.error(f"❌ 工作流目录: {len(workflow_dirs_result['directory_issues'])} 个问题")
        
        if not imports_result["passed"]:
            logger.error(f"❌ 模块导入: {len(imports_result['import_issues'])} 个问题")
    
    logger.info(f"📄 详细报告已保存: {report_path}")
    
    return report

def main():
    """主函数"""
    import argparse
    from datetime import datetime
    
    parser = argparse.ArgumentParser(description='千川自动化项目系统健康检查')
    parser.add_argument('--check', choices=['files', 'config', 'database', 'dirs', 'imports', 'all'],
                       default='all', help='执行的检查类型')
    parser.add_argument('--report', action='store_true', help='生成详细报告')
    
    args = parser.parse_args()
    
    if args.check == 'files':
        result = check_core_files()
    elif args.check == 'config':
        result = check_configuration_files()
    elif args.check == 'database':
        result = check_database_connection()
    elif args.check == 'dirs':
        result = check_workflow_directories()
    elif args.check == 'imports':
        result = check_import_dependencies()
    else:
        result = generate_health_report()
    
    if args.report and args.check != 'all':
        print(json.dumps(result, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
