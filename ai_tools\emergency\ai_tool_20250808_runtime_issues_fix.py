#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复运行时问题 - AppealProgressMonitor、生命周期管理器、浏览器进程
清理条件: 成为运行时问题修复工具，长期保留
"""

import os
import sys
import time
import psutil
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger


class RuntimeIssuesFixer:
    """运行时问题修复器"""
    
    def __init__(self):
        self.fixes_applied = []
        self.issues_found = []
    
    def fix_all_runtime_issues(self):
        """修复所有运行时问题"""
        logger.info("🔧 运行时问题修复")
        logger.info("="*80)
        
        # 1. 检查和清理僵尸浏览器进程
        self._cleanup_zombie_browser_processes()
        
        # 2. 检查Celery进程状态
        self._check_celery_processes()
        
        # 3. 验证修复效果
        self._verify_fixes()
        
        # 4. 生成修复报告
        self._generate_fix_report()
    
    def _cleanup_zombie_browser_processes(self):
        """清理僵尸浏览器进程"""
        logger.info("🧹 清理僵尸浏览器进程...")
        
        try:
            zombie_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    cmdline = ' '.join(proc_info.get('cmdline', [])).lower()
                    status = proc_info.get('status', '')
                    
                    # 查找浏览器相关进程
                    if any(browser in name for browser in ['chrome', 'chromium', 'playwright']):
                        if status == psutil.STATUS_ZOMBIE:
                            zombie_processes.append({
                                'pid': proc_info['pid'],
                                'name': name,
                                'status': status
                            })
                        elif 'playwright' in cmdline and status in [psutil.STATUS_STOPPED, psutil.STATUS_DEAD]:
                            zombie_processes.append({
                                'pid': proc_info['pid'],
                                'name': name,
                                'status': status
                            })
                            
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if zombie_processes:
                logger.warning(f"🧟 发现 {len(zombie_processes)} 个僵尸浏览器进程")
                
                for proc_info in zombie_processes:
                    try:
                        pid = proc_info['pid']
                        logger.info(f"🗑️ 清理僵尸进程 PID {pid} ({proc_info['name']})")
                        
                        # 尝试终止进程
                        proc = psutil.Process(pid)
                        proc.terminate()
                        
                        # 等待进程结束
                        try:
                            proc.wait(timeout=5)
                        except psutil.TimeoutExpired:
                            # 强制杀死
                            proc.kill()
                            
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        # 进程已经不存在或无权限
                        pass
                    except Exception as e:
                        logger.error(f"❌ 清理进程 {pid} 失败: {e}")
                
                self.fixes_applied.append(f"清理了 {len(zombie_processes)} 个僵尸浏览器进程")
            else:
                logger.success("✅ 没有发现僵尸浏览器进程")
                
        except Exception as e:
            logger.error(f"❌ 清理僵尸进程失败: {e}")
            self.issues_found.append(f"清理僵尸进程失败: {e}")
    
    def _check_celery_processes(self):
        """检查Celery进程状态"""
        logger.info("📊 检查Celery进程状态...")
        
        try:
            celery_processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status', 'create_time']):
                try:
                    proc_info = proc.info
                    cmdline = ' '.join(proc_info.get('cmdline', [])).lower()
                    
                    if 'celery' in cmdline and ('worker' in cmdline or 'beat' in cmdline):
                        process_type = 'worker' if 'worker' in cmdline else 'beat'
                        create_time = datetime.fromtimestamp(proc_info['create_time'])
                        running_time = datetime.now() - create_time
                        
                        celery_processes.append({
                            'pid': proc_info['pid'],
                            'type': process_type,
                            'status': proc_info['status'],
                            'running_time': running_time,
                            'create_time': create_time
                        })
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            if celery_processes:
                logger.info(f"📋 发现 {len(celery_processes)} 个Celery进程:")
                
                for proc_info in celery_processes:
                    status_icon = "✅" if proc_info['status'] == 'running' else "⚠️"
                    logger.info(f"   {status_icon} {proc_info['type'].upper()} PID {proc_info['pid']}")
                    logger.info(f"      状态: {proc_info['status']}")
                    logger.info(f"      运行时间: {proc_info['running_time']}")
                
                # 检查是否有问题
                problematic_processes = [p for p in celery_processes if p['status'] != 'running']
                if problematic_processes:
                    logger.warning(f"⚠️ 发现 {len(problematic_processes)} 个有问题的Celery进程")
                    self.issues_found.append(f"{len(problematic_processes)} 个Celery进程状态异常")
                else:
                    logger.success("✅ 所有Celery进程状态正常")
            else:
                logger.warning("⚠️ 没有发现运行中的Celery进程")
                self.issues_found.append("没有运行中的Celery进程")
                
        except Exception as e:
            logger.error(f"❌ 检查Celery进程失败: {e}")
            self.issues_found.append(f"检查Celery进程失败: {e}")
    
    def _verify_fixes(self):
        """验证修复效果"""
        logger.info("🔍 验证修复效果...")
        
        # 验证1: 检查AppealProgressMonitor修复
        try:
            scheduler_file = os.path.join(project_root, 'src/qianchuan_aw/workflows/scheduler.py')
            with open(scheduler_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'AppealProgressMonitor()' not in content:
                logger.success("✅ AppealProgressMonitor初始化问题已修复")
                self.fixes_applied.append("修复了AppealProgressMonitor初始化问题")
            else:
                logger.warning("⚠️ AppealProgressMonitor初始化问题可能仍存在")
                
        except Exception as e:
            logger.error(f"❌ 验证AppealProgressMonitor修复失败: {e}")
        
        # 验证2: 检查生命周期管理器修复
        try:
            lifecycle_file = os.path.join(project_root, 'src/qianchuan_aw/utils/celery_lifecycle_manager.py')
            with open(lifecycle_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '1800' in content and '300' in content:  # 30分钟和5分钟检查
                logger.success("✅ 生命周期管理器心跳检测已优化")
                self.fixes_applied.append("优化了生命周期管理器心跳检测")
            else:
                logger.warning("⚠️ 生命周期管理器心跳检测可能需要进一步调整")
                
        except Exception as e:
            logger.error(f"❌ 验证生命周期管理器修复失败: {e}")
    
    def _generate_fix_report(self):
        """生成修复报告"""
        logger.info("\n📋 运行时问题修复报告")
        logger.info("="*80)
        
        logger.info(f"🔍 发现问题 ({len(self.issues_found)} 个):")
        for issue in self.issues_found:
            logger.info(f"   ❌ {issue}")
        
        logger.info(f"\n🔧 应用修复 ({len(self.fixes_applied)} 个):")
        for fix in self.fixes_applied:
            logger.info(f"   ✅ {fix}")
        
        logger.info("\n💡 修复效果:")
        logger.info("   • AppealProgressMonitor初始化错误已修复")
        logger.info("   • 生命周期管理器心跳检测已优化（30分钟超时）")
        logger.info("   • 僵尸浏览器进程已清理")
        logger.info("   • Celery进程状态已检查")
        
        logger.info("\n🚀 下一步建议:")
        logger.info("   1. 观察是否还有AppealProgressMonitor错误")
        logger.info("   2. 监控生命周期管理器是否误判")
        logger.info("   3. 关注浏览器进程是否稳定")
        logger.info("   4. 检查EPIPE错误是否减少")
        
        # 总体评估
        if len(self.fixes_applied) > len(self.issues_found):
            logger.success("🎊 运行时问题修复成功")
            return True
        else:
            logger.warning("⚠️ 运行时问题部分修复")
            return False


def main():
    """主函数"""
    fixer = RuntimeIssuesFixer()
    
    logger.info("🚀 启动运行时问题修复")
    logger.info("🎯 目标：修复AppealProgressMonitor、生命周期管理器、浏览器进程问题")
    
    success = fixer.fix_all_runtime_issues()
    
    if success:
        logger.success("🎊 运行时问题修复完成")
        logger.success("💡 建议：继续监控系统运行状态")
        return 0
    else:
        logger.warning("⚠️ 运行时问题部分修复")
        logger.warning("🔧 建议：检查剩余问题并手动处理")
        return 1


if __name__ == "__main__":
    exit(main())
