#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时测试工具
生命周期: 7天
创建目的: 测试统一账户选择器在表单内的兼容性
依赖关系: Streamlit, 统一账户选择器组件
清理条件: 测试完成后可删除
"""

import sys
import os
import streamlit as st
from pathlib import Path

# 必须在所有其他Streamlit命令之前设置页面配置
st.set_page_config(
    page_title="表单兼容性测试",
    page_icon="🔧",
    layout="wide"
)

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
sys.path.insert(0, str(project_root / 'ai_tools' / 'maintenance'))

try:
    from ai_tool_20250718_maintenance_unified_account_selector import (
        create_single_account_selector,
        create_multi_account_selector
    )
except ImportError as e:
    st.error(f"导入统一账户选择器失败: {e}")
    st.stop()

def main():
    st.title("🔧 表单兼容性测试")
    st.markdown("---")
    
    st.markdown("""
    ## 🎯 测试目标
    
    验证统一账户选择器在以下场景中的兼容性：
    - ✅ **表单外使用**：完整功能，包括收藏管理
    - ✅ **表单内使用**：核心选择功能，隐藏管理按钮
    """)
    
    # 测试1：表单外使用（完整功能）
    st.markdown("### 🔸 测试1：表单外使用（完整功能）")
    st.markdown("应该显示完整的筛选、统计和管理功能")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**单选账户选择器**")
        selected_outside_single = create_single_account_selector(
            key="outside_single",
            label="表单外单选",
            show_filter=True,
            show_stats=True,
            in_form=False  # 明确指定不在表单内
        )
        
        if selected_outside_single:
            st.success(f"✅ 选择: {selected_outside_single.name}")
    
    with col2:
        st.markdown("**多选账户选择器**")
        selected_outside_multi = create_multi_account_selector(
            key="outside_multi",
            label="表单外多选",
            show_filter=True,
            show_stats=True,
            in_form=False  # 明确指定不在表单内
        )
        
        if selected_outside_multi:
            st.success(f"✅ 选择了 {len(selected_outside_multi)} 个账户")
    
    st.markdown("---")
    
    # 测试2：表单内使用（简化功能）
    st.markdown("### 🔸 测试2：表单内使用（简化功能）")
    st.markdown("应该隐藏管理按钮，但保持核心选择功能")
    
    col3, col4 = st.columns(2)
    
    with col3:
        st.markdown("**表单内单选测试**")
        with st.form("form_single_test"):
            selected_inside_single = create_single_account_selector(
                key="inside_single",
                label="表单内单选",
                show_filter=True,
                show_stats=True,
                in_form=True  # 明确指定在表单内
            )
            
            submitted_single = st.form_submit_button("提交单选测试")
            
            if submitted_single and selected_inside_single:
                st.success(f"✅ 表单提交成功: {selected_inside_single.name}")
    
    with col4:
        st.markdown("**表单内多选测试**")
        with st.form("form_multi_test"):
            selected_inside_multi = create_multi_account_selector(
                key="inside_multi",
                label="表单内多选",
                show_filter=True,
                show_stats=True,
                in_form=True  # 明确指定在表单内
            )
            
            submitted_multi = st.form_submit_button("提交多选测试")
            
            if submitted_multi and selected_inside_multi:
                st.success(f"✅ 表单提交成功: 选择了 {len(selected_inside_multi)} 个账户")
    
    st.markdown("---")
    
    # 测试3：自动检测模式
    st.markdown("### 🔸 测试3：自动检测模式")
    st.markdown("不明确指定in_form参数，让组件自动检测")
    
    col5, col6 = st.columns(2)
    
    with col5:
        st.markdown("**表单外自动检测**")
        selected_auto_outside = create_single_account_selector(
            key="auto_outside",
            label="自动检测（表单外）",
            show_filter=True,
            show_stats=True
            # 不指定in_form参数，让组件自动检测
        )
        
        if selected_auto_outside:
            st.success(f"✅ 自动检测成功: {selected_auto_outside.name}")
    
    with col6:
        st.markdown("**表单内自动检测**")
        with st.form("form_auto_test"):
            selected_auto_inside = create_single_account_selector(
                key="auto_inside",
                label="自动检测（表单内）",
                show_filter=True,
                show_stats=True
                # 不指定in_form参数，让组件自动检测
            )
            
            submitted_auto = st.form_submit_button("提交自动检测测试")
            
            if submitted_auto and selected_auto_inside:
                st.success(f"✅ 自动检测表单提交成功: {selected_auto_inside.name}")
    
    st.markdown("---")
    
    # 测试结果总结
    st.markdown("### 📊 测试结果总结")
    
    test_results = []
    
    # 检查各个测试的状态
    if selected_outside_single:
        test_results.append("✅ 表单外单选：正常工作")
    else:
        test_results.append("⚠️ 表单外单选：未选择")
    
    if selected_outside_multi:
        test_results.append("✅ 表单外多选：正常工作")
    else:
        test_results.append("⚠️ 表单外多选：未选择")
    
    if 'submitted_single' in locals() and submitted_single:
        test_results.append("✅ 表单内单选：提交成功")
    else:
        test_results.append("⚠️ 表单内单选：未提交")
    
    if 'submitted_multi' in locals() and submitted_multi:
        test_results.append("✅ 表单内多选：提交成功")
    else:
        test_results.append("⚠️ 表单内多选：未提交")
    
    if selected_auto_outside:
        test_results.append("✅ 自动检测（表单外）：正常工作")
    else:
        test_results.append("⚠️ 自动检测（表单外）：未选择")
    
    if 'submitted_auto' in locals() and submitted_auto:
        test_results.append("✅ 自动检测（表单内）：提交成功")
    else:
        test_results.append("⚠️ 自动检测（表单内）：未提交")
    
    for result in test_results:
        st.write(result)
    
    st.markdown("---")
    st.markdown("### 🎉 如果所有测试都能正常运行而不报错，说明表单兼容性修复成功！")

if __name__ == "__main__":
    main()
