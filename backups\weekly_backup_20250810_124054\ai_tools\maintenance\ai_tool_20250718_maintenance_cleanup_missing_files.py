#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 7天（清理完成后可删除）
创建目的: 清理数据库中不存在的文件记录
依赖关系: 依赖数据库连接
清理条件: 数据库清理完成后
"""

import os
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative

def setup_logger():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/cleanup_missing_files.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def cleanup_missing_files(dry_run=False):
    """清理数据库中不存在的文件记录"""
    logger = setup_logger()
    logger.info("🧹 开始清理不存在的文件记录...")
    
    try:
        with database_session() as db:
            # 查询所有有文件路径的记录
            all_records = db.query(LocalCreative).filter(
                LocalCreative.file_path.isnot(None)
            ).all()
            
            logger.info(f"检查 {len(all_records)} 条记录...")
            
            missing_records = []
            existing_count = 0
            
            for record in all_records:
                if not os.path.exists(record.file_path):
                    missing_records.append(record)
                else:
                    existing_count += 1
            
            logger.info(f"发现 {len(missing_records)} 条记录对应的文件不存在")
            logger.info(f"存在的文件: {existing_count} 条")
            
            if dry_run:
                logger.info("预览模式 - 不会实际删除记录")
                for record in missing_records[:10]:  # 只显示前10条
                    logger.info(f"  将删除: ID={record.id}, 文件={record.file_path}")
                if len(missing_records) > 10:
                    logger.info(f"  ... 还有 {len(missing_records) - 10} 条记录")
                return len(missing_records), 0
            
            # 实际删除不存在的记录
            deleted_count = 0
            for record in missing_records:
                try:
                    db.delete(record)
                    deleted_count += 1
                    
                    if deleted_count % 100 == 0:
                        logger.info(f"已删除 {deleted_count} 条记录...")
                        
                except Exception as e:
                    logger.error(f"删除记录失败 ID:{record.id}, 错误:{e}")
            
            # 提交更改
            db.commit()
            
            logger.info(f"✅ 清理完成!")
            logger.info(f"   - 删除记录: {deleted_count} 条")
            logger.info(f"   - 保留记录: {existing_count} 条")
            
            return deleted_count, existing_count
            
    except Exception as e:
        logger.error(f"❌ 清理失败: {e}")
        raise

def update_pending_status():
    """将upload_failed状态的记录重置为pending_upload"""
    logger = setup_logger()
    logger.info("🔄 重置失败状态的记录...")
    
    try:
        with database_session() as db:
            # 查询所有upload_failed状态的记录
            failed_records = db.query(LocalCreative).filter(
                LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value
            ).all()
            
            logger.info(f"发现 {len(failed_records)} 条失败状态的记录")
            
            # 重置为pending_upload状态
            updated_count = 0
            for record in failed_records:
                if os.path.exists(record.file_path):  # 只重置文件存在的记录
                    record.status = MaterialStatus.PENDING_UPLOAD.value
                    updated_count += 1
            
            db.commit()
            
            logger.info(f"✅ 重置完成: {updated_count} 条记录重置为pending_upload状态")
            
            return updated_count
            
    except Exception as e:
        logger.error(f"❌ 重置失败: {e}")
        raise

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='清理不存在的文件记录')
    parser.add_argument('action', choices=['cleanup', 'reset', 'both'], 
                       help='执行的操作: cleanup=清理不存在文件, reset=重置失败状态, both=两者都执行')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际修改')
    
    args = parser.parse_args()
    
    if args.action in ['cleanup', 'both']:
        deleted, existing = cleanup_missing_files(args.dry_run)
        print(f"\n🧹 清理结果: 删除 {deleted} 条, 保留 {existing} 条")
    
    if args.action in ['reset', 'both'] and not args.dry_run:
        updated = update_pending_status()
        print(f"\n🔄 重置结果: {updated} 条记录重置为待上传状态")

if __name__ == "__main__":
    main()
