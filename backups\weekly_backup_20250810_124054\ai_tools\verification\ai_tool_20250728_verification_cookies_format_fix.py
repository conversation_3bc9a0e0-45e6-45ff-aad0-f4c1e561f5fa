#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 验证cookies格式修复是否生效
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import json
import asyncio
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_cookies_loading():
    """测试cookies加载"""
    logger.info("🧪 测试cookies加载...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_copilot_service import AsyncCopilotService
        
        # 测试主体
        test_principals = ["缇萃百货", "萃阳百货"]
        
        for principal in test_principals:
            logger.info(f"🔍 测试主体: {principal}")
            
            # 创建服务实例
            service = AsyncCopilotService(
                principal_name=principal,
                account_id=**********,
                app_settings={}
            )
            
            # 测试cookies加载
            async def test_load():
                try:
                    cookies = await service._load_cookies()
                    return cookies
                except Exception as e:
                    logger.error(f"❌ 加载失败: {e}")
                    return None
            
            cookies = asyncio.run(test_load())
            
            if cookies:
                logger.success(f"✅ {principal}: 成功加载 {len(cookies)} 个cookies")
            else:
                logger.error(f"❌ {principal}: cookies加载失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试cookies加载失败: {e}")
        return False

def test_manual_appeal():
    """测试手动提审"""
    logger.info("🎯 测试手动提审...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        # 手动触发任务
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 任务已派发，ID: {result.id}")
        
        # 等待任务执行
        logger.info("⏳ 等待任务执行...")
        
        import time
        for i in range(20):  # 等待20秒
            time.sleep(1)
            
            if result.ready():
                if result.successful():
                    logger.success("✅ 任务执行成功")
                    return True, "任务执行成功"
                else:
                    error = result.result
                    logger.error(f"❌ 任务执行失败: {error}")
                    return False, str(error)
            
            if i % 5 == 0 and i > 0:
                logger.info(f"⏳ 等待中... ({i}秒)")
        
        logger.warning("⚠️ 任务仍在执行中")
        return True, "任务正在执行"
        
    except Exception as e:
        logger.error(f"❌ 测试手动提审失败: {e}")
        return False, str(e)

def check_recent_logs():
    """检查最近的日志"""
    logger.info("📋 检查最近的提审日志...")
    
    try:
        log_dir = project_root / 'logs'
        
        if not log_dir.exists():
            logger.warning("⚠️ 日志目录不存在")
            return False
        
        # 查找最新的日志文件
        log_files = list(log_dir.glob('*.log'))
        if not log_files:
            logger.warning("⚠️ 未找到日志文件")
            return False
        
        latest_log = max(log_files, key=lambda f: f.stat().st_mtime)
        
        logger.info(f"📄 检查日志文件: {latest_log}")
        
        # 读取最近的日志内容
        with open(latest_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 查找最近的提审相关日志
        recent_logs = []
        cookies_errors = []
        format_errors = []
        success_logs = []
        
        for line in lines[-200:]:  # 只检查最后200行
            if any(keyword in line for keyword in ['提审', 'appeal', 'submit_plans']):
                recent_logs.append(line.strip())
                
                if "'list' object has no attribute 'get'" in line:
                    format_errors.append(line.strip())
                elif 'cookies' in line.lower() and any(error in line for error in ['失败', 'ERROR', '❌']):
                    cookies_errors.append(line.strip())
                elif any(success in line for success in ['成功', 'SUCCESS', '✅']):
                    success_logs.append(line.strip())
        
        logger.info(f"📊 最近提审相关日志: {len(recent_logs)} 条")
        logger.info(f"📊 成功日志: {len(success_logs)} 条")
        logger.info(f"📊 Cookies错误: {len(cookies_errors)} 条")
        logger.info(f"📊 格式错误: {len(format_errors)} 条")
        
        if recent_logs:
            logger.info("📋 最近的提审日志（最后5条）:")
            for log_line in recent_logs[-5:]:
                if "'list' object has no attribute 'get'" in log_line:
                    logger.error(f"   🚨 格式错误: {log_line}")
                elif 'cookies' in log_line.lower() and any(error in log_line for error in ['失败', 'ERROR', '❌']):
                    logger.error(f"   🚨 Cookies错误: {log_line}")
                elif any(success in log_line for success in ['成功', 'SUCCESS', '✅']):
                    logger.success(f"   ✅ {log_line}")
                else:
                    logger.info(f"   📋 {log_line}")
        
        # 检查是否还有格式错误
        if format_errors:
            logger.error(f"❌ 仍有格式错误: {len(format_errors)} 条")
            return False
        
        if cookies_errors:
            logger.warning(f"⚠️ 仍有cookies错误: {len(cookies_errors)} 条")
            return False
        
        logger.success("✅ 未发现格式或cookies错误")
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查日志失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始验证cookies格式修复...")
    logger.info("="*60)
    
    results = {
        'cookies_loading_ok': False,
        'manual_appeal_ok': False,
        'logs_clean': False
    }
    
    try:
        # 1. 测试cookies加载
        results['cookies_loading_ok'] = test_cookies_loading()
        
        # 2. 测试手动提审
        appeal_ok, appeal_msg = test_manual_appeal()
        results['manual_appeal_ok'] = appeal_ok
        
        # 3. 检查日志
        results['logs_clean'] = check_recent_logs()
        
        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("🎯 Cookies格式修复验证结果")
        logger.info("="*60)
        
        for check_name, result in results.items():
            status = "✅" if result else "❌"
            check_display = check_name.replace('_', ' ').title()
            logger.info(f"{status} {check_display}")
        
        success_count = sum(results.values())
        total_count = len(results)
        success_rate = (success_count / total_count) * 100
        
        logger.info(f"\n📈 验证通过率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 100:
            logger.success("🎉 Cookies格式修复完全成功！")
            logger.info("\n📋 修复效果:")
            logger.info("✅ Cookies加载不再出现格式错误")
            logger.info("✅ 提审任务可以正常执行")
            logger.info("✅ 日志中无格式相关错误")
            
            logger.info("\n🎯 预期效果:")
            logger.info("- 提审任务不再因cookies格式而失败")
            logger.info("- 计划提审功能完全恢复正常")
            logger.info("- 工作流：创建→提审→监控→收割")
            
        elif success_rate >= 66:
            logger.warning("⚠️ Cookies格式修复基本成功，但仍有小问题")
        else:
            logger.error("❌ Cookies格式修复仍存在问题")
        
        return success_rate >= 66
        
    except Exception as e:
        logger.error(f"❌ 验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
