"""
千川申诉真实流程参数捕获器
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 基于用户描述的真实申诉流程精准捕获callback参数
依赖关系: Playwright浏览器自动化
清理条件: 功能被官方API替代时可删除

真实流程：
1. 点击"自助申诉表单"
2. 选择"计划/商品申诉" 
3. 选择"计划审核不通过/结果申诉"
4. 输入计划ID
5. 点击"提交"
6. 捕获callback接口参数
"""

import json
import time
from typing import Dict, List, Optional, Any
from pathlib import Path
from loguru import logger
from playwright.sync_api import sync_playwright, Page, BrowserContext

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.qianchuan_aw.utils.cookie_utils import clean_cookies_for_playwright


class QianchuanRealFlowCapture:
    """千川申诉真实流程参数捕获器"""
    
    def __init__(self, principal_name: str = "缇萃百货"):
        self.principal_name = principal_name
        self.project_root = Path(__file__).parent.parent
        self.cookies_path = self.project_root / "config" / "browser_cookies.json"
        
        # 捕获的callback请求
        self.callback_requests = []
        
    def _load_cookies(self) -> List[Dict[str, Any]]:
        """加载cookies"""
        try:
            if not self.cookies_path.exists():
                raise FileNotFoundError(f"Cookies文件不存在: {self.cookies_path}")
            
            with open(self.cookies_path, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            if self.principal_name not in cookies_data:
                raise ValueError(f"未找到主体 '{self.principal_name}' 的cookies")
            
            cookies_array = cookies_data[self.principal_name].get("cookies", [])
            cleaned_cookies = clean_cookies_for_playwright(cookies_array)
            logger.info(f"✅ 加载了 {len(cleaned_cookies)} 个cookies")
            return cleaned_cookies
            
        except Exception as e:
            logger.error(f"❌ 加载cookies失败: {e}")
            raise
    
    def _setup_callback_capture(self, page: Page):
        """设置callback请求捕获"""
        def handle_request(request):
            """处理网络请求"""
            try:
                url = request.url
                if 'copilot/api/v1/agw/card/callback' in url and request.method == 'POST':
                    logger.success(f"🎯 捕获到目标callback请求: {url}")
                    
                    try:
                        post_data = request.post_data
                        if post_data:
                            data = json.loads(post_data)
                            
                            # 记录完整的请求信息
                            callback_info = {
                                'url': url,
                                'method': request.method,
                                'data': data,
                                'headers': dict(request.headers),
                                'timestamp': time.time(),
                                'params': dict(request.url.split('?')[1].split('&') if '?' in request.url else [])
                            }
                            
                            self.callback_requests.append(callback_info)
                            logger.success(f"✅ 成功捕获callback参数: {list(data.keys())}")
                            
                            # 详细记录关键参数
                            logger.info(f"📋 sessionId: {data.get('sessionId', 'N/A')}")
                            logger.info(f"📋 windowId: {data.get('windowId', 'N/A')}")
                            logger.info(f"📋 messageId: {data.get('messageId', 'N/A')}")
                            logger.info(f"📋 callBackCode: {data.get('callBackCode', 'N/A')}")
                            
                    except Exception as e:
                        logger.warning(f"解析callback数据失败: {e}")
                        
            except Exception as e:
                logger.debug(f"处理请求失败: {e}")
        
        page.on("request", handle_request)
    
    def capture_real_appeal_flow(self, advertiser_id: str, test_plan_id: str) -> Dict[str, Any]:
        """捕获真实申诉流程的参数"""
        playwright = None
        browser = None
        context = None
        
        try:
            logger.info(f"🚀 开始捕获真实申诉流程参数...")
            logger.info(f"📋 广告户ID: {advertiser_id}")
            logger.info(f"📋 测试计划ID: {test_plan_id}")
            
            # 启动浏览器
            cookies = self._load_cookies()
            playwright = sync_playwright().start()
            browser = playwright.chromium.launch(headless=False)  # 可见模式便于观察
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            context.add_cookies(cookies)
            page = context.new_page()
            
            # 设置callback捕获
            self._setup_callback_capture(page)
            
            # 导航到千川后台
            url = f"https://qianchuan.jinritemai.com/promotion-v2/standard?aavid={advertiser_id}"
            logger.info(f"📍 导航到千川后台: {url}")
            page.goto(url, wait_until="networkidle", timeout=60000)
            
            # 等待页面完全加载
            page.wait_for_timeout(5000)
            logger.info("✅ 页面加载完成")
            
            # 步骤1: 寻找并点击"自助申诉表单"
            logger.info("🔍 步骤1: 寻找'自助申诉表单'按钮...")
            appeal_form_clicked = self._click_appeal_form_button(page)
            
            if not appeal_form_clicked:
                return {"success": False, "error": "未找到自助申诉表单按钮"}
            
            # 等待申诉表单界面出现
            page.wait_for_timeout(3000)
            
            # 步骤2: 点击"计划/商品申诉"
            logger.info("🔍 步骤2: 点击'计划/商品申诉'...")
            plan_appeal_clicked = self._click_plan_appeal_option(page)
            
            if not plan_appeal_clicked:
                return {"success": False, "error": "未找到计划/商品申诉选项"}
            
            # 等待表单展开
            page.wait_for_timeout(2000)
            
            # 步骤3: 选择问题类型"计划审核不通过/结果申诉"
            logger.info("🔍 步骤3: 选择问题类型...")
            problem_type_selected = self._select_problem_type(page)
            
            if not problem_type_selected:
                return {"success": False, "error": "未能选择问题类型"}
            
            # 步骤4: 输入计划ID
            logger.info("🔍 步骤4: 输入计划ID...")
            plan_id_entered = self._enter_plan_id(page, test_plan_id)
            
            if not plan_id_entered:
                return {"success": False, "error": "未能输入计划ID"}
            
            # 步骤5: 点击提交按钮
            logger.info("🔍 步骤5: 点击提交按钮...")
            submit_clicked = self._click_submit_button(page)
            
            if not submit_clicked:
                return {"success": False, "error": "未能点击提交按钮"}
            
            # 等待callback请求
            logger.info("⏳ 等待callback请求...")
            page.wait_for_timeout(10000)
            
            # 检查是否捕获到callback请求
            if self.callback_requests:
                logger.success(f"🎉 成功捕获 {len(self.callback_requests)} 个callback请求")
                return self._extract_callback_params()
            else:
                logger.warning("⚠️ 未捕获到callback请求")
                return {"success": False, "error": "未捕获到callback请求"}
            
        except Exception as e:
            logger.error(f"❌ 捕获真实申诉流程失败: {e}")
            return {"success": False, "error": str(e)}
            
        finally:
            # 清理资源
            try:
                if context:
                    context.close()
                if browser:
                    browser.close()
                if playwright:
                    playwright.stop()
            except:
                pass
    
    def _click_appeal_form_button(self, page: Page) -> bool:
        """点击自助申诉表单按钮"""
        try:
            # 多种可能的选择器
            selectors = [
                "button:has-text('自助申诉表单')",
                "[class*='appeal']:has-text('自助申诉表单')",
                "div:has-text('自助申诉表单')",
                ".copilot-button:has-text('自助申诉表单')",
                "button:has-text('申诉表单')",
                "button:has-text('自助申诉')"
            ]
            
            for selector in selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到自助申诉表单按钮: {selector}")
                            element.click(timeout=5000)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"点击自助申诉表单按钮失败: {e}")
            return False
    
    def _click_plan_appeal_option(self, page: Page) -> bool:
        """点击计划/商品申诉选项"""
        try:
            selectors = [
                "button:has-text('计划/商品申诉')",
                "div:has-text('计划/商品申诉')",
                "[class*='option']:has-text('计划/商品申诉')",
                "li:has-text('计划/商品申诉')",
                ":has-text('计划') >> :has-text('商品') >> :has-text('申诉')"
            ]
            
            for selector in selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到计划/商品申诉选项: {selector}")
                            element.click(timeout=5000)
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"点击计划/商品申诉选项失败: {e}")
            return False
    
    def _select_problem_type(self, page: Page) -> bool:
        """选择问题类型：计划审核不通过/结果申诉"""
        try:
            # 先点击下拉框
            dropdown_selectors = [
                "select",
                ".select",
                "[class*='dropdown']",
                "[class*='select']",
                "div:has-text('请选择')"
            ]
            
            dropdown_clicked = False
            for selector in dropdown_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到下拉框: {selector}")
                            element.click(timeout=3000)
                            dropdown_clicked = True
                            break
                except:
                    continue
            
            if dropdown_clicked:
                page.wait_for_timeout(1000)
                
                # 选择"计划审核不通过/结果申诉"选项
                option_selectors = [
                    "option:has-text('计划审核不通过/结果申诉')",
                    "li:has-text('计划审核不通过/结果申诉')",
                    "div:has-text('计划审核不通过/结果申诉')",
                    ":has-text('计划审核不通过') >> :has-text('结果申诉')"
                ]
                
                for selector in option_selectors:
                    try:
                        elements = page.locator(selector)
                        if elements.count() > 0:
                            element = elements.first
                            if element.is_visible():
                                logger.info(f"✅ 找到问题类型选项: {selector}")
                                element.click(timeout=3000)
                                return True
                    except:
                        continue
            
            return False
            
        except Exception as e:
            logger.error(f"选择问题类型失败: {e}")
            return False
    
    def _enter_plan_id(self, page: Page, plan_id: str) -> bool:
        """输入计划ID"""
        try:
            # 寻找计划ID输入框
            input_selectors = [
                "input[placeholder*='计划ID']",
                "input[placeholder*='ID']",
                "textarea[placeholder*='计划ID']",
                "input[name*='planId']",
                "input[name*='plan_id']",
                ".plan-id-input",
                "[class*='plan-id'] input"
            ]
            
            for selector in input_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible():
                            logger.info(f"✅ 找到计划ID输入框: {selector}")
                            element.fill(plan_id)
                            logger.info(f"✅ 输入计划ID: {plan_id}")
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"输入计划ID失败: {e}")
            return False
    
    def _click_submit_button(self, page: Page) -> bool:
        """点击提交按钮"""
        try:
            submit_selectors = [
                "button:has-text('提交')",
                "button[type='submit']",
                ".submit-button",
                "[class*='submit']",
                "button:has-text('确认')",
                "button:has-text('发送')"
            ]
            
            for selector in submit_selectors:
                try:
                    elements = page.locator(selector)
                    if elements.count() > 0:
                        element = elements.first
                        if element.is_visible() and element.is_enabled():
                            logger.info(f"✅ 找到提交按钮: {selector}")
                            element.click(timeout=5000)
                            logger.info("✅ 提交按钮点击成功")
                            return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"点击提交按钮失败: {e}")
            return False
    
    def _extract_callback_params(self) -> Dict[str, Any]:
        """从捕获的callback请求中提取参数"""
        try:
            if not self.callback_requests:
                return {"success": False, "error": "没有捕获到callback请求"}
            
            # 使用最新的请求
            latest_request = self.callback_requests[-1]
            data = latest_request['data']
            
            # 提取关键参数
            params = {
                "success": True,
                "sessionId": data.get('sessionId'),
                "windowId": data.get('windowId'),
                "messageId": data.get('messageId'),
                "callValue": data.get('callValue'),
                "applicationCode": data.get('applicationCode', 'QC'),
                "callBackCode": data.get('callBackCode'),
                "url": latest_request['url'],
                "headers": latest_request['headers'],
                "captured_at": latest_request['timestamp'],
                "total_requests": len(self.callback_requests),
                "raw_data": data  # 保存完整的原始数据
            }
            
            logger.success(f"✅ 成功提取callback参数")
            logger.info(f"📋 参数数量: {len(params)}")
            
            return params
            
        except Exception as e:
            logger.error(f"提取callback参数失败: {e}")
            return {"success": False, "error": str(e)}


def capture_qianchuan_real_params(advertiser_id: str, test_plan_id: str, principal_name: str = "缇萃百货") -> Dict[str, Any]:
    """
    便捷函数：捕获千川真实申诉流程参数
    
    Args:
        advertiser_id: 广告户ID
        test_plan_id: 测试用的计划ID
        principal_name: 主体名称
        
    Returns:
        包含真实callback参数的字典
    """
    capture = QianchuanRealFlowCapture(principal_name)
    return capture.capture_real_appeal_flow(advertiser_id, test_plan_id)


if __name__ == "__main__":
    # 测试真实流程参数捕获
    test_advertiser_id = "1836333804939273"
    test_plan_id = "1838840072680523"
    
    print("🎯 测试千川真实申诉流程参数捕获")
    print("=" * 60)
    print("基于用户描述的真实操作流程:")
    print("1. 点击'自助申诉表单'")
    print("2. 选择'计划/商品申诉'")
    print("3. 选择'计划审核不通过/结果申诉'")
    print("4. 输入计划ID")
    print("5. 点击'提交'")
    print("6. 捕获callback接口参数")
    print()
    
    try:
        result = capture_qianchuan_real_params(test_advertiser_id, test_plan_id)
        
        if result.get('success'):
            print("✅ 真实流程参数捕获成功:")
            for key, value in result.items():
                if key in ['callValue', 'raw_data']:
                    print(f"  {key}: {str(value)[:100]}...")
                else:
                    print(f"  {key}: {value}")
                    
            print("\n🎉 现在您可以使用这些真实参数进行API调用！")
        else:
            print(f"❌ 真实流程参数捕获失败: {result.get('error')}")
            
    except Exception as e:
        print(f"💥 测试异常: {e}")
    
    print("\n💡 使用方法:")
    print("from ai_tools.ai_tool_20250728_qianchuan_real_flow_capture import capture_qianchuan_real_params")
    print("params = capture_qianchuan_real_params('广告户ID', '计划ID')")
