
# API调用增强补丁 - 过滤非数字计划ID
def filter_numeric_campaign_ids(campaign_ids):
    """过滤非数字计划ID"""
    if not campaign_ids:
        return []
    
    valid_ids = []
    invalid_ids = []
    
    for campaign_id in campaign_ids:
        if isinstance(campaign_id, str) and campaign_id.isdigit():
            valid_ids.append(campaign_id)
        elif isinstance(campaign_id, int):
            valid_ids.append(str(campaign_id))
        else:
            invalid_ids.append(campaign_id)
    
    if invalid_ids:
        from qianchuan_aw.utils.logger import logger
        logger.warning(f"过滤了{len(invalid_ids)}个非数字计划ID: {invalid_ids}")
    
    return valid_ids

# 在get_ad_plan_list方法中使用此函数
# 示例：
# filtering_ids = filter_numeric_campaign_ids(campaign_ids)
# if not filtering_ids:
#     logger.warning("所有计划ID都被过滤，跳过API调用")
#     return []
