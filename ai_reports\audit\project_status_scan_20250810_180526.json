{"scan_results": {"web_ui.py": {"['\\\"]processing['\\\"]": [[93, "'processing': '处理中',"], [1848, "st.info(\"管理卡在 'processing' 状态的僵尸素材 - Manage materials stuck in 'processing' status\")"], [1885, "LocalCreative.status == 'processing',"], [1909, "LocalCreative.status == 'processing',"], [1936, "LocalCreative.status == 'processing'"], [2425, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[95, "'uploaded_pending_plan': '已上传待创建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[97, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[98, "'approved': '审核通过',"], [2959, "approved_count = len([m for m in all_materials if m.status == 'approved'])"], [2993, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2996, "if material.status == 'approved':"], [2997, "daily_stats[date_key]['approved'] += 1"], [3009, "daily_df['通过率'] = (daily_df['approved'] / daily_df['total'] * 100).round(1)"], [3011, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [3044, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [3048, "if material.status == 'approved':"], [3049, "author_stats[author]['approved'] += 1"], [3057, "author_df['通过率'] = (author_df['approved'] / author_df['total'] * 100).round(1)"]], "['\\\"]rejected['\\\"]": [[99, "'rejected': '审核拒绝',"], [2960, "rejected_count = len([m for m in all_materials if m.status == 'rejected'])"], [2993, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2998, "elif material.status == 'rejected':"], [2999, "daily_stats[date_key]['rejected'] += 1"], [3011, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [3044, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [3050, "elif material.status == 'rejected':"], [3051, "author_stats[author]['rejected'] += 1"]], "['\\\"]pending_grouping['\\\"]": [[92, "'pending_grouping': '待分组',"], [1916, "material.status = 'pending_grouping'"], [1942, "material.status = 'pending_grouping'"], [2401, "LocalCreative.status == 'pending_grouping'"], [2416, "LocalCreative.status == 'pending_grouping'"], [3909, "LocalCreative.status == 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[94, "'upload_failed': '上传失败',"], [2961, "failed_count = len([m for m in all_materials if m.status == 'upload_failed'])"], [3000, "elif material.status == 'upload_failed':"], [3052, "elif material.status == 'upload_failed':"]], "['\\\"]already_tested['\\\"]": [[100, "'already_tested': '已测试',"]]}, "web_ui_backup_20250723.py": {"['\\\"]processing['\\\"]": [[84, "'processing': '处理中',"], [1432, "st.info(\"管理卡在 'processing' 状态的僵尸素材 - Manage materials stuck in 'processing' status\")"], [1469, "LocalCreative.status == 'processing',"], [1493, "LocalCreative.status == 'processing',"], [1520, "LocalCreative.status == 'processing'"], [2009, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[86, "'uploaded_pending_plan': '已上传待创建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[88, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[89, "'approved': '审核通过',"], [2509, "approved_count = len([m for m in all_materials if m.status == 'approved'])"], [2543, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2546, "if material.status == 'approved':"], [2547, "daily_stats[date_key]['approved'] += 1"], [2559, "daily_df['通过率'] = (daily_df['approved'] / daily_df['total'] * 100).round(1)"], [2561, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [2594, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2598, "if material.status == 'approved':"], [2599, "author_stats[author]['approved'] += 1"], [2607, "author_df['通过率'] = (author_df['approved'] / author_df['total'] * 100).round(1)"]], "['\\\"]rejected['\\\"]": [[90, "'rejected': '审核拒绝',"], [2510, "rejected_count = len([m for m in all_materials if m.status == 'rejected'])"], [2543, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2548, "elif material.status == 'rejected':"], [2549, "daily_stats[date_key]['rejected'] += 1"], [2561, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [2594, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2600, "elif material.status == 'rejected':"], [2601, "author_stats[author]['rejected'] += 1"]], "['\\\"]pending_grouping['\\\"]": [[83, "'pending_grouping': '待分组',"], [1500, "material.status = 'pending_grouping'"], [1526, "material.status = 'pending_grouping'"], [1985, "LocalCreative.status == 'pending_grouping'"], [2000, "LocalCreative.status == 'pending_grouping'"], [2941, "LocalCreative.status == 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[85, "'upload_failed': '上传失败',"], [2511, "failed_count = len([m for m in all_materials if m.status == 'upload_failed'])"], [2550, "elif material.status == 'upload_failed':"], [2602, "elif material.status == 'upload_failed':"]], "['\\\"]already_tested['\\\"]": [[91, "'already_tested': '已测试',"]]}, "ai_temp\\20250808_quick_status_check.py": {"['\\\"]pending_upload['\\\"]": [[35, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [47, "logger.info(f\"   📤 待上传: {status_data['pending_upload']} 个\")"], [56, "pending_upload_count = status_data['pending_upload']"], [153, "pending_upload_count = status_data['pending_upload']"]], "['\\\"]processing['\\\"]": [[35, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [48, "logger.info(f\"   ⏳ 处理中: {status_data['processing']} 个\")"], [55, "processing_count = status_data['processing']"], [108, "LocalCreative.status == 'processing',"], [152, "processing_count = status_data['processing']"]], "['\\\"]uploaded_pending_plan['\\\"]": [[35, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [49, "logger.info(f\"   📋 待创建计划: {status_data['uploaded_pending_plan']} 个\")"], [57, "uploaded_pending_plan_count = status_data['uploaded_pending_plan']"], [113, "LocalCreative.status == 'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[36, "'testing_pending_review', 'approved', 'rejected'"], [50, "logger.info(f\"   🔍 测试中: {status_data['testing_pending_review']} 个\")"]], "['\\\"]approved['\\\"]": [[36, "'testing_pending_review', 'approved', 'rejected'"], [51, "logger.info(f\"   ✅ 已通过: {status_data['approved']} 个\")"]], "['\\\"]rejected['\\\"]": [[36, "'testing_pending_review', 'approved', 'rejected'"], [52, "logger.info(f\"   ❌ 已拒绝: {status_data['rejected']} 个\")"]]}, "ai_temp\\20250808_quick_verification.py": {"['\\\"]pending_upload['\\\"]": [[110, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[106, "LocalCreative.status == 'processing'"]]}, "ai_temp\\20250808_test_atomic_state_integration.py": {"['\\\"]pending_upload['\\\"]": [[61, "pending_count = status_counts.get('pending_upload', 0)"], [68, "LocalCreative.status == 'pending_upload'"], [77, "test_creative.id, 'pending_upload', 'processing'"], [185, "logger.info(f\"   待上传素材: {status_dist.get('pending_upload', 0)} 个\")"]], "['\\\"]processing['\\\"]": [[54, "processing_count = status_counts.get('processing', 0)"], [77, "test_creative.id, 'pending_upload', 'processing'"], [184, "logger.info(f\"   Processing积压: {status_dist.get('processing', 0)} 个\")"]], "['\\\"]uploaded_pending_plan['\\\"]": [[186, "logger.info(f\"   已上传素材: {status_dist.get('uploaded_pending_plan', 0)} 个\")"]]}, "ai_temp\\20250808_test_batch_plans_fix.py": {"['\\\"]pending_upload['\\\"]": [[84, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[80, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[88, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "ai_temp\\20250808_test_batch_processing.py": {"['\\\"]pending_upload['\\\"]": [[121, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [137, "pending_upload = status_counts.get('pending_upload', 0)"], [292, "logger.info(f\"   待上传素材: {status_counts.get('pending_upload', 0)} 个\")"]], "['\\\"]processing['\\\"]": [[121, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [294, "logger.info(f\"   处理中素材: {status_counts.get('processing', 0)} 个\")"]], "['\\\"]uploaded_pending_plan['\\\"]": [[121, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [138, "uploaded_pending_plan = status_counts.get('uploaded_pending_plan', 0)"], [293, "logger.info(f\"   待创建计划: {status_counts.get('uploaded_pending_plan', 0)} 个\")"]], "['\\\"]testing_pending_review['\\\"]": [[122, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]], "['\\\"]approved['\\\"]": [[122, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]], "['\\\"]rejected['\\\"]": [[122, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]], "['\\\"]upload_failed['\\\"]": [[122, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]]}, "ai_temp\\20250809_check_file_existence.py": {"['\\\"]pending_upload['\\\"]": [[36, "LocalCreative.status == 'pending_upload',"]]}, "ai_temp\\20250809_direct_status_conversion.py": {"['\\\"]pending_upload['\\\"]": [[74, "creative.status = 'pending_upload'"], [123, "elif status == 'pending_upload':"]], "['\\\"]pending_grouping['\\\"]": [[42, "LocalCreative.status == 'pending_grouping'"], [121, "if status == 'pending_grouping':"]]}, "ai_temp\\20250809_final_manual_trigger.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan'"], [68, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[73, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_final_plan_creation_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[37, "LocalCreative.status == 'uploaded_pending_plan'"], [43, "LocalCreative.status == 'uploaded_pending_plan'"], [78, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[84, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_hardcoded_fix_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan'"], [73, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[78, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_immediate_plan_creation_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan'"], [64, "LocalCreative.status == 'uploaded_pending_plan'"], [120, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[69, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_manual_plan_creation_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[37, "LocalCreative.status == 'uploaded_pending_plan'"], [67, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[73, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_manual_trigger_plan_creation.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan'"], [68, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[73, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_test_batch_upload_fix.py": {"['\\\"]pending_upload['\\\"]": [[41, "LocalCreative.status == 'pending_upload'"], [83, "LocalCreative.status == 'pending_upload'"]], "['\\\"]pending_grouping['\\\"]": [[35, "LocalCreative.status == 'pending_grouping'"], [77, "LocalCreative.status == 'pending_grouping'"]]}, "ai_temp\\20250809_test_enhanced_health_check.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[46, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "ai_temp\\20250809_test_plan_creation_after_reassignment.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan'"], [68, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[73, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_test_plan_creation_config_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[37, "LocalCreative.status == 'uploaded_pending_plan'"], [82, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[88, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_temp\\20250809_verify_health_check_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[89, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "ai_temp\\check_video_files_status_20250810.py": {"['\\\"]pending_upload['\\\"]": [[193, "if 'pending_upload' in status_groups and len(status_groups['pending_upload']) > 0:"], [196, "'count': len(status_groups['pending_upload']),"], [198, "'files': [f['filename'] for f in status_groups['pending_upload'][:5]]"]], "['\\\"]processing['\\\"]": [[185, "if 'processing' in status_groups and len(status_groups['processing']) > 0:"], [188, "'count': len(status_groups['processing']),"], [190, "'files': [f['filename'] for f in status_groups['processing'][:5]]"]], "['\\\"]new['\\\"]": [[177, "if 'new' in status_groups and len(status_groups['new']) > 0:"], [180, "'count': len(status_groups['new']),"], [182, "'files': [f['filename'] for f in status_groups['new'][:5]]"]]}, "ai_temp\\check_workflow_execution_20250810.py": {"['\\\"]processing['\\\"]": [[76, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"], [165, "if status_counts.get('new', 0) > 0 and status_counts.get('processing', 0) == 0:"], [168, "if status_counts.get('processing', 0) > 0 and status_counts.get('uploaded_pending_plan', 0) == 0:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[76, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"], [168, "if status_counts.get('processing', 0) > 0 and status_counts.get('uploaded_pending_plan', 0) == 0:"], [171, "if status_counts.get('uploaded_pending_plan', 0) > 0 and status_counts.get('testing_pending_review', 0) == 0:"]], "['\\\"]testing_pending_review['\\\"]": [[76, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"], [171, "if status_counts.get('uploaded_pending_plan', 0) > 0 and status_counts.get('testing_pending_review', 0) == 0:"]], "['\\\"]approved['\\\"]": [[76, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]], "['\\\"]new['\\\"]": [[76, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"], [162, "if file_count > 0 and status_counts.get('new', 0) == 0:"], [165, "if status_counts.get('new', 0) > 0 and status_counts.get('processing', 0) == 0:"]]}, "ai_temp\\comprehensive_project_scan_20250810.py": {"['\\\"]pending_upload['\\\"]": [[127, "elif any(status in pattern for status in ['pending_upload', 'uploading', 'processing', 'approved', 'rejected']):"]], "['\\\"]uploading['\\\"]": [[127, "elif any(status in pattern for status in ['pending_upload', 'uploading', 'processing', 'approved', 'rejected']):"]], "['\\\"]processing['\\\"]": [[127, "elif any(status in pattern for status in ['pending_upload', 'uploading', 'processing', 'approved', 'rejected']):"]], "['\\\"]approved['\\\"]": [[127, "elif any(status in pattern for status in ['pending_upload', 'uploading', 'processing', 'approved', 'rejected']):"]], "['\\\"]rejected['\\\"]": [[127, "elif any(status in pattern for status in ['pending_upload', 'uploading', 'processing', 'approved', 'rejected']):"]]}, "ai_temp\\database_migration_20250810_add_state_version.py": {"['\\\"]new['\\\"]": [[87, "\"\"\"将状态为'new'的记录更新为'pending_grouping'\"\"\""], [89, "logger.info(\"🔄 更新'new'状态为'pending_grouping'...\")"], [91, "# 查询当前'new'状态的记录数"], [92, "result = db.execute(text(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'new'\"))"], [96, "logger.info(\"✅ 没有'new'状态的记录需要更新\")"], [99, "logger.info(f\"📊 发现 {count_before} 个'new'状态的记录\")"], [107, "WHERE status = 'new'"], [179, "# 3. 更新'new'状态为'pending_grouping'"]], "['\\\"]pending_grouping['\\\"]": [[87, "\"\"\"将状态为'new'的记录更新为'pending_grouping'\"\"\""], [89, "logger.info(\"🔄 更新'new'状态为'pending_grouping'...\")"], [179, "# 3. 更新'new'状态为'pending_grouping'"]], "MaterialStatus\\.": [[104, "SET status = '{MaterialStatus.PENDING_GROUPING.value}',"]]}, "ai_temp\\diagnose_upload_stuck_20250810.py": {"['\\\"]pending_upload['\\\"]": [[144, "'status': 'pending_upload',"]], "['\\\"]uploading['\\\"]": [[36, "LocalCreative.status == 'uploading',"], [141, "LocalCreative.status == 'uploading',"]], "['\\\"]upload_failed['\\\"]": [[98, "LocalCreative.status == 'upload_failed',"]]}, "ai_temp\\fix_file_ingestion_config_20250810.py": {"['\\\"]processing['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]], "['\\\"]testing_pending_review['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]], "['\\\"]approved['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]], "['\\\"]new['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved']:"]]}, "ai_temp\\force_test_account_plan_creation_20250810.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan',"], [126, "LocalCreative.status == 'uploaded_pending_plan',"], [144, "LocalCreative.status == 'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[135, "LocalCreative.status == 'testing_pending_review',"]]}, "ai_temp\\manual_celery_worker_20250810.py": {"['\\\"]processing['\\\"]": [[125, "LocalCreative.status == 'processing'"], [190, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[145, "LocalCreative.status == 'uploaded_pending_plan'"], [190, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[190, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]new['\\\"]": [[190, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]]}, "ai_temp\\manual_test_account_plan_creation_20250810.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[36, "LocalCreative.status == 'uploaded_pending_plan',"], [125, "if account_type == 'TEST' and status == 'uploaded_pending_plan':"], [127, "elif account_type == 'DELIVERY' and status == 'uploaded_pending_plan':"]]}, "ai_temp\\manual_workflow_processor_20250810.py": {"['\\\"]processing['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"], [126, "if initial_status.get('processing', 0) > 0:"], [155, "for status in ['processing', 'uploaded_pending_plan', 'approved']:"], [167, "processing_reduced = initial_status.get('processing', 0) - final_status.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"], [138, "if initial_status.get('uploaded_pending_plan', 0) > 0:"], [155, "for status in ['processing', 'uploaded_pending_plan', 'approved']:"], [168, "pending_reduced = initial_status.get('uploaded_pending_plan', 0) - final_status.get('uploaded_pending_plan', 0)"]], "['\\\"]testing_pending_review['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]approved['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"], [155, "for status in ['processing', 'uploaded_pending_plan', 'approved']:"], [169, "approved_increased = final_status.get('approved', 0) - initial_status.get('approved', 0)"]], "['\\\"]rejected['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]new['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[101, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected', 'upload_failed']:"]]}, "ai_temp\\performance_benchmark_20250810.py": {"MaterialStatus\\.": [[45, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [70, "MaterialStatus.PROCESSING.value"], [79, "MaterialStatus.PROCESSING.value,"], [98, "MaterialStatus.PROCESSING.value"], [107, "MaterialStatus.PROCESSING.value,"], [174, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [181, "test_data = [(c.id, c.status, MaterialStatus.PROCESSING.value) for c in creatives]"], [219, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [253, "MaterialStatus.PROCESSING.value"], [265, "MaterialStatus.PROCESSING.value,"]]}, "ai_temp\\process_remaining_files_20250810.py": {"['\\\"]uploading['\\\"]": [[64, "LocalCreative.status == 'uploading'"]]}, "ai_temp\\replace_hardcoded_statuses_20250810.py": {"['\\\"]pending_upload['\\\"]": [[23, "\"'pending_upload'\": \"MaterialStatus.PENDING_UPLOAD.value\","], [24, "'\"pending_upload\"': \"MaterialStatus.PENDING_UPLOAD.value\","]], "['\\\"]uploading['\\\"]": [[25, "\"'uploading'\": \"MaterialStatus.UPLOADING.value\","], [26, "'\"uploading\"': \"MaterialStatus.UPLOADING.value\","]], "['\\\"]processing['\\\"]": [[27, "\"'processing'\": \"MaterialStatus.PROCESSING.value\","], [28, "'\"processing\"': \"MaterialStatus.PROCESSING.value\","]], "['\\\"]uploaded_pending_plan['\\\"]": [[29, "\"'uploaded_pending_plan'\": \"MaterialStatus.UPLOADED_PENDING_PLAN.value\","], [30, "'\"uploaded_pending_plan\"': \"MaterialStatus.UPLOADED_PENDING_PLAN.value\","]], "['\\\"]testing_pending_review['\\\"]": [[31, "\"'testing_pending_review'\": \"MaterialStatus.TESTING_PENDING_REVIEW.value\","], [32, "'\"testing_pending_review\"': \"MaterialStatus.TESTING_PENDING_REVIEW.value\","]], "['\\\"]approved['\\\"]": [[33, "\"'approved'\": \"MaterialStatus.APPROVED.value\","], [34, "'\"approved\"': \"MaterialStatus.APPROVED.value\","]], "['\\\"]rejected['\\\"]": [[35, "\"'rejected'\": \"MaterialStatus.REJECTED.value\","], [36, "'\"rejected\"': \"MaterialStatus.REJECTED.value\","]], "['\\\"]harvested['\\\"]": [[37, "\"'harvested'\": \"MaterialStatus.HARVESTED.value\","], [38, "'\"harvested\"': \"MaterialStatus.HARVESTED.value\","]], "['\\\"]new['\\\"]": [[19, "\"'new'\": \"MaterialStatus.NEW.value\","], [20, "'\"new\"': \"MaterialStatus.NEW.value\","]], "['\\\"]pending_grouping['\\\"]": [[21, "\"'pending_grouping'\": \"MaterialStatus.PENDING_GROUPING.value\","], [22, "'\"pending_grouping\"': \"MaterialStatus.PENDING_GROUPING.value\","]], "['\\\"]upload_failed['\\\"]": [[41, "\"'upload_failed'\": \"MaterialStatus.UPLOAD_FAILED.value\","], [42, "'\"upload_failed\"': \"MaterialStatus.UPLOAD_FAILED.value\","]], "['\\\"]quality_failed['\\\"]": [[43, "\"'quality_failed'\": \"MaterialStatus.QUALITY_FAILED.value\","], [44, "'\"quality_failed\"': \"MaterialStatus.QUALITY_FAILED.value\","]], "['\\\"]plan_creation_failed['\\\"]": [[45, "\"'plan_creation_failed'\": \"MaterialStatus.PLAN_CREATION_FAILED.value\","], [46, "'\"plan_creation_failed\"': \"MaterialStatus.PLAN_CREATION_FAILED.value\","]], "['\\\"]already_tested['\\\"]": [[39, "\"'already_tested'\": \"MaterialStatus.ALREADY_TESTED.value\","], [40, "'\"already_tested\"': \"MaterialStatus.ALREADY_TESTED.value\","]], "MaterialStatus\\.": [[19, "\"'new'\": \"MaterialStatus.NEW.value\","], [20, "'\"new\"': \"MaterialStatus.NEW.value\","], [21, "\"'pending_grouping'\": \"MaterialStatus.PENDING_GROUPING.value\","], [22, "'\"pending_grouping\"': \"MaterialStatus.PENDING_GROUPING.value\","], [23, "\"'pending_upload'\": \"MaterialStatus.PENDING_UPLOAD.value\","], [24, "'\"pending_upload\"': \"MaterialStatus.PENDING_UPLOAD.value\","], [25, "\"'uploading'\": \"MaterialStatus.UPLOADING.value\","], [26, "'\"uploading\"': \"MaterialStatus.UPLOADING.value\","], [27, "\"'processing'\": \"MaterialStatus.PROCESSING.value\","], [28, "'\"processing\"': \"MaterialStatus.PROCESSING.value\","], [29, "\"'uploaded_pending_plan'\": \"MaterialStatus.UPLOADED_PENDING_PLAN.value\","], [30, "'\"uploaded_pending_plan\"': \"MaterialStatus.UPLOADED_PENDING_PLAN.value\","], [31, "\"'testing_pending_review'\": \"MaterialStatus.TESTING_PENDING_REVIEW.value\","], [32, "'\"testing_pending_review\"': \"MaterialStatus.TESTING_PENDING_REVIEW.value\","], [33, "\"'approved'\": \"MaterialStatus.APPROVED.value\","], [34, "'\"approved\"': \"MaterialStatus.APPROVED.value\","], [35, "\"'rejected'\": \"MaterialStatus.REJECTED.value\","], [36, "'\"rejected\"': \"MaterialStatus.REJECTED.value\","], [37, "\"'harvested'\": \"MaterialStatus.HARVESTED.value\","], [38, "'\"harvested\"': \"MaterialStatus.HARVESTED.value\","], [39, "\"'already_tested'\": \"MaterialStatus.ALREADY_TESTED.value\","], [40, "'\"already_tested\"': \"MaterialStatus.ALREADY_TESTED.value\","], [41, "\"'upload_failed'\": \"MaterialStatus.UPLOAD_FAILED.value\","], [42, "'\"upload_failed\"': \"MaterialStatus.UPLOAD_FAILED.value\","], [43, "\"'quality_failed'\": \"MaterialStatus.QUALITY_FAILED.value\","], [44, "'\"quality_failed\"': \"MaterialStatus.QUALITY_FAILED.value\","], [45, "\"'plan_creation_failed'\": \"MaterialStatus.PLAN_CREATION_FAILED.value\","], [46, "'\"plan_creation_failed\"': \"MaterialStatus.PLAN_CREATION_FAILED.value\","]]}, "ai_temp\\test_critical_fixes_20250810.py": {"['\\\"]new['\\\"]": [[37, "LocalCreative.status == 'new'"]]}, "ai_temp\\test_enhanced_state_manager_20250810.py": {"MaterialStatus\\.": [[38, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [54, "MaterialStatus.PROCESSING.value,"], [65, "MaterialStatus.PROCESSING.value,"], [91, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [104, "MaterialStatus.PROCESSING.value"], [169, "MaterialStatus.HARVESTED.value  # 通常不能直接转换到harvested"], [180, "MaterialStatus.HARVESTED.value,"], [202, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [211, "MaterialStatus.PROCESSING.value"], [216, "MaterialStatus.PROCESSING.value,"]]}, "ai_temp\\test_fixed_upload_20250810.py": {"['\\\"]pending_upload['\\\"]": [[51, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [92, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]], "['\\\"]uploading['\\\"]": [[51, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [92, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[51, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [92, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[51, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [92, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]]}, "ai_temp\\test_scheduled_health_monitor_20250810.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[38, "LocalCreative.status == 'uploaded_pending_plan'"], [48, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]testing_pending_review['\\\"]": [[42, "LocalCreative.status == 'testing_pending_review'"], [48, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]]}, "ai_temp\\test_upload_fix_final_20250810.py": {"['\\\"]pending_upload['\\\"]": [[91, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [126, "logger.info(f\"pending_upload状态: {db_status.get('pending_upload', 0)}\")"]], "['\\\"]uploading['\\\"]": [[34, "LocalCreative.status == 'uploading'"], [91, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"], [125, "logger.info(f\"uploading状态: {db_status.get('uploading', 0)}\")"]], "['\\\"]uploaded_pending_plan['\\\"]": [[91, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[91, "for status in ['pending_upload', 'uploading', 'uploaded_pending_plan', 'upload_failed']:"]]}, "ai_temp\\test_workflow_fixes_20250810.py": {"['\\\"]uploading['\\\"]": [[75, "for status in ['processing', 'uploaded_pending_plan', 'uploading', 'approved', 'rejected']:"]], "['\\\"]processing['\\\"]": [[39, "LocalCreative.status == 'processing'"], [75, "for status in ['processing', 'uploaded_pending_plan', 'uploading', 'approved', 'rejected']:"], [103, "LocalCreative.status == 'processing',"], [154, "logger.info(f\"📊 当前状态: processing={status_counts.get('processing', 0)}, \""]], "['\\\"]uploaded_pending_plan['\\\"]": [[75, "for status in ['processing', 'uploaded_pending_plan', 'uploading', 'approved', 'rejected']:"], [108, "LocalCreative.status == 'uploaded_pending_plan',"], [155, "f\"uploaded_pending_plan={status_counts.get('uploaded_pending_plan', 0)}\")"]], "['\\\"]approved['\\\"]": [[75, "for status in ['processing', 'uploaded_pending_plan', 'uploading', 'approved', 'rejected']:"]], "['\\\"]rejected['\\\"]": [[75, "for status in ['processing', 'uploaded_pending_plan', 'uploading', 'approved', 'rejected']:"]]}, "ai_temp\\verify_workflow_config_20250810.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[123, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "ai_temp\\workflow_recovery_plan_20250810.py": {"['\\\"]processing['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[180, "LocalCreative.status == 'uploaded_pending_plan'"], [215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]testing_pending_review['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]approved['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]rejected['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]new['\\\"]": [[215, "for status in ['new', 'processing', 'uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected']:"]]}, "ai_tools\\ai_tool_20250720_complete_material_analytics.py": {"['\\\"]pending_upload['\\\"]": [[156, "'pending_upload': '待上传',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [558, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]processing['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1140, "WHEN 'processing' THEN 3"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1352, "SUM(CASE WHEN lc.status = 'processing' THEN 1 ELSE 0 END) as processing_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[157, "'uploaded_pending_plan': '已上传待建计划',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [569, "AVG(CASE WHEN lc.status = 'uploaded_pending_plan'"], [696, "AND lc.status IN ('creating_plan', 'uploaded_pending_plan')"], [711, "elif row.status == 'uploaded_pending_plan' and row.count > 10:"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1141, "WHEN 'uploaded_pending_plan' THEN 4"], [1161, "'uploaded_pending_plan': '已上传待建计划', 'creating_plan': '创建计划中',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1353, "SUM(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 ELSE 0 END) as uploaded_pending_plan_count,"], [1359, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"]], "['\\\"]testing_pending_review['\\\"]": [[159, "'testing_pending_review': '测试待审核',"], [535, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [558, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [565, "AVG(CASE WHEN lc.status = 'testing_pending_review'"], [897, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [925, "if result.status == 'testing_pending_review' and result.hours_since_update > 12:"], [1143, "WHEN 'testing_pending_review' THEN 6"], [1162, "'testing_pending_review': '测试待审核', 'approved': '审核通过',"], [1225, "elif row.status == 'testing_pending_review' and row.avg_duration_hours and row.avg_duration_hours > 24:"], [1286, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [1355, "SUM(CASE WHEN lc.status = 'testing_pending_review' THEN 1 ELSE 0 END) as testing_pending_review_count,"], [1359, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"]], "['\\\"]approved['\\\"]": [[160, "'approved': '审核通过',"], [411, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_materials,"], [485, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [754, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [797, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_materials,"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1144, "WHEN 'approved' THEN 7"], [1162, "'testing_pending_review': '测试待审核', 'approved': '审核通过',"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1349, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [1610, "AND lc.status = 'approved'"]], "['\\\"]rejected['\\\"]": [[161, "'rejected': '审核拒绝',"], [412, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_materials,"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [798, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_materials"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [994, "AND lc.status IN ('upload_failed', 'rejected')"], [1006, "rejected = len([m for m in failed_materials if m.status == 'rejected'])"], [1145, "WHEN 'rejected' THEN 8"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1350, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]new['\\\"]": [[155, "'new': '新建',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1138, "WHEN 'new' THEN 1"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]pending_grouping['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1139, "WHEN 'pending_grouping' THEN 2"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1351, "SUM(CASE WHEN lc.status = 'pending_grouping' THEN 1 ELSE 0 END) as pending_grouping_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[162, "'upload_failed': '上传失败'"], [413, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [559, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"], [755, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_count"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [994, "AND lc.status IN ('upload_failed', 'rejected')"], [1005, "upload_failed = len([m for m in failed_materials if m.status == 'upload_failed'])"], [1021, "'失败类型': '上传失败' if material.status == 'upload_failed' else '审核拒绝',"], [1147, "WHEN 'upload_failed' THEN 10"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1356, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as upload_failed_count,"], [1360, "'upload_failed', 'already_tested') THEN 1 ELSE 0 END) as other_status_count"]], "['\\\"]already_tested['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1146, "WHEN 'already_tested' THEN 9"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1357, "SUM(CASE WHEN lc.status = 'already_tested' THEN 1 ELSE 0 END) as already_tested_count,"], [1360, "'upload_failed', 'already_tested') THEN 1 ELSE 0 END) as other_status_count"]]}, "ai_tools\\ai_tool_20250720_enhanced_material_analytics.py": {"['\\\"]pending_upload['\\\"]": [[424, "LocalCreative.status.in_(['pending_upload', 'creating_plan', 'testing_pending_review'])"], [593, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[594, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[424, "LocalCreative.status.in_(['pending_upload', 'creating_plan', 'testing_pending_review'])"], [596, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[319, "if stat.status == 'approved')"], [492, "LocalCreative.status == 'approved',"], [531, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved"], [597, "'approved': '审核通过',"], [858, "func.sum(func.case([(LocalCreative.status == 'approved', 1)], else_=0)).label('approved_materials'),"]], "['\\\"]rejected['\\\"]": [[598, "'rejected': '审核拒绝',"], [794, "LocalCreative.status == 'rejected',"], [859, "func.sum(func.case([(LocalCreative.status == 'rejected', 1)], else_=0)).label('rejected_materials')"]], "['\\\"]new['\\\"]": [[592, "'new': '新建',"]], "['\\\"]pending_grouping['\\\"]": [[601, "'pending_grouping': '待分组'"]], "['\\\"]upload_failed['\\\"]": [[599, "'upload_failed': '上传失败',"], [763, "LocalCreative.status == 'upload_failed',"]], "['\\\"]already_tested['\\\"]": [[600, "'already_tested': '已测试',"]]}, "ai_tools\\ai_tool_20250720_lifecycle_monitor.py": {"['\\\"]approved['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"], [41, "COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved,"], [47, "AND lc.status IN ('approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"], [42, "COUNT(CASE WHEN lc.status = 'rejected' THEN 1 END) as rejected"], [47, "AND lc.status IN ('approved', 'rejected')"]], "['\\\"]already_tested['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"]]}, "ai_tools\\ai_tool_20250720_monitoring_dashboard.py": {"['\\\"]pending_upload['\\\"]": [[44, "{\"status\":\"pending_upload\",\"count\":25}"]], "['\\\"]uploading['\\\"]": [[45, "{\"status\":\"uploading\",\"count\":8}"]], "['\\\"]approved['\\\"]": [[43, "return '''{\"status\":\"approved\",\"count\":1481}"]], "['\\\"]rejected['\\\"]": [[48, "{\"status\":\"rejected\",\"count\":23}'''"]], "['\\\"]harvested['\\\"]": [[51, "{\"harvest_status\":\"harvested\",\"count\":281}"]]}, "ai_tools\\ai_tool_20250720_status_duration_test.py": {"['\\\"]pending_upload['\\\"]": [[26, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[37, "AVG(CASE WHEN lc.status = 'uploaded_pending_plan'"], [79, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [89, "'uploaded_pending_plan': '已上传待建计划'"], [105, "elif row.status == 'uploaded_pending_plan' and row.max_hours > 2:"], [123, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"]], "['\\\"]testing_pending_review['\\\"]": [[26, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [33, "AVG(CASE WHEN lc.status = 'testing_pending_review'"], [79, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [87, "'testing_pending_review': '测试待审核',"], [101, "if row.status == 'testing_pending_review' and row.max_hours > 24:"], [123, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [143, "if result.status == 'testing_pending_review' and result.hours_since_update > 12:"]], "['\\\"]upload_failed['\\\"]": [[27, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"]]}, "ai_tools\\ai_tool_20250720_status_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]processing['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [72, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"], [96, "SUM(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 ELSE 0 END) as uploaded_pending_plan_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[40, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [72, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"], [97, "SUM(CASE WHEN lc.status = 'testing_pending_review' THEN 1 ELSE 0 END) as testing_pending_review_count,"], [100, "'testing_pending_review', 'already_tested') THEN 1 ELSE 0 END) as other_count"]], "['\\\"]approved['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [93, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]rejected['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [94, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]new['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]pending_grouping['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]already_tested['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"], [98, "SUM(CASE WHEN lc.status = 'already_tested' THEN 1 ELSE 0 END) as already_tested_count,"], [100, "'testing_pending_review', 'already_tested') THEN 1 ELSE 0 END) as other_count"]]}, "ai_tools\\ai_tool_20250720_system_monitoring_page.py": {"['\\\"]pending_upload['\\\"]": [[408, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[409, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[411, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[412, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[413, "'rejected': '审核拒绝',"]], "['\\\"]new['\\\"]": [[407, "'new': '新建',"]], "['\\\"]pending_grouping['\\\"]": [[416, "'pending_grouping': '待分组'"]], "['\\\"]upload_failed['\\\"]": [[414, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[415, "'already_tested': '已测试',"]]}, "ai_tools\\ai_tool_20250720_webui_monitoring_integration.py": {"['\\\"]pending_upload['\\\"]": [[279, "{\"status\":\"pending_upload\",\"count\":25}"]], "['\\\"]uploading['\\\"]": [[97, "WHERE status IN ('uploading', 'plan_pending', 'creating_plan')"], [280, "{\"status\":\"uploading\",\"count\":8}"], [287, "return '''{\"status\":\"uploading\",\"stuck_count\":8}"]], "['\\\"]approved['\\\"]": [[70, "COUNT(CASE WHEN harvest_status = 'not_harvested' AND status = 'approved' THEN 1 END) as pending_harvest"], [72, "WHERE status = 'approved'"], [150, "approved_count = material_dist.get('approved', 0)"], [278, "return '''{\"status\":\"approved\",\"count\":1481}"]], "['\\\"]rejected['\\\"]": [[283, "{\"status\":\"rejected\",\"count\":23}'''"]], "['\\\"]harvested['\\\"]": [[69, "COUNT(CASE WHEN harvest_status = 'harvested' THEN 1 END) as harvested_count,"]]}, "ai_tools\\ai_tool_20250720_workflow_optimization_test.py": {"['\\\"]pending_upload['\\\"]": [[121, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[62, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [71, "'uploaded_pending_plan': '已上传待建计划'"]], "['\\\"]testing_pending_review['\\\"]": [[62, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [69, "'testing_pending_review': '测试待审核',"], [121, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [123, "AVG(CASE WHEN lc.status = 'testing_pending_review'"]], "['\\\"]upload_failed['\\\"]": [[122, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"]]}, "ai_tools\\ai_tool_20250721_file_management_analysis.py": {"['\\\"]processing['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"], [245, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]pending_grouping['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]upload_failed['\\\"]": [[184, "AND lc.status = 'upload_failed'"], [245, "AND lc.status IN ('processing', 'upload_failed')"]]}, "ai_tools\\ai_tool_20250721_file_management_fixes.py": {"['\\\"]processing['\\\"]": [[205, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]pending_grouping['\\\"]": [[294, "SET status = 'pending_grouping', updated_at = NOW()"]], "['\\\"]upload_failed['\\\"]": [[205, "AND lc.status IN ('processing', 'upload_failed')"], [299, "AND lc.status = 'upload_failed'"]]}, "ai_tools\\ai_tool_20250721_quick_file_fix.py": {"['\\\"]processing['\\\"]": [[34, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]pending_grouping['\\\"]": [[85, "SET status = 'pending_grouping', updated_at = NOW()"]], "['\\\"]upload_failed['\\\"]": [[34, "AND lc.status IN ('processing', 'upload_failed')"], [90, "AND lc.status = 'upload_failed'"], [173, "if row.status == 'upload_failed':"]]}, "ai_tools\\ai_tool_20250721_system_monitoring.py": {"['\\\"]processing['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"]], "['\\\"]uploaded_pending_plan['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"], [66, "AND lc.status IN ('uploaded_pending_plan', 'creating_plan', 'testing_pending_review')"]], "['\\\"]testing_pending_review['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"], [66, "AND lc.status IN ('uploaded_pending_plan', 'creating_plan', 'testing_pending_review')"]], "['\\\"]approved['\\\"]": [[29, "SUM(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 ELSE 0 END) as completed_count,"]], "['\\\"]rejected['\\\"]": [[29, "SUM(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 ELSE 0 END) as completed_count,"]], "['\\\"]upload_failed['\\\"]": [[28, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_count,"], [92, "AND lc.status = 'upload_failed'"]]}, "ai_tools\\ai_tool_20250722_appeal_fixes.py": {"['\\\"]rejected['\\\"]": [[313, "appeal_needed_statuses = ['rejected', 'under_review', 'failed_review']"], [746, "appealable_statuses = ['rejected', 'under_review', 'failed_review']"], [939, "AND ap.status IN ('rejected', 'under_review', 'failed_review')"]]}, "ai_tools\\ai_tool_20250722_celery_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[44, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan'])"], [48, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [53, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan'])"], [57, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [61, "old_status_check = \"\"\"if locked_creative.status != 'uploaded_pending_plan':\"\"\""], [62, "new_status_check = \"\"\"if locked_creative.status not in ['uploaded_pending_plan', 'approved']:\"\"\""]], "['\\\"]approved['\\\"]": [[48, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [57, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [62, "new_status_check = \"\"\"if locked_creative.status not in ['uploaded_pending_plan', 'approved']:\"\"\""]]}, "ai_tools\\ai_tool_20250722_comprehensive_material_verification.py": {"['\\\"]new['\\\"]": [[129, "WHERE lc.status = 'new'"]]}, "ai_tools\\ai_tool_20250722_deep_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[80, "AND lc.status IN ('uploaded_pending_plan', 'approved', 'creating_plan')) as available_materials"], [147, "AND lc.status IN ('uploaded_pending_plan', 'approved')) as available_materials"], [203, "if \"LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])\" in content:"], [206, "if \"locked_creative.status not in ['uploaded_pending_plan', 'approved']\" in content:"], [217, "if \"if locked_creative.status != 'uploaded_pending_plan':\" in content:"]], "['\\\"]approved['\\\"]": [[80, "AND lc.status IN ('uploaded_pending_plan', 'approved', 'creating_plan')) as available_materials"], [114, "SET status = 'approved',"], [147, "AND lc.status IN ('uploaded_pending_plan', 'approved')) as available_materials"], [203, "if \"LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])\" in content:"], [206, "if \"locked_creative.status not in ['uploaded_pending_plan', 'approved']\" in content:"]]}, "ai_tools\\ai_tool_20250722_deep_log_analysis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[35, "'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[37, "'testing_pending_review',"]], "['\\\"]approved['\\\"]": [[38, "'approved',  # 或 'rejected'"]], "['\\\"]rejected['\\\"]": [[38, "'approved',  # 或 'rejected'"]], "['\\\"]new['\\\"]": [[33, "'new',"]], "['\\\"]pending_grouping['\\\"]": [[34, "'pending_grouping',"]]}, "ai_tools\\ai_tool_20250722_deploy_verification.py": {"['\\\"]approved['\\\"]": [[96, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed_uploads"], [162, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]rejected['\\\"]": [[96, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed_uploads"], [162, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]upload_failed['\\\"]": [[95, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_uploads,"], [161, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,"]]}, "ai_tools\\ai_tool_20250722_duplicate_plan_emergency_fix.py": {"['\\\"]already_tested['\\\"]": [[244, "SET status = 'already_tested',"], [374, "'already_tested',"]]}, "ai_tools\\ai_tool_20250722_emergency_fix.py": {"['\\\"]pending_upload['\\\"]": [[144, "('pending_upload', 48),  # pending_upload超过48小时"], [165, "new_status = 'pending_upload'"], [166, "elif status == 'pending_upload':"], [169, "new_status = 'pending_upload'  # 重试上传"]], "['\\\"]uploaded_pending_plan['\\\"]": [[74, "SET status = 'uploaded_pending_plan',"], [102, "WHERE status = 'uploaded_pending_plan'"], [120, "WHERE status = 'uploaded_pending_plan'"], [211, "WHERE lc.status = 'uploaded_pending_plan'"], [219, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[118, "SET status = 'approved',"]], "['\\\"]new['\\\"]": [[143, "('new', 72),  # new状态超过72小时"], [164, "if status == 'new':"], [167, "new_status = 'new'  # 重新开始"]], "['\\\"]upload_failed['\\\"]": [[145, "('upload_failed', 24),  # upload_failed超过24小时"], [168, "elif status == 'upload_failed':"]]}, "ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[373, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' AND c.id IS NOT NULL THEN 1 END) as pending_but_has_plan,"]], "['\\\"]testing_pending_review['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]approved['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]rejected['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]new['\\\"]": [[372, "COUNT(CASE WHEN lc.status = 'new' AND pc.id IS NOT NULL THEN 1 END) as new_but_uploaded,"]], "['\\\"]already_tested['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]]}, "ai_tools\\ai_tool_20250722_export_new_materials_excel.py": {"['\\\"]new['\\\"]": [[64, "WHERE lc.status = 'new'"], [290, "WHERE status = 'new';"]]}, "ai_tools\\ai_tool_20250722_final_solution.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[115, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [144, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]], "['\\\"]approved['\\\"]": [[115, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [144, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]]}, "ai_tools\\ai_tool_20250722_fix_scheduler_logic.py": {"['\\\"]already_tested['\\\"]": [[54, "'already_tested', '重复', 'duplicate'"], [95, "locked_creative.status = 'already_tested'"], [124, "locked_creative.status = 'already_tested'"], [137, "locked_creative.status = 'already_tested'"], [194, "locked_creative.status = 'already_tested'"], [223, "locked_creative.status = 'already_tested'"], [236, "locked_creative.status = 'already_tested'"]]}, "ai_tools\\ai_tool_20250722_handle_missing_files.py": {"['\\\"]new['\\\"]": [[44, "WHERE lc.status = 'new'"]]}, "ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[131, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[237, "SET status = 'testing_pending_review',"], [246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]approved['\\\"]": [[246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]already_tested['\\\"]": [[117, "SET status = 'already_tested',"]]}, "ai_tools\\ai_tool_20250722_log_analysis_verification.py": {"['\\\"]testing_pending_review['\\\"]": [[277, "SET status = 'testing_pending_review',"]], "['\\\"]new['\\\"]": [[186, "WHERE status = 'new'"], [220, "WHERE lc.status = 'new'"]]}, "ai_tools\\ai_tool_20250722_platform_creative_fix.py": {"['\\\"]approved['\\\"]": [[42, "WHERE lc.status = 'approved'"], [64, "WHERE lc.status = 'approved'"], [158, "WHERE lc.status = 'approved'"], [219, "WHERE lc.status = 'approved'"], [253, "WHERE lc.status = 'approved'"], [285, "WHERE lc.status = 'approved'"]]}, "ai_tools\\ai_tool_20250722_realtime_monitor.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[29, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [58, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]], "['\\\"]approved['\\\"]": [[29, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [58, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]]}, "ai_tools\\ai_tool_20250722_status_flow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[106, "if 'approved' in content and 'uploaded_pending_plan' in content:"], [111, "if ('approved' in line and 'uploaded_pending_plan' in line) or \\"], [112, "('status' in line and '=' in line and ('approved' in line or 'uploaded_pending_plan' in line)):"], [153, "SET status = 'uploaded_pending_plan',"], [184, "SET status = 'uploaded_pending_plan',"], [223, "AND lc.status IN ('approved', 'uploaded_pending_plan')"], [243, "WHERE lc.status = 'uploaded_pending_plan'"], [312, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [332, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[44, "WHERE lc.status = 'approved'"], [106, "if 'approved' in content and 'uploaded_pending_plan' in content:"], [111, "if ('approved' in line and 'uploaded_pending_plan' in line) or \\"], [112, "('status' in line and '=' in line and ('approved' in line or 'uploaded_pending_plan' in line)):"], [138, "WHERE lc.status = 'approved'"], [155, "WHERE status = 'approved'"], [161, "WHERE lc.status = 'approved'"], [173, "WHERE lc.status = 'approved'"], [223, "AND lc.status IN ('approved', 'uploaded_pending_plan')"], [312, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"]]}, "ai_tools\\ai_tool_20250722_status_monitor.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[33, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [53, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[33, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"]]}, "ai_tools\\ai_tool_20250722_system_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[168, "COUNT(CASE WHEN m.status = 'uploaded_pending_plan' THEN 1 END) as pending_plan_count"], [251, "'uploaded', 'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]approved['\\\"]": [[252, "'plan_created', 'monitoring', 'approved', 'rejected'"]], "['\\\"]rejected['\\\"]": [[252, "'plan_created', 'monitoring', 'approved', 'rejected'"]]}, "ai_tools\\ai_tool_20250722_upload_stability_analysis.py": {"['\\\"]upload_failed['\\\"]": [[120, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_count"]]}, "ai_tools\\ai_tool_20250722_validation_dashboard.py": {"['\\\"]approved['\\\"]": [[35, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]rejected['\\\"]": [[35, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]upload_failed['\\\"]": [[34, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,"]]}, "ai_tools\\ai_tool_20250722_verify_new_materials.py": {"['\\\"]new['\\\"]": [[48, "WHERE lc.status = 'new'"], [74, "WHERE status = 'new'"], [111, "WHERE lc.status = 'new'"]], "['\\\"]already_tested['\\\"]": [[176, "SET status = 'already_tested',"]]}, "ai_tools\\ai_tool_20250722_verify_recovered_files.py": {"['\\\"]new['\\\"]": [[49, "WHERE lc.status = 'new'"], [118, "WHERE lc.status = 'new'"], [134, "WHERE lc.status != 'new'"]], "['\\\"]already_tested['\\\"]": [[250, "SET status = 'already_tested',"]]}, "ai_tools\\ai_tool_20250722_workflow_analysis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[132, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 END) as pending_materials,"], [203, "if 'uploaded_pending_plan' in line and 'filter' in line:"]], "['\\\"]approved['\\\"]": [[131, "COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved_materials,"], [276, "WHERE lc.status = 'approved'"]]}, "ai_tools\\ai_tool_20250722_workflow_monitor.py": {"['\\\"]pending_upload['\\\"]": [[137, "WHEN status = 'pending_upload' THEN '2-待上传'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]], "['\\\"]processing['\\\"]": [[138, "WHEN status = 'processing' THEN '3-上传中'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[139, "WHEN status = 'uploaded_pending_plan' THEN '4-待建计划'"], [149, "'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[141, "WHEN status = 'testing_pending_review' THEN '6-待审核'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]approved['\\\"]": [[142, "WHEN status = 'approved' THEN '7-已通过'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[143, "WHEN status = 'rejected' THEN '8-已拒绝'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]new['\\\"]": [[40, "if row.status == 'new':"], [136, "WHEN status = 'new' THEN '1-待处理'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]]}, "ai_tools\\ai_tool_20250722_workflow_restoration.py": {"['\\\"]pending_upload['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]processing['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[110, "'uploaded_pending_plan', 'creating_plan',"], [333, "'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[111, "'testing_pending_review', 'approved', 'rejected'"], [252, "SET status = 'testing_pending_review',"], [334, "'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]approved['\\\"]": [[64, "if row.status == 'approved' and row.has_campaign == 0:"], [104, "logger.info(\"  - 'approved': 498个无campaign - 状态含义混乱\")"], [111, "'testing_pending_review', 'approved', 'rejected'"], [113, "'problematic_statuses': ['already_tested', 'approved'],"], [139, "WHERE lc.status = 'approved'"], [162, "logger.info(\"  'approved'状态是本地检测逻辑错误产生的\")"], [172, "WHERE status = 'approved'"], [334, "'testing_pending_review', 'approved', 'rejected']:"], [345, "WHERE status IN ('already_tested', 'approved')"]], "['\\\"]rejected['\\\"]": [[111, "'testing_pending_review', 'approved', 'rejected'"], [334, "'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]new['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [170, "SET status = 'new',"], [233, "SET status = 'new',"], [329, "if row.status == 'new':"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]already_tested['\\\"]": [[70, "elif row.status == 'already_tested' and row.count > 1000:"], [103, "logger.info(\"  - 'already_tested': 1685个 - 这是本地检测逻辑产生的\")"], [113, "'problematic_statuses': ['already_tested', 'approved'],"], [114, "'local_detection_artifacts': ['already_tested']"], [209, "WHERE lc.status = 'already_tested'"], [235, "WHERE status = 'already_tested'"], [254, "WHERE status = 'already_tested'"], [345, "WHERE status IN ('already_tested', 'approved')"]]}, "ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py": {"['\\\"]pending_upload['\\\"]": [[101, "WHERE lc.status IN ('upload_failed', 'pending_upload')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [61, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 END) as uploaded_pending_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [121, "WHERE lc.status IN ('pending_grouping', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]new['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [59, "COUNT(CASE WHEN lc.status = 'new' THEN 1 END) as new_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]pending_grouping['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [60, "COUNT(CASE WHEN lc.status = 'pending_grouping' THEN 1 END) as pending_grouping_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [121, "WHERE lc.status IN ('pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [432, "LocalCreative.status == 'pending_grouping',"]], "['\\\"]upload_failed['\\\"]": [[101, "WHERE lc.status IN ('upload_failed', 'pending_upload')"]]}, "ai_tools\\ai_tool_20250723_immediate_grouping_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[141, "LocalCreative.status.in_(['pending_grouping', 'uploaded_pending_plan']),"]], "['\\\"]pending_grouping['\\\"]": [[141, "LocalCreative.status.in_(['pending_grouping', 'uploaded_pending_plan']),"]]}, "ai_tools\\ai_tool_20250801_workflow_recovery_executor.py": {"['\\\"]new['\\\"]": [[199, "creative.status = 'new'"]], "['\\\"]upload_failed['\\\"]": [[188, "LocalCreative.status == 'upload_failed',"]], "WorkflowStatus\\.": [[101, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [115, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"]]}, "ai_tools\\ai_tool_20250801_workflow_resilience_system.py": {"['\\\"]processing['\\\"]": [[105, "'new', 'processing'"]], "['\\\"]rejected['\\\"]": [[102, "'rejected'"]], "['\\\"]new['\\\"]": [[105, "'new', 'processing'"]], "WorkflowStatus\\.": [[97, "WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [98, "WorkflowStatus.APPROVED.value"], [101, "WorkflowStatus.UPLOAD_FAILED.value,"], [129, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [218, "LocalCreative.status == WorkflowStatus.APPROVED.value"]]}, "ai_tools\\appeal_workflow_integration_patch.py": {"['\\\"]rejected['\\\"]": [[94, "AND ap.status IN ('rejected', 'under_review', 'failed_review')"]]}, "ai_tools\\enhanced_duplicate_check_logic.py": {"['\\\"]already_tested['\\\"]": [[16, "locked_creative.status = 'already_tested'"], [45, "locked_creative.status = 'already_tested'"], [58, "locked_creative.status = 'already_tested'"]]}, "ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py": {"['\\\"]processing['\\\"]": [[232, "WHERE status = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[205, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[180, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"]], "['\\\"]upload_failed['\\\"]": [[285, "WHERE status = 'upload_failed'"]]}, "ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py": {"['\\\"]processing['\\\"]": [[201, "LocalCreative.status == 'processing'"], [373, "WHERE status = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[390, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]upload_failed['\\\"]": [[220, "LocalCreative.status == 'upload_failed'"]]}, "ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py": {"['\\\"]approved['\\\"]": [[277, "approved_materials = [lc for lc in account_materials if lc['status'] == 'approved']"], [506, "daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}"], [514, "daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}"], [518, "daily_stats[date_key]['approved'] += 1"], [525, "approved_count = [daily_stats[date]['approved'] for date in dates]"]], "['\\\"]rejected['\\\"]": [[278, "rejected_materials = [lc for lc in account_materials if lc['status'] == 'rejected']"]], "['\\\"]harvested['\\\"]": [[257, "harvested_materials = [lc for lc in data['local_creatives_raw'] if lc['harvest_status'] == 'harvested']"], [276, "harvested_materials = [lc for lc in account_materials if lc['harvest_status'] == 'harvested']"]]}, "ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py": {"['\\\"]pending_upload['\\\"]": [[146, "if creative and creative.status == 'pending_upload':"]], "['\\\"]processing['\\\"]": [[92, "with manager.atomic_state_transition(creative_id, 'processing', 'uploaded_pending_plan') as creative:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[92, "with manager.atomic_state_transition(creative_id, 'processing', 'uploaded_pending_plan') as creative:"]]}, "ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py": {"['\\\"]pending_upload['\\\"]": [[123, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[119, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[127, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py": {"['\\\"]pending_upload['\\\"]": [[94, "logger.info(\"       pending = db.query(LocalCreative).filter(LocalCreative.status=='pending_upload').count()\")"]], "['\\\"]processing['\\\"]": [[93, "logger.info(\"       processing = db.query(LocalCreative).filter(LocalCreative.status=='processing').count()\")"]]}, "ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py": {"['\\\"]pending_upload['\\\"]": [[156, "'pending_upload': '📤',"], [169, "pending_count = status_counts.get('pending_upload', 0)"], [188, "'pending_upload': pending_count,"]], "['\\\"]processing['\\\"]": [[157, "'processing': '⏳',"], [168, "processing_count = status_counts.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[158, "'uploaded_pending_plan': '📋',"], [170, "uploaded_count = status_counts.get('uploaded_pending_plan', 0)"], [189, "'uploaded_pending_plan': uploaded_count,"]], "['\\\"]testing_pending_review['\\\"]": [[159, "'testing_pending_review': '🔍',"]], "['\\\"]approved['\\\"]": [[160, "'approved': '✅',"]], "['\\\"]rejected['\\\"]": [[161, "'rejected': '❌',"]], "['\\\"]upload_failed['\\\"]": [[162, "'upload_failed': '💀'"]]}, "ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py": {"['\\\"]testing_pending_review['\\\"]": [[180, "LocalCreative.status == 'testing_pending_review'"]], "['\\\"]rejected['\\\"]": [[174, "LocalCreative.status == 'rejected',"]], "['\\\"]harvested['\\\"]": [[133, "LocalCreative.harvest_status == 'harvested',"], [229, "db_material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[118, "LocalCreative.status == 'already_tested',"]]}, "ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py": {"['\\\"]processing['\\\"]": [[236, "elif status == 'processing' and count > 0:"], [250, "processing_count = status_counts.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[238, "elif status == 'uploaded_pending_plan' and count > 0:"], [251, "pending_plan_count = status_counts.get('uploaded_pending_plan', 0)"], [383, "pending_plan_count = workflow_status.get('uploaded_pending_plan', 0)"]], "['\\\"]approved['\\\"]": [[240, "elif status == 'approved' and count > 0:"], [252, "approved_count = status_counts.get('approved', 0)"]], "['\\\"]new['\\\"]": [[234, "if status == 'new' and count > 0:"], [249, "new_count = status_counts.get('new', 0)"], [382, "new_count = workflow_status.get('new', 0)"]]}, "ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py": {"['\\\"]processing['\\\"]": [[122, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing'])"], [136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[122, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing'])"]], "['\\\"]new['\\\"]": [[136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]], "['\\\"]pending_grouping['\\\"]": [[136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]]}, "ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py": {"['\\\"]pending_upload['\\\"]": [[165, "if status in ['pending_grouping', 'pending_upload'] and count > 0:"]], "['\\\"]pending_grouping['\\\"]": [[165, "if status in ['pending_grouping', 'pending_upload'] and count > 0:"]]}, "ai_tools\\diagnosis\\ai_tool_20250803_workflow_final_analysis.py": {"['\\\"]pending_grouping['\\\"]": [[209, "if 'pending_grouping' in analysis_result['status_analysis']:"], [210, "count = analysis_result['status_analysis']['pending_grouping']"]]}, "ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py": {"['\\\"]processing['\\\"]": [[98, "LocalCreative.status == 'processing'"], [143, "LocalCreative.status == 'processing'"]], "['\\\"]pending_grouping['\\\"]": [[124, "LocalCreative.status == 'pending_grouping'"]]}, "ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py": {"['\\\"]approved['\\\"]": [[92, "LocalCreative.status == 'approved',"], [101, "LocalCreative.status == 'approved'"]]}, "ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py": {"['\\\"]pending_upload['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]uploading['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]processing['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]pending_grouping['\\\"]": [[98, "material.status = 'pending_grouping'"]]}, "ai_tools\\emergency\\ai_tool_20250726_emergency_business_rules_fix.py": {"['\\\"]already_tested['\\\"]": [[157, "if local_creative and local_creative.status != 'already_tested':"], [158, "local_creative.status = 'already_tested'"], [256, "WHERE status = 'already_tested'"]]}, "ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py": {"['\\\"]pending_upload['\\\"]": [[133, "'pending_upload': 7,"]], "['\\\"]uploading['\\\"]": [[175, "LocalCreative.status.in_(['processing', 'creating_plan', 'uploading'])"]], "['\\\"]processing['\\\"]": [[107, "if primary_record.status in ['processing', 'creating_plan']:"], [138, "'processing': 2,"], [175, "LocalCreative.status.in_(['processing', 'creating_plan', 'uploading'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[132, "'uploaded_pending_plan': 8,"]], "['\\\"]testing_pending_review['\\\"]": [[131, "'testing_pending_review': 9,"]], "['\\\"]rejected['\\\"]": [[137, "'rejected': 3,"]], "['\\\"]new['\\\"]": [[135, "'new': 5,"]], "['\\\"]pending_grouping['\\\"]": [[109, "primary_record.status = 'pending_grouping'"], [134, "'pending_grouping': 6,"], [186, "record.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[136, "'upload_failed': 4,"]], "['\\\"]already_tested['\\\"]": [[130, "'already_tested': 10,"]]}, "ai_tools\\emergency\\ai_tool_20250726_emergency_harvest_duplicate_fix.py": {"['\\\"]harvested['\\\"]": [[157, "if material.harvest_status != 'harvested':"], [158, "material.harvest_status = 'harvested'"], [255, "material.harvest_status = 'harvested'"], [317, "4. 收割后必须更新数据库状态为 'harvested'"]], "['\\\"]already_tested['\\\"]": [[227, "LocalCreative.status == 'already_tested',"], [320, "1. 查询 status='already_tested' AND harvest_status='not_harvested'"]]}, "ai_tools\\emergency\\ai_tool_20250726_emergency_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[130, "WHERE status = 'uploaded_pending_plan';"]]}, "ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py": {"['\\\"]approved['\\\"]": [[120, "LocalCreative.status == 'approved'"], [152, "if material.status == 'approved':"]], "['\\\"]harvested['\\\"]": [[154, "material.status = 'harvested'"]]}, "ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [113, "pending_upload_count = status_counts.get('pending_upload', 0)"], [118, "'pending_upload': '📤',"], [192, "'pending_grouping' in content and 'pending_upload' in content  # 状态转换"]], "['\\\"]processing['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [112, "processing_count = status_counts.get('processing', 0)"], [119, "'processing': '⏳',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [120, "'uploaded_pending_plan': '📋',"]], "['\\\"]testing_pending_review['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]], "['\\\"]approved['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"], [121, "'approved': '✅',"]], "['\\\"]rejected['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"], [122, "'rejected': '❌'"]], "['\\\"]pending_grouping['\\\"]": [[192, "'pending_grouping' in content and 'pending_upload' in content  # 状态转换"]], "['\\\"]upload_failed['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]]}, "ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py": {"['\\\"]pending_upload['\\\"]": [[178, "creative.status = 'pending_upload'"], [206, "if creative.status != 'pending_upload':"], [211, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as processing_creative:"]], "['\\\"]processing['\\\"]": [[65, "LocalCreative.status == 'processing'"], [70, "LocalCreative.status == 'processing',"], [84, "WHERE status = 'processing'"], [169, "LocalCreative.status == 'processing',"], [211, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as processing_creative:"]]}, "ai_tools\\emergency\\ai_tool_20250809_direct_config_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[58, "LocalCreative.status == 'uploaded_pending_plan'"], [77, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[82, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[162, "test_creative.status = 'pending_upload'"], [180, "'conversion_successful': new_status == 'pending_upload'"]], "['\\\"]pending_grouping['\\\"]": [[71, "WHERE lc.status = 'pending_grouping'"], [112, "'pending_grouping_query': 'pending_grouping' in content and 'batch_upload_videos' in content,"], [114, "'commit_changes': 'db.commit()' in content and 'pending_grouping' in content,"], [147, "LocalCreative.status == 'pending_grouping'"], [196, "LocalCreative.status == 'pending_grouping'"]]}, "ai_tools\\emergency\\ai_tool_20250809_plan_creation_deep_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[65, "LocalCreative.status == 'uploaded_pending_plan'"], [129, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[135, "LocalCreative.status == 'testing_pending_review'"]]}, "ai_tools\\emergency\\ai_tool_20250809_plan_creation_fix_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[91, "if status == 'uploaded_pending_plan':"], [124, "'direct_local_creative_query': 'db.query(LocalCreative).filter(' in content and 'uploaded_pending_plan' in content,"]]}, "ai_tools\\emergency\\ai_tool_20250809_reassign_materials_to_test_accounts.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[66, "LocalCreative.status == 'uploaded_pending_plan',"], [175, "LocalCreative.status == 'uploaded_pending_plan',"], [183, "LocalCreative.status == 'uploaded_pending_plan',"]]}, "ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py": {"['\\\"]pending_upload['\\\"]": [[105, "material.status = 'pending_upload'"], [183, "LocalCreative.status == 'pending_upload',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[66, "LocalCreative.status == 'uploaded_pending_plan',"], [190, "LocalCreative.status == 'uploaded_pending_plan',"]]}, "ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [228, "successful_uploads = status_counts.get('uploaded_pending_plan', 0) + \\"]], "['\\\"]testing_pending_review['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [229, "status_counts.get('testing_pending_review', 0) + \\"]], "['\\\"]approved['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [230, "status_counts.get('approved', 0)"]], "['\\\"]rejected['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"]]}, "ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py": {"['\\\"]pending_upload['\\\"]": [[57, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[59, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[60, "'testing_pending_review': '测试中待审核',"]], "['\\\"]approved['\\\"]": [[61, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[62, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[63, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[55, "'new': '新素材',"]], "['\\\"]pending_grouping['\\\"]": [[56, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[58, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[64, "'already_tested': '已测试过',"]]}, "ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py": {"['\\\"]pending_upload['\\\"]": [[54, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[56, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[57, "'testing_pending_review': '测试中待审核',"]], "['\\\"]approved['\\\"]": [[58, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[59, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[60, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[52, "'new': '新素材',"]], "['\\\"]pending_grouping['\\\"]": [[53, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[55, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[61, "'already_tested': '已测试过',"]]}, "ai_tools\\harvest\\ai_tool_20250726_harvest_proper_harvest.py": {"['\\\"]harvested['\\\"]": [[81, "material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[53, "LocalCreative.status == 'already_tested',"]]}, "ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py": {"['\\\"]pending_upload['\\\"]": [[72, "creative.status = 'pending_upload'"], [135, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as creative:"]], "['\\\"]processing['\\\"]": [[62, "LocalCreative.status == 'processing',"], [135, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as creative:"]]}, "ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py": {"['\\\"]pending_upload['\\\"]": [[114, "record.status = 'pending_upload'"]], "['\\\"]upload_failed['\\\"]": [[105, "LocalCreative.status == 'upload_failed'"]]}, "ai_tools\\maintenance\\ai_tool_20250723_comprehensive_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[228, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as success,"]], "['\\\"]upload_failed['\\\"]": [[229, "COUNT(CASE WHEN status = 'upload_failed' THEN 1 END) as failed"]]}, "ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py": {"['\\\"]processing['\\\"]": [[149, "{'status': 'processing', 'count': 66, 'percentage': 47.83},"]], "['\\\"]uploaded_pending_plan['\\\"]": [[152, "{'status': 'uploaded_pending_plan', 'count': 13, 'percentage': 9.42},"], [163, "success_uploads = next((r['count'] for r in mock_status_results if r['status'] == 'uploaded_pending_plan'), 0)"]], "['\\\"]upload_failed['\\\"]": [[150, "{'status': 'upload_failed', 'count': 28, 'percentage': 20.29},"], [162, "failed_uploads = next((r['count'] for r in mock_status_results if r['status'] == 'upload_failed'), 0)"]], "['\\\"]already_tested['\\\"]": [[151, "{'status': 'already_tested', 'count': 18, 'percentage': 13.04},"]]}, "ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py": {"['\\\"]processing['\\\"]": [[300, "COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_uploads"]], "['\\\"]uploaded_pending_plan['\\\"]": [[298, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as successful_uploads,"]], "['\\\"]upload_failed['\\\"]": [[299, "COUNT(CASE WHEN status = 'upload_failed' THEN 1 END) as failed_uploads,"]]}, "ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py": {"['\\\"]processing['\\\"]": [[199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]], "['\\\"]pending_grouping['\\\"]": [[166, "creative.status = 'pending_grouping'"], [199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]], "['\\\"]upload_failed['\\\"]": [[157, "LocalCreative.status == 'upload_failed',"], [199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]]}, "ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py": {"['\\\"]processing['\\\"]": [[413, "LocalCreative.status == 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[416, "'status': 'pending_grouping',"]]}, "ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py": {"['\\\"]pending_upload['\\\"]": [[70, "PENDING_UPLOAD = \"pending_upload\"           # 待上传 (旧)"], [95, "'pending_upload': '待上传 (旧)',"]], "['\\\"]uploading['\\\"]": [[96, "'uploading': '上传中 (旧)',"]], "['\\\"]processing['\\\"]": [[53, "PROCESSING = \"processing\"                   # 处理中"], [78, "'processing': '处理中',"], [229, "LocalCreative.status.in_(['processing', 'creating_plan', 'testing_pending_review'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[54, "UPLOADED_PENDING_PLAN = \"uploaded_pending_plan\"  # 已上传待建计划"], [79, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[56, "TESTING_PENDING_REVIEW = \"testing_pending_review\"  # 测试待审核"], [81, "'testing_pending_review': '测试待审核',"], [229, "LocalCreative.status.in_(['processing', 'creating_plan', 'testing_pending_review'])"]], "['\\\"]approved['\\\"]": [[59, "APPROVED = \"approved\"                       # 审核通过"], [84, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[60, "REJECTED = \"rejected\"                       # 审核拒绝"], [85, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[62, "HARVESTED = \"harvested\"                     # 已收割"], [87, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[51, "NEW = \"new\"                                 # 新文件入库"], [76, "'new': '新入库',"]], "['\\\"]pending_grouping['\\\"]": [[52, "PENDING_GROUPING = \"pending_grouping\"       # 待分组"], [77, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[65, "UPLOAD_FAILED = \"upload_failed\"             # 上传失败"], [90, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[61, "ALREADY_TESTED = \"already_tested\"           # 已测试过"], [86, "'already_tested': '已测试',"]], "MaterialStatus\\.": [[110, "MaterialStatus.NEW: [MaterialStatus.PENDING_GROUPING],"], [111, "MaterialStatus.PENDING_GROUPING: [MaterialStatus.PROCESSING, MaterialStatus.UPLOAD_FAILED],"], [112, "MaterialStatus.PROCESSING: [MaterialStatus.UPLOADED_PENDING_PLAN, MaterialStatus.UPLOAD_FAILED],"], [113, "MaterialStatus.UPLOADED_PENDING_PLAN: [MaterialStatus.CREATING_PLAN, MaterialStatus.ALREADY_TESTED],"], [114, "MaterialStatus.CREATING_PLAN: [MaterialStatus.TESTING_PENDING_REVIEW, MaterialStatus.UPLOAD_FAILED],"], [115, "MaterialStatus.TESTING_PENDING_REVIEW: [MaterialStatus.APPROVED, MaterialStatus.REJECTED],"], [116, "MaterialStatus.APPROVED: [MaterialStatus.HARVESTED],"], [117, "MaterialStatus.REJECTED: [],  # 终态"], [118, "MaterialStatus.ALREADY_TESTED: [],  # 终态"], [119, "MaterialStatus.UPLOAD_FAILED: [MaterialStatus.PENDING_GROUPING],  # 重试"], [120, "MaterialStatus.CHECK_FAILED: [MaterialStatus.PENDING_GROUPING],   # 重试"], [121, "MaterialStatus.RESET_BY_SYSTEM: [MaterialStatus.PENDING_GROUPING], # 重置"], [122, "MaterialStatus.HARVESTED: [],  # 终态"], [123, "MaterialStatus.PENDING_UPLOAD: [MaterialStatus.PENDING_GROUPING],  # 向后兼容"]]}, "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py": {"['\\\"]processing['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]], "['\\\"]approved['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]], "['\\\"]rejected['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]]}, "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py": {"['\\\"]processing['\\\"]": [[127, "result = WorkflowValidator.check_status_timeout('processing', old_time)"], [132, "result = WorkflowValidator.check_status_timeout('processing', recent_time)"], [375, "1, 'processing', 'uploaded_pending_plan'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[375, "1, 'processing', 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[107, "1, 'new', 'approved'"], [388, "1, 'approved', 'harvested'"]], "['\\\"]harvested['\\\"]": [[388, "1, 'approved', 'harvested'"]], "['\\\"]new['\\\"]": [[101, "1, 'new', 'pending_grouping'"], [107, "1, 'new', 'approved'"], [114, "1, 'invalid_status', 'new'"]], "['\\\"]pending_grouping['\\\"]": [[101, "1, 'new', 'pending_grouping'"]], "WorkflowStatus\\.": [[73, "WorkflowStatus.NEW, WorkflowStatus.PENDING_GROUPING"], [77, "WorkflowStatus.PENDING_GROUPING, WorkflowStatus.PROCESSING"], [81, "WorkflowStatus.PROCESSING, WorkflowStatus.UPLOADED_PENDING_PLAN"], [86, "WorkflowStatus.NEW, WorkflowStatus.APPROVED"], [90, "WorkflowStatus.HARVESTED, WorkflowStatus.PROCESSING"]]}, "ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py": {"['\\\"]processing['\\\"]": [[153, "LocalCreative.status == 'processing'"], [285, "LocalCreative.status == 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[263, "creative.status = 'pending_grouping'"], [290, "creative.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[145, "LocalCreative.status == 'upload_failed'"], [257, "LocalCreative.status == 'upload_failed',"]]}, "ai_tools\\maintenance\\ai_tool_20250727_auto_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[106, "SET status = 'already_tested'"]]}, "ai_tools\\maintenance\\ai_tool_20250727_batch_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[95, "SET status = 'already_tested'"], [152, "-- UPDATE local_creatives SET status = 'already_tested' WHERE filename = '素材文件名';"]]}, "ai_tools\\maintenance\\ai_tool_20250727_emergency_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[175, "material.status = 'already_tested'"]]}, "ai_tools\\maintenance\\ai_tool_20250727_immediate_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[74, "SET status = 'already_tested'"], [138, "SET status = 'already_tested'"]]}, "ai_tools\\maintenance\\ai_tool_20250728_maintenance_material_file_organizer.py": {"['\\\"]new['\\\"]": [[7, "创建目的: 整理千川自动化项目中'new'状态素材的文件路径，确保路径一致性"], [73, "# 查询所有'new'状态的素材"], [77, "WHERE status = 'new'"], [256, "# 检查'new'状态素材的路径分布"], [267, "WHERE status = 'new'"]]}, "ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py": {"['\\\"]uploading['\\\"]": [[31, "UPLOADING = \"uploading\""]]}, "ai_tools\\maintenance\\ai_tool_20250801_workflow_fix.py": {"['\\\"]new['\\\"]": [[110, "status='new'  # 设置为new状态，等待处理"]]}, "ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py": {"['\\\"]approved['\\\"]": [[45, "LocalCreative.status == 'approved'"], [105, "if pc.local_creative.status != 'approved':"], [111, "if pc.local_creative.status != 'approved':"], [159, "LocalCreative.status == 'approved'"], [227, "LocalCreative.status == 'approved'"]], "['\\\"]harvested['\\\"]": [[193, "material.status = 'harvested'"], [294, "LocalCreative.status == 'harvested'"]]}, "ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py": {"['\\\"]approved['\\\"]": [[82, "if pc.local_creative.status != 'approved':"], [92, "if pc.local_creative.status != 'approved':"], [95, "pc.local_creative.status = 'approved'"], [215, "if \"pc.local_creative.status = 'approved'\" in content:"]]}, "ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py": {"['\\\"]testing_pending_review['\\\"]": [[130, "if row.status in ['approved', 'testing_pending_review']:"]], "['\\\"]approved['\\\"]": [[130, "if row.status in ['approved', 'testing_pending_review']:"], [353, "WHEN lc.status = 'approved' AND pc.review_status NOT IN ('PASS', 'APPROVED') THEN 'local_approved_platform_not_pass'"], [355, "WHEN lc.harvest_status = 'not_harvested' AND lc.status = 'approved' AND pc.review_status = 'PASS' THEN 'harvest_pending'"]], "['\\\"]rejected['\\\"]": [[354, "WHEN lc.status = 'rejected' AND pc.review_status = 'PASS' THEN 'local_rejected_platform_pass'"]], "['\\\"]harvested['\\\"]": [[322, "if row.harvest_status == 'harvested':"]]}, "ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py": {"['\\\"]approved['\\\"]": [[236, "if audit_status == 'PASS' and current_status != 'approved':"], [238, "pc.local_creative.status = 'approved'"]], "['\\\"]rejected['\\\"]": [[242, "elif audit_status in ['REJECT', 'AUDIT_REJECT'] and current_status != 'rejected':"], [244, "pc.local_creative.status = 'rejected'"]]}, "ai_tools\\maintenance\\ai_tool_20250803_web_report_optimization.py": {"['\\\"]harvested['\\\"]": [[65, "harvested_count = harvest_stats.get('harvested', 0)"]]}, "ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[90, "record.status = 'pending_upload'"]], "['\\\"]pending_grouping['\\\"]": [[80, "LocalCreative.status == 'pending_grouping'"]]}, "ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py": {"['\\\"]pending_upload['\\\"]": [[41, "LocalCreative.status == 'pending_upload'"], [94, "WHERE status = 'pending_upload'"]]}, "ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py": {"['\\\"]pending_upload['\\\"]": [[145, "if status_dict.get('pending_upload', 0) > 50:"], [148, "'issue': f'pending_upload状态堆积 ({status_dict[\"pending_upload\"]} 个)',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[98, "LocalCreative.status == 'uploaded_pending_plan'"], [153, "if status_dict.get('uploaded_pending_plan', 0) > 100:"], [156, "'issue': f'uploaded_pending_plan状态堆积 ({status_dict[\"uploaded_pending_plan\"]} 个)',"]], "['\\\"]testing_pending_review['\\\"]": [[161, "if status_dict.get('testing_pending_review', 0) > 200:"], [164, "'issue': f'testing_pending_review状态堆积 ({status_dict[\"testing_pending_review\"]} 个)',"]]}, "ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py": {"['\\\"]pending_upload['\\\"]": [[75, "LocalCreative.status == 'pending_upload'"], [202, "(count for status, count in new_status_distribution if status == 'pending_upload'),"]], "WorkflowStatus\\.": [[162, "material.status = WorkflowStatus.TESTING_PENDING_REVIEW.value"]]}, "ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py": {"['\\\"]processing['\\\"]": [[265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[260, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]testing_pending_review['\\\"]": [[260, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]]}, "ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py": {"['\\\"]processing['\\\"]": [[194, "creative.id, 'uploaded_pending_plan', 'processing'"], [260, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing']),"]], "['\\\"]uploaded_pending_plan['\\\"]": [[125, "creative.id, 'testing_pending_review', 'uploaded_pending_plan'"], [157, "'stage': 'uploaded_pending_plan',"], [170, "LocalCreative.status == 'uploaded_pending_plan',"], [194, "creative.id, 'uploaded_pending_plan', 'processing'"], [255, "LocalCreative.status.in_(['testing_pending_review', 'uploaded_pending_plan']),"], [260, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing']),"]], "['\\\"]testing_pending_review['\\\"]": [[79, "'stage': 'testing_pending_review',"], [92, "LocalCreative.status == 'testing_pending_review',"], [112, "creative.id, 'testing_pending_review', 'approved'"], [125, "creative.id, 'testing_pending_review', 'uploaded_pending_plan'"], [255, "LocalCreative.status.in_(['testing_pending_review', 'uploaded_pending_plan']),"]], "['\\\"]approved['\\\"]": [[112, "creative.id, 'testing_pending_review', 'approved'"]]}, "ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py": {"['\\\"]pending_upload['\\\"]": [[64, "COUNT(CASE WHEN status = 'pending_upload' THEN 1 END) as pending_upload,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[65, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as uploaded_pending_plan,"]], "['\\\"]rejected['\\\"]": [[67, "COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected"]], "['\\\"]new['\\\"]": [[63, "COUNT(CASE WHEN status = 'new' THEN 1 END) as new_status,"]], "['\\\"]already_tested['\\\"]": [[66, "COUNT(CASE WHEN status = 'already_tested' THEN 1 END) as already_tested,"]]}, "ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py": {"['\\\"]rejected['\\\"]": [[226, "WHERE lc.status = 'rejected'"], [237, "WHERE status = 'rejected';"]]}, "ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py": {"['\\\"]pending_upload['\\\"]": [[86, "'pending_upload': 0,"], [261, "logger.info(f\"   - 待上传: {materials['pending_upload']}\")"]], "['\\\"]uploaded_pending_plan['\\\"]": [[87, "'uploaded_pending_plan': 0,"], [129, "'upload_rate': (materials['uploaded_pending_plan'] + materials['already_tested']) / total_materials,"], [262, "logger.info(f\"   - 待创建计划: {materials['uploaded_pending_plan']}\")"]], "['\\\"]rejected['\\\"]": [[89, "'rejected': 0"], [264, "logger.info(f\"   - 已拒绝: {materials['rejected']}\")"]], "['\\\"]new['\\\"]": [[85, "'new': 0,"], [260, "logger.info(f\"   - 新入库: {materials['new']}\")"]], "['\\\"]already_tested['\\\"]": [[88, "'already_tested': 0,"], [129, "'upload_rate': (materials['uploaded_pending_plan'] + materials['already_tested']) / total_materials,"], [130, "'plan_creation_rate': materials['already_tested'] / total_materials if total_materials > 0 else 0,"], [263, "logger.info(f\"   - 已测试: {materials['already_tested']}\")"]]}, "ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py": {"['\\\"]testing_pending_review['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]approved['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]rejected['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]already_tested['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]]}, "ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py": {"['\\\"]pending_upload['\\\"]": [[130, "'pending_upload' as stage,"], [133, "WHERE status = 'pending_upload'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[138, "'uploaded_pending_plan' as stage,"], [141, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]rejected['\\\"]": [[226, "WHERE lc.status = 'rejected'"]], "['\\\"]harvested['\\\"]": [[308, "harvested_count = material_stats.get('harvested', {}).get('count', 0)"]]}, "ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py": {"['\\\"]processing['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]new['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]]}, "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py": {"['\\\"]pending_upload['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"], [26, "alert = \"🚨\" if (status == 'pending_upload' and count > 50) else \"✅\""]], "['\\\"]processing['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]approved['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]rejected['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]new['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]]}, "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py": {"['\\\"]pending_upload['\\\"]": [[63, "if status == 'pending_upload' and count > 50:"]], "['\\\"]approved['\\\"]": [[153, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'approved'\")"]], "['\\\"]new['\\\"]": [[69, "elif status == 'new' and count > 100:"], [139, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'new'\")"]], "['\\\"]upload_failed['\\\"]": [[66, "elif status == 'upload_failed' and count > 20:"], [146, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'upload_failed'\")"]]}, "ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py": {"['\\\"]pending_upload['\\\"]": [[240, "pending_upload_queue = queue_depths.get('pending_upload', 0)"]], "['\\\"]processing['\\\"]": [[100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"], [239, "processing_queue = queue_depths.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[93, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"]], "['\\\"]testing_pending_review['\\\"]": [[93, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]upload_failed['\\\"]": [[100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"], [241, "upload_failed_queue = queue_depths.get('upload_failed', 0)"], [263, "LocalCreative.status.in_(['upload_failed', 'processing_failed'])"]]}, "ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py": {"['\\\"]pending_upload['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"], [92, "('pending_upload', '📤', '待上传'),"], [132, "pending_upload_count = current_counts.get('pending_upload', 0)"], [171, "if status == 'pending_upload' and change < 0:"], [183, "upload_progress = abs(total_changes.get('pending_upload', 0))"]], "['\\\"]processing['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"], [93, "('processing', '⏳', '处理中'),"], [131, "processing_count = current_counts.get('processing', 0)"], [175, "elif status == 'processing' and change > 0:"], [185, "processing_change = total_changes.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[67, "'uploaded_pending_plan', 'testing_pending_review',"], [94, "('uploaded_pending_plan', '📋', '待创建计划'),"], [173, "elif status == 'uploaded_pending_plan' and change > 0:"], [184, "upload_success = total_changes.get('uploaded_pending_plan', 0)"]], "['\\\"]testing_pending_review['\\\"]": [[67, "'uploaded_pending_plan', 'testing_pending_review',"], [95, "('testing_pending_review', '🔍', '测试中'),"]], "['\\\"]approved['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"], [96, "('approved', '✅', '已通过'),"]], "['\\\"]rejected['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"], [97, "('rejected', '❌', '已拒绝')"]], "['\\\"]new['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"]], "['\\\"]already_tested['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"]]}, "ai_tools\\monitoring\\ai_tool_20250810_alert_system.py": {"['\\\"]processing['\\\"]": [[129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[109, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"], [240, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]testing_pending_review['\\\"]": [[109, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"], [240, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]]}, "ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py": {"['\\\"]processing['\\\"]": [[113, "LocalCreative.status == 'processing',"], [176, "'processing': db.query(LocalCreative).filter(LocalCreative.status == 'processing').count(),"]], "['\\\"]uploaded_pending_plan['\\\"]": [[177, "'uploaded_pending_plan': db.query(LocalCreative).filter(LocalCreative.status == 'uploaded_pending_plan').count(),"], [204, "if workflow_stats['uploaded_pending_plan'] > 50:"], [205, "issues.append(f\"有{workflow_stats['uploaded_pending_plan']}个素材等待创建计划\")"], [214, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]testing_pending_review['\\\"]": [[178, "'testing_pending_review': db.query(LocalCreative).filter(LocalCreative.status == 'testing_pending_review').count(),"], [208, "if workflow_stats['testing_pending_review'] > 100:"], [209, "issues.append(f\"有{workflow_stats['testing_pending_review']}个素材等待审核\")"], [214, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]approved['\\\"]": [[179, "'approved': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),"]], "['\\\"]harvested['\\\"]": [[180, "'harvested': db.query(LocalCreative).filter(LocalCreative.status == 'harvested').count()"]], "['\\\"]new['\\\"]": [[174, "'new': db.query(LocalCreative).filter(LocalCreative.status == 'new').count(),"]], "['\\\"]pending_grouping['\\\"]": [[175, "'pending_grouping': db.query(LocalCreative).filter(LocalCreative.status == 'pending_grouping').count(),"]]}, "ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py": {"['\\\"]processing['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]new['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]]}, "ai_tools\\recovery\\ai_tool_20250726_recovery_manual_harvest.py": {"['\\\"]harvested['\\\"]": [[77, "material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[44, "LocalCreative.status == 'already_tested',"]]}, "ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[287, "SET status = 'pending_upload', updated_at = NOW()"]], "['\\\"]uploaded_pending_plan['\\\"]": [[245, "SET status = 'uploaded_pending_plan', updated_at = NOW()"]], "['\\\"]testing_pending_review['\\\"]": [[231, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"], [323, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"]], "['\\\"]upload_failed['\\\"]": [[271, "WHERE status = 'upload_failed'"]]}, "ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py": {"['\\\"]processing['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [198, "elif status == 'processing':"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [192, "if status == 'uploaded_pending_plan':"], [275, "if initial_progress.get('uploaded_pending_plan', 0) > 50:"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]testing_pending_review['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"]], "['\\\"]approved['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [195, "elif status == 'approved':"], [279, "if initial_progress.get('approved', 0) > 50:"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]new['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"]]}, "ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[128, "LocalCreative.status == 'pending_upload'"], [217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"], [224, "alert = \"🚨\" if (status == 'pending_upload' and count > 50) else \"✅\""]], "['\\\"]processing['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]approved['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]rejected['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]new['\\\"]": [[136, "material.status = 'new'"], [140, "logger.success(f\"✅ 已重置 {len(orphaned_materials)} 个孤岛素材状态为 'new'\")"], [217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]]}, "ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py": {"['\\\"]pending_upload['\\\"]": [[204, "creative.status = 'pending_upload'"], [231, "pending_upload_creatives = [c for c in missing_creatives if c['status'] == 'pending_upload']"]], "['\\\"]uploaded_pending_plan['\\\"]": [[202, "if status == 'uploaded_pending_plan' and '01_materials_to_process' in file_path:"], [232, "uploaded_pending_plan_creatives = [c for c in missing_creatives if c['status'] == 'uploaded_pending_plan']"]]}, "ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py": {"['\\\"]pending_upload['\\\"]": [[49, "LocalCreative.status.in_(['uploaded_pending_plan', 'pending_upload'])"], [93, "if current_status != 'pending_upload':"], [94, "creative.status = 'pending_upload'"], [182, "pending_upload_videos = [v for v in target_videos if v['status'] == 'pending_upload']"]], "['\\\"]uploaded_pending_plan['\\\"]": [[49, "LocalCreative.status.in_(['uploaded_pending_plan', 'pending_upload'])"], [181, "uploaded_pending_videos = [v for v in target_videos if v['status'] == 'uploaded_pending_plan']"]]}, "ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py": {"['\\\"]processing['\\\"]": [[143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [172, "processing_activity = final_stats.get('processing', 0) > 0"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]approved['\\\"]": [[159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]new['\\\"]": [[107, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'new'\")"], [143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [171, "new_decreased = final_stats.get('new', 0) < initial_stats.get('new', 0)"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]]}, "ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py": {"['\\\"]pending_upload['\\\"]": [[276, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[271, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[280, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "backups\\weekly_backup_20250810_124054\\web_ui.py": {"['\\\"]processing['\\\"]": [[93, "'processing': '处理中',"], [1817, "st.info(\"管理卡在 'processing' 状态的僵尸素材 - Manage materials stuck in 'processing' status\")"], [1854, "LocalCreative.status == 'processing',"], [1878, "LocalCreative.status == 'processing',"], [1905, "LocalCreative.status == 'processing'"], [2394, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[95, "'uploaded_pending_plan': '已上传待创建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[97, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[98, "'approved': '审核通过',"], [2928, "approved_count = len([m for m in all_materials if m.status == 'approved'])"], [2962, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2965, "if material.status == 'approved':"], [2966, "daily_stats[date_key]['approved'] += 1"], [2978, "daily_df['通过率'] = (daily_df['approved'] / daily_df['total'] * 100).round(1)"], [2980, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [3013, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [3017, "if material.status == 'approved':"], [3018, "author_stats[author]['approved'] += 1"], [3026, "author_df['通过率'] = (author_df['approved'] / author_df['total'] * 100).round(1)"]], "['\\\"]rejected['\\\"]": [[99, "'rejected': '审核拒绝',"], [2929, "rejected_count = len([m for m in all_materials if m.status == 'rejected'])"], [2962, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [2967, "elif material.status == 'rejected':"], [2968, "daily_stats[date_key]['rejected'] += 1"], [2980, "st.line_chart(daily_df[['total', 'approved', 'rejected', 'failed']])"], [3013, "'total': 0, 'approved': 0, 'rejected': 0, 'failed': 0"], [3019, "elif material.status == 'rejected':"], [3020, "author_stats[author]['rejected'] += 1"]], "['\\\"]pending_grouping['\\\"]": [[92, "'pending_grouping': '待分组',"], [1885, "material.status = 'pending_grouping'"], [1911, "material.status = 'pending_grouping'"], [2370, "LocalCreative.status == 'pending_grouping'"], [2385, "LocalCreative.status == 'pending_grouping'"], [3878, "LocalCreative.status == 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[94, "'upload_failed': '上传失败',"], [2930, "failed_count = len([m for m in all_materials if m.status == 'upload_failed'])"], [2969, "elif material.status == 'upload_failed':"], [3021, "elif material.status == 'upload_failed':"]], "['\\\"]already_tested['\\\"]": [[100, "'already_tested': '已测试',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_complete_material_analytics.py": {"['\\\"]pending_upload['\\\"]": [[156, "'pending_upload': '待上传',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [558, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]processing['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1140, "WHEN 'processing' THEN 3"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1352, "SUM(CASE WHEN lc.status = 'processing' THEN 1 ELSE 0 END) as processing_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[157, "'uploaded_pending_plan': '已上传待建计划',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [569, "AVG(CASE WHEN lc.status = 'uploaded_pending_plan'"], [696, "AND lc.status IN ('creating_plan', 'uploaded_pending_plan')"], [711, "elif row.status == 'uploaded_pending_plan' and row.count > 10:"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1141, "WHEN 'uploaded_pending_plan' THEN 4"], [1161, "'uploaded_pending_plan': '已上传待建计划', 'creating_plan': '创建计划中',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1353, "SUM(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 ELSE 0 END) as uploaded_pending_plan_count,"], [1359, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"]], "['\\\"]testing_pending_review['\\\"]": [[159, "'testing_pending_review': '测试待审核',"], [535, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [558, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [565, "AVG(CASE WHEN lc.status = 'testing_pending_review'"], [897, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [925, "if result.status == 'testing_pending_review' and result.hours_since_update > 12:"], [1143, "WHEN 'testing_pending_review' THEN 6"], [1162, "'testing_pending_review': '测试待审核', 'approved': '审核通过',"], [1225, "elif row.status == 'testing_pending_review' and row.avg_duration_hours and row.avg_duration_hours > 24:"], [1286, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [1355, "SUM(CASE WHEN lc.status = 'testing_pending_review' THEN 1 ELSE 0 END) as testing_pending_review_count,"], [1359, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"]], "['\\\"]approved['\\\"]": [[160, "'approved': '审核通过',"], [411, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_materials,"], [485, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [754, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [797, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_materials,"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1144, "WHEN 'approved' THEN 7"], [1162, "'testing_pending_review': '测试待审核', 'approved': '审核通过',"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1349, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [1610, "AND lc.status = 'approved'"]], "['\\\"]rejected['\\\"]": [[161, "'rejected': '审核拒绝',"], [412, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_materials,"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [798, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_materials"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [994, "AND lc.status IN ('upload_failed', 'rejected')"], [1006, "rejected = len([m for m in failed_materials if m.status == 'rejected'])"], [1145, "WHEN 'rejected' THEN 8"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1350, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]new['\\\"]": [[155, "'new': '新建',"], [534, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [896, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [1138, "WHEN 'new' THEN 1"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1285, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]pending_grouping['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1139, "WHEN 'pending_grouping' THEN 2"], [1160, "'new': '新建', 'pending_grouping': '待分组', 'processing': '处理中',"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1351, "SUM(CASE WHEN lc.status = 'pending_grouping' THEN 1 ELSE 0 END) as pending_grouping_count,"], [1358, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[162, "'upload_failed': '上传失败'"], [413, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials"], [536, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败'"], [559, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"], [755, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_count"], [898, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [994, "AND lc.status IN ('upload_failed', 'rejected')"], [1005, "upload_failed = len([m for m in failed_materials if m.status == 'upload_failed'])"], [1021, "'失败类型': '上传失败' if material.status == 'upload_failed' else '审核拒绝',"], [1147, "WHEN 'upload_failed' THEN 10"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1287, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [1356, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as upload_failed_count,"], [1360, "'upload_failed', 'already_tested') THEN 1 ELSE 0 END) as other_status_count"]], "['\\\"]already_tested['\\\"]": [[899, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试',"], [1146, "WHEN 'already_tested' THEN 9"], [1163, "'rejected': '审核拒绝', 'already_tested': '已测试', 'upload_failed': '上传失败'"], [1223, "if row.status in ['approved', 'rejected', 'already_tested']:"], [1288, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [1357, "SUM(CASE WHEN lc.status = 'already_tested' THEN 1 ELSE 0 END) as already_tested_count,"], [1360, "'upload_failed', 'already_tested') THEN 1 ELSE 0 END) as other_status_count"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_enhanced_material_analytics.py": {"['\\\"]pending_upload['\\\"]": [[424, "LocalCreative.status.in_(['pending_upload', 'creating_plan', 'testing_pending_review'])"], [593, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[594, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[424, "LocalCreative.status.in_(['pending_upload', 'creating_plan', 'testing_pending_review'])"], [596, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[319, "if stat.status == 'approved')"], [492, "LocalCreative.status == 'approved',"], [531, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved"], [597, "'approved': '审核通过',"], [858, "func.sum(func.case([(LocalCreative.status == 'approved', 1)], else_=0)).label('approved_materials'),"]], "['\\\"]rejected['\\\"]": [[598, "'rejected': '审核拒绝',"], [794, "LocalCreative.status == 'rejected',"], [859, "func.sum(func.case([(LocalCreative.status == 'rejected', 1)], else_=0)).label('rejected_materials')"]], "['\\\"]new['\\\"]": [[592, "'new': '新建',"]], "['\\\"]pending_grouping['\\\"]": [[601, "'pending_grouping': '待分组'"]], "['\\\"]upload_failed['\\\"]": [[599, "'upload_failed': '上传失败',"], [763, "LocalCreative.status == 'upload_failed',"]], "['\\\"]already_tested['\\\"]": [[600, "'already_tested': '已测试',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_lifecycle_monitor.py": {"['\\\"]approved['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"], [41, "COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved,"], [47, "AND lc.status IN ('approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"], [42, "COUNT(CASE WHEN lc.status = 'rejected' THEN 1 END) as rejected"], [47, "AND lc.status IN ('approved', 'rejected')"]], "['\\\"]already_tested['\\\"]": [[32, "AND lc.status NOT IN ('approved', 'rejected', 'already_tested')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_monitoring_dashboard.py": {"['\\\"]pending_upload['\\\"]": [[44, "{\"status\":\"pending_upload\",\"count\":25}"]], "['\\\"]uploading['\\\"]": [[45, "{\"status\":\"uploading\",\"count\":8}"]], "['\\\"]approved['\\\"]": [[43, "return '''{\"status\":\"approved\",\"count\":1481}"]], "['\\\"]rejected['\\\"]": [[48, "{\"status\":\"rejected\",\"count\":23}'''"]], "['\\\"]harvested['\\\"]": [[51, "{\"harvest_status\":\"harvested\",\"count\":281}"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_duration_test.py": {"['\\\"]pending_upload['\\\"]": [[26, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[37, "AVG(CASE WHEN lc.status = 'uploaded_pending_plan'"], [79, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [89, "'uploaded_pending_plan': '已上传待建计划'"], [105, "elif row.status == 'uploaded_pending_plan' and row.max_hours > 2:"], [123, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"]], "['\\\"]testing_pending_review['\\\"]": [[26, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [33, "AVG(CASE WHEN lc.status = 'testing_pending_review'"], [79, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [87, "'testing_pending_review': '测试待审核',"], [101, "if row.status == 'testing_pending_review' and row.max_hours > 24:"], [123, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [143, "if result.status == 'testing_pending_review' and result.hours_since_update > 12:"]], "['\\\"]upload_failed['\\\"]": [[27, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]processing['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [72, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"], [96, "SUM(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 ELSE 0 END) as uploaded_pending_plan_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[40, "'creating_plan': '创建计划中', 'testing_pending_review': '测试待审核',"], [72, "'uploaded_pending_plan', 'creating_plan', 'testing_pending_review',"], [97, "SUM(CASE WHEN lc.status = 'testing_pending_review' THEN 1 ELSE 0 END) as testing_pending_review_count,"], [100, "'testing_pending_review', 'already_tested') THEN 1 ELSE 0 END) as other_count"]], "['\\\"]approved['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [93, "SUM(CASE WHEN lc.status = 'approved' THEN 1 ELSE 0 END) as approved_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]rejected['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"], [94, "SUM(CASE WHEN lc.status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,"], [99, "SUM(CASE WHEN lc.status NOT IN ('approved', 'rejected', 'creating_plan', 'uploaded_pending_plan',"]], "['\\\"]new['\\\"]": [[39, "'new': '新建', 'pending_upload': '待上传', 'uploaded_pending_plan': '已上传待建计划',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]pending_grouping['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [71, "AND lc.status NOT IN ('approved', 'rejected', 'pending_grouping', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[41, "'approved': '审核通过', 'rejected': '审核拒绝', 'upload_failed': '上传失败',"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"]], "['\\\"]already_tested['\\\"]": [[42, "'pending_grouping': '待分组', 'processing': '处理中', 'already_tested': '已测试'"], [73, "'upload_failed', 'already_tested', 'new', 'pending_upload')"], [98, "SUM(CASE WHEN lc.status = 'already_tested' THEN 1 ELSE 0 END) as already_tested_count,"], [100, "'testing_pending_review', 'already_tested') THEN 1 ELSE 0 END) as other_count"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_system_monitoring_page.py": {"['\\\"]pending_upload['\\\"]": [[408, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[409, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[411, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[412, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[413, "'rejected': '审核拒绝',"]], "['\\\"]new['\\\"]": [[407, "'new': '新建',"]], "['\\\"]pending_grouping['\\\"]": [[416, "'pending_grouping': '待分组'"]], "['\\\"]upload_failed['\\\"]": [[414, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[415, "'already_tested': '已测试',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_webui_monitoring_integration.py": {"['\\\"]pending_upload['\\\"]": [[279, "{\"status\":\"pending_upload\",\"count\":25}"]], "['\\\"]uploading['\\\"]": [[97, "WHERE status IN ('uploading', 'plan_pending', 'creating_plan')"], [280, "{\"status\":\"uploading\",\"count\":8}"], [287, "return '''{\"status\":\"uploading\",\"stuck_count\":8}"]], "['\\\"]approved['\\\"]": [[70, "COUNT(CASE WHEN harvest_status = 'not_harvested' AND status = 'approved' THEN 1 END) as pending_harvest"], [72, "WHERE status = 'approved'"], [150, "approved_count = material_dist.get('approved', 0)"], [278, "return '''{\"status\":\"approved\",\"count\":1481}"]], "['\\\"]rejected['\\\"]": [[283, "{\"status\":\"rejected\",\"count\":23}'''"]], "['\\\"]harvested['\\\"]": [[69, "COUNT(CASE WHEN harvest_status = 'harvested' THEN 1 END) as harvested_count,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_workflow_optimization_test.py": {"['\\\"]pending_upload['\\\"]": [[121, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[62, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [71, "'uploaded_pending_plan': '已上传待建计划'"]], "['\\\"]testing_pending_review['\\\"]": [[62, "AND lc.status IN ('testing_pending_review', 'creating_plan', 'uploaded_pending_plan')"], [69, "'testing_pending_review': '测试待审核',"], [121, "SUM(CASE WHEN lc.status IN ('pending_upload', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_materials,"], [123, "AVG(CASE WHEN lc.status = 'testing_pending_review'"]], "['\\\"]upload_failed['\\\"]": [[122, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_materials,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_analysis.py": {"['\\\"]processing['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"], [245, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]pending_grouping['\\\"]": [[151, "AND lc.status IN ('pending_grouping', 'processing', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]upload_failed['\\\"]": [[184, "AND lc.status = 'upload_failed'"], [245, "AND lc.status IN ('processing', 'upload_failed')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_fixes.py": {"['\\\"]processing['\\\"]": [[205, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]pending_grouping['\\\"]": [[294, "SET status = 'pending_grouping', updated_at = NOW()"]], "['\\\"]upload_failed['\\\"]": [[205, "AND lc.status IN ('processing', 'upload_failed')"], [299, "AND lc.status = 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_quick_file_fix.py": {"['\\\"]processing['\\\"]": [[34, "AND lc.status IN ('processing', 'upload_failed')"]], "['\\\"]pending_grouping['\\\"]": [[85, "SET status = 'pending_grouping', updated_at = NOW()"]], "['\\\"]upload_failed['\\\"]": [[34, "AND lc.status IN ('processing', 'upload_failed')"], [90, "AND lc.status = 'upload_failed'"], [173, "if row.status == 'upload_failed':"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_system_monitoring.py": {"['\\\"]processing['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"]], "['\\\"]uploaded_pending_plan['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"], [66, "AND lc.status IN ('uploaded_pending_plan', 'creating_plan', 'testing_pending_review')"]], "['\\\"]testing_pending_review['\\\"]": [[30, "SUM(CASE WHEN lc.status IN ('processing', 'uploaded_pending_plan', 'creating_plan', 'testing_pending_review') THEN 1 ELSE 0 END) as processing_count"], [66, "AND lc.status IN ('uploaded_pending_plan', 'creating_plan', 'testing_pending_review')"]], "['\\\"]approved['\\\"]": [[29, "SUM(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 ELSE 0 END) as completed_count,"]], "['\\\"]rejected['\\\"]": [[29, "SUM(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 ELSE 0 END) as completed_count,"]], "['\\\"]upload_failed['\\\"]": [[28, "SUM(CASE WHEN lc.status = 'upload_failed' THEN 1 ELSE 0 END) as failed_count,"], [92, "AND lc.status = 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_appeal_fixes.py": {"['\\\"]rejected['\\\"]": [[313, "appeal_needed_statuses = ['rejected', 'under_review', 'failed_review']"], [746, "appealable_statuses = ['rejected', 'under_review', 'failed_review']"], [939, "AND ap.status IN ('rejected', 'under_review', 'failed_review')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_celery_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[44, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan'])"], [48, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [53, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan'])"], [57, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [61, "old_status_check = \"\"\"if locked_creative.status != 'uploaded_pending_plan':\"\"\""], [62, "new_status_check = \"\"\"if locked_creative.status not in ['uploaded_pending_plan', 'approved']:\"\"\""]], "['\\\"]approved['\\\"]": [[48, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [57, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [62, "new_status_check = \"\"\"if locked_creative.status not in ['uploaded_pending_plan', 'approved']:\"\"\""]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_comprehensive_material_verification.py": {"['\\\"]new['\\\"]": [[129, "WHERE lc.status = 'new'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[80, "AND lc.status IN ('uploaded_pending_plan', 'approved', 'creating_plan')) as available_materials"], [147, "AND lc.status IN ('uploaded_pending_plan', 'approved')) as available_materials"], [203, "if \"LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])\" in content:"], [206, "if \"locked_creative.status not in ['uploaded_pending_plan', 'approved']\" in content:"], [217, "if \"if locked_creative.status != 'uploaded_pending_plan':\" in content:"]], "['\\\"]approved['\\\"]": [[80, "AND lc.status IN ('uploaded_pending_plan', 'approved', 'creating_plan')) as available_materials"], [114, "SET status = 'approved',"], [147, "AND lc.status IN ('uploaded_pending_plan', 'approved')) as available_materials"], [203, "if \"LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])\" in content:"], [206, "if \"locked_creative.status not in ['uploaded_pending_plan', 'approved']\" in content:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_log_analysis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[35, "'uploaded_pending_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[37, "'testing_pending_review',"]], "['\\\"]approved['\\\"]": [[38, "'approved',  # 或 'rejected'"]], "['\\\"]rejected['\\\"]": [[38, "'approved',  # 或 'rejected'"]], "['\\\"]new['\\\"]": [[33, "'new',"]], "['\\\"]pending_grouping['\\\"]": [[34, "'pending_grouping',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deploy_verification.py": {"['\\\"]approved['\\\"]": [[96, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed_uploads"], [162, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]rejected['\\\"]": [[96, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed_uploads"], [162, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]upload_failed['\\\"]": [[95, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_uploads,"], [161, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_duplicate_plan_emergency_fix.py": {"['\\\"]already_tested['\\\"]": [[244, "SET status = 'already_tested',"], [374, "'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_fix.py": {"['\\\"]pending_upload['\\\"]": [[144, "('pending_upload', 48),  # pending_upload超过48小时"], [165, "new_status = 'pending_upload'"], [166, "elif status == 'pending_upload':"], [169, "new_status = 'pending_upload'  # 重试上传"]], "['\\\"]uploaded_pending_plan['\\\"]": [[74, "SET status = 'uploaded_pending_plan',"], [102, "WHERE status = 'uploaded_pending_plan'"], [120, "WHERE status = 'uploaded_pending_plan'"], [211, "WHERE lc.status = 'uploaded_pending_plan'"], [219, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[118, "SET status = 'approved',"]], "['\\\"]new['\\\"]": [[143, "('new', 72),  # new状态超过72小时"], [164, "if status == 'new':"], [167, "new_status = 'new'  # 重新开始"]], "['\\\"]upload_failed['\\\"]": [[145, "('upload_failed', 24),  # upload_failed超过24小时"], [168, "elif status == 'upload_failed':"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[373, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' AND c.id IS NOT NULL THEN 1 END) as pending_but_has_plan,"]], "['\\\"]testing_pending_review['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]approved['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]rejected['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]new['\\\"]": [[372, "COUNT(CASE WHEN lc.status = 'new' AND pc.id IS NOT NULL THEN 1 END) as new_but_uploaded,"]], "['\\\"]already_tested['\\\"]": [[355, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_export_new_materials_excel.py": {"['\\\"]new['\\\"]": [[64, "WHERE lc.status = 'new'"], [290, "WHERE status = 'new';"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_final_solution.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[115, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [144, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]], "['\\\"]approved['\\\"]": [[115, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [144, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_fix_scheduler_logic.py": {"['\\\"]already_tested['\\\"]": [[54, "'already_tested', '重复', 'duplicate'"], [95, "locked_creative.status = 'already_tested'"], [124, "locked_creative.status = 'already_tested'"], [137, "locked_creative.status = 'already_tested'"], [194, "locked_creative.status = 'already_tested'"], [223, "locked_creative.status = 'already_tested'"], [236, "locked_creative.status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_handle_missing_files.py": {"['\\\"]new['\\\"]": [[44, "WHERE lc.status = 'new'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[131, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[237, "SET status = 'testing_pending_review',"], [246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]approved['\\\"]": [[246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[246, "AND status NOT IN ('testing_pending_review', 'approved', 'rejected')"]], "['\\\"]already_tested['\\\"]": [[117, "SET status = 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_log_analysis_verification.py": {"['\\\"]testing_pending_review['\\\"]": [[277, "SET status = 'testing_pending_review',"]], "['\\\"]new['\\\"]": [[186, "WHERE status = 'new'"], [220, "WHERE lc.status = 'new'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_platform_creative_fix.py": {"['\\\"]approved['\\\"]": [[42, "WHERE lc.status = 'approved'"], [64, "WHERE lc.status = 'approved'"], [158, "WHERE lc.status = 'approved'"], [219, "WHERE lc.status = 'approved'"], [253, "WHERE lc.status = 'approved'"], [285, "WHERE lc.status = 'approved'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_realtime_monitor.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[29, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [58, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]], "['\\\"]approved['\\\"]": [[29, "WHERE status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [58, "AND lc.status IN ('approved', 'uploaded_pending_plan')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_flow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[106, "if 'approved' in content and 'uploaded_pending_plan' in content:"], [111, "if ('approved' in line and 'uploaded_pending_plan' in line) or \\"], [112, "('status' in line and '=' in line and ('approved' in line or 'uploaded_pending_plan' in line)):"], [153, "SET status = 'uploaded_pending_plan',"], [184, "SET status = 'uploaded_pending_plan',"], [223, "AND lc.status IN ('approved', 'uploaded_pending_plan')"], [243, "WHERE lc.status = 'uploaded_pending_plan'"], [312, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [332, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[44, "WHERE lc.status = 'approved'"], [106, "if 'approved' in content and 'uploaded_pending_plan' in content:"], [111, "if ('approved' in line and 'uploaded_pending_plan' in line) or \\"], [112, "('status' in line and '=' in line and ('approved' in line or 'uploaded_pending_plan' in line)):"], [138, "WHERE lc.status = 'approved'"], [155, "WHERE status = 'approved'"], [161, "WHERE lc.status = 'approved'"], [173, "WHERE lc.status = 'approved'"], [223, "AND lc.status IN ('approved', 'uploaded_pending_plan')"], [312, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_monitor.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[33, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"], [53, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[33, "AND lc.status IN ('approved', 'uploaded_pending_plan', 'creating_plan')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_system_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[168, "COUNT(CASE WHEN m.status = 'uploaded_pending_plan' THEN 1 END) as pending_plan_count"], [251, "'uploaded', 'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]approved['\\\"]": [[252, "'plan_created', 'monitoring', 'approved', 'rejected'"]], "['\\\"]rejected['\\\"]": [[252, "'plan_created', 'monitoring', 'approved', 'rejected'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_upload_stability_analysis.py": {"['\\\"]upload_failed['\\\"]": [[120, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed_count"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_validation_dashboard.py": {"['\\\"]approved['\\\"]": [[35, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]rejected['\\\"]": [[35, "COUNT(CASE WHEN lc.status IN ('approved', 'rejected') THEN 1 END) as completed,"]], "['\\\"]upload_failed['\\\"]": [[34, "COUNT(CASE WHEN lc.status = 'upload_failed' THEN 1 END) as failed,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_verify_new_materials.py": {"['\\\"]new['\\\"]": [[48, "WHERE lc.status = 'new'"], [74, "WHERE status = 'new'"], [111, "WHERE lc.status = 'new'"]], "['\\\"]already_tested['\\\"]": [[176, "SET status = 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_verify_recovered_files.py": {"['\\\"]new['\\\"]": [[49, "WHERE lc.status = 'new'"], [118, "WHERE lc.status = 'new'"], [134, "WHERE lc.status != 'new'"]], "['\\\"]already_tested['\\\"]": [[250, "SET status = 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_analysis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[132, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 END) as pending_materials,"], [203, "if 'uploaded_pending_plan' in line and 'filter' in line:"]], "['\\\"]approved['\\\"]": [[131, "COUNT(CASE WHEN lc.status = 'approved' THEN 1 END) as approved_materials,"], [276, "WHERE lc.status = 'approved'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_monitor.py": {"['\\\"]pending_upload['\\\"]": [[137, "WHEN status = 'pending_upload' THEN '2-待上传'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]], "['\\\"]processing['\\\"]": [[138, "WHEN status = 'processing' THEN '3-上传中'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[139, "WHEN status = 'uploaded_pending_plan' THEN '4-待建计划'"], [149, "'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[141, "WHEN status = 'testing_pending_review' THEN '6-待审核'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]approved['\\\"]": [[142, "WHEN status = 'approved' THEN '7-已通过'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]rejected['\\\"]": [[143, "WHEN status = 'rejected' THEN '8-已拒绝'"], [150, "'testing_pending_review', 'approved', 'rejected')"]], "['\\\"]new['\\\"]": [[40, "if row.status == 'new':"], [136, "WHEN status = 'new' THEN '1-待处理'"], [148, "WHERE status IN ('new', 'pending_upload', 'processing',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_restoration.py": {"['\\\"]pending_upload['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]processing['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[110, "'uploaded_pending_plan', 'creating_plan',"], [333, "'uploaded_pending_plan', 'creating_plan',"]], "['\\\"]testing_pending_review['\\\"]": [[111, "'testing_pending_review', 'approved', 'rejected'"], [252, "SET status = 'testing_pending_review',"], [334, "'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]approved['\\\"]": [[64, "if row.status == 'approved' and row.has_campaign == 0:"], [104, "logger.info(\"  - 'approved': 498个无campaign - 状态含义混乱\")"], [111, "'testing_pending_review', 'approved', 'rejected'"], [113, "'problematic_statuses': ['already_tested', 'approved'],"], [139, "WHERE lc.status = 'approved'"], [162, "logger.info(\"  'approved'状态是本地检测逻辑错误产生的\")"], [172, "WHERE status = 'approved'"], [334, "'testing_pending_review', 'approved', 'rejected']:"], [345, "WHERE status IN ('already_tested', 'approved')"]], "['\\\"]rejected['\\\"]": [[111, "'testing_pending_review', 'approved', 'rejected'"], [334, "'testing_pending_review', 'approved', 'rejected']:"]], "['\\\"]new['\\\"]": [[109, "'new', 'pending_upload', 'processing',"], [170, "SET status = 'new',"], [233, "SET status = 'new',"], [329, "if row.status == 'new':"], [332, "if row.status in ['new', 'pending_upload', 'processing',"]], "['\\\"]already_tested['\\\"]": [[70, "elif row.status == 'already_tested' and row.count > 1000:"], [103, "logger.info(\"  - 'already_tested': 1685个 - 这是本地检测逻辑产生的\")"], [113, "'problematic_statuses': ['already_tested', 'approved'],"], [114, "'local_detection_artifacts': ['already_tested']"], [209, "WHERE lc.status = 'already_tested'"], [235, "WHERE status = 'already_tested'"], [254, "WHERE status = 'already_tested'"], [345, "WHERE status IN ('already_tested', 'approved')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py": {"['\\\"]pending_upload['\\\"]": [[101, "WHERE lc.status IN ('upload_failed', 'pending_upload')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [61, "COUNT(CASE WHEN lc.status = 'uploaded_pending_plan' THEN 1 END) as uploaded_pending_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [121, "WHERE lc.status IN ('pending_grouping', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]new['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [59, "COUNT(CASE WHEN lc.status = 'new' THEN 1 END) as new_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"]], "['\\\"]pending_grouping['\\\"]": [[41, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [60, "COUNT(CASE WHEN lc.status = 'pending_grouping' THEN 1 END) as pending_grouping_count,"], [67, "WHERE lc.status IN ('new', 'pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [121, "WHERE lc.status IN ('pending_grouping', 'uploaded_pending_plan', 'creating_plan')"], [432, "LocalCreative.status == 'pending_grouping',"]], "['\\\"]upload_failed['\\\"]": [[101, "WHERE lc.status IN ('upload_failed', 'pending_upload')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250723_immediate_grouping_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[141, "LocalCreative.status.in_(['pending_grouping', 'uploaded_pending_plan']),"]], "['\\\"]pending_grouping['\\\"]": [[141, "LocalCreative.status.in_(['pending_grouping', 'uploaded_pending_plan']),"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_recovery_executor.py": {"['\\\"]new['\\\"]": [[199, "creative.status = 'new'"]], "['\\\"]upload_failed['\\\"]": [[188, "LocalCreative.status == 'upload_failed',"]], "WorkflowStatus\\.": [[101, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [115, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_resilience_system.py": {"['\\\"]processing['\\\"]": [[105, "'new', 'processing'"]], "['\\\"]rejected['\\\"]": [[102, "'rejected'"]], "['\\\"]new['\\\"]": [[105, "'new', 'processing'"]], "WorkflowStatus\\.": [[97, "WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [98, "WorkflowStatus.APPROVED.value"], [101, "WorkflowStatus.UPLOAD_FAILED.value,"], [129, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [218, "LocalCreative.status == WorkflowStatus.APPROVED.value"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\appeal_workflow_integration_patch.py": {"['\\\"]rejected['\\\"]": [[94, "AND ap.status IN ('rejected', 'under_review', 'failed_review')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\enhanced_duplicate_check_logic.py": {"['\\\"]already_tested['\\\"]": [[16, "locked_creative.status = 'already_tested'"], [45, "locked_creative.status = 'already_tested'"], [58, "locked_creative.status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py": {"['\\\"]processing['\\\"]": [[232, "WHERE status = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[205, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[180, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"]], "['\\\"]upload_failed['\\\"]": [[285, "WHERE status = 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py": {"['\\\"]processing['\\\"]": [[201, "LocalCreative.status == 'processing'"], [373, "WHERE status = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[390, "WHERE lc.status = 'uploaded_pending_plan'"]], "['\\\"]upload_failed['\\\"]": [[220, "LocalCreative.status == 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py": {"['\\\"]approved['\\\"]": [[277, "approved_materials = [lc for lc in account_materials if lc['status'] == 'approved']"], [506, "daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}"], [514, "daily_stats[date_key] = {'materials': 0, 'campaigns': 0, 'approved': 0}"], [518, "daily_stats[date_key]['approved'] += 1"], [525, "approved_count = [daily_stats[date]['approved'] for date in dates]"]], "['\\\"]rejected['\\\"]": [[278, "rejected_materials = [lc for lc in account_materials if lc['status'] == 'rejected']"]], "['\\\"]harvested['\\\"]": [[257, "harvested_materials = [lc for lc in data['local_creatives_raw'] if lc['harvest_status'] == 'harvested']"], [276, "harvested_materials = [lc for lc in account_materials if lc['harvest_status'] == 'harvested']"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py": {"['\\\"]pending_upload['\\\"]": [[146, "if creative and creative.status == 'pending_upload':"]], "['\\\"]processing['\\\"]": [[92, "with manager.atomic_state_transition(creative_id, 'processing', 'uploaded_pending_plan') as creative:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[92, "with manager.atomic_state_transition(creative_id, 'processing', 'uploaded_pending_plan') as creative:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py": {"['\\\"]pending_upload['\\\"]": [[123, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[119, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[127, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py": {"['\\\"]pending_upload['\\\"]": [[94, "logger.info(\"       pending = db.query(LocalCreative).filter(LocalCreative.status=='pending_upload').count()\")"]], "['\\\"]processing['\\\"]": [[93, "logger.info(\"       processing = db.query(LocalCreative).filter(LocalCreative.status=='processing').count()\")"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py": {"['\\\"]pending_upload['\\\"]": [[156, "'pending_upload': '📤',"], [169, "pending_count = status_counts.get('pending_upload', 0)"], [188, "'pending_upload': pending_count,"]], "['\\\"]processing['\\\"]": [[157, "'processing': '⏳',"], [168, "processing_count = status_counts.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[158, "'uploaded_pending_plan': '📋',"], [170, "uploaded_count = status_counts.get('uploaded_pending_plan', 0)"], [189, "'uploaded_pending_plan': uploaded_count,"]], "['\\\"]testing_pending_review['\\\"]": [[159, "'testing_pending_review': '🔍',"]], "['\\\"]approved['\\\"]": [[160, "'approved': '✅',"]], "['\\\"]rejected['\\\"]": [[161, "'rejected': '❌',"]], "['\\\"]upload_failed['\\\"]": [[162, "'upload_failed': '💀'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py": {"['\\\"]testing_pending_review['\\\"]": [[180, "LocalCreative.status == 'testing_pending_review'"]], "['\\\"]rejected['\\\"]": [[174, "LocalCreative.status == 'rejected',"]], "['\\\"]harvested['\\\"]": [[133, "LocalCreative.harvest_status == 'harvested',"], [229, "db_material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[118, "LocalCreative.status == 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py": {"['\\\"]processing['\\\"]": [[236, "elif status == 'processing' and count > 0:"], [250, "processing_count = status_counts.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[238, "elif status == 'uploaded_pending_plan' and count > 0:"], [251, "pending_plan_count = status_counts.get('uploaded_pending_plan', 0)"], [383, "pending_plan_count = workflow_status.get('uploaded_pending_plan', 0)"]], "['\\\"]approved['\\\"]": [[240, "elif status == 'approved' and count > 0:"], [252, "approved_count = status_counts.get('approved', 0)"]], "['\\\"]new['\\\"]": [[234, "if status == 'new' and count > 0:"], [249, "new_count = status_counts.get('new', 0)"], [382, "new_count = workflow_status.get('new', 0)"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py": {"['\\\"]processing['\\\"]": [[122, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing'])"], [136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[122, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing'])"]], "['\\\"]new['\\\"]": [[136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]], "['\\\"]pending_grouping['\\\"]": [[136, "LocalCreative.status.in_(['new', 'processing', 'pending_grouping'])"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py": {"['\\\"]pending_upload['\\\"]": [[165, "if status in ['pending_grouping', 'pending_upload'] and count > 0:"]], "['\\\"]pending_grouping['\\\"]": [[165, "if status in ['pending_grouping', 'pending_upload'] and count > 0:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250803_workflow_final_analysis.py": {"['\\\"]pending_grouping['\\\"]": [[209, "if 'pending_grouping' in analysis_result['status_analysis']:"], [210, "count = analysis_result['status_analysis']['pending_grouping']"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py": {"['\\\"]processing['\\\"]": [[98, "LocalCreative.status == 'processing'"], [143, "LocalCreative.status == 'processing'"]], "['\\\"]pending_grouping['\\\"]": [[124, "LocalCreative.status == 'pending_grouping'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py": {"['\\\"]approved['\\\"]": [[92, "LocalCreative.status == 'approved',"], [101, "LocalCreative.status == 'approved'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py": {"['\\\"]pending_upload['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]uploading['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]processing['\\\"]": [[88, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan', 'pending_upload'])"]], "['\\\"]pending_grouping['\\\"]": [[98, "material.status = 'pending_grouping'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_business_rules_fix.py": {"['\\\"]already_tested['\\\"]": [[157, "if local_creative and local_creative.status != 'already_tested':"], [158, "local_creative.status = 'already_tested'"], [256, "WHERE status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py": {"['\\\"]pending_upload['\\\"]": [[133, "'pending_upload': 7,"]], "['\\\"]uploading['\\\"]": [[175, "LocalCreative.status.in_(['processing', 'creating_plan', 'uploading'])"]], "['\\\"]processing['\\\"]": [[107, "if primary_record.status in ['processing', 'creating_plan']:"], [138, "'processing': 2,"], [175, "LocalCreative.status.in_(['processing', 'creating_plan', 'uploading'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[132, "'uploaded_pending_plan': 8,"]], "['\\\"]testing_pending_review['\\\"]": [[131, "'testing_pending_review': 9,"]], "['\\\"]rejected['\\\"]": [[137, "'rejected': 3,"]], "['\\\"]new['\\\"]": [[135, "'new': 5,"]], "['\\\"]pending_grouping['\\\"]": [[109, "primary_record.status = 'pending_grouping'"], [134, "'pending_grouping': 6,"], [186, "record.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[136, "'upload_failed': 4,"]], "['\\\"]already_tested['\\\"]": [[130, "'already_tested': 10,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_harvest_duplicate_fix.py": {"['\\\"]harvested['\\\"]": [[157, "if material.harvest_status != 'harvested':"], [158, "material.harvest_status = 'harvested'"], [255, "material.harvest_status = 'harvested'"], [317, "4. 收割后必须更新数据库状态为 'harvested'"]], "['\\\"]already_tested['\\\"]": [[227, "LocalCreative.status == 'already_tested',"], [320, "1. 查询 status='already_tested' AND harvest_status='not_harvested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[130, "WHERE status = 'uploaded_pending_plan';"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py": {"['\\\"]approved['\\\"]": [[120, "LocalCreative.status == 'approved'"], [152, "if material.status == 'approved':"]], "['\\\"]harvested['\\\"]": [[154, "material.status = 'harvested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [113, "pending_upload_count = status_counts.get('pending_upload', 0)"], [118, "'pending_upload': '📤',"], [192, "'pending_grouping' in content and 'pending_upload' in content  # 状态转换"]], "['\\\"]processing['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [112, "processing_count = status_counts.get('processing', 0)"], [119, "'processing': '⏳',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[100, "'pending_upload', 'processing', 'uploaded_pending_plan',"], [120, "'uploaded_pending_plan': '📋',"]], "['\\\"]testing_pending_review['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]], "['\\\"]approved['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"], [121, "'approved': '✅',"]], "['\\\"]rejected['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"], [122, "'rejected': '❌'"]], "['\\\"]pending_grouping['\\\"]": [[192, "'pending_grouping' in content and 'pending_upload' in content  # 状态转换"]], "['\\\"]upload_failed['\\\"]": [[101, "'testing_pending_review', 'approved', 'rejected', 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py": {"['\\\"]pending_upload['\\\"]": [[178, "creative.status = 'pending_upload'"], [206, "if creative.status != 'pending_upload':"], [211, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as processing_creative:"]], "['\\\"]processing['\\\"]": [[65, "LocalCreative.status == 'processing'"], [70, "LocalCreative.status == 'processing',"], [84, "WHERE status = 'processing'"], [169, "LocalCreative.status == 'processing',"], [211, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as processing_creative:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_direct_config_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[58, "LocalCreative.status == 'uploaded_pending_plan'"], [77, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[82, "LocalCreative.status == 'testing_pending_review'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py": {"['\\\"]pending_upload['\\\"]": [[162, "test_creative.status = 'pending_upload'"], [180, "'conversion_successful': new_status == 'pending_upload'"]], "['\\\"]pending_grouping['\\\"]": [[71, "WHERE lc.status = 'pending_grouping'"], [112, "'pending_grouping_query': 'pending_grouping' in content and 'batch_upload_videos' in content,"], [114, "'commit_changes': 'db.commit()' in content and 'pending_grouping' in content,"], [147, "LocalCreative.status == 'pending_grouping'"], [196, "LocalCreative.status == 'pending_grouping'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_plan_creation_deep_diagnosis.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[65, "LocalCreative.status == 'uploaded_pending_plan'"], [129, "LocalCreative.status == 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[135, "LocalCreative.status == 'testing_pending_review'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_plan_creation_fix_test.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[91, "if status == 'uploaded_pending_plan':"], [124, "'direct_local_creative_query': 'db.query(LocalCreative).filter(' in content and 'uploaded_pending_plan' in content,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_reassign_materials_to_test_accounts.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[66, "LocalCreative.status == 'uploaded_pending_plan',"], [175, "LocalCreative.status == 'uploaded_pending_plan',"], [183, "LocalCreative.status == 'uploaded_pending_plan',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py": {"['\\\"]pending_upload['\\\"]": [[105, "material.status = 'pending_upload'"], [183, "LocalCreative.status == 'pending_upload',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[66, "LocalCreative.status == 'uploaded_pending_plan',"], [190, "LocalCreative.status == 'uploaded_pending_plan',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [228, "successful_uploads = status_counts.get('uploaded_pending_plan', 0) + \\"]], "['\\\"]testing_pending_review['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [229, "status_counts.get('testing_pending_review', 0) + \\"]], "['\\\"]approved['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"], [230, "status_counts.get('approved', 0)"]], "['\\\"]rejected['\\\"]": [[226, "if status in ['uploaded_pending_plan', 'testing_pending_review', 'approved', 'rejected'])"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py": {"['\\\"]pending_upload['\\\"]": [[57, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[59, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[60, "'testing_pending_review': '测试中待审核',"]], "['\\\"]approved['\\\"]": [[61, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[62, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[63, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[55, "'new': '新素材',"]], "['\\\"]pending_grouping['\\\"]": [[56, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[58, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[64, "'already_tested': '已测试过',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py": {"['\\\"]pending_upload['\\\"]": [[54, "'pending_upload': '待上传',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[56, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[57, "'testing_pending_review': '测试中待审核',"]], "['\\\"]approved['\\\"]": [[58, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[59, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[60, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[52, "'new': '新素材',"]], "['\\\"]pending_grouping['\\\"]": [[53, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[55, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[61, "'already_tested': '已测试过',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\harvest\\ai_tool_20250726_harvest_proper_harvest.py": {"['\\\"]harvested['\\\"]": [[81, "material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[53, "LocalCreative.status == 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py": {"['\\\"]pending_upload['\\\"]": [[72, "creative.status = 'pending_upload'"], [135, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as creative:"]], "['\\\"]processing['\\\"]": [[62, "LocalCreative.status == 'processing',"], [135, "with self.atomic_state_transition(creative_id, 'pending_upload', 'processing') as creative:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py": {"['\\\"]pending_upload['\\\"]": [[114, "record.status = 'pending_upload'"]], "['\\\"]upload_failed['\\\"]": [[105, "LocalCreative.status == 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_comprehensive_workflow_fix.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[228, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as success,"]], "['\\\"]upload_failed['\\\"]": [[229, "COUNT(CASE WHEN status = 'upload_failed' THEN 1 END) as failed"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py": {"['\\\"]processing['\\\"]": [[149, "{'status': 'processing', 'count': 66, 'percentage': 47.83},"]], "['\\\"]uploaded_pending_plan['\\\"]": [[152, "{'status': 'uploaded_pending_plan', 'count': 13, 'percentage': 9.42},"], [163, "success_uploads = next((r['count'] for r in mock_status_results if r['status'] == 'uploaded_pending_plan'), 0)"]], "['\\\"]upload_failed['\\\"]": [[150, "{'status': 'upload_failed', 'count': 28, 'percentage': 20.29},"], [162, "failed_uploads = next((r['count'] for r in mock_status_results if r['status'] == 'upload_failed'), 0)"]], "['\\\"]already_tested['\\\"]": [[151, "{'status': 'already_tested', 'count': 18, 'percentage': 13.04},"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py": {"['\\\"]processing['\\\"]": [[300, "COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_uploads"]], "['\\\"]uploaded_pending_plan['\\\"]": [[298, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as successful_uploads,"]], "['\\\"]upload_failed['\\\"]": [[299, "COUNT(CASE WHEN status = 'upload_failed' THEN 1 END) as failed_uploads,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py": {"['\\\"]processing['\\\"]": [[199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]], "['\\\"]pending_grouping['\\\"]": [[166, "creative.status = 'pending_grouping'"], [199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]], "['\\\"]upload_failed['\\\"]": [[157, "LocalCreative.status == 'upload_failed',"], [199, "statuses = ['pending_grouping', 'upload_failed', 'uploaded', 'processing', 'completed']"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py": {"['\\\"]processing['\\\"]": [[413, "LocalCreative.status == 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[416, "'status': 'pending_grouping',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py": {"['\\\"]pending_upload['\\\"]": [[70, "PENDING_UPLOAD = \"pending_upload\"           # 待上传 (旧)"], [95, "'pending_upload': '待上传 (旧)',"]], "['\\\"]uploading['\\\"]": [[96, "'uploading': '上传中 (旧)',"]], "['\\\"]processing['\\\"]": [[53, "PROCESSING = \"processing\"                   # 处理中"], [78, "'processing': '处理中',"], [229, "LocalCreative.status.in_(['processing', 'creating_plan', 'testing_pending_review'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[54, "UPLOADED_PENDING_PLAN = \"uploaded_pending_plan\"  # 已上传待建计划"], [79, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[56, "TESTING_PENDING_REVIEW = \"testing_pending_review\"  # 测试待审核"], [81, "'testing_pending_review': '测试待审核',"], [229, "LocalCreative.status.in_(['processing', 'creating_plan', 'testing_pending_review'])"]], "['\\\"]approved['\\\"]": [[59, "APPROVED = \"approved\"                       # 审核通过"], [84, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[60, "REJECTED = \"rejected\"                       # 审核拒绝"], [85, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[62, "HARVESTED = \"harvested\"                     # 已收割"], [87, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[51, "NEW = \"new\"                                 # 新文件入库"], [76, "'new': '新入库',"]], "['\\\"]pending_grouping['\\\"]": [[52, "PENDING_GROUPING = \"pending_grouping\"       # 待分组"], [77, "'pending_grouping': '待分组',"]], "['\\\"]upload_failed['\\\"]": [[65, "UPLOAD_FAILED = \"upload_failed\"             # 上传失败"], [90, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[61, "ALREADY_TESTED = \"already_tested\"           # 已测试过"], [86, "'already_tested': '已测试',"]], "MaterialStatus\\.": [[110, "MaterialStatus.NEW: [MaterialStatus.PENDING_GROUPING],"], [111, "MaterialStatus.PENDING_GROUPING: [MaterialStatus.PROCESSING, MaterialStatus.UPLOAD_FAILED],"], [112, "MaterialStatus.PROCESSING: [MaterialStatus.UPLOADED_PENDING_PLAN, MaterialStatus.UPLOAD_FAILED],"], [113, "MaterialStatus.UPLOADED_PENDING_PLAN: [MaterialStatus.CREATING_PLAN, MaterialStatus.ALREADY_TESTED],"], [114, "MaterialStatus.CREATING_PLAN: [MaterialStatus.TESTING_PENDING_REVIEW, MaterialStatus.UPLOAD_FAILED],"], [115, "MaterialStatus.TESTING_PENDING_REVIEW: [MaterialStatus.APPROVED, MaterialStatus.REJECTED],"], [116, "MaterialStatus.APPROVED: [MaterialStatus.HARVESTED],"], [117, "MaterialStatus.REJECTED: [],  # 终态"], [118, "MaterialStatus.ALREADY_TESTED: [],  # 终态"], [119, "MaterialStatus.UPLOAD_FAILED: [MaterialStatus.PENDING_GROUPING],  # 重试"], [120, "MaterialStatus.CHECK_FAILED: [MaterialStatus.PENDING_GROUPING],   # 重试"], [121, "MaterialStatus.RESET_BY_SYSTEM: [MaterialStatus.PENDING_GROUPING], # 重置"], [122, "MaterialStatus.HARVESTED: [],  # 终态"], [123, "MaterialStatus.PENDING_UPLOAD: [MaterialStatus.PENDING_GROUPING],  # 向后兼容"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py": {"['\\\"]processing['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]], "['\\\"]approved['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]], "['\\\"]rejected['\\\"]": [[251, "'pending', 'processing', 'uploaded', 'approved', 'rejected'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py": {"['\\\"]processing['\\\"]": [[127, "result = WorkflowValidator.check_status_timeout('processing', old_time)"], [132, "result = WorkflowValidator.check_status_timeout('processing', recent_time)"], [375, "1, 'processing', 'uploaded_pending_plan'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[375, "1, 'processing', 'uploaded_pending_plan'"]], "['\\\"]approved['\\\"]": [[107, "1, 'new', 'approved'"], [388, "1, 'approved', 'harvested'"]], "['\\\"]harvested['\\\"]": [[388, "1, 'approved', 'harvested'"]], "['\\\"]new['\\\"]": [[101, "1, 'new', 'pending_grouping'"], [107, "1, 'new', 'approved'"], [114, "1, 'invalid_status', 'new'"]], "['\\\"]pending_grouping['\\\"]": [[101, "1, 'new', 'pending_grouping'"]], "WorkflowStatus\\.": [[73, "WorkflowStatus.NEW, WorkflowStatus.PENDING_GROUPING"], [77, "WorkflowStatus.PENDING_GROUPING, WorkflowStatus.PROCESSING"], [81, "WorkflowStatus.PROCESSING, WorkflowStatus.UPLOADED_PENDING_PLAN"], [86, "WorkflowStatus.NEW, WorkflowStatus.APPROVED"], [90, "WorkflowStatus.HARVESTED, WorkflowStatus.PROCESSING"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py": {"['\\\"]processing['\\\"]": [[153, "LocalCreative.status == 'processing'"], [285, "LocalCreative.status == 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[263, "creative.status = 'pending_grouping'"], [290, "creative.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[145, "LocalCreative.status == 'upload_failed'"], [257, "LocalCreative.status == 'upload_failed',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250727_auto_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[106, "SET status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250727_batch_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[95, "SET status = 'already_tested'"], [152, "-- UPDATE local_creatives SET status = 'already_tested' WHERE filename = '素材文件名';"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250727_emergency_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[175, "material.status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250727_immediate_compliance_fix.py": {"['\\\"]already_tested['\\\"]": [[74, "SET status = 'already_tested'"], [138, "SET status = 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250728_maintenance_material_file_organizer.py": {"['\\\"]new['\\\"]": [[7, "创建目的: 整理千川自动化项目中'new'状态素材的文件路径，确保路径一致性"], [73, "# 查询所有'new'状态的素材"], [77, "WHERE status = 'new'"], [256, "# 检查'new'状态素材的路径分布"], [267, "WHERE status = 'new'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py": {"['\\\"]uploading['\\\"]": [[31, "UPLOADING = \"uploading\""]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250801_workflow_fix.py": {"['\\\"]new['\\\"]": [[110, "status='new'  # 设置为new状态，等待处理"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py": {"['\\\"]approved['\\\"]": [[45, "LocalCreative.status == 'approved'"], [105, "if pc.local_creative.status != 'approved':"], [111, "if pc.local_creative.status != 'approved':"], [159, "LocalCreative.status == 'approved'"], [227, "LocalCreative.status == 'approved'"]], "['\\\"]harvested['\\\"]": [[193, "material.status = 'harvested'"], [294, "LocalCreative.status == 'harvested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py": {"['\\\"]approved['\\\"]": [[82, "if pc.local_creative.status != 'approved':"], [92, "if pc.local_creative.status != 'approved':"], [95, "pc.local_creative.status = 'approved'"], [215, "if \"pc.local_creative.status = 'approved'\" in content:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py": {"['\\\"]testing_pending_review['\\\"]": [[130, "if row.status in ['approved', 'testing_pending_review']:"]], "['\\\"]approved['\\\"]": [[130, "if row.status in ['approved', 'testing_pending_review']:"], [353, "WHEN lc.status = 'approved' AND pc.review_status NOT IN ('PASS', 'APPROVED') THEN 'local_approved_platform_not_pass'"], [355, "WHEN lc.harvest_status = 'not_harvested' AND lc.status = 'approved' AND pc.review_status = 'PASS' THEN 'harvest_pending'"]], "['\\\"]rejected['\\\"]": [[354, "WHEN lc.status = 'rejected' AND pc.review_status = 'PASS' THEN 'local_rejected_platform_pass'"]], "['\\\"]harvested['\\\"]": [[322, "if row.harvest_status == 'harvested':"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py": {"['\\\"]approved['\\\"]": [[236, "if audit_status == 'PASS' and current_status != 'approved':"], [238, "pc.local_creative.status = 'approved'"]], "['\\\"]rejected['\\\"]": [[242, "elif audit_status in ['REJECT', 'AUDIT_REJECT'] and current_status != 'rejected':"], [244, "pc.local_creative.status = 'rejected'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_web_report_optimization.py": {"['\\\"]harvested['\\\"]": [[65, "harvested_count = harvest_stats.get('harvested', 0)"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[90, "record.status = 'pending_upload'"]], "['\\\"]pending_grouping['\\\"]": [[80, "LocalCreative.status == 'pending_grouping'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py": {"['\\\"]pending_upload['\\\"]": [[41, "LocalCreative.status == 'pending_upload'"], [94, "WHERE status = 'pending_upload'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py": {"['\\\"]pending_upload['\\\"]": [[145, "if status_dict.get('pending_upload', 0) > 50:"], [148, "'issue': f'pending_upload状态堆积 ({status_dict[\"pending_upload\"]} 个)',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[98, "LocalCreative.status == 'uploaded_pending_plan'"], [153, "if status_dict.get('uploaded_pending_plan', 0) > 100:"], [156, "'issue': f'uploaded_pending_plan状态堆积 ({status_dict[\"uploaded_pending_plan\"]} 个)',"]], "['\\\"]testing_pending_review['\\\"]": [[161, "if status_dict.get('testing_pending_review', 0) > 200:"], [164, "'issue': f'testing_pending_review状态堆积 ({status_dict[\"testing_pending_review\"]} 个)',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py": {"['\\\"]pending_upload['\\\"]": [[75, "LocalCreative.status == 'pending_upload'"], [202, "(count for status, count in new_status_distribution if status == 'pending_upload'),"]], "WorkflowStatus\\.": [[162, "material.status = WorkflowStatus.TESTING_PENDING_REVIEW.value"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py": {"['\\\"]processing['\\\"]": [[265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[260, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]testing_pending_review['\\\"]": [[260, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [265, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py": {"['\\\"]processing['\\\"]": [[194, "creative.id, 'uploaded_pending_plan', 'processing'"], [260, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing']),"]], "['\\\"]uploaded_pending_plan['\\\"]": [[125, "creative.id, 'testing_pending_review', 'uploaded_pending_plan'"], [157, "'stage': 'uploaded_pending_plan',"], [170, "LocalCreative.status == 'uploaded_pending_plan',"], [194, "creative.id, 'uploaded_pending_plan', 'processing'"], [255, "LocalCreative.status.in_(['testing_pending_review', 'uploaded_pending_plan']),"], [260, "LocalCreative.status.in_(['uploaded_pending_plan', 'processing']),"]], "['\\\"]testing_pending_review['\\\"]": [[79, "'stage': 'testing_pending_review',"], [92, "LocalCreative.status == 'testing_pending_review',"], [112, "creative.id, 'testing_pending_review', 'approved'"], [125, "creative.id, 'testing_pending_review', 'uploaded_pending_plan'"], [255, "LocalCreative.status.in_(['testing_pending_review', 'uploaded_pending_plan']),"]], "['\\\"]approved['\\\"]": [[112, "creative.id, 'testing_pending_review', 'approved'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py": {"['\\\"]pending_upload['\\\"]": [[64, "COUNT(CASE WHEN status = 'pending_upload' THEN 1 END) as pending_upload,"]], "['\\\"]uploaded_pending_plan['\\\"]": [[65, "COUNT(CASE WHEN status = 'uploaded_pending_plan' THEN 1 END) as uploaded_pending_plan,"]], "['\\\"]rejected['\\\"]": [[67, "COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected"]], "['\\\"]new['\\\"]": [[63, "COUNT(CASE WHEN status = 'new' THEN 1 END) as new_status,"]], "['\\\"]already_tested['\\\"]": [[66, "COUNT(CASE WHEN status = 'already_tested' THEN 1 END) as already_tested,"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py": {"['\\\"]rejected['\\\"]": [[226, "WHERE lc.status = 'rejected'"], [237, "WHERE status = 'rejected';"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py": {"['\\\"]pending_upload['\\\"]": [[86, "'pending_upload': 0,"], [261, "logger.info(f\"   - 待上传: {materials['pending_upload']}\")"]], "['\\\"]uploaded_pending_plan['\\\"]": [[87, "'uploaded_pending_plan': 0,"], [129, "'upload_rate': (materials['uploaded_pending_plan'] + materials['already_tested']) / total_materials,"], [262, "logger.info(f\"   - 待创建计划: {materials['uploaded_pending_plan']}\")"]], "['\\\"]rejected['\\\"]": [[89, "'rejected': 0"], [264, "logger.info(f\"   - 已拒绝: {materials['rejected']}\")"]], "['\\\"]new['\\\"]": [[85, "'new': 0,"], [260, "logger.info(f\"   - 新入库: {materials['new']}\")"]], "['\\\"]already_tested['\\\"]": [[88, "'already_tested': 0,"], [129, "'upload_rate': (materials['uploaded_pending_plan'] + materials['already_tested']) / total_materials,"], [130, "'plan_creation_rate': materials['already_tested'] / total_materials if total_materials > 0 else 0,"], [263, "logger.info(f\"   - 已测试: {materials['already_tested']}\")"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py": {"['\\\"]testing_pending_review['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]approved['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]rejected['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]], "['\\\"]already_tested['\\\"]": [[311, "WHERE lc.status NOT IN ('testing_pending_review', 'approved', 'rejected', 'already_tested')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py": {"['\\\"]pending_upload['\\\"]": [[130, "'pending_upload' as stage,"], [133, "WHERE status = 'pending_upload'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[138, "'uploaded_pending_plan' as stage,"], [141, "WHERE status = 'uploaded_pending_plan'"]], "['\\\"]rejected['\\\"]": [[226, "WHERE lc.status = 'rejected'"]], "['\\\"]harvested['\\\"]": [[308, "harvested_count = material_stats.get('harvested', {}).get('count', 0)"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py": {"['\\\"]processing['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]new['\\\"]": [[27, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [44, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [49, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [63, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [72, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py": {"['\\\"]pending_upload['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"], [26, "alert = \"🚨\" if (status == 'pending_upload' and count > 50) else \"✅\""]], "['\\\"]processing['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]approved['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]rejected['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]new['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[19, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py": {"['\\\"]pending_upload['\\\"]": [[63, "if status == 'pending_upload' and count > 50:"]], "['\\\"]approved['\\\"]": [[153, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'approved'\")"]], "['\\\"]new['\\\"]": [[69, "elif status == 'new' and count > 100:"], [139, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'new'\")"]], "['\\\"]upload_failed['\\\"]": [[66, "elif status == 'upload_failed' and count > 20:"], [146, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'upload_failed'\")"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py": {"['\\\"]pending_upload['\\\"]": [[240, "pending_upload_queue = queue_depths.get('pending_upload', 0)"]], "['\\\"]processing['\\\"]": [[100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"], [239, "processing_queue = queue_depths.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[93, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"]], "['\\\"]testing_pending_review['\\\"]": [[93, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]upload_failed['\\\"]": [[100, "LocalCreative.status.in_(['uploaded_pending_plan', 'upload_failed', 'processing'])"], [241, "upload_failed_queue = queue_depths.get('upload_failed', 0)"], [263, "LocalCreative.status.in_(['upload_failed', 'processing_failed'])"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py": {"['\\\"]pending_upload['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"], [92, "('pending_upload', '📤', '待上传'),"], [132, "pending_upload_count = current_counts.get('pending_upload', 0)"], [171, "if status == 'pending_upload' and change < 0:"], [183, "upload_progress = abs(total_changes.get('pending_upload', 0))"]], "['\\\"]processing['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"], [93, "('processing', '⏳', '处理中'),"], [131, "processing_count = current_counts.get('processing', 0)"], [175, "elif status == 'processing' and change > 0:"], [185, "processing_change = total_changes.get('processing', 0)"]], "['\\\"]uploaded_pending_plan['\\\"]": [[67, "'uploaded_pending_plan', 'testing_pending_review',"], [94, "('uploaded_pending_plan', '📋', '待创建计划'),"], [173, "elif status == 'uploaded_pending_plan' and change > 0:"], [184, "upload_success = total_changes.get('uploaded_pending_plan', 0)"]], "['\\\"]testing_pending_review['\\\"]": [[67, "'uploaded_pending_plan', 'testing_pending_review',"], [95, "('testing_pending_review', '🔍', '测试中'),"]], "['\\\"]approved['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"], [96, "('approved', '✅', '已通过'),"]], "['\\\"]rejected['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"], [97, "('rejected', '❌', '已拒绝')"]], "['\\\"]new['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"]], "['\\\"]pending_grouping['\\\"]": [[66, "'new', 'pending_grouping', 'pending_upload', 'processing',"]], "['\\\"]upload_failed['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"]], "['\\\"]already_tested['\\\"]": [[68, "'approved', 'rejected', 'upload_failed', 'already_tested'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_alert_system.py": {"['\\\"]processing['\\\"]": [[129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"]], "['\\\"]uploaded_pending_plan['\\\"]": [[109, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"], [240, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]testing_pending_review['\\\"]": [[109, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"], [129, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review', 'processing'])"], [240, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py": {"['\\\"]processing['\\\"]": [[113, "LocalCreative.status == 'processing',"], [176, "'processing': db.query(LocalCreative).filter(LocalCreative.status == 'processing').count(),"]], "['\\\"]uploaded_pending_plan['\\\"]": [[177, "'uploaded_pending_plan': db.query(LocalCreative).filter(LocalCreative.status == 'uploaded_pending_plan').count(),"], [204, "if workflow_stats['uploaded_pending_plan'] > 50:"], [205, "issues.append(f\"有{workflow_stats['uploaded_pending_plan']}个素材等待创建计划\")"], [214, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]testing_pending_review['\\\"]": [[178, "'testing_pending_review': db.query(LocalCreative).filter(LocalCreative.status == 'testing_pending_review').count(),"], [208, "if workflow_stats['testing_pending_review'] > 100:"], [209, "issues.append(f\"有{workflow_stats['testing_pending_review']}个素材等待审核\")"], [214, "LocalCreative.status.in_(['uploaded_pending_plan', 'testing_pending_review']),"]], "['\\\"]approved['\\\"]": [[179, "'approved': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),"]], "['\\\"]harvested['\\\"]": [[180, "'harvested': db.query(LocalCreative).filter(LocalCreative.status == 'harvested').count()"]], "['\\\"]new['\\\"]": [[174, "'new': db.query(LocalCreative).filter(LocalCreative.status == 'new').count(),"]], "['\\\"]pending_grouping['\\\"]": [[175, "'pending_grouping': db.query(LocalCreative).filter(LocalCreative.status == 'pending_grouping').count(),"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py": {"['\\\"]processing['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]], "['\\\"]new['\\\"]": [[176, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [193, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [198, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [212, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [221, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\recovery\\ai_tool_20250726_recovery_manual_harvest.py": {"['\\\"]harvested['\\\"]": [[77, "material.harvest_status = 'harvested'"]], "['\\\"]already_tested['\\\"]": [[44, "LocalCreative.status == 'already_tested',"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[287, "SET status = 'pending_upload', updated_at = NOW()"]], "['\\\"]uploaded_pending_plan['\\\"]": [[245, "SET status = 'uploaded_pending_plan', updated_at = NOW()"]], "['\\\"]testing_pending_review['\\\"]": [[231, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"], [323, "WHERE lc.status = 'testing_pending_review' AND cpc.campaign_id IS NULL"]], "['\\\"]upload_failed['\\\"]": [[271, "WHERE status = 'upload_failed'"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py": {"['\\\"]processing['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [198, "elif status == 'processing':"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]uploaded_pending_plan['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [192, "if status == 'uploaded_pending_plan':"], [275, "if initial_progress.get('uploaded_pending_plan', 0) > 50:"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]testing_pending_review['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"]], "['\\\"]approved['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"], [195, "elif status == 'approved':"], [279, "if initial_progress.get('approved', 0) > 50:"], [299, "for status in ['uploaded_pending_plan', 'approved', 'processing']:"]], "['\\\"]new['\\\"]": [[179, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved', 'testing_pending_review')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py": {"['\\\"]pending_upload['\\\"]": [[128, "LocalCreative.status == 'pending_upload'"], [217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"], [224, "alert = \"🚨\" if (status == 'pending_upload' and count > 50) else \"✅\""]], "['\\\"]processing['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]approved['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]rejected['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]new['\\\"]": [[136, "material.status = 'new'"], [140, "logger.success(f\"✅ 已重置 {len(orphaned_materials)} 个孤岛素材状态为 'new'\")"], [217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]], "['\\\"]upload_failed['\\\"]": [[217, "for status in ['new', 'pending_upload', 'processing', 'approved', 'rejected', 'upload_failed']:"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py": {"['\\\"]pending_upload['\\\"]": [[204, "creative.status = 'pending_upload'"], [231, "pending_upload_creatives = [c for c in missing_creatives if c['status'] == 'pending_upload']"]], "['\\\"]uploaded_pending_plan['\\\"]": [[202, "if status == 'uploaded_pending_plan' and '01_materials_to_process' in file_path:"], [232, "uploaded_pending_plan_creatives = [c for c in missing_creatives if c['status'] == 'uploaded_pending_plan']"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py": {"['\\\"]pending_upload['\\\"]": [[49, "LocalCreative.status.in_(['uploaded_pending_plan', 'pending_upload'])"], [93, "if current_status != 'pending_upload':"], [94, "creative.status = 'pending_upload'"], [182, "pending_upload_videos = [v for v in target_videos if v['status'] == 'pending_upload']"]], "['\\\"]uploaded_pending_plan['\\\"]": [[49, "LocalCreative.status.in_(['uploaded_pending_plan', 'pending_upload'])"], [181, "uploaded_pending_videos = [v for v in target_videos if v['status'] == 'uploaded_pending_plan']"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py": {"['\\\"]processing['\\\"]": [[143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [172, "processing_activity = final_stats.get('processing', 0) > 0"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]uploaded_pending_plan['\\\"]": [[143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]approved['\\\"]": [[159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]], "['\\\"]new['\\\"]": [[107, "cursor.execute(\"SELECT COUNT(*) FROM local_creatives WHERE status = 'new'\")"], [143, "for status in ['new', 'processing', 'uploaded_pending_plan']:"], [159, "for status in ['new', 'processing', 'uploaded_pending_plan', 'approved']:"], [171, "new_decreased = final_stats.get('new', 0) < initial_stats.get('new', 0)"], [196, "WHERE status IN ('new', 'processing', 'uploaded_pending_plan', 'approved')"]]}, "backups\\weekly_backup_20250810_124054\\ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py": {"['\\\"]pending_upload['\\\"]": [[276, "LocalCreative.status == 'pending_upload'"]], "['\\\"]processing['\\\"]": [[271, "LocalCreative.status == 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[280, "LocalCreative.status == 'uploaded_pending_plan'"]]}, "src\\qianchuan_aw\\database\\models.py": {"MaterialStatus\\.": [[70, "status: Mapped[str] = mapped_column(String, default=MaterialStatus.PENDING_GROUPING.value, index=True, comment=\"素材状态，使用统一枚举定义\")"]]}, "src\\qianchuan_aw\\services\\event_detector.py": {"['\\\"]rejected['\\\"]": [[202, "# 避免重复告警：如果 PlatformCreative 的状态已经是 'rejected'，则不再告警"], [207, "# 更新 PlatformCreative 的状态为 'rejected'"]]}, "src\\qianchuan_aw\\services\\harvest_idempotency_manager.py": {"['\\\"]approved['\\\"]": [[140, "if material_status.get('status') == 'approved' and material_status.get('harvest_status') == 'harvested':"]], "['\\\"]harvested['\\\"]": [[140, "if material_status.get('status') == 'approved' and material_status.get('harvest_status') == 'harvested':"]]}, "src\\qianchuan_aw\\services\\monitoring_service.py": {"['\\\"]pending_upload['\\\"]": [[332, "WHERE status IN ('pending_upload', 'uploading')"]], "['\\\"]uploading['\\\"]": [[332, "WHERE status IN ('pending_upload', 'uploading')"]], "['\\\"]approved['\\\"]": [[310, "WHERE status = 'approved' AND harvest_status = 'not_harvested'"]]}, "src\\qianchuan_aw\\services\\realtime_harvest_monitor.py": {"['\\\"]approved['\\\"]": [[326, "if local_status != 'approved' and harvest_status == 'not_harvested':"], [552, "if data.get('status') == 'approved' and data.get('harvest_status') == 'harvested':"], [588, "SET status = 'approved',"]], "['\\\"]harvested['\\\"]": [[552, "if data.get('status') == 'approved' and data.get('harvest_status') == 'harvested':"], [616, "SET harvest_status = 'harvested',"]]}, "src\\qianchuan_aw\\services\\unified_appeal_service.py": {"['\\\"]rejected['\\\"]": [[108, "appeal_needed_statuses = ['rejected', 'under_review', 'failed_review']"]]}, "src\\qianchuan_aw\\ui\\pages\\failed_materials_manager.py": {"['\\\"]pending_grouping['\\\"]": [[281, "creative.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[65, "LocalCreative.status == 'upload_failed',"], [279, "if creative and creative.status == 'upload_failed':"]]}, "src\\qianchuan_aw\\ui\\pages\\material_search.py": {"['\\\"]pending_upload['\\\"]": [[28, "'pending_upload': '待上传',"], [346, "'pending_upload': '📤 待上传',"]], "['\\\"]uploading['\\\"]": [[347, "'uploading': '⬆️ 上传中',"]], "['\\\"]processing['\\\"]": [[342, "'processing': '🔄 处理中',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[30, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[31, "'testing_pending_review': '测试中待审核',"]], "['\\\"]approved['\\\"]": [[32, "'approved': '审核通过',"]], "['\\\"]rejected['\\\"]": [[33, "'rejected': '审核拒绝',"]], "['\\\"]harvested['\\\"]": [[34, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[26, "'new': '新素材',"]], "['\\\"]pending_grouping['\\\"]": [[27, "'pending_grouping': '待分组',"], [341, "'pending_grouping': '⏳ 待分组',"]], "['\\\"]upload_failed['\\\"]": [[29, "'upload_failed': '上传失败',"]], "['\\\"]already_tested['\\\"]": [[35, "'already_tested': '已测试过',"]]}, "src\\qianchuan_aw\\utils\\appeal_state_manager.py": {"['\\\"]rejected['\\\"]": [[102, "appealable_statuses = ['rejected', 'under_review', 'failed_review']"]]}, "src\\qianchuan_aw\\utils\\atomic_state_manager.py": {"MaterialStatus\\.": [[104, "MaterialStatus.UPLOADING.value,"], [134, "MaterialStatus.UPLOADING.value,"], [135, "MaterialStatus.PENDING_UPLOAD.value"]]}, "src\\qianchuan_aw\\utils\\constants.py": {"['\\\"]processing['\\\"]": [[63, "PROCESSING = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[64, "UPLOADED_PENDING_PLAN = 'uploaded_pending_plan'"]], "['\\\"]testing_pending_review['\\\"]": [[65, "TESTING_PENDING_REVIEW = 'testing_pending_review'"]], "['\\\"]approved['\\\"]": [[66, "APPROVED = 'approved'"]], "['\\\"]rejected['\\\"]": [[67, "REJECTED = 'rejected'"]], "['\\\"]new['\\\"]": [[61, "NEW = 'new'"]], "['\\\"]pending_grouping['\\\"]": [[62, "PENDING_GROUPING = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[68, "UPLOAD_FAILED = 'upload_failed'"]], "['\\\"]already_tested['\\\"]": [[71, "ALREADY_TESTED = 'already_tested'  # 已经创建过测试计划，跳过重复测试"]]}, "src\\qianchuan_aw\\utils\\material_state_validator.py": {"MaterialStatus\\.": [[79, "MaterialStatus.PROCESSING.value: 60,  # 处理状态最多60分钟"], [80, "MaterialStatus.TESTING_PENDING_REVIEW.value: 1440,  # 测试状态最多24小时"], [85, "(MaterialStatus.QUALITY_FAILED.value, MaterialStatus.PENDING_GROUPING.value),"], [86, "(MaterialStatus.REJECTED.value, MaterialStatus.HARVESTED.value),"], [94, "MaterialStatus.APPROVED.value: ['account_balance', 'material_quality'],"], [95, "MaterialStatus.HARVESTED.value: ['harvest_conditions'],"], [227, "MaterialStatus.PENDING_UPLOAD.value,"], [228, "MaterialStatus.UPLOADING.value,"], [229, "MaterialStatus.UPLOADED.value"], [311, "MaterialStatus.PENDING_UPLOAD.value,"], [312, "MaterialStatus.UPLOADING.value,"], [313, "MaterialStatus.UPLOADED.value"], [320, "if current_state == MaterialStatus.PROCESSING.value:"], [322, "if next_state == MaterialStatus.UPLOADED_PENDING_PLAN.value:"]]}, "src\\qianchuan_aw\\utils\\monitoring_mcp.py": {"['\\\"]uploading['\\\"]": [[266, "WHERE status IN ('uploading', 'plan_pending', 'creating_plan')"]], "['\\\"]approved['\\\"]": [[152, "COUNT(CASE WHEN harvest_status = 'not_harvested' AND status = 'approved' THEN 1 END) as pending_harvest,"], [156, "WHERE status = 'approved'"]], "['\\\"]harvested['\\\"]": [[151, "COUNT(CASE WHEN harvest_status = 'harvested' THEN 1 END) as harvested_count,"], [153, "AVG(CASE WHEN harvest_status = 'harvested' AND last_harvest_attempt IS NOT NULL"]]}, "src\\qianchuan_aw\\utils\\safe_deletion_checker.py": {"['\\\"]rejected['\\\"]": [[91, "result['video_status'] = 'rejected'"]], "['\\\"]quality_failed['\\\"]": [[185, "creative.status = 'quality_failed'"]]}, "src\\qianchuan_aw\\utils\\state_managers.py": {"['\\\"]pending_upload['\\\"]": [[45, "PENDING_UPLOAD = \"pending_upload\"           # 等待上传"]], "['\\\"]uploading['\\\"]": [[46, "UPLOADING = \"uploading\"                     # 上传中"]], "['\\\"]approved['\\\"]": [[52, "APPROVED = \"approved\"                       # 审核通过"]], "['\\\"]rejected['\\\"]": [[53, "REJECTED = \"rejected\"                       # 审核拒绝"]], "['\\\"]harvested['\\\"]": [[54, "HARVESTED = \"harvested\"                     # 已收割"], [62, "HARVESTED = \"harvested\"                     # 已收割"]], "['\\\"]new['\\\"]": [[44, "NEW = \"new\"                                 # 新文件入库"]], "['\\\"]upload_failed['\\\"]": [[48, "UPLOAD_FAILED = \"upload_failed\"             # 上传失败"]], "['\\\"]already_tested['\\\"]": [[55, "ALREADY_TESTED = \"already_tested\"           # 已测试过"]], "MaterialStatus\\.": [[79, "MaterialStatus.UPLOADING: 10,           # 上传超时10分钟"], [80, "MaterialStatus.PLAN_PENDING: 30,        # 等待计划创建超时30分钟"], [81, "MaterialStatus.UNDER_REVIEW: 360,       # 审核超时6小时"], [87, "MaterialStatus.UPLOAD_FAILED: {'max_retries': 3, 'backoff_minutes': 5},"], [88, "MaterialStatus.PLAN_PENDING: {'max_retries': 2, 'backoff_minutes': 10},"], [201, "if material.status == MaterialStatus.APPROVED.value:"], [202, "material.status = MaterialStatus.HARVESTED.value"], [271, "if current_status == MaterialStatus.UPLOADING:"], [272, "material.status = MaterialStatus.UPLOAD_FAILED.value"], [273, "elif current_status == MaterialStatus.PLAN_PENDING:"], [274, "material.status = MaterialStatus.UPLOADED.value  # 重新等待计划创建"], [280, "if current_status == MaterialStatus.UPLOADING:"], [281, "material.status = MaterialStatus.UPLOAD_FAILED.value"], [282, "elif current_status == MaterialStatus.PLAN_PENDING:"], [283, "material.status = MaterialStatus.UPLOAD_FAILED.value  # 回退到失败状态"], [284, "elif current_status == MaterialStatus.UNDER_REVIEW:"], [285, "material.status = MaterialStatus.REJECTED.value  # 审核超时视为拒绝"], [747, "MaterialStatus.NEW: [MaterialStatus.PENDING_UPLOAD],"], [748, "MaterialStatus.PENDING_UPLOAD: [MaterialStatus.UPLOADING],"], [749, "MaterialStatus.UPLOADING: [MaterialStatus.UPLOADED, MaterialStatus.UPLOAD_FAILED],"], [750, "MaterialStatus.UPLOADED: [MaterialStatus.PLAN_PENDING],"], [751, "MaterialStatus.UPLOAD_FAILED: [MaterialStatus.PENDING_UPLOAD],  # 重试"], [752, "MaterialStatus.PLAN_PENDING: [MaterialStatus.PLAN_CREATED, MaterialStatus.UPLOAD_FAILED],"], [753, "MaterialStatus.PLAN_CREATED: [MaterialStatus.UNDER_REVIEW],"], [754, "MaterialStatus.UNDER_REVIEW: [MaterialStatus.APPROVED, MaterialStatus.REJECTED],"], [755, "MaterialStatus.APPROVED: [MaterialStatus.HARVESTED],"], [756, "MaterialStatus.REJECTED: [],  # 终态"], [757, "MaterialStatus.HARVESTED: [],  # 终态"], [758, "MaterialStatus.ALREADY_TESTED: [],  # 终态"]]}, "src\\qianchuan_aw\\utils\\unified_material_status.py": {"['\\\"]pending_upload['\\\"]": [[57, "PENDING_UPLOAD = \"pending_upload\"             # 废弃：使用 PENDING_GROUPING 替代"]], "['\\\"]uploading['\\\"]": [[58, "UPLOADING = \"uploading\"                       # 废弃：使用 PROCESSING 替代"]], "['\\\"]processing['\\\"]": [[40, "PROCESSING = \"processing\"                      # 正在处理（上传中）"]], "['\\\"]uploaded_pending_plan['\\\"]": [[41, "UPLOADED_PENDING_PLAN = \"uploaded_pending_plan\"  # 已上传，等待创建计划"]], "['\\\"]testing_pending_review['\\\"]": [[42, "TESTING_PENDING_REVIEW = \"testing_pending_review\"  # 测试中，等待审核"]], "['\\\"]approved['\\\"]": [[45, "APPROVED = \"approved\"                          # 审核通过"]], "['\\\"]rejected['\\\"]": [[46, "REJECTED = \"rejected\"                          # 审核拒绝"]], "['\\\"]harvested['\\\"]": [[47, "HARVESTED = \"harvested\"                        # 已收割（最终状态）"]], "['\\\"]new['\\\"]": [[38, "NEW = \"new\"                                    # 新创建的素材"]], "['\\\"]pending_grouping['\\\"]": [[39, "PENDING_GROUPING = \"pending_grouping\"          # 等待分组处理"]], "['\\\"]upload_failed['\\\"]": [[51, "UPLOAD_FAILED = \"upload_failed\"               # 上传失败"]], "['\\\"]quality_failed['\\\"]": [[52, "QUALITY_FAILED = \"quality_failed\"             # 质量检查失败"]], "['\\\"]plan_creation_failed['\\\"]": [[53, "PLAN_CREATION_FAILED = \"plan_creation_failed\" # 计划创建失败"]], "['\\\"]already_tested['\\\"]": [[48, "ALREADY_TESTED = \"already_tested\"              # 已测试过（跳过状态）"]], "MaterialStatus\\.": [[132, "MaterialStatus.NEW.value: ["], [133, "MaterialStatus.PENDING_GROUPING.value"], [136, "MaterialStatus.PENDING_GROUPING.value: ["], [137, "MaterialStatus.PROCESSING.value,"], [138, "MaterialStatus.ALREADY_TESTED.value,"], [140, "MaterialStatus.PENDING_UPLOAD.value"], [143, "MaterialStatus.PROCESSING.value: ["], [144, "MaterialStatus.UPLOADED_PENDING_PLAN.value,"], [145, "MaterialStatus.UPLOAD_FAILED.value,"], [146, "MaterialStatus.QUALITY_FAILED.value"], [149, "MaterialStatus.UPLOADED_PENDING_PLAN.value: ["], [150, "MaterialStatus.TESTING_PENDING_REVIEW.value,"], [151, "MaterialStatus.PLAN_CREATION_FAILED.value"], [154, "MaterialStatus.TESTING_PENDING_REVIEW.value: ["], [155, "MaterialStatus.APPROVED.value,"], [156, "MaterialStatus.REJECTED.value"], [159, "MaterialStatus.APPROVED.value: ["], [160, "MaterialStatus.HARVESTED.value"], [164, "MaterialStatus.REJECTED.value: ["], [165, "MaterialStatus.PENDING_GROUPING.value   # 可以重新处理"], [168, "MaterialStatus.UPLOAD_FAILED.value: ["], [169, "MaterialStatus.PENDING_GROUPING.value   # 可以重试"], [172, "MaterialStatus.PLAN_CREATION_FAILED.value: ["], [173, "MaterialStatus.UPLOADED_PENDING_PLAN.value  # 可以重试"], [177, "MaterialStatus.PENDING_UPLOAD.value: ["], [178, "MaterialStatus.UPLOADING.value,"], [179, "MaterialStatus.PROCESSING.value"], [182, "MaterialStatus.UPLOADING.value: ["], [183, "MaterialStatus.UPLOADED.value,"], [184, "MaterialStatus.PROCESSING.value,"], [185, "MaterialStatus.UPLOAD_FAILED.value"], [188, "MaterialStatus.UPLOADED.value: ["], [189, "MaterialStatus.UPLOADED_PENDING_PLAN.value"], [193, "MaterialStatus.HARVESTED.value: [],"], [194, "MaterialStatus.ALREADY_TESTED.value: [],"], [195, "MaterialStatus.QUALITY_FAILED.value: []"], [217, "MaterialStatus.NEW.value,"], [218, "MaterialStatus.PENDING_GROUPING.value,"], [222, "MaterialStatus.PENDING_GROUPING.value,"], [223, "MaterialStatus.PROCESSING.value,"], [227, "MaterialStatus.PROCESSING.value,"], [228, "MaterialStatus.UPLOADED_PENDING_PLAN.value,"], [232, "MaterialStatus.UPLOADED_PENDING_PLAN.value,"], [233, "MaterialStatus.TESTING_PENDING_REVIEW.value,"], [237, "MaterialStatus.TESTING_PENDING_REVIEW.value,"], [238, "MaterialStatus.APPROVED.value,"], [242, "MaterialStatus.APPROVED.value,"], [243, "MaterialStatus.HARVESTED.value,"], [251, "MaterialStatus.PROCESSING.value,"], [252, "MaterialStatus.UPLOAD_FAILED.value,"], [257, "MaterialStatus.PROCESSING.value,"], [258, "MaterialStatus.QUALITY_FAILED.value,"], [263, "MaterialStatus.UPLOADED_PENDING_PLAN.value,"], [264, "MaterialStatus.PLAN_CREATION_FAILED.value,"], [295, "MaterialStatus.NEW.value,"], [296, "MaterialStatus.PENDING_GROUPING.value,"], [297, "MaterialStatus.PROCESSING.value,"], [298, "MaterialStatus.UPLOADED_PENDING_PLAN.value,"], [299, "MaterialStatus.TESTING_PENDING_REVIEW.value"], [304, "MaterialStatus.UPLOAD_FAILED.value,"], [305, "MaterialStatus.PLAN_CREATION_FAILED.value,"], [306, "MaterialStatus.REJECTED.value"], [311, "MaterialStatus.APPROVED.value,"], [312, "MaterialStatus.HARVESTED.value"], [317, "MaterialStatus.UPLOAD_FAILED.value,"], [318, "MaterialStatus.PLAN_CREATION_FAILED.value"], [323, "MaterialStatus.QUALITY_FAILED.value"], [348, "return MaterialStatus.is_valid_status(status)"]]}, "src\\qianchuan_aw\\utils\\workflow_helpers.py": {"['\\\"]pending_upload['\\\"]": [[217, "lc.status = 'pending_upload'"]], "['\\\"]new['\\\"]": [[148, "status='new'"]]}, "src\\qianchuan_aw\\utils\\workflow_quality_control.py": {"['\\\"]harvested['\\\"]": [[254, "LocalCreative.status == 'harvested'"], [447, "WHERE status = 'harvested'"]], "['\\\"]quality_failed['\\\"]": [[298, "creative.status = 'quality_failed'"]], "['\\\"]already_tested['\\\"]": [[199, "'already_tested': bool,"], [220, "'already_tested': True,"], [233, "'already_tested': <PERSON><PERSON><PERSON>,"]]}, "src\\qianchuan_aw\\utils\\workflow_status.py": {"['\\\"]pending_upload['\\\"]": [[302, "'pending_upload': WorkflowStatus.PENDING_GROUPING.value,"]], "['\\\"]uploading['\\\"]": [[303, "'uploading': WorkflowStatus.PROCESSING.value,"]], "['\\\"]processing['\\\"]": [[19, "PROCESSING = \"processing\"                   # 上传中"], [320, "'processing': '上传中',"]], "['\\\"]uploaded_pending_plan['\\\"]": [[20, "UPLOADED_PENDING_PLAN = \"uploaded_pending_plan\"  # 已上传待建计划"], [321, "'uploaded_pending_plan': '已上传待建计划',"]], "['\\\"]testing_pending_review['\\\"]": [[21, "TESTING_PENDING_REVIEW = \"testing_pending_review\"  # 测试待审核"], [322, "'testing_pending_review': '测试待审核',"]], "['\\\"]approved['\\\"]": [[24, "APPROVED = \"approved\"                       # 审核通过（可收割）"], [325, "'approved': '审核通过（可收割）',"]], "['\\\"]harvested['\\\"]": [[25, "HARVESTED = \"harvested\"                     # 已收割（最终状态）"], [326, "'harvested': '已收割',"]], "['\\\"]new['\\\"]": [[17, "NEW = \"new\"                                 # 新文件入库"], [318, "'new': '新入库',"]], "['\\\"]pending_grouping['\\\"]": [[18, "PENDING_GROUPING = \"pending_grouping\"       # 待分组（快速中转状态）"], [319, "'pending_grouping': '待分组（快速中转）',"]], "['\\\"]upload_failed['\\\"]": [[31, "UPLOAD_FAILED = \"upload_failed\"             # 技术性上传失败（可重试）"], [332, "'upload_failed': '技术性上传失败',"]], "['\\\"]plan_creation_failed['\\\"]": [[32, "PLAN_CREATION_FAILED = \"plan_creation_failed\"  # 计划创建失败（可重试）"], [333, "'plan_creation_failed': '计划创建失败',"]], "['\\\"]already_tested['\\\"]": [[28, "ALREADY_TESTED = \"already_tested\"           # 已测试过（基于file_hash全主体唯一性检查）"], [329, "'already_tested': '已测试过（基于file_hash）',"]], "WorkflowStatus\\.": [[61, "WorkflowStatus.NEW: ["], [62, "WorkflowStatus.PENDING_GROUPING"], [64, "WorkflowStatus.PENDING_GROUPING: ["], [65, "WorkflowStatus.PROCESSING,          # 正常分组后进入上传"], [66, "WorkflowStatus.ALREADY_TESTED       # 基于file_hash检查发现已测试过"], [68, "WorkflowStatus.PROCESSING: ["], [69, "WorkflowStatus.UPLOADED_PENDING_PLAN,  # 上传成功"], [70, "WorkflowStatus.UPLOAD_FAILED            # 技术性失败（可重试）"], [73, "WorkflowStatus.UPLOADED_PENDING_PLAN: ["], [74, "WorkflowStatus.TESTING_PENDING_REVIEW,  # 计划创建成功（同步操作）"], [75, "WorkflowStatus.PLAN_CREATION_FAILED     # 计划创建失败"], [77, "WorkflowStatus.TESTING_PENDING_REVIEW: ["], [78, "WorkflowStatus.APPROVED                 # API返回PASS状态"], [83, "WorkflowStatus.APPROVED: ["], [84, "WorkflowStatus.HARVESTED               # 收割成功（严格幂等性检查）"], [88, "WorkflowStatus.HARVESTED: [],              # 最终状态，不再转换"], [89, "WorkflowStatus.ALREADY_TESTED: [],         # 终态，不再处理"], [92, "WorkflowStatus.UPLOAD_FAILED: ["], [93, "WorkflowStatus.PROCESSING              # 智能重试（指数退避）"], [95, "WorkflowStatus.PLAN_CREATION_FAILED: ["], [96, "WorkflowStatus.UPLOADED_PENDING_PLAN   # 重试计划创建"], [103, "WorkflowStatus.NEW: WorkflowStage.FILE_INGESTION,"], [104, "WorkflowStatus.PENDING_GROUPING: WorkflowStage.GROUP_DISPATCH,"], [105, "WorkflowStatus.PROCESSING: WorkflowStage.UPLOAD_ARCHIVE,"], [106, "WorkflowStatus.UPLOADED_PENDING_PLAN: WorkflowStage.PLAN_CREATION,"], [107, "WorkflowStatus.TESTING_PENDING_REVIEW: WorkflowStage.REVIEW_MONITORING,"], [110, "WorkflowStatus.APPROVED: WorkflowStage.HARVEST,"], [111, "WorkflowStatus.HARVESTED: WorkflowStage.COMPLETION,"], [114, "WorkflowStatus.ALREADY_TESTED: WorkflowStage.COMPLETION,"], [117, "WorkflowStatus.UPLOAD_FAILED: WorkflowStage.UPLOAD_ARCHIVE,"], [118, "WorkflowStatus.PLAN_CREATION_FAILED: WorkflowStage.PLAN_CREATION,"], [123, "WorkflowStatus.PENDING_GROUPING: 5,               # 分组应该是快速操作，5分钟超时"], [124, "WorkflowStatus.PROCESSING: 15,                    # 上传超时15分钟（考虑大文件）"], [125, "WorkflowStatus.UPLOADED_PENDING_PLAN: 10,         # 计划创建是同步操作，10分钟超时"], [126, "WorkflowStatus.TESTING_PENDING_REVIEW: 1440,      # 审核可能需要24小时"], [131, "WorkflowStatus.UPLOAD_FAILED: {"], [136, "WorkflowStatus.PLAN_CREATION_FAILED: {"], [173, "WorkflowStatus.UPLOAD_FAILED,"], [174, "WorkflowStatus.PLAN_CREATION_FAILED,"], [182, "WorkflowStatus.NEW,"], [183, "WorkflowStatus.PENDING_GROUPING,"], [184, "WorkflowStatus.PROCESSING,"], [185, "WorkflowStatus.UPLOADED_PENDING_PLAN,"], [186, "WorkflowStatus.TESTING_PENDING_REVIEW,"], [187, "WorkflowStatus.UPLOAD_FAILED,"], [188, "WorkflowStatus.PLAN_CREATION_FAILED,"], [196, "WorkflowStatus.APPROVED,"], [197, "WorkflowStatus.HARVESTED,"], [302, "'pending_upload': WorkflowStatus.PENDING_GROUPING.value,"], [303, "'uploading': WorkflowStatus.PROCESSING.value,"], [304, "'uploaded': WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [305, "'plan_pending': WorkflowStatus.UPLOADED_PENDING_PLAN.value,"], [306, "'plan_created': WorkflowStatus.TESTING_PENDING_REVIEW.value,"], [307, "'under_review': WorkflowStatus.TESTING_PENDING_REVIEW.value,"]]}, "src\\qianchuan_aw\\workflows\\appeal_and_monitor.py": {"['\\\"]approved['\\\"]": [[32, "\"AD_AUDIT_STATUS_DONE\": \"approved\","], [260, "# 如果素材状态不是 'approved' 或 'rejected'，则认为它仍在审核中"]], "['\\\"]rejected['\\\"]": [[33, "\"AD_AUDIT_STATUS_REJECT\": \"rejected\","], [260, "# 如果素材状态不是 'approved' 或 'rejected'，则认为它仍在审核中"]]}, "src\\qianchuan_aw\\workflows\\flexible_grouping.py": {"MaterialStatus\\.": [[74, "LocalCreative.status.in_([MaterialStatus.PENDING_GROUPING.value, MaterialStatus.UPLOADED_PENDING_PLAN.value]),"]]}, "src\\qianchuan_aw\\workflows\\independent_material_harvest.py": {"['\\\"]approved['\\\"]": [[152, "if pc.local_creative and pc.local_creative.status != 'approved':"], [197, "local_creative.status = 'approved'"], [341, "'approved_materials': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),"]], "['\\\"]rejected['\\\"]": [[161, "if pc.local_creative and pc.local_creative.status != 'rejected':"], [163, "pc.local_creative.status = 'rejected'"], [342, "'rejected_materials': db.query(LocalCreative).filter(LocalCreative.status == 'rejected').count(),"]], "['\\\"]harvested['\\\"]": [[192, "if local_creative.harvest_status == 'harvested':"], [198, "local_creative.harvest_status = 'harvested'"]]}, "src\\qianchuan_aw\\workflows\\realtime_harvest_workflow.py": {"['\\\"]processing['\\\"]": [[236, "'processing': metrics.get('processing_count', 0)"]]}, "src\\qianchuan_aw\\workflows\\scheduler.py": {"MaterialStatus\\.": [[788, "LocalCreative.status == MaterialStatus.UPLOAD_FAILED.value"], [835, "creative.status = MaterialStatus.PENDING_GROUPING.value"], [850, "creative.status = MaterialStatus.PENDING_GROUPING.value"], [866, "creative.status = MaterialStatus.PENDING_GROUPING.value"], [895, "[Producer V2] 扫描文件，计算哈希，并将素材信息存入数据库，状态为 MaterialStatus.PENDING_GROUPING.value。"], [946, "creative.status = MaterialStatus.PENDING_GROUPING.value"], [950, "elif creative.status in [MaterialStatus.NEW.value, MaterialStatus.UPLOAD_FAILED.value, 'check_failed', 'reset_by_system', MaterialStatus.REJECTED.value, MaterialStatus.PENDING_GROUPING.value]:"], [951, "creative.status = MaterialStatus.PENDING_GROUPING.value"], [1022, "LocalCreative.status == MaterialStatus.PENDING_GROUPING.value"], [1026, "creative.status = MaterialStatus.PENDING_UPLOAD.value"], [1036, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [1718, "if pc.local_creative.status != MaterialStatus.APPROVED.value:"], [1721, "pc.local_creative.status = MaterialStatus.APPROVED.value"], [1723, "pc.local_creative.harvest_status = MaterialStatus.HARVESTED.value"], [1730, "if pc.local_creative.status != MaterialStatus.REJECTED.value:"], [1732, "pc.local_creative.status = MaterialStatus.REJECTED.value"], [1795, "if pc.local_creative and pc.local_creative.status == MaterialStatus.REJECTED.value:"]], "WorkflowStatus\\.": [[174, "if local_creative_locked.status != WorkflowStatus.APPROVED.value:"], [191, "local_creative_locked.status = WorkflowStatus.HARVESTED.value"], [196, "logger.info(f\"   状态: {WorkflowStatus.HARVESTED.value}\")"], [200, "local_creative_locked.status = WorkflowStatus.HARVESTED.value"], [231, "\"status\": WorkflowStatus.HARVESTED.value"], [337, "local_creative.status = WorkflowStatus.UPLOADED_PENDING_PLAN.value"], [682, "local_creative.status = WorkflowStatus.UPLOADED_PENDING_PLAN.value"], [694, "logger.info(f\"   状态: {WorkflowStatus.UPLOADED_PENDING_PLAN.value}\")"], [760, "\"status\": WorkflowStatus.UPLOAD_FAILED.value"], [767, "\"status\": WorkflowStatus.UPLOAD_FAILED.value"], [1129, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value"], [1207, "if locked_creative.status != WorkflowStatus.UPLOADED_PENDING_PLAN.value:"], [1237, "locked_creative.status = WorkflowStatus.ALREADY_TESTED.value"], [1272, "locked_creative.status = WorkflowStatus.ALREADY_TESTED.value"], [1285, "locked_creative.status = WorkflowStatus.ALREADY_TESTED.value"]]}, "src\\qianchuan_aw\\workflows\\scheduler_backup_20250722_155220.py": {"['\\\"]processing['\\\"]": [[651, "# 更新状态为 'processing'，实现锁定"], [652, "creative.status = 'processing'"]], "['\\\"]uploaded_pending_plan['\\\"]": [[375, "local_creative.status = 'uploaded_pending_plan'"], [688, "creative.status = 'uploaded_pending_plan'"], [694, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [720, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [726, "logger.info(f\"--- [追踪点A] 找到 {len(candidate_creatives)} 个状态为 'uploaded_pending_plan' 的素材候选。\")"], [747, "if locked_creative.status not in ['uploaded_pending_plan', 'approved']:"]], "['\\\"]approved['\\\"]": [[122, "# Lock the record and attempt to change the state from a non-approved to 'approved'"], [133, "if local_creative_locked.status == 'approved':"], [134, "logger.info(f\"素材 LocalCreative ID: {local_creative_locked.id} 的数据库状态已是 'approved'，跳过所有归档操作。\")"], [137, "# The state is not 'approved', so we claim it now."], [138, "local_creative_locked.status = 'approved'"], [141, "logger.success(f\"✅ 状态锁定成功！本地素材 LocalCreative ID: {local_creative_locked.id} 的状态已在数据库中更新为 'approved'。\")"], [145, "# The state is guaranteed to be 'approved'. Now we can safely perform"], [694, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [720, "LocalCreative.status.in_(['uploaded_pending_plan', 'creating_plan', 'approved'])"], [747, "if locked_creative.status not in ['uploaded_pending_plan', 'approved']:"], [1004, "if pc.local_creative.status != 'approved':"]], "['\\\"]rejected['\\\"]": [[579, "elif creative.status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [1007, "if pc.local_creative.status != 'rejected':"], [1009, "pc.local_creative.status = 'rejected'"], [1024, "if pc.local_creative and pc.local_creative.status == 'rejected':"]], "['\\\"]new['\\\"]": [[579, "elif creative.status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"]], "['\\\"]pending_grouping['\\\"]": [[464, "creative.status = 'pending_grouping'"], [479, "creative.status = 'pending_grouping'"], [495, "creative.status = 'pending_grouping'"], [524, "[Producer V2] 扫描文件，计算哈希，并将素材信息存入数据库，状态为 'pending_grouping'。"], [575, "creative.status = 'pending_grouping'"], [579, "elif creative.status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [580, "creative.status = 'pending_grouping'"], [618, "LocalCreative.status == 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[400, "db.query(LocalCreative).filter_by(id=local_creative_id).update({\"status\": \"upload_failed\"})"], [417, "LocalCreative.status == 'upload_failed'"], [579, "elif creative.status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"]], "['\\\"]already_tested['\\\"]": [[776, "locked_creative.status = 'already_tested'"]]}, "src\\qianchuan_aw\\workflows\\tasks.py": {"MaterialStatus\\.": [[131, "if creative.status not in [MaterialStatus.PENDING_UPLOAD.value, MaterialStatus.PROCESSING.value, MaterialStatus.UPLOADING.value]:"], [138, "target_status = MaterialStatus.PROCESSING.value"], [139, "if creative.status == MaterialStatus.UPLOADING.value:"], [141, "with manager.atomic_state_transition(local_creative_id, MaterialStatus.UPLOADING.value, MaterialStatus.PROCESSING.value) as processing_creative:"], [150, "processing_creative.status = MaterialStatus.UPLOADED_PENDING_PLAN.value"], [161, "with manager.atomic_state_transition(local_creative_id, creative.status, MaterialStatus.PROCESSING.value) as processing_creative:"], [170, "processing_creative.status = MaterialStatus.UPLOADED_PENDING_PLAN.value"], [200, "with manager.atomic_state_transition(local_creative_id, MaterialStatus.PROCESSING.value, MaterialStatus.UPLOAD_FAILED.value) as failed_creative:"], [221, "LocalCreative.status == MaterialStatus.PENDING_GROUPING.value"], [227, "creative.status = MaterialStatus.PENDING_UPLOAD.value"], [234, "LocalCreative.status == MaterialStatus.PENDING_UPLOAD.value"], [359, "LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value"], [377, "LocalCreative.status == MaterialStatus.UPLOADED_PENDING_PLAN.value"], [582, "[Watchdog Task] 定期巡检并重置卡死在 MaterialStatus.PROCESSING.value 状态的僵尸任务。"], [596, "LocalCreative.status == MaterialStatus.PROCESSING.value,"], [608, "creative.status = MaterialStatus.PENDING_GROUPING.value # 重置为待分组状态，让它重新进入处理队列"]]}, "src\\qianchuan_aw\\workflows\\common\\plan_creation.py": {"WorkflowStatus\\.": [[394, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value"], [396, ".update({\"status\": WorkflowStatus.TESTING_PENDING_REVIEW.value}, synchronize_session=False)"], [778, "LocalCreative.status == WorkflowStatus.UPLOADED_PENDING_PLAN.value"], [780, ".update({\"status\": WorkflowStatus.TESTING_PENDING_REVIEW.value}, synchronize_session=False)"]]}, "temp_tools\\emergency_fix.py": {"['\\\"]uploading['\\\"]": [[60, "'processing', 'uploading', 'creating_plan',"]], "['\\\"]processing['\\\"]": [[60, "'processing', 'uploading', 'creating_plan',"]], "['\\\"]pending_grouping['\\\"]": [[70, "creative.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[61, "'upload_failed'  # 也重置失败状态"]]}, "temp_tools\\fix_failed_uploads_sql.py": {"['\\\"]pending_grouping['\\\"]": [[29, "print(\"UPDATE local_creatives SET status = 'pending_grouping', video_id = NULL, material_id_qc = NULL, uploaded_to_account_id = NULL WHERE status = 'upload_failed';\")"]], "['\\\"]upload_failed['\\\"]": [[26, "print(\"SELECT id, file_path, status, created_at, updated_at FROM local_creatives WHERE status = 'upload_failed' LIMIT 10;\")"], [29, "print(\"UPDATE local_creatives SET status = 'pending_grouping', video_id = NULL, material_id_qc = NULL, uploaded_to_account_id = NULL WHERE status = 'upload_failed';\")"]]}, "temp_tools\\move_specific_failed_files.py": {"['\\\"]pending_grouping['\\\"]": [[28, "LocalCreative.status == 'pending_grouping'"]]}, "tests\\test_fault_tolerance.py": {"['\\\"]approved['\\\"]": [[105, "mock_execute_sql.side_effect = ['', '{\"status\":\"approved\",\"harvest_status\":\"pending\"}', '']"]]}, "tests\\test_monitoring_system.py": {"['\\\"]pending_upload['\\\"]": [[211, "'pending_upload': 10,"], [360, "'material_states': {'pending_upload': 10, 'approved': 20},"], [371, "assert data['data']['material_states']['pending_upload'] == 10"]], "['\\\"]uploading['\\\"]": [[212, "'uploading': 5,"]], "['\\\"]approved['\\\"]": [[213, "'approved': 20"], [360, "'material_states': {'pending_upload': 10, 'approved': 20},"], [413, "'material_states': {'approved': 20}"], [423, "assert 'qianchuan_state_count{type=\"material_states\",state=\"approved\"} 20' in content"]]}, "tests\\test_realtime_harvest.py": {"['\\\"]approved['\\\"]": [[110, "mock_execute_sql.return_value = '{\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"], [167, "mock_execute_sql.return_value = '{\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"], [255, "mock_execute_sql.return_value = '{\"platform_creative_id\":1,\"local_creative_id\":1,\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"]], "['\\\"]harvested['\\\"]": [[110, "mock_execute_sql.return_value = '{\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"], [167, "mock_execute_sql.return_value = '{\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"], [255, "mock_execute_sql.return_value = '{\"platform_creative_id\":1,\"local_creative_id\":1,\"status\":\"approved\",\"harvest_status\":\"harvested\"}'"]]}, "tests\\test_state_managers.py": {"MaterialStatus\\.": [[54, "material.status = MaterialStatus.PENDING_UPLOAD.value"], [90, "MaterialStatus.PENDING_UPLOAD,"], [91, "MaterialStatus.UPLOADING"], [95, "assert mock_material.status == MaterialStatus.UPLOADING.value"], [108, "MaterialStatus.PENDING_UPLOAD,"], [109, "MaterialStatus.UPLOADING"], [129, "MaterialStatus.PENDING_UPLOAD,"], [130, "MaterialStatus.UPLOADING"], [146, "mock_material.status = MaterialStatus.UPLOADING.value"], [151, "MaterialStatus.PENDING_UPLOAD,  # 期望状态"], [152, "MaterialStatus.UPLOADING"], [186, "timeout_material.status = MaterialStatus.UPLOADING.value"], [203, "(MaterialStatus.PENDING_UPLOAD.value, 5),"], [204, "(MaterialStatus.UPLOADING.value, 3),"], [205, "(MaterialStatus.APPROVED.value, 10)"], [238, "MaterialStatus.APPROVED,"], [243, "assert mock_material.status == MaterialStatus.APPROVED.value"], [350, "MaterialStatus.PENDING_UPLOAD,"], [351, "MaterialStatus.UPLOADING"], [355, "MaterialStatus.UPLOADING,"], [356, "MaterialStatus.UPLOADED"], [360, "MaterialStatus.APPROVED,"], [361, "MaterialStatus.HARVESTED"], [368, "MaterialStatus.PENDING_UPLOAD,"], [369, "MaterialStatus.APPROVED  # 跳跃式转换"], [373, "MaterialStatus.HARVESTED,"], [374, "MaterialStatus.UPLOADING  # 从终态转换"]]}, "tests\\load\\test_load_testing.py": {"['\\\"]approved['\\\"]": [[75, "return '{\"status\":\"approved\",\"harvest_status\":\"pending\"}'"]]}, "tests\\performance\\test_performance_suite.py": {"['\\\"]approved['\\\"]": [[67, "return '{\"status\":\"approved\",\"harvest_status\":\"pending\"}'"]]}, "tools\\check_material_status.py": {"['\\\"]uploaded_pending_plan['\\\"]": [[213, "logger.info(\"   - 查找状态为 'uploaded_pending_plan' 的素材\")"]], "['\\\"]approved['\\\"]": [[220, "logger.info(\"   - 'approved': 测试计划审核通过，不会重复处理\")"]], "['\\\"]rejected['\\\"]": [[97, "if status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [210, "logger.info(\"     * 'new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping'\")"], [221, "logger.info(\"   - 'rejected': 测试计划审核拒绝，可能重新处理\")"]], "['\\\"]new['\\\"]": [[97, "if status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [210, "logger.info(\"     * 'new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping'\")"]], "['\\\"]pending_grouping['\\\"]": [[97, "if status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [208, "logger.info(\"   - 如果不存在：创建新记录，状态设为 'pending_grouping'\")"], [210, "logger.info(\"     * 'new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping'\")"]], "['\\\"]upload_failed['\\\"]": [[97, "if status in ['new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping']:"], [210, "logger.info(\"     * 'new', 'upload_failed', 'check_failed', 'reset_by_system', 'rejected', 'pending_grouping'\")"]], "['\\\"]already_tested['\\\"]": [[100, "elif status == 'already_tested':"], [187, "if has_campaigns and local_creative.status != 'already_tested':"], [190, "local_creative.status = 'already_tested'"], [215, "logger.info(\"     * 如果已创建过：状态改为 'already_tested'，跳过\")"], [219, "logger.info(\"   - 'already_tested': 已创建过测试计划，不会重复处理\")"]]}, "tools\\cleanup_after_force_stop.py": {"['\\\"]uploading['\\\"]": [[36, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan'])"]], "['\\\"]processing['\\\"]": [[36, "LocalCreative.status.in_(['processing', 'uploading', 'creating_plan'])"]], "['\\\"]pending_grouping['\\\"]": [[44, "creative.status = 'pending_grouping'"]]}, "tools\\fix_failed_uploads.py": {"['\\\"]pending_grouping['\\\"]": [[120, "creative.status = 'pending_grouping'"]], "['\\\"]upload_failed['\\\"]": [[48, "query = db.query(LocalCreative).filter(LocalCreative.status == 'upload_failed')"]]}, "tools\\manage_already_tested.py": {"['\\\"]already_tested['\\\"]": [[113, "creative.status = 'already_tested'"], [192, "LocalCreative.status != 'already_tested'"], [197, "creative.status = 'already_tested'"]]}, "tools\\material_harvest_monitor.py": {"['\\\"]approved['\\\"]": [[108, "'approved_creatives': db.query(LocalCreative).filter(LocalCreative.status == 'approved').count(),"]], "['\\\"]rejected['\\\"]": [[109, "'rejected_creatives': db.query(LocalCreative).filter(LocalCreative.status == 'rejected').count(),"]]}, "tools\\reset_zombie_materials.py": {"['\\\"]processing['\\\"]": [[36, "LocalCreative.status == 'processing',"], [82, "LocalCreative.status == 'processing'"], [125, "LocalCreative.status == 'processing'"]], "['\\\"]pending_grouping['\\\"]": [[59, "material.status = 'pending_grouping'  # 重置为待分组状态"], [96, "material.status = 'pending_grouping'"]]}}, "analysis": {"hardcoded_status": ["backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py", "ai_tools\\ai_tool_20250722_deploy_verification.py", "tests\\test_realtime_harvest.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py", "ai_tools\\ai_tool_20250722_validation_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py", "tests\\test_monitoring_system.py", "tools\\check_material_status.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py", "ai_tools\\ai_tool_20250722_workflow_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_system_monitoring_page.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_fix.py", "ai_temp\\check_workflow_execution_20250810.py", "ai_temp\\20250809_check_file_existence.py", "ai_temp\\replace_hardcoded_statuses_20250810.py", "ai_tools\\ai_tool_20250722_system_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_final_solution.py", "ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py", "ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_flow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_monitor.py", "ai_temp\\check_video_files_status_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_quick_file_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py", "ai_temp\\manual_workflow_processor_20250810.py", "ai_tools\\appeal_workflow_integration_patch.py", "ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py", "ai_tools\\ai_tool_20250721_file_management_fixes.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py", "src\\qianchuan_aw\\workflows\\appeal_and_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py", "ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py", "tools\\cleanup_after_force_stop.py", "ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py", "tools\\material_harvest_monitor.py", "ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py", "ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py", "ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py", "ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py", "ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py", "ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py", "ai_tools\\ai_tool_20250720_enhanced_material_analytics.py", "ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py", "ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py", "ai_temp\\manual_celery_worker_20250810.py", "ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py", "ai_tools\\ai_tool_20250722_platform_creative_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py", "ai_temp\\20250808_quick_status_check.py", "ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py", "ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py", "ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py", "ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py", "src\\qianchuan_aw\\services\\monitoring_service.py", "src\\qianchuan_aw\\utils\\safe_deletion_checker.py", "ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py", "ai_tools\\ai_tool_20250720_webui_monitoring_integration.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "src\\qianchuan_aw\\utils\\constants.py", "ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_duration_test.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py", "ai_tools\\ai_tool_20250722_celery_workflow_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_system_monitoring.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_celery_workflow_fix.py", "tests\\test_fault_tolerance.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py", "ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py", "ai_tools\\ai_tool_20250722_workflow_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py", "ai_temp\\20250808_test_atomic_state_integration.py", "ai_tools\\ai_tool_20250720_complete_material_analytics.py", "ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py", "ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py", "ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_system_diagnosis.py", "tools\\reset_zombie_materials.py", "ai_tools\\ai_tool_20250720_status_fix_verification.py", "ai_tools\\ai_tool_20250722_status_flow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_complete_material_analytics.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py", "ai_temp\\test_upload_fix_final_20250810.py", "src\\qianchuan_aw\\utils\\workflow_helpers.py", "ai_tools\\ai_tool_20250722_workflow_restoration.py", "ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py", "ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py", "ai_temp\\20250808_quick_verification.py", "ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_platform_creative_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py", "ai_temp\\workflow_recovery_plan_20250810.py", "ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_appeal_fixes.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py", "ai_tools\\ai_tool_20250722_final_solution.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py", "src\\qianchuan_aw\\services\\realtime_harvest_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_realtime_monitor.py", "ai_tools\\ai_tool_20250721_quick_file_fix.py", "src\\qianchuan_aw\\utils\\state_managers.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py", "ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py", "ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py", "ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py", "ai_tools\\ai_tool_20250720_monitoring_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250810_alert_system.py", "web_ui.py", "ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py", "backups\\weekly_backup_20250810_124054\\web_ui.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deploy_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_restoration.py", "ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py", "src\\qianchuan_aw\\services\\harvest_idempotency_manager.py", "ai_tools\\ai_tool_20250720_workflow_optimization_test.py", "src\\qianchuan_aw\\ui\\pages\\material_search.py", "ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_lifecycle_monitor.py", "ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py", "ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_log_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py", "ai_tools\\ai_tool_20250721_system_monitoring.py", "ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py", "src\\qianchuan_aw\\utils\\workflow_status.py", "ai_temp\\20250809_direct_status_conversion.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_analysis.py", "ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py", "temp_tools\\emergency_fix.py", "ai_temp\\20250808_test_batch_plans_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py", "src\\qianchuan_aw\\utils\\monitoring_mcp.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\appeal_workflow_integration_patch.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py", "ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py", "ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py", "src\\qianchuan_aw\\utils\\unified_material_status.py", "ai_tools\\ai_tool_20250720_system_monitoring_page.py", "ai_temp\\comprehensive_project_scan_20250810.py", "ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py", "ai_tools\\ai_tool_20250720_status_duration_test.py", "ai_tools\\ai_tool_20250722_status_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_validation_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_enhanced_material_analytics.py", "src\\qianchuan_aw\\workflows\\independent_material_harvest.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py", "ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py", "ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py", "ai_temp\\test_fixed_upload_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py", "ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py", "ai_tools\\ai_tool_20250722_realtime_monitor.py", "ai_temp\\test_workflow_fixes_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py", "ai_temp\\20250809_test_batch_upload_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py", "ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py", "src\\qianchuan_aw\\utils\\appeal_state_manager.py", "ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py", "ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py", "ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py", "src\\qianchuan_aw\\services\\event_detector.py", "ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py", "ai_tools\\ai_tool_20250720_lifecycle_monitor.py", "ai_temp\\20250808_test_batch_processing.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py", "ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_monitoring_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py", "ai_tools\\ai_tool_20250722_deep_log_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py", "src\\qianchuan_aw\\workflows\\scheduler_backup_20250722_155220.py", "ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py", "src\\qianchuan_aw\\workflows\\realtime_harvest_workflow.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py", "ai_temp\\diagnose_upload_stuck_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py", "ai_tools\\ai_tool_20250801_workflow_resilience_system.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "tests\\load\\test_load_testing.py", "ai_temp\\fix_file_ingestion_config_20250810.py", "web_ui_backup_20250723.py", "ai_tools\\ai_tool_20250721_file_management_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "tests\\performance\\test_performance_suite.py", "ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py", "ai_tools\\ai_tool_20250722_appeal_fixes.py", "ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_fixes.py", "src\\qianchuan_aw\\services\\unified_appeal_service.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_workflow_optimization_test.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_webui_monitoring_integration.py", "ai_tools\\ai_tool_20250722_emergency_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py", "ai_temp\\process_remaining_files_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_alert_system.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py", "ai_tools\\ai_tool_20250722_deep_fix.py", "ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_resilience_system.py"], "missing_import": ["src\\qianchuan_aw\\database\\models.py", "tests\\test_state_managers.py", "src\\qianchuan_aw\\workflows\\tasks.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "ai_temp\\test_enhanced_state_manager_20250810.py", "src\\qianchuan_aw\\utils\\unified_material_status.py", "ai_temp\\replace_hardcoded_statuses_20250810.py", "src\\qianchuan_aw\\utils\\material_state_validator.py", "ai_temp\\performance_benchmark_20250810.py", "src\\qianchuan_aw\\utils\\state_managers.py", "src\\qianchuan_aw\\utils\\atomic_state_manager.py", "ai_temp\\database_migration_20250810_add_state_version.py", "src\\qianchuan_aw\\workflows\\flexible_grouping.py", "src\\qianchuan_aw\\workflows\\scheduler.py"], "other_status_enums": ["backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_recovery_executor.py", "src\\qianchuan_aw\\workflows\\scheduler.py", "src\\qianchuan_aw\\utils\\workflow_status.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "src\\qianchuan_aw\\workflows\\common\\plan_creation.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "ai_tools\\ai_tool_20250801_workflow_resilience_system.py", "ai_tools\\ai_tool_20250801_workflow_recovery_executor.py", "ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_resilience_system.py"], "high_priority": ["backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py", "ai_tools\\ai_tool_20250722_deploy_verification.py", "tests\\test_realtime_harvest.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py", "ai_tools\\ai_tool_20250722_validation_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_task_scheduling_effectiveness.py", "tests\\test_monitoring_system.py", "tools\\check_material_status.py", "ai_temp\\performance_benchmark_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py", "ai_tools\\ai_tool_20250722_workflow_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_system_monitoring_page.py", "src\\qianchuan_aw\\workflows\\scheduler.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_fix.py", "ai_temp\\check_workflow_execution_20250810.py", "ai_temp\\20250809_check_file_existence.py", "ai_temp\\replace_hardcoded_statuses_20250810.py", "ai_tools\\ai_tool_20250722_system_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_final_solution.py", "ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py", "ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_flow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_monitor.py", "ai_temp\\check_video_files_status_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_quick_file_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py", "ai_temp\\manual_workflow_processor_20250810.py", "ai_tools\\appeal_workflow_integration_patch.py", "ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py", "ai_tools\\ai_tool_20250721_file_management_fixes.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py", "src\\qianchuan_aw\\workflows\\appeal_and_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py", "ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py", "tools\\cleanup_after_force_stop.py", "ai_tools\\maintenance\\ai_tool_20250803_comprehensive_harvest_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py", "tools\\material_harvest_monitor.py", "ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py", "ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py", "ai_tools\\maintenance\\ai_tool_20250804_restore_pending_files.py", "ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py", "ai_tools\\monitoring\\ai_tool_20250808_realtime_system_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py", "ai_tools\\diagnosis\\ai_tool_20250804_workflow_upload_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py", "ai_tools\\ai_tool_20250720_enhanced_material_analytics.py", "ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py", "ai_tools\\optimization\\ai_tool_20250728_optimization_task_scheduling.py", "ai_temp\\manual_celery_worker_20250810.py", "ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py", "ai_tools\\ai_tool_20250722_platform_creative_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_reset_video_status_for_workflow.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py", "ai_temp\\20250808_quick_status_check.py", "ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_comprehensive_workflow_health_check.py", "ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py", "ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py", "ai_tools\\deployment\\ai_tool_20250808_system_restart_guide.py", "src\\qianchuan_aw\\services\\monitoring_service.py", "src\\qianchuan_aw\\utils\\safe_deletion_checker.py", "ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py", "src\\qianchuan_aw\\workflows\\flexible_grouping.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_deployment_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_health.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py", "ai_tools\\ai_tool_20250720_webui_monitoring_integration.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "src\\qianchuan_aw\\utils\\constants.py", "ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_status_duration_test.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py", "ai_tools\\ai_tool_20250722_celery_workflow_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_system_recovery_plan.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_system_monitoring.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_celery_workflow_fix.py", "tests\\test_fault_tolerance.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_anomaly_diagnosis.py", "ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py", "ai_tools\\ai_tool_20250722_workflow_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py", "ai_temp\\20250808_test_atomic_state_integration.py", "ai_tools\\ai_tool_20250720_complete_material_analytics.py", "ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py", "ai_tools\\enhancement\\ai_tool_20250731_enhancement_upload_reliability.py", "ai_tools\\monitoring\\ai_tool_20250726_rules_audit_real.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_system_diagnosis.py", "tools\\reset_zombie_materials.py", "ai_tools\\ai_tool_20250720_status_fix_verification.py", "ai_tools\\ai_tool_20250722_status_flow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_complete_material_analytics.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py", "ai_temp\\test_upload_fix_final_20250810.py", "src\\qianchuan_aw\\utils\\workflow_helpers.py", "ai_tools\\ai_tool_20250722_workflow_restoration.py", "ai_tools\\analysis\\ai_tool_20250728_analysis_core_workflow_diagnosis.py", "ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py", "ai_temp\\20250808_quick_verification.py", "ai_tools\\maintenance\\ai_tool_20250730_enhanced_upload_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_platform_creative_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_database_cleanup.py", "ai_temp\\workflow_recovery_plan_20250810.py", "ai_tools\\repair\\ai_tool_20250810_repair_missing_platform_creative_records.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_appeal_fixes.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\implementation\\ai_tool_20250808_phase1_emergency_fixes.py", "ai_tools\\ai_tool_20250722_final_solution.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_realtime_monitor.py", "src\\qianchuan_aw\\services\\realtime_harvest_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_realtime_monitor.py", "ai_tools\\ai_tool_20250721_quick_file_fix.py", "src\\qianchuan_aw\\utils\\state_managers.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py", "ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py", "ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py", "ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_data_consistency_verification.py", "ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py", "ai_tools\\ai_tool_20250720_monitoring_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250810_alert_system.py", "web_ui.py", "ai_tools\\ai_tool_20250722_immediate_duplicate_fix.py", "backups\\weekly_backup_20250810_124054\\web_ui.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deploy_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_restoration.py", "ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py", "src\\qianchuan_aw\\services\\harvest_idempotency_manager.py", "ai_tools\\ai_tool_20250720_workflow_optimization_test.py", "src\\qianchuan_aw\\ui\\pages\\material_search.py", "ai_tools\\ai_tool_20250723_grouping_optimization_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_lifecycle_monitor.py", "src\\qianchuan_aw\\utils\\atomic_state_manager.py", "ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py", "ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_deep_log_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py", "src\\qianchuan_aw\\database\\models.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250805_harvest_workflow_diagnosis.py", "ai_tools\\ai_tool_20250721_system_monitoring.py", "ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_core_workflow_comprehensive_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_bottleneck_fix.py", "src\\qianchuan_aw\\utils\\workflow_status.py", "src\\qianchuan_aw\\workflows\\tasks.py", "ai_temp\\20250809_direct_status_conversion.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_workflow_analysis.py", "ai_tools\\testing\\ai_tool_20250728_testing_workflow_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_stop_and_fix.py", "ai_temp\\database_migration_20250810_add_state_version.py", "temp_tools\\emergency_fix.py", "ai_temp\\20250808_test_batch_plans_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py", "src\\qianchuan_aw\\utils\\monitoring_mcp.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_status_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_streamlit_material_search_integration.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\appeal_workflow_integration_patch.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_reupload_materials_to_test_accounts.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250718_maintenance_cleanup_missing_files.py", "ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py", "ai_tools\\diagnosis\\ai_tool_20250801_upload_verification_diagnosis.py", "src\\qianchuan_aw\\utils\\unified_material_status.py", "ai_tools\\ai_tool_20250720_system_monitoring_page.py", "ai_temp\\comprehensive_project_scan_20250810.py", "ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py", "ai_tools\\ai_tool_20250720_status_duration_test.py", "ai_tools\\ai_tool_20250722_status_monitor.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py", "tests\\test_state_managers.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_validation_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_enhanced_material_analytics.py", "src\\qianchuan_aw\\workflows\\independent_material_harvest.py", "ai_tools\\maintenance\\ai_tool_20250724_maintenance_workflow_test_suite.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\verification\\ai_tool_20250808_phase2_complete_verification.py", "ai_tools\\maintenance\\ai_tool_20250802_fix_harvest_account_type_filtering.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_emergency_workflow_recovery.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250808_performance_metrics_dashboard.py", "ai_tools\\monitoring\\ai_tool_20250727_workflow_monitor_fixed.py", "ai_tools\\diagnosis\\ai_tool_20250726_diagnosis_appeal_harvest_issues.py", "ai_temp\\test_fixed_upload_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250728_monitoring_workflow_status_simple.py", "ai_tools\\maintenance\\ai_tool_20250723_mcp_duplicate_fix.py", "ai_tools\\ai_tool_20250722_realtime_monitor.py", "ai_temp\\test_workflow_fixes_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250726_workflow_monitor.py", "ai_temp\\20250809_test_batch_upload_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250803_workflow_comprehensive_fix.py", "ai_tools\\emergency\\ai_tool_20250725_emergency_task_cleanup.py", "src\\qianchuan_aw\\utils\\appeal_state_manager.py", "ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py", "ai_tools\\emergency\\ai_tool_20250726_emergency_concurrent_fix.py", "ai_tools\\deployment\\ai_tool_20250808_safe_restart_guide.py", "src\\qianchuan_aw\\services\\event_detector.py", "ai_tools\\diagnosis\\ai_tool_20250803_workflow_comprehensive_diagnosis.py", "ai_tools\\ai_tool_20250720_lifecycle_monitor.py", "ai_temp\\20250808_test_batch_processing.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\analytics\\ai_tool_20250803_analytics_test_material_report.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\frontend\\ai_tool_20250727_frontend_material_search_enhancement.py", "ai_tools\\deployment\\ai_tool_20250808_final_restart_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_monitoring_dashboard.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250810_workflow_backlog_processor.py", "ai_tools\\ai_tool_20250722_deep_log_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250727_scheduled_compliance_audit.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\diagnosis\\ai_tool_20250728_diagnosis_celery_task_scheduling.py", "src\\qianchuan_aw\\workflows\\scheduler_backup_20250722_155220.py", "ai_tools\\monitoring\\ai_tool_20250810_comprehensive_health_monitor.py", "src\\qianchuan_aw\\workflows\\realtime_harvest_workflow.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\repair\\ai_tool_20250728_repair_workflow_comprehensive_fix.py", "ai_temp\\diagnose_upload_stuck_20250810.py", "ai_temp\\test_enhanced_state_manager_20250810.py", "src\\qianchuan_aw\\utils\\material_state_validator.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250727_emergency_workflow_repair.py", "ai_tools\\ai_tool_20250801_workflow_resilience_system.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250724_maintenance_status_consistency_fix.py", "tests\\load\\test_load_testing.py", "ai_temp\\fix_file_ingestion_config_20250810.py", "web_ui_backup_20250723.py", "ai_tools\\ai_tool_20250721_file_management_analysis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250809_smart_status_correction.py", "tests\\performance\\test_performance_suite.py", "ai_tools\\maintenance\\ai_tool_20250810_automated_operations.py", "ai_tools\\ai_tool_20250722_appeal_fixes.py", "ai_tools\\analysis\\ai_tool_20250808_comprehensive_architecture_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250808_emergency_fix_verification.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250721_file_management_fixes.py", "src\\qianchuan_aw\\services\\unified_appeal_service.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250722_emergency_workflow_diagnosis.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_workflow_optimization_test.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250720_webui_monitoring_integration.py", "ai_tools\\ai_tool_20250722_emergency_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\emergency\\ai_tool_20250809_pending_grouping_fix_verification.py", "ai_temp\\process_remaining_files_20250810.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\monitoring\\ai_tool_20250810_alert_system.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\maintenance\\ai_tool_20250723_optimize_upload_performance.py", "ai_tools\\ai_tool_20250722_deep_fix.py", "ai_tools\\maintenance\\ai_tool_20250803_harvest_workflow_fix.py", "backups\\weekly_backup_20250810_124054\\ai_tools\\ai_tool_20250801_workflow_resilience_system.py"]}, "timestamp": "2025-08-10T18:05:26.007565"}