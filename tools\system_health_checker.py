#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康检查器
================
文件类型: 核心工具
生命周期: 永久保留
创建目的: 集成所有维护工具，提供统一的系统健康检查入口
维护团队: 技术团队
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# 导入维护工具
try:
    from tools.maintenance.error_analysis_and_fix import ErrorAnalyzer
    from tools.maintenance.smart_retry_optimizer import SmartRetryOptimizer
    from tools.quality.video_quality_precheck import VideoQualityPreChecker
except ImportError as e:
    print(f"❌ 导入维护工具失败: {e}")
    print("请确保所有维护工具都已正确安装")
    sys.exit(1)

class SystemHealthChecker:
    """系统健康检查器"""
    
    def __init__(self):
        self.error_analyzer = ErrorAnalyzer()
        self.retry_optimizer = SmartRetryOptimizer()
        self.quality_checker = VideoQualityPreChecker()
        
        self.health_score = 100
        self.issues_found = []
        self.fixes_applied = []
    
    def check_database_health(self) -> dict:
        """检查数据库健康状态"""
        print("🔍 检查数据库健康状态...")
        
        try:
            from qianchuan_aw.utils.db_utils import database_session
            from qianchuan_aw.database.models import LocalCreative, Campaign, AdAccount
            
            with database_session() as db:
                # 检查基本连接
                material_count = db.query(LocalCreative).count()
                campaign_count = db.query(Campaign).count()
                account_count = db.query(AdAccount).count()
                
                print(f"✅ 数据库连接正常")
                print(f"  - 素材记录: {material_count}")
                print(f"  - 计划记录: {campaign_count}")
                print(f"  - 账户记录: {account_count}")
                
                return {
                    'healthy': True,
                    'material_count': material_count,
                    'campaign_count': campaign_count,
                    'account_count': account_count
                }
                
        except Exception as e:
            print(f"❌ 数据库健康检查失败: {e}")
            self.health_score -= 20
            self.issues_found.append(f"数据库连接问题: {e}")
            return {'healthy': False, 'error': str(e)}
    
    def check_file_system_health(self) -> dict:
        """检查文件系统健康状态"""
        print("\n🔍 检查文件系统健康状态...")

        # 使用错误分析器检查文件问题
        file_analysis = self.error_analyzer.analyze_missing_files()
        missing_count = len(file_analysis.get('missing_files', []))
        existing_count = len(file_analysis.get('existing_files', []))

        if missing_count > 0:
            print(f"⚠️ 发现 {missing_count} 个文件缺失问题")
            self.health_score -= min(missing_count * 2, 30)  # 最多扣30分
            self.issues_found.append(f"{missing_count}个文件缺失")

            # 自动修复
            fixed_count = self.error_analyzer.fix_missing_files(file_analysis['missing_files'])
            if fixed_count > 0:
                self.fixes_applied.append(f"修复了{fixed_count}个文件缺失问题")

        print(f"📊 文件系统状态: {existing_count}个文件正常, {missing_count}个文件缺失")

        return {
            'healthy': missing_count == 0,
            'existing_files': existing_count,
            'missing_files': missing_count
        }

    def check_upload_system_health(self) -> dict:
        """检查上传系统健康状态"""
        print("\n🔍 检查上传系统健康状态...")

        # 检查卡住的上传任务
        fixed_stuck = self.error_analyzer.fix_stuck_uploading_materials()

        if fixed_stuck > 0:
            print(f"⚠️ 发现并修复了 {fixed_stuck} 个卡住的上传任务")
            self.health_score -= min(fixed_stuck * 3, 20)  # 最多扣20分
            self.issues_found.append(f"{fixed_stuck}个卡住的上传任务")
            self.fixes_applied.append(f"重置了{fixed_stuck}个卡住的上传任务")

        # 分析重试模式
        retry_analysis = self.retry_optimizer.analyze_current_retry_patterns()

        # 计算失败率
        total_materials = sum(retry_analysis.values()) if retry_analysis else 0
        failed_materials = retry_analysis.get('upload_failed', 0) if retry_analysis else 0
        failure_rate = 0

        if total_materials > 0:
            failure_rate = (failed_materials / total_materials) * 100
            print(f"📊 上传失败率: {failure_rate:.2f}%")

            if failure_rate > 20:
                self.health_score -= 25
                self.issues_found.append(f"上传失败率过高: {failure_rate:.1f}%")
            elif failure_rate > 10:
                self.health_score -= 10
                self.issues_found.append(f"上传失败率偏高: {failure_rate:.1f}%")

        return {
            'healthy': fixed_stuck == 0 and (failure_rate < 10 if total_materials > 0 else True),
            'stuck_tasks_fixed': fixed_stuck,
            'failure_rate': failure_rate
        }

    def check_video_quality_health(self) -> dict:
        """检查视频质量健康状态"""
        print("\n🔍 检查视频质量健康状态...")
        
        # 批量检查待上传素材质量
        quality_result = self.quality_checker.batch_check_pending_materials(limit=5)
        
        valid_count = len(quality_result['valid_materials'])
        invalid_count = len(quality_result['invalid_materials'])
        total_checked = valid_count + invalid_count
        
        if total_checked > 0:
            quality_rate = (valid_count / total_checked) * 100
            print(f"📊 视频质量合格率: {quality_rate:.2f}%")
            
            if quality_rate < 50:
                self.health_score -= 30
                self.issues_found.append(f"视频质量合格率过低: {quality_rate:.1f}%")
            elif quality_rate < 80:
                self.health_score -= 15
                self.issues_found.append(f"视频质量合格率偏低: {quality_rate:.1f}%")
        
        return {
            'healthy': invalid_count == 0 or (valid_count / max(total_checked, 1)) >= 0.8,
            'valid_materials': valid_count,
            'invalid_materials': invalid_count,
            'quality_rate': quality_rate if total_checked > 0 else 100
        }
    
    def generate_health_report(self) -> str:
        """生成健康报告"""
        report_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 确定健康等级
        if self.health_score >= 90:
            health_level = "🟢 优秀"
        elif self.health_score >= 70:
            health_level = "🟡 良好"
        elif self.health_score >= 50:
            health_level = "🟠 一般"
        else:
            health_level = "🔴 需要关注"
        
        report = f"""
{'='*60}
🏥 系统健康检查报告
{'='*60}
检查时间: {report_time}
健康评分: {self.health_score}/100
健康等级: {health_level}

"""
        
        if self.issues_found:
            report += "🚨 发现的问题:\n"
            for i, issue in enumerate(self.issues_found, 1):
                report += f"  {i}. {issue}\n"
            report += "\n"
        
        if self.fixes_applied:
            report += "🔧 已应用的修复:\n"
            for i, fix in enumerate(self.fixes_applied, 1):
                report += f"  {i}. {fix}\n"
            report += "\n"
        
        # 建议
        report += "💡 建议:\n"
        if self.health_score >= 90:
            report += "  - 系统运行良好，继续保持\n"
            report += "  - 建议定期运行健康检查\n"
        elif self.health_score >= 70:
            report += "  - 系统基本正常，有小问题需要关注\n"
            report += "  - 建议每周运行一次健康检查\n"
        else:
            report += "  - 系统存在较多问题，需要立即处理\n"
            report += "  - 建议每天运行健康检查直到问题解决\n"
        
        report += f"\n{'='*60}\n"
        
        return report
    
    def run_comprehensive_check(self) -> dict:
        """运行综合健康检查"""
        print("🚀 开始系统综合健康检查...")
        
        results = {}
        
        # 1. 数据库健康检查
        results['database'] = self.check_database_health()
        
        # 2. 文件系统健康检查
        results['filesystem'] = self.check_file_system_health()
        
        # 3. 上传系统健康检查
        results['upload_system'] = self.check_upload_system_health()
        
        # 4. 视频质量健康检查
        results['video_quality'] = self.check_video_quality_health()
        
        # 5. 生成报告
        health_report = self.generate_health_report()
        print(health_report)
        
        results['health_score'] = self.health_score
        results['health_report'] = health_report
        results['issues_found'] = self.issues_found
        results['fixes_applied'] = self.fixes_applied
        
        return results

def main():
    """主函数"""
    print("🏥 千川自动化系统健康检查器")
    print("=" * 60)
    
    checker = SystemHealthChecker()
    
    try:
        # 运行综合检查
        results = checker.run_comprehensive_check()
        
        # 保存报告
        report_file = Path("logs") / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        report_file.parent.mkdir(exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results['health_report'])
        
        print(f"📄 健康报告已保存到: {report_file}")
        
        # 返回健康评分
        if results['health_score'] >= 70:
            return 0  # 健康
        else:
            return 1  # 需要关注
            
    except Exception as e:
        print(f"❌ 健康检查过程中发生错误: {e}")
        return 2  # 检查失败

if __name__ == "__main__":
    exit(main())
