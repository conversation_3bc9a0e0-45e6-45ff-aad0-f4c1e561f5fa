"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 增强视频上传可靠性，实现铁律1改进方案
清理条件: 当上传可靠性机制被更好的方案替代时可删除
"""

import os
import sys
import time
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / 'src'))

from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.db_utils import database_session
from qianchuan_aw.database.models import LocalCreative


class UploadErrorType(Enum):
    """上传错误类型分类"""
    TEMPORARY_NETWORK = "temporary_network"      # 临时网络问题
    TEMPORARY_API_LIMIT = "temporary_api_limit"  # API限流
    TEMPORARY_SERVER = "temporary_server"        # 服务器临时错误
    PERMANENT_FILE = "permanent_file"            # 文件问题（格式、损坏等）
    PERMANENT_AUTH = "permanent_auth"            # 认证问题
    PERMANENT_QUOTA = "permanent_quota"          # 配额耗尽
    UNKNOWN = "unknown"                          # 未知错误


class UploadReliabilityEnhancer:
    """视频上传可靠性增强器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        self.max_retries = 5  # 增加到5次重试
        self.base_delay = 60  # 基础延迟60秒
        
    def classify_upload_error(self, error_message: str, status_code: Optional[int] = None) -> UploadErrorType:
        """分类上传错误类型"""
        error_msg = str(error_message).lower()
        
        # 临时性网络错误
        network_keywords = [
            'timeout', 'connection', 'network', 'dns', 'socket',
            'connection reset', 'connection refused', 'connection timeout'
        ]
        
        # 临时性API限流错误
        limit_keywords = [
            'rate limit', 'limit exceeded', 'too many requests',
            'quota exceeded', 'throttled', 'rate_limit'
        ]
        
        # 临时性服务器错误
        server_keywords = [
            'server error', 'internal server error', 'service unavailable',
            'temporarily unavailable', 'maintenance', 'overloaded'
        ]
        
        # 永久性文件错误
        file_keywords = [
            'invalid format', 'format not supported', 'file corrupted',
            'invalid file', 'file size', 'duration', 'resolution',
            'codec not supported', 'malformed file'
        ]
        
        # 永久性认证错误
        auth_keywords = [
            'unauthorized', 'authentication failed', 'invalid token',
            'access denied', 'permission denied', 'forbidden'
        ]
        
        # 永久性配额错误
        quota_keywords = [
            'quota exhausted', 'storage full', 'account suspended',
            'plan limit reached', 'billing issue'
        ]
        
        # 根据状态码判断
        if status_code:
            if status_code in [429]:  # Too Many Requests
                return UploadErrorType.TEMPORARY_API_LIMIT
            elif status_code in [500, 502, 503, 504]:  # Server errors
                return UploadErrorType.TEMPORARY_SERVER
            elif status_code in [401, 403]:  # Auth errors
                return UploadErrorType.PERMANENT_AUTH
            elif status_code in [400, 422]:  # Bad request, validation error
                return UploadErrorType.PERMANENT_FILE
        
        # 根据错误消息判断
        if any(keyword in error_msg for keyword in network_keywords):
            return UploadErrorType.TEMPORARY_NETWORK
        elif any(keyword in error_msg for keyword in limit_keywords):
            return UploadErrorType.TEMPORARY_API_LIMIT
        elif any(keyword in error_msg for keyword in server_keywords):
            return UploadErrorType.TEMPORARY_SERVER
        elif any(keyword in error_msg for keyword in file_keywords):
            return UploadErrorType.PERMANENT_FILE
        elif any(keyword in error_msg for keyword in auth_keywords):
            return UploadErrorType.PERMANENT_AUTH
        elif any(keyword in error_msg for keyword in quota_keywords):
            return UploadErrorType.PERMANENT_QUOTA
        
        return UploadErrorType.UNKNOWN
    
    def calculate_retry_delay(self, attempt: int, error_type: UploadErrorType) -> int:
        """计算重试延迟时间（指数退避）"""
        if error_type in [UploadErrorType.PERMANENT_FILE, UploadErrorType.PERMANENT_AUTH, UploadErrorType.PERMANENT_QUOTA]:
            return 0  # 永久性错误不重试
        
        # 指数退避：2^attempt * base_delay，但有上限
        delay = min(self.base_delay * (2 ** attempt), 600)  # 最大10分钟
        
        # 根据错误类型调整延迟
        if error_type == UploadErrorType.TEMPORARY_API_LIMIT:
            delay *= 2  # API限流延迟更长
        elif error_type == UploadErrorType.TEMPORARY_NETWORK:
            delay *= 0.5  # 网络错误延迟较短
        
        return int(delay)
    
    def should_retry(self, attempt: int, error_type: UploadErrorType) -> bool:
        """判断是否应该重试"""
        if attempt >= self.max_retries:
            return False
        
        # 永久性错误不重试
        if error_type in [UploadErrorType.PERMANENT_FILE, UploadErrorType.PERMANENT_AUTH, UploadErrorType.PERMANENT_QUOTA]:
            return False
        
        return True
    
    def send_upload_failure_alert(self, local_creative_id: int, error_message: str, error_type: UploadErrorType, attempt_count: int):
        """发送上传失败告警"""
        try:
            with database_session() as db:
                local_creative = db.get(LocalCreative, local_creative_id)
                if not local_creative:
                    logger.error(f"无法发送告警：找不到素材 ID {local_creative_id}")
                    return
                
                filename = os.path.basename(local_creative.file_path) if local_creative.file_path else "未知文件"
                
                alert_data = {
                    'timestamp': datetime.now().isoformat(),
                    'alert_type': 'upload_failure',
                    'severity': 'high' if error_type.value.startswith('permanent') else 'medium',
                    'material_id': local_creative_id,
                    'filename': filename,
                    'error_type': error_type.value,
                    'error_message': error_message,
                    'attempt_count': attempt_count,
                    'max_retries': self.max_retries
                }
                
                # 记录到日志
                logger.error(f"🚨 视频上传失败告警: {filename}")
                logger.error(f"   错误类型: {error_type.value}")
                logger.error(f"   错误信息: {error_message}")
                logger.error(f"   重试次数: {attempt_count}/{self.max_retries}")
                
                # TODO: 集成实际的告警系统（邮件、钉钉、企业微信等）
                self._write_alert_to_file(alert_data)
                
        except Exception as e:
            logger.error(f"发送上传失败告警时出错: {e}")
    
    def _write_alert_to_file(self, alert_data: Dict[str, Any]):
        """将告警写入文件（临时方案）"""
        try:
            alert_dir = Path("ai_reports/alerts")
            alert_dir.mkdir(parents=True, exist_ok=True)
            
            alert_file = alert_dir / f"upload_failures_{datetime.now().strftime('%Y%m%d')}.log"
            
            with open(alert_file, 'a', encoding='utf-8') as f:
                import json
                f.write(json.dumps(alert_data, ensure_ascii=False) + '\n')
                
        except Exception as e:
            logger.error(f"写入告警文件失败: {e}")
    
    def verify_upload_success(self, client, account_id: int, material_id: str, video_id: str) -> bool:
        """验证上传是否真正成功 - 简化版本，只要有material_id就认为成功"""
        import time

        # 如果有material_id，就认为上传成功（因为API已经返回了material_id）
        if material_id and str(material_id).strip():
            logger.info(f"✅ 上传验证成功: material_id={material_id} (简化验证)")
            return True

        # 如果没有material_id但有video_id，也认为成功
        if video_id and str(video_id).strip():
            logger.info(f"✅ 上传验证成功: video_id={video_id} (简化验证)")
            return True

        # 都没有才认为失败
        logger.warning(f"⚠️ 上传验证失败: material_id和video_id都为空")
        return False
    
    def get_upload_statistics(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        try:
            with database_session() as db:
                # 统计各种状态的素材数量
                from sqlalchemy import func
                from qianchuan_aw.database.models import LocalCreative
                
                stats = db.query(
                    LocalCreative.status,
                    func.count(LocalCreative.id).label('count')
                ).group_by(LocalCreative.status).all()
                
                status_counts = {stat.status: stat.count for stat in stats}
                
                # 计算成功率
                total_processed = sum(count for status, count in status_counts.items() 
                                    if status in [MaterialStatus.UPLOADED_PENDING_PLAN.value, MaterialStatus.TESTING_PENDING_REVIEW.value, MaterialStatus.APPROVED.value, MaterialStatus.REJECTED.value])
                
                successful_uploads = status_counts.get(MaterialStatus.UPLOADED_PENDING_PLAN.value, 0) + \
                                   status_counts.get(MaterialStatus.TESTING_PENDING_REVIEW.value, 0) + \
                                   status_counts.get(MaterialStatus.APPROVED.value, 0)
                
                success_rate = (successful_uploads / total_processed * 100) if total_processed > 0 else 0
                
                return {
                    'total_materials': sum(status_counts.values()),
                    'total_processed': total_processed,
                    'successful_uploads': successful_uploads,
                    'upload_success_rate': round(success_rate, 2),
                    'status_breakdown': status_counts,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取上传统计信息失败: {e}")
            return {}


def main():
    """测试上传可靠性增强器"""
    logger.info("🔧 测试上传可靠性增强器")
    
    # 模拟配置
    app_settings = {
        'robustness': {
            'max_retries_for_upload': 3,
            'upload_retry_delay': 30
        }
    }
    
    enhancer = UploadReliabilityEnhancer(app_settings)
    
    # 测试错误分类
    test_errors = [
        ("Connection timeout", None),
        ("Rate limit exceeded", 429),
        ("Invalid video format", 400),
        ("Server internal error", 500),
        ("Authentication failed", 401),
    ]
    
    for error_msg, status_code in test_errors:
        error_type = enhancer.classify_upload_error(error_msg, status_code)
        should_retry = enhancer.should_retry(1, error_type)
        delay = enhancer.calculate_retry_delay(1, error_type)
        
        logger.info(f"错误: {error_msg}")
        logger.info(f"  类型: {error_type.value}")
        logger.info(f"  重试: {should_retry}")
        logger.info(f"  延迟: {delay}秒")
        logger.info("---")
    
    # 获取统计信息
    stats = enhancer.get_upload_statistics()
    if stats:
        logger.info("📊 上传统计信息:")
        logger.info(f"  总素材数: {stats.get('total_materials', 0)}")
        logger.info(f"  已处理数: {stats.get('total_processed', 0)}")
        logger.info(f"  成功上传: {stats.get('successful_uploads', 0)}")
        logger.info(f"  成功率: {stats.get('upload_success_rate', 0)}%")


if __name__ == "__main__":
    main()
