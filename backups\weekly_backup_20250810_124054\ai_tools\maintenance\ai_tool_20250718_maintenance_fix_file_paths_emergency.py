#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 紧急修复工具
生命周期: 7天（紧急修复后可删除）
创建目的: 紧急修复数据库中错误的文件路径（G盘->D盘）
依赖关系: 依赖数据库连接和配置文件
清理条件: 文件路径修复完成后
"""

import os
import sys
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.qianchuan_aw.utils.db_utils import database_session
from src.qianchuan_aw.database.models import LocalCreative
from src.qianchuan_aw.utils.config_loader import load_settings

def setup_logger():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/emergency_path_fix.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def fix_file_paths_emergency():
    """紧急修复文件路径"""
    logger = setup_logger()
    logger.info("🚨 开始紧急修复文件路径...")
    
    try:
        # 加载配置
        app_settings = load_settings()
        correct_base_dir = app_settings.get('custom_workflow_assets_dir', 'D:/Project/qianchuangzl/workflow_assets')
        
        logger.info(f"正确的基础目录: {correct_base_dir}")
        
        with database_session() as db:
            # 查询所有G盘路径的记录
            g_drive_records = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('G:/%')
            ).all()
            
            logger.info(f"发现 {len(g_drive_records)} 条G盘路径记录需要修复")
            
            fixed_count = 0
            error_count = 0
            
            for record in g_drive_records:
                try:
                    old_path = record.file_path
                    
                    # 将G:/workflow_assets替换为正确路径
                    if old_path.startswith('G:/workflow_assets'):
                        new_path = old_path.replace('G:/workflow_assets', correct_base_dir)
                    elif old_path.startswith('G:\\workflow_assets'):
                        new_path = old_path.replace('G:\\workflow_assets', correct_base_dir)
                    else:
                        # 其他G盘路径，尝试智能替换
                        new_path = old_path.replace('G:/', 'D:/Project/qianchuangzl/')
                    
                    # 标准化路径分隔符
                    new_path = new_path.replace('\\', '/')
                    
                    # 更新数据库记录
                    record.file_path = new_path
                    
                    fixed_count += 1
                    
                    if fixed_count % 100 == 0:
                        logger.info(f"已修复 {fixed_count} 条记录...")
                        
                except Exception as e:
                    logger.error(f"修复记录失败 ID:{record.id}, 路径:{record.file_path}, 错误:{e}")
                    error_count += 1
            
            # 提交更改
            db.commit()
            
            logger.info(f"✅ 路径修复完成!")
            logger.info(f"   - 成功修复: {fixed_count} 条记录")
            logger.info(f"   - 修复失败: {error_count} 条记录")
            
            # 验证修复结果
            remaining_g_paths = db.query(LocalCreative).filter(
                LocalCreative.file_path.like('G:/%')
            ).count()
            
            logger.info(f"   - 剩余G盘路径: {remaining_g_paths} 条")
            
            return fixed_count, error_count
            
    except Exception as e:
        logger.error(f"❌ 紧急修复失败: {e}")
        raise

def verify_file_existence():
    """验证修复后的文件是否存在"""
    logger = setup_logger()
    logger.info("🔍 验证文件存在性...")
    
    try:
        with database_session() as db:
            # 随机抽取一些记录验证
            sample_records = db.query(LocalCreative).filter(
                LocalCreative.file_path.isnot(None)
            ).limit(50).all()
            
            exists_count = 0
            missing_count = 0
            
            for record in sample_records:
                if os.path.exists(record.file_path):
                    exists_count += 1
                else:
                    missing_count += 1
                    logger.warning(f"文件不存在: {record.file_path}")
            
            logger.info(f"📊 文件存在性验证结果:")
            logger.info(f"   - 存在: {exists_count} 个文件")
            logger.info(f"   - 缺失: {missing_count} 个文件")
            
            return exists_count, missing_count
            
    except Exception as e:
        logger.error(f"验证失败: {e}")
        return 0, 0

def check_workflow_directories():
    """检查工作流目录状态"""
    logger = setup_logger()
    logger.info("📁 检查工作流目录状态...")
    
    try:
        app_settings = load_settings()
        base_dir = app_settings.get('custom_workflow_assets_dir', 'D:/Project/qianchuangzl/workflow_assets')
        
        workflow_dirs = [
            '00_materials_archived',
            '01_materials_to_process', 
            '02_materials_in_testing',
            '03_materials_approved',
            '04_materials_rejected',
            '05_manual_promotion'
        ]
        
        for dir_name in workflow_dirs:
            dir_path = os.path.join(base_dir, dir_name)
            if os.path.exists(dir_path):
                file_count = sum(len(files) for _, _, files in os.walk(dir_path))
                logger.info(f"✅ {dir_name}: 存在，包含 {file_count} 个文件")
            else:
                logger.warning(f"⚠️ {dir_name}: 不存在")
                
    except Exception as e:
        logger.error(f"检查目录失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='紧急修复文件路径工具')
    parser.add_argument('action', choices=['fix', 'verify', 'check'], 
                       help='执行的操作: fix=修复路径, verify=验证文件, check=检查目录')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际修改')
    
    args = parser.parse_args()
    
    if args.action == 'fix':
        if args.dry_run:
            print("预览模式 - 不会实际修改数据库")
            # 这里可以添加预览逻辑
        else:
            fixed, errors = fix_file_paths_emergency()
            print(f"\n🎉 修复完成: 成功 {fixed} 条, 失败 {errors} 条")
    
    elif args.action == 'verify':
        exists, missing = verify_file_existence()
        print(f"\n📊 验证结果: 存在 {exists} 个, 缺失 {missing} 个")
    
    elif args.action == 'check':
        check_workflow_directories()

if __name__ == "__main__":
    main()
