#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 启动实时合规性监控服务
清理条件: 集成到主系统后可删除

千川自动化实时监控服务启动器
========================

启动24/7合规性监控，设置检查间隔为60秒
配置警报阈值：新违规1个立即报警，总违规超过5个报警
"""

import sys
import os
import time
import threading
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from qianchuan_aw.utils.logger import logger

try:
    from ai_tool_20250727_realtime_compliance_monitor import RealTimeComplianceMonitor
    MONITOR_AVAILABLE = True
except ImportError as e:
    MONITOR_AVAILABLE = False
    logger.error(f"监控模块导入失败: {e}")


class MonitoringService:
    """监控服务管理器"""
    
    def __init__(self):
        self.monitor = None
        self.service_running = False
        self.service_thread = None
        
        if MONITOR_AVAILABLE:
            self.monitor = RealTimeComplianceMonitor()
            # 配置监控参数
            self.monitor.check_interval = 60  # 60秒检查间隔
            self.monitor.alert_threshold = {
                'new_violations': 1,  # 新违规1个立即报警
                'total_violations': 5,  # 总违规超过5个报警
                'violation_growth_rate': 0.1  # 增长率超过10%报警
            }
            logger.info("✅ 监控服务初始化成功")
        else:
            logger.error("❌ 监控服务初始化失败")
    
    def start_service(self):
        """启动监控服务"""
        if not MONITOR_AVAILABLE or not self.monitor:
            logger.error("❌ 监控模块不可用，无法启动服务")
            return False
        
        if self.service_running:
            logger.warning("⚠️ 监控服务已在运行中")
            return True
        
        try:
            logger.info("🚀 启动实时合规性监控服务...")
            
            # 启动监控
            self.monitor.start_monitoring()
            self.service_running = True
            
            logger.info("✅ 实时合规性监控服务启动成功")
            logger.info(f"📊 监控配置:")
            logger.info(f"   检查间隔: {self.monitor.check_interval} 秒")
            logger.info(f"   新违规阈值: {self.monitor.alert_threshold['new_violations']} 个")
            logger.info(f"   总违规阈值: {self.monitor.alert_threshold['total_violations']} 个")
            logger.info(f"   增长率阈值: {self.monitor.alert_threshold['violation_growth_rate']:.1%}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 监控服务启动失败: {e}")
            return False
    
    def stop_service(self):
        """停止监控服务"""
        if not self.service_running:
            logger.info("ℹ️ 监控服务未运行")
            return True
        
        try:
            logger.info("⏹️ 停止实时合规性监控服务...")
            
            if self.monitor:
                self.monitor.stop_monitoring()
            
            self.service_running = False
            
            logger.info("✅ 实时合规性监控服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"❌ 监控服务停止失败: {e}")
            return False
    
    def get_service_status(self):
        """获取服务状态"""
        if not MONITOR_AVAILABLE:
            return {
                'available': False,
                'running': False,
                'error': '监控模块不可用'
            }
        
        if not self.monitor:
            return {
                'available': False,
                'running': False,
                'error': '监控实例未初始化'
            }
        
        monitor_status = self.monitor.get_monitoring_status()
        
        return {
            'available': True,
            'running': self.service_running and monitor_status['monitoring'],
            'last_check_time': monitor_status.get('last_check_time'),
            'check_interval': monitor_status.get('check_interval'),
            'alert_count': monitor_status.get('alert_count', 0),
            'recent_alerts': monitor_status.get('recent_alerts', [])
        }
    
    def test_monitoring(self):
        """测试监控功能"""
        if not MONITOR_AVAILABLE or not self.monitor:
            logger.error("❌ 监控模块不可用，无法测试")
            return False
        
        try:
            logger.info("🧪 测试监控功能...")
            
            # 执行一次违规检测
            violations = self.monitor.detect_violations()
            logger.info(f"检测到 {len(violations)} 个违规")
            
            # 分析趋势
            trend_analysis = self.monitor.analyze_violation_trends(violations)
            logger.info(f"趋势分析: {trend_analysis['trend']}")
            
            # 生成警报
            alerts = self.monitor.generate_alerts(violations, trend_analysis)
            logger.info(f"生成 {len(alerts)} 个警报")
            
            # 生成报告
            report = self.monitor.generate_compliance_report()
            
            # 保存测试报告
            os.makedirs('ai_reports', exist_ok=True)
            test_report_file = f"ai_reports/monitoring_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            with open(test_report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"📄 测试报告已保存: {test_report_file}")
            logger.info("✅ 监控功能测试完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 监控功能测试失败: {e}")
            return False
    
    def run_interactive_service(self):
        """运行交互式监控服务"""
        logger.info("🎮 启动交互式监控服务...")
        
        if not self.start_service():
            return
        
        try:
            print("\n" + "="*60)
            print("千川自动化实时合规性监控服务")
            print("="*60)
            print("监控状态: 🟢 运行中")
            print(f"检查间隔: {self.monitor.check_interval} 秒")
            print("="*60)
            print("\n可用命令:")
            print("  status  - 查看监控状态")
            print("  test    - 执行监控测试")
            print("  report  - 生成合规报告")
            print("  stop    - 停止监控服务")
            print("  quit    - 退出程序")
            print("\n按 Ctrl+C 或输入 'quit' 退出")
            print("-"*60)
            
            while self.service_running:
                try:
                    command = input("\n监控服务> ").strip().lower()
                    
                    if command == 'quit' or command == 'exit':
                        break
                    elif command == 'status':
                        status = self.get_service_status()
                        print(f"\n📊 监控状态:")
                        print(f"   运行状态: {'🟢 运行中' if status['running'] else '🔴 已停止'}")
                        print(f"   最后检查: {status.get('last_check_time', 'N/A')}")
                        print(f"   警报数量: {status.get('alert_count', 0)} 个")
                    elif command == 'test':
                        print("\n🧪 执行监控测试...")
                        self.test_monitoring()
                    elif command == 'report':
                        print("\n📊 生成合规报告...")
                        if self.monitor:
                            report = self.monitor.generate_compliance_report()
                            print(report[:500] + "..." if len(report) > 500 else report)
                    elif command == 'stop':
                        break
                    elif command == '':
                        continue
                    else:
                        print(f"未知命令: {command}")
                        
                except KeyboardInterrupt:
                    break
                except EOFError:
                    break
                except Exception as e:
                    logger.error(f"命令执行异常: {e}")
            
        except KeyboardInterrupt:
            logger.info("收到中断信号...")
        finally:
            self.stop_service()
            logger.info("👋 监控服务已退出")


def main():
    """主函数"""
    print("🔍 千川自动化实时合规性监控服务")
    print("=" * 60)
    
    service = MonitoringService()
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'start':
            # 启动服务
            if service.start_service():
                print("✅ 监控服务启动成功")
                try:
                    while True:
                        time.sleep(60)
                        status = service.get_service_status()
                        if status['running']:
                            print(f"📊 监控运行中... (警报: {status.get('alert_count', 0)} 个)")
                        else:
                            print("⚠️ 监控服务已停止")
                            break
                except KeyboardInterrupt:
                    print("\n收到中断信号，停止监控...")
                    service.stop_service()
            else:
                print("❌ 监控服务启动失败")
                return 1
                
        elif command == 'test':
            # 测试监控功能
            if service.test_monitoring():
                print("✅ 监控功能测试成功")
                return 0
            else:
                print("❌ 监控功能测试失败")
                return 1
                
        elif command == 'status':
            # 查看状态
            status = service.get_service_status()
            print(f"监控状态: {'可用' if status['available'] else '不可用'}")
            print(f"运行状态: {'运行中' if status['running'] else '已停止'}")
            if status.get('last_check_time'):
                print(f"最后检查: {status['last_check_time']}")
            print(f"警报数量: {status.get('alert_count', 0)} 个")
            return 0
            
        else:
            print(f"未知命令: {command}")
            print("可用命令: start, test, status")
            return 1
    else:
        # 交互式模式
        service.run_interactive_service()
        return 0


if __name__ == '__main__':
    exit(main())
