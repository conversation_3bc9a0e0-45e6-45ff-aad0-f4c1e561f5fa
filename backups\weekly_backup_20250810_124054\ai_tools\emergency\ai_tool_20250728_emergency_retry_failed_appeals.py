#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 临时工具
生命周期: 7天
创建目的: 重试失败的提审计划
清理条件: 问题解决后可删除
"""

import os
import sys
import time
from pathlib import Path
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def retry_failed_appeal(campaign_id: str):
    """重试失败的提审"""
    logger.info(f"🔄 重试失败提审: {campaign_id}")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from qianchuan_aw.services.production_appeal_service import create_production_appeal_service
        from sqlalchemy.orm import joinedload
        import yaml
        
        # 加载配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            app_settings = yaml.safe_load(f)
        
        # 查找指定计划
        db = SessionLocal()
        try:
            campaign = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(Campaign.campaign_id_qc == campaign_id).first()
            
            if not campaign:
                logger.error(f"❌ 未找到计划: {campaign_id}")
                return False
            
            logger.info(f"📋 计划详情:")
            logger.info(f"   📊 状态: {campaign.status}")
            logger.info(f"   📊 提审状态: {campaign.appeal_status}")
            logger.info(f"   📊 尝试次数: {campaign.appeal_attempt_count}")
            logger.info(f"   📊 错误信息: {campaign.appeal_error_message}")
            logger.info(f"   🏢 广告户: {campaign.account.principal.name}")
            
            # 检查是否可以重试
            if campaign.status != 'AUDITING':
                logger.warning(f"⚠️ 计划状态不是AUDITING: {campaign.status}")
                return False
            
            if campaign.appeal_status == 'appeal_pending':
                logger.warning(f"⚠️ 计划已经提审成功，无需重试")
                return False
            
            # 重置提审状态，准备重试
            logger.info("🔄 重置提审状态，准备重试...")
            campaign.appeal_status = None
            campaign.appeal_error_message = None
            campaign.first_appeal_at = None
            campaign.appeal_attempt_count = 0
            db.commit()
            
            logger.info("✅ 提审状态已重置")
            
            # 准备提审数据
            plans_data = [{
                'campaign_id': campaign.campaign_id_qc,
                'principal_name': campaign.account.principal.name,
                'account_id': campaign.account.account_id_qc
            }]
            
            # 创建提审服务
            appeal_service = create_production_appeal_service(app_settings)
            
            logger.info("🚀 开始重新提审...")
            
            # 执行提审
            results = appeal_service.batch_appeal_all_plans(plans_data)
            
            # 更新数据库
            updated_count = appeal_service.update_database_with_results(db, results)
            
            # 分析结果
            if results and len(results) > 0:
                result = results[0]
                if result['success']:
                    logger.success(f"✅ 计划 {campaign_id} 重新提审成功！")
                    logger.info(f"📝 回复内容: {result['message'][:100]}...")
                    return True
                else:
                    logger.error(f"❌ 计划 {campaign_id} 重新提审仍然失败")
                    logger.error(f"📝 失败原因: {result['message']}")
                    return False
            else:
                logger.error(f"❌ 未获取到提审结果")
                return False
                
        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"❌ 重试提审失败: {e}")
        return False

def find_failed_appeals():
    """查找所有失败的提审"""
    logger.info("🔍 查找所有失败的提审...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.database.database import SessionLocal
        from qianchuan_aw.database.models import Campaign, AdAccount, Principal
        from sqlalchemy.orm import joinedload
        
        db = SessionLocal()
        try:
            # 查找所有提审失败的计划
            failed_campaigns = db.query(Campaign).options(
                joinedload(Campaign.account).joinedload(AdAccount.principal)
            ).filter(
                Campaign.status == 'AUDITING',
                Campaign.appeal_status == 'submission_failed'
            ).all()
            
            logger.info(f"📊 找到 {len(failed_campaigns)} 个提审失败的计划:")
            
            for campaign in failed_campaigns:
                time_diff = time.time() - campaign.created_at.timestamp()
                hours_ago = time_diff / 3600
                
                logger.info(f"   📋 {campaign.campaign_id_qc} - {campaign.account.principal.name} - {hours_ago:.1f}小时前 - {campaign.appeal_error_message[:50]}...")
            
            return failed_campaigns
            
        finally:
            db.close()
        
    except Exception as e:
        logger.error(f"❌ 查找失败提审失败: {e}")
        return []

def check_celery_status():
    """检查Celery服务状态"""
    logger.info("🔍 检查Celery服务状态...")
    
    try:
        # 检查是否有Celery进程在运行
        import psutil
        celery_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and any('celery' in arg for arg in proc.info['cmdline']):
                    celery_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if celery_processes:
            logger.info(f"✅ 找到 {len(celery_processes)} 个Celery进程")
            for proc in celery_processes:
                logger.info(f"   📋 PID: {proc['pid']}, 命令: {' '.join(proc['cmdline'][:3])}...")
        else:
            logger.warning("⚠️ 没有找到运行中的Celery进程")
            logger.info("💡 这解释了为什么自动提审没有工作")
        
        return len(celery_processes) > 0
        
    except ImportError:
        logger.warning("⚠️ psutil未安装，无法检查进程状态")
        return False
    except Exception as e:
        logger.error(f"❌ 检查Celery状态失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚨 紧急重试失败提审工具")
    logger.info("="*60)
    logger.info("🎯 目标: 重试因各种原因失败的提审计划")
    logger.info("="*60)
    
    try:
        # 1. 检查Celery服务状态
        celery_running = check_celery_status()
        
        if not celery_running:
            logger.warning("⚠️ Celery服务未运行，这是自动提审失败的根本原因")
        
        # 2. 查找失败的提审
        failed_campaigns = find_failed_appeals()
        
        if not failed_campaigns:
            logger.info("✅ 没有失败的提审计划")
            return True
        
        # 3. 特别处理用户关注的计划
        target_campaign = "1838875703448667"
        logger.info(f"\n🎯 优先处理用户关注的计划: {target_campaign}")
        
        target_found = False
        for campaign in failed_campaigns:
            if campaign.campaign_id_qc == target_campaign:
                target_found = True
                break
        
        if target_found:
            if retry_failed_appeal(target_campaign):
                logger.success(f"✅ 用户关注的计划 {target_campaign} 重新提审成功")
            else:
                logger.error(f"❌ 用户关注的计划 {target_campaign} 重新提审失败")
        else:
            logger.warning(f"⚠️ 用户关注的计划 {target_campaign} 不在失败列表中")
        
        # 4. 处理其他失败的计划
        other_campaigns = [c for c in failed_campaigns if c.campaign_id_qc != target_campaign]
        
        if other_campaigns:
            logger.info(f"\n📋 还有 {len(other_campaigns)} 个其他失败的提审计划")
            logger.info("🚀 开始批量重试...")
            
            success_count = 0
            for campaign in other_campaigns:
                logger.info(f"\n📋 处理计划: {campaign.campaign_id_qc}")
                if retry_failed_appeal(campaign.campaign_id_qc):
                    success_count += 1
                time.sleep(3)  # 避免过于频繁
            
            logger.info(f"📊 批量重试完成: {success_count}/{len(other_campaigns)} 个成功")
        
        logger.success("\n🎉 失败提审重试完成！")
        
        if not celery_running:
            logger.warning("\n⚠️ 重要提醒:")
            logger.warning("Celery服务未运行，需要修复以下问题:")
            logger.warning("1. Redis服务问题")
            logger.warning("2. Celery配置问题")
            logger.warning("3. 重启Celery worker和beat服务")
            logger.warning("否则新的计划仍然不会自动提审")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 重试失败提审过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        logger.success("\n🎉 紧急重试失败提审执行成功！")
        logger.info("💡 用户关注的计划应该已经被重新提审")
    else:
        logger.error("\n❌ 紧急重试失败提审执行失败")
    
    sys.exit(0 if success else 1)
