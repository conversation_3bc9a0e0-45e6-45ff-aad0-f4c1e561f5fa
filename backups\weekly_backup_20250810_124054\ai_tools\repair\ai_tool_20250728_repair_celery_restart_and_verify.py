#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 重启Celery进程并验证计划提审功能
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
import psutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def kill_celery_processes():
    """杀死现有的Celery进程"""
    logger.info("🔄 停止现有Celery进程...")
    
    killed_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline'] or [])
            
            if ('run_celery_worker.py' in cmdline or 
                'run_celery_beat.py' in cmdline or
                ('celery' in cmdline and 'qianchuan' in cmdline)):
                
                logger.info(f"🔪 终止进程: PID {proc.info['pid']} - {cmdline[:100]}...")
                proc.terminate()
                killed_processes.append(proc.info['pid'])
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # 等待进程终止
    if killed_processes:
        logger.info("⏳ 等待进程终止...")
        time.sleep(5)
        
        # 强制杀死仍在运行的进程
        for pid in killed_processes:
            try:
                proc = psutil.Process(pid)
                if proc.is_running():
                    logger.warning(f"🔨 强制杀死进程: PID {pid}")
                    proc.kill()
            except psutil.NoSuchProcess:
                pass
    
    logger.success(f"✅ 已停止 {len(killed_processes)} 个Celery进程")
    return len(killed_processes)

def delete_celery_schedule_file():
    """删除Celery Beat调度文件"""
    logger.info("🗑️ 删除Celery Beat调度文件...")
    
    schedule_file = project_root / 'logs' / 'celerybeat-schedule.db'
    
    if schedule_file.exists():
        try:
            schedule_file.unlink()
            logger.success(f"✅ 已删除调度文件: {schedule_file}")
            return True
        except Exception as e:
            logger.error(f"❌ 删除调度文件失败: {e}")
            return False
    else:
        logger.info("📋 调度文件不存在，无需删除")
        return True

def start_celery_worker():
    """启动Celery Worker"""
    logger.info("🚀 启动Celery Worker...")
    
    try:
        import subprocess
        
        # 启动Worker进程
        worker_cmd = [sys.executable, 'run_celery_worker.py']
        
        worker_process = subprocess.Popen(
            worker_cmd,
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间检查是否启动成功
        time.sleep(5)
        
        if worker_process.poll() is None:
            logger.success(f"✅ Celery Worker已启动 (PID: {worker_process.pid})")
            return True, worker_process.pid
        else:
            stdout, stderr = worker_process.communicate()
            logger.error(f"❌ Celery Worker启动失败")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False, None
            
    except Exception as e:
        logger.error(f"❌ 启动Celery Worker失败: {e}")
        return False, None

def start_celery_beat():
    """启动Celery Beat"""
    logger.info("📅 启动Celery Beat...")
    
    try:
        import subprocess
        
        # 启动Beat进程
        beat_cmd = [sys.executable, 'run_celery_beat.py']
        
        beat_process = subprocess.Popen(
            beat_cmd,
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间检查是否启动成功
        time.sleep(5)
        
        if beat_process.poll() is None:
            logger.success(f"✅ Celery Beat已启动 (PID: {beat_process.pid})")
            return True, beat_process.pid
        else:
            stdout, stderr = beat_process.communicate()
            logger.error(f"❌ Celery Beat启动失败")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False, None
            
    except Exception as e:
        logger.error(f"❌ 启动Celery Beat失败: {e}")
        return False, None

def verify_task_registration():
    """验证任务注册"""
    logger.info("🔍 验证任务注册...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.celery_app import app
        
        # 检查关键任务
        critical_tasks = ['tasks.submit_plans', 'tasks.create_plans', 'tasks.appeal_plans']
        
        registered_tasks = list(app.tasks.keys())
        
        all_registered = True
        for task in critical_tasks:
            if task in registered_tasks:
                logger.success(f"✅ {task} 已注册")
            else:
                logger.error(f"❌ {task} 未注册")
                all_registered = False
        
        # 检查Beat调度
        beat_schedule = app.conf.beat_schedule
        if 'plan-submission-configurable' in beat_schedule:
            logger.success("✅ plan-submission-configurable 调度已配置")
        else:
            logger.error("❌ plan-submission-configurable 调度未配置")
            all_registered = False
        
        return all_registered
        
    except Exception as e:
        logger.error(f"❌ 验证任务注册失败: {e}")
        return False

def manual_trigger_submission():
    """手动触发提审任务"""
    logger.info("🎯 手动触发计划提审任务...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.workflows.tasks import task_submit_plans
        
        # 手动调用任务
        result = task_submit_plans.delay()
        
        logger.success(f"✅ 提审任务已派发，ID: {result.id}")
        
        # 等待任务执行
        logger.info("⏳ 等待任务执行...")
        time.sleep(15)
        
        if result.ready():
            if result.successful():
                logger.success("✅ 提审任务执行成功")
                return True
            else:
                logger.error(f"❌ 提审任务执行失败: {result.result}")
                return False
        else:
            logger.info("⏳ 提审任务仍在执行中...")
            return True
            
    except Exception as e:
        logger.error(f"❌ 手动触发提审任务失败: {e}")
        return False

def check_database_changes():
    """检查数据库变化"""
    logger.info("📊 检查数据库中的提审状态变化...")
    
    try:
        import psycopg2
        import yaml
        
        # 加载数据库配置
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        
        # 检查未提审计划数量
        cursor.execute("""
            SELECT COUNT(*) 
            FROM campaigns 
            WHERE status = 'AUDITING' 
            AND appeal_status IS NULL 
            AND first_appeal_at IS NULL
        """)
        
        unsubmitted_count = cursor.fetchone()[0]
        
        # 检查最近提审的计划
        cursor.execute("""
            SELECT COUNT(*) 
            FROM campaigns 
            WHERE appeal_status IS NOT NULL 
            AND first_appeal_at >= NOW() - INTERVAL '10 minutes'
        """)
        
        recent_submitted = cursor.fetchone()[0]
        
        logger.info(f"📋 当前未提审计划: {unsubmitted_count}")
        logger.info(f"📈 最近10分钟提审计划: {recent_submitted}")
        
        cursor.close()
        conn.close()
        
        return unsubmitted_count, recent_submitted
        
    except Exception as e:
        logger.error(f"❌ 检查数据库失败: {e}")
        return -1, -1

def main():
    """主函数"""
    logger.info("🚀 开始Celery重启和验证流程...")
    logger.info("="*60)
    
    results = {
        'processes_killed': 0,
        'schedule_deleted': False,
        'worker_started': False,
        'beat_started': False,
        'tasks_verified': False,
        'manual_trigger_success': False
    }
    
    try:
        # 1. 停止现有进程
        results['processes_killed'] = kill_celery_processes()
        
        # 2. 删除调度文件
        results['schedule_deleted'] = delete_celery_schedule_file()
        
        # 3. 启动Worker
        worker_success, worker_pid = start_celery_worker()
        results['worker_started'] = worker_success
        
        # 4. 启动Beat
        beat_success, beat_pid = start_celery_beat()
        results['beat_started'] = beat_success
        
        # 等待服务启动
        logger.info("⏳ 等待Celery服务完全启动...")
        time.sleep(10)
        
        # 5. 验证任务注册
        results['tasks_verified'] = verify_task_registration()
        
        # 6. 手动触发测试
        results['manual_trigger_success'] = manual_trigger_submission()
        
        # 7. 检查数据库变化
        unsubmitted, recent_submitted = check_database_changes()
        
        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("🎯 重启和验证结果")
        logger.info("="*60)
        
        for action, result in results.items():
            status = "✅" if result else "❌"
            action_name = action.replace('_', ' ').title()
            logger.info(f"{status} {action_name}")
        
        success_rate = sum(1 for r in results.values() if r) / len(results) * 100
        logger.info(f"\n📈 成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            logger.success("🎉 Celery重启和验证成功！")
            logger.info("\n📋 后续操作:")
            logger.info("1. 观察Celery日志中是否出现 '[Task Start] Submit Plans'")
            logger.info("2. 监控数据库中未提审计划数量是否减少")
            logger.info("3. 检查新创建的计划是否能及时提审")
        else:
            logger.error("❌ Celery重启和验证存在问题")
            logger.info("\n🔧 建议操作:")
            logger.info("1. 检查Celery进程是否正常运行")
            logger.info("2. 查看Celery日志中的错误信息")
            logger.info("3. 验证代码修改是否正确")
        
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ 重启和验证过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
