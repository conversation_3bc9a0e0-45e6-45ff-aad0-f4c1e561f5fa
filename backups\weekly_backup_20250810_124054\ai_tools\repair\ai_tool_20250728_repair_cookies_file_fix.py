#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 修复cookies文件路径问题
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
from loguru import logger

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_existing_cookies_files():
    """检查现有的cookies文件"""
    logger.info("🔍 检查现有的cookies文件...")
    
    files_to_check = [
        project_root / 'config' / 'cookies.json',
        project_root / 'config' / 'browser_cookies.json',
        project_root / 'cookies.json'
    ]
    
    found_files = []
    
    for file_path in files_to_check:
        if file_path.exists():
            stat = file_path.stat()
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            
            found_files.append({
                'path': file_path,
                'size': size,
                'modified': modified
            })
            
            logger.success(f"✅ 找到文件: {file_path}")
            logger.info(f"   📊 大小: {size} 字节")
            logger.info(f"   📅 修改时间: {modified}")
        else:
            logger.warning(f"❌ 文件不存在: {file_path}")
    
    return found_files

def validate_cookies_format(file_path):
    """验证cookies文件格式"""
    logger.info(f"🔍 验证cookies文件格式: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, dict):
            logger.error("❌ cookies文件不是字典格式")
            return False, None
        
        # 检查是否包含主体信息
        principals = []
        for key, value in data.items():
            if key == "说明":  # 跳过说明字段
                continue
            
            if isinstance(value, dict) and 'cookies' in value:
                # 新格式：{"主体名": {"cookies": [...]}}
                principals.append(key)
                cookies_list = value['cookies']
            elif isinstance(value, list):
                # 旧格式：{"主体名": [...]}
                principals.append(key)
                cookies_list = value
            else:
                logger.warning(f"⚠️ 主体 '{key}' 格式异常")
                continue
            
            if not isinstance(cookies_list, list):
                logger.warning(f"⚠️ 主体 '{key}' 的cookies不是列表格式")
                continue
            
            logger.info(f"   🔹 主体: {key}, cookies数量: {len(cookies_list)}")
        
        logger.success(f"✅ cookies文件格式正确，包含 {len(principals)} 个主体")
        return True, principals
        
    except json.JSONDecodeError as e:
        logger.error(f"❌ JSON格式错误: {e}")
        return False, None
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        return False, None

def convert_cookies_format(source_file, target_file):
    """转换cookies文件格式"""
    logger.info(f"🔄 转换cookies文件格式: {source_file} → {target_file}")
    
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
        
        # 检查源文件格式
        converted_data = {}
        
        for key, value in source_data.items():
            if key == "说明":  # 跳过说明字段
                continue
            
            if isinstance(value, dict) and 'cookies' in value:
                # 新格式：{"主体名": {"cookies": [...]}} → {"主体名": [...]}
                converted_data[key] = value['cookies']
                logger.info(f"   🔄 转换主体: {key} (新格式 → 旧格式)")
            elif isinstance(value, list):
                # 旧格式：{"主体名": [...]} → 保持不变
                converted_data[key] = value
                logger.info(f"   ✅ 保持主体: {key} (已是旧格式)")
            else:
                logger.warning(f"   ⚠️ 跳过主体: {key} (格式异常)")
        
        # 保存转换后的文件
        with open(target_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        logger.success(f"✅ cookies文件转换完成: {target_file}")
        logger.info(f"   📊 转换了 {len(converted_data)} 个主体")
        
        return True, len(converted_data)
        
    except Exception as e:
        logger.error(f"❌ 转换失败: {e}")
        return False, 0

def create_cookies_symlink():
    """创建cookies文件符号链接"""
    logger.info("🔗 创建cookies文件符号链接...")
    
    source_file = project_root / 'config' / 'browser_cookies.json'
    target_file = project_root / 'config' / 'cookies.json'
    
    if not source_file.exists():
        logger.error(f"❌ 源文件不存在: {source_file}")
        return False
    
    if target_file.exists():
        logger.warning(f"⚠️ 目标文件已存在: {target_file}")
        # 备份现有文件
        backup_file = target_file.with_suffix(f'.json.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        shutil.copy2(target_file, backup_file)
        logger.info(f"📋 已备份现有文件: {backup_file}")
        target_file.unlink()
    
    try:
        # 在Windows上创建硬链接而不是符号链接
        if os.name == 'nt':
            # Windows: 复制文件而不是创建链接
            shutil.copy2(source_file, target_file)
            logger.success(f"✅ 已复制文件: {source_file} → {target_file}")
        else:
            # Unix/Linux: 创建符号链接
            target_file.symlink_to(source_file)
            logger.success(f"✅ 已创建符号链接: {source_file} → {target_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建链接失败: {e}")
        return False

def test_cookies_loading():
    """测试cookies加载"""
    logger.info("🧪 测试cookies加载...")
    
    try:
        sys.path.insert(0, str(project_root / 'src'))
        from qianchuan_aw.services.async_copilot_service import AsyncCopilotService
        
        # 尝试加载cookies
        cookies_file = project_root / 'config' / 'cookies.json'
        
        if not cookies_file.exists():
            logger.error("❌ cookies.json文件不存在")
            return False
        
        # 模拟加载cookies
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        
        logger.success("✅ cookies文件加载成功")
        
        # 检查主体
        principals = list(cookies_data.keys())
        logger.info(f"📊 可用主体: {len(principals)} 个")
        
        for principal in principals:
            cookies_count = len(cookies_data[principal]) if isinstance(cookies_data[principal], list) else 0
            logger.info(f"   🔹 {principal}: {cookies_count} 个cookies")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试cookies加载失败: {e}")
        return False

def check_principal_mapping():
    """检查主体映射"""
    logger.info("🔍 检查主体映射...")
    
    try:
        # 查询数据库中的主体
        import psycopg2
        import yaml
        
        config_path = project_root / 'config' / 'settings.yml'
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        db_config = config['database']['postgresql']
        
        conn = psycopg2.connect(
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password']
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM principals ORDER BY name")
        db_principals = [row[0] for row in cursor.fetchall()]
        cursor.close()
        conn.close()
        
        logger.info(f"📊 数据库中的主体: {db_principals}")
        
        # 检查cookies文件中的主体
        cookies_file = project_root / 'config' / 'cookies.json'
        if cookies_file.exists():
            with open(cookies_file, 'r', encoding='utf-8') as f:
                cookies_data = json.load(f)
            
            cookies_principals = list(cookies_data.keys())
            logger.info(f"📊 cookies文件中的主体: {cookies_principals}")
            
            # 检查匹配情况
            missing_in_cookies = []
            for db_principal in db_principals:
                if db_principal not in cookies_principals:
                    missing_in_cookies.append(db_principal)
                    logger.warning(f"⚠️ 数据库主体 '{db_principal}' 在cookies文件中缺失")
                else:
                    logger.success(f"✅ 主体 '{db_principal}' 匹配")
            
            if missing_in_cookies:
                logger.error(f"❌ {len(missing_in_cookies)} 个主体缺少cookies")
                return False, missing_in_cookies
            else:
                logger.success("✅ 所有主体都有对应的cookies")
                return True, []
        else:
            logger.error("❌ cookies.json文件不存在")
            return False, db_principals
        
    except Exception as e:
        logger.error(f"❌ 检查主体映射失败: {e}")
        return False, []

def main():
    """主函数"""
    logger.info("🚀 开始修复cookies文件问题...")
    logger.info("="*60)
    
    results = {
        'cookies_files_found': False,
        'format_valid': False,
        'symlink_created': False,
        'loading_test_passed': False,
        'principal_mapping_ok': False
    }
    
    try:
        # 1. 检查现有文件
        found_files = check_existing_cookies_files()
        results['cookies_files_found'] = len(found_files) > 0
        
        if not results['cookies_files_found']:
            logger.error("❌ 未找到任何cookies文件")
            return False
        
        # 2. 验证browser_cookies.json格式
        browser_cookies_file = project_root / 'config' / 'browser_cookies.json'
        if browser_cookies_file.exists():
            valid, principals = validate_cookies_format(browser_cookies_file)
            results['format_valid'] = valid
            
            if valid:
                # 3. 转换格式并创建cookies.json
                cookies_file = project_root / 'config' / 'cookies.json'
                success, count = convert_cookies_format(browser_cookies_file, cookies_file)
                results['symlink_created'] = success
            else:
                logger.error("❌ browser_cookies.json格式无效")
                return False
        else:
            logger.error("❌ browser_cookies.json文件不存在")
            return False
        
        # 4. 测试cookies加载
        results['loading_test_passed'] = test_cookies_loading()
        
        # 5. 检查主体映射
        mapping_ok, missing = check_principal_mapping()
        results['principal_mapping_ok'] = mapping_ok
        
        # 输出结果
        logger.info("\n" + "="*60)
        logger.info("🎯 Cookies修复结果")
        logger.info("="*60)
        
        for action, result in results.items():
            status = "✅" if result else "❌"
            action_name = action.replace('_', ' ').title()
            logger.info(f"{status} {action_name}")
        
        success_count = sum(results.values())
        total_count = len(results)
        success_rate = (success_count / total_count) * 100
        
        logger.info(f"\n📈 修复成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            logger.success("🎉 Cookies文件修复成功！")
            
            logger.info("\n📋 修复效果:")
            logger.info("✅ cookies.json文件已创建")
            logger.info("✅ cookies格式已验证")
            logger.info("✅ cookies加载测试通过")
            logger.info("✅ 主体映射检查完成")
            
            logger.info("\n🔄 后续操作:")
            logger.info("1. 重启Celery Worker进程")
            logger.info("2. 观察提审任务是否不再因cookies问题而失败")
            logger.info("3. 监控计划提审状态变化")
            
        else:
            logger.error("❌ Cookies文件修复存在问题")
            
            if not mapping_ok and missing:
                logger.info("\n💡 主体映射问题:")
                logger.info("以下主体缺少cookies:")
                for principal in missing:
                    logger.info(f"   ⚠️ {principal}")
                logger.info("请确保browser_cookies.json中包含所有主体的cookies")
        
        return success_rate >= 80
        
    except Exception as e:
        logger.error(f"❌ 修复过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
